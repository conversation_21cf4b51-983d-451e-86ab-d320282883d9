﻿using UnityEditor;
using UnityEngine;

[CanEditMultipleObjects]
[CustomEditor(typeof(Invector.vCharacterController.vThirdPersonInput), true)]
public class vThirdPersonInputEditor : Editor
{
    GUISkin skin;

    public override void OnInspectorGUI()
    {
        if (!skin) skin = Resources.Load("vSkin") as GUISkin;
        GUI.skin = skin;

        GUILayout.BeginVertical("INPUT MANAGER", "window");

        GUILayout.Space(30);

        EditorGUILayout.BeginVertical();

        // Update serialized object
        serializedObject.Update();

        // Draw Controller Input section
        DrawControllerInputSection();

        EditorGUILayout.Space();

        // Draw Camera Input section
        DrawCameraInputSection();

        EditorGUILayout.Space();

        // Draw Camera Control Settings section
        DrawCameraControlSection();

        EditorGUILayout.Space();

        // Draw Joystick section
        DrawJoystickSection();

        // Apply changes
        serializedObject.ApplyModifiedProperties();

        GUILayout.Space(10);

        GUILayout.EndVertical();
        EditorGUILayout.EndVertical();

        GUILayout.Space(2);
    }

    private void DrawControllerInputSection()
    {
        EditorGUILayout.LabelField("Controller Input", EditorStyles.boldLabel);
        EditorGUILayout.PropertyField(serializedObject.FindProperty("horizontalInput"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("verticallInput"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("jumpInput"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("strafeInput"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("sprintInput"));
    }

    private void DrawCameraInputSection()
    {
        EditorGUILayout.LabelField("Camera Input", EditorStyles.boldLabel);
        EditorGUILayout.PropertyField(serializedObject.FindProperty("rotateCameraXInput"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("rotateCameraYInput"));
    }

    private void DrawCameraControlSection()
    {
        EditorGUILayout.LabelField("Camera Control Settings", EditorStyles.boldLabel);

        // Add a colored box around camera control settings
        EditorGUILayout.BeginVertical(GUI.skin.box);

        var enablePCProp = serializedObject.FindProperty("enablePCCameraControl");
        var enableMobileProp = serializedObject.FindProperty("enableMobileCameraControl");
        var touchSensitivityProp = serializedObject.FindProperty("touchSensitivity");

        EditorGUILayout.PropertyField(enablePCProp, new GUIContent("Enable PC Camera Control", "Enable camera rotation with left click + mouse drag on PC"));
        EditorGUILayout.PropertyField(enableMobileProp, new GUIContent("Enable Mobile Camera Control", "Enable touch camera rotation on mobile"));

        // Only show touch sensitivity if mobile camera control is enabled
        if (enableMobileProp.boolValue)
        {
            EditorGUILayout.PropertyField(touchSensitivityProp, new GUIContent("Touch Sensitivity", "Camera rotation sensitivity for touch input"));
        }

        // Add helpful info
        EditorGUILayout.Space();
        EditorGUILayout.HelpBox("PC: Hold left mouse button and drag to rotate camera\nMobile: Touch and drag to rotate camera (avoids UI elements)", MessageType.Info);

        EditorGUILayout.EndVertical();
    }

    private void DrawJoystickSection()
    {
        EditorGUILayout.LabelField("Joystick", EditorStyles.boldLabel);

        EditorGUILayout.BeginVertical(GUI.skin.box);

        var joystickProp = serializedObject.FindProperty("joystick");
        EditorGUILayout.PropertyField(joystickProp, new GUIContent("Joystick", "Assign a joystick from the Joystick Pack for movement control"));

        if (joystickProp.objectReferenceValue == null)
        {
            EditorGUILayout.HelpBox("Assign a joystick from the Joystick Pack to enable touch movement controls.", MessageType.Warning);
        }
        else
        {
            EditorGUILayout.HelpBox("Joystick assigned! Movement input will be combined with keyboard/gamepad input.", MessageType.Info);
        }

        EditorGUILayout.EndVertical();
    }
}
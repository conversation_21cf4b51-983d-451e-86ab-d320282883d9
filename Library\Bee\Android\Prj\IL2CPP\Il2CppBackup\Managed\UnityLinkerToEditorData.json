{"report": {"modules": [{"name": "AndroidJNI", "dependencies": []}, {"name": "Animation", "dependencies": [{"name": "AnimationClip", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "Animator", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "AnimatorController", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 1, "icon": null}, {"name": "AnimatorOverrideController", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "Avatar", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "Motion", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "RuntimeAnimatorController", "scenes": [], "dependencyType": 1, "icon": null}]}, {"name": "Audio", "dependencies": [{"name": "AudioBehaviour", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "AudioClip", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "AudioListener", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "AudioManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "AudioResource", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "AudioSource", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "SampleClip", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Required by Video Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.video"}]}, {"name": "Core", "dependencies": [{"name": "Behaviour", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "BuildSettings", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Camera", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "Component", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "Cubemap", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "CubemapArray", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "DelayedCallManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "EditorExtension", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 1, "icon": null}, {"name": "GameManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "GameObject", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "GlobalGameManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "GraphicsSettings", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "InputManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "LevelGameManager", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 1, "icon": null}, {"name": "Light", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "LightProbes", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "LightingSettings", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "LightmapSettings", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "LowerResBlitTexture", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "Material", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON>", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "MonoBehaviour", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "MonoManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "MonoScript", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 1, "icon": null}, {"name": "NamedObject", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 1, "icon": null}, {"name": "Object", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "PlayerSettings", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "PreloadData", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "QualitySettings", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "RectTransform", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "ReflectionProbe", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "RenderSettings", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "RenderTexture", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON><PERSON>", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "ResourceManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "RuntimeInitializeOnLoadManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Shader", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "ShaderNameRegistry", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "SortingGroup", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "Sprite", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "SpriteAtlas", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "TagManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "TextAsset", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Texture", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Texture2D", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Texture2DArray", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "Texture3D", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "TimeManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Transform", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "Required by AndroidJNI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.androidjni"}, {"name": "Required by Animation Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.animation"}, {"name": "Required by Audio Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.audio"}, {"name": "Required by GraphicsStateCollectionSerializer Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.graphicsstatecollectionserializer"}, {"name": "Required by HierarchyCore <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.hierarchycore"}, {"name": "Required by IMGUI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.imgui"}, {"name": "Required by Input Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.input"}, {"name": "Required by InputForUI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.inputforui"}, {"name": "Required by InputLegacy Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.inputlegacy"}, {"name": "Required by JSONSerialize Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.jsonserialize"}, {"name": "Required by Physics Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.physics"}, {"name": "Required by Physics2D Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.physics2d"}, {"name": "Required by Properties Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.properties"}, {"name": "Required by RuntimeInitializeOnLoadManagerInitializer Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.runtimeinitializeonloadmanagerinitializer"}, {"name": "Required by Subsystems Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.subsystems"}, {"name": "Required by TLS Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.tls"}, {"name": "Required by <PERSON><PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.terrain"}, {"name": "Required by TerrainPhysics Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.terrainphysics"}, {"name": "Required by TextCoreFontEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcorefontengine"}, {"name": "Required by TextCoreTextEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcoretextengine"}, {"name": "Required by TextRendering Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textrendering"}, {"name": "Required by UI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.ui"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}, {"name": "Required by UnityAnalyticsCommon <PERSON>dule", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.unityanalyticscommon"}, {"name": "Required by UnityWebRequest Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.unitywebrequest"}, {"name": "Required by VR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.vr"}, {"name": "Required by Video Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.video"}, {"name": "Required by XR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.xr"}]}, {"name": "GraphicsStateCollectionSerializer", "dependencies": [{"name": "GraphicsStateCollectionSerializer is always required", "scenes": [], "dependencyType": 3, "icon": "class/DefaultAsset"}]}, {"name": "HierarchyCore", "dependencies": [{"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}]}, {"name": "IMGUI", "dependencies": [{"name": "Required by InputForUI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.inputforui"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}]}, {"name": "Input", "dependencies": [{"name": "Required by VR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.vr"}, {"name": "Required by XR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.xr"}]}, {"name": "InputForUI", "dependencies": [{"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}]}, {"name": "InputLegacy", "dependencies": [{"name": "Required by IMGUI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.imgui"}, {"name": "Required by InputForUI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.inputforui"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}]}, {"name": "JSONSerialize", "dependencies": [{"name": "Required by GraphicsStateCollectionSerializer Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.graphicsstatecollectionserializer"}, {"name": "Required by Input Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.input"}, {"name": "Required by RuntimeInitializeOnLoadManagerInitializer Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.runtimeinitializeonloadmanagerinitializer"}, {"name": "Required by Subsystems Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.subsystems"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}, {"name": "Required by VR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.vr"}, {"name": "Required by XR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.xr"}]}, {"name": "Physics", "dependencies": [{"name": "CapsuleCollider", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "Collider", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "PhysicsManager", "scenes": [], "dependencyType": 1, "icon": null}, {"name": "PhysicsMaterial", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "Rigidbody", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "Required by TerrainPhysics Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.terrainphysics"}, {"name": "Required by VR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.vr"}, {"name": "Required by XR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.xr"}]}, {"name": "Physics2D", "dependencies": [{"name": "Physics2DSettings", "scenes": [], "dependencyType": 1, "icon": null}]}, {"name": "Properties", "dependencies": [{"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}]}, {"name": "RuntimeInitializeOnLoadManagerInitializer", "dependencies": [{"name": "RuntimeInitializeOnLoadManagerInitializer is always required", "scenes": [], "dependencyType": 3, "icon": "class/DefaultAsset"}]}, {"name": "SharedInternals", "dependencies": [{"name": "Required by AndroidJNI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.androidjni"}, {"name": "Required by Animation Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.animation"}, {"name": "Required by Audio Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.audio"}, {"name": "Required by Core Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.core"}, {"name": "Required by GraphicsStateCollectionSerializer Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.graphicsstatecollectionserializer"}, {"name": "Required by HierarchyCore <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.hierarchycore"}, {"name": "Required by IMGUI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.imgui"}, {"name": "Required by Input Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.input"}, {"name": "Required by InputForUI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.inputforui"}, {"name": "Required by InputLegacy Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.inputlegacy"}, {"name": "Required by JSONSerialize Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.jsonserialize"}, {"name": "Required by Physics Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.physics"}, {"name": "Required by Physics2D Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.physics2d"}, {"name": "Required by Properties Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.properties"}, {"name": "Required by RuntimeInitializeOnLoadManagerInitializer Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.runtimeinitializeonloadmanagerinitializer"}, {"name": "Required by Subsystems Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.subsystems"}, {"name": "Required by TLS Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.tls"}, {"name": "Required by <PERSON><PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.terrain"}, {"name": "Required by TerrainPhysics Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.terrainphysics"}, {"name": "Required by TextCoreFontEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcorefontengine"}, {"name": "Required by TextCoreTextEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcoretextengine"}, {"name": "Required by TextRendering Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textrendering"}, {"name": "Required by UI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.ui"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}, {"name": "Required by UnityAnalyticsCommon <PERSON>dule", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.unityanalyticscommon"}, {"name": "Required by UnityWebRequest Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.unitywebrequest"}, {"name": "Required by VR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.vr"}, {"name": "Required by Video Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.video"}, {"name": "Required by XR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.xr"}]}, {"name": "Subsystems", "dependencies": [{"name": "Required by XR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.xr"}]}, {"name": "TLS", "dependencies": [{"name": "Required by UnityWebRequest Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.unitywebrequest"}, {"name": "Required by Video Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.video"}]}, {"name": "Terrain", "dependencies": [{"name": "Terrain", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "TerrainData", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "Required by TerrainPhysics Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.terrainphysics"}]}, {"name": "TerrainPhysics", "dependencies": [{"name": "<PERSON>in<PERSON><PERSON><PERSON>", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}]}, {"name": "TextCoreFontEngine", "dependencies": [{"name": "Required by TextCoreTextEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcoretextengine"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}]}, {"name": "TextCoreTextEngine", "dependencies": [{"name": "Required by IMGUI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.imgui"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}]}, {"name": "TextRendering", "dependencies": [{"name": "Font", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "Required by IMGUI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.imgui"}, {"name": "Required by TextCoreFontEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcorefontengine"}, {"name": "Required by TextCoreTextEngine Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.textcoretextengine"}, {"name": "Required by UI Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.ui"}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}]}, {"name": "UI", "dependencies": [{"name": "<PERSON><PERSON>", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "CanvasGroup", "scenes": [], "dependencyType": 0, "icon": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "Required by UIElements <PERSON>", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.uielements"}, {"name": "Required by Video Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.video"}]}, {"name": "UIElements", "dependencies": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "scenes": [], "dependencyType": 0, "icon": null}]}, {"name": "UnityAnalyticsCommon", "dependencies": [{"name": "Required by Subsystems Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.subsystems"}, {"name": "Required by VR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.vr"}]}, {"name": "UnityWebRequest", "dependencies": [{"name": "Required by Video Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.video"}]}, {"name": "VR", "dependencies": []}, {"name": "Video", "dependencies": [{"name": "VideoClip", "scenes": ["Assets/Scenes/TPSFREEHAND.unity"], "dependencyType": 0, "icon": null}, {"name": "VideoPlayer", "scenes": [], "dependencyType": 0, "icon": null}]}, {"name": "XR", "dependencies": [{"name": "Required by VR Module", "scenes": [], "dependencyType": 2, "icon": "package/com.unity.modules.vr"}]}]}}
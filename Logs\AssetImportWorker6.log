Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.30f1 (62b05ba0686a) revision 6467675'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker6
-projectPath
D:/My Project/Driving Simulator Game Z TEC
-logFile
Logs/AssetImportWorker6.log
-srvPort
53742
-job-worker-count
11
-background-job-worker-count
8
-asset-garbage-collectors
1
Successfully changed project path to: D:/My Project/Driving Simulator Game Z TEC
D:/My Project/Driving Simulator Game Z TEC
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [30348]  Target information:

Player connection [30348]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1768652195 [EditorId] 1768652195 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [30348]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1768652195 [EditorId] 1768652195 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [30348] Host joined multi-casting on [***********:54997]...
Player connection [30348] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 11
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.92 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.30f1 (62b05ba0686a)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Driving Simulator Game Z TEC/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56360
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.008050 seconds.
- Loaded All Assemblies, in  0.776 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 401 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.090 seconds
Domain Reload Profiling: 1860ms
	BeginReloadAssembly (283ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (68ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (95ms)
	LoadAllAssembliesAndSetupDomain (300ms)
		LoadAssemblies (278ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (292ms)
			TypeCache.Refresh (289ms)
				TypeCache.ScanAssembly (264ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1090ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1005ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (555ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (90ms)
			ProcessInitializeOnLoadAttributes (233ms)
			ProcessInitializeOnLoadMethodAttributes (121ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.481 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.13 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.038 seconds
Domain Reload Profiling: 2513ms
	BeginReloadAssembly (346ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (75ms)
	RebuildCommonClasses (95ms)
	RebuildNativeTypeToScriptingClass (28ms)
	initialDomainReloadingComplete (75ms)
	LoadAllAssembliesAndSetupDomain (931ms)
		LoadAssemblies (696ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (433ms)
			TypeCache.Refresh (332ms)
				TypeCache.ScanAssembly (301ms)
			BuildScriptInfoCaches (83ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1038ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (839ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (158ms)
			ProcessInitializeOnLoadAttributes (602ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 2.24 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 66 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4568 unused Assets / (1.7 MB). Loaded Objects now: 5129.
Memory consumption went from 117.6 MB to 115.9 MB.
Total: 16.025500 ms (FindLiveObjects: 2.289900 ms CreateObjectMapping: 0.873800 ms MarkObjects: 10.754500 ms  DeleteObjects: 2.102100 ms)

========================================================================
Received Import Request.
  Time since last request: 14576.661934 seconds.
  path: Assets/UnityTechnologies/EffectExamples/WeaponEffects/Prefabs/BulletImpactWaterContainerExtinguisherEffect.prefab
  artifactKey: Guid(8c0568c457f38824d8649f26f2363927) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/WeaponEffects/Prefabs/BulletImpactWaterContainerExtinguisherEffect.prefab using Guid(8c0568c457f38824d8649f26f2363927) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '55e6d7071ba6f2628bde3244e231c78e') in 0.5609739 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 23

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/_Prefabs/CargoWagon02_Brown_Stones.prefab
  artifactKey: Guid(3f577d58984d25c44acf1133f14cd1a0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/_Prefabs/CargoWagon02_Brown_Stones.prefab using Guid(3f577d58984d25c44acf1133f14cd1a0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd2ef7b3e50fa8f3850ffcb0151ab9062') in 1.0890062 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 255

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Furniture/Chair_01.fbx
  artifactKey: Guid(00d268b527d3f304c947e67bb17920d5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Furniture/Chair_01.fbx using Guid(00d268b527d3f304c947e67bb17920d5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6a9e457ccc0b254684e3762d24d7938b') in 0.1578295 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Misc/Chair_01.prefab
  artifactKey: Guid(196aae5aa8f94654488dbaed63b6f912) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Misc/Chair_01.prefab using Guid(196aae5aa8f94654488dbaed63b6f912) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fbef8c225894e2946323d8abbf1d66a9') in 0.1142916 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Models/CableCar_01_Lever.fbx
  artifactKey: Guid(293a7f0c35d4e3149a860df754a6aa9b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Models/CableCar_01_Lever.fbx using Guid(293a7f0c35d4e3149a860df754a6aa9b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '20e82913da0bb55b8fcdff5a1c17c773') in 0.6833436 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000083 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Models/CableCar_01_LOD1.fbx
  artifactKey: Guid(9e885caaed79d604aa0252a4710d460c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Models/CableCar_01_LOD1.fbx using Guid(9e885caaed79d604aa0252a4710d460c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '19251e75db1aa6a1a6f86ebe24517a73') in 2.7753095 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 1/_Prefabs/CargoWagon01_Gray.prefab
  artifactKey: Guid(14c0dac1ad2c6fb4d9470b01dacb73a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 1/_Prefabs/CargoWagon01_Gray.prefab using Guid(14c0dac1ad2c6fb4d9470b01dacb73a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5b73bf790d09d94b7aee48b18ecc23cb') in 0.4256103 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 279

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/_Prefabs/CargoWagon02_Brown.prefab
  artifactKey: Guid(bc024d9f3c5497c4a8cfb1b9c48e1614) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/_Prefabs/CargoWagon02_Brown.prefab using Guid(bc024d9f3c5497c4a8cfb1b9c48e1614) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '354aed162c56be63cbe4f29c3b18edd8') in 0.3554577 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 244

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Bumpers_35cm_low.fbx
  artifactKey: Guid(97c2ed3dda9165145ab737637b4cbfda) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Bumpers_35cm_low.fbx using Guid(97c2ed3dda9165145ab737637b4cbfda) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '00735fd384c3b80eed9e77a2fd3d1179') in 0.1379553 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Invector-3rdPersonController_LITE/3D Models/Others/NewAssets/columnB.prefab
  artifactKey: Guid(961d0fe6a50d8e3459a12b224216eb19) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Invector-3rdPersonController_LITE/3D Models/Others/NewAssets/columnB.prefab using Guid(961d0fe6a50d8e3459a12b224216eb19) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2eba2461189ffcfdfd174b62b6a4b437') in 0.3491159 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Misc/Container_Green_12_19.prefab
  artifactKey: Guid(997dd8836a055db4db24740d2ffeb63d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Misc/Container_Green_12_19.prefab using Guid(997dd8836a055db4db24740d2ffeb63d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'efda920521ace4bcae23f1d849476948') in 0.1145895 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 3/Models/CargoWagon_03.fbx
  artifactKey: Guid(9cd329b514f933540b69c4140412435e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 3/Models/CargoWagon_03.fbx using Guid(9cd329b514f933540b69c4140412435e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0e06d01131d304b0c3753ca9b97384c7') in 0.1040594 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/_Prefabs/CargoWagon02_Blue_Gravel.prefab
  artifactKey: Guid(3832346a6d3dfb141a4b46d3e8475627) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/_Prefabs/CargoWagon02_Blue_Gravel.prefab using Guid(3832346a6d3dfb141a4b46d3e8475627) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0e26b0f07a6a7ff348625eb02085b8d6') in 0.4887214 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 255

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/_Prefabs/CargoWagon02_Blue_Stones.prefab
  artifactKey: Guid(e5d169344a0fd3c41ab6a502926a76d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/_Prefabs/CargoWagon02_Blue_Stones.prefab using Guid(e5d169344a0fd3c41ab6a502926a76d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '87a02c690e3d18e0cf15a5a00ee81e0a') in 0.3907984 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 255

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Cable Car Rails/Models/CableCar_RailSegment_LOD0.fbx
  artifactKey: Guid(4f03776b1c2340e488c859b755a5405b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Cable Car Rails/Models/CableCar_RailSegment_LOD0.fbx using Guid(4f03776b1c2340e488c859b755a5405b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '65b160678a0ed9fb1076d1ec294c2d3b') in 0.4259258 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000085 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Misc/Container_Blue_12_19.prefab
  artifactKey: Guid(d4968c2b520e4f44fbf3f0e5ae374eb3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Misc/Container_Blue_12_19.prefab using Guid(d4968c2b520e4f44fbf3f0e5ae374eb3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dcacad1ace7a7966e5d459e466455e7a') in 0.116043 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/_Prefabs/Parts/Benches/CableCar_01_Bench_Short.prefab
  artifactKey: Guid(be6656e540f91594497fe195b3b93152) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/_Prefabs/Parts/Benches/CableCar_01_Bench_Short.prefab using Guid(be6656e540f91594497fe195b3b93152) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c710fe62f690c9d6fbdb66fb3cf921e3') in 0.4137591 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 1/_Prefabs/CargoWagon01_Blue.prefab
  artifactKey: Guid(ffb22ec6d1acb6845b21c71cadfa40ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 1/_Prefabs/CargoWagon01_Blue.prefab using Guid(ffb22ec6d1acb6845b21c71cadfa40ae) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b7ed00831a5fae0cddf5d22d8c030ff2') in 0.4210782 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 279

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Models/CableCar_01_WheelsTruck.fbx
  artifactKey: Guid(2325da7cfb71c4c45b5718c8ba593052) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Models/CableCar_01_WheelsTruck.fbx using Guid(2325da7cfb71c4c45b5718c8ba593052) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '00aa8d12bd74b5adda39d6ad296cb5af') in 0.7410696 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Controls_Mic_01.fbx
  artifactKey: Guid(45cc6ccc2234b3042b4004477881ff36) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Controls_Mic_01.fbx using Guid(45cc6ccc2234b3042b4004477881ff36) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '752e9a8ea1aeefb1282aff703311fb55') in 0.0281613 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Controls_Lever_02.fbx
  artifactKey: Guid(9364451d7ad243b4ea9c97d2bdb9c6ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Controls_Lever_02.fbx using Guid(9364451d7ad243b4ea9c97d2bdb9c6ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2f773d90aa2ea1ff0b638fc4858ae6dc') in 0.0305973 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/AdvancedHelicopterController/Prefabs/Collapsibles/Custom_House_Modular.prefab
  artifactKey: Guid(603b661e609da4967b00e2bc7ea9091f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Prefabs/Collapsibles/Custom_House_Modular.prefab using Guid(603b661e609da4967b00e2bc7ea9091f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '67dd11a79cabfa24e2653024ea3f7ba4') in 0.1614092 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1917

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/DirectionIndicator_01_Arrow.fbx
  artifactKey: Guid(6d3d6c07dae87f447bd621688c5ab814) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/DirectionIndicator_01_Arrow.fbx using Guid(6d3d6c07dae87f447bd621688c5ab814) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd852232252c14f71d3df49862723d91c') in 0.1279945 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/SFX/DoorOpenSFX.prefab
  artifactKey: Guid(a2b43e15ab703de4482ded40f40cad0f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/SFX/DoorOpenSFX.prefab using Guid(a2b43e15ab703de4482ded40f40cad0f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8033362f92ded2a6bea1fbb6a08ad07c') in 0.005792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/UnityTechnologies/EffectExamples/FireExplosionEffects/Prefabs/DrippingFlames.prefab
  artifactKey: Guid(ce4133109fd2cb7469faad8e7c4307a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/FireExplosionEffects/Prefabs/DrippingFlames.prefab using Guid(ce4133109fd2cb7469faad8e7c4307a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '536d2cb2d17717dab5a357b777564ca8') in 0.3618802 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 35

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/AdvancedHelicopterController/Prefabs/Bullets/EnemyMissile.prefab
  artifactKey: Guid(41a572c0268c248b7968142264d00419) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Prefabs/Bullets/EnemyMissile.prefab using Guid(41a572c0268c248b7968142264d00419) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd3385e31df7c38c9f3da655c1619ffea') in 0.1264248 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 65

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Controls_Button_01.fbx
  artifactKey: Guid(cdb3f2e4349a6404683d25e48c556067) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Controls_Button_01.fbx using Guid(cdb3f2e4349a6404683d25e48c556067) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '424e9c266d806e4c8c3665282f50703c') in 0.0265421 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 1/Models/CargoWagon01.fbx
  artifactKey: Guid(2884cfa21ff60ee449abfc2e0eb26109) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 1/Models/CargoWagon01.fbx using Guid(2884cfa21ff60ee449abfc2e0eb26109) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bf08315940f8f43c1336efe79ddca664') in 0.1192291 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/UnityTechnologies/EffectExamples/FireExplosionEffects/Prefabs/FlamesEffects.prefab
  artifactKey: Guid(b9e167e31f640b34f87caab122fca598) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/FireExplosionEffects/Prefabs/FlamesEffects.prefab using Guid(b9e167e31f640b34f87caab122fca598) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c0b0f36427e1ffe327a9a07b3741fb74') in 0.1069507 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 66

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Control_Panel.fbx
  artifactKey: Guid(62067ba57bfbb2d409e33f01f67596a9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Control_Panel.fbx using Guid(62067ba57bfbb2d409e33f01f67596a9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '292554ca916734bd5e00a6f1718289a0') in 0.1235673 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Cable Car Rails/Models/CableCar_RailSegment_LOD2.fbx
  artifactKey: Guid(8355453c5b537c043ae6cf0143570780) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Cable Car Rails/Models/CableCar_RailSegment_LOD2.fbx using Guid(8355453c5b537c043ae6cf0143570780) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '62508cab1d19403a27568172ce2d51ef') in 0.4317489 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/AdvancedHelicopterController/Prefabs/Helicopter/HelicopterComponents/Helicopter.prefab
  artifactKey: Guid(d159f5a612da04c2bb2105ecc1f0da2d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Prefabs/Helicopter/HelicopterComponents/Helicopter.prefab using Guid(d159f5a612da04c2bb2105ecc1f0da2d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '60af95290fcc4bf94914e377ee3925c4') in 0.2296238 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 293

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Locomotive_01_Interior.fbx
  artifactKey: Guid(a7fa5da1437be1a43a7282a92f45d60d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Locomotive_01_Interior.fbx using Guid(a7fa5da1437be1a43a7282a92f45d60d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b3c9a93eaf8b63db248da96cdf06ee86') in 0.1205152 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_CW_7_prefab.prefab
  artifactKey: Guid(fdb11f4995495a04a9f2b9cc5c2fed3c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_CW_7_prefab.prefab using Guid(fdb11f4995495a04a9f2b9cc5c2fed3c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6cc59702b4baaba2713fef626777f0d9') in 0.3650477 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 64

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/AdvancedHelicopterController/Prefabs/Bullets/Missile.prefab
  artifactKey: Guid(4cbe3d66c22cb400d845b1a96a75bf08) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Prefabs/Bullets/Missile.prefab using Guid(4cbe3d66c22cb400d845b1a96a75bf08) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '63e0072e132de7e4c92db9c845fe2889') in 0.0846794 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 64

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/MSK 2.2/Perfabs/Perfabs/Motorbike3.prefab
  artifactKey: Guid(2e90b869411be9940ba47eb56f564704) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MSK 2.2/Perfabs/Perfabs/Motorbike3.prefab using Guid(2e90b869411be9940ba47eb56f564704) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 1.53 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: '8c7c6f5f71470df81f48a1f8e8bd9fd6') in 1.7951529 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 289

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Subway_01_Glass_WindowSmall.fbx
  artifactKey: Guid(97ce8cd8fffcbb6408820f8fbc227987) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Subway_01_Glass_WindowSmall.fbx using Guid(97ce8cd8fffcbb6408820f8fbc227987) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4ed1af64a8235a95dbc1a801d25a81cd') in 0.3909769 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Locomotive_01_Orange.prefab
  artifactKey: Guid(d2f5297eca90b63479a9aafd50a3ed7a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Locomotive_01_Orange.prefab using Guid(d2f5297eca90b63479a9aafd50a3ed7a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e18e4b311d4545149210295cfa42bcc5') in 4.6015859 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1216

========================================================================
Received Import Request.
  Time since last request: 0.000092 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Subway_01_Window_Small.fbx
  artifactKey: Guid(aac1c9b0bc5b894439f327fe195cae6b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Subway_01_Window_Small.fbx using Guid(aac1c9b0bc5b894439f327fe195cae6b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1d2e0a356533cb683bebb711817bfef0') in 0.2317148 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000090 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/Models/TenderWagon_01_Coal.fbx
  artifactKey: Guid(92761b46446de934d8d59126f98ba0b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/Models/TenderWagon_01_Coal.fbx using Guid(92761b46446de934d8d59126f98ba0b0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f82de661d54a0b4df250b3f017bc4981') in 2.7422778 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_CW_5_prefab.prefab
  artifactKey: Guid(b8fb9159d0cd2684b91bb2caec533892) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_CW_5_prefab.prefab using Guid(b8fb9159d0cd2684b91bb2caec533892) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dbe0ea38b42e466a9a89315c1e84a523') in 0.6332395 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 64

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Subway_01_Glass_Front.fbx
  artifactKey: Guid(222a6922e09c68345ba4676c70060d75) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Subway_01_Glass_Front.fbx using Guid(222a6922e09c68345ba4676c70060d75) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '46e012365ed475af7d88d21e91e838f3') in 0.1580247 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_ACW_2_prefab.prefab
  artifactKey: Guid(d29cca60e0d2ae140ab4f902074668e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_ACW_2_prefab.prefab using Guid(d29cca60e0d2ae140ab4f902074668e1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bee45b396c9318422284329ff3fab0b7') in 0.6272813 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 64

========================================================================
Received Import Request.
  Time since last request: 0.000127 seconds.
  path: Assets/MSK 2.2/Standard Assets/Terrain Assets/Trees Ambient-Occlusion/Palm/Palm.fbx
  artifactKey: Guid(4c85abd0a4d974473b97e81d10a3a6bc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MSK 2.2/Standard Assets/Terrain Assets/Trees Ambient-Occlusion/Palm/Palm.fbx using Guid(4c85abd0a4d974473b97e81d10a3a6bc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7b43432c45b88a52fa914f5dc424f2b2') in 0.2932315 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Parts/SFX/SteamLocomotiveWheelsSFX.prefab
  artifactKey: Guid(72a3046f45c770942a94ab9fd879fea7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Parts/SFX/SteamLocomotiveWheelsSFX.prefab using Guid(72a3046f45c770942a94ab9fd879fea7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '269cefdcbf5efdb1c3e2f3315a8db5f6') in 0.0108265 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_ACW_3_prefab.prefab
  artifactKey: Guid(0d9227391f875fb4d966b6ff0e93f432) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_ACW_3_prefab.prefab using Guid(0d9227391f875fb4d966b6ff0e93f432) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8397a693103bcf829da53a2cab65b147') in 0.5222354 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 64

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/Models/TenderWagon_01_LOD0.fbx
  artifactKey: Guid(4e8b4246f8c242449b4a66b50690d135) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/Models/TenderWagon_01_LOD0.fbx using Guid(4e8b4246f8c242449b4a66b50690d135) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '79b6d2de9ad3c96257fa9163c8b36f3e') in 3.0290958 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 42.955632 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Sample Trains/s_Steam Train Sample.prefab
  artifactKey: Guid(9b6b14626141490498a0a90e98b7b182) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Sample Trains/s_Steam Train Sample.prefab using Guid(9b6b14626141490498a0a90e98b7b182) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '88a7254bd27c1fd3eceea626d157134b') in 16.8416074 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13433

========================================================================
Received Import Request.
  Time since last request: 363.332245 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Locomotive_01_Blue.prefab
  artifactKey: Guid(aa385a449fb386b4880456df01a4469f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Locomotive_01_Blue.prefab using Guid(aa385a449fb386b4880456df01a4469f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9acec63253525215c5ecf03a68c83708') in 4.7074186 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1216

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.353 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.17 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.008 seconds
Domain Reload Profiling: 2359ms
	BeginReloadAssembly (367ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (90ms)
	RebuildCommonClasses (86ms)
	RebuildNativeTypeToScriptingClass (29ms)
	initialDomainReloadingComplete (50ms)
	LoadAllAssembliesAndSetupDomain (819ms)
		LoadAssemblies (685ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (336ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (297ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1009ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (807ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (154ms)
			ProcessInitializeOnLoadAttributes (574ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 2.08 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4570 unused Assets / (2.0 MB). Loaded Objects now: 5146.
Memory consumption went from 116.4 MB to 114.4 MB.
Total: 14.228800 ms (FindLiveObjects: 1.350600 ms CreateObjectMapping: 0.564200 ms MarkObjects: 7.601100 ms  DeleteObjects: 4.711000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.208 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.19 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.012 seconds
Domain Reload Profiling: 2219ms
	BeginReloadAssembly (332ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (82ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (740ms)
		LoadAssemblies (598ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (317ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (286ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1012ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (814ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (167ms)
			ProcessInitializeOnLoadAttributes (571ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.63 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4566 unused Assets / (1.8 MB). Loaded Objects now: 5146.
Memory consumption went from 116.4 MB to 114.5 MB.
Total: 10.487300 ms (FindLiveObjects: 0.774400 ms CreateObjectMapping: 0.488900 ms MarkObjects: 6.295700 ms  DeleteObjects: 2.926100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.220 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.21 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.121 seconds
Domain Reload Profiling: 2340ms
	BeginReloadAssembly (333ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (88ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (61ms)
	LoadAllAssembliesAndSetupDomain (739ms)
		LoadAssemblies (594ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (317ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (285ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1122ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (904ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (178ms)
			ProcessInitializeOnLoadAttributes (635ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.53 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4566 unused Assets / (2.6 MB). Loaded Objects now: 5146.
Memory consumption went from 116.4 MB to 113.7 MB.
Total: 12.001500 ms (FindLiveObjects: 0.892300 ms CreateObjectMapping: 0.580500 ms MarkObjects: 6.531600 ms  DeleteObjects: 3.995800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.177 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.36 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.018 seconds
Domain Reload Profiling: 2196ms
	BeginReloadAssembly (333ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (82ms)
	RebuildCommonClasses (63ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (711ms)
		LoadAssemblies (562ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (321ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (275ms)
			ResolveRequiredComponents (27ms)
	FinalizeReload (1018ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (805ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (145ms)
			ProcessInitializeOnLoadAttributes (576ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 2.01 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4566 unused Assets / (1.9 MB). Loaded Objects now: 5146.
Memory consumption went from 116.4 MB to 114.5 MB.
Total: 13.808700 ms (FindLiveObjects: 0.864600 ms CreateObjectMapping: 0.495100 ms MarkObjects: 9.722200 ms  DeleteObjects: 2.724400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.142 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.98 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.037 seconds
Domain Reload Profiling: 2179ms
	BeginReloadAssembly (332ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (76ms)
	RebuildCommonClasses (63ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (678ms)
		LoadAssemblies (547ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (315ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (284ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1038ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (808ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (158ms)
			ProcessInitializeOnLoadAttributes (568ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.83 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4566 unused Assets / (1.9 MB). Loaded Objects now: 5146.
Memory consumption went from 116.3 MB to 114.4 MB.
Total: 9.482200 ms (FindLiveObjects: 0.771100 ms CreateObjectMapping: 0.504600 ms MarkObjects: 5.804100 ms  DeleteObjects: 2.401200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.135 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.30 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.029 seconds
Domain Reload Profiling: 2164ms
	BeginReloadAssembly (308ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (72ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (48ms)
	LoadAllAssembliesAndSetupDomain (692ms)
		LoadAssemblies (536ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (322ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (286ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1030ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (823ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (146ms)
			ProcessInitializeOnLoadAttributes (588ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.38 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4566 unused Assets / (1.9 MB). Loaded Objects now: 5146.
Memory consumption went from 116.4 MB to 114.4 MB.
Total: 10.208800 ms (FindLiveObjects: 0.788200 ms CreateObjectMapping: 0.581300 ms MarkObjects: 6.372300 ms  DeleteObjects: 2.465700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.168 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.28 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.004 seconds
Domain Reload Profiling: 2166ms
	BeginReloadAssembly (314ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (70ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (713ms)
		LoadAssemblies (561ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (325ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (289ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1004ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (803ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (156ms)
			ProcessInitializeOnLoadAttributes (566ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.82 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4566 unused Assets / (1.9 MB). Loaded Objects now: 5146.
Memory consumption went from 116.4 MB to 114.5 MB.
Total: 11.431000 ms (FindLiveObjects: 1.575700 ms CreateObjectMapping: 0.525300 ms MarkObjects: 6.797000 ms  DeleteObjects: 2.531400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.237 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.24 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.000 seconds
Domain Reload Profiling: 2237ms
	BeginReloadAssembly (368ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (81ms)
	RebuildCommonClasses (72ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (52ms)
	LoadAllAssembliesAndSetupDomain (719ms)
		LoadAssemblies (607ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (325ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (287ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1000ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (782ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (24ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (153ms)
			ProcessInitializeOnLoadAttributes (543ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.43 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4566 unused Assets / (1.8 MB). Loaded Objects now: 5146.
Memory consumption went from 116.4 MB to 114.6 MB.
Total: 9.311000 ms (FindLiveObjects: 0.704100 ms CreateObjectMapping: 0.471100 ms MarkObjects: 5.771600 ms  DeleteObjects: 2.362600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.165 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.30 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.054 seconds
Domain Reload Profiling: 2220ms
	BeginReloadAssembly (332ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (68ms)
	RebuildCommonClasses (63ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (53ms)
	LoadAllAssembliesAndSetupDomain (691ms)
		LoadAssemblies (556ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (326ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (282ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1055ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (851ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (187ms)
			ProcessInitializeOnLoadAttributes (576ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.72 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4566 unused Assets / (1.7 MB). Loaded Objects now: 5146.
Memory consumption went from 116.4 MB to 114.7 MB.
Total: 11.201900 ms (FindLiveObjects: 1.280900 ms CreateObjectMapping: 0.537700 ms MarkObjects: 6.786800 ms  DeleteObjects: 2.595300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.169 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.28 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.009 seconds
Domain Reload Profiling: 2178ms
	BeginReloadAssembly (345ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (73ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (691ms)
		LoadAssemblies (547ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (326ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (296ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1010ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (805ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (143ms)
			ProcessInitializeOnLoadAttributes (578ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.65 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4566 unused Assets / (2.2 MB). Loaded Objects now: 5146.
Memory consumption went from 116.3 MB to 114.2 MB.
Total: 11.562200 ms (FindLiveObjects: 0.833400 ms CreateObjectMapping: 0.552800 ms MarkObjects: 6.550700 ms  DeleteObjects: 3.624200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.191 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.21 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.037 seconds
Domain Reload Profiling: 2227ms
	BeginReloadAssembly (355ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (86ms)
	RebuildCommonClasses (68ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (688ms)
		LoadAssemblies (567ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (308ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (279ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1038ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (832ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (156ms)
			ProcessInitializeOnLoadAttributes (591ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.35 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4566 unused Assets / (1.9 MB). Loaded Objects now: 5146.
Memory consumption went from 116.4 MB to 114.4 MB.
Total: 12.191600 ms (FindLiveObjects: 0.982300 ms CreateObjectMapping: 0.767900 ms MarkObjects: 7.848700 ms  DeleteObjects: 2.590800 ms)

Prepare: number of updated asset objects reloaded= 0

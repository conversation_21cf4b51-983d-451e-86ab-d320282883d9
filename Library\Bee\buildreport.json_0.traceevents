{ "pid": 41616, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 41616, "tid": 1, "ts": 1751353154902426, "dur": 8463, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 41616, "tid": 1, "ts": 1751353154910895, "dur": 92771, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 41616, "tid": 1, "ts": 1751353155003680, "dur": 3926, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 41616, "tid": 1748, "ts": 1751353156046515, "dur": 2556, "ph": "X", "name": "", "args": {} },
{ "pid": 41616, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 41616, "tid": 12884901888, "ts": 1751353154898880, "dur": 21642, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 41616, "tid": 12884901888, "ts": 1751353154920528, "dur": 1107846, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 41616, "tid": 12884901888, "ts": 1751353154922294, "dur": 4692, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 41616, "tid": 12884901888, "ts": 1751353154926997, "dur": 4049, "ph": "X", "name": "ProcessMessages 571", "args": {} },
{ "pid": 41616, "tid": 12884901888, "ts": 1751353154931053, "dur": 1078585, "ph": "X", "name": "ReadAsync 571", "args": {} },
{ "pid": 41616, "tid": 12884901888, "ts": 1751353156009651, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 12884901888, "ts": 1751353156009656, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 12884901888, "ts": 1751353156009718, "dur": 2325, "ph": "X", "name": "ProcessMessages 12173", "args": {} },
{ "pid": 41616, "tid": 12884901888, "ts": 1751353156012047, "dur": 225, "ph": "X", "name": "ReadAsync 12173", "args": {} },
{ "pid": 41616, "tid": 12884901888, "ts": 1751353156012281, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 12884901888, "ts": 1751353156012285, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 12884901888, "ts": 1751353156012355, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 12884901888, "ts": 1751353156012359, "dur": 1191, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 12884901888, "ts": 1751353156013556, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 12884901888, "ts": 1751353156013561, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 12884901888, "ts": 1751353156013610, "dur": 546, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 41616, "tid": 12884901888, "ts": 1751353156014163, "dur": 13947, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 41616, "tid": 1748, "ts": 1751353156049077, "dur": 44, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 41616, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 41616, "tid": 8589934592, "ts": 1751353154893619, "dur": 114034, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 41616, "tid": 8589934592, "ts": 1751353155007656, "dur": 8, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 41616, "tid": 8589934592, "ts": 1751353155007666, "dur": 2839, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 41616, "tid": 1748, "ts": 1751353156049123, "dur": 10, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 41616, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 41616, "tid": 4294967296, "ts": 1751353154861166, "dur": 1168910, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 41616, "tid": 4294967296, "ts": 1751353154869401, "dur": 12734, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 41616, "tid": 4294967296, "ts": 1751353156030361, "dur": 7393, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 41616, "tid": 4294967296, "ts": 1751353156034469, "dur": 149, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 41616, "tid": 4294967296, "ts": 1751353156037945, "dur": 26, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 41616, "tid": 1748, "ts": 1751353156049135, "dur": 13, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1751353154916206, "dur":2874, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353154919100, "dur":341, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353154919475, "dur":1268, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353154920783, "dur":97, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353154920881, "dur":1092231, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353156013114, "dur":351, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353156013466, "dur":241, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353156013714, "dur":57, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353156013771, "dur":142, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353156014024, "dur":127, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353156014322, "dur":9281, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1751353154920627, "dur":264, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353154920924, "dur":1092163, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353154920689, "dur":223, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353154920913, "dur":92433, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353155013347, "dur":999751, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353154920753, "dur":178, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353154920932, "dur":1092201, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353154920824, "dur":128, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353154920952, "dur":1092137, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353154920680, "dur":222, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353154920903, "dur":90310, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353155012889, "dur":444, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":5, "ts":1751353155011215, "dur":2123, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353155013339, "dur":999778, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353154921719, "dur":1091416, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353154921782, "dur":1091328, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353154921116, "dur":182, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":8, "ts":1751353154920895, "dur":761, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":8, "ts":1751353154922555, "dur":1090128, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":9, "ts":1751353154920937, "dur":1092169, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353154920998, "dur":1092094, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353154921047, "dur":1092077, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353154921106, "dur":1092002, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353154921151, "dur":1091967, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353154921204, "dur":1091909, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353154921253, "dur":1091897, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353154921310, "dur":1091810, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353154921387, "dur":1091739, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353154921446, "dur":1091681, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353154921502, "dur":1091639, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353154921557, "dur":1091582, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353154921614, "dur":1091528, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353154921670, "dur":1091460, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353154920738, "dur":185, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353154920923, "dur":1092173, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353154920808, "dur":132, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353154920941, "dur":1092153, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353156028008, "dur":292, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 41616, "tid": 1748, "ts": 1751353156049942, "dur": 3367, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 41616, "tid": 1748, "ts": 1751353156053364, "dur": 2723, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 41616, "tid": 1748, "ts": 1751353156045309, "dur": 12134, "ph": "X", "name": "Write chrome-trace events", "args": {} },

{ "pid": 13400, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 13400, "tid": 1, "ts": 1751348733492512, "dur": 8896, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 13400, "tid": 1, "ts": 1751348733501413, "dur": 155137, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 13400, "tid": 1, "ts": 1751348733656564, "dur": 4575, "ph": "X", "name": "Write<PERSON>son", "args": {} },
{ "pid": 13400, "tid": 710, "ts": 1751348734599215, "dur": 2486, "ph": "X", "name": "", "args": {} },
{ "pid": 13400, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 13400, "tid": 12884901888, "ts": 1751348733488603, "dur": 29256, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 13400, "tid": 12884901888, "ts": 1751348733517863, "dur": 1064761, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 13400, "tid": 12884901888, "ts": 1751348733520108, "dur": 6446, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 13400, "tid": 12884901888, "ts": 1751348733526563, "dur": 3943, "ph": "X", "name": "ProcessMessages 571", "args": {} },
{ "pid": 13400, "tid": 12884901888, "ts": 1751348733530511, "dur": 1034096, "ph": "X", "name": "ReadAsync 571", "args": {} },
{ "pid": 13400, "tid": 12884901888, "ts": 1751348734564621, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 13400, "tid": 12884901888, "ts": 1751348734564627, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 13400, "tid": 12884901888, "ts": 1751348734564692, "dur": 2550, "ph": "X", "name": "ProcessMessages 12173", "args": {} },
{ "pid": 13400, "tid": 12884901888, "ts": 1751348734567248, "dur": 78, "ph": "X", "name": "ReadAsync 12173", "args": {} },
{ "pid": 13400, "tid": 12884901888, "ts": 1751348734567348, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 13400, "tid": 12884901888, "ts": 1751348734567358, "dur": 1593, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 13400, "tid": 12884901888, "ts": 1751348734568966, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 13400, "tid": 12884901888, "ts": 1751348734568969, "dur": 1620, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 13400, "tid": 12884901888, "ts": 1751348734570602, "dur": 878, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 13400, "tid": 12884901888, "ts": 1751348734571488, "dur": 10799, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 13400, "tid": 710, "ts": 1751348734601707, "dur": 50, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 13400, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 13400, "tid": 8589934592, "ts": 1751348733482888, "dur": 178365, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 13400, "tid": 8589934592, "ts": 1751348733661257, "dur": 7, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 13400, "tid": 8589934592, "ts": 1751348733661266, "dur": 2386, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 13400, "tid": 710, "ts": 1751348734601760, "dur": 9, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 13400, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 13400, "tid": 4294967296, "ts": 1751348733446119, "dur": 1138073, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 13400, "tid": 4294967296, "ts": 1751348733455113, "dur": 14476, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 13400, "tid": 4294967296, "ts": 1751348734584454, "dur": 6091, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 13400, "tid": 4294967296, "ts": 1751348734588169, "dur": 140, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 13400, "tid": 4294967296, "ts": 1751348734590696, "dur": 19, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 13400, "tid": 710, "ts": 1751348734601772, "dur": 11, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1751348733512727, "dur":52, "ph":"X", "name": "IPC_Client_InitializeAndConnectToParent",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751348733512839, "dur":2958, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751348733515814, "dur":365, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751348733516220, "dur":1347, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751348733517612, "dur":108, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751348733517720, "dur":1049750, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751348734567472, "dur":661, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751348734568134, "dur":179, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751348734568314, "dur":219, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751348734568601, "dur":65, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751348734569007, "dur":8588, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1751348733517382, "dur":349, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751348733517966, "dur":151, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1751348733517748, "dur":684, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":1, "ts":1751348733519274, "dur":1047724, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":2, "ts":1751348733517516, "dur":269, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751348733517787, "dur":145875, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751348733665433, "dur":498, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":2, "ts":1751348733663663, "dur":2273, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751348733665937, "dur":901495, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751348733519266, "dur":1048182, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751348733519424, "dur":1048021, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751348733517792, "dur":148158, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751348733665952, "dur":901491, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751348733517857, "dur":1049566, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751348733517929, "dur":1049463, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751348733518001, "dur":1049452, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751348733518055, "dur":1049344, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751348733518265, "dur":1049144, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751348733518349, "dur":1049102, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751348733518422, "dur":1049012, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751348733518524, "dur":1048881, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751348733518707, "dur":1048755, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751348733518770, "dur":1048713, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751348733518825, "dur":1048588, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751348733518925, "dur":1048540, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751348733519008, "dur":1048429, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751348733519057, "dur":1048360, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751348733519109, "dur":1048319, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751348733519170, "dur":1048250, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751348733517582, "dur":218, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751348733517801, "dur":1049638, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751348733519341, "dur":1048170, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751348733517666, "dur":156, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751348733517824, "dur":1049602, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751348734581610, "dur":282, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 13400, "tid": 710, "ts": 1751348734602612, "dur": 3231, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 13400, "tid": 710, "ts": 1751348734605895, "dur": 2644, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 13400, "tid": 710, "ts": 1751348734597761, "dur": 12116, "ph": "X", "name": "Write chrome-trace events", "args": {} },

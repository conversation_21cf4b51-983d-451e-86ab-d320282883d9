using System.Collections;
using UnityEngine;

namespace AdvancedHelicopterControllerwithShooting
{
    public class MissileScript : MonoBehaviour
    {
        public GameObject explosionPrefab;
        public int DamagePower = 25;
        public Transform particle_following;
        public bool isEnemyMissile = false;
        public Collider collider;
        public float detectionRange = 80f;
        public float maxAutoAimingAngle = 45f;
        public float rotationSpeedForAutoAiming = 5f;
        public Rigidbody rigidbody;

        private IEnumerator Start()
        {
            yield return new WaitForSeconds(0.1f);
            collider.enabled = true;
            if (HelicopterSystemManager.Instance.MissilesAutoAim && !isEnemyMissile)
            {
                rigidbody.useGravity = false;
                Destroy(gameObject, 6);
            }
            else
            {
                Destroy(gameObject, 4);
            }
        }

        void FixedUpdate()
        {
            if (HelicopterSystemManager.Instance.MissilesAutoAim && !isEnemyMissile)
            {
                // F�zenin �n�ndeki hedefleri alg�la
                Collider[] hits = Physics.OverlapSphere(transform.position, detectionRange);

                Transform closestTarget = null;
                float closestAngle = Mathf.Infinity;

                foreach (var hit in hits)
                {
                    if (!hit.CompareTag("Enemy")) continue;
                    Vector3 directionToTarget = (hit.transform.position - transform.position).normalized;
                    float angle = Vector3.Angle(transform.forward, directionToTarget);

                    if (angle < maxAutoAimingAngle && angle < closestAngle)
                    {
                        closestTarget = hit.transform;
                        closestAngle = angle;
                    }
                }

                // Hedef bulunduysa ona do�ru y�nel
                if (closestTarget != null)
                {
                    Vector3 targetDirection = (closestTarget.position - transform.position).normalized;
                    Quaternion targetRotation = Quaternion.LookRotation(targetDirection);
                    rigidbody.rotation = Quaternion.Slerp(rigidbody.rotation, targetRotation, rotationSpeedForAutoAiming * Time.fixedDeltaTime);
                }

                // F�zenin ileriye do�ru hareketi
                rigidbody.linearVelocity = transform.forward * 120;
            }
        }



        private void OnCollisionEnter(Collision collision)
        {
            if(isEnemyMissile)
            {
                if (collision.collider.CompareTag("Ground") || collision.collider.CompareTag("Helicopter"))
                {
                    Instantiate(explosionPrefab, transform.position, Quaternion.identity);
                    if (particle_following != null)
                    {
                        particle_following.parent = null;
                    }
                    if(collision.collider.CompareTag("Helicopter"))
                    {
                        HelicopterController.Instance.GetDamage(DamagePower);
                    }
                    Destroy(gameObject);
                }
            }
            else
            {
                if (collision.collider.CompareTag("Ground") || collision.collider.CompareTag("Enemy"))
                {
                    Instantiate(explosionPrefab, transform.position, Quaternion.identity);
                    if (particle_following != null)
                    {
                        particle_following.parent = null;
                    }
                    Vector3 explosionPos = transform.position;
                    Collider[] colliders = Physics.OverlapSphere(explosionPos, 15);
                    foreach (Collider hit in colliders)
                    {
                        if (hit.CompareTag("Enemy"))
                        {
                            hit.GetComponent<EnemyAI>().GetDamage(DamagePower);
                        }
                    }
                    Destroy(gameObject);
                }
                else if (collision.collider.CompareTag("Collapsable"))
                {
                    Instantiate(explosionPrefab, transform.position, Quaternion.identity);
                    Vector3 explosionPos = transform.position;
                    Collider[] colliders = Physics.OverlapSphere(explosionPos, 10);
                    foreach (Collider hit in colliders)
                    {
                        Rigidbody rb = hit.GetComponent<Rigidbody>();
                        if (rb != null)
                        {
                            rb.isKinematic = false;
                            rb.useGravity = true;
                            rb.AddExplosionForce(100, explosionPos, 10, 3.0F);
                            Destroy(hit.gameObject, 10);
                        }
                    }
                    Destroy(gameObject);
                }
            }
        }
    }
}

# C/C++ build system timings
generate_cxx_metadata
  [gap of 264ms]
  create-invalidation-state 22ms
  generate-prefab-packages
    [gap of 22ms]
    exec-prefab 1456ms
    [gap of 318ms]
  generate-prefab-packages completed in 1796ms
  execute-generate-process
    [gap of 247ms]
    exec-configure 9994ms
    [gap of 389ms]
  execute-generate-process completed in 10630ms
  [gap of 109ms]
  write-metadata-json-to-file 11ms
generate_cxx_metadata completed in 12853ms


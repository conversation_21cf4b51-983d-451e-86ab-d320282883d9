void RegisterAllStrippedInternalCalls()
{
	//Start Registrations for type : Unity.Burst.LowLevel.BurstCompilerService

		//System.Void* Unity.Burst.LowLevel.BurstCompilerService::GetOrCreateSharedMemory(UnityEngine.Hash128&,System.UInt32,System.UInt32)
		void Register_Unity_Burst_LowLevel_BurstCompilerService_GetOrCreateSharedMemory();
		Register_Unity_Burst_LowLevel_BurstCompilerService_GetOrCreateSharedMemory();

	//End Registrations for type : Unity.Burst.LowLevel.BurstCompilerService

	//Start Registrations for type : Unity.Collections.LowLevel.Unsafe.UnsafeUtility

		//System.Int32 Unity.Collections.LowLevel.Unsafe.UnsafeUtility::MemCmp(System.Void*,System.Void*,System.Int64)
		void Register_Unity_Collections_LowLevel_Unsafe_UnsafeUtility_MemCmp();
		Register_Unity_Collections_LowLevel_Unsafe_UnsafeUtility_MemCmp();

		//System.Void Unity.Collections.LowLevel.Unsafe.UnsafeUtility::Free(System.Void*,Unity.Collections.Allocator)
		void Register_Unity_Collections_LowLevel_Unsafe_UnsafeUtility_Free();
		Register_Unity_Collections_LowLevel_Unsafe_UnsafeUtility_Free();

		//System.Void Unity.Collections.LowLevel.Unsafe.UnsafeUtility::FreeTracked(System.Void*,Unity.Collections.Allocator)
		void Register_Unity_Collections_LowLevel_Unsafe_UnsafeUtility_FreeTracked();
		Register_Unity_Collections_LowLevel_Unsafe_UnsafeUtility_FreeTracked();

		//System.Void Unity.Collections.LowLevel.Unsafe.UnsafeUtility::MemCpy(System.Void*,System.Void*,System.Int64)
		void Register_Unity_Collections_LowLevel_Unsafe_UnsafeUtility_MemCpy();
		Register_Unity_Collections_LowLevel_Unsafe_UnsafeUtility_MemCpy();

		//System.Void Unity.Collections.LowLevel.Unsafe.UnsafeUtility::MemCpyStride(System.Void*,System.Int32,System.Void*,System.Int32,System.Int32,System.Int32)
		void Register_Unity_Collections_LowLevel_Unsafe_UnsafeUtility_MemCpyStride();
		Register_Unity_Collections_LowLevel_Unsafe_UnsafeUtility_MemCpyStride();

		//System.Void Unity.Collections.LowLevel.Unsafe.UnsafeUtility::MemMove(System.Void*,System.Void*,System.Int64)
		void Register_Unity_Collections_LowLevel_Unsafe_UnsafeUtility_MemMove();
		Register_Unity_Collections_LowLevel_Unsafe_UnsafeUtility_MemMove();

		//System.Void Unity.Collections.LowLevel.Unsafe.UnsafeUtility::MemSet(System.Void*,System.Byte,System.Int64)
		void Register_Unity_Collections_LowLevel_Unsafe_UnsafeUtility_MemSet();
		Register_Unity_Collections_LowLevel_Unsafe_UnsafeUtility_MemSet();

		//System.Void* Unity.Collections.LowLevel.Unsafe.UnsafeUtility::Malloc(System.Int64,System.Int32,Unity.Collections.Allocator)
		void Register_Unity_Collections_LowLevel_Unsafe_UnsafeUtility_Malloc();
		Register_Unity_Collections_LowLevel_Unsafe_UnsafeUtility_Malloc();

		//System.Void* Unity.Collections.LowLevel.Unsafe.UnsafeUtility::MallocTracked(System.Int64,System.Int32,Unity.Collections.Allocator,System.Int32)
		void Register_Unity_Collections_LowLevel_Unsafe_UnsafeUtility_MallocTracked();
		Register_Unity_Collections_LowLevel_Unsafe_UnsafeUtility_MallocTracked();

	//End Registrations for type : Unity.Collections.LowLevel.Unsafe.UnsafeUtility

	//Start Registrations for type : Unity.Hierarchy.Hierarchy

		//System.Boolean Unity.Hierarchy.Hierarchy::Exists_Injected(System.IntPtr,Unity.Hierarchy.HierarchyNode&)
		void Register_Unity_Hierarchy_Hierarchy_Exists_Injected();
		Register_Unity_Hierarchy_Hierarchy_Exists_Injected();

		//System.Boolean Unity.Hierarchy.Hierarchy::SetParent_Injected(System.IntPtr,Unity.Hierarchy.HierarchyNode&,Unity.Hierarchy.HierarchyNode&)
		void Register_Unity_Hierarchy_Hierarchy_SetParent_Injected();
		Register_Unity_Hierarchy_Hierarchy_SetParent_Injected();

		//System.Boolean Unity.Hierarchy.Hierarchy::get_UpdateNeeded_Injected(System.IntPtr)
		void Register_Unity_Hierarchy_Hierarchy_get_UpdateNeeded_Injected();
		Register_Unity_Hierarchy_Hierarchy_get_UpdateNeeded_Injected();

		//System.Int32 Unity.Hierarchy.Hierarchy::GetChildrenCount_Injected(System.IntPtr,Unity.Hierarchy.HierarchyNode&)
		void Register_Unity_Hierarchy_Hierarchy_GetChildrenCount_Injected();
		Register_Unity_Hierarchy_Hierarchy_GetChildrenCount_Injected();

		//System.Int32 Unity.Hierarchy.Hierarchy::GetNodeTypeHandlersBaseCount_Injected(System.IntPtr)
		void Register_Unity_Hierarchy_Hierarchy_GetNodeTypeHandlersBaseCount_Injected();
		Register_Unity_Hierarchy_Hierarchy_GetNodeTypeHandlersBaseCount_Injected();

		//System.Int32 Unity.Hierarchy.Hierarchy::GetNodeTypeHandlersBaseSpan_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_Unity_Hierarchy_Hierarchy_GetNodeTypeHandlersBaseSpan_Injected();
		Register_Unity_Hierarchy_Hierarchy_GetNodeTypeHandlersBaseSpan_Injected();

		//System.IntPtr Unity.Hierarchy.Hierarchy::Create(System.IntPtr,System.IntPtr&,System.IntPtr&)
		void Register_Unity_Hierarchy_Hierarchy_Create();
		Register_Unity_Hierarchy_Hierarchy_Create();

		//System.IntPtr Unity.Hierarchy.Hierarchy::EnumerateChildrenPtr_Injected(System.IntPtr,Unity.Hierarchy.HierarchyNode&)
		void Register_Unity_Hierarchy_Hierarchy_EnumerateChildrenPtr_Injected();
		Register_Unity_Hierarchy_Hierarchy_EnumerateChildrenPtr_Injected();

		//System.Void Unity.Hierarchy.Hierarchy::AddNode_Injected(System.IntPtr,Unity.Hierarchy.HierarchyNode&,Unity.Hierarchy.HierarchyNode&)
		void Register_Unity_Hierarchy_Hierarchy_AddNode_Injected();
		Register_Unity_Hierarchy_Hierarchy_AddNode_Injected();

		//System.Void Unity.Hierarchy.Hierarchy::Destroy(System.IntPtr)
		void Register_Unity_Hierarchy_Hierarchy_Destroy();
		Register_Unity_Hierarchy_Hierarchy_Destroy();

		//System.Void Unity.Hierarchy.Hierarchy::GetChildren_Injected(System.IntPtr,Unity.Hierarchy.HierarchyNode&,UnityEngine.Bindings.BlittableArrayWrapper&)
		void Register_Unity_Hierarchy_Hierarchy_GetChildren_Injected();
		Register_Unity_Hierarchy_Hierarchy_GetChildren_Injected();

		//System.Void Unity.Hierarchy.Hierarchy::GetOrCreateProperty_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&,Unity.Hierarchy.HierarchyPropertyDescriptor&,Unity.Hierarchy.HierarchyPropertyId&)
		void Register_Unity_Hierarchy_Hierarchy_GetOrCreateProperty_Injected();
		Register_Unity_Hierarchy_Hierarchy_GetOrCreateProperty_Injected();

		//System.Void Unity.Hierarchy.Hierarchy::GetParent_Injected(System.IntPtr,Unity.Hierarchy.HierarchyNode&,Unity.Hierarchy.HierarchyNode&)
		void Register_Unity_Hierarchy_Hierarchy_GetParent_Injected();
		Register_Unity_Hierarchy_Hierarchy_GetParent_Injected();

		//System.Void Unity.Hierarchy.Hierarchy::SetPropertyRaw_Injected(System.IntPtr,Unity.Hierarchy.HierarchyPropertyId&,Unity.Hierarchy.HierarchyNode&,System.Void*,System.Int32)
		void Register_Unity_Hierarchy_Hierarchy_SetPropertyRaw_Injected();
		Register_Unity_Hierarchy_Hierarchy_SetPropertyRaw_Injected();

		//System.Void Unity.Hierarchy.Hierarchy::SetSortIndex_Injected(System.IntPtr,Unity.Hierarchy.HierarchyNode&,System.Int32)
		void Register_Unity_Hierarchy_Hierarchy_SetSortIndex_Injected();
		Register_Unity_Hierarchy_Hierarchy_SetSortIndex_Injected();

		//System.Void Unity.Hierarchy.Hierarchy::SortChildren_Injected(System.IntPtr,Unity.Hierarchy.HierarchyNode&,System.Boolean)
		void Register_Unity_Hierarchy_Hierarchy_SortChildren_Injected();
		Register_Unity_Hierarchy_Hierarchy_SortChildren_Injected();

		//System.Void Unity.Hierarchy.Hierarchy::Update_Injected(System.IntPtr)
		void Register_Unity_Hierarchy_Hierarchy_Update_Injected();
		Register_Unity_Hierarchy_Hierarchy_Update_Injected();

		//System.Void* Unity.Hierarchy.Hierarchy::GetPropertyRaw_Injected(System.IntPtr,Unity.Hierarchy.HierarchyPropertyId&,Unity.Hierarchy.HierarchyNode&,System.Int32&)
		void Register_Unity_Hierarchy_Hierarchy_GetPropertyRaw_Injected();
		Register_Unity_Hierarchy_Hierarchy_GetPropertyRaw_Injected();

	//End Registrations for type : Unity.Hierarchy.Hierarchy

	//Start Registrations for type : Unity.Hierarchy.HierarchyCommandList

		//System.Void Unity.Hierarchy.HierarchyCommandList::Destroy(System.IntPtr)
		void Register_Unity_Hierarchy_HierarchyCommandList_Destroy();
		Register_Unity_Hierarchy_HierarchyCommandList_Destroy();

	//End Registrations for type : Unity.Hierarchy.HierarchyCommandList

	//Start Registrations for type : Unity.Hierarchy.HierarchyFlattened

		//System.Boolean Unity.Hierarchy.HierarchyFlattened::Contains_Injected(System.IntPtr,Unity.Hierarchy.HierarchyNode&)
		void Register_Unity_Hierarchy_HierarchyFlattened_Contains_Injected();
		Register_Unity_Hierarchy_HierarchyFlattened_Contains_Injected();

		//System.Boolean Unity.Hierarchy.HierarchyFlattened::get_UpdateNeeded_Injected(System.IntPtr)
		void Register_Unity_Hierarchy_HierarchyFlattened_get_UpdateNeeded_Injected();
		Register_Unity_Hierarchy_HierarchyFlattened_get_UpdateNeeded_Injected();

		//System.Int32 Unity.Hierarchy.HierarchyFlattened::GetChildrenCount_Injected(System.IntPtr,Unity.Hierarchy.HierarchyNode&)
		void Register_Unity_Hierarchy_HierarchyFlattened_GetChildrenCount_Injected();
		Register_Unity_Hierarchy_HierarchyFlattened_GetChildrenCount_Injected();

		//System.Int32 Unity.Hierarchy.HierarchyFlattened::IndexOf_Injected(System.IntPtr,Unity.Hierarchy.HierarchyNode&)
		void Register_Unity_Hierarchy_HierarchyFlattened_IndexOf_Injected();
		Register_Unity_Hierarchy_HierarchyFlattened_IndexOf_Injected();

		//System.IntPtr Unity.Hierarchy.HierarchyFlattened::Create_Injected(System.IntPtr,System.IntPtr,System.IntPtr&,System.Int32&,System.Int32&)
		void Register_Unity_Hierarchy_HierarchyFlattened_Create_Injected();
		Register_Unity_Hierarchy_HierarchyFlattened_Create_Injected();

		//System.Void Unity.Hierarchy.HierarchyFlattened::Destroy(System.IntPtr)
		void Register_Unity_Hierarchy_HierarchyFlattened_Destroy();
		Register_Unity_Hierarchy_HierarchyFlattened_Destroy();

		//System.Void Unity.Hierarchy.HierarchyFlattened::Update_Injected(System.IntPtr)
		void Register_Unity_Hierarchy_HierarchyFlattened_Update_Injected();
		Register_Unity_Hierarchy_HierarchyFlattened_Update_Injected();

	//End Registrations for type : Unity.Hierarchy.HierarchyFlattened

	//Start Registrations for type : Unity.Hierarchy.HierarchyNodeTypeHandlerBase

		//System.Boolean Unity.Hierarchy.HierarchyNodeTypeHandlerBase::ChangesPending_Injected(System.IntPtr)
		void Register_Unity_Hierarchy_HierarchyNodeTypeHandlerBase_ChangesPending_Injected();
		Register_Unity_Hierarchy_HierarchyNodeTypeHandlerBase_ChangesPending_Injected();

		//System.Boolean Unity.Hierarchy.HierarchyNodeTypeHandlerBase::IntegrateChanges_Injected(System.IntPtr,System.IntPtr)
		void Register_Unity_Hierarchy_HierarchyNodeTypeHandlerBase_IntegrateChanges_Injected();
		Register_Unity_Hierarchy_HierarchyNodeTypeHandlerBase_IntegrateChanges_Injected();

		//System.Boolean Unity.Hierarchy.HierarchyNodeTypeHandlerBase::SearchMatch_Injected(System.IntPtr,Unity.Hierarchy.HierarchyNode&)
		void Register_Unity_Hierarchy_HierarchyNodeTypeHandlerBase_SearchMatch_Injected();
		Register_Unity_Hierarchy_HierarchyNodeTypeHandlerBase_SearchMatch_Injected();

		//System.Void Unity.Hierarchy.HierarchyNodeTypeHandlerBase::GetNodeTypeName_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_Unity_Hierarchy_HierarchyNodeTypeHandlerBase_GetNodeTypeName_Injected();
		Register_Unity_Hierarchy_HierarchyNodeTypeHandlerBase_GetNodeTypeName_Injected();

		//System.Void Unity.Hierarchy.HierarchyNodeTypeHandlerBase::SearchBegin_Injected(System.IntPtr,Unity.Hierarchy.HierarchySearchQueryDescriptor)
		void Register_Unity_Hierarchy_HierarchyNodeTypeHandlerBase_SearchBegin_Injected();
		Register_Unity_Hierarchy_HierarchyNodeTypeHandlerBase_SearchBegin_Injected();

		//System.Void Unity.Hierarchy.HierarchyNodeTypeHandlerBase::SearchEnd_Injected(System.IntPtr)
		void Register_Unity_Hierarchy_HierarchyNodeTypeHandlerBase_SearchEnd_Injected();
		Register_Unity_Hierarchy_HierarchyNodeTypeHandlerBase_SearchEnd_Injected();

		//Unity.Hierarchy.HierarchyNodeFlags Unity.Hierarchy.HierarchyNodeTypeHandlerBase::GetDefaultNodeFlags_Injected(System.IntPtr,Unity.Hierarchy.HierarchyNode&,Unity.Hierarchy.HierarchyNodeFlags)
		void Register_Unity_Hierarchy_HierarchyNodeTypeHandlerBase_GetDefaultNodeFlags_Injected();
		Register_Unity_Hierarchy_HierarchyNodeTypeHandlerBase_GetDefaultNodeFlags_Injected();

	//End Registrations for type : Unity.Hierarchy.HierarchyNodeTypeHandlerBase

	//Start Registrations for type : Unity.Hierarchy.HierarchyViewModel

		//System.Boolean Unity.Hierarchy.HierarchyViewModel::Contains_Injected(System.IntPtr,Unity.Hierarchy.HierarchyNode&)
		void Register_Unity_Hierarchy_HierarchyViewModel_Contains_Injected();
		Register_Unity_Hierarchy_HierarchyViewModel_Contains_Injected();

		//System.Boolean Unity.Hierarchy.HierarchyViewModel::HasAllFlagsNode_Injected(System.IntPtr,Unity.Hierarchy.HierarchyNode&,Unity.Hierarchy.HierarchyNodeFlags)
		void Register_Unity_Hierarchy_HierarchyViewModel_HasAllFlagsNode_Injected();
		Register_Unity_Hierarchy_HierarchyViewModel_HasAllFlagsNode_Injected();

		//System.Boolean Unity.Hierarchy.HierarchyViewModel::get_UpdateNeeded_Injected(System.IntPtr)
		void Register_Unity_Hierarchy_HierarchyViewModel_get_UpdateNeeded_Injected();
		Register_Unity_Hierarchy_HierarchyViewModel_get_UpdateNeeded_Injected();

		//System.Int32 Unity.Hierarchy.HierarchyViewModel::GetChildrenCount_Injected(System.IntPtr,Unity.Hierarchy.HierarchyNode&)
		void Register_Unity_Hierarchy_HierarchyViewModel_GetChildrenCount_Injected();
		Register_Unity_Hierarchy_HierarchyViewModel_GetChildrenCount_Injected();

		//System.Int32 Unity.Hierarchy.HierarchyViewModel::IndexOf_Injected(System.IntPtr,Unity.Hierarchy.HierarchyNode&)
		void Register_Unity_Hierarchy_HierarchyViewModel_IndexOf_Injected();
		Register_Unity_Hierarchy_HierarchyViewModel_IndexOf_Injected();

		//System.IntPtr Unity.Hierarchy.HierarchyViewModel::Create_Injected(System.IntPtr,System.IntPtr,Unity.Hierarchy.HierarchyNodeFlags,System.IntPtr&,System.Int32&,System.Int32&)
		void Register_Unity_Hierarchy_HierarchyViewModel_Create_Injected();
		Register_Unity_Hierarchy_HierarchyViewModel_Create_Injected();

		//System.Void Unity.Hierarchy.HierarchyViewModel::ClearFlagsNode_Injected(System.IntPtr,Unity.Hierarchy.HierarchyNode&,Unity.Hierarchy.HierarchyNodeFlags,System.Boolean)
		void Register_Unity_Hierarchy_HierarchyViewModel_ClearFlagsNode_Injected();
		Register_Unity_Hierarchy_HierarchyViewModel_ClearFlagsNode_Injected();

		//System.Void Unity.Hierarchy.HierarchyViewModel::Destroy(System.IntPtr)
		void Register_Unity_Hierarchy_HierarchyViewModel_Destroy();
		Register_Unity_Hierarchy_HierarchyViewModel_Destroy();

		//System.Void Unity.Hierarchy.HierarchyViewModel::SetFlagsNode_Injected(System.IntPtr,Unity.Hierarchy.HierarchyNode&,Unity.Hierarchy.HierarchyNodeFlags,System.Boolean)
		void Register_Unity_Hierarchy_HierarchyViewModel_SetFlagsNode_Injected();
		Register_Unity_Hierarchy_HierarchyViewModel_SetFlagsNode_Injected();

		//System.Void Unity.Hierarchy.HierarchyViewModel::Update_Injected(System.IntPtr)
		void Register_Unity_Hierarchy_HierarchyViewModel_Update_Injected();
		Register_Unity_Hierarchy_HierarchyViewModel_Update_Injected();

		//Unity.Hierarchy.HierarchySearchQueryDescriptor Unity.Hierarchy.HierarchyViewModel::get_Query_Injected(System.IntPtr)
		void Register_Unity_Hierarchy_HierarchyViewModel_get_Query_Injected();
		Register_Unity_Hierarchy_HierarchyViewModel_get_Query_Injected();

	//End Registrations for type : Unity.Hierarchy.HierarchyViewModel

	//Start Registrations for type : Unity.IntegerTime.RationalTimeExtensions

		//System.Void Unity.IntegerTime.RationalTimeExtensions::Convert_Injected(Unity.IntegerTime.RationalTime&,Unity.IntegerTime.RationalTime/TicksPerSecond&,Unity.IntegerTime.RationalTime&)
		void Register_Unity_IntegerTime_RationalTimeExtensions_Convert_Injected();
		Register_Unity_IntegerTime_RationalTimeExtensions_Convert_Injected();

	//End Registrations for type : Unity.IntegerTime.RationalTimeExtensions

	//Start Registrations for type : Unity.Jobs.JobHandle

		//System.Void Unity.Jobs.JobHandle::CombineDependenciesInternalPtr_Injected(System.Void*,System.Int32,Unity.Jobs.JobHandle&)
		void Register_Unity_Jobs_JobHandle_CombineDependenciesInternalPtr_Injected();
		Register_Unity_Jobs_JobHandle_CombineDependenciesInternalPtr_Injected();

		//System.Void Unity.Jobs.JobHandle::ScheduleBatchedJobs()
		void Register_Unity_Jobs_JobHandle_ScheduleBatchedJobs();
		Register_Unity_Jobs_JobHandle_ScheduleBatchedJobs();

		//System.Void Unity.Jobs.JobHandle::ScheduleBatchedJobsAndComplete(Unity.Jobs.JobHandle&)
		void Register_Unity_Jobs_JobHandle_ScheduleBatchedJobsAndComplete();
		Register_Unity_Jobs_JobHandle_ScheduleBatchedJobsAndComplete();

	//End Registrations for type : Unity.Jobs.JobHandle

	//Start Registrations for type : Unity.Jobs.LowLevel.Unsafe.JobsUtility

		//System.Boolean Unity.Jobs.LowLevel.Unsafe.JobsUtility::GetWorkStealingRange(Unity.Jobs.LowLevel.Unsafe.JobRanges&,System.Int32,System.Int32&,System.Int32&)
		void Register_Unity_Jobs_LowLevel_Unsafe_JobsUtility_GetWorkStealingRange();
		Register_Unity_Jobs_LowLevel_Unsafe_JobsUtility_GetWorkStealingRange();

		//System.Boolean Unity.Jobs.LowLevel.Unsafe.JobsUtility::get_IsExecutingJob()
		void Register_Unity_Jobs_LowLevel_Unsafe_JobsUtility_get_IsExecutingJob();
		Register_Unity_Jobs_LowLevel_Unsafe_JobsUtility_get_IsExecutingJob();

		//System.Int32 Unity.Jobs.LowLevel.Unsafe.JobsUtility::get_ThreadIndex()
		void Register_Unity_Jobs_LowLevel_Unsafe_JobsUtility_get_ThreadIndex();
		Register_Unity_Jobs_LowLevel_Unsafe_JobsUtility_get_ThreadIndex();

		//System.Int32 Unity.Jobs.LowLevel.Unsafe.JobsUtility::get_ThreadIndexCount()
		void Register_Unity_Jobs_LowLevel_Unsafe_JobsUtility_get_ThreadIndexCount();
		Register_Unity_Jobs_LowLevel_Unsafe_JobsUtility_get_ThreadIndexCount();

		//System.IntPtr Unity.Jobs.LowLevel.Unsafe.JobsUtility::CreateJobReflectionData(System.Type,System.Type,System.Object,System.Object,System.Object)
		void Register_Unity_Jobs_LowLevel_Unsafe_JobsUtility_CreateJobReflectionData();
		Register_Unity_Jobs_LowLevel_Unsafe_JobsUtility_CreateJobReflectionData();

		//System.Void Unity.Jobs.LowLevel.Unsafe.JobsUtility::ScheduleParallelFor_Injected(Unity.Jobs.LowLevel.Unsafe.JobsUtility/JobScheduleParameters&,System.Int32,System.Int32,Unity.Jobs.JobHandle&)
		void Register_Unity_Jobs_LowLevel_Unsafe_JobsUtility_ScheduleParallelFor_Injected();
		Register_Unity_Jobs_LowLevel_Unsafe_JobsUtility_ScheduleParallelFor_Injected();

	//End Registrations for type : Unity.Jobs.LowLevel.Unsafe.JobsUtility

	//Start Registrations for type : Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility

		//System.IntPtr Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::CreateMarker_Injected(UnityEngine.Bindings.ManagedSpanWrapper&,System.UInt16,Unity.Profiling.LowLevel.MarkerFlags,System.Int32)
		void Register_Unity_Profiling_LowLevel_Unsafe_ProfilerUnsafeUtility_CreateMarker_Injected();
		Register_Unity_Profiling_LowLevel_Unsafe_ProfilerUnsafeUtility_CreateMarker_Injected();

		//System.IntPtr Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::CreateMarker__Unmanaged(System.Byte*,System.Int32,System.UInt16,Unity.Profiling.LowLevel.MarkerFlags,System.Int32)
		void Register_Unity_Profiling_LowLevel_Unsafe_ProfilerUnsafeUtility_CreateMarker__Unmanaged();
		Register_Unity_Profiling_LowLevel_Unsafe_ProfilerUnsafeUtility_CreateMarker__Unmanaged();

		//System.UInt16 Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::CreateCategory__Unmanaged(System.Byte*,System.Int32,Unity.Profiling.ProfilerCategoryColor)
		void Register_Unity_Profiling_LowLevel_Unsafe_ProfilerUnsafeUtility_CreateCategory__Unmanaged();
		Register_Unity_Profiling_LowLevel_Unsafe_ProfilerUnsafeUtility_CreateCategory__Unmanaged();

		//System.Void Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::BeginSample(System.IntPtr)
		void Register_Unity_Profiling_LowLevel_Unsafe_ProfilerUnsafeUtility_BeginSample();
		Register_Unity_Profiling_LowLevel_Unsafe_ProfilerUnsafeUtility_BeginSample();

		//System.Void Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::EndSample(System.IntPtr)
		void Register_Unity_Profiling_LowLevel_Unsafe_ProfilerUnsafeUtility_EndSample();
		Register_Unity_Profiling_LowLevel_Unsafe_ProfilerUnsafeUtility_EndSample();

		//System.Void Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::GetCategoryDescription_Injected(System.UInt16,Unity.Profiling.LowLevel.Unsafe.ProfilerCategoryDescription&)
		void Register_Unity_Profiling_LowLevel_Unsafe_ProfilerUnsafeUtility_GetCategoryDescription_Injected();
		Register_Unity_Profiling_LowLevel_Unsafe_ProfilerUnsafeUtility_GetCategoryDescription_Injected();

		//System.Void Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::SetMarkerMetadata__Unmanaged(System.IntPtr,System.Int32,System.Byte*,System.Int32,System.Byte,System.Byte)
		void Register_Unity_Profiling_LowLevel_Unsafe_ProfilerUnsafeUtility_SetMarkerMetadata__Unmanaged();
		Register_Unity_Profiling_LowLevel_Unsafe_ProfilerUnsafeUtility_SetMarkerMetadata__Unmanaged();

		//System.Void* Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::CreateCounterValue__Unmanaged(System.IntPtr&,System.Byte*,System.Int32,System.UInt16,Unity.Profiling.LowLevel.MarkerFlags,System.Byte,System.Byte,System.Int32,Unity.Profiling.ProfilerCounterOptions)
		void Register_Unity_Profiling_LowLevel_Unsafe_ProfilerUnsafeUtility_CreateCounterValue__Unmanaged();
		Register_Unity_Profiling_LowLevel_Unsafe_ProfilerUnsafeUtility_CreateCounterValue__Unmanaged();

	//End Registrations for type : Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility

	//Start Registrations for type : UnityEngine.Android.AndroidApplication

		//System.IntPtr UnityEngine.Android.AndroidApplication::get_UnityPlayerRaw()
		void Register_UnityEngine_Android_AndroidApplication_get_UnityPlayerRaw();
		Register_UnityEngine_Android_AndroidApplication_get_UnityPlayerRaw();

	//End Registrations for type : UnityEngine.Android.AndroidApplication

	//Start Registrations for type : UnityEngine.Android.AndroidGame

		//System.Void UnityEngine.Android.AndroidGame::StopLoading(System.Int32)
		void Register_UnityEngine_Android_AndroidGame_StopLoading();
		Register_UnityEngine_Android_AndroidGame_StopLoading();

	//End Registrations for type : UnityEngine.Android.AndroidGame

	//Start Registrations for type : UnityEngine.AndroidJNI

		//System.Boolean UnityEngine.AndroidJNI::CallBooleanMethodUnsafe(System.IntPtr,System.IntPtr,UnityEngine.jvalue*)
		void Register_UnityEngine_AndroidJNI_CallBooleanMethodUnsafe();
		Register_UnityEngine_AndroidJNI_CallBooleanMethodUnsafe();

		//System.Boolean UnityEngine.AndroidJNI::CallStaticBooleanMethodUnsafe(System.IntPtr,System.IntPtr,UnityEngine.jvalue*)
		void Register_UnityEngine_AndroidJNI_CallStaticBooleanMethodUnsafe();
		Register_UnityEngine_AndroidJNI_CallStaticBooleanMethodUnsafe();

		//System.Boolean UnityEngine.AndroidJNI::GetBooleanArrayElement(System.IntPtr,System.Int32)
		void Register_UnityEngine_AndroidJNI_GetBooleanArrayElement();
		Register_UnityEngine_AndroidJNI_GetBooleanArrayElement();

		//System.Boolean UnityEngine.AndroidJNI::GetBooleanField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetBooleanField();
		Register_UnityEngine_AndroidJNI_GetBooleanField();

		//System.Boolean UnityEngine.AndroidJNI::GetStaticBooleanField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetStaticBooleanField();
		Register_UnityEngine_AndroidJNI_GetStaticBooleanField();

		//System.Boolean UnityEngine.AndroidJNI::IsAssignableFrom(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_IsAssignableFrom();
		Register_UnityEngine_AndroidJNI_IsAssignableFrom();

		//System.Boolean UnityEngine.AndroidJNI::IsInstanceOf(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_IsInstanceOf();
		Register_UnityEngine_AndroidJNI_IsInstanceOf();

		//System.Boolean UnityEngine.AndroidJNI::IsSameObject(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_IsSameObject();
		Register_UnityEngine_AndroidJNI_IsSameObject();

		//System.Char UnityEngine.AndroidJNI::CallCharMethodUnsafe(System.IntPtr,System.IntPtr,UnityEngine.jvalue*)
		void Register_UnityEngine_AndroidJNI_CallCharMethodUnsafe();
		Register_UnityEngine_AndroidJNI_CallCharMethodUnsafe();

		//System.Char UnityEngine.AndroidJNI::CallStaticCharMethodUnsafe(System.IntPtr,System.IntPtr,UnityEngine.jvalue*)
		void Register_UnityEngine_AndroidJNI_CallStaticCharMethodUnsafe();
		Register_UnityEngine_AndroidJNI_CallStaticCharMethodUnsafe();

		//System.Char UnityEngine.AndroidJNI::GetCharArrayElement(System.IntPtr,System.Int32)
		void Register_UnityEngine_AndroidJNI_GetCharArrayElement();
		Register_UnityEngine_AndroidJNI_GetCharArrayElement();

		//System.Char UnityEngine.AndroidJNI::GetCharField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetCharField();
		Register_UnityEngine_AndroidJNI_GetCharField();

		//System.Char UnityEngine.AndroidJNI::GetStaticCharField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetStaticCharField();
		Register_UnityEngine_AndroidJNI_GetStaticCharField();

		//System.Char[] UnityEngine.AndroidJNI::FromCharArray(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_FromCharArray();
		Register_UnityEngine_AndroidJNI_FromCharArray();

		//System.Double UnityEngine.AndroidJNI::CallDoubleMethodUnsafe(System.IntPtr,System.IntPtr,UnityEngine.jvalue*)
		void Register_UnityEngine_AndroidJNI_CallDoubleMethodUnsafe();
		Register_UnityEngine_AndroidJNI_CallDoubleMethodUnsafe();

		//System.Double UnityEngine.AndroidJNI::CallStaticDoubleMethodUnsafe(System.IntPtr,System.IntPtr,UnityEngine.jvalue*)
		void Register_UnityEngine_AndroidJNI_CallStaticDoubleMethodUnsafe();
		Register_UnityEngine_AndroidJNI_CallStaticDoubleMethodUnsafe();

		//System.Double UnityEngine.AndroidJNI::GetDoubleArrayElement(System.IntPtr,System.Int32)
		void Register_UnityEngine_AndroidJNI_GetDoubleArrayElement();
		Register_UnityEngine_AndroidJNI_GetDoubleArrayElement();

		//System.Double UnityEngine.AndroidJNI::GetDoubleField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetDoubleField();
		Register_UnityEngine_AndroidJNI_GetDoubleField();

		//System.Double UnityEngine.AndroidJNI::GetStaticDoubleField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetStaticDoubleField();
		Register_UnityEngine_AndroidJNI_GetStaticDoubleField();

		//System.Double[] UnityEngine.AndroidJNI::FromDoubleArray(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_FromDoubleArray();
		Register_UnityEngine_AndroidJNI_FromDoubleArray();

		//System.Int16 UnityEngine.AndroidJNI::CallShortMethodUnsafe(System.IntPtr,System.IntPtr,UnityEngine.jvalue*)
		void Register_UnityEngine_AndroidJNI_CallShortMethodUnsafe();
		Register_UnityEngine_AndroidJNI_CallShortMethodUnsafe();

		//System.Int16 UnityEngine.AndroidJNI::CallStaticShortMethodUnsafe(System.IntPtr,System.IntPtr,UnityEngine.jvalue*)
		void Register_UnityEngine_AndroidJNI_CallStaticShortMethodUnsafe();
		Register_UnityEngine_AndroidJNI_CallStaticShortMethodUnsafe();

		//System.Int16 UnityEngine.AndroidJNI::GetShortArrayElement(System.IntPtr,System.Int32)
		void Register_UnityEngine_AndroidJNI_GetShortArrayElement();
		Register_UnityEngine_AndroidJNI_GetShortArrayElement();

		//System.Int16 UnityEngine.AndroidJNI::GetShortField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetShortField();
		Register_UnityEngine_AndroidJNI_GetShortField();

		//System.Int16 UnityEngine.AndroidJNI::GetStaticShortField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetStaticShortField();
		Register_UnityEngine_AndroidJNI_GetStaticShortField();

		//System.Int16[] UnityEngine.AndroidJNI::FromShortArray(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_FromShortArray();
		Register_UnityEngine_AndroidJNI_FromShortArray();

		//System.Int32 UnityEngine.AndroidJNI::AttachCurrentThread()
		void Register_UnityEngine_AndroidJNI_AttachCurrentThread();
		Register_UnityEngine_AndroidJNI_AttachCurrentThread();

		//System.Int32 UnityEngine.AndroidJNI::CallIntMethodUnsafe(System.IntPtr,System.IntPtr,UnityEngine.jvalue*)
		void Register_UnityEngine_AndroidJNI_CallIntMethodUnsafe();
		Register_UnityEngine_AndroidJNI_CallIntMethodUnsafe();

		//System.Int32 UnityEngine.AndroidJNI::CallStaticIntMethodUnsafe(System.IntPtr,System.IntPtr,UnityEngine.jvalue*)
		void Register_UnityEngine_AndroidJNI_CallStaticIntMethodUnsafe();
		Register_UnityEngine_AndroidJNI_CallStaticIntMethodUnsafe();

		//System.Int32 UnityEngine.AndroidJNI::DetachCurrentThread()
		void Register_UnityEngine_AndroidJNI_DetachCurrentThread();
		Register_UnityEngine_AndroidJNI_DetachCurrentThread();

		//System.Int32 UnityEngine.AndroidJNI::EnsureLocalCapacity(System.Int32)
		void Register_UnityEngine_AndroidJNI_EnsureLocalCapacity();
		Register_UnityEngine_AndroidJNI_EnsureLocalCapacity();

		//System.Int32 UnityEngine.AndroidJNI::GetArrayLength(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetArrayLength();
		Register_UnityEngine_AndroidJNI_GetArrayLength();

		//System.Int32 UnityEngine.AndroidJNI::GetIntArrayElement(System.IntPtr,System.Int32)
		void Register_UnityEngine_AndroidJNI_GetIntArrayElement();
		Register_UnityEngine_AndroidJNI_GetIntArrayElement();

		//System.Int32 UnityEngine.AndroidJNI::GetIntField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetIntField();
		Register_UnityEngine_AndroidJNI_GetIntField();

		//System.Int32 UnityEngine.AndroidJNI::GetStaticIntField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetStaticIntField();
		Register_UnityEngine_AndroidJNI_GetStaticIntField();

		//System.Int32 UnityEngine.AndroidJNI::GetStringLength(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetStringLength();
		Register_UnityEngine_AndroidJNI_GetStringLength();

		//System.Int32 UnityEngine.AndroidJNI::GetStringUTFLength(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetStringUTFLength();
		Register_UnityEngine_AndroidJNI_GetStringUTFLength();

		//System.Int32 UnityEngine.AndroidJNI::GetVersion()
		void Register_UnityEngine_AndroidJNI_GetVersion();
		Register_UnityEngine_AndroidJNI_GetVersion();

		//System.Int32 UnityEngine.AndroidJNI::PushLocalFrame(System.Int32)
		void Register_UnityEngine_AndroidJNI_PushLocalFrame();
		Register_UnityEngine_AndroidJNI_PushLocalFrame();

		//System.Int32 UnityEngine.AndroidJNI::RegisterNativesAndFree(System.IntPtr,System.IntPtr,System.Int32)
		void Register_UnityEngine_AndroidJNI_RegisterNativesAndFree();
		Register_UnityEngine_AndroidJNI_RegisterNativesAndFree();

		//System.Int32 UnityEngine.AndroidJNI::Throw(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_Throw();
		Register_UnityEngine_AndroidJNI_Throw();

		//System.Int32 UnityEngine.AndroidJNI::ThrowNew_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_AndroidJNI_ThrowNew_Injected();
		Register_UnityEngine_AndroidJNI_ThrowNew_Injected();

		//System.Int32 UnityEngine.AndroidJNI::UnregisterNatives(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_UnregisterNatives();
		Register_UnityEngine_AndroidJNI_UnregisterNatives();

		//System.Int32[] UnityEngine.AndroidJNI::FromIntArray(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_FromIntArray();
		Register_UnityEngine_AndroidJNI_FromIntArray();

		//System.Int64 UnityEngine.AndroidJNI::CallLongMethodUnsafe(System.IntPtr,System.IntPtr,UnityEngine.jvalue*)
		void Register_UnityEngine_AndroidJNI_CallLongMethodUnsafe();
		Register_UnityEngine_AndroidJNI_CallLongMethodUnsafe();

		//System.Int64 UnityEngine.AndroidJNI::CallStaticLongMethodUnsafe(System.IntPtr,System.IntPtr,UnityEngine.jvalue*)
		void Register_UnityEngine_AndroidJNI_CallStaticLongMethodUnsafe();
		Register_UnityEngine_AndroidJNI_CallStaticLongMethodUnsafe();

		//System.Int64 UnityEngine.AndroidJNI::GetDirectBufferCapacity(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetDirectBufferCapacity();
		Register_UnityEngine_AndroidJNI_GetDirectBufferCapacity();

		//System.Int64 UnityEngine.AndroidJNI::GetLongArrayElement(System.IntPtr,System.Int32)
		void Register_UnityEngine_AndroidJNI_GetLongArrayElement();
		Register_UnityEngine_AndroidJNI_GetLongArrayElement();

		//System.Int64 UnityEngine.AndroidJNI::GetLongField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetLongField();
		Register_UnityEngine_AndroidJNI_GetLongField();

		//System.Int64 UnityEngine.AndroidJNI::GetStaticLongField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetStaticLongField();
		Register_UnityEngine_AndroidJNI_GetStaticLongField();

		//System.Int64[] UnityEngine.AndroidJNI::FromLongArray(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_FromLongArray();
		Register_UnityEngine_AndroidJNI_FromLongArray();

		//System.IntPtr UnityEngine.AndroidJNI::AllocObject(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_AllocObject();
		Register_UnityEngine_AndroidJNI_AllocObject();

		//System.IntPtr UnityEngine.AndroidJNI::CallObjectMethodUnsafe(System.IntPtr,System.IntPtr,UnityEngine.jvalue*)
		void Register_UnityEngine_AndroidJNI_CallObjectMethodUnsafe();
		Register_UnityEngine_AndroidJNI_CallObjectMethodUnsafe();

		//System.IntPtr UnityEngine.AndroidJNI::CallStaticObjectMethodUnsafe(System.IntPtr,System.IntPtr,UnityEngine.jvalue*)
		void Register_UnityEngine_AndroidJNI_CallStaticObjectMethodUnsafe();
		Register_UnityEngine_AndroidJNI_CallStaticObjectMethodUnsafe();

		//System.IntPtr UnityEngine.AndroidJNI::ExceptionOccurred()
		void Register_UnityEngine_AndroidJNI_ExceptionOccurred();
		Register_UnityEngine_AndroidJNI_ExceptionOccurred();

		//System.IntPtr UnityEngine.AndroidJNI::FindClass_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_AndroidJNI_FindClass_Injected();
		Register_UnityEngine_AndroidJNI_FindClass_Injected();

		//System.IntPtr UnityEngine.AndroidJNI::FromReflectedField(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_FromReflectedField();
		Register_UnityEngine_AndroidJNI_FromReflectedField();

		//System.IntPtr UnityEngine.AndroidJNI::FromReflectedMethod(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_FromReflectedMethod();
		Register_UnityEngine_AndroidJNI_FromReflectedMethod();

		//System.IntPtr UnityEngine.AndroidJNI::GetFieldID_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_AndroidJNI_GetFieldID_Injected();
		Register_UnityEngine_AndroidJNI_GetFieldID_Injected();

		//System.IntPtr UnityEngine.AndroidJNI::GetJavaVM()
		void Register_UnityEngine_AndroidJNI_GetJavaVM();
		Register_UnityEngine_AndroidJNI_GetJavaVM();

		//System.IntPtr UnityEngine.AndroidJNI::GetMethodID_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_AndroidJNI_GetMethodID_Injected();
		Register_UnityEngine_AndroidJNI_GetMethodID_Injected();

		//System.IntPtr UnityEngine.AndroidJNI::GetObjectArrayElement(System.IntPtr,System.Int32)
		void Register_UnityEngine_AndroidJNI_GetObjectArrayElement();
		Register_UnityEngine_AndroidJNI_GetObjectArrayElement();

		//System.IntPtr UnityEngine.AndroidJNI::GetObjectClass(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetObjectClass();
		Register_UnityEngine_AndroidJNI_GetObjectClass();

		//System.IntPtr UnityEngine.AndroidJNI::GetObjectField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetObjectField();
		Register_UnityEngine_AndroidJNI_GetObjectField();

		//System.IntPtr UnityEngine.AndroidJNI::GetStaticFieldID_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_AndroidJNI_GetStaticFieldID_Injected();
		Register_UnityEngine_AndroidJNI_GetStaticFieldID_Injected();

		//System.IntPtr UnityEngine.AndroidJNI::GetStaticMethodID_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_AndroidJNI_GetStaticMethodID_Injected();
		Register_UnityEngine_AndroidJNI_GetStaticMethodID_Injected();

		//System.IntPtr UnityEngine.AndroidJNI::GetStaticObjectField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetStaticObjectField();
		Register_UnityEngine_AndroidJNI_GetStaticObjectField();

		//System.IntPtr UnityEngine.AndroidJNI::GetSuperclass(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetSuperclass();
		Register_UnityEngine_AndroidJNI_GetSuperclass();

		//System.IntPtr UnityEngine.AndroidJNI::NewBooleanArray(System.Int32)
		void Register_UnityEngine_AndroidJNI_NewBooleanArray();
		Register_UnityEngine_AndroidJNI_NewBooleanArray();

		//System.IntPtr UnityEngine.AndroidJNI::NewCharArray(System.Int32)
		void Register_UnityEngine_AndroidJNI_NewCharArray();
		Register_UnityEngine_AndroidJNI_NewCharArray();

		//System.IntPtr UnityEngine.AndroidJNI::NewDirectByteBuffer(System.Byte*,System.Int64)
		void Register_UnityEngine_AndroidJNI_NewDirectByteBuffer();
		Register_UnityEngine_AndroidJNI_NewDirectByteBuffer();

		//System.IntPtr UnityEngine.AndroidJNI::NewDoubleArray(System.Int32)
		void Register_UnityEngine_AndroidJNI_NewDoubleArray();
		Register_UnityEngine_AndroidJNI_NewDoubleArray();

		//System.IntPtr UnityEngine.AndroidJNI::NewFloatArray(System.Int32)
		void Register_UnityEngine_AndroidJNI_NewFloatArray();
		Register_UnityEngine_AndroidJNI_NewFloatArray();

		//System.IntPtr UnityEngine.AndroidJNI::NewGlobalRef(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_NewGlobalRef();
		Register_UnityEngine_AndroidJNI_NewGlobalRef();

		//System.IntPtr UnityEngine.AndroidJNI::NewIntArray(System.Int32)
		void Register_UnityEngine_AndroidJNI_NewIntArray();
		Register_UnityEngine_AndroidJNI_NewIntArray();

		//System.IntPtr UnityEngine.AndroidJNI::NewLocalRef(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_NewLocalRef();
		Register_UnityEngine_AndroidJNI_NewLocalRef();

		//System.IntPtr UnityEngine.AndroidJNI::NewLongArray(System.Int32)
		void Register_UnityEngine_AndroidJNI_NewLongArray();
		Register_UnityEngine_AndroidJNI_NewLongArray();

		//System.IntPtr UnityEngine.AndroidJNI::NewObjectA(System.IntPtr,System.IntPtr,UnityEngine.jvalue*)
		void Register_UnityEngine_AndroidJNI_NewObjectA();
		Register_UnityEngine_AndroidJNI_NewObjectA();

		//System.IntPtr UnityEngine.AndroidJNI::NewObjectArray(System.Int32,System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_NewObjectArray();
		Register_UnityEngine_AndroidJNI_NewObjectArray();

		//System.IntPtr UnityEngine.AndroidJNI::NewSByteArray(System.Int32)
		void Register_UnityEngine_AndroidJNI_NewSByteArray();
		Register_UnityEngine_AndroidJNI_NewSByteArray();

		//System.IntPtr UnityEngine.AndroidJNI::NewShortArray(System.Int32)
		void Register_UnityEngine_AndroidJNI_NewShortArray();
		Register_UnityEngine_AndroidJNI_NewShortArray();

		//System.IntPtr UnityEngine.AndroidJNI::NewStringFromStr_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_AndroidJNI_NewStringFromStr_Injected();
		Register_UnityEngine_AndroidJNI_NewStringFromStr_Injected();

		//System.IntPtr UnityEngine.AndroidJNI::NewStringUTF_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_AndroidJNI_NewStringUTF_Injected();
		Register_UnityEngine_AndroidJNI_NewStringUTF_Injected();

		//System.IntPtr UnityEngine.AndroidJNI::NewString_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_AndroidJNI_NewString_Injected();
		Register_UnityEngine_AndroidJNI_NewString_Injected();

		//System.IntPtr UnityEngine.AndroidJNI::NewWeakGlobalRef(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_NewWeakGlobalRef();
		Register_UnityEngine_AndroidJNI_NewWeakGlobalRef();

		//System.IntPtr UnityEngine.AndroidJNI::PopLocalFrame(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_PopLocalFrame();
		Register_UnityEngine_AndroidJNI_PopLocalFrame();

		//System.IntPtr UnityEngine.AndroidJNI::RegisterNativesAllocate(System.Int32)
		void Register_UnityEngine_AndroidJNI_RegisterNativesAllocate();
		Register_UnityEngine_AndroidJNI_RegisterNativesAllocate();

		//System.IntPtr UnityEngine.AndroidJNI::ToBooleanArray_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_AndroidJNI_ToBooleanArray_Injected();
		Register_UnityEngine_AndroidJNI_ToBooleanArray_Injected();

		//System.IntPtr UnityEngine.AndroidJNI::ToByteArray_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_AndroidJNI_ToByteArray_Injected();
		Register_UnityEngine_AndroidJNI_ToByteArray_Injected();

		//System.IntPtr UnityEngine.AndroidJNI::ToCharArray(System.Char*,System.Int32)
		void Register_UnityEngine_AndroidJNI_ToCharArray();
		Register_UnityEngine_AndroidJNI_ToCharArray();

		//System.IntPtr UnityEngine.AndroidJNI::ToDoubleArray(System.Double*,System.Int32)
		void Register_UnityEngine_AndroidJNI_ToDoubleArray();
		Register_UnityEngine_AndroidJNI_ToDoubleArray();

		//System.IntPtr UnityEngine.AndroidJNI::ToFloatArray(System.Single*,System.Int32)
		void Register_UnityEngine_AndroidJNI_ToFloatArray();
		Register_UnityEngine_AndroidJNI_ToFloatArray();

		//System.IntPtr UnityEngine.AndroidJNI::ToIntArray(System.Int32*,System.Int32)
		void Register_UnityEngine_AndroidJNI_ToIntArray();
		Register_UnityEngine_AndroidJNI_ToIntArray();

		//System.IntPtr UnityEngine.AndroidJNI::ToLongArray(System.Int64*,System.Int32)
		void Register_UnityEngine_AndroidJNI_ToLongArray();
		Register_UnityEngine_AndroidJNI_ToLongArray();

		//System.IntPtr UnityEngine.AndroidJNI::ToObjectArray(System.IntPtr*,System.Int32,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_ToObjectArray();
		Register_UnityEngine_AndroidJNI_ToObjectArray();

		//System.IntPtr UnityEngine.AndroidJNI::ToReflectedField(System.IntPtr,System.IntPtr,System.Boolean)
		void Register_UnityEngine_AndroidJNI_ToReflectedField();
		Register_UnityEngine_AndroidJNI_ToReflectedField();

		//System.IntPtr UnityEngine.AndroidJNI::ToReflectedMethod(System.IntPtr,System.IntPtr,System.Boolean)
		void Register_UnityEngine_AndroidJNI_ToReflectedMethod();
		Register_UnityEngine_AndroidJNI_ToReflectedMethod();

		//System.IntPtr UnityEngine.AndroidJNI::ToSByteArray(System.SByte*,System.Int32)
		void Register_UnityEngine_AndroidJNI_ToSByteArray();
		Register_UnityEngine_AndroidJNI_ToSByteArray();

		//System.IntPtr UnityEngine.AndroidJNI::ToShortArray(System.Int16*,System.Int32)
		void Register_UnityEngine_AndroidJNI_ToShortArray();
		Register_UnityEngine_AndroidJNI_ToShortArray();

		//System.SByte UnityEngine.AndroidJNI::CallSByteMethodUnsafe(System.IntPtr,System.IntPtr,UnityEngine.jvalue*)
		void Register_UnityEngine_AndroidJNI_CallSByteMethodUnsafe();
		Register_UnityEngine_AndroidJNI_CallSByteMethodUnsafe();

		//System.SByte UnityEngine.AndroidJNI::CallStaticSByteMethodUnsafe(System.IntPtr,System.IntPtr,UnityEngine.jvalue*)
		void Register_UnityEngine_AndroidJNI_CallStaticSByteMethodUnsafe();
		Register_UnityEngine_AndroidJNI_CallStaticSByteMethodUnsafe();

		//System.SByte UnityEngine.AndroidJNI::GetSByteArrayElement(System.IntPtr,System.Int32)
		void Register_UnityEngine_AndroidJNI_GetSByteArrayElement();
		Register_UnityEngine_AndroidJNI_GetSByteArrayElement();

		//System.SByte UnityEngine.AndroidJNI::GetSByteField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetSByteField();
		Register_UnityEngine_AndroidJNI_GetSByteField();

		//System.SByte UnityEngine.AndroidJNI::GetStaticSByteField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetStaticSByteField();
		Register_UnityEngine_AndroidJNI_GetStaticSByteField();

		//System.SByte* UnityEngine.AndroidJNI::GetDirectBufferAddress(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetDirectBufferAddress();
		Register_UnityEngine_AndroidJNI_GetDirectBufferAddress();

		//System.SByte[] UnityEngine.AndroidJNI::FromSByteArray(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_FromSByteArray();
		Register_UnityEngine_AndroidJNI_FromSByteArray();

		//System.Single UnityEngine.AndroidJNI::CallFloatMethodUnsafe(System.IntPtr,System.IntPtr,UnityEngine.jvalue*)
		void Register_UnityEngine_AndroidJNI_CallFloatMethodUnsafe();
		Register_UnityEngine_AndroidJNI_CallFloatMethodUnsafe();

		//System.Single UnityEngine.AndroidJNI::CallStaticFloatMethodUnsafe(System.IntPtr,System.IntPtr,UnityEngine.jvalue*)
		void Register_UnityEngine_AndroidJNI_CallStaticFloatMethodUnsafe();
		Register_UnityEngine_AndroidJNI_CallStaticFloatMethodUnsafe();

		//System.Single UnityEngine.AndroidJNI::GetFloatArrayElement(System.IntPtr,System.Int32)
		void Register_UnityEngine_AndroidJNI_GetFloatArrayElement();
		Register_UnityEngine_AndroidJNI_GetFloatArrayElement();

		//System.Single UnityEngine.AndroidJNI::GetFloatField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetFloatField();
		Register_UnityEngine_AndroidJNI_GetFloatField();

		//System.Single UnityEngine.AndroidJNI::GetStaticFloatField(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_GetStaticFloatField();
		Register_UnityEngine_AndroidJNI_GetStaticFloatField();

		//System.Single[] UnityEngine.AndroidJNI::FromFloatArray(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_FromFloatArray();
		Register_UnityEngine_AndroidJNI_FromFloatArray();

		//System.UInt32 UnityEngine.AndroidJNI::GetQueueGlobalRefsCount()
		void Register_UnityEngine_AndroidJNI_GetQueueGlobalRefsCount();
		Register_UnityEngine_AndroidJNI_GetQueueGlobalRefsCount();

		//System.Void UnityEngine.AndroidJNI::CallStaticStringMethodUnsafeInternal_Injected(System.IntPtr,System.IntPtr,UnityEngine.jvalue*,UnityEngine.AndroidJNI/JStringBinding&)
		void Register_UnityEngine_AndroidJNI_CallStaticStringMethodUnsafeInternal_Injected();
		Register_UnityEngine_AndroidJNI_CallStaticStringMethodUnsafeInternal_Injected();

		//System.Void UnityEngine.AndroidJNI::CallStaticVoidMethodUnsafe(System.IntPtr,System.IntPtr,UnityEngine.jvalue*)
		void Register_UnityEngine_AndroidJNI_CallStaticVoidMethodUnsafe();
		Register_UnityEngine_AndroidJNI_CallStaticVoidMethodUnsafe();

		//System.Void UnityEngine.AndroidJNI::CallStringMethodUnsafeInternal_Injected(System.IntPtr,System.IntPtr,UnityEngine.jvalue*,UnityEngine.AndroidJNI/JStringBinding&)
		void Register_UnityEngine_AndroidJNI_CallStringMethodUnsafeInternal_Injected();
		Register_UnityEngine_AndroidJNI_CallStringMethodUnsafeInternal_Injected();

		//System.Void UnityEngine.AndroidJNI::CallVoidMethodUnsafe(System.IntPtr,System.IntPtr,UnityEngine.jvalue*)
		void Register_UnityEngine_AndroidJNI_CallVoidMethodUnsafe();
		Register_UnityEngine_AndroidJNI_CallVoidMethodUnsafe();

		//System.Void UnityEngine.AndroidJNI::DeleteGlobalRef(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_DeleteGlobalRef();
		Register_UnityEngine_AndroidJNI_DeleteGlobalRef();

		//System.Void UnityEngine.AndroidJNI::DeleteLocalRef(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_DeleteLocalRef();
		Register_UnityEngine_AndroidJNI_DeleteLocalRef();

		//System.Void UnityEngine.AndroidJNI::DeleteWeakGlobalRef(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_DeleteWeakGlobalRef();
		Register_UnityEngine_AndroidJNI_DeleteWeakGlobalRef();

		//System.Void UnityEngine.AndroidJNI::ExceptionClear()
		void Register_UnityEngine_AndroidJNI_ExceptionClear();
		Register_UnityEngine_AndroidJNI_ExceptionClear();

		//System.Void UnityEngine.AndroidJNI::ExceptionDescribe()
		void Register_UnityEngine_AndroidJNI_ExceptionDescribe();
		Register_UnityEngine_AndroidJNI_ExceptionDescribe();

		//System.Void UnityEngine.AndroidJNI::FatalError_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_AndroidJNI_FatalError_Injected();
		Register_UnityEngine_AndroidJNI_FatalError_Injected();

		//System.Void UnityEngine.AndroidJNI::FromBooleanArray_Injected(System.IntPtr,UnityEngine.Bindings.BlittableArrayWrapper&)
		void Register_UnityEngine_AndroidJNI_FromBooleanArray_Injected();
		Register_UnityEngine_AndroidJNI_FromBooleanArray_Injected();

		//System.Void UnityEngine.AndroidJNI::FromByteArray_Injected(System.IntPtr,UnityEngine.Bindings.BlittableArrayWrapper&)
		void Register_UnityEngine_AndroidJNI_FromByteArray_Injected();
		Register_UnityEngine_AndroidJNI_FromByteArray_Injected();

		//System.Void UnityEngine.AndroidJNI::FromObjectArray_Injected(System.IntPtr,UnityEngine.Bindings.BlittableArrayWrapper&)
		void Register_UnityEngine_AndroidJNI_FromObjectArray_Injected();
		Register_UnityEngine_AndroidJNI_FromObjectArray_Injected();

		//System.Void UnityEngine.AndroidJNI::GetStaticStringFieldInternal_Injected(System.IntPtr,System.IntPtr,UnityEngine.AndroidJNI/JStringBinding&)
		void Register_UnityEngine_AndroidJNI_GetStaticStringFieldInternal_Injected();
		Register_UnityEngine_AndroidJNI_GetStaticStringFieldInternal_Injected();

		//System.Void UnityEngine.AndroidJNI::GetStringCharsInternal_Injected(System.IntPtr,UnityEngine.AndroidJNI/JStringBinding&)
		void Register_UnityEngine_AndroidJNI_GetStringCharsInternal_Injected();
		Register_UnityEngine_AndroidJNI_GetStringCharsInternal_Injected();

		//System.Void UnityEngine.AndroidJNI::GetStringFieldInternal_Injected(System.IntPtr,System.IntPtr,UnityEngine.AndroidJNI/JStringBinding&)
		void Register_UnityEngine_AndroidJNI_GetStringFieldInternal_Injected();
		Register_UnityEngine_AndroidJNI_GetStringFieldInternal_Injected();

		//System.Void UnityEngine.AndroidJNI::GetStringUTFChars_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_AndroidJNI_GetStringUTFChars_Injected();
		Register_UnityEngine_AndroidJNI_GetStringUTFChars_Injected();

		//System.Void UnityEngine.AndroidJNI::InvokeAttached(System.Action)
		void Register_UnityEngine_AndroidJNI_InvokeAttached();
		Register_UnityEngine_AndroidJNI_InvokeAttached();

		//System.Void UnityEngine.AndroidJNI::QueueDeleteGlobalRef(System.IntPtr)
		void Register_UnityEngine_AndroidJNI_QueueDeleteGlobalRef();
		Register_UnityEngine_AndroidJNI_QueueDeleteGlobalRef();

		//System.Void UnityEngine.AndroidJNI::RegisterNativesSet_Injected(System.IntPtr,System.Int32,UnityEngine.Bindings.ManagedSpanWrapper&,UnityEngine.Bindings.ManagedSpanWrapper&,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_RegisterNativesSet_Injected();
		Register_UnityEngine_AndroidJNI_RegisterNativesSet_Injected();

		//System.Void UnityEngine.AndroidJNI::ReleaseStringChars_Injected(UnityEngine.AndroidJNI/JStringBinding&)
		void Register_UnityEngine_AndroidJNI_ReleaseStringChars_Injected();
		Register_UnityEngine_AndroidJNI_ReleaseStringChars_Injected();

		//System.Void UnityEngine.AndroidJNI::SetBooleanArrayElement(System.IntPtr,System.Int32,System.Boolean)
		void Register_UnityEngine_AndroidJNI_SetBooleanArrayElement();
		Register_UnityEngine_AndroidJNI_SetBooleanArrayElement();

		//System.Void UnityEngine.AndroidJNI::SetBooleanField(System.IntPtr,System.IntPtr,System.Boolean)
		void Register_UnityEngine_AndroidJNI_SetBooleanField();
		Register_UnityEngine_AndroidJNI_SetBooleanField();

		//System.Void UnityEngine.AndroidJNI::SetCharArrayElement(System.IntPtr,System.Int32,System.Char)
		void Register_UnityEngine_AndroidJNI_SetCharArrayElement();
		Register_UnityEngine_AndroidJNI_SetCharArrayElement();

		//System.Void UnityEngine.AndroidJNI::SetCharField(System.IntPtr,System.IntPtr,System.Char)
		void Register_UnityEngine_AndroidJNI_SetCharField();
		Register_UnityEngine_AndroidJNI_SetCharField();

		//System.Void UnityEngine.AndroidJNI::SetDoubleArrayElement(System.IntPtr,System.Int32,System.Double)
		void Register_UnityEngine_AndroidJNI_SetDoubleArrayElement();
		Register_UnityEngine_AndroidJNI_SetDoubleArrayElement();

		//System.Void UnityEngine.AndroidJNI::SetDoubleField(System.IntPtr,System.IntPtr,System.Double)
		void Register_UnityEngine_AndroidJNI_SetDoubleField();
		Register_UnityEngine_AndroidJNI_SetDoubleField();

		//System.Void UnityEngine.AndroidJNI::SetFloatArrayElement(System.IntPtr,System.Int32,System.Single)
		void Register_UnityEngine_AndroidJNI_SetFloatArrayElement();
		Register_UnityEngine_AndroidJNI_SetFloatArrayElement();

		//System.Void UnityEngine.AndroidJNI::SetFloatField(System.IntPtr,System.IntPtr,System.Single)
		void Register_UnityEngine_AndroidJNI_SetFloatField();
		Register_UnityEngine_AndroidJNI_SetFloatField();

		//System.Void UnityEngine.AndroidJNI::SetIntArrayElement(System.IntPtr,System.Int32,System.Int32)
		void Register_UnityEngine_AndroidJNI_SetIntArrayElement();
		Register_UnityEngine_AndroidJNI_SetIntArrayElement();

		//System.Void UnityEngine.AndroidJNI::SetIntField(System.IntPtr,System.IntPtr,System.Int32)
		void Register_UnityEngine_AndroidJNI_SetIntField();
		Register_UnityEngine_AndroidJNI_SetIntField();

		//System.Void UnityEngine.AndroidJNI::SetLongArrayElement(System.IntPtr,System.Int32,System.Int64)
		void Register_UnityEngine_AndroidJNI_SetLongArrayElement();
		Register_UnityEngine_AndroidJNI_SetLongArrayElement();

		//System.Void UnityEngine.AndroidJNI::SetLongField(System.IntPtr,System.IntPtr,System.Int64)
		void Register_UnityEngine_AndroidJNI_SetLongField();
		Register_UnityEngine_AndroidJNI_SetLongField();

		//System.Void UnityEngine.AndroidJNI::SetObjectArrayElement(System.IntPtr,System.Int32,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_SetObjectArrayElement();
		Register_UnityEngine_AndroidJNI_SetObjectArrayElement();

		//System.Void UnityEngine.AndroidJNI::SetObjectField(System.IntPtr,System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_SetObjectField();
		Register_UnityEngine_AndroidJNI_SetObjectField();

		//System.Void UnityEngine.AndroidJNI::SetSByteArrayElement(System.IntPtr,System.Int32,System.SByte)
		void Register_UnityEngine_AndroidJNI_SetSByteArrayElement();
		Register_UnityEngine_AndroidJNI_SetSByteArrayElement();

		//System.Void UnityEngine.AndroidJNI::SetSByteField(System.IntPtr,System.IntPtr,System.SByte)
		void Register_UnityEngine_AndroidJNI_SetSByteField();
		Register_UnityEngine_AndroidJNI_SetSByteField();

		//System.Void UnityEngine.AndroidJNI::SetShortArrayElement(System.IntPtr,System.Int32,System.Int16)
		void Register_UnityEngine_AndroidJNI_SetShortArrayElement();
		Register_UnityEngine_AndroidJNI_SetShortArrayElement();

		//System.Void UnityEngine.AndroidJNI::SetShortField(System.IntPtr,System.IntPtr,System.Int16)
		void Register_UnityEngine_AndroidJNI_SetShortField();
		Register_UnityEngine_AndroidJNI_SetShortField();

		//System.Void UnityEngine.AndroidJNI::SetStaticBooleanField(System.IntPtr,System.IntPtr,System.Boolean)
		void Register_UnityEngine_AndroidJNI_SetStaticBooleanField();
		Register_UnityEngine_AndroidJNI_SetStaticBooleanField();

		//System.Void UnityEngine.AndroidJNI::SetStaticCharField(System.IntPtr,System.IntPtr,System.Char)
		void Register_UnityEngine_AndroidJNI_SetStaticCharField();
		Register_UnityEngine_AndroidJNI_SetStaticCharField();

		//System.Void UnityEngine.AndroidJNI::SetStaticDoubleField(System.IntPtr,System.IntPtr,System.Double)
		void Register_UnityEngine_AndroidJNI_SetStaticDoubleField();
		Register_UnityEngine_AndroidJNI_SetStaticDoubleField();

		//System.Void UnityEngine.AndroidJNI::SetStaticFloatField(System.IntPtr,System.IntPtr,System.Single)
		void Register_UnityEngine_AndroidJNI_SetStaticFloatField();
		Register_UnityEngine_AndroidJNI_SetStaticFloatField();

		//System.Void UnityEngine.AndroidJNI::SetStaticIntField(System.IntPtr,System.IntPtr,System.Int32)
		void Register_UnityEngine_AndroidJNI_SetStaticIntField();
		Register_UnityEngine_AndroidJNI_SetStaticIntField();

		//System.Void UnityEngine.AndroidJNI::SetStaticLongField(System.IntPtr,System.IntPtr,System.Int64)
		void Register_UnityEngine_AndroidJNI_SetStaticLongField();
		Register_UnityEngine_AndroidJNI_SetStaticLongField();

		//System.Void UnityEngine.AndroidJNI::SetStaticObjectField(System.IntPtr,System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AndroidJNI_SetStaticObjectField();
		Register_UnityEngine_AndroidJNI_SetStaticObjectField();

		//System.Void UnityEngine.AndroidJNI::SetStaticSByteField(System.IntPtr,System.IntPtr,System.SByte)
		void Register_UnityEngine_AndroidJNI_SetStaticSByteField();
		Register_UnityEngine_AndroidJNI_SetStaticSByteField();

		//System.Void UnityEngine.AndroidJNI::SetStaticShortField(System.IntPtr,System.IntPtr,System.Int16)
		void Register_UnityEngine_AndroidJNI_SetStaticShortField();
		Register_UnityEngine_AndroidJNI_SetStaticShortField();

		//System.Void UnityEngine.AndroidJNI::SetStaticStringField_Injected(System.IntPtr,System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_AndroidJNI_SetStaticStringField_Injected();
		Register_UnityEngine_AndroidJNI_SetStaticStringField_Injected();

		//System.Void UnityEngine.AndroidJNI::SetStringField_Injected(System.IntPtr,System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_AndroidJNI_SetStringField_Injected();
		Register_UnityEngine_AndroidJNI_SetStringField_Injected();

	//End Registrations for type : UnityEngine.AndroidJNI

	//Start Registrations for type : UnityEngine.AndroidJNIHelper

		//System.Boolean UnityEngine.AndroidJNIHelper::get_debug()
		void Register_UnityEngine_AndroidJNIHelper_get_debug();
		Register_UnityEngine_AndroidJNIHelper_get_debug();

		//System.Void UnityEngine.AndroidJNIHelper::set_debug(System.Boolean)
		void Register_UnityEngine_AndroidJNIHelper_set_debug();
		Register_UnityEngine_AndroidJNIHelper_set_debug();

	//End Registrations for type : UnityEngine.AndroidJNIHelper

	//Start Registrations for type : UnityEngine.AnimationCurve

		//System.Boolean UnityEngine.AnimationCurve::Internal_Equals_Injected(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_AnimationCurve_Internal_Equals_Injected();
		Register_UnityEngine_AnimationCurve_Internal_Equals_Injected();

		//System.Int32 UnityEngine.AnimationCurve::GetHashCode_Injected(System.IntPtr)
		void Register_UnityEngine_AnimationCurve_GetHashCode_Injected();
		Register_UnityEngine_AnimationCurve_GetHashCode_Injected();

		//System.IntPtr UnityEngine.AnimationCurve::Internal_Create_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_AnimationCurve_Internal_Create_Injected();
		Register_UnityEngine_AnimationCurve_Internal_Create_Injected();

		//System.Void UnityEngine.AnimationCurve::Internal_Destroy(System.IntPtr)
		void Register_UnityEngine_AnimationCurve_Internal_Destroy();
		Register_UnityEngine_AnimationCurve_Internal_Destroy();

	//End Registrations for type : UnityEngine.AnimationCurve

	//Start Registrations for type : UnityEngine.Animations.AnimationLayerMixerPlayable

		//System.Void UnityEngine.Animations.AnimationLayerMixerPlayable::SetSingleLayerOptimizationInternal(UnityEngine.Playables.PlayableHandle&,System.Boolean)
		void Register_UnityEngine_Animations_AnimationLayerMixerPlayable_SetSingleLayerOptimizationInternal();
		Register_UnityEngine_Animations_AnimationLayerMixerPlayable_SetSingleLayerOptimizationInternal();

	//End Registrations for type : UnityEngine.Animations.AnimationLayerMixerPlayable

	//Start Registrations for type : UnityEngine.Animator

		//System.Boolean UnityEngine.Animator::get_hasBoundPlayables_Injected(System.IntPtr)
		void Register_UnityEngine_Animator_get_hasBoundPlayables_Injected();
		Register_UnityEngine_Animator_get_hasBoundPlayables_Injected();

		//System.Int32 UnityEngine.Animator::StringToHash_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_Animator_StringToHash_Injected();
		Register_UnityEngine_Animator_StringToHash_Injected();

		//System.Void UnityEngine.Animator::CrossFadeInFixedTime_Injected(System.IntPtr,System.Int32,System.Single,System.Int32,System.Single,System.Single)
		void Register_UnityEngine_Animator_CrossFadeInFixedTime_Injected();
		Register_UnityEngine_Animator_CrossFadeInFixedTime_Injected();

		//System.Void UnityEngine.Animator::ResetTriggerString_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_Animator_ResetTriggerString_Injected();
		Register_UnityEngine_Animator_ResetTriggerString_Injected();

		//System.Void UnityEngine.Animator::SetBoolID_Injected(System.IntPtr,System.Int32,System.Boolean)
		void Register_UnityEngine_Animator_SetBoolID_Injected();
		Register_UnityEngine_Animator_SetBoolID_Injected();

		//System.Void UnityEngine.Animator::SetFloatIDDamp_Injected(System.IntPtr,System.Int32,System.Single,System.Single,System.Single)
		void Register_UnityEngine_Animator_SetFloatIDDamp_Injected();
		Register_UnityEngine_Animator_SetFloatIDDamp_Injected();

		//System.Void UnityEngine.Animator::SetFloatID_Injected(System.IntPtr,System.Int32,System.Single)
		void Register_UnityEngine_Animator_SetFloatID_Injected();
		Register_UnityEngine_Animator_SetFloatID_Injected();

		//System.Void UnityEngine.Animator::SetTriggerString_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_Animator_SetTriggerString_Injected();
		Register_UnityEngine_Animator_SetTriggerString_Injected();

		//System.Void UnityEngine.Animator::get_rootPosition_Injected(System.IntPtr,UnityEngine.Vector3&)
		void Register_UnityEngine_Animator_get_rootPosition_Injected();
		Register_UnityEngine_Animator_get_rootPosition_Injected();

		//System.Void UnityEngine.Animator::get_rootRotation_Injected(System.IntPtr,UnityEngine.Quaternion&)
		void Register_UnityEngine_Animator_get_rootRotation_Injected();
		Register_UnityEngine_Animator_get_rootRotation_Injected();

		//System.Void UnityEngine.Animator::set_updateMode_Injected(System.IntPtr,UnityEngine.AnimatorUpdateMode)
		void Register_UnityEngine_Animator_set_updateMode_Injected();
		Register_UnityEngine_Animator_set_updateMode_Injected();

	//End Registrations for type : UnityEngine.Animator

	//Start Registrations for type : UnityEngine.Application

		//System.Boolean UnityEngine.Application::get_isBatchMode()
		void Register_UnityEngine_Application_get_isBatchMode();
		Register_UnityEngine_Application_get_isBatchMode();

		//System.Boolean UnityEngine.Application::get_isFocused()
		void Register_UnityEngine_Application_get_isFocused();
		Register_UnityEngine_Application_get_isFocused();

		//System.Boolean UnityEngine.Application::get_isPlaying()
		void Register_UnityEngine_Application_get_isPlaying();
		Register_UnityEngine_Application_get_isPlaying();

		//System.Boolean UnityEngine.Application::get_runInBackground()
		void Register_UnityEngine_Application_get_runInBackground();
		Register_UnityEngine_Application_get_runInBackground();

		//System.Void UnityEngine.Application::OpenURL_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_Application_OpenURL_Injected();
		Register_UnityEngine_Application_OpenURL_Injected();

		//UnityEngine.RuntimePlatform UnityEngine.Application::get_platform()
		void Register_UnityEngine_Application_get_platform();
		Register_UnityEngine_Application_get_platform();

	//End Registrations for type : UnityEngine.Application

	//Start Registrations for type : UnityEngine.AudioClip

		//System.Single UnityEngine.AudioClip::get_length_Injected(System.IntPtr)
		void Register_UnityEngine_AudioClip_get_length_Injected();
		Register_UnityEngine_AudioClip_get_length_Injected();

	//End Registrations for type : UnityEngine.AudioClip

	//Start Registrations for type : UnityEngine.AudioSettings

		//System.Boolean UnityEngine.AudioSettings::StartAudioOutput()
		void Register_UnityEngine_AudioSettings_StartAudioOutput();
		Register_UnityEngine_AudioSettings_StartAudioOutput();

		//System.Boolean UnityEngine.AudioSettings::StopAudioOutput()
		void Register_UnityEngine_AudioSettings_StopAudioOutput();
		Register_UnityEngine_AudioSettings_StopAudioOutput();

	//End Registrations for type : UnityEngine.AudioSettings

	//Start Registrations for type : UnityEngine.AudioSource

		//System.Void UnityEngine.AudioSource::PlayOneShotHelper_Injected(System.IntPtr,System.IntPtr,System.Single)
		void Register_UnityEngine_AudioSource_PlayOneShotHelper_Injected();
		Register_UnityEngine_AudioSource_PlayOneShotHelper_Injected();

	//End Registrations for type : UnityEngine.AudioSource

	//Start Registrations for type : UnityEngine.Awaitable

		//System.Int32 UnityEngine.Awaitable::IsNativeAwaitableCompleted(System.IntPtr)
		void Register_UnityEngine_Awaitable_IsNativeAwaitableCompleted();
		Register_UnityEngine_Awaitable_IsNativeAwaitableCompleted();

		//System.Void UnityEngine.Awaitable::ReleaseNativeAwaitable(System.IntPtr)
		void Register_UnityEngine_Awaitable_ReleaseNativeAwaitable();
		Register_UnityEngine_Awaitable_ReleaseNativeAwaitable();

	//End Registrations for type : UnityEngine.Awaitable

	//Start Registrations for type : UnityEngine.Behaviour

		//System.Boolean UnityEngine.Behaviour::get_enabled_Injected(System.IntPtr)
		void Register_UnityEngine_Behaviour_get_enabled_Injected();
		Register_UnityEngine_Behaviour_get_enabled_Injected();

		//System.Boolean UnityEngine.Behaviour::get_isActiveAndEnabled_Injected(System.IntPtr)
		void Register_UnityEngine_Behaviour_get_isActiveAndEnabled_Injected();
		Register_UnityEngine_Behaviour_get_isActiveAndEnabled_Injected();

		//System.Void UnityEngine.Behaviour::set_enabled_Injected(System.IntPtr,System.Boolean)
		void Register_UnityEngine_Behaviour_set_enabled_Injected();
		Register_UnityEngine_Behaviour_set_enabled_Injected();

	//End Registrations for type : UnityEngine.Behaviour

	//Start Registrations for type : UnityEngine.Bindings.BindingsAllocator

		//System.Void UnityEngine.Bindings.BindingsAllocator::Free(System.Void*)
		void Register_UnityEngine_Bindings_BindingsAllocator_Free();
		Register_UnityEngine_Bindings_BindingsAllocator_Free();

		//System.Void UnityEngine.Bindings.BindingsAllocator::FreeNativeOwnedMemory(System.Void*)
		void Register_UnityEngine_Bindings_BindingsAllocator_FreeNativeOwnedMemory();
		Register_UnityEngine_Bindings_BindingsAllocator_FreeNativeOwnedMemory();

	//End Registrations for type : UnityEngine.Bindings.BindingsAllocator

	//Start Registrations for type : UnityEngine.Camera

		//System.Boolean UnityEngine.Camera::get_stereoEnabled_Injected(System.IntPtr)
		void Register_UnityEngine_Camera_get_stereoEnabled_Injected();
		Register_UnityEngine_Camera_get_stereoEnabled_Injected();

		//System.Int32 UnityEngine.Camera::GetAllCamerasCount()
		void Register_UnityEngine_Camera_GetAllCamerasCount();
		Register_UnityEngine_Camera_GetAllCamerasCount();

		//System.Int32 UnityEngine.Camera::GetAllCamerasImpl_Injected(UnityEngine.Camera[])
		void Register_UnityEngine_Camera_GetAllCamerasImpl_Injected();
		Register_UnityEngine_Camera_GetAllCamerasImpl_Injected();

		//System.Int32 UnityEngine.Camera::get_cullingMask_Injected(System.IntPtr)
		void Register_UnityEngine_Camera_get_cullingMask_Injected();
		Register_UnityEngine_Camera_get_cullingMask_Injected();

		//System.Int32 UnityEngine.Camera::get_eventMask_Injected(System.IntPtr)
		void Register_UnityEngine_Camera_get_eventMask_Injected();
		Register_UnityEngine_Camera_get_eventMask_Injected();

		//System.Int32 UnityEngine.Camera::get_targetDisplay_Injected(System.IntPtr)
		void Register_UnityEngine_Camera_get_targetDisplay_Injected();
		Register_UnityEngine_Camera_get_targetDisplay_Injected();

		//System.IntPtr UnityEngine.Camera::get_currentInternal_Injected()
		void Register_UnityEngine_Camera_get_currentInternal_Injected();
		Register_UnityEngine_Camera_get_currentInternal_Injected();

		//System.IntPtr UnityEngine.Camera::get_main_Injected()
		void Register_UnityEngine_Camera_get_main_Injected();
		Register_UnityEngine_Camera_get_main_Injected();

		//System.IntPtr UnityEngine.Camera::get_targetTexture_Injected(System.IntPtr)
		void Register_UnityEngine_Camera_get_targetTexture_Injected();
		Register_UnityEngine_Camera_get_targetTexture_Injected();

		//System.Single UnityEngine.Camera::get_aspect_Injected(System.IntPtr)
		void Register_UnityEngine_Camera_get_aspect_Injected();
		Register_UnityEngine_Camera_get_aspect_Injected();

		//System.Single UnityEngine.Camera::get_depth_Injected(System.IntPtr)
		void Register_UnityEngine_Camera_get_depth_Injected();
		Register_UnityEngine_Camera_get_depth_Injected();

		//System.Single UnityEngine.Camera::get_farClipPlane_Injected(System.IntPtr)
		void Register_UnityEngine_Camera_get_farClipPlane_Injected();
		Register_UnityEngine_Camera_get_farClipPlane_Injected();

		//System.Single UnityEngine.Camera::get_fieldOfView_Injected(System.IntPtr)
		void Register_UnityEngine_Camera_get_fieldOfView_Injected();
		Register_UnityEngine_Camera_get_fieldOfView_Injected();

		//System.Single UnityEngine.Camera::get_nearClipPlane_Injected(System.IntPtr)
		void Register_UnityEngine_Camera_get_nearClipPlane_Injected();
		Register_UnityEngine_Camera_get_nearClipPlane_Injected();

		//System.Void UnityEngine.Camera::ScreenPointToRay_Injected(System.IntPtr,UnityEngine.Vector2&,UnityEngine.Camera/MonoOrStereoscopicEye,UnityEngine.Ray&)
		void Register_UnityEngine_Camera_ScreenPointToRay_Injected();
		Register_UnityEngine_Camera_ScreenPointToRay_Injected();

		//System.Void UnityEngine.Camera::ScreenToViewportPoint_Injected(System.IntPtr,UnityEngine.Vector3&,UnityEngine.Vector3&)
		void Register_UnityEngine_Camera_ScreenToViewportPoint_Injected();
		Register_UnityEngine_Camera_ScreenToViewportPoint_Injected();

		//System.Void UnityEngine.Camera::SetupCurrent_Injected(System.IntPtr)
		void Register_UnityEngine_Camera_SetupCurrent_Injected();
		Register_UnityEngine_Camera_SetupCurrent_Injected();

		//System.Void UnityEngine.Camera::WorldToScreenPoint_Injected(System.IntPtr,UnityEngine.Vector3&,UnityEngine.Camera/MonoOrStereoscopicEye,UnityEngine.Vector3&)
		void Register_UnityEngine_Camera_WorldToScreenPoint_Injected();
		Register_UnityEngine_Camera_WorldToScreenPoint_Injected();

		//System.Void UnityEngine.Camera::get_pixelRect_Injected(System.IntPtr,UnityEngine.Rect&)
		void Register_UnityEngine_Camera_get_pixelRect_Injected();
		Register_UnityEngine_Camera_get_pixelRect_Injected();

		//System.Void UnityEngine.Camera::set_rect_Injected(System.IntPtr,UnityEngine.Rect&)
		void Register_UnityEngine_Camera_set_rect_Injected();
		Register_UnityEngine_Camera_set_rect_Injected();

		//UnityEngine.CameraClearFlags UnityEngine.Camera::get_clearFlags_Injected(System.IntPtr)
		void Register_UnityEngine_Camera_get_clearFlags_Injected();
		Register_UnityEngine_Camera_get_clearFlags_Injected();

	//End Registrations for type : UnityEngine.Camera

	//Start Registrations for type : UnityEngine.CameraRaycastHelper

		//System.IntPtr UnityEngine.CameraRaycastHelper::RaycastTry2D_Injected(System.IntPtr,UnityEngine.Ray&,System.Single,System.Int32)
		void Register_UnityEngine_CameraRaycastHelper_RaycastTry2D_Injected();
		Register_UnityEngine_CameraRaycastHelper_RaycastTry2D_Injected();

		//System.IntPtr UnityEngine.CameraRaycastHelper::RaycastTry_Injected(System.IntPtr,UnityEngine.Ray&,System.Single,System.Int32)
		void Register_UnityEngine_CameraRaycastHelper_RaycastTry_Injected();
		Register_UnityEngine_CameraRaycastHelper_RaycastTry_Injected();

	//End Registrations for type : UnityEngine.CameraRaycastHelper

	//Start Registrations for type : UnityEngine.Canvas

		//System.Boolean UnityEngine.Canvas::get_isRootCanvas_Injected(System.IntPtr)
		void Register_UnityEngine_Canvas_get_isRootCanvas_Injected();
		Register_UnityEngine_Canvas_get_isRootCanvas_Injected();

		//System.Boolean UnityEngine.Canvas::get_overrideSorting_Injected(System.IntPtr)
		void Register_UnityEngine_Canvas_get_overrideSorting_Injected();
		Register_UnityEngine_Canvas_get_overrideSorting_Injected();

		//System.Boolean UnityEngine.Canvas::get_pixelPerfect_Injected(System.IntPtr)
		void Register_UnityEngine_Canvas_get_pixelPerfect_Injected();
		Register_UnityEngine_Canvas_get_pixelPerfect_Injected();

		//System.Int32 UnityEngine.Canvas::get_renderOrder_Injected(System.IntPtr)
		void Register_UnityEngine_Canvas_get_renderOrder_Injected();
		Register_UnityEngine_Canvas_get_renderOrder_Injected();

		//System.Int32 UnityEngine.Canvas::get_sortingLayerID_Injected(System.IntPtr)
		void Register_UnityEngine_Canvas_get_sortingLayerID_Injected();
		Register_UnityEngine_Canvas_get_sortingLayerID_Injected();

		//System.Int32 UnityEngine.Canvas::get_sortingOrder_Injected(System.IntPtr)
		void Register_UnityEngine_Canvas_get_sortingOrder_Injected();
		Register_UnityEngine_Canvas_get_sortingOrder_Injected();

		//System.Int32 UnityEngine.Canvas::get_targetDisplay_Injected(System.IntPtr)
		void Register_UnityEngine_Canvas_get_targetDisplay_Injected();
		Register_UnityEngine_Canvas_get_targetDisplay_Injected();

		//System.IntPtr UnityEngine.Canvas::GetDefaultCanvasMaterial_Injected()
		void Register_UnityEngine_Canvas_GetDefaultCanvasMaterial_Injected();
		Register_UnityEngine_Canvas_GetDefaultCanvasMaterial_Injected();

		//System.IntPtr UnityEngine.Canvas::GetETC1SupportedCanvasMaterial_Injected()
		void Register_UnityEngine_Canvas_GetETC1SupportedCanvasMaterial_Injected();
		Register_UnityEngine_Canvas_GetETC1SupportedCanvasMaterial_Injected();

		//System.IntPtr UnityEngine.Canvas::get_rootCanvas_Injected(System.IntPtr)
		void Register_UnityEngine_Canvas_get_rootCanvas_Injected();
		Register_UnityEngine_Canvas_get_rootCanvas_Injected();

		//System.IntPtr UnityEngine.Canvas::get_worldCamera_Injected(System.IntPtr)
		void Register_UnityEngine_Canvas_get_worldCamera_Injected();
		Register_UnityEngine_Canvas_get_worldCamera_Injected();

		//System.Single UnityEngine.Canvas::get_referencePixelsPerUnit_Injected(System.IntPtr)
		void Register_UnityEngine_Canvas_get_referencePixelsPerUnit_Injected();
		Register_UnityEngine_Canvas_get_referencePixelsPerUnit_Injected();

		//System.Single UnityEngine.Canvas::get_scaleFactor_Injected(System.IntPtr)
		void Register_UnityEngine_Canvas_get_scaleFactor_Injected();
		Register_UnityEngine_Canvas_get_scaleFactor_Injected();

		//System.Void UnityEngine.Canvas::SetExternalCanvasEnabled(System.Boolean)
		void Register_UnityEngine_Canvas_SetExternalCanvasEnabled();
		Register_UnityEngine_Canvas_SetExternalCanvasEnabled();

		//System.Void UnityEngine.Canvas::get_pixelRect_Injected(System.IntPtr,UnityEngine.Rect&)
		void Register_UnityEngine_Canvas_get_pixelRect_Injected();
		Register_UnityEngine_Canvas_get_pixelRect_Injected();

		//System.Void UnityEngine.Canvas::get_renderingDisplaySize_Injected(System.IntPtr,UnityEngine.Vector2&)
		void Register_UnityEngine_Canvas_get_renderingDisplaySize_Injected();
		Register_UnityEngine_Canvas_get_renderingDisplaySize_Injected();

		//System.Void UnityEngine.Canvas::set_overrideSorting_Injected(System.IntPtr,System.Boolean)
		void Register_UnityEngine_Canvas_set_overrideSorting_Injected();
		Register_UnityEngine_Canvas_set_overrideSorting_Injected();

		//System.Void UnityEngine.Canvas::set_referencePixelsPerUnit_Injected(System.IntPtr,System.Single)
		void Register_UnityEngine_Canvas_set_referencePixelsPerUnit_Injected();
		Register_UnityEngine_Canvas_set_referencePixelsPerUnit_Injected();

		//System.Void UnityEngine.Canvas::set_renderMode_Injected(System.IntPtr,UnityEngine.RenderMode)
		void Register_UnityEngine_Canvas_set_renderMode_Injected();
		Register_UnityEngine_Canvas_set_renderMode_Injected();

		//System.Void UnityEngine.Canvas::set_scaleFactor_Injected(System.IntPtr,System.Single)
		void Register_UnityEngine_Canvas_set_scaleFactor_Injected();
		Register_UnityEngine_Canvas_set_scaleFactor_Injected();

		//System.Void UnityEngine.Canvas::set_sortingLayerID_Injected(System.IntPtr,System.Int32)
		void Register_UnityEngine_Canvas_set_sortingLayerID_Injected();
		Register_UnityEngine_Canvas_set_sortingLayerID_Injected();

		//System.Void UnityEngine.Canvas::set_sortingOrder_Injected(System.IntPtr,System.Int32)
		void Register_UnityEngine_Canvas_set_sortingOrder_Injected();
		Register_UnityEngine_Canvas_set_sortingOrder_Injected();

		//UnityEngine.RenderMode UnityEngine.Canvas::get_renderMode_Injected(System.IntPtr)
		void Register_UnityEngine_Canvas_get_renderMode_Injected();
		Register_UnityEngine_Canvas_get_renderMode_Injected();

	//End Registrations for type : UnityEngine.Canvas

	//Start Registrations for type : UnityEngine.CanvasGroup

		//System.Boolean UnityEngine.CanvasGroup::get_blocksRaycasts_Injected(System.IntPtr)
		void Register_UnityEngine_CanvasGroup_get_blocksRaycasts_Injected();
		Register_UnityEngine_CanvasGroup_get_blocksRaycasts_Injected();

		//System.Boolean UnityEngine.CanvasGroup::get_ignoreParentGroups_Injected(System.IntPtr)
		void Register_UnityEngine_CanvasGroup_get_ignoreParentGroups_Injected();
		Register_UnityEngine_CanvasGroup_get_ignoreParentGroups_Injected();

		//System.Boolean UnityEngine.CanvasGroup::get_interactable_Injected(System.IntPtr)
		void Register_UnityEngine_CanvasGroup_get_interactable_Injected();
		Register_UnityEngine_CanvasGroup_get_interactable_Injected();

		//System.Single UnityEngine.CanvasGroup::get_alpha_Injected(System.IntPtr)
		void Register_UnityEngine_CanvasGroup_get_alpha_Injected();
		Register_UnityEngine_CanvasGroup_get_alpha_Injected();

		//System.Void UnityEngine.CanvasGroup::set_alpha_Injected(System.IntPtr,System.Single)
		void Register_UnityEngine_CanvasGroup_set_alpha_Injected();
		Register_UnityEngine_CanvasGroup_set_alpha_Injected();

		//System.Void UnityEngine.CanvasGroup::set_ignoreParentGroups_Injected(System.IntPtr,System.Boolean)
		void Register_UnityEngine_CanvasGroup_set_ignoreParentGroups_Injected();
		Register_UnityEngine_CanvasGroup_set_ignoreParentGroups_Injected();

	//End Registrations for type : UnityEngine.CanvasGroup

	//Start Registrations for type : UnityEngine.CanvasRenderer

		//System.Boolean UnityEngine.CanvasRenderer::get_cull_Injected(System.IntPtr)
		void Register_UnityEngine_CanvasRenderer_get_cull_Injected();
		Register_UnityEngine_CanvasRenderer_get_cull_Injected();

		//System.Boolean UnityEngine.CanvasRenderer::get_hasMoved_Injected(System.IntPtr)
		void Register_UnityEngine_CanvasRenderer_get_hasMoved_Injected();
		Register_UnityEngine_CanvasRenderer_get_hasMoved_Injected();

		//System.Int32 UnityEngine.CanvasRenderer::get_absoluteDepth_Injected(System.IntPtr)
		void Register_UnityEngine_CanvasRenderer_get_absoluteDepth_Injected();
		Register_UnityEngine_CanvasRenderer_get_absoluteDepth_Injected();

		//System.Int32 UnityEngine.CanvasRenderer::get_materialCount_Injected(System.IntPtr)
		void Register_UnityEngine_CanvasRenderer_get_materialCount_Injected();
		Register_UnityEngine_CanvasRenderer_get_materialCount_Injected();

		//System.Void UnityEngine.CanvasRenderer::Clear_Injected(System.IntPtr)
		void Register_UnityEngine_CanvasRenderer_Clear_Injected();
		Register_UnityEngine_CanvasRenderer_Clear_Injected();

		//System.Void UnityEngine.CanvasRenderer::CreateUIVertexStreamInternal(System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object)
		void Register_UnityEngine_CanvasRenderer_CreateUIVertexStreamInternal();
		Register_UnityEngine_CanvasRenderer_CreateUIVertexStreamInternal();

		//System.Void UnityEngine.CanvasRenderer::DisableRectClipping_Injected(System.IntPtr)
		void Register_UnityEngine_CanvasRenderer_DisableRectClipping_Injected();
		Register_UnityEngine_CanvasRenderer_DisableRectClipping_Injected();

		//System.Void UnityEngine.CanvasRenderer::EnableRectClipping_Injected(System.IntPtr,UnityEngine.Rect&)
		void Register_UnityEngine_CanvasRenderer_EnableRectClipping_Injected();
		Register_UnityEngine_CanvasRenderer_EnableRectClipping_Injected();

		//System.Void UnityEngine.CanvasRenderer::GetColor_Injected(System.IntPtr,UnityEngine.Color&)
		void Register_UnityEngine_CanvasRenderer_GetColor_Injected();
		Register_UnityEngine_CanvasRenderer_GetColor_Injected();

		//System.Void UnityEngine.CanvasRenderer::SetAlphaTexture_Injected(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_CanvasRenderer_SetAlphaTexture_Injected();
		Register_UnityEngine_CanvasRenderer_SetAlphaTexture_Injected();

		//System.Void UnityEngine.CanvasRenderer::SetColor_Injected(System.IntPtr,UnityEngine.Color&)
		void Register_UnityEngine_CanvasRenderer_SetColor_Injected();
		Register_UnityEngine_CanvasRenderer_SetColor_Injected();

		//System.Void UnityEngine.CanvasRenderer::SetMaterial_Injected(System.IntPtr,System.IntPtr,System.Int32)
		void Register_UnityEngine_CanvasRenderer_SetMaterial_Injected();
		Register_UnityEngine_CanvasRenderer_SetMaterial_Injected();

		//System.Void UnityEngine.CanvasRenderer::SetMesh_Injected(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_CanvasRenderer_SetMesh_Injected();
		Register_UnityEngine_CanvasRenderer_SetMesh_Injected();

		//System.Void UnityEngine.CanvasRenderer::SetPopMaterial_Injected(System.IntPtr,System.IntPtr,System.Int32)
		void Register_UnityEngine_CanvasRenderer_SetPopMaterial_Injected();
		Register_UnityEngine_CanvasRenderer_SetPopMaterial_Injected();

		//System.Void UnityEngine.CanvasRenderer::SetTexture_Injected(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_CanvasRenderer_SetTexture_Injected();
		Register_UnityEngine_CanvasRenderer_SetTexture_Injected();

		//System.Void UnityEngine.CanvasRenderer::SplitIndicesStreamsInternal(System.Object,System.Object)
		void Register_UnityEngine_CanvasRenderer_SplitIndicesStreamsInternal();
		Register_UnityEngine_CanvasRenderer_SplitIndicesStreamsInternal();

		//System.Void UnityEngine.CanvasRenderer::SplitUIVertexStreamsInternal(System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object,System.Object)
		void Register_UnityEngine_CanvasRenderer_SplitUIVertexStreamsInternal();
		Register_UnityEngine_CanvasRenderer_SplitUIVertexStreamsInternal();

		//System.Void UnityEngine.CanvasRenderer::set_clippingSoftness_Injected(System.IntPtr,UnityEngine.Vector2&)
		void Register_UnityEngine_CanvasRenderer_set_clippingSoftness_Injected();
		Register_UnityEngine_CanvasRenderer_set_clippingSoftness_Injected();

		//System.Void UnityEngine.CanvasRenderer::set_cull_Injected(System.IntPtr,System.Boolean)
		void Register_UnityEngine_CanvasRenderer_set_cull_Injected();
		Register_UnityEngine_CanvasRenderer_set_cull_Injected();

		//System.Void UnityEngine.CanvasRenderer::set_hasPopInstruction_Injected(System.IntPtr,System.Boolean)
		void Register_UnityEngine_CanvasRenderer_set_hasPopInstruction_Injected();
		Register_UnityEngine_CanvasRenderer_set_hasPopInstruction_Injected();

		//System.Void UnityEngine.CanvasRenderer::set_materialCount_Injected(System.IntPtr,System.Int32)
		void Register_UnityEngine_CanvasRenderer_set_materialCount_Injected();
		Register_UnityEngine_CanvasRenderer_set_materialCount_Injected();

		//System.Void UnityEngine.CanvasRenderer::set_popMaterialCount_Injected(System.IntPtr,System.Int32)
		void Register_UnityEngine_CanvasRenderer_set_popMaterialCount_Injected();
		Register_UnityEngine_CanvasRenderer_set_popMaterialCount_Injected();

	//End Registrations for type : UnityEngine.CanvasRenderer

	//Start Registrations for type : UnityEngine.CapsuleCollider

		//System.Single UnityEngine.CapsuleCollider::get_height_Injected(System.IntPtr)
		void Register_UnityEngine_CapsuleCollider_get_height_Injected();
		Register_UnityEngine_CapsuleCollider_get_height_Injected();

		//System.Single UnityEngine.CapsuleCollider::get_radius_Injected(System.IntPtr)
		void Register_UnityEngine_CapsuleCollider_get_radius_Injected();
		Register_UnityEngine_CapsuleCollider_get_radius_Injected();

		//System.Void UnityEngine.CapsuleCollider::get_center_Injected(System.IntPtr,UnityEngine.Vector3&)
		void Register_UnityEngine_CapsuleCollider_get_center_Injected();
		Register_UnityEngine_CapsuleCollider_get_center_Injected();

	//End Registrations for type : UnityEngine.CapsuleCollider

	//Start Registrations for type : UnityEngine.Collider

		//System.Boolean UnityEngine.Collider::get_isTrigger_Injected(System.IntPtr)
		void Register_UnityEngine_Collider_get_isTrigger_Injected();
		Register_UnityEngine_Collider_get_isTrigger_Injected();

		//System.Void UnityEngine.Collider::set_material_Injected(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_Collider_set_material_Injected();
		Register_UnityEngine_Collider_set_material_Injected();

	//End Registrations for type : UnityEngine.Collider

	//Start Registrations for type : UnityEngine.ColorUtility

		//System.Boolean UnityEngine.ColorUtility::DoTryParseHtmlColor_Injected(UnityEngine.Bindings.ManagedSpanWrapper&,UnityEngine.Color32&)
		void Register_UnityEngine_ColorUtility_DoTryParseHtmlColor_Injected();
		Register_UnityEngine_ColorUtility_DoTryParseHtmlColor_Injected();

	//End Registrations for type : UnityEngine.ColorUtility

	//Start Registrations for type : UnityEngine.Component

		//System.IntPtr UnityEngine.Component::get_gameObject_Injected(System.IntPtr)
		void Register_UnityEngine_Component_get_gameObject_Injected();
		Register_UnityEngine_Component_get_gameObject_Injected();

		//System.IntPtr UnityEngine.Component::get_transform_Injected(System.IntPtr)
		void Register_UnityEngine_Component_get_transform_Injected();
		Register_UnityEngine_Component_get_transform_Injected();

		//System.Void UnityEngine.Component::BroadcastMessage_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&,System.Object,UnityEngine.SendMessageOptions)
		void Register_UnityEngine_Component_BroadcastMessage_Injected();
		Register_UnityEngine_Component_BroadcastMessage_Injected();

		//System.Void UnityEngine.Component::GetComponentFastPath_Injected(System.IntPtr,System.Type,System.IntPtr)
		void Register_UnityEngine_Component_GetComponentFastPath_Injected();
		Register_UnityEngine_Component_GetComponentFastPath_Injected();

		//System.Void UnityEngine.Component::GetComponentsForListInternal_Injected(System.IntPtr,System.Type,System.Object)
		void Register_UnityEngine_Component_GetComponentsForListInternal_Injected();
		Register_UnityEngine_Component_GetComponentsForListInternal_Injected();

		//System.Void UnityEngine.Component::SendMessage_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&,System.Object,UnityEngine.SendMessageOptions)
		void Register_UnityEngine_Component_SendMessage_Injected();
		Register_UnityEngine_Component_SendMessage_Injected();

	//End Registrations for type : UnityEngine.Component

	//Start Registrations for type : UnityEngine.ComputeShader

		//System.Int32 UnityEngine.ComputeShader::FindKernel_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_ComputeShader_FindKernel_Injected();
		Register_UnityEngine_ComputeShader_FindKernel_Injected();

	//End Registrations for type : UnityEngine.ComputeShader

	//Start Registrations for type : UnityEngine.ContactFilter2D

		//System.Void UnityEngine.ContactFilter2D::CheckConsistency()
		void Register_UnityEngine_ContactFilter2D_CheckConsistency();
		Register_UnityEngine_ContactFilter2D_CheckConsistency();

	//End Registrations for type : UnityEngine.ContactFilter2D

	//Start Registrations for type : UnityEngine.Coroutine

		//System.Void UnityEngine.Coroutine::ReleaseCoroutine(System.IntPtr)
		void Register_UnityEngine_Coroutine_ReleaseCoroutine();
		Register_UnityEngine_Coroutine_ReleaseCoroutine();

	//End Registrations for type : UnityEngine.Coroutine

	//Start Registrations for type : UnityEngine.Cubemap

		//System.Boolean UnityEngine.Cubemap::Internal_CreateImpl(UnityEngine.Cubemap,System.Int32,System.Int32,UnityEngine.Experimental.Rendering.GraphicsFormat,UnityEngine.TextureColorSpace,UnityEngine.Experimental.Rendering.TextureCreationFlags,System.IntPtr)
		void Register_UnityEngine_Cubemap_Internal_CreateImpl();
		Register_UnityEngine_Cubemap_Internal_CreateImpl();

		//System.Boolean UnityEngine.Cubemap::get_isReadable_Injected(System.IntPtr)
		void Register_UnityEngine_Cubemap_get_isReadable_Injected();
		Register_UnityEngine_Cubemap_get_isReadable_Injected();

	//End Registrations for type : UnityEngine.Cubemap

	//Start Registrations for type : UnityEngine.CubemapArray

		//System.Boolean UnityEngine.CubemapArray::Internal_CreateImpl(UnityEngine.CubemapArray,System.Int32,System.Int32,System.Int32,UnityEngine.Experimental.Rendering.GraphicsFormat,UnityEngine.TextureColorSpace,UnityEngine.Experimental.Rendering.TextureCreationFlags)
		void Register_UnityEngine_CubemapArray_Internal_CreateImpl();
		Register_UnityEngine_CubemapArray_Internal_CreateImpl();

		//System.Boolean UnityEngine.CubemapArray::get_isReadable_Injected(System.IntPtr)
		void Register_UnityEngine_CubemapArray_get_isReadable_Injected();
		Register_UnityEngine_CubemapArray_get_isReadable_Injected();

	//End Registrations for type : UnityEngine.CubemapArray

	//Start Registrations for type : UnityEngine.Cursor

		//System.Void UnityEngine.Cursor::SetCursor_Injected(System.IntPtr,UnityEngine.Vector2&,UnityEngine.CursorMode)
		void Register_UnityEngine_Cursor_SetCursor_Injected();
		Register_UnityEngine_Cursor_SetCursor_Injected();

		//UnityEngine.CursorLockMode UnityEngine.Cursor::get_lockState()
		void Register_UnityEngine_Cursor_get_lockState();
		Register_UnityEngine_Cursor_get_lockState();

	//End Registrations for type : UnityEngine.Cursor

	//Start Registrations for type : UnityEngine.Debug

		//System.Boolean UnityEngine.Debug::get_isDebugBuild()
		void Register_UnityEngine_Debug_get_isDebugBuild();
		Register_UnityEngine_Debug_get_isDebugBuild();

		//System.Int32 UnityEngine.Debug::ExtractStackTraceNoAlloc_Injected(System.Byte*,System.Int32,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_Debug_ExtractStackTraceNoAlloc_Injected();
		Register_UnityEngine_Debug_ExtractStackTraceNoAlloc_Injected();

	//End Registrations for type : UnityEngine.Debug

	//Start Registrations for type : UnityEngine.DebugLogHandler

		//System.Void UnityEngine.DebugLogHandler::Internal_LogException_Injected(System.Exception,System.IntPtr)
		void Register_UnityEngine_DebugLogHandler_Internal_LogException_Injected();
		Register_UnityEngine_DebugLogHandler_Internal_LogException_Injected();

		//System.Void UnityEngine.DebugLogHandler::Internal_Log_Injected(UnityEngine.LogType,UnityEngine.LogOption,UnityEngine.Bindings.ManagedSpanWrapper&,System.IntPtr)
		void Register_UnityEngine_DebugLogHandler_Internal_Log_Injected();
		Register_UnityEngine_DebugLogHandler_Internal_Log_Injected();

	//End Registrations for type : UnityEngine.DebugLogHandler

	//Start Registrations for type : UnityEngine.Display

		//System.Int32 UnityEngine.Display::RelativeMouseAtImpl(System.Int32,System.Int32,System.Int32&,System.Int32&)
		void Register_UnityEngine_Display_RelativeMouseAtImpl();
		Register_UnityEngine_Display_RelativeMouseAtImpl();

		//System.Void UnityEngine.Display::GetRenderingExtImpl(System.IntPtr,System.Int32&,System.Int32&)
		void Register_UnityEngine_Display_GetRenderingExtImpl();
		Register_UnityEngine_Display_GetRenderingExtImpl();

		//System.Void UnityEngine.Display::GetSystemExtImpl(System.IntPtr,System.Int32&,System.Int32&)
		void Register_UnityEngine_Display_GetSystemExtImpl();
		Register_UnityEngine_Display_GetSystemExtImpl();

	//End Registrations for type : UnityEngine.Display

	//Start Registrations for type : UnityEngine.Event

		//System.Boolean UnityEngine.Event::PopEvent_Injected(System.IntPtr)
		void Register_UnityEngine_Event_PopEvent_Injected();
		Register_UnityEngine_Event_PopEvent_Injected();

		//System.Char UnityEngine.Event::get_character_Injected(System.IntPtr)
		void Register_UnityEngine_Event_get_character_Injected();
		Register_UnityEngine_Event_get_character_Injected();

		//System.Int32 UnityEngine.Event::GetDoubleClickTime()
		void Register_UnityEngine_Event_GetDoubleClickTime();
		Register_UnityEngine_Event_GetDoubleClickTime();

		//System.Int32 UnityEngine.Event::GetEventCount()
		void Register_UnityEngine_Event_GetEventCount();
		Register_UnityEngine_Event_GetEventCount();

		//System.Int32 UnityEngine.Event::get_button_Injected(System.IntPtr)
		void Register_UnityEngine_Event_get_button_Injected();
		Register_UnityEngine_Event_get_button_Injected();

		//System.Int32 UnityEngine.Event::get_clickCount_Injected(System.IntPtr)
		void Register_UnityEngine_Event_get_clickCount_Injected();
		Register_UnityEngine_Event_get_clickCount_Injected();

		//System.Int32 UnityEngine.Event::get_displayIndex_Injected(System.IntPtr)
		void Register_UnityEngine_Event_get_displayIndex_Injected();
		Register_UnityEngine_Event_get_displayIndex_Injected();

		//System.IntPtr UnityEngine.Event::Internal_Create(System.Int32)
		void Register_UnityEngine_Event_Internal_Create();
		Register_UnityEngine_Event_Internal_Create();

		//System.Single UnityEngine.Event::get_pressure_Injected(System.IntPtr)
		void Register_UnityEngine_Event_get_pressure_Injected();
		Register_UnityEngine_Event_get_pressure_Injected();

		//System.Single UnityEngine.Event::get_twist_Injected(System.IntPtr)
		void Register_UnityEngine_Event_get_twist_Injected();
		Register_UnityEngine_Event_get_twist_Injected();

		//System.Void UnityEngine.Event::CopyFromPtr_Injected(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_Event_CopyFromPtr_Injected();
		Register_UnityEngine_Event_CopyFromPtr_Injected();

		//System.Void UnityEngine.Event::GetEventAtIndex_Injected(System.Int32,System.IntPtr)
		void Register_UnityEngine_Event_GetEventAtIndex_Injected();
		Register_UnityEngine_Event_GetEventAtIndex_Injected();

		//System.Void UnityEngine.Event::Internal_Destroy(System.IntPtr)
		void Register_UnityEngine_Event_Internal_Destroy();
		Register_UnityEngine_Event_Internal_Destroy();

		//System.Void UnityEngine.Event::Internal_SetNativeEvent(System.IntPtr)
		void Register_UnityEngine_Event_Internal_SetNativeEvent();
		Register_UnityEngine_Event_Internal_SetNativeEvent();

		//System.Void UnityEngine.Event::Internal_Use_Injected(System.IntPtr)
		void Register_UnityEngine_Event_Internal_Use_Injected();
		Register_UnityEngine_Event_Internal_Use_Injected();

		//System.Void UnityEngine.Event::get_commandName_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_Event_get_commandName_Injected();
		Register_UnityEngine_Event_get_commandName_Injected();

		//System.Void UnityEngine.Event::get_delta_Injected(System.IntPtr,UnityEngine.Vector2&)
		void Register_UnityEngine_Event_get_delta_Injected();
		Register_UnityEngine_Event_get_delta_Injected();

		//System.Void UnityEngine.Event::get_mousePosition_Injected(System.IntPtr,UnityEngine.Vector2&)
		void Register_UnityEngine_Event_get_mousePosition_Injected();
		Register_UnityEngine_Event_get_mousePosition_Injected();

		//System.Void UnityEngine.Event::get_tilt_Injected(System.IntPtr,UnityEngine.Vector2&)
		void Register_UnityEngine_Event_get_tilt_Injected();
		Register_UnityEngine_Event_get_tilt_Injected();

		//System.Void UnityEngine.Event::set_Internal_keyCode_Injected(System.IntPtr,UnityEngine.KeyCode)
		void Register_UnityEngine_Event_set_Internal_keyCode_Injected();
		Register_UnityEngine_Event_set_Internal_keyCode_Injected();

		//System.Void UnityEngine.Event::set_character_Injected(System.IntPtr,System.Char)
		void Register_UnityEngine_Event_set_character_Injected();
		Register_UnityEngine_Event_set_character_Injected();

		//System.Void UnityEngine.Event::set_commandName_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_Event_set_commandName_Injected();
		Register_UnityEngine_Event_set_commandName_Injected();

		//System.Void UnityEngine.Event::set_delta_Injected(System.IntPtr,UnityEngine.Vector2&)
		void Register_UnityEngine_Event_set_delta_Injected();
		Register_UnityEngine_Event_set_delta_Injected();

		//System.Void UnityEngine.Event::set_displayIndex_Injected(System.IntPtr,System.Int32)
		void Register_UnityEngine_Event_set_displayIndex_Injected();
		Register_UnityEngine_Event_set_displayIndex_Injected();

		//System.Void UnityEngine.Event::set_modifiers_Injected(System.IntPtr,UnityEngine.EventModifiers)
		void Register_UnityEngine_Event_set_modifiers_Injected();
		Register_UnityEngine_Event_set_modifiers_Injected();

		//System.Void UnityEngine.Event::set_mousePosition_Injected(System.IntPtr,UnityEngine.Vector2&)
		void Register_UnityEngine_Event_set_mousePosition_Injected();
		Register_UnityEngine_Event_set_mousePosition_Injected();

		//System.Void UnityEngine.Event::set_type_Injected(System.IntPtr,UnityEngine.EventType)
		void Register_UnityEngine_Event_set_type_Injected();
		Register_UnityEngine_Event_set_type_Injected();

		//UnityEngine.EventModifiers UnityEngine.Event::get_modifiers_Injected(System.IntPtr)
		void Register_UnityEngine_Event_get_modifiers_Injected();
		Register_UnityEngine_Event_get_modifiers_Injected();

		//UnityEngine.EventType UnityEngine.Event::get_rawType_Injected(System.IntPtr)
		void Register_UnityEngine_Event_get_rawType_Injected();
		Register_UnityEngine_Event_get_rawType_Injected();

		//UnityEngine.EventType UnityEngine.Event::get_type_Injected(System.IntPtr)
		void Register_UnityEngine_Event_get_type_Injected();
		Register_UnityEngine_Event_get_type_Injected();

		//UnityEngine.KeyCode UnityEngine.Event::get_Internal_keyCode_Injected(System.IntPtr)
		void Register_UnityEngine_Event_get_Internal_keyCode_Injected();
		Register_UnityEngine_Event_get_Internal_keyCode_Injected();

		//UnityEngine.PenStatus UnityEngine.Event::get_penStatus_Injected(System.IntPtr)
		void Register_UnityEngine_Event_get_penStatus_Injected();
		Register_UnityEngine_Event_get_penStatus_Injected();

		//UnityEngine.PointerType UnityEngine.Event::get_pointerType_Injected(System.IntPtr)
		void Register_UnityEngine_Event_get_pointerType_Injected();
		Register_UnityEngine_Event_get_pointerType_Injected();

	//End Registrations for type : UnityEngine.Event

	//Start Registrations for type : UnityEngine.Experimental.Rendering.BuiltinRuntimeReflectionSystem

		//System.Boolean UnityEngine.Experimental.Rendering.BuiltinRuntimeReflectionSystem::BuiltinUpdate()
		void Register_UnityEngine_Experimental_Rendering_BuiltinRuntimeReflectionSystem_BuiltinUpdate();
		Register_UnityEngine_Experimental_Rendering_BuiltinRuntimeReflectionSystem_BuiltinUpdate();

	//End Registrations for type : UnityEngine.Experimental.Rendering.BuiltinRuntimeReflectionSystem

	//Start Registrations for type : UnityEngine.Experimental.Rendering.GraphicsFormatUtility

		//System.Boolean UnityEngine.Experimental.Rendering.GraphicsFormatUtility::CanDecompressFormat(UnityEngine.Experimental.Rendering.GraphicsFormat,System.Boolean)
		void Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_CanDecompressFormat();
		Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_CanDecompressFormat();

		//System.Boolean UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsCompressedFormat_Native_TextureFormat(UnityEngine.TextureFormat)
		void Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_IsCompressedFormat_Native_TextureFormat();
		Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_IsCompressedFormat_Native_TextureFormat();

		//System.Boolean UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsCrunchFormat(UnityEngine.TextureFormat)
		void Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_IsCrunchFormat();
		Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_IsCrunchFormat();

		//System.Boolean UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsDepthStencilFormat(UnityEngine.Experimental.Rendering.GraphicsFormat)
		void Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_IsDepthStencilFormat();
		Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_IsDepthStencilFormat();

		//System.Boolean UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsPVRTCFormat(UnityEngine.Experimental.Rendering.GraphicsFormat)
		void Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_IsPVRTCFormat();
		Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_IsPVRTCFormat();

		//System.Boolean UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsSRGBFormat(UnityEngine.Experimental.Rendering.GraphicsFormat)
		void Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_IsSRGBFormat();
		Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_IsSRGBFormat();

		//UnityEngine.Experimental.Rendering.GraphicsFormat UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetDepthStencilFormatFromBitsLegacy_Native(System.Int32)
		void Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_GetDepthStencilFormatFromBitsLegacy_Native();
		Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_GetDepthStencilFormatFromBitsLegacy_Native();

		//UnityEngine.Experimental.Rendering.GraphicsFormat UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetGraphicsFormat_Native_RenderTextureFormat(UnityEngine.RenderTextureFormat,System.Boolean)
		void Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_GetGraphicsFormat_Native_RenderTextureFormat();
		Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_GetGraphicsFormat_Native_RenderTextureFormat();

		//UnityEngine.Experimental.Rendering.GraphicsFormat UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetGraphicsFormat_Native_TextureFormat(UnityEngine.TextureFormat,System.Boolean)
		void Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_GetGraphicsFormat_Native_TextureFormat();
		Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_GetGraphicsFormat_Native_TextureFormat();

		//UnityEngine.RenderTextureFormat UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetRenderTextureFormat(UnityEngine.Experimental.Rendering.GraphicsFormat)
		void Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_GetRenderTextureFormat();
		Register_UnityEngine_Experimental_Rendering_GraphicsFormatUtility_GetRenderTextureFormat();

	//End Registrations for type : UnityEngine.Experimental.Rendering.GraphicsFormatUtility

	//Start Registrations for type : UnityEngine.Experimental.Rendering.ScriptableRuntimeReflectionSystemSettings

		//System.Void UnityEngine.Experimental.Rendering.ScriptableRuntimeReflectionSystemSettings::ScriptingDirtyReflectionSystemInstance()
		void Register_UnityEngine_Experimental_Rendering_ScriptableRuntimeReflectionSystemSettings_ScriptingDirtyReflectionSystemInstance();
		Register_UnityEngine_Experimental_Rendering_ScriptableRuntimeReflectionSystemSettings_ScriptingDirtyReflectionSystemInstance();

	//End Registrations for type : UnityEngine.Experimental.Rendering.ScriptableRuntimeReflectionSystemSettings

	//Start Registrations for type : UnityEngine.Font

		//System.Boolean UnityEngine.Font::HasCharacter_Injected(System.IntPtr,System.Int32)
		void Register_UnityEngine_Font_HasCharacter_Injected();
		Register_UnityEngine_Font_HasCharacter_Injected();

		//System.Boolean UnityEngine.Font::get_dynamic_Injected(System.IntPtr)
		void Register_UnityEngine_Font_get_dynamic_Injected();
		Register_UnityEngine_Font_get_dynamic_Injected();

		//System.Int32 UnityEngine.Font::get_fontSize_Injected(System.IntPtr)
		void Register_UnityEngine_Font_get_fontSize_Injected();
		Register_UnityEngine_Font_get_fontSize_Injected();

		//System.IntPtr UnityEngine.Font::get_material_Injected(System.IntPtr)
		void Register_UnityEngine_Font_get_material_Injected();
		Register_UnityEngine_Font_get_material_Injected();

		//System.String[] UnityEngine.Font::GetOSFallbacks()
		void Register_UnityEngine_Font_GetOSFallbacks();
		Register_UnityEngine_Font_GetOSFallbacks();

	//End Registrations for type : UnityEngine.Font

	//Start Registrations for type : UnityEngine.GameObject

		//System.Array UnityEngine.GameObject::GetComponentsInternal_Injected(System.IntPtr,System.Type,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Object)
		void Register_UnityEngine_GameObject_GetComponentsInternal_Injected();
		Register_UnityEngine_GameObject_GetComponentsInternal_Injected();

		//System.Boolean UnityEngine.GameObject::CompareTag_Internal_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_GameObject_CompareTag_Internal_Injected();
		Register_UnityEngine_GameObject_CompareTag_Internal_Injected();

		//System.Boolean UnityEngine.GameObject::get_activeInHierarchy_Injected(System.IntPtr)
		void Register_UnityEngine_GameObject_get_activeInHierarchy_Injected();
		Register_UnityEngine_GameObject_get_activeInHierarchy_Injected();

		//System.Boolean UnityEngine.GameObject::get_activeSelf_Injected(System.IntPtr)
		void Register_UnityEngine_GameObject_get_activeSelf_Injected();
		Register_UnityEngine_GameObject_get_activeSelf_Injected();

		//System.Int32 UnityEngine.GameObject::get_layer_Injected(System.IntPtr)
		void Register_UnityEngine_GameObject_get_layer_Injected();
		Register_UnityEngine_GameObject_get_layer_Injected();

		//System.IntPtr UnityEngine.GameObject::Find_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_GameObject_Find_Injected();
		Register_UnityEngine_GameObject_Find_Injected();

		//System.IntPtr UnityEngine.GameObject::GetComponentInChildren_Injected(System.IntPtr,System.Type,System.Boolean)
		void Register_UnityEngine_GameObject_GetComponentInChildren_Injected();
		Register_UnityEngine_GameObject_GetComponentInChildren_Injected();

		//System.IntPtr UnityEngine.GameObject::GetComponentInParent_Injected(System.IntPtr,System.Type,System.Boolean)
		void Register_UnityEngine_GameObject_GetComponentInParent_Injected();
		Register_UnityEngine_GameObject_GetComponentInParent_Injected();

		//System.IntPtr UnityEngine.GameObject::GetComponent_Injected(System.IntPtr,System.Type)
		void Register_UnityEngine_GameObject_GetComponent_Injected();
		Register_UnityEngine_GameObject_GetComponent_Injected();

		//System.IntPtr UnityEngine.GameObject::Internal_AddComponentWithType_Injected(System.IntPtr,System.Type)
		void Register_UnityEngine_GameObject_Internal_AddComponentWithType_Injected();
		Register_UnityEngine_GameObject_Internal_AddComponentWithType_Injected();

		//System.IntPtr UnityEngine.GameObject::TryGetComponentInternal_Injected(System.IntPtr,System.Type)
		void Register_UnityEngine_GameObject_TryGetComponentInternal_Injected();
		Register_UnityEngine_GameObject_TryGetComponentInternal_Injected();

		//System.IntPtr UnityEngine.GameObject::get_transform_Injected(System.IntPtr)
		void Register_UnityEngine_GameObject_get_transform_Injected();
		Register_UnityEngine_GameObject_get_transform_Injected();

		//System.Void UnityEngine.GameObject::GetComponentFastPath_Injected(System.IntPtr,System.Type,System.IntPtr)
		void Register_UnityEngine_GameObject_GetComponentFastPath_Injected();
		Register_UnityEngine_GameObject_GetComponentFastPath_Injected();

		//System.Void UnityEngine.GameObject::Internal_CreateGameObject_Injected(UnityEngine.GameObject,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_GameObject_Internal_CreateGameObject_Injected();
		Register_UnityEngine_GameObject_Internal_CreateGameObject_Injected();

		//System.Void UnityEngine.GameObject::SendMessage_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&,System.Object,UnityEngine.SendMessageOptions)
		void Register_UnityEngine_GameObject_SendMessage_Injected();
		Register_UnityEngine_GameObject_SendMessage_Injected();

		//System.Void UnityEngine.GameObject::SetActive_Injected(System.IntPtr,System.Boolean)
		void Register_UnityEngine_GameObject_SetActive_Injected();
		Register_UnityEngine_GameObject_SetActive_Injected();

		//System.Void UnityEngine.GameObject::TryGetComponentFastPath_Injected(System.IntPtr,System.Type,System.IntPtr)
		void Register_UnityEngine_GameObject_TryGetComponentFastPath_Injected();
		Register_UnityEngine_GameObject_TryGetComponentFastPath_Injected();

		//System.Void UnityEngine.GameObject::set_layer_Injected(System.IntPtr,System.Int32)
		void Register_UnityEngine_GameObject_set_layer_Injected();
		Register_UnityEngine_GameObject_set_layer_Injected();

	//End Registrations for type : UnityEngine.GameObject

	//Start Registrations for type : UnityEngine.Gizmos

		//System.Void UnityEngine.Gizmos::DrawLine_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&)
		void Register_UnityEngine_Gizmos_DrawLine_Injected();
		Register_UnityEngine_Gizmos_DrawLine_Injected();

		//System.Void UnityEngine.Gizmos::set_color_Injected(UnityEngine.Color&)
		void Register_UnityEngine_Gizmos_set_color_Injected();
		Register_UnityEngine_Gizmos_set_color_Injected();

		//System.Void UnityEngine.Gizmos::set_matrix_Injected(UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Gizmos_set_matrix_Injected();
		Register_UnityEngine_Gizmos_set_matrix_Injected();

	//End Registrations for type : UnityEngine.Gizmos

	//Start Registrations for type : UnityEngine.GL

		//System.Void UnityEngine.GL::Begin(System.Int32)
		void Register_UnityEngine_GL_Begin();
		Register_UnityEngine_GL_Begin();

		//System.Void UnityEngine.GL::End()
		void Register_UnityEngine_GL_End();
		Register_UnityEngine_GL_End();

		//System.Void UnityEngine.GL::GLClear_Injected(System.Boolean,System.Boolean,UnityEngine.Color&,System.Single)
		void Register_UnityEngine_GL_GLClear_Injected();
		Register_UnityEngine_GL_GLClear_Injected();

		//System.Void UnityEngine.GL::GLLoadPixelMatrixScript(System.Single,System.Single,System.Single,System.Single)
		void Register_UnityEngine_GL_GLLoadPixelMatrixScript();
		Register_UnityEngine_GL_GLLoadPixelMatrixScript();

		//System.Void UnityEngine.GL::ImmediateColor(System.Single,System.Single,System.Single,System.Single)
		void Register_UnityEngine_GL_ImmediateColor();
		Register_UnityEngine_GL_ImmediateColor();

		//System.Void UnityEngine.GL::LoadOrtho()
		void Register_UnityEngine_GL_LoadOrtho();
		Register_UnityEngine_GL_LoadOrtho();

		//System.Void UnityEngine.GL::LoadProjectionMatrix_Injected(UnityEngine.Matrix4x4&)
		void Register_UnityEngine_GL_LoadProjectionMatrix_Injected();
		Register_UnityEngine_GL_LoadProjectionMatrix_Injected();

		//System.Void UnityEngine.GL::PopMatrix()
		void Register_UnityEngine_GL_PopMatrix();
		Register_UnityEngine_GL_PopMatrix();

		//System.Void UnityEngine.GL::PushMatrix()
		void Register_UnityEngine_GL_PushMatrix();
		Register_UnityEngine_GL_PushMatrix();

		//System.Void UnityEngine.GL::SetViewMatrix_Injected(UnityEngine.Matrix4x4&)
		void Register_UnityEngine_GL_SetViewMatrix_Injected();
		Register_UnityEngine_GL_SetViewMatrix_Injected();

		//System.Void UnityEngine.GL::TexCoord3(System.Single,System.Single,System.Single)
		void Register_UnityEngine_GL_TexCoord3();
		Register_UnityEngine_GL_TexCoord3();

		//System.Void UnityEngine.GL::Vertex3(System.Single,System.Single,System.Single)
		void Register_UnityEngine_GL_Vertex3();
		Register_UnityEngine_GL_Vertex3();

		//System.Void UnityEngine.GL::Viewport_Injected(UnityEngine.Rect&)
		void Register_UnityEngine_GL_Viewport_Injected();
		Register_UnityEngine_GL_Viewport_Injected();

	//End Registrations for type : UnityEngine.GL

	//Start Registrations for type : UnityEngine.Gradient

		//System.Boolean UnityEngine.Gradient::Internal_Equals_Injected(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_Gradient_Internal_Equals_Injected();
		Register_UnityEngine_Gradient_Internal_Equals_Injected();

		//System.IntPtr UnityEngine.Gradient::Init()
		void Register_UnityEngine_Gradient_Init();
		Register_UnityEngine_Gradient_Init();

		//System.Void UnityEngine.Gradient::Cleanup_Injected(System.IntPtr)
		void Register_UnityEngine_Gradient_Cleanup_Injected();
		Register_UnityEngine_Gradient_Cleanup_Injected();

	//End Registrations for type : UnityEngine.Gradient

	//Start Registrations for type : UnityEngine.Graphics

		//System.Int32 UnityEngine.Graphics::Internal_GetMaxDrawMeshInstanceCount()
		void Register_UnityEngine_Graphics_Internal_GetMaxDrawMeshInstanceCount();
		Register_UnityEngine_Graphics_Internal_GetMaxDrawMeshInstanceCount();

		//System.Void UnityEngine.Graphics::Internal_SetNullRT()
		void Register_UnityEngine_Graphics_Internal_SetNullRT();
		Register_UnityEngine_Graphics_Internal_SetNullRT();

		//System.Void UnityEngine.Graphics::Internal_SetRTSimple_Injected(UnityEngine.RenderBuffer&,UnityEngine.RenderBuffer&,System.Int32,UnityEngine.CubemapFace,System.Int32)
		void Register_UnityEngine_Graphics_Internal_SetRTSimple_Injected();
		Register_UnityEngine_Graphics_Internal_SetRTSimple_Injected();

	//End Registrations for type : UnityEngine.Graphics

	//Start Registrations for type : UnityEngine.GUI

		//System.Boolean UnityEngine.GUI::get_changed()
		void Register_UnityEngine_GUI_get_changed();
		Register_UnityEngine_GUI_get_changed();

		//System.Boolean UnityEngine.GUI::get_enabled()
		void Register_UnityEngine_GUI_get_enabled();
		Register_UnityEngine_GUI_get_enabled();

		//System.Void UnityEngine.GUI::get_backgroundColor_Injected(UnityEngine.Color&)
		void Register_UnityEngine_GUI_get_backgroundColor_Injected();
		Register_UnityEngine_GUI_get_backgroundColor_Injected();

		//System.Void UnityEngine.GUI::get_color_Injected(UnityEngine.Color&)
		void Register_UnityEngine_GUI_get_color_Injected();
		Register_UnityEngine_GUI_get_color_Injected();

		//System.Void UnityEngine.GUI::get_contentColor_Injected(UnityEngine.Color&)
		void Register_UnityEngine_GUI_get_contentColor_Injected();
		Register_UnityEngine_GUI_get_contentColor_Injected();

		//System.Void UnityEngine.GUI::set_backgroundColor_Injected(UnityEngine.Color&)
		void Register_UnityEngine_GUI_set_backgroundColor_Injected();
		Register_UnityEngine_GUI_set_backgroundColor_Injected();

		//System.Void UnityEngine.GUI::set_changed(System.Boolean)
		void Register_UnityEngine_GUI_set_changed();
		Register_UnityEngine_GUI_set_changed();

		//System.Void UnityEngine.GUI::set_color_Injected(UnityEngine.Color&)
		void Register_UnityEngine_GUI_set_color_Injected();
		Register_UnityEngine_GUI_set_color_Injected();

		//System.Void UnityEngine.GUI::set_contentColor_Injected(UnityEngine.Color&)
		void Register_UnityEngine_GUI_set_contentColor_Injected();
		Register_UnityEngine_GUI_set_contentColor_Injected();

		//System.Void UnityEngine.GUI::set_enabled(System.Boolean)
		void Register_UnityEngine_GUI_set_enabled();
		Register_UnityEngine_GUI_set_enabled();

	//End Registrations for type : UnityEngine.GUI

	//Start Registrations for type : UnityEngine.GUIClip

		//System.Int32 UnityEngine.GUIClip::Internal_GetCount()
		void Register_UnityEngine_GUIClip_Internal_GetCount();
		Register_UnityEngine_GUIClip_Internal_GetCount();

		//System.Void UnityEngine.GUIClip::GetMatrix_Injected(UnityEngine.Matrix4x4&)
		void Register_UnityEngine_GUIClip_GetMatrix_Injected();
		Register_UnityEngine_GUIClip_GetMatrix_Injected();

		//System.Void UnityEngine.GUIClip::Internal_Pop()
		void Register_UnityEngine_GUIClip_Internal_Pop();
		Register_UnityEngine_GUIClip_Internal_Pop();

		//System.Void UnityEngine.GUIClip::Internal_PopParentClip()
		void Register_UnityEngine_GUIClip_Internal_PopParentClip();
		Register_UnityEngine_GUIClip_Internal_PopParentClip();

		//System.Void UnityEngine.GUIClip::Internal_PushParentClip_Injected(UnityEngine.Matrix4x4&,UnityEngine.Matrix4x4&,UnityEngine.Rect&)
		void Register_UnityEngine_GUIClip_Internal_PushParentClip_Injected();
		Register_UnityEngine_GUIClip_Internal_PushParentClip_Injected();

		//System.Void UnityEngine.GUIClip::SetMatrix_Injected(UnityEngine.Matrix4x4&)
		void Register_UnityEngine_GUIClip_SetMatrix_Injected();
		Register_UnityEngine_GUIClip_SetMatrix_Injected();

		//System.Void UnityEngine.GUIClip::get_visibleRect_Injected(UnityEngine.Rect&)
		void Register_UnityEngine_GUIClip_get_visibleRect_Injected();
		Register_UnityEngine_GUIClip_get_visibleRect_Injected();

	//End Registrations for type : UnityEngine.GUIClip

	//Start Registrations for type : UnityEngine.GUILayoutUtility

		//System.Void UnityEngine.GUILayoutUtility::Internal_GetWindowRect_Injected(System.Int32,UnityEngine.Rect&)
		void Register_UnityEngine_GUILayoutUtility_Internal_GetWindowRect_Injected();
		Register_UnityEngine_GUILayoutUtility_Internal_GetWindowRect_Injected();

		//System.Void UnityEngine.GUILayoutUtility::Internal_MoveWindow_Injected(System.Int32,UnityEngine.Rect&)
		void Register_UnityEngine_GUILayoutUtility_Internal_MoveWindow_Injected();
		Register_UnityEngine_GUILayoutUtility_Internal_MoveWindow_Injected();

	//End Registrations for type : UnityEngine.GUILayoutUtility

	//Start Registrations for type : UnityEngine.GUIStyle

		//System.Boolean UnityEngine.GUIStyle::IsTooltipActive_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_GUIStyle_IsTooltipActive_Injected();
		Register_UnityEngine_GUIStyle_IsTooltipActive_Injected();

		//System.Boolean UnityEngine.GUIStyle::get_richText_Injected(System.IntPtr)
		void Register_UnityEngine_GUIStyle_get_richText_Injected();
		Register_UnityEngine_GUIStyle_get_richText_Injected();

		//System.Boolean UnityEngine.GUIStyle::get_stretchHeight_Injected(System.IntPtr)
		void Register_UnityEngine_GUIStyle_get_stretchHeight_Injected();
		Register_UnityEngine_GUIStyle_get_stretchHeight_Injected();

		//System.Boolean UnityEngine.GUIStyle::get_stretchWidth_Injected(System.IntPtr)
		void Register_UnityEngine_GUIStyle_get_stretchWidth_Injected();
		Register_UnityEngine_GUIStyle_get_stretchWidth_Injected();

		//System.Boolean UnityEngine.GUIStyle::get_wordWrap_Injected(System.IntPtr)
		void Register_UnityEngine_GUIStyle_get_wordWrap_Injected();
		Register_UnityEngine_GUIStyle_get_wordWrap_Injected();

		//System.Int32 UnityEngine.GUIStyle::get_fontSize_Injected(System.IntPtr)
		void Register_UnityEngine_GUIStyle_get_fontSize_Injected();
		Register_UnityEngine_GUIStyle_get_fontSize_Injected();

		//System.IntPtr UnityEngine.GUIStyle::GetDefaultFont_Injected()
		void Register_UnityEngine_GUIStyle_GetDefaultFont_Injected();
		Register_UnityEngine_GUIStyle_GetDefaultFont_Injected();

		//System.IntPtr UnityEngine.GUIStyle::GetRectOffsetPtr_Injected(System.IntPtr,System.Int32)
		void Register_UnityEngine_GUIStyle_GetRectOffsetPtr_Injected();
		Register_UnityEngine_GUIStyle_GetRectOffsetPtr_Injected();

		//System.IntPtr UnityEngine.GUIStyle::GetStyleStatePtr_Injected(System.IntPtr,System.Int32)
		void Register_UnityEngine_GUIStyle_GetStyleStatePtr_Injected();
		Register_UnityEngine_GUIStyle_GetStyleStatePtr_Injected();

		//System.IntPtr UnityEngine.GUIStyle::Internal_Create(UnityEngine.GUIStyle)
		void Register_UnityEngine_GUIStyle_Internal_Create();
		Register_UnityEngine_GUIStyle_Internal_Create();

		//System.IntPtr UnityEngine.GUIStyle::get_font_Injected(System.IntPtr)
		void Register_UnityEngine_GUIStyle_get_font_Injected();
		Register_UnityEngine_GUIStyle_get_font_Injected();

		//System.Single UnityEngine.GUIStyle::get_fixedHeight_Injected(System.IntPtr)
		void Register_UnityEngine_GUIStyle_get_fixedHeight_Injected();
		Register_UnityEngine_GUIStyle_get_fixedHeight_Injected();

		//System.Single UnityEngine.GUIStyle::get_fixedWidth_Injected(System.IntPtr)
		void Register_UnityEngine_GUIStyle_get_fixedWidth_Injected();
		Register_UnityEngine_GUIStyle_get_fixedWidth_Injected();

		//System.Void UnityEngine.GUIStyle::Internal_Destroy(System.IntPtr)
		void Register_UnityEngine_GUIStyle_Internal_Destroy();
		Register_UnityEngine_GUIStyle_Internal_Destroy();

		//System.Void UnityEngine.GUIStyle::Internal_DestroyTextGenerator(System.Int32)
		void Register_UnityEngine_GUIStyle_Internal_DestroyTextGenerator();
		Register_UnityEngine_GUIStyle_Internal_DestroyTextGenerator();

		//System.Void UnityEngine.GUIStyle::Internal_Draw2_Injected(System.IntPtr,UnityEngine.Rect&,UnityEngine.GUIContent,System.Int32,System.Boolean)
		void Register_UnityEngine_GUIStyle_Internal_Draw2_Injected();
		Register_UnityEngine_GUIStyle_Internal_Draw2_Injected();

		//System.Void UnityEngine.GUIStyle::Internal_Draw_Injected(System.IntPtr,UnityEngine.Rect&,UnityEngine.GUIContent,System.Boolean,System.Boolean,System.Boolean,System.Boolean)
		void Register_UnityEngine_GUIStyle_Internal_Draw_Injected();
		Register_UnityEngine_GUIStyle_Internal_Draw_Injected();

		//System.Void UnityEngine.GUIStyle::Internal_GetTextRectOffset_Injected(System.IntPtr,UnityEngine.Rect&,UnityEngine.GUIContent,UnityEngine.Vector2&,UnityEngine.Vector2&)
		void Register_UnityEngine_GUIStyle_Internal_GetTextRectOffset_Injected();
		Register_UnityEngine_GUIStyle_Internal_GetTextRectOffset_Injected();

		//System.Void UnityEngine.GUIStyle::SetDefaultFont_Injected(System.IntPtr)
		void Register_UnityEngine_GUIStyle_SetDefaultFont_Injected();
		Register_UnityEngine_GUIStyle_SetDefaultFont_Injected();

		//System.Void UnityEngine.GUIStyle::SetMouseTooltip_Injected(UnityEngine.Bindings.ManagedSpanWrapper&,UnityEngine.Rect&)
		void Register_UnityEngine_GUIStyle_SetMouseTooltip_Injected();
		Register_UnityEngine_GUIStyle_SetMouseTooltip_Injected();

		//System.Void UnityEngine.GUIStyle::get_contentOffset_Injected(System.IntPtr,UnityEngine.Vector2&)
		void Register_UnityEngine_GUIStyle_get_contentOffset_Injected();
		Register_UnityEngine_GUIStyle_get_contentOffset_Injected();

		//System.Void UnityEngine.GUIStyle::get_rawName_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_GUIStyle_get_rawName_Injected();
		Register_UnityEngine_GUIStyle_get_rawName_Injected();

		//System.Void UnityEngine.GUIStyle::set_rawName_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_GUIStyle_set_rawName_Injected();
		Register_UnityEngine_GUIStyle_set_rawName_Injected();

		//System.Void UnityEngine.GUIStyle::set_stretchHeight_Injected(System.IntPtr,System.Boolean)
		void Register_UnityEngine_GUIStyle_set_stretchHeight_Injected();
		Register_UnityEngine_GUIStyle_set_stretchHeight_Injected();

		//UnityEngine.FontStyle UnityEngine.GUIStyle::get_fontStyle_Injected(System.IntPtr)
		void Register_UnityEngine_GUIStyle_get_fontStyle_Injected();
		Register_UnityEngine_GUIStyle_get_fontStyle_Injected();

		//UnityEngine.ImagePosition UnityEngine.GUIStyle::get_imagePosition_Injected(System.IntPtr)
		void Register_UnityEngine_GUIStyle_get_imagePosition_Injected();
		Register_UnityEngine_GUIStyle_get_imagePosition_Injected();

		//UnityEngine.TextAnchor UnityEngine.GUIStyle::get_alignment_Injected(System.IntPtr)
		void Register_UnityEngine_GUIStyle_get_alignment_Injected();
		Register_UnityEngine_GUIStyle_get_alignment_Injected();

		//UnityEngine.TextClipping UnityEngine.GUIStyle::get_clipping_Injected(System.IntPtr)
		void Register_UnityEngine_GUIStyle_get_clipping_Injected();
		Register_UnityEngine_GUIStyle_get_clipping_Injected();

	//End Registrations for type : UnityEngine.GUIStyle

	//Start Registrations for type : UnityEngine.GUIStyleState

		//System.IntPtr UnityEngine.GUIStyleState::Init()
		void Register_UnityEngine_GUIStyleState_Init();
		Register_UnityEngine_GUIStyleState_Init();

		//System.Void UnityEngine.GUIStyleState::Cleanup_Injected(System.IntPtr)
		void Register_UnityEngine_GUIStyleState_Cleanup_Injected();
		Register_UnityEngine_GUIStyleState_Cleanup_Injected();

		//System.Void UnityEngine.GUIStyleState::set_textColor_Injected(System.IntPtr,UnityEngine.Color&)
		void Register_UnityEngine_GUIStyleState_set_textColor_Injected();
		Register_UnityEngine_GUIStyleState_set_textColor_Injected();

	//End Registrations for type : UnityEngine.GUIStyleState

	//Start Registrations for type : UnityEngine.GUIUtility

		//System.Boolean UnityEngine.GUIUtility::HasFocusableControls()
		void Register_UnityEngine_GUIUtility_HasFocusableControls();
		Register_UnityEngine_GUIUtility_HasFocusableControls();

		//System.Boolean UnityEngine.GUIUtility::OwnsId(System.Int32)
		void Register_UnityEngine_GUIUtility_OwnsId();
		Register_UnityEngine_GUIUtility_OwnsId();

		//System.Boolean UnityEngine.GUIUtility::get_textFieldInput()
		void Register_UnityEngine_GUIUtility_get_textFieldInput();
		Register_UnityEngine_GUIUtility_get_textFieldInput();

		//System.Int32 UnityEngine.GUIUtility::CheckForTabEvent_Injected(System.IntPtr)
		void Register_UnityEngine_GUIUtility_CheckForTabEvent_Injected();
		Register_UnityEngine_GUIUtility_CheckForTabEvent_Injected();

		//System.Int32 UnityEngine.GUIUtility::Internal_GetControlID_Injected(System.Int32,UnityEngine.FocusType,UnityEngine.Rect&)
		void Register_UnityEngine_GUIUtility_Internal_GetControlID_Injected();
		Register_UnityEngine_GUIUtility_Internal_GetControlID_Injected();

		//System.Int32 UnityEngine.GUIUtility::Internal_GetHotControl()
		void Register_UnityEngine_GUIUtility_Internal_GetHotControl();
		Register_UnityEngine_GUIUtility_Internal_GetHotControl();

		//System.Int32 UnityEngine.GUIUtility::Internal_GetKeyboardControl()
		void Register_UnityEngine_GUIUtility_Internal_GetKeyboardControl();
		Register_UnityEngine_GUIUtility_Internal_GetKeyboardControl();

		//System.Int32 UnityEngine.GUIUtility::get_guiDepth()
		void Register_UnityEngine_GUIUtility_get_guiDepth();
		Register_UnityEngine_GUIUtility_get_guiDepth();

		//System.Object UnityEngine.GUIUtility::Internal_GetDefaultSkin(System.Int32)
		void Register_UnityEngine_GUIUtility_Internal_GetDefaultSkin();
		Register_UnityEngine_GUIUtility_Internal_GetDefaultSkin();

		//System.Single UnityEngine.GUIUtility::get_pixelsPerPoint()
		void Register_UnityEngine_GUIUtility_get_pixelsPerPoint();
		Register_UnityEngine_GUIUtility_get_pixelsPerPoint();

		//System.Void UnityEngine.GUIUtility::AlignRectToDevice_Injected(UnityEngine.Rect&,System.Int32&,System.Int32&,UnityEngine.Rect&)
		void Register_UnityEngine_GUIUtility_AlignRectToDevice_Injected();
		Register_UnityEngine_GUIUtility_AlignRectToDevice_Injected();

		//System.Void UnityEngine.GUIUtility::BeginContainerFromOwner_Injected(System.IntPtr)
		void Register_UnityEngine_GUIUtility_BeginContainerFromOwner_Injected();
		Register_UnityEngine_GUIUtility_BeginContainerFromOwner_Injected();

		//System.Void UnityEngine.GUIUtility::BeginContainer_Injected(System.IntPtr)
		void Register_UnityEngine_GUIUtility_BeginContainer_Injected();
		Register_UnityEngine_GUIUtility_BeginContainer_Injected();

		//System.Void UnityEngine.GUIUtility::Internal_EndContainer()
		void Register_UnityEngine_GUIUtility_Internal_EndContainer();
		Register_UnityEngine_GUIUtility_Internal_EndContainer();

		//System.Void UnityEngine.GUIUtility::Internal_ExitGUI()
		void Register_UnityEngine_GUIUtility_Internal_ExitGUI();
		Register_UnityEngine_GUIUtility_Internal_ExitGUI();

		//System.Void UnityEngine.GUIUtility::Internal_SetHotControl(System.Int32)
		void Register_UnityEngine_GUIUtility_Internal_SetHotControl();
		Register_UnityEngine_GUIUtility_Internal_SetHotControl();

		//System.Void UnityEngine.GUIUtility::Internal_SetKeyboardControl(System.Int32)
		void Register_UnityEngine_GUIUtility_Internal_SetKeyboardControl();
		Register_UnityEngine_GUIUtility_Internal_SetKeyboardControl();

		//System.Void UnityEngine.GUIUtility::SetKeyboardControlToFirstControlId()
		void Register_UnityEngine_GUIUtility_SetKeyboardControlToFirstControlId();
		Register_UnityEngine_GUIUtility_SetKeyboardControlToFirstControlId();

		//System.Void UnityEngine.GUIUtility::SetKeyboardControlToLastControlId()
		void Register_UnityEngine_GUIUtility_SetKeyboardControlToLastControlId();
		Register_UnityEngine_GUIUtility_SetKeyboardControlToLastControlId();

		//System.Void UnityEngine.GUIUtility::get_compositionString_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_GUIUtility_get_compositionString_Injected();
		Register_UnityEngine_GUIUtility_get_compositionString_Injected();

		//System.Void UnityEngine.GUIUtility::get_systemCopyBuffer_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_GUIUtility_get_systemCopyBuffer_Injected();
		Register_UnityEngine_GUIUtility_get_systemCopyBuffer_Injected();

		//System.Void UnityEngine.GUIUtility::set_compositionCursorPos_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_GUIUtility_set_compositionCursorPos_Injected();
		Register_UnityEngine_GUIUtility_set_compositionCursorPos_Injected();

		//System.Void UnityEngine.GUIUtility::set_imeCompositionMode(UnityEngine.IMECompositionMode)
		void Register_UnityEngine_GUIUtility_set_imeCompositionMode();
		Register_UnityEngine_GUIUtility_set_imeCompositionMode();

		//System.Void UnityEngine.GUIUtility::set_pixelsPerPoint(System.Single)
		void Register_UnityEngine_GUIUtility_set_pixelsPerPoint();
		Register_UnityEngine_GUIUtility_set_pixelsPerPoint();

		//System.Void UnityEngine.GUIUtility::set_systemCopyBuffer_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_GUIUtility_set_systemCopyBuffer_Injected();
		Register_UnityEngine_GUIUtility_set_systemCopyBuffer_Injected();

	//End Registrations for type : UnityEngine.GUIUtility

	//Start Registrations for type : UnityEngine.Hash128

		//System.Void UnityEngine.Hash128::Hash128ToStringImpl_Injected(UnityEngine.Hash128&,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_Hash128_Hash128ToStringImpl_Injected();
		Register_UnityEngine_Hash128_Hash128ToStringImpl_Injected();

		//System.Void UnityEngine.Hash128::Parse_Injected(UnityEngine.Bindings.ManagedSpanWrapper&,UnityEngine.Hash128&)
		void Register_UnityEngine_Hash128_Parse_Injected();
		Register_UnityEngine_Hash128_Parse_Injected();

	//End Registrations for type : UnityEngine.Hash128

	//Start Registrations for type : UnityEngine.Input

		//System.Boolean UnityEngine.Input::CheckDisabled()
		void Register_UnityEngine_Input_CheckDisabled();
		Register_UnityEngine_Input_CheckDisabled();

		//System.Boolean UnityEngine.Input::GetKeyDownInt(UnityEngine.KeyCode)
		void Register_UnityEngine_Input_GetKeyDownInt();
		Register_UnityEngine_Input_GetKeyDownInt();

		//System.Boolean UnityEngine.Input::GetKeyInt(UnityEngine.KeyCode)
		void Register_UnityEngine_Input_GetKeyInt();
		Register_UnityEngine_Input_GetKeyInt();

		//System.Boolean UnityEngine.Input::GetKeyUpInt(UnityEngine.KeyCode)
		void Register_UnityEngine_Input_GetKeyUpInt();
		Register_UnityEngine_Input_GetKeyUpInt();

		//System.Boolean UnityEngine.Input::GetMouseButton(System.Int32)
		void Register_UnityEngine_Input_GetMouseButton();
		Register_UnityEngine_Input_GetMouseButton();

		//System.Boolean UnityEngine.Input::GetMouseButtonDown(System.Int32)
		void Register_UnityEngine_Input_GetMouseButtonDown();
		Register_UnityEngine_Input_GetMouseButtonDown();

		//System.Boolean UnityEngine.Input::GetMouseButtonUp(System.Int32)
		void Register_UnityEngine_Input_GetMouseButtonUp();
		Register_UnityEngine_Input_GetMouseButtonUp();

		//System.Boolean UnityEngine.Input::GetMousePresentInternal()
		void Register_UnityEngine_Input_GetMousePresentInternal();
		Register_UnityEngine_Input_GetMousePresentInternal();

		//System.Boolean UnityEngine.Input::GetTouchSupportedInternal()
		void Register_UnityEngine_Input_GetTouchSupportedInternal();
		Register_UnityEngine_Input_GetTouchSupportedInternal();

		//System.Boolean UnityEngine.Input::get_anyKey()
		void Register_UnityEngine_Input_get_anyKey();
		Register_UnityEngine_Input_get_anyKey();

		//System.Int32 UnityEngine.Input::get_touchCount()
		void Register_UnityEngine_Input_get_touchCount();
		Register_UnityEngine_Input_get_touchCount();

		//System.Void UnityEngine.Input::ClearLastPenContactEvent()
		void Register_UnityEngine_Input_ClearLastPenContactEvent();
		Register_UnityEngine_Input_ClearLastPenContactEvent();

		//System.Void UnityEngine.Input::GetLastPenContactEvent_Injected(UnityEngine.PenData&)
		void Register_UnityEngine_Input_GetLastPenContactEvent_Injected();
		Register_UnityEngine_Input_GetLastPenContactEvent_Injected();

		//System.Void UnityEngine.Input::GetTouch_Injected(System.Int32,UnityEngine.Touch&)
		void Register_UnityEngine_Input_GetTouch_Injected();
		Register_UnityEngine_Input_GetTouch_Injected();

		//System.Void UnityEngine.Input::get_compositionCursorPos_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_Input_get_compositionCursorPos_Injected();
		Register_UnityEngine_Input_get_compositionCursorPos_Injected();

		//System.Void UnityEngine.Input::get_compositionString_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_Input_get_compositionString_Injected();
		Register_UnityEngine_Input_get_compositionString_Injected();

		//System.Void UnityEngine.Input::get_mousePosition_Injected(UnityEngine.Vector3&)
		void Register_UnityEngine_Input_get_mousePosition_Injected();
		Register_UnityEngine_Input_get_mousePosition_Injected();

		//System.Void UnityEngine.Input::get_mouseScrollDelta_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_Input_get_mouseScrollDelta_Injected();
		Register_UnityEngine_Input_get_mouseScrollDelta_Injected();

		//System.Void UnityEngine.Input::set_compositionCursorPos_Injected(UnityEngine.Vector2&)
		void Register_UnityEngine_Input_set_compositionCursorPos_Injected();
		Register_UnityEngine_Input_set_compositionCursorPos_Injected();

		//System.Void UnityEngine.Input::set_imeCompositionMode(UnityEngine.IMECompositionMode)
		void Register_UnityEngine_Input_set_imeCompositionMode();
		Register_UnityEngine_Input_set_imeCompositionMode();

		//UnityEngine.IMECompositionMode UnityEngine.Input::get_imeCompositionMode()
		void Register_UnityEngine_Input_get_imeCompositionMode();
		Register_UnityEngine_Input_get_imeCompositionMode();

	//End Registrations for type : UnityEngine.Input

	//Start Registrations for type : UnityEngine.IntegratedSubsystem

		//System.Void UnityEngine.IntegratedSubsystem::SetHandle_Injected(System.IntPtr,UnityEngine.IntegratedSubsystem)
		void Register_UnityEngine_IntegratedSubsystem_SetHandle_Injected();
		Register_UnityEngine_IntegratedSubsystem_SetHandle_Injected();

	//End Registrations for type : UnityEngine.IntegratedSubsystem

	//Start Registrations for type : UnityEngine.Internal.InputUnsafeUtility

		//System.Boolean UnityEngine.Internal.InputUnsafeUtility::GetButtonDown_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_Internal_InputUnsafeUtility_GetButtonDown_Injected();
		Register_UnityEngine_Internal_InputUnsafeUtility_GetButtonDown_Injected();

		//System.Boolean UnityEngine.Internal.InputUnsafeUtility::GetButtonUp__Unmanaged(System.Byte*,System.Int32)
		void Register_UnityEngine_Internal_InputUnsafeUtility_GetButtonUp__Unmanaged();
		Register_UnityEngine_Internal_InputUnsafeUtility_GetButtonUp__Unmanaged();

		//System.Boolean UnityEngine.Internal.InputUnsafeUtility::GetButton__Unmanaged(System.Byte*,System.Int32)
		void Register_UnityEngine_Internal_InputUnsafeUtility_GetButton__Unmanaged();
		Register_UnityEngine_Internal_InputUnsafeUtility_GetButton__Unmanaged();

		//System.Boolean UnityEngine.Internal.InputUnsafeUtility::GetKeyDownString__Unmanaged(System.Byte*,System.Int32)
		void Register_UnityEngine_Internal_InputUnsafeUtility_GetKeyDownString__Unmanaged();
		Register_UnityEngine_Internal_InputUnsafeUtility_GetKeyDownString__Unmanaged();

		//System.Boolean UnityEngine.Internal.InputUnsafeUtility::GetKeyString__Unmanaged(System.Byte*,System.Int32)
		void Register_UnityEngine_Internal_InputUnsafeUtility_GetKeyString__Unmanaged();
		Register_UnityEngine_Internal_InputUnsafeUtility_GetKeyString__Unmanaged();

		//System.Boolean UnityEngine.Internal.InputUnsafeUtility::GetKeyUpString__Unmanaged(System.Byte*,System.Int32)
		void Register_UnityEngine_Internal_InputUnsafeUtility_GetKeyUpString__Unmanaged();
		Register_UnityEngine_Internal_InputUnsafeUtility_GetKeyUpString__Unmanaged();

		//System.Byte UnityEngine.Internal.InputUnsafeUtility::GetButtonDown__Unmanaged(System.Byte*,System.Int32)
		void Register_UnityEngine_Internal_InputUnsafeUtility_GetButtonDown__Unmanaged();
		Register_UnityEngine_Internal_InputUnsafeUtility_GetButtonDown__Unmanaged();

		//System.Single UnityEngine.Internal.InputUnsafeUtility::GetAxisRaw_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_Internal_InputUnsafeUtility_GetAxisRaw_Injected();
		Register_UnityEngine_Internal_InputUnsafeUtility_GetAxisRaw_Injected();

		//System.Single UnityEngine.Internal.InputUnsafeUtility::GetAxisRaw__Unmanaged(System.Byte*,System.Int32)
		void Register_UnityEngine_Internal_InputUnsafeUtility_GetAxisRaw__Unmanaged();
		Register_UnityEngine_Internal_InputUnsafeUtility_GetAxisRaw__Unmanaged();

		//System.Single UnityEngine.Internal.InputUnsafeUtility::GetAxis_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_Internal_InputUnsafeUtility_GetAxis_Injected();
		Register_UnityEngine_Internal_InputUnsafeUtility_GetAxis_Injected();

		//System.Single UnityEngine.Internal.InputUnsafeUtility::GetAxis__Unmanaged(System.Byte*,System.Int32)
		void Register_UnityEngine_Internal_InputUnsafeUtility_GetAxis__Unmanaged();
		Register_UnityEngine_Internal_InputUnsafeUtility_GetAxis__Unmanaged();

	//End Registrations for type : UnityEngine.Internal.InputUnsafeUtility

	//Start Registrations for type : UnityEngine.JsonUtility

		//System.Object UnityEngine.JsonUtility::FromJsonInternal_Injected(UnityEngine.Bindings.ManagedSpanWrapper&,System.Object,System.Type)
		void Register_UnityEngine_JsonUtility_FromJsonInternal_Injected();
		Register_UnityEngine_JsonUtility_FromJsonInternal_Injected();

		//System.Void UnityEngine.JsonUtility::ToJsonInternal_Injected(System.Object,System.Boolean,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_JsonUtility_ToJsonInternal_Injected();
		Register_UnityEngine_JsonUtility_ToJsonInternal_Injected();

	//End Registrations for type : UnityEngine.JsonUtility

	//Start Registrations for type : UnityEngine.Light

		//System.Boolean UnityEngine.Light::get_useColorTemperature_Injected(System.IntPtr)
		void Register_UnityEngine_Light_get_useColorTemperature_Injected();
		Register_UnityEngine_Light_get_useColorTemperature_Injected();

		//System.IntPtr UnityEngine.Light::get_cookie_Injected(System.IntPtr)
		void Register_UnityEngine_Light_get_cookie_Injected();
		Register_UnityEngine_Light_get_cookie_Injected();

		//System.Single UnityEngine.Light::get_bounceIntensity_Injected(System.IntPtr)
		void Register_UnityEngine_Light_get_bounceIntensity_Injected();
		Register_UnityEngine_Light_get_bounceIntensity_Injected();

		//System.Single UnityEngine.Light::get_colorTemperature_Injected(System.IntPtr)
		void Register_UnityEngine_Light_get_colorTemperature_Injected();
		Register_UnityEngine_Light_get_colorTemperature_Injected();

		//System.Single UnityEngine.Light::get_cookieSize_Injected(System.IntPtr)
		void Register_UnityEngine_Light_get_cookieSize_Injected();
		Register_UnityEngine_Light_get_cookieSize_Injected();

		//System.Single UnityEngine.Light::get_dilatedRange_Injected(System.IntPtr)
		void Register_UnityEngine_Light_get_dilatedRange_Injected();
		Register_UnityEngine_Light_get_dilatedRange_Injected();

		//System.Single UnityEngine.Light::get_intensity_Injected(System.IntPtr)
		void Register_UnityEngine_Light_get_intensity_Injected();
		Register_UnityEngine_Light_get_intensity_Injected();

		//System.Single UnityEngine.Light::get_range_Injected(System.IntPtr)
		void Register_UnityEngine_Light_get_range_Injected();
		Register_UnityEngine_Light_get_range_Injected();

		//System.Single UnityEngine.Light::get_spotAngle_Injected(System.IntPtr)
		void Register_UnityEngine_Light_get_spotAngle_Injected();
		Register_UnityEngine_Light_get_spotAngle_Injected();

		//System.Void UnityEngine.Light::get_bakingOutput_Injected(System.IntPtr,UnityEngine.LightBakingOutput&)
		void Register_UnityEngine_Light_get_bakingOutput_Injected();
		Register_UnityEngine_Light_get_bakingOutput_Injected();

		//System.Void UnityEngine.Light::get_color_Injected(System.IntPtr,UnityEngine.Color&)
		void Register_UnityEngine_Light_get_color_Injected();
		Register_UnityEngine_Light_get_color_Injected();

		//UnityEngine.LightShadows UnityEngine.Light::get_shadows_Injected(System.IntPtr)
		void Register_UnityEngine_Light_get_shadows_Injected();
		Register_UnityEngine_Light_get_shadows_Injected();

		//UnityEngine.LightType UnityEngine.Light::get_type_Injected(System.IntPtr)
		void Register_UnityEngine_Light_get_type_Injected();
		Register_UnityEngine_Light_get_type_Injected();

	//End Registrations for type : UnityEngine.Light

	//Start Registrations for type : UnityEngine.Material

		//System.Boolean UnityEngine.Material::HasFloatImpl_Injected(System.IntPtr,System.Int32)
		void Register_UnityEngine_Material_HasFloatImpl_Injected();
		Register_UnityEngine_Material_HasFloatImpl_Injected();

		//System.Boolean UnityEngine.Material::HasProperty_Injected(System.IntPtr,System.Int32)
		void Register_UnityEngine_Material_HasProperty_Injected();
		Register_UnityEngine_Material_HasProperty_Injected();

		//System.Boolean UnityEngine.Material::SetPass_Injected(System.IntPtr,System.Int32)
		void Register_UnityEngine_Material_SetPass_Injected();
		Register_UnityEngine_Material_SetPass_Injected();

		//System.Int32 UnityEngine.Material::ComputeCRC_Injected(System.IntPtr)
		void Register_UnityEngine_Material_ComputeCRC_Injected();
		Register_UnityEngine_Material_ComputeCRC_Injected();

		//System.Int32 UnityEngine.Material::GetFirstPropertyNameIdByAttribute_Injected(System.IntPtr,UnityEngine.Rendering.ShaderPropertyFlags)
		void Register_UnityEngine_Material_GetFirstPropertyNameIdByAttribute_Injected();
		Register_UnityEngine_Material_GetFirstPropertyNameIdByAttribute_Injected();

		//System.IntPtr UnityEngine.Material::GetTextureImpl_Injected(System.IntPtr,System.Int32)
		void Register_UnityEngine_Material_GetTextureImpl_Injected();
		Register_UnityEngine_Material_GetTextureImpl_Injected();

		//System.IntPtr UnityEngine.Material::get_shader_Injected(System.IntPtr)
		void Register_UnityEngine_Material_get_shader_Injected();
		Register_UnityEngine_Material_get_shader_Injected();

		//System.Single UnityEngine.Material::GetFloatImpl_Injected(System.IntPtr,System.Int32)
		void Register_UnityEngine_Material_GetFloatImpl_Injected();
		Register_UnityEngine_Material_GetFloatImpl_Injected();

		//System.String[] UnityEngine.Material::GetShaderKeywords_Injected(System.IntPtr)
		void Register_UnityEngine_Material_GetShaderKeywords_Injected();
		Register_UnityEngine_Material_GetShaderKeywords_Injected();

		//System.Void UnityEngine.Material::CopyPropertiesFromMaterial_Injected(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_Material_CopyPropertiesFromMaterial_Injected();
		Register_UnityEngine_Material_CopyPropertiesFromMaterial_Injected();

		//System.Void UnityEngine.Material::CreateWithMaterial_Injected(UnityEngine.Material,System.IntPtr)
		void Register_UnityEngine_Material_CreateWithMaterial_Injected();
		Register_UnityEngine_Material_CreateWithMaterial_Injected();

		//System.Void UnityEngine.Material::CreateWithShader_Injected(UnityEngine.Material,System.IntPtr)
		void Register_UnityEngine_Material_CreateWithShader_Injected();
		Register_UnityEngine_Material_CreateWithShader_Injected();

		//System.Void UnityEngine.Material::CreateWithString(UnityEngine.Material)
		void Register_UnityEngine_Material_CreateWithString();
		Register_UnityEngine_Material_CreateWithString();

		//System.Void UnityEngine.Material::DisableKeyword_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_Material_DisableKeyword_Injected();
		Register_UnityEngine_Material_DisableKeyword_Injected();

		//System.Void UnityEngine.Material::EnableKeyword_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_Material_EnableKeyword_Injected();
		Register_UnityEngine_Material_EnableKeyword_Injected();

		//System.Void UnityEngine.Material::SetFloatImpl_Injected(System.IntPtr,System.Int32,System.Single)
		void Register_UnityEngine_Material_SetFloatImpl_Injected();
		Register_UnityEngine_Material_SetFloatImpl_Injected();

		//System.Void UnityEngine.Material::SetShaderKeywords_Injected(System.IntPtr,System.String[])
		void Register_UnityEngine_Material_SetShaderKeywords_Injected();
		Register_UnityEngine_Material_SetShaderKeywords_Injected();

		//System.Void UnityEngine.Material::SetTextureImpl_Injected(System.IntPtr,System.Int32,System.IntPtr)
		void Register_UnityEngine_Material_SetTextureImpl_Injected();
		Register_UnityEngine_Material_SetTextureImpl_Injected();

		//System.Void UnityEngine.Material::SetTextureOffsetImpl_Injected(System.IntPtr,System.Int32,UnityEngine.Vector2&)
		void Register_UnityEngine_Material_SetTextureOffsetImpl_Injected();
		Register_UnityEngine_Material_SetTextureOffsetImpl_Injected();

		//System.Void UnityEngine.Material::set_shader_Injected(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_Material_set_shader_Injected();
		Register_UnityEngine_Material_set_shader_Injected();

	//End Registrations for type : UnityEngine.Material

	//Start Registrations for type : UnityEngine.MaterialPropertyBlock

		//System.IntPtr UnityEngine.MaterialPropertyBlock::CreateImpl()
		void Register_UnityEngine_MaterialPropertyBlock_CreateImpl();
		Register_UnityEngine_MaterialPropertyBlock_CreateImpl();

		//System.Void UnityEngine.MaterialPropertyBlock::Clear_Injected(System.IntPtr,System.Boolean)
		void Register_UnityEngine_MaterialPropertyBlock_Clear_Injected();
		Register_UnityEngine_MaterialPropertyBlock_Clear_Injected();

		//System.Void UnityEngine.MaterialPropertyBlock::DestroyImpl(System.IntPtr)
		void Register_UnityEngine_MaterialPropertyBlock_DestroyImpl();
		Register_UnityEngine_MaterialPropertyBlock_DestroyImpl();

		//System.Void UnityEngine.MaterialPropertyBlock::SetTextureImpl_Injected(System.IntPtr,System.Int32,System.IntPtr)
		void Register_UnityEngine_MaterialPropertyBlock_SetTextureImpl_Injected();
		Register_UnityEngine_MaterialPropertyBlock_SetTextureImpl_Injected();

		//System.Void UnityEngine.MaterialPropertyBlock::SetVectorArrayImpl_Injected(System.IntPtr,System.Int32,UnityEngine.Bindings.ManagedSpanWrapper&,System.Int32)
		void Register_UnityEngine_MaterialPropertyBlock_SetVectorArrayImpl_Injected();
		Register_UnityEngine_MaterialPropertyBlock_SetVectorArrayImpl_Injected();

	//End Registrations for type : UnityEngine.MaterialPropertyBlock

	//Start Registrations for type : UnityEngine.Mathf

		//System.Single UnityEngine.Mathf::GammaToLinearSpace(System.Single)
		void Register_UnityEngine_Mathf_GammaToLinearSpace();
		Register_UnityEngine_Mathf_GammaToLinearSpace();

		//System.Void UnityEngine.Mathf::CorrelatedColorTemperatureToRGB_Injected(System.Single,UnityEngine.Color&)
		void Register_UnityEngine_Mathf_CorrelatedColorTemperatureToRGB_Injected();
		Register_UnityEngine_Mathf_CorrelatedColorTemperatureToRGB_Injected();

	//End Registrations for type : UnityEngine.Mathf

	//Start Registrations for type : UnityEngine.Matrix4x4

		//System.Boolean UnityEngine.Matrix4x4::Inverse3DAffine_Injected(UnityEngine.Matrix4x4&,UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Matrix4x4_Inverse3DAffine_Injected();
		Register_UnityEngine_Matrix4x4_Inverse3DAffine_Injected();

		//System.Void UnityEngine.Matrix4x4::GetLossyScale_Injected(UnityEngine.Matrix4x4&,UnityEngine.Vector3&)
		void Register_UnityEngine_Matrix4x4_GetLossyScale_Injected();
		Register_UnityEngine_Matrix4x4_GetLossyScale_Injected();

		//System.Void UnityEngine.Matrix4x4::GetRotation_Injected(UnityEngine.Matrix4x4&,UnityEngine.Quaternion&)
		void Register_UnityEngine_Matrix4x4_GetRotation_Injected();
		Register_UnityEngine_Matrix4x4_GetRotation_Injected();

		//System.Void UnityEngine.Matrix4x4::Inverse_Injected(UnityEngine.Matrix4x4&,UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Matrix4x4_Inverse_Injected();
		Register_UnityEngine_Matrix4x4_Inverse_Injected();

		//System.Void UnityEngine.Matrix4x4::TRS_Injected(UnityEngine.Vector3&,UnityEngine.Quaternion&,UnityEngine.Vector3&,UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Matrix4x4_TRS_Injected();
		Register_UnityEngine_Matrix4x4_TRS_Injected();

	//End Registrations for type : UnityEngine.Matrix4x4

	//Start Registrations for type : UnityEngine.Mesh

		//System.Array UnityEngine.Mesh::GetAllocArrayFromChannelImpl_Injected(System.IntPtr,UnityEngine.Rendering.VertexAttribute,UnityEngine.Rendering.VertexAttributeFormat,System.Int32)
		void Register_UnityEngine_Mesh_GetAllocArrayFromChannelImpl_Injected();
		Register_UnityEngine_Mesh_GetAllocArrayFromChannelImpl_Injected();

		//System.Boolean UnityEngine.Mesh::HasVertexAttribute_Injected(System.IntPtr,UnityEngine.Rendering.VertexAttribute)
		void Register_UnityEngine_Mesh_HasVertexAttribute_Injected();
		Register_UnityEngine_Mesh_HasVertexAttribute_Injected();

		//System.Boolean UnityEngine.Mesh::get_canAccess_Injected(System.IntPtr)
		void Register_UnityEngine_Mesh_get_canAccess_Injected();
		Register_UnityEngine_Mesh_get_canAccess_Injected();

		//System.Int32 UnityEngine.Mesh::get_subMeshCount_Injected(System.IntPtr)
		void Register_UnityEngine_Mesh_get_subMeshCount_Injected();
		Register_UnityEngine_Mesh_get_subMeshCount_Injected();

		//System.Int32 UnityEngine.Mesh::get_vertexCount_Injected(System.IntPtr)
		void Register_UnityEngine_Mesh_get_vertexCount_Injected();
		Register_UnityEngine_Mesh_get_vertexCount_Injected();

		//System.Void UnityEngine.Mesh::ClearImpl_Injected(System.IntPtr,System.Boolean)
		void Register_UnityEngine_Mesh_ClearImpl_Injected();
		Register_UnityEngine_Mesh_ClearImpl_Injected();

		//System.Void UnityEngine.Mesh::GetArrayFromChannelImpl_Injected(System.IntPtr,UnityEngine.Rendering.VertexAttribute,UnityEngine.Rendering.VertexAttributeFormat,System.Int32,System.Array)
		void Register_UnityEngine_Mesh_GetArrayFromChannelImpl_Injected();
		Register_UnityEngine_Mesh_GetArrayFromChannelImpl_Injected();

		//System.Void UnityEngine.Mesh::GetIndicesImpl_Injected(System.IntPtr,System.Int32,System.Boolean,UnityEngine.Bindings.BlittableArrayWrapper&)
		void Register_UnityEngine_Mesh_GetIndicesImpl_Injected();
		Register_UnityEngine_Mesh_GetIndicesImpl_Injected();

		//System.Void UnityEngine.Mesh::Internal_Create(UnityEngine.Mesh)
		void Register_UnityEngine_Mesh_Internal_Create();
		Register_UnityEngine_Mesh_Internal_Create();

		//System.Void UnityEngine.Mesh::PrintErrorCantAccessChannel_Injected(System.IntPtr,UnityEngine.Rendering.VertexAttribute)
		void Register_UnityEngine_Mesh_PrintErrorCantAccessChannel_Injected();
		Register_UnityEngine_Mesh_PrintErrorCantAccessChannel_Injected();

		//System.Void UnityEngine.Mesh::RecalculateBoundsImpl_Injected(System.IntPtr,UnityEngine.Rendering.MeshUpdateFlags)
		void Register_UnityEngine_Mesh_RecalculateBoundsImpl_Injected();
		Register_UnityEngine_Mesh_RecalculateBoundsImpl_Injected();

		//System.Void UnityEngine.Mesh::SetArrayForChannelImpl_Injected(System.IntPtr,UnityEngine.Rendering.VertexAttribute,UnityEngine.Rendering.VertexAttributeFormat,System.Int32,System.Array,System.Int32,System.Int32,System.Int32,UnityEngine.Rendering.MeshUpdateFlags)
		void Register_UnityEngine_Mesh_SetArrayForChannelImpl_Injected();
		Register_UnityEngine_Mesh_SetArrayForChannelImpl_Injected();

		//System.Void UnityEngine.Mesh::SetIndicesImpl_Injected(System.IntPtr,System.Int32,UnityEngine.MeshTopology,UnityEngine.Rendering.IndexFormat,System.Array,System.Int32,System.Int32,System.Boolean,System.Int32)
		void Register_UnityEngine_Mesh_SetIndicesImpl_Injected();
		Register_UnityEngine_Mesh_SetIndicesImpl_Injected();

	//End Registrations for type : UnityEngine.Mesh

	//Start Registrations for type : UnityEngine.MonoBehaviour

		//System.Boolean UnityEngine.MonoBehaviour::Internal_IsInvokingAll_Injected(System.IntPtr)
		void Register_UnityEngine_MonoBehaviour_Internal_IsInvokingAll_Injected();
		Register_UnityEngine_MonoBehaviour_Internal_IsInvokingAll_Injected();

		//System.Boolean UnityEngine.MonoBehaviour::IsInvoking_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_MonoBehaviour_IsInvoking_Injected();
		Register_UnityEngine_MonoBehaviour_IsInvoking_Injected();

		//System.Boolean UnityEngine.MonoBehaviour::IsObjectMonoBehaviour_Injected(System.IntPtr)
		void Register_UnityEngine_MonoBehaviour_IsObjectMonoBehaviour_Injected();
		Register_UnityEngine_MonoBehaviour_IsObjectMonoBehaviour_Injected();

		//System.Boolean UnityEngine.MonoBehaviour::get_didAwake_Injected(System.IntPtr)
		void Register_UnityEngine_MonoBehaviour_get_didAwake_Injected();
		Register_UnityEngine_MonoBehaviour_get_didAwake_Injected();

		//System.Boolean UnityEngine.MonoBehaviour::get_didStart_Injected(System.IntPtr)
		void Register_UnityEngine_MonoBehaviour_get_didStart_Injected();
		Register_UnityEngine_MonoBehaviour_get_didStart_Injected();

		//System.Boolean UnityEngine.MonoBehaviour::get_useGUILayout_Injected(System.IntPtr)
		void Register_UnityEngine_MonoBehaviour_get_useGUILayout_Injected();
		Register_UnityEngine_MonoBehaviour_get_useGUILayout_Injected();

		//System.Void UnityEngine.MonoBehaviour::CancelInvoke_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_MonoBehaviour_CancelInvoke_Injected();
		Register_UnityEngine_MonoBehaviour_CancelInvoke_Injected();

		//System.Void UnityEngine.MonoBehaviour::GetScriptClassName_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_MonoBehaviour_GetScriptClassName_Injected();
		Register_UnityEngine_MonoBehaviour_GetScriptClassName_Injected();

		//System.Void UnityEngine.MonoBehaviour::Internal_CancelInvokeAll_Injected(System.IntPtr)
		void Register_UnityEngine_MonoBehaviour_Internal_CancelInvokeAll_Injected();
		Register_UnityEngine_MonoBehaviour_Internal_CancelInvokeAll_Injected();

		//System.Void UnityEngine.MonoBehaviour::InvokeDelayed_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&,System.Single,System.Single)
		void Register_UnityEngine_MonoBehaviour_InvokeDelayed_Injected();
		Register_UnityEngine_MonoBehaviour_InvokeDelayed_Injected();

		//System.Void UnityEngine.MonoBehaviour::OnCancellationTokenCreated_Injected(System.IntPtr)
		void Register_UnityEngine_MonoBehaviour_OnCancellationTokenCreated_Injected();
		Register_UnityEngine_MonoBehaviour_OnCancellationTokenCreated_Injected();

		//System.Void UnityEngine.MonoBehaviour::StopAllCoroutines_Injected(System.IntPtr)
		void Register_UnityEngine_MonoBehaviour_StopAllCoroutines_Injected();
		Register_UnityEngine_MonoBehaviour_StopAllCoroutines_Injected();

		//System.Void UnityEngine.MonoBehaviour::StopCoroutineFromEnumeratorManaged_Injected(System.IntPtr,System.Collections.IEnumerator)
		void Register_UnityEngine_MonoBehaviour_StopCoroutineFromEnumeratorManaged_Injected();
		Register_UnityEngine_MonoBehaviour_StopCoroutineFromEnumeratorManaged_Injected();

		//System.Void UnityEngine.MonoBehaviour::StopCoroutineManaged_Injected(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_MonoBehaviour_StopCoroutineManaged_Injected();
		Register_UnityEngine_MonoBehaviour_StopCoroutineManaged_Injected();

		//System.Void UnityEngine.MonoBehaviour::StopCoroutine_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_MonoBehaviour_StopCoroutine_Injected();
		Register_UnityEngine_MonoBehaviour_StopCoroutine_Injected();

		//System.Void UnityEngine.MonoBehaviour::set_useGUILayout_Injected(System.IntPtr,System.Boolean)
		void Register_UnityEngine_MonoBehaviour_set_useGUILayout_Injected();
		Register_UnityEngine_MonoBehaviour_set_useGUILayout_Injected();

		//UnityEngine.Coroutine UnityEngine.MonoBehaviour::StartCoroutineManaged2_Injected(System.IntPtr,System.Collections.IEnumerator)
		void Register_UnityEngine_MonoBehaviour_StartCoroutineManaged2_Injected();
		Register_UnityEngine_MonoBehaviour_StartCoroutineManaged2_Injected();

		//UnityEngine.Coroutine UnityEngine.MonoBehaviour::StartCoroutineManaged_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&,System.Object)
		void Register_UnityEngine_MonoBehaviour_StartCoroutineManaged_Injected();
		Register_UnityEngine_MonoBehaviour_StartCoroutineManaged_Injected();

	//End Registrations for type : UnityEngine.MonoBehaviour

	//Start Registrations for type : UnityEngine.NameFormatter

		//System.Void UnityEngine.NameFormatter::FormatVariableName_Injected(UnityEngine.Bindings.ManagedSpanWrapper&,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_NameFormatter_FormatVariableName_Injected();
		Register_UnityEngine_NameFormatter_FormatVariableName_Injected();

	//End Registrations for type : UnityEngine.NameFormatter

	//Start Registrations for type : UnityEngine.Object

		//System.Boolean UnityEngine.Object::IsPersistent_Injected(System.IntPtr)
		void Register_UnityEngine_Object_IsPersistent_Injected();
		Register_UnityEngine_Object_IsPersistent_Injected();

		//System.Int32 UnityEngine.Object::GetOffsetOfInstanceIDInCPlusPlusObject()
		void Register_UnityEngine_Object_GetOffsetOfInstanceIDInCPlusPlusObject();
		Register_UnityEngine_Object_GetOffsetOfInstanceIDInCPlusPlusObject();

		//System.IntPtr UnityEngine.Object::FindObjectFromInstanceID_Injected(System.Int32)
		void Register_UnityEngine_Object_FindObjectFromInstanceID_Injected();
		Register_UnityEngine_Object_FindObjectFromInstanceID_Injected();

		//System.IntPtr UnityEngine.Object::ForceLoadFromInstanceID_Injected(System.Int32)
		void Register_UnityEngine_Object_ForceLoadFromInstanceID_Injected();
		Register_UnityEngine_Object_ForceLoadFromInstanceID_Injected();

		//System.IntPtr UnityEngine.Object::Internal_CloneSingle_Injected(System.IntPtr)
		void Register_UnityEngine_Object_Internal_CloneSingle_Injected();
		Register_UnityEngine_Object_Internal_CloneSingle_Injected();

		//System.Void UnityEngine.Object::DestroyImmediate_Injected(System.IntPtr,System.Boolean)
		void Register_UnityEngine_Object_DestroyImmediate_Injected();
		Register_UnityEngine_Object_DestroyImmediate_Injected();

		//System.Void UnityEngine.Object::Destroy_Injected(System.IntPtr,System.Single)
		void Register_UnityEngine_Object_Destroy_Injected();
		Register_UnityEngine_Object_Destroy_Injected();

		//System.Void UnityEngine.Object::GetName_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_Object_GetName_Injected();
		Register_UnityEngine_Object_GetName_Injected();

		//System.Void UnityEngine.Object::SetName_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_Object_SetName_Injected();
		Register_UnityEngine_Object_SetName_Injected();

		//System.Void UnityEngine.Object::ToString_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_Object_ToString_Injected();
		Register_UnityEngine_Object_ToString_Injected();

		//System.Void UnityEngine.Object::set_hideFlags_Injected(System.IntPtr,UnityEngine.HideFlags)
		void Register_UnityEngine_Object_set_hideFlags_Injected();
		Register_UnityEngine_Object_set_hideFlags_Injected();

		//UnityEngine.HideFlags UnityEngine.Object::get_hideFlags_Injected(System.IntPtr)
		void Register_UnityEngine_Object_get_hideFlags_Injected();
		Register_UnityEngine_Object_get_hideFlags_Injected();

		//UnityEngine.Object[] UnityEngine.Object::FindObjectsByType(System.Type,UnityEngine.FindObjectsInactive,UnityEngine.FindObjectsSortMode)
		void Register_UnityEngine_Object_FindObjectsByType();
		Register_UnityEngine_Object_FindObjectsByType();

		//UnityEngine.Object[] UnityEngine.Object::FindObjectsOfType(System.Type,System.Boolean)
		void Register_UnityEngine_Object_FindObjectsOfType();
		Register_UnityEngine_Object_FindObjectsOfType();

	//End Registrations for type : UnityEngine.Object

	//Start Registrations for type : UnityEngine.ObjectGUIState

		//System.IntPtr UnityEngine.ObjectGUIState::Internal_Create()
		void Register_UnityEngine_ObjectGUIState_Internal_Create();
		Register_UnityEngine_ObjectGUIState_Internal_Create();

		//System.Void UnityEngine.ObjectGUIState::Internal_Destroy(System.IntPtr)
		void Register_UnityEngine_ObjectGUIState_Internal_Destroy();
		Register_UnityEngine_ObjectGUIState_Internal_Destroy();

	//End Registrations for type : UnityEngine.ObjectGUIState

	//Start Registrations for type : UnityEngine.Physics

		//System.Boolean UnityEngine.Physics::get_invokeCollisionCallbacks()
		void Register_UnityEngine_Physics_get_invokeCollisionCallbacks();
		Register_UnityEngine_Physics_get_invokeCollisionCallbacks();

		//System.Boolean UnityEngine.Physics::get_reuseCollisionCallbacks()
		void Register_UnityEngine_Physics_get_reuseCollisionCallbacks();
		Register_UnityEngine_Physics_get_reuseCollisionCallbacks();

		//System.IntPtr UnityEngine.Physics::GetBodyByInstanceID_Injected(System.Int32)
		void Register_UnityEngine_Physics_GetBodyByInstanceID_Injected();
		Register_UnityEngine_Physics_GetBodyByInstanceID_Injected();

		//System.IntPtr UnityEngine.Physics::GetColliderByInstanceID_Injected(System.Int32)
		void Register_UnityEngine_Physics_GetColliderByInstanceID_Injected();
		Register_UnityEngine_Physics_GetColliderByInstanceID_Injected();

		//System.Void UnityEngine.Physics::Internal_RaycastAll_Injected(UnityEngine.PhysicsScene&,UnityEngine.Ray&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction,UnityEngine.Bindings.BlittableArrayWrapper&)
		void Register_UnityEngine_Physics_Internal_RaycastAll_Injected();
		Register_UnityEngine_Physics_Internal_RaycastAll_Injected();

		//System.Void UnityEngine.Physics::Query_CapsuleCastAll_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single,UnityEngine.Vector3&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction,UnityEngine.Bindings.BlittableArrayWrapper&)
		void Register_UnityEngine_Physics_Query_CapsuleCastAll_Injected();
		Register_UnityEngine_Physics_Query_CapsuleCastAll_Injected();

		//System.Void UnityEngine.Physics::SendOnCollisionEnter_Injected(System.IntPtr,UnityEngine.Collision)
		void Register_UnityEngine_Physics_SendOnCollisionEnter_Injected();
		Register_UnityEngine_Physics_SendOnCollisionEnter_Injected();

		//System.Void UnityEngine.Physics::SendOnCollisionExit_Injected(System.IntPtr,UnityEngine.Collision)
		void Register_UnityEngine_Physics_SendOnCollisionExit_Injected();
		Register_UnityEngine_Physics_SendOnCollisionExit_Injected();

		//System.Void UnityEngine.Physics::SendOnCollisionStay_Injected(System.IntPtr,UnityEngine.Collision)
		void Register_UnityEngine_Physics_SendOnCollisionStay_Injected();
		Register_UnityEngine_Physics_SendOnCollisionStay_Injected();

		//System.Void UnityEngine.Physics::get_defaultPhysicsScene_Injected(UnityEngine.PhysicsScene&)
		void Register_UnityEngine_Physics_get_defaultPhysicsScene_Injected();
		Register_UnityEngine_Physics_get_defaultPhysicsScene_Injected();

	//End Registrations for type : UnityEngine.Physics

	//Start Registrations for type : UnityEngine.Physics2D

		//System.Boolean UnityEngine.Physics2D::get_queriesHitTriggers()
		void Register_UnityEngine_Physics2D_get_queriesHitTriggers();
		Register_UnityEngine_Physics2D_get_queriesHitTriggers();

		//System.Void UnityEngine.Physics2D::GetRayIntersectionAll_Internal_Injected(UnityEngine.PhysicsScene2D&,UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single,System.Int32,UnityEngine.Bindings.BlittableArrayWrapper&)
		void Register_UnityEngine_Physics2D_GetRayIntersectionAll_Internal_Injected();
		Register_UnityEngine_Physics2D_GetRayIntersectionAll_Internal_Injected();

	//End Registrations for type : UnityEngine.Physics2D

	//Start Registrations for type : UnityEngine.PhysicsMaterial

		//System.Void UnityEngine.PhysicsMaterial::Internal_CreateDynamicsMaterial_Injected(UnityEngine.PhysicsMaterial,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_PhysicsMaterial_Internal_CreateDynamicsMaterial_Injected();
		Register_UnityEngine_PhysicsMaterial_Internal_CreateDynamicsMaterial_Injected();

		//System.Void UnityEngine.PhysicsMaterial::set_dynamicFriction_Injected(System.IntPtr,System.Single)
		void Register_UnityEngine_PhysicsMaterial_set_dynamicFriction_Injected();
		Register_UnityEngine_PhysicsMaterial_set_dynamicFriction_Injected();

		//System.Void UnityEngine.PhysicsMaterial::set_frictionCombine_Injected(System.IntPtr,UnityEngine.PhysicsMaterialCombine)
		void Register_UnityEngine_PhysicsMaterial_set_frictionCombine_Injected();
		Register_UnityEngine_PhysicsMaterial_set_frictionCombine_Injected();

		//System.Void UnityEngine.PhysicsMaterial::set_staticFriction_Injected(System.IntPtr,System.Single)
		void Register_UnityEngine_PhysicsMaterial_set_staticFriction_Injected();
		Register_UnityEngine_PhysicsMaterial_set_staticFriction_Injected();

	//End Registrations for type : UnityEngine.PhysicsMaterial

	//Start Registrations for type : UnityEngine.PhysicsScene

		//System.Boolean UnityEngine.PhysicsScene::Internal_RaycastTest_Injected(UnityEngine.PhysicsScene&,UnityEngine.Ray&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_PhysicsScene_Internal_RaycastTest_Injected();
		Register_UnityEngine_PhysicsScene_Internal_RaycastTest_Injected();

		//System.Boolean UnityEngine.PhysicsScene::Internal_Raycast_Injected(UnityEngine.PhysicsScene&,UnityEngine.Ray&,System.Single,UnityEngine.RaycastHit&,System.Int32,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_PhysicsScene_Internal_Raycast_Injected();
		Register_UnityEngine_PhysicsScene_Internal_Raycast_Injected();

		//System.Boolean UnityEngine.PhysicsScene::Query_SphereCast_Injected(UnityEngine.PhysicsScene&,UnityEngine.Vector3&,System.Single,UnityEngine.Vector3&,System.Single,UnityEngine.RaycastHit&,System.Int32,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_PhysicsScene_Query_SphereCast_Injected();
		Register_UnityEngine_PhysicsScene_Query_SphereCast_Injected();

		//System.Int32 UnityEngine.PhysicsScene::Internal_RaycastNonAlloc_Injected(UnityEngine.PhysicsScene&,UnityEngine.Ray&,UnityEngine.Bindings.ManagedSpanWrapper&,System.Single,System.Int32,UnityEngine.QueryTriggerInteraction)
		void Register_UnityEngine_PhysicsScene_Internal_RaycastNonAlloc_Injected();
		Register_UnityEngine_PhysicsScene_Internal_RaycastNonAlloc_Injected();

	//End Registrations for type : UnityEngine.PhysicsScene

	//Start Registrations for type : UnityEngine.PhysicsScene2D

		//System.Int32 UnityEngine.PhysicsScene2D::GetRayIntersectionArray_Internal_Injected(UnityEngine.PhysicsScene2D&,UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single,System.Int32,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_PhysicsScene2D_GetRayIntersectionArray_Internal_Injected();
		Register_UnityEngine_PhysicsScene2D_GetRayIntersectionArray_Internal_Injected();

		//System.Int32 UnityEngine.PhysicsScene2D::RaycastArray_Internal_Injected(UnityEngine.PhysicsScene2D&,UnityEngine.Vector2&,UnityEngine.Vector2&,System.Single,UnityEngine.ContactFilter2D&,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_PhysicsScene2D_RaycastArray_Internal_Injected();
		Register_UnityEngine_PhysicsScene2D_RaycastArray_Internal_Injected();

		//System.Int32 UnityEngine.PhysicsScene2D::RaycastList_Internal_Injected(UnityEngine.PhysicsScene2D&,UnityEngine.Vector2&,UnityEngine.Vector2&,System.Single,UnityEngine.ContactFilter2D&,UnityEngine.Bindings.BlittableListWrapper&)
		void Register_UnityEngine_PhysicsScene2D_RaycastList_Internal_Injected();
		Register_UnityEngine_PhysicsScene2D_RaycastList_Internal_Injected();

		//System.Void UnityEngine.PhysicsScene2D::GetRayIntersection_Internal_Injected(UnityEngine.PhysicsScene2D&,UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single,System.Int32,UnityEngine.RaycastHit2D&)
		void Register_UnityEngine_PhysicsScene2D_GetRayIntersection_Internal_Injected();
		Register_UnityEngine_PhysicsScene2D_GetRayIntersection_Internal_Injected();

		//System.Void UnityEngine.PhysicsScene2D::Raycast_Internal_Injected(UnityEngine.PhysicsScene2D&,UnityEngine.Vector2&,UnityEngine.Vector2&,System.Single,UnityEngine.ContactFilter2D&,UnityEngine.RaycastHit2D&)
		void Register_UnityEngine_PhysicsScene2D_Raycast_Internal_Injected();
		Register_UnityEngine_PhysicsScene2D_Raycast_Internal_Injected();

	//End Registrations for type : UnityEngine.PhysicsScene2D

	//Start Registrations for type : UnityEngine.Playables.PlayableHandle

		//System.Boolean UnityEngine.Playables.PlayableHandle::IsValid()
		void Register_UnityEngine_Playables_PlayableHandle_IsValid();
		Register_UnityEngine_Playables_PlayableHandle_IsValid();

		//System.Type UnityEngine.Playables.PlayableHandle::GetPlayableType()
		void Register_UnityEngine_Playables_PlayableHandle_GetPlayableType();
		Register_UnityEngine_Playables_PlayableHandle_GetPlayableType();

	//End Registrations for type : UnityEngine.Playables.PlayableHandle

	//Start Registrations for type : UnityEngine.PlayerConnectionInternal

		//System.Boolean UnityEngine.PlayerConnectionInternal::IsConnected()
		void Register_UnityEngine_PlayerConnectionInternal_IsConnected();
		Register_UnityEngine_PlayerConnectionInternal_IsConnected();

		//System.Boolean UnityEngine.PlayerConnectionInternal::TrySendMessage_Injected(UnityEngine.Bindings.ManagedSpanWrapper&,UnityEngine.Bindings.ManagedSpanWrapper&,System.Int32)
		void Register_UnityEngine_PlayerConnectionInternal_TrySendMessage_Injected();
		Register_UnityEngine_PlayerConnectionInternal_TrySendMessage_Injected();

		//System.Void UnityEngine.PlayerConnectionInternal::DisconnectAll()
		void Register_UnityEngine_PlayerConnectionInternal_DisconnectAll();
		Register_UnityEngine_PlayerConnectionInternal_DisconnectAll();

		//System.Void UnityEngine.PlayerConnectionInternal::Initialize()
		void Register_UnityEngine_PlayerConnectionInternal_Initialize();
		Register_UnityEngine_PlayerConnectionInternal_Initialize();

		//System.Void UnityEngine.PlayerConnectionInternal::PollInternal()
		void Register_UnityEngine_PlayerConnectionInternal_PollInternal();
		Register_UnityEngine_PlayerConnectionInternal_PollInternal();

		//System.Void UnityEngine.PlayerConnectionInternal::RegisterInternal_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_PlayerConnectionInternal_RegisterInternal_Injected();
		Register_UnityEngine_PlayerConnectionInternal_RegisterInternal_Injected();

		//System.Void UnityEngine.PlayerConnectionInternal::SendMessage_Injected(UnityEngine.Bindings.ManagedSpanWrapper&,UnityEngine.Bindings.ManagedSpanWrapper&,System.Int32)
		void Register_UnityEngine_PlayerConnectionInternal_SendMessage_Injected();
		Register_UnityEngine_PlayerConnectionInternal_SendMessage_Injected();

		//System.Void UnityEngine.PlayerConnectionInternal::UnregisterInternal_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_PlayerConnectionInternal_UnregisterInternal_Injected();
		Register_UnityEngine_PlayerConnectionInternal_UnregisterInternal_Injected();

	//End Registrations for type : UnityEngine.PlayerConnectionInternal

	//Start Registrations for type : UnityEngine.PropertyNameUtils

		//System.Void UnityEngine.PropertyNameUtils::PropertyNameFromString_Injected(UnityEngine.Bindings.ManagedSpanWrapper&,UnityEngine.PropertyName&)
		void Register_UnityEngine_PropertyNameUtils_PropertyNameFromString_Injected();
		Register_UnityEngine_PropertyNameUtils_PropertyNameFromString_Injected();

	//End Registrations for type : UnityEngine.PropertyNameUtils

	//Start Registrations for type : UnityEngine.QualitySettings

		//UnityEngine.ColorSpace UnityEngine.QualitySettings::get_activeColorSpace()
		void Register_UnityEngine_QualitySettings_get_activeColorSpace();
		Register_UnityEngine_QualitySettings_get_activeColorSpace();

	//End Registrations for type : UnityEngine.QualitySettings

	//Start Registrations for type : UnityEngine.Quaternion

		//System.Void UnityEngine.Quaternion::AngleAxis_Injected(System.Single,UnityEngine.Vector3&,UnityEngine.Quaternion&)
		void Register_UnityEngine_Quaternion_AngleAxis_Injected();
		Register_UnityEngine_Quaternion_AngleAxis_Injected();

		//System.Void UnityEngine.Quaternion::Internal_FromEulerRad_Injected(UnityEngine.Vector3&,UnityEngine.Quaternion&)
		void Register_UnityEngine_Quaternion_Internal_FromEulerRad_Injected();
		Register_UnityEngine_Quaternion_Internal_FromEulerRad_Injected();

		//System.Void UnityEngine.Quaternion::Internal_ToAxisAngleRad_Injected(UnityEngine.Quaternion&,UnityEngine.Vector3&,System.Single&)
		void Register_UnityEngine_Quaternion_Internal_ToAxisAngleRad_Injected();
		Register_UnityEngine_Quaternion_Internal_ToAxisAngleRad_Injected();

		//System.Void UnityEngine.Quaternion::Internal_ToEulerRad_Injected(UnityEngine.Quaternion&,UnityEngine.Vector3&)
		void Register_UnityEngine_Quaternion_Internal_ToEulerRad_Injected();
		Register_UnityEngine_Quaternion_Internal_ToEulerRad_Injected();

		//System.Void UnityEngine.Quaternion::Inverse_Injected(UnityEngine.Quaternion&,UnityEngine.Quaternion&)
		void Register_UnityEngine_Quaternion_Inverse_Injected();
		Register_UnityEngine_Quaternion_Inverse_Injected();

		//System.Void UnityEngine.Quaternion::LookRotation_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&,UnityEngine.Quaternion&)
		void Register_UnityEngine_Quaternion_LookRotation_Injected();
		Register_UnityEngine_Quaternion_LookRotation_Injected();

		//System.Void UnityEngine.Quaternion::Slerp_Injected(UnityEngine.Quaternion&,UnityEngine.Quaternion&,System.Single,UnityEngine.Quaternion&)
		void Register_UnityEngine_Quaternion_Slerp_Injected();
		Register_UnityEngine_Quaternion_Slerp_Injected();

	//End Registrations for type : UnityEngine.Quaternion

	//Start Registrations for type : UnityEngine.RectOffset

		//System.Int32 UnityEngine.RectOffset::get_bottom_Injected(System.IntPtr)
		void Register_UnityEngine_RectOffset_get_bottom_Injected();
		Register_UnityEngine_RectOffset_get_bottom_Injected();

		//System.Int32 UnityEngine.RectOffset::get_horizontal_Injected(System.IntPtr)
		void Register_UnityEngine_RectOffset_get_horizontal_Injected();
		Register_UnityEngine_RectOffset_get_horizontal_Injected();

		//System.Int32 UnityEngine.RectOffset::get_left_Injected(System.IntPtr)
		void Register_UnityEngine_RectOffset_get_left_Injected();
		Register_UnityEngine_RectOffset_get_left_Injected();

		//System.Int32 UnityEngine.RectOffset::get_right_Injected(System.IntPtr)
		void Register_UnityEngine_RectOffset_get_right_Injected();
		Register_UnityEngine_RectOffset_get_right_Injected();

		//System.Int32 UnityEngine.RectOffset::get_top_Injected(System.IntPtr)
		void Register_UnityEngine_RectOffset_get_top_Injected();
		Register_UnityEngine_RectOffset_get_top_Injected();

		//System.Int32 UnityEngine.RectOffset::get_vertical_Injected(System.IntPtr)
		void Register_UnityEngine_RectOffset_get_vertical_Injected();
		Register_UnityEngine_RectOffset_get_vertical_Injected();

		//System.IntPtr UnityEngine.RectOffset::InternalCreate()
		void Register_UnityEngine_RectOffset_InternalCreate();
		Register_UnityEngine_RectOffset_InternalCreate();

		//System.Void UnityEngine.RectOffset::InternalDestroy(System.IntPtr)
		void Register_UnityEngine_RectOffset_InternalDestroy();
		Register_UnityEngine_RectOffset_InternalDestroy();

		//System.Void UnityEngine.RectOffset::Remove_Injected(System.IntPtr,UnityEngine.Rect&,UnityEngine.Rect&)
		void Register_UnityEngine_RectOffset_Remove_Injected();
		Register_UnityEngine_RectOffset_Remove_Injected();

	//End Registrations for type : UnityEngine.RectOffset

	//Start Registrations for type : UnityEngine.RectTransform

		//System.Void UnityEngine.RectTransform::get_anchorMax_Injected(System.IntPtr,UnityEngine.Vector2&)
		void Register_UnityEngine_RectTransform_get_anchorMax_Injected();
		Register_UnityEngine_RectTransform_get_anchorMax_Injected();

		//System.Void UnityEngine.RectTransform::get_anchorMin_Injected(System.IntPtr,UnityEngine.Vector2&)
		void Register_UnityEngine_RectTransform_get_anchorMin_Injected();
		Register_UnityEngine_RectTransform_get_anchorMin_Injected();

		//System.Void UnityEngine.RectTransform::get_anchoredPosition_Injected(System.IntPtr,UnityEngine.Vector2&)
		void Register_UnityEngine_RectTransform_get_anchoredPosition_Injected();
		Register_UnityEngine_RectTransform_get_anchoredPosition_Injected();

		//System.Void UnityEngine.RectTransform::get_pivot_Injected(System.IntPtr,UnityEngine.Vector2&)
		void Register_UnityEngine_RectTransform_get_pivot_Injected();
		Register_UnityEngine_RectTransform_get_pivot_Injected();

		//System.Void UnityEngine.RectTransform::get_rect_Injected(System.IntPtr,UnityEngine.Rect&)
		void Register_UnityEngine_RectTransform_get_rect_Injected();
		Register_UnityEngine_RectTransform_get_rect_Injected();

		//System.Void UnityEngine.RectTransform::get_sizeDelta_Injected(System.IntPtr,UnityEngine.Vector2&)
		void Register_UnityEngine_RectTransform_get_sizeDelta_Injected();
		Register_UnityEngine_RectTransform_get_sizeDelta_Injected();

		//System.Void UnityEngine.RectTransform::set_anchorMax_Injected(System.IntPtr,UnityEngine.Vector2&)
		void Register_UnityEngine_RectTransform_set_anchorMax_Injected();
		Register_UnityEngine_RectTransform_set_anchorMax_Injected();

		//System.Void UnityEngine.RectTransform::set_anchorMin_Injected(System.IntPtr,UnityEngine.Vector2&)
		void Register_UnityEngine_RectTransform_set_anchorMin_Injected();
		Register_UnityEngine_RectTransform_set_anchorMin_Injected();

		//System.Void UnityEngine.RectTransform::set_anchoredPosition_Injected(System.IntPtr,UnityEngine.Vector2&)
		void Register_UnityEngine_RectTransform_set_anchoredPosition_Injected();
		Register_UnityEngine_RectTransform_set_anchoredPosition_Injected();

		//System.Void UnityEngine.RectTransform::set_pivot_Injected(System.IntPtr,UnityEngine.Vector2&)
		void Register_UnityEngine_RectTransform_set_pivot_Injected();
		Register_UnityEngine_RectTransform_set_pivot_Injected();

		//System.Void UnityEngine.RectTransform::set_sizeDelta_Injected(System.IntPtr,UnityEngine.Vector2&)
		void Register_UnityEngine_RectTransform_set_sizeDelta_Injected();
		Register_UnityEngine_RectTransform_set_sizeDelta_Injected();

	//End Registrations for type : UnityEngine.RectTransform

	//Start Registrations for type : UnityEngine.RectTransformUtility

		//System.Boolean UnityEngine.RectTransformUtility::PointInRectangle_Injected(UnityEngine.Vector2&,System.IntPtr,System.IntPtr,UnityEngine.Vector4&)
		void Register_UnityEngine_RectTransformUtility_PointInRectangle_Injected();
		Register_UnityEngine_RectTransformUtility_PointInRectangle_Injected();

		//System.Void UnityEngine.RectTransformUtility::PixelAdjustPoint_Injected(UnityEngine.Vector2&,System.IntPtr,System.IntPtr,UnityEngine.Vector2&)
		void Register_UnityEngine_RectTransformUtility_PixelAdjustPoint_Injected();
		Register_UnityEngine_RectTransformUtility_PixelAdjustPoint_Injected();

		//System.Void UnityEngine.RectTransformUtility::PixelAdjustRect_Injected(System.IntPtr,System.IntPtr,UnityEngine.Rect&)
		void Register_UnityEngine_RectTransformUtility_PixelAdjustRect_Injected();
		Register_UnityEngine_RectTransformUtility_PixelAdjustRect_Injected();

	//End Registrations for type : UnityEngine.RectTransformUtility

	//Start Registrations for type : UnityEngine.Renderer

		//System.Int32 UnityEngine.Renderer::get_sortingGroupID_Injected(System.IntPtr)
		void Register_UnityEngine_Renderer_get_sortingGroupID_Injected();
		Register_UnityEngine_Renderer_get_sortingGroupID_Injected();

		//System.Int32 UnityEngine.Renderer::get_sortingGroupOrder_Injected(System.IntPtr)
		void Register_UnityEngine_Renderer_get_sortingGroupOrder_Injected();
		Register_UnityEngine_Renderer_get_sortingGroupOrder_Injected();

		//System.Int32 UnityEngine.Renderer::get_sortingLayerID_Injected(System.IntPtr)
		void Register_UnityEngine_Renderer_get_sortingLayerID_Injected();
		Register_UnityEngine_Renderer_get_sortingLayerID_Injected();

		//System.Int32 UnityEngine.Renderer::get_sortingOrder_Injected(System.IntPtr)
		void Register_UnityEngine_Renderer_get_sortingOrder_Injected();
		Register_UnityEngine_Renderer_get_sortingOrder_Injected();

		//System.IntPtr UnityEngine.Renderer::GetMaterial_Injected(System.IntPtr)
		void Register_UnityEngine_Renderer_GetMaterial_Injected();
		Register_UnityEngine_Renderer_GetMaterial_Injected();

		//System.Void UnityEngine.Renderer::set_enabled_Injected(System.IntPtr,System.Boolean)
		void Register_UnityEngine_Renderer_set_enabled_Injected();
		Register_UnityEngine_Renderer_set_enabled_Injected();

		//System.Void UnityEngine.Renderer::set_localBounds_Injected(System.IntPtr,UnityEngine.Bounds&)
		void Register_UnityEngine_Renderer_set_localBounds_Injected();
		Register_UnityEngine_Renderer_set_localBounds_Injected();

	//End Registrations for type : UnityEngine.Renderer

	//Start Registrations for type : UnityEngine.Rendering.GraphicsSettings

		//System.Boolean UnityEngine.Rendering.GraphicsSettings::get_lightsUseLinearIntensity()
		void Register_UnityEngine_Rendering_GraphicsSettings_get_lightsUseLinearIntensity();
		Register_UnityEngine_Rendering_GraphicsSettings_get_lightsUseLinearIntensity();

		//System.IntPtr UnityEngine.Rendering.GraphicsSettings::Internal_GetSettingsForRenderPipeline_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_Rendering_GraphicsSettings_Internal_GetSettingsForRenderPipeline_Injected();
		Register_UnityEngine_Rendering_GraphicsSettings_Internal_GetSettingsForRenderPipeline_Injected();

		//System.IntPtr UnityEngine.Rendering.GraphicsSettings::get_INTERNAL_currentRenderPipeline_Injected()
		void Register_UnityEngine_Rendering_GraphicsSettings_get_INTERNAL_currentRenderPipeline_Injected();
		Register_UnityEngine_Rendering_GraphicsSettings_get_INTERNAL_currentRenderPipeline_Injected();

	//End Registrations for type : UnityEngine.Rendering.GraphicsSettings

	//Start Registrations for type : UnityEngine.Rendering.ScriptableRenderContext

		//System.Void UnityEngine.Rendering.ScriptableRenderContext::GetCameras_Internal(System.Type,System.Object)
		void Register_UnityEngine_Rendering_ScriptableRenderContext_GetCameras_Internal();
		Register_UnityEngine_Rendering_ScriptableRenderContext_GetCameras_Internal();

	//End Registrations for type : UnityEngine.Rendering.ScriptableRenderContext

	//Start Registrations for type : UnityEngine.Rendering.SortingGroup

		//System.Int32 UnityEngine.Rendering.SortingGroup::get_invalidSortingGroupID()
		void Register_UnityEngine_Rendering_SortingGroup_get_invalidSortingGroupID();
		Register_UnityEngine_Rendering_SortingGroup_get_invalidSortingGroupID();

		//System.Int32 UnityEngine.Rendering.SortingGroup::get_sortingLayerID_Injected(System.IntPtr)
		void Register_UnityEngine_Rendering_SortingGroup_get_sortingLayerID_Injected();
		Register_UnityEngine_Rendering_SortingGroup_get_sortingLayerID_Injected();

		//System.Int32 UnityEngine.Rendering.SortingGroup::get_sortingOrder_Injected(System.IntPtr)
		void Register_UnityEngine_Rendering_SortingGroup_get_sortingOrder_Injected();
		Register_UnityEngine_Rendering_SortingGroup_get_sortingOrder_Injected();

		//System.IntPtr UnityEngine.Rendering.SortingGroup::GetSortingGroupByIndex_Injected(System.Int32)
		void Register_UnityEngine_Rendering_SortingGroup_GetSortingGroupByIndex_Injected();
		Register_UnityEngine_Rendering_SortingGroup_GetSortingGroupByIndex_Injected();

	//End Registrations for type : UnityEngine.Rendering.SortingGroup

	//Start Registrations for type : UnityEngine.RenderTexture

		//System.Int32 UnityEngine.RenderTexture::get_height_Injected(System.IntPtr)
		void Register_UnityEngine_RenderTexture_get_height_Injected();
		Register_UnityEngine_RenderTexture_get_height_Injected();

		//System.Int32 UnityEngine.RenderTexture::get_width_Injected(System.IntPtr)
		void Register_UnityEngine_RenderTexture_get_width_Injected();
		Register_UnityEngine_RenderTexture_get_width_Injected();

		//System.IntPtr UnityEngine.RenderTexture::GetActive_Injected()
		void Register_UnityEngine_RenderTexture_GetActive_Injected();
		Register_UnityEngine_RenderTexture_GetActive_Injected();

		//System.IntPtr UnityEngine.RenderTexture::GetTemporary_Internal_Injected(UnityEngine.RenderTextureDescriptor&)
		void Register_UnityEngine_RenderTexture_GetTemporary_Internal_Injected();
		Register_UnityEngine_RenderTexture_GetTemporary_Internal_Injected();

		//System.Void UnityEngine.RenderTexture::GetColorBuffer_Injected(System.IntPtr,UnityEngine.RenderBuffer&)
		void Register_UnityEngine_RenderTexture_GetColorBuffer_Injected();
		Register_UnityEngine_RenderTexture_GetColorBuffer_Injected();

		//System.Void UnityEngine.RenderTexture::GetDepthBuffer_Injected(System.IntPtr,UnityEngine.RenderBuffer&)
		void Register_UnityEngine_RenderTexture_GetDepthBuffer_Injected();
		Register_UnityEngine_RenderTexture_GetDepthBuffer_Injected();

		//System.Void UnityEngine.RenderTexture::GetDescriptor_Injected(System.IntPtr,UnityEngine.RenderTextureDescriptor&)
		void Register_UnityEngine_RenderTexture_GetDescriptor_Injected();
		Register_UnityEngine_RenderTexture_GetDescriptor_Injected();

		//System.Void UnityEngine.RenderTexture::Internal_Create(UnityEngine.RenderTexture)
		void Register_UnityEngine_RenderTexture_Internal_Create();
		Register_UnityEngine_RenderTexture_Internal_Create();

		//System.Void UnityEngine.RenderTexture::ReleaseTemporary_Injected(System.IntPtr)
		void Register_UnityEngine_RenderTexture_ReleaseTemporary_Injected();
		Register_UnityEngine_RenderTexture_ReleaseTemporary_Injected();

		//System.Void UnityEngine.RenderTexture::SetActive_Injected(System.IntPtr)
		void Register_UnityEngine_RenderTexture_SetActive_Injected();
		Register_UnityEngine_RenderTexture_SetActive_Injected();

		//System.Void UnityEngine.RenderTexture::SetColorFormat_Injected(System.IntPtr,UnityEngine.Experimental.Rendering.GraphicsFormat)
		void Register_UnityEngine_RenderTexture_SetColorFormat_Injected();
		Register_UnityEngine_RenderTexture_SetColorFormat_Injected();

		//System.Void UnityEngine.RenderTexture::SetMipMapCount_Injected(System.IntPtr,System.Int32)
		void Register_UnityEngine_RenderTexture_SetMipMapCount_Injected();
		Register_UnityEngine_RenderTexture_SetMipMapCount_Injected();

		//System.Void UnityEngine.RenderTexture::SetRenderTextureDescriptor_Injected(System.IntPtr,UnityEngine.RenderTextureDescriptor&)
		void Register_UnityEngine_RenderTexture_SetRenderTextureDescriptor_Injected();
		Register_UnityEngine_RenderTexture_SetRenderTextureDescriptor_Injected();

		//System.Void UnityEngine.RenderTexture::SetSRGBReadWrite_Injected(System.IntPtr,System.Boolean)
		void Register_UnityEngine_RenderTexture_SetSRGBReadWrite_Injected();
		Register_UnityEngine_RenderTexture_SetSRGBReadWrite_Injected();

		//System.Void UnityEngine.RenderTexture::SetShadowSamplingMode_Injected(System.IntPtr,UnityEngine.Rendering.ShadowSamplingMode)
		void Register_UnityEngine_RenderTexture_SetShadowSamplingMode_Injected();
		Register_UnityEngine_RenderTexture_SetShadowSamplingMode_Injected();

		//System.Void UnityEngine.RenderTexture::set_depthStencilFormat_Injected(System.IntPtr,UnityEngine.Experimental.Rendering.GraphicsFormat)
		void Register_UnityEngine_RenderTexture_set_depthStencilFormat_Injected();
		Register_UnityEngine_RenderTexture_set_depthStencilFormat_Injected();

		//System.Void UnityEngine.RenderTexture::set_height_Injected(System.IntPtr,System.Int32)
		void Register_UnityEngine_RenderTexture_set_height_Injected();
		Register_UnityEngine_RenderTexture_set_height_Injected();

		//System.Void UnityEngine.RenderTexture::set_width_Injected(System.IntPtr,System.Int32)
		void Register_UnityEngine_RenderTexture_set_width_Injected();
		Register_UnityEngine_RenderTexture_set_width_Injected();

	//End Registrations for type : UnityEngine.RenderTexture

	//Start Registrations for type : UnityEngine.Resources

		//System.IntPtr UnityEngine.Resources::GetBuiltinResource_Injected(System.Type,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_Resources_GetBuiltinResource_Injected();
		Register_UnityEngine_Resources_GetBuiltinResource_Injected();

	//End Registrations for type : UnityEngine.Resources

	//Start Registrations for type : UnityEngine.ResourcesAPIInternal

		//System.IntPtr UnityEngine.ResourcesAPIInternal::FindShaderByName_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_ResourcesAPIInternal_FindShaderByName_Injected();
		Register_UnityEngine_ResourcesAPIInternal_FindShaderByName_Injected();

		//System.IntPtr UnityEngine.ResourcesAPIInternal::Load_Injected(UnityEngine.Bindings.ManagedSpanWrapper&,System.Type)
		void Register_UnityEngine_ResourcesAPIInternal_Load_Injected();
		Register_UnityEngine_ResourcesAPIInternal_Load_Injected();

		//UnityEngine.Object[] UnityEngine.ResourcesAPIInternal::FindObjectsOfTypeAll(System.Type)
		void Register_UnityEngine_ResourcesAPIInternal_FindObjectsOfTypeAll();
		Register_UnityEngine_ResourcesAPIInternal_FindObjectsOfTypeAll();

	//End Registrations for type : UnityEngine.ResourcesAPIInternal

	//Start Registrations for type : UnityEngine.Rigidbody

		//System.Void UnityEngine.Rigidbody::AddForce_Injected(System.IntPtr,UnityEngine.Vector3&,UnityEngine.ForceMode)
		void Register_UnityEngine_Rigidbody_AddForce_Injected();
		Register_UnityEngine_Rigidbody_AddForce_Injected();

		//System.Void UnityEngine.Rigidbody::get_linearVelocity_Injected(System.IntPtr,UnityEngine.Vector3&)
		void Register_UnityEngine_Rigidbody_get_linearVelocity_Injected();
		Register_UnityEngine_Rigidbody_get_linearVelocity_Injected();

		//System.Void UnityEngine.Rigidbody::get_position_Injected(System.IntPtr,UnityEngine.Vector3&)
		void Register_UnityEngine_Rigidbody_get_position_Injected();
		Register_UnityEngine_Rigidbody_get_position_Injected();

		//System.Void UnityEngine.Rigidbody::set_linearVelocity_Injected(System.IntPtr,UnityEngine.Vector3&)
		void Register_UnityEngine_Rigidbody_set_linearVelocity_Injected();
		Register_UnityEngine_Rigidbody_set_linearVelocity_Injected();

	//End Registrations for type : UnityEngine.Rigidbody

	//Start Registrations for type : UnityEngine.Screen

		//System.Boolean UnityEngine.Screen::get_fullScreen()
		void Register_UnityEngine_Screen_get_fullScreen();
		Register_UnityEngine_Screen_get_fullScreen();

		//System.Int32 UnityEngine.Screen::get_height()
		void Register_UnityEngine_Screen_get_height();
		Register_UnityEngine_Screen_get_height();

		//System.Int32 UnityEngine.Screen::get_width()
		void Register_UnityEngine_Screen_get_width();
		Register_UnityEngine_Screen_get_width();

		//System.Single UnityEngine.Screen::get_dpi()
		void Register_UnityEngine_Screen_get_dpi();
		Register_UnityEngine_Screen_get_dpi();

		//UnityEngine.ScreenOrientation UnityEngine.Screen::GetScreenOrientation()
		void Register_UnityEngine_Screen_GetScreenOrientation();
		Register_UnityEngine_Screen_GetScreenOrientation();

	//End Registrations for type : UnityEngine.Screen

	//Start Registrations for type : UnityEngine.ScriptableObject

		//System.IntPtr UnityEngine.ScriptableObject::CreateScriptableObjectInstanceFromType_Injected(System.Type,System.Boolean)
		void Register_UnityEngine_ScriptableObject_CreateScriptableObjectInstanceFromType_Injected();
		Register_UnityEngine_ScriptableObject_CreateScriptableObjectInstanceFromType_Injected();

		//System.Void UnityEngine.ScriptableObject::CreateScriptableObject(UnityEngine.ScriptableObject)
		void Register_UnityEngine_ScriptableObject_CreateScriptableObject();
		Register_UnityEngine_ScriptableObject_CreateScriptableObject();

	//End Registrations for type : UnityEngine.ScriptableObject

	//Start Registrations for type : UnityEngine.ScriptingRuntime

		//System.String[] UnityEngine.ScriptingRuntime::GetAllUserAssemblies()
		void Register_UnityEngine_ScriptingRuntime_GetAllUserAssemblies();
		Register_UnityEngine_ScriptingRuntime_GetAllUserAssemblies();

	//End Registrations for type : UnityEngine.ScriptingRuntime

	//Start Registrations for type : UnityEngine.Shader

		//System.Int32 UnityEngine.Shader::PropertyToID_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_Shader_PropertyToID_Injected();
		Register_UnityEngine_Shader_PropertyToID_Injected();

		//System.Int32 UnityEngine.Shader::TagToID_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_Shader_TagToID_Injected();
		Register_UnityEngine_Shader_TagToID_Injected();

		//System.Void UnityEngine.Shader::set_globalRenderPipeline_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_Shader_set_globalRenderPipeline_Injected();
		Register_UnityEngine_Shader_set_globalRenderPipeline_Injected();

	//End Registrations for type : UnityEngine.Shader

	//Start Registrations for type : UnityEngine.SortingLayer

		//System.Int32 UnityEngine.SortingLayer::GetLayerValueFromID(System.Int32)
		void Register_UnityEngine_SortingLayer_GetLayerValueFromID();
		Register_UnityEngine_SortingLayer_GetLayerValueFromID();

	//End Registrations for type : UnityEngine.SortingLayer

	//Start Registrations for type : UnityEngine.Sprite

		//System.Int32 UnityEngine.Sprite::GetPacked_Injected(System.IntPtr)
		void Register_UnityEngine_Sprite_GetPacked_Injected();
		Register_UnityEngine_Sprite_GetPacked_Injected();

		//System.Int32 UnityEngine.Sprite::GetPackingRotation_Injected(System.IntPtr)
		void Register_UnityEngine_Sprite_GetPackingRotation_Injected();
		Register_UnityEngine_Sprite_GetPackingRotation_Injected();

		//System.IntPtr UnityEngine.Sprite::CreateSprite_Injected(System.IntPtr,UnityEngine.Rect&,UnityEngine.Vector2&,System.Single,System.UInt32,UnityEngine.SpriteMeshType,UnityEngine.Vector4&,System.Boolean,UnityEngine.SecondarySpriteTexture[])
		void Register_UnityEngine_Sprite_CreateSprite_Injected();
		Register_UnityEngine_Sprite_CreateSprite_Injected();

		//System.IntPtr UnityEngine.Sprite::get_associatedAlphaSplitTexture_Injected(System.IntPtr)
		void Register_UnityEngine_Sprite_get_associatedAlphaSplitTexture_Injected();
		Register_UnityEngine_Sprite_get_associatedAlphaSplitTexture_Injected();

		//System.IntPtr UnityEngine.Sprite::get_texture_Injected(System.IntPtr)
		void Register_UnityEngine_Sprite_get_texture_Injected();
		Register_UnityEngine_Sprite_get_texture_Injected();

		//System.Single UnityEngine.Sprite::get_pixelsPerUnit_Injected(System.IntPtr)
		void Register_UnityEngine_Sprite_get_pixelsPerUnit_Injected();
		Register_UnityEngine_Sprite_get_pixelsPerUnit_Injected();

		//System.UInt16[] UnityEngine.Sprite::get_triangles_Injected(System.IntPtr)
		void Register_UnityEngine_Sprite_get_triangles_Injected();
		Register_UnityEngine_Sprite_get_triangles_Injected();

		//System.Void UnityEngine.Sprite::GetInnerUVs_Injected(System.IntPtr,UnityEngine.Vector4&)
		void Register_UnityEngine_Sprite_GetInnerUVs_Injected();
		Register_UnityEngine_Sprite_GetInnerUVs_Injected();

		//System.Void UnityEngine.Sprite::GetOuterUVs_Injected(System.IntPtr,UnityEngine.Vector4&)
		void Register_UnityEngine_Sprite_GetOuterUVs_Injected();
		Register_UnityEngine_Sprite_GetOuterUVs_Injected();

		//System.Void UnityEngine.Sprite::GetPadding_Injected(System.IntPtr,UnityEngine.Vector4&)
		void Register_UnityEngine_Sprite_GetPadding_Injected();
		Register_UnityEngine_Sprite_GetPadding_Injected();

		//System.Void UnityEngine.Sprite::get_border_Injected(System.IntPtr,UnityEngine.Vector4&)
		void Register_UnityEngine_Sprite_get_border_Injected();
		Register_UnityEngine_Sprite_get_border_Injected();

		//System.Void UnityEngine.Sprite::get_bounds_Injected(System.IntPtr,UnityEngine.Bounds&)
		void Register_UnityEngine_Sprite_get_bounds_Injected();
		Register_UnityEngine_Sprite_get_bounds_Injected();

		//System.Void UnityEngine.Sprite::get_pivot_Injected(System.IntPtr,UnityEngine.Vector2&)
		void Register_UnityEngine_Sprite_get_pivot_Injected();
		Register_UnityEngine_Sprite_get_pivot_Injected();

		//System.Void UnityEngine.Sprite::get_rect_Injected(System.IntPtr,UnityEngine.Rect&)
		void Register_UnityEngine_Sprite_get_rect_Injected();
		Register_UnityEngine_Sprite_get_rect_Injected();

		//UnityEngine.Vector2[] UnityEngine.Sprite::get_uv_Injected(System.IntPtr)
		void Register_UnityEngine_Sprite_get_uv_Injected();
		Register_UnityEngine_Sprite_get_uv_Injected();

		//UnityEngine.Vector2[] UnityEngine.Sprite::get_vertices_Injected(System.IntPtr)
		void Register_UnityEngine_Sprite_get_vertices_Injected();
		Register_UnityEngine_Sprite_get_vertices_Injected();

	//End Registrations for type : UnityEngine.Sprite

	//Start Registrations for type : UnityEngine.SubsystemDescriptorBindings

		//System.Void UnityEngine.SubsystemDescriptorBindings::GetId_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_SubsystemDescriptorBindings_GetId_Injected();
		Register_UnityEngine_SubsystemDescriptorBindings_GetId_Injected();

	//End Registrations for type : UnityEngine.SubsystemDescriptorBindings

	//Start Registrations for type : UnityEngine.SubsystemManager

		//System.Void UnityEngine.SubsystemManager::StaticConstructScriptingClassMap()
		void Register_UnityEngine_SubsystemManager_StaticConstructScriptingClassMap();
		Register_UnityEngine_SubsystemManager_StaticConstructScriptingClassMap();

	//End Registrations for type : UnityEngine.SubsystemManager

	//Start Registrations for type : UnityEngine.SubsystemsImplementation.SubsystemDescriptorStore

		//System.Void UnityEngine.SubsystemsImplementation.SubsystemDescriptorStore::ReportSingleSubsystemAnalytics_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_SubsystemsImplementation_SubsystemDescriptorStore_ReportSingleSubsystemAnalytics_Injected();
		Register_UnityEngine_SubsystemsImplementation_SubsystemDescriptorStore_ReportSingleSubsystemAnalytics_Injected();

	//End Registrations for type : UnityEngine.SubsystemsImplementation.SubsystemDescriptorStore

	//Start Registrations for type : UnityEngine.SystemInfo

		//System.Boolean UnityEngine.SystemInfo::IsFormatSupported(UnityEngine.Experimental.Rendering.GraphicsFormat,UnityEngine.Experimental.Rendering.GraphicsFormatUsage)
		void Register_UnityEngine_SystemInfo_IsFormatSupported();
		Register_UnityEngine_SystemInfo_IsFormatSupported();

		//System.Boolean UnityEngine.SystemInfo::SupportsTextureFormatNative(UnityEngine.TextureFormat)
		void Register_UnityEngine_SystemInfo_SupportsTextureFormatNative();
		Register_UnityEngine_SystemInfo_SupportsTextureFormatNative();

		//System.Int32 UnityEngine.SystemInfo::GetMaxRenderTextureSize()
		void Register_UnityEngine_SystemInfo_GetMaxRenderTextureSize();
		Register_UnityEngine_SystemInfo_GetMaxRenderTextureSize();

		//System.Int32 UnityEngine.SystemInfo::GetMaxTextureSize()
		void Register_UnityEngine_SystemInfo_GetMaxTextureSize();
		Register_UnityEngine_SystemInfo_GetMaxTextureSize();

		//System.Void UnityEngine.SystemInfo::GetDeviceModel_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_SystemInfo_GetDeviceModel_Injected();
		Register_UnityEngine_SystemInfo_GetDeviceModel_Injected();

		//UnityEngine.Experimental.Rendering.GraphicsFormat UnityEngine.SystemInfo::GetCompatibleFormat(UnityEngine.Experimental.Rendering.GraphicsFormat,UnityEngine.Experimental.Rendering.GraphicsFormatUsage)
		void Register_UnityEngine_SystemInfo_GetCompatibleFormat();
		Register_UnityEngine_SystemInfo_GetCompatibleFormat();

		//UnityEngine.Experimental.Rendering.GraphicsFormat UnityEngine.SystemInfo::GetGraphicsFormat(UnityEngine.Experimental.Rendering.DefaultFormat)
		void Register_UnityEngine_SystemInfo_GetGraphicsFormat();
		Register_UnityEngine_SystemInfo_GetGraphicsFormat();

		//UnityEngine.OperatingSystemFamily UnityEngine.SystemInfo::GetOperatingSystemFamily()
		void Register_UnityEngine_SystemInfo_GetOperatingSystemFamily();
		Register_UnityEngine_SystemInfo_GetOperatingSystemFamily();

	//End Registrations for type : UnityEngine.SystemInfo

	//Start Registrations for type : UnityEngine.Terrain

		//System.Boolean UnityEngine.Terrain::get_allowAutoConnect_Injected(System.IntPtr)
		void Register_UnityEngine_Terrain_get_allowAutoConnect_Injected();
		Register_UnityEngine_Terrain_get_allowAutoConnect_Injected();

		//System.Int32 UnityEngine.Terrain::get_groupingID_Injected(System.IntPtr)
		void Register_UnityEngine_Terrain_get_groupingID_Injected();
		Register_UnityEngine_Terrain_get_groupingID_Injected();

		//System.IntPtr UnityEngine.Terrain::get_terrainData_Injected(System.IntPtr)
		void Register_UnityEngine_Terrain_get_terrainData_Injected();
		Register_UnityEngine_Terrain_get_terrainData_Injected();

		//System.Void UnityEngine.Terrain::SetNeighbors_Injected(System.IntPtr,System.IntPtr,System.IntPtr,System.IntPtr,System.IntPtr)
		void Register_UnityEngine_Terrain_SetNeighbors_Injected();
		Register_UnityEngine_Terrain_SetNeighbors_Injected();

		//UnityEngine.Terrain[] UnityEngine.Terrain::get_activeTerrains()
		void Register_UnityEngine_Terrain_get_activeTerrains();
		Register_UnityEngine_Terrain_get_activeTerrains();

	//End Registrations for type : UnityEngine.Terrain

	//Start Registrations for type : UnityEngine.TerrainData

		//System.Int32 UnityEngine.TerrainData::GetBoundaryValue(UnityEngine.TerrainData/BoundaryValueType)
		void Register_UnityEngine_TerrainData_GetBoundaryValue();
		Register_UnityEngine_TerrainData_GetBoundaryValue();

		//System.Single UnityEngine.TerrainData::GetAlphamapResolutionInternal_Injected(System.IntPtr)
		void Register_UnityEngine_TerrainData_GetAlphamapResolutionInternal_Injected();
		Register_UnityEngine_TerrainData_GetAlphamapResolutionInternal_Injected();

		//System.Void UnityEngine.TerrainData::get_size_Injected(System.IntPtr,UnityEngine.Vector3&)
		void Register_UnityEngine_TerrainData_get_size_Injected();
		Register_UnityEngine_TerrainData_get_size_Injected();

		//UnityEngine.Terrain[] UnityEngine.TerrainData::get_users_Injected(System.IntPtr)
		void Register_UnityEngine_TerrainData_get_users_Injected();
		Register_UnityEngine_TerrainData_get_users_Injected();

	//End Registrations for type : UnityEngine.TerrainData

	//Start Registrations for type : UnityEngine.TextAsset

		//System.Byte[] UnityEngine.TextAsset::get_bytes_Injected(System.IntPtr)
		void Register_UnityEngine_TextAsset_get_bytes_Injected();
		Register_UnityEngine_TextAsset_get_bytes_Injected();

	//End Registrations for type : UnityEngine.TextAsset

	//Start Registrations for type : UnityEngine.TextCore.LowLevel.FontEngine

		//System.Boolean UnityEngine.TextCore.LowLevel.FontEngine::TryAddGlyphToTexture_Internal_Injected(System.UInt32,System.Int32,UnityEngine.TextCore.LowLevel.GlyphPackingMode,UnityEngine.Bindings.BlittableArrayWrapper&,System.Int32&,UnityEngine.Bindings.BlittableArrayWrapper&,System.Int32&,UnityEngine.TextCore.LowLevel.GlyphRenderMode,System.IntPtr,UnityEngine.TextCore.LowLevel.GlyphMarshallingStruct&)
		void Register_UnityEngine_TextCore_LowLevel_FontEngine_TryAddGlyphToTexture_Internal_Injected();
		Register_UnityEngine_TextCore_LowLevel_FontEngine_TryAddGlyphToTexture_Internal_Injected();

		//System.Boolean UnityEngine.TextCore.LowLevel.FontEngine::TryAddGlyphsToTexture_Internal_Injected(UnityEngine.Bindings.ManagedSpanWrapper&,System.Int32,UnityEngine.TextCore.LowLevel.GlyphPackingMode,UnityEngine.Bindings.BlittableArrayWrapper&,System.Int32&,UnityEngine.Bindings.BlittableArrayWrapper&,System.Int32&,UnityEngine.TextCore.LowLevel.GlyphRenderMode,System.IntPtr,UnityEngine.Bindings.BlittableArrayWrapper&,System.Int32&)
		void Register_UnityEngine_TextCore_LowLevel_FontEngine_TryAddGlyphsToTexture_Internal_Injected();
		Register_UnityEngine_TextCore_LowLevel_FontEngine_TryAddGlyphsToTexture_Internal_Injected();

		//System.Boolean UnityEngine.TextCore.LowLevel.FontEngine::TryGetGlyphWithUnicodeValue_Internal(System.UInt32,UnityEngine.TextCore.LowLevel.GlyphLoadFlags,UnityEngine.TextCore.LowLevel.GlyphMarshallingStruct&)
		void Register_UnityEngine_TextCore_LowLevel_FontEngine_TryGetGlyphWithUnicodeValue_Internal();
		Register_UnityEngine_TextCore_LowLevel_FontEngine_TryGetGlyphWithUnicodeValue_Internal();

		//System.Boolean UnityEngine.TextCore.LowLevel.FontEngine::TryGetSystemFontReference_Internal_Injected(UnityEngine.Bindings.ManagedSpanWrapper&,UnityEngine.Bindings.ManagedSpanWrapper&,UnityEngine.TextCore.LowLevel.FontReference&)
		void Register_UnityEngine_TextCore_LowLevel_FontEngine_TryGetSystemFontReference_Internal_Injected();
		Register_UnityEngine_TextCore_LowLevel_FontEngine_TryGetSystemFontReference_Internal_Injected();

		//System.Int32 UnityEngine.TextCore.LowLevel.FontEngine::GetFaceInfo_Internal(UnityEngine.TextCore.FaceInfo&)
		void Register_UnityEngine_TextCore_LowLevel_FontEngine_GetFaceInfo_Internal();
		Register_UnityEngine_TextCore_LowLevel_FontEngine_GetFaceInfo_Internal();

		//System.Int32 UnityEngine.TextCore.LowLevel.FontEngine::GetLigatureSubstitutionRecordsFromMarshallingArray(UnityEngine.TextCore.LowLevel.LigatureSubstitutionRecord[])
		void Register_UnityEngine_TextCore_LowLevel_FontEngine_GetLigatureSubstitutionRecordsFromMarshallingArray();
		Register_UnityEngine_TextCore_LowLevel_FontEngine_GetLigatureSubstitutionRecordsFromMarshallingArray();

		//System.Int32 UnityEngine.TextCore.LowLevel.FontEngine::GetMarkToBaseAdjustmentRecordsFromMarshallingArray_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_TextCore_LowLevel_FontEngine_GetMarkToBaseAdjustmentRecordsFromMarshallingArray_Injected();
		Register_UnityEngine_TextCore_LowLevel_FontEngine_GetMarkToBaseAdjustmentRecordsFromMarshallingArray_Injected();

		//System.Int32 UnityEngine.TextCore.LowLevel.FontEngine::GetMarkToMarkAdjustmentRecordsFromMarshallingArray_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_TextCore_LowLevel_FontEngine_GetMarkToMarkAdjustmentRecordsFromMarshallingArray_Injected();
		Register_UnityEngine_TextCore_LowLevel_FontEngine_GetMarkToMarkAdjustmentRecordsFromMarshallingArray_Injected();

		//System.Int32 UnityEngine.TextCore.LowLevel.FontEngine::GetPairAdjustmentRecordsFromMarshallingArray_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_TextCore_LowLevel_FontEngine_GetPairAdjustmentRecordsFromMarshallingArray_Injected();
		Register_UnityEngine_TextCore_LowLevel_FontEngine_GetPairAdjustmentRecordsFromMarshallingArray_Injected();

		//System.Int32 UnityEngine.TextCore.LowLevel.FontEngine::LoadFontFace_With_Size_And_FaceIndex_Internal_Injected(UnityEngine.Bindings.ManagedSpanWrapper&,System.Single,System.Int32)
		void Register_UnityEngine_TextCore_LowLevel_FontEngine_LoadFontFace_With_Size_And_FaceIndex_Internal_Injected();
		Register_UnityEngine_TextCore_LowLevel_FontEngine_LoadFontFace_With_Size_And_FaceIndex_Internal_Injected();

		//System.Int32 UnityEngine.TextCore.LowLevel.FontEngine::LoadFontFace_With_Size_and_FaceIndex_FromFont_Internal_Injected(System.IntPtr,System.Single,System.Int32)
		void Register_UnityEngine_TextCore_LowLevel_FontEngine_LoadFontFace_With_Size_and_FaceIndex_FromFont_Internal_Injected();
		Register_UnityEngine_TextCore_LowLevel_FontEngine_LoadFontFace_With_Size_and_FaceIndex_FromFont_Internal_Injected();

		//System.Int32 UnityEngine.TextCore.LowLevel.FontEngine::LoadFontFace_With_Size_by_FamilyName_and_StyleName_Internal_Injected(UnityEngine.Bindings.ManagedSpanWrapper&,UnityEngine.Bindings.ManagedSpanWrapper&,System.Single)
		void Register_UnityEngine_TextCore_LowLevel_FontEngine_LoadFontFace_With_Size_by_FamilyName_and_StyleName_Internal_Injected();
		Register_UnityEngine_TextCore_LowLevel_FontEngine_LoadFontFace_With_Size_by_FamilyName_and_StyleName_Internal_Injected();

		//System.Int32 UnityEngine.TextCore.LowLevel.FontEngine::PopulateLigatureSubstitutionRecordMarshallingArray_Injected(UnityEngine.Bindings.ManagedSpanWrapper&,System.Int32&)
		void Register_UnityEngine_TextCore_LowLevel_FontEngine_PopulateLigatureSubstitutionRecordMarshallingArray_Injected();
		Register_UnityEngine_TextCore_LowLevel_FontEngine_PopulateLigatureSubstitutionRecordMarshallingArray_Injected();

		//System.Int32 UnityEngine.TextCore.LowLevel.FontEngine::PopulateMarkToBaseAdjustmentRecordMarshallingArray_Injected(UnityEngine.Bindings.ManagedSpanWrapper&,System.Int32&)
		void Register_UnityEngine_TextCore_LowLevel_FontEngine_PopulateMarkToBaseAdjustmentRecordMarshallingArray_Injected();
		Register_UnityEngine_TextCore_LowLevel_FontEngine_PopulateMarkToBaseAdjustmentRecordMarshallingArray_Injected();

		//System.Int32 UnityEngine.TextCore.LowLevel.FontEngine::PopulateMarkToMarkAdjustmentRecordMarshallingArray_Injected(UnityEngine.Bindings.ManagedSpanWrapper&,System.Int32&)
		void Register_UnityEngine_TextCore_LowLevel_FontEngine_PopulateMarkToMarkAdjustmentRecordMarshallingArray_Injected();
		Register_UnityEngine_TextCore_LowLevel_FontEngine_PopulateMarkToMarkAdjustmentRecordMarshallingArray_Injected();

		//System.Int32 UnityEngine.TextCore.LowLevel.FontEngine::PopulatePairAdjustmentRecordMarshallingArray_Injected(UnityEngine.Bindings.ManagedSpanWrapper&,System.Int32&)
		void Register_UnityEngine_TextCore_LowLevel_FontEngine_PopulatePairAdjustmentRecordMarshallingArray_Injected();
		Register_UnityEngine_TextCore_LowLevel_FontEngine_PopulatePairAdjustmentRecordMarshallingArray_Injected();

		//System.UInt32 UnityEngine.TextCore.LowLevel.FontEngine::GetGlyphIndex(System.UInt32)
		void Register_UnityEngine_TextCore_LowLevel_FontEngine_GetGlyphIndex();
		Register_UnityEngine_TextCore_LowLevel_FontEngine_GetGlyphIndex();

		//System.UInt32 UnityEngine.TextCore.LowLevel.FontEngine::GetVariantGlyphIndex(System.UInt32,System.UInt32)
		void Register_UnityEngine_TextCore_LowLevel_FontEngine_GetVariantGlyphIndex();
		Register_UnityEngine_TextCore_LowLevel_FontEngine_GetVariantGlyphIndex();

		//System.Void UnityEngine.TextCore.LowLevel.FontEngine::GetAllMarkToBaseAdjustmentRecords_Injected(UnityEngine.Bindings.BlittableArrayWrapper&)
		void Register_UnityEngine_TextCore_LowLevel_FontEngine_GetAllMarkToBaseAdjustmentRecords_Injected();
		Register_UnityEngine_TextCore_LowLevel_FontEngine_GetAllMarkToBaseAdjustmentRecords_Injected();

		//System.Void UnityEngine.TextCore.LowLevel.FontEngine::GetAllMarkToMarkAdjustmentRecords_Injected(UnityEngine.Bindings.BlittableArrayWrapper&)
		void Register_UnityEngine_TextCore_LowLevel_FontEngine_GetAllMarkToMarkAdjustmentRecords_Injected();
		Register_UnityEngine_TextCore_LowLevel_FontEngine_GetAllMarkToMarkAdjustmentRecords_Injected();

		//System.Void UnityEngine.TextCore.LowLevel.FontEngine::GetAllPairAdjustmentRecords_Injected(UnityEngine.Bindings.BlittableArrayWrapper&)
		void Register_UnityEngine_TextCore_LowLevel_FontEngine_GetAllPairAdjustmentRecords_Injected();
		Register_UnityEngine_TextCore_LowLevel_FontEngine_GetAllPairAdjustmentRecords_Injected();

		//System.Void UnityEngine.TextCore.LowLevel.FontEngine::ResetAtlasTexture_Injected(System.IntPtr)
		void Register_UnityEngine_TextCore_LowLevel_FontEngine_ResetAtlasTexture_Injected();
		Register_UnityEngine_TextCore_LowLevel_FontEngine_ResetAtlasTexture_Injected();

		//System.Void UnityEngine.TextCore.LowLevel.FontEngine::SetTextureUploadMode(System.Boolean)
		void Register_UnityEngine_TextCore_LowLevel_FontEngine_SetTextureUploadMode();
		Register_UnityEngine_TextCore_LowLevel_FontEngine_SetTextureUploadMode();

		//UnityEngine.TextCore.LowLevel.LigatureSubstitutionRecord[] UnityEngine.TextCore.LowLevel.FontEngine::GetAllLigatureSubstitutionRecords()
		void Register_UnityEngine_TextCore_LowLevel_FontEngine_GetAllLigatureSubstitutionRecords();
		Register_UnityEngine_TextCore_LowLevel_FontEngine_GetAllLigatureSubstitutionRecords();

	//End Registrations for type : UnityEngine.TextCore.LowLevel.FontEngine

	//Start Registrations for type : UnityEngine.TextCore.Text.FontAsset

		//System.IntPtr UnityEngine.TextCore.Text.FontAsset::Create_Injected(UnityEngine.TextCore.FaceInfo&,System.IntPtr,System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&,System.Int32,UnityEngine.Bindings.ManagedSpanWrapper&,UnityEngine.Bindings.ManagedSpanWrapper&,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_TextCore_Text_FontAsset_Create_Injected();
		Register_UnityEngine_TextCore_Text_FontAsset_Create_Injected();

		//System.Void UnityEngine.TextCore.Text.FontAsset::Destroy(System.IntPtr)
		void Register_UnityEngine_TextCore_Text_FontAsset_Destroy();
		Register_UnityEngine_TextCore_Text_FontAsset_Destroy();

		//System.Void UnityEngine.TextCore.Text.FontAsset::UpdateFaceInfo_Injected(System.IntPtr,UnityEngine.TextCore.FaceInfo&)
		void Register_UnityEngine_TextCore_Text_FontAsset_UpdateFaceInfo_Injected();
		Register_UnityEngine_TextCore_Text_FontAsset_UpdateFaceInfo_Injected();

		//System.Void UnityEngine.TextCore.Text.FontAsset::UpdateFallbacks_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_TextCore_Text_FontAsset_UpdateFallbacks_Injected();
		Register_UnityEngine_TextCore_Text_FontAsset_UpdateFallbacks_Injected();

		//System.Void UnityEngine.TextCore.Text.FontAsset::UpdateWeightFallbacks_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_TextCore_Text_FontAsset_UpdateWeightFallbacks_Injected();
		Register_UnityEngine_TextCore_Text_FontAsset_UpdateWeightFallbacks_Injected();

	//End Registrations for type : UnityEngine.TextCore.Text.FontAsset

	//Start Registrations for type : UnityEngine.TextCore.Text.TextGenerationInfo

		//System.IntPtr UnityEngine.TextCore.Text.TextGenerationInfo::Create()
		void Register_UnityEngine_TextCore_Text_TextGenerationInfo_Create();
		Register_UnityEngine_TextCore_Text_TextGenerationInfo_Create();

		//System.Void UnityEngine.TextCore.Text.TextGenerationInfo::Destroy(System.IntPtr)
		void Register_UnityEngine_TextCore_Text_TextGenerationInfo_Destroy();
		Register_UnityEngine_TextCore_Text_TextGenerationInfo_Destroy();

	//End Registrations for type : UnityEngine.TextCore.Text.TextGenerationInfo

	//Start Registrations for type : UnityEngine.TextCore.Text.TextLib

		//System.Int32 UnityEngine.TextCore.Text.TextLib::FindIntersectingLink_Injected(UnityEngine.Vector2&,System.IntPtr)
		void Register_UnityEngine_TextCore_Text_TextLib_FindIntersectingLink_Injected();
		Register_UnityEngine_TextCore_Text_TextLib_FindIntersectingLink_Injected();

		//System.IntPtr UnityEngine.TextCore.Text.TextLib::GetInstance_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_TextCore_Text_TextLib_GetInstance_Injected();
		Register_UnityEngine_TextCore_Text_TextLib_GetInstance_Injected();

		//System.Void UnityEngine.TextCore.Text.TextLib::GenerateTextInternal_Injected(System.IntPtr,UnityEngine.TextCore.NativeTextGenerationSettings&,System.IntPtr,UnityEngine.TextCore.Text.NativeTextInfo&)
		void Register_UnityEngine_TextCore_Text_TextLib_GenerateTextInternal_Injected();
		Register_UnityEngine_TextCore_Text_TextLib_GenerateTextInternal_Injected();

		//System.Void UnityEngine.TextCore.Text.TextLib::MeasureText_Injected(System.IntPtr,UnityEngine.TextCore.NativeTextGenerationSettings&,System.IntPtr,UnityEngine.Vector2&)
		void Register_UnityEngine_TextCore_Text_TextLib_MeasureText_Injected();
		Register_UnityEngine_TextCore_Text_TextLib_MeasureText_Injected();

	//End Registrations for type : UnityEngine.TextCore.Text.TextLib

	//Start Registrations for type : UnityEngine.TextCore.Text.TextSelectionService

		//System.Int32 UnityEngine.TextCore.Text.TextSelectionService::GetCursorLogicalIndexFromPosition_Injected(System.IntPtr,UnityEngine.Vector2&)
		void Register_UnityEngine_TextCore_Text_TextSelectionService_GetCursorLogicalIndexFromPosition_Injected();
		Register_UnityEngine_TextCore_Text_TextSelectionService_GetCursorLogicalIndexFromPosition_Injected();

		//System.Int32 UnityEngine.TextCore.Text.TextSelectionService::GetEndOfPreviousWord(System.IntPtr,System.Int32)
		void Register_UnityEngine_TextCore_Text_TextSelectionService_GetEndOfPreviousWord();
		Register_UnityEngine_TextCore_Text_TextSelectionService_GetEndOfPreviousWord();

		//System.Int32 UnityEngine.TextCore.Text.TextSelectionService::GetFirstCharacterIndexOnLine(System.IntPtr,System.Int32)
		void Register_UnityEngine_TextCore_Text_TextSelectionService_GetFirstCharacterIndexOnLine();
		Register_UnityEngine_TextCore_Text_TextSelectionService_GetFirstCharacterIndexOnLine();

		//System.Int32 UnityEngine.TextCore.Text.TextSelectionService::GetLastCharacterIndexOnLine(System.IntPtr,System.Int32)
		void Register_UnityEngine_TextCore_Text_TextSelectionService_GetLastCharacterIndexOnLine();
		Register_UnityEngine_TextCore_Text_TextSelectionService_GetLastCharacterIndexOnLine();

		//System.Int32 UnityEngine.TextCore.Text.TextSelectionService::GetLineNumber(System.IntPtr,System.Int32)
		void Register_UnityEngine_TextCore_Text_TextSelectionService_GetLineNumber();
		Register_UnityEngine_TextCore_Text_TextSelectionService_GetLineNumber();

		//System.Int32 UnityEngine.TextCore.Text.TextSelectionService::GetStartOfNextWord(System.IntPtr,System.Int32)
		void Register_UnityEngine_TextCore_Text_TextSelectionService_GetStartOfNextWord();
		Register_UnityEngine_TextCore_Text_TextSelectionService_GetStartOfNextWord();

		//System.Int32 UnityEngine.TextCore.Text.TextSelectionService::LineDownCharacterPosition(System.IntPtr,System.Int32)
		void Register_UnityEngine_TextCore_Text_TextSelectionService_LineDownCharacterPosition();
		Register_UnityEngine_TextCore_Text_TextSelectionService_LineDownCharacterPosition();

		//System.Int32 UnityEngine.TextCore.Text.TextSelectionService::LineUpCharacterPosition(System.IntPtr,System.Int32)
		void Register_UnityEngine_TextCore_Text_TextSelectionService_LineUpCharacterPosition();
		Register_UnityEngine_TextCore_Text_TextSelectionService_LineUpCharacterPosition();

		//System.Int32 UnityEngine.TextCore.Text.TextSelectionService::NextCodePointIndex(System.IntPtr,System.Int32)
		void Register_UnityEngine_TextCore_Text_TextSelectionService_NextCodePointIndex();
		Register_UnityEngine_TextCore_Text_TextSelectionService_NextCodePointIndex();

		//System.Int32 UnityEngine.TextCore.Text.TextSelectionService::PreviousCodePointIndex(System.IntPtr,System.Int32)
		void Register_UnityEngine_TextCore_Text_TextSelectionService_PreviousCodePointIndex();
		Register_UnityEngine_TextCore_Text_TextSelectionService_PreviousCodePointIndex();

		//System.Single UnityEngine.TextCore.Text.TextSelectionService::GetCharacterHeightFromIndex(System.IntPtr,System.Int32)
		void Register_UnityEngine_TextCore_Text_TextSelectionService_GetCharacterHeightFromIndex();
		Register_UnityEngine_TextCore_Text_TextSelectionService_GetCharacterHeightFromIndex();

		//System.Single UnityEngine.TextCore.Text.TextSelectionService::GetLineHeight(System.IntPtr,System.Int32)
		void Register_UnityEngine_TextCore_Text_TextSelectionService_GetLineHeight();
		Register_UnityEngine_TextCore_Text_TextSelectionService_GetLineHeight();

		//System.Void UnityEngine.TextCore.Text.TextSelectionService::GetCursorPositionFromLogicalIndex_Injected(System.IntPtr,System.Int32,UnityEngine.Vector2&)
		void Register_UnityEngine_TextCore_Text_TextSelectionService_GetCursorPositionFromLogicalIndex_Injected();
		Register_UnityEngine_TextCore_Text_TextSelectionService_GetCursorPositionFromLogicalIndex_Injected();

		//System.Void UnityEngine.TextCore.Text.TextSelectionService::GetHighlightRectangles_Injected(System.IntPtr,System.Int32,System.Int32,UnityEngine.Bindings.BlittableArrayWrapper&)
		void Register_UnityEngine_TextCore_Text_TextSelectionService_GetHighlightRectangles_Injected();
		Register_UnityEngine_TextCore_Text_TextSelectionService_GetHighlightRectangles_Injected();

		//System.Void UnityEngine.TextCore.Text.TextSelectionService::SelectCurrentParagraph(System.IntPtr,System.Int32&,System.Int32&)
		void Register_UnityEngine_TextCore_Text_TextSelectionService_SelectCurrentParagraph();
		Register_UnityEngine_TextCore_Text_TextSelectionService_SelectCurrentParagraph();

		//System.Void UnityEngine.TextCore.Text.TextSelectionService::SelectCurrentWord(System.IntPtr,System.Int32,System.Int32&,System.Int32&)
		void Register_UnityEngine_TextCore_Text_TextSelectionService_SelectCurrentWord();
		Register_UnityEngine_TextCore_Text_TextSelectionService_SelectCurrentWord();

		//System.Void UnityEngine.TextCore.Text.TextSelectionService::SelectToEndOfParagraph(System.IntPtr,System.Int32&)
		void Register_UnityEngine_TextCore_Text_TextSelectionService_SelectToEndOfParagraph();
		Register_UnityEngine_TextCore_Text_TextSelectionService_SelectToEndOfParagraph();

		//System.Void UnityEngine.TextCore.Text.TextSelectionService::SelectToNextParagraph(System.IntPtr,System.Int32&)
		void Register_UnityEngine_TextCore_Text_TextSelectionService_SelectToNextParagraph();
		Register_UnityEngine_TextCore_Text_TextSelectionService_SelectToNextParagraph();

		//System.Void UnityEngine.TextCore.Text.TextSelectionService::SelectToPreviousParagraph(System.IntPtr,System.Int32&)
		void Register_UnityEngine_TextCore_Text_TextSelectionService_SelectToPreviousParagraph();
		Register_UnityEngine_TextCore_Text_TextSelectionService_SelectToPreviousParagraph();

		//System.Void UnityEngine.TextCore.Text.TextSelectionService::SelectToStartOfParagraph(System.IntPtr,System.Int32&)
		void Register_UnityEngine_TextCore_Text_TextSelectionService_SelectToStartOfParagraph();
		Register_UnityEngine_TextCore_Text_TextSelectionService_SelectToStartOfParagraph();

		//System.Void UnityEngine.TextCore.Text.TextSelectionService::Substring_Injected(System.IntPtr,System.Int32,System.Int32,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_TextCore_Text_TextSelectionService_Substring_Injected();
		Register_UnityEngine_TextCore_Text_TextSelectionService_Substring_Injected();

	//End Registrations for type : UnityEngine.TextCore.Text.TextSelectionService

	//Start Registrations for type : UnityEngine.TextGenerator

		//System.Boolean UnityEngine.TextGenerator::Populate_Internal_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&,System.IntPtr,UnityEngine.Color&,System.Int32,System.Single,System.Single,UnityEngine.FontStyle,System.Boolean,System.Boolean,System.Int32,System.Int32,System.Int32,System.Int32,System.Boolean,UnityEngine.TextAnchor,System.Single,System.Single,System.Single,System.Single,System.Boolean,System.Boolean,System.UInt32&)
		void Register_UnityEngine_TextGenerator_Populate_Internal_Injected();
		Register_UnityEngine_TextGenerator_Populate_Internal_Injected();

		//System.Int32 UnityEngine.TextGenerator::get_characterCount_Injected(System.IntPtr)
		void Register_UnityEngine_TextGenerator_get_characterCount_Injected();
		Register_UnityEngine_TextGenerator_get_characterCount_Injected();

		//System.Int32 UnityEngine.TextGenerator::get_lineCount_Injected(System.IntPtr)
		void Register_UnityEngine_TextGenerator_get_lineCount_Injected();
		Register_UnityEngine_TextGenerator_get_lineCount_Injected();

		//System.IntPtr UnityEngine.TextGenerator::Internal_Create()
		void Register_UnityEngine_TextGenerator_Internal_Create();
		Register_UnityEngine_TextGenerator_Internal_Create();

		//System.Void UnityEngine.TextGenerator::GetCharactersInternal_Injected(System.IntPtr,System.Object)
		void Register_UnityEngine_TextGenerator_GetCharactersInternal_Injected();
		Register_UnityEngine_TextGenerator_GetCharactersInternal_Injected();

		//System.Void UnityEngine.TextGenerator::GetLinesInternal_Injected(System.IntPtr,System.Object)
		void Register_UnityEngine_TextGenerator_GetLinesInternal_Injected();
		Register_UnityEngine_TextGenerator_GetLinesInternal_Injected();

		//System.Void UnityEngine.TextGenerator::GetVerticesInternal_Injected(System.IntPtr,System.Object)
		void Register_UnityEngine_TextGenerator_GetVerticesInternal_Injected();
		Register_UnityEngine_TextGenerator_GetVerticesInternal_Injected();

		//System.Void UnityEngine.TextGenerator::Internal_Destroy(System.IntPtr)
		void Register_UnityEngine_TextGenerator_Internal_Destroy();
		Register_UnityEngine_TextGenerator_Internal_Destroy();

		//System.Void UnityEngine.TextGenerator::get_rectExtents_Injected(System.IntPtr,UnityEngine.Rect&)
		void Register_UnityEngine_TextGenerator_get_rectExtents_Injected();
		Register_UnityEngine_TextGenerator_get_rectExtents_Injected();

	//End Registrations for type : UnityEngine.TextGenerator

	//Start Registrations for type : UnityEngine.Texture

		//System.Boolean UnityEngine.Texture::get_isReadable_Injected(System.IntPtr)
		void Register_UnityEngine_Texture_get_isReadable_Injected();
		Register_UnityEngine_Texture_get_isReadable_Injected();

		//System.Int32 UnityEngine.Texture::GetDataHeight_Injected(System.IntPtr)
		void Register_UnityEngine_Texture_GetDataHeight_Injected();
		Register_UnityEngine_Texture_GetDataHeight_Injected();

		//System.Int32 UnityEngine.Texture::GetDataWidth_Injected(System.IntPtr)
		void Register_UnityEngine_Texture_GetDataWidth_Injected();
		Register_UnityEngine_Texture_GetDataWidth_Injected();

		//System.Int32 UnityEngine.Texture::Internal_GetActiveTextureColorSpace_Injected(System.IntPtr)
		void Register_UnityEngine_Texture_Internal_GetActiveTextureColorSpace_Injected();
		Register_UnityEngine_Texture_Internal_GetActiveTextureColorSpace_Injected();

		//System.Void UnityEngine.Texture::get_texelSize_Injected(System.IntPtr,UnityEngine.Vector2&)
		void Register_UnityEngine_Texture_get_texelSize_Injected();
		Register_UnityEngine_Texture_get_texelSize_Injected();

		//System.Void UnityEngine.Texture::set_filterMode_Injected(System.IntPtr,UnityEngine.FilterMode)
		void Register_UnityEngine_Texture_set_filterMode_Injected();
		Register_UnityEngine_Texture_set_filterMode_Injected();

		//UnityEngine.FilterMode UnityEngine.Texture::get_filterMode_Injected(System.IntPtr)
		void Register_UnityEngine_Texture_get_filterMode_Injected();
		Register_UnityEngine_Texture_get_filterMode_Injected();

		//UnityEngine.TextureWrapMode UnityEngine.Texture::get_wrapMode_Injected(System.IntPtr)
		void Register_UnityEngine_Texture_get_wrapMode_Injected();
		Register_UnityEngine_Texture_get_wrapMode_Injected();

	//End Registrations for type : UnityEngine.Texture

	//Start Registrations for type : UnityEngine.Texture2D

		//System.Boolean UnityEngine.Texture2D::Internal_CreateImpl_Injected(UnityEngine.Texture2D,System.Int32,System.Int32,System.Int32,UnityEngine.Experimental.Rendering.GraphicsFormat,UnityEngine.TextureColorSpace,UnityEngine.Experimental.Rendering.TextureCreationFlags,System.IntPtr,System.Boolean,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_Texture2D_Internal_CreateImpl_Injected();
		Register_UnityEngine_Texture2D_Internal_CreateImpl_Injected();

		//System.Boolean UnityEngine.Texture2D::ReinitializeImpl_Injected(System.IntPtr,System.Int32,System.Int32)
		void Register_UnityEngine_Texture2D_ReinitializeImpl_Injected();
		Register_UnityEngine_Texture2D_ReinitializeImpl_Injected();

		//System.Boolean UnityEngine.Texture2D::ReinitializeWithTextureFormatImpl_Injected(System.IntPtr,System.Int32,System.Int32,UnityEngine.TextureFormat,System.Boolean)
		void Register_UnityEngine_Texture2D_ReinitializeWithTextureFormatImpl_Injected();
		Register_UnityEngine_Texture2D_ReinitializeWithTextureFormatImpl_Injected();

		//System.Boolean UnityEngine.Texture2D::get_isReadable_Injected(System.IntPtr)
		void Register_UnityEngine_Texture2D_get_isReadable_Injected();
		Register_UnityEngine_Texture2D_get_isReadable_Injected();

		//System.IntPtr UnityEngine.Texture2D::GetWritableImageData_Injected(System.IntPtr,System.Int32)
		void Register_UnityEngine_Texture2D_GetWritableImageData_Injected();
		Register_UnityEngine_Texture2D_GetWritableImageData_Injected();

		//System.IntPtr UnityEngine.Texture2D::get_whiteTexture_Injected()
		void Register_UnityEngine_Texture2D_get_whiteTexture_Injected();
		Register_UnityEngine_Texture2D_get_whiteTexture_Injected();

		//System.UInt64 UnityEngine.Texture2D::GetImageDataSize_Injected(System.IntPtr)
		void Register_UnityEngine_Texture2D_GetImageDataSize_Injected();
		Register_UnityEngine_Texture2D_GetImageDataSize_Injected();

		//System.Void UnityEngine.Texture2D::ApplyImpl_Injected(System.IntPtr,System.Boolean,System.Boolean)
		void Register_UnityEngine_Texture2D_ApplyImpl_Injected();
		Register_UnityEngine_Texture2D_ApplyImpl_Injected();

		//System.Void UnityEngine.Texture2D::GetPixelBilinearImpl_Injected(System.IntPtr,System.Int32,System.Int32,System.Single,System.Single,UnityEngine.Color&)
		void Register_UnityEngine_Texture2D_GetPixelBilinearImpl_Injected();
		Register_UnityEngine_Texture2D_GetPixelBilinearImpl_Injected();

		//System.Void UnityEngine.Texture2D::SetAllPixels32_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&,System.Int32)
		void Register_UnityEngine_Texture2D_SetAllPixels32_Injected();
		Register_UnityEngine_Texture2D_SetAllPixels32_Injected();

		//System.Void UnityEngine.Texture2D::SetPixelImpl_Injected(System.IntPtr,System.Int32,System.Int32,System.Int32,System.Int32,UnityEngine.Color&)
		void Register_UnityEngine_Texture2D_SetPixelImpl_Injected();
		Register_UnityEngine_Texture2D_SetPixelImpl_Injected();

		//UnityEngine.TextureFormat UnityEngine.Texture2D::get_format_Injected(System.IntPtr)
		void Register_UnityEngine_Texture2D_get_format_Injected();
		Register_UnityEngine_Texture2D_get_format_Injected();

	//End Registrations for type : UnityEngine.Texture2D

	//Start Registrations for type : UnityEngine.Texture2DArray

		//System.Boolean UnityEngine.Texture2DArray::Internal_CreateImpl_Injected(UnityEngine.Texture2DArray,System.Int32,System.Int32,System.Int32,System.Int32,UnityEngine.Experimental.Rendering.GraphicsFormat,UnityEngine.TextureColorSpace,UnityEngine.Experimental.Rendering.TextureCreationFlags,System.Boolean,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_Texture2DArray_Internal_CreateImpl_Injected();
		Register_UnityEngine_Texture2DArray_Internal_CreateImpl_Injected();

		//System.Boolean UnityEngine.Texture2DArray::get_isReadable_Injected(System.IntPtr)
		void Register_UnityEngine_Texture2DArray_get_isReadable_Injected();
		Register_UnityEngine_Texture2DArray_get_isReadable_Injected();

	//End Registrations for type : UnityEngine.Texture2DArray

	//Start Registrations for type : UnityEngine.Texture3D

		//System.Boolean UnityEngine.Texture3D::Internal_CreateImpl(UnityEngine.Texture3D,System.Int32,System.Int32,System.Int32,System.Int32,UnityEngine.Experimental.Rendering.GraphicsFormat,UnityEngine.TextureColorSpace,UnityEngine.Experimental.Rendering.TextureCreationFlags,System.IntPtr)
		void Register_UnityEngine_Texture3D_Internal_CreateImpl();
		Register_UnityEngine_Texture3D_Internal_CreateImpl();

		//System.Boolean UnityEngine.Texture3D::get_isReadable_Injected(System.IntPtr)
		void Register_UnityEngine_Texture3D_get_isReadable_Injected();
		Register_UnityEngine_Texture3D_get_isReadable_Injected();

	//End Registrations for type : UnityEngine.Texture3D

	//Start Registrations for type : UnityEngine.Time

		//System.Int32 UnityEngine.Time::get_frameCount()
		void Register_UnityEngine_Time_get_frameCount();
		Register_UnityEngine_Time_get_frameCount();

		//System.Single UnityEngine.Time::get_deltaTime()
		void Register_UnityEngine_Time_get_deltaTime();
		Register_UnityEngine_Time_get_deltaTime();

		//System.Single UnityEngine.Time::get_fixedDeltaTime()
		void Register_UnityEngine_Time_get_fixedDeltaTime();
		Register_UnityEngine_Time_get_fixedDeltaTime();

		//System.Single UnityEngine.Time::get_fixedUnscaledTime()
		void Register_UnityEngine_Time_get_fixedUnscaledTime();
		Register_UnityEngine_Time_get_fixedUnscaledTime();

		//System.Single UnityEngine.Time::get_realtimeSinceStartup()
		void Register_UnityEngine_Time_get_realtimeSinceStartup();
		Register_UnityEngine_Time_get_realtimeSinceStartup();

		//System.Single UnityEngine.Time::get_unscaledDeltaTime()
		void Register_UnityEngine_Time_get_unscaledDeltaTime();
		Register_UnityEngine_Time_get_unscaledDeltaTime();

		//System.Single UnityEngine.Time::get_unscaledTime()
		void Register_UnityEngine_Time_get_unscaledTime();
		Register_UnityEngine_Time_get_unscaledTime();

		//System.Void UnityEngine.Time::get_timeAsRational_Injected(Unity.IntegerTime.RationalTime&)
		void Register_UnityEngine_Time_get_timeAsRational_Injected();
		Register_UnityEngine_Time_get_timeAsRational_Injected();

	//End Registrations for type : UnityEngine.Time

	//Start Registrations for type : UnityEngine.TouchScreenKeyboard

		//System.Boolean UnityEngine.TouchScreenKeyboard::IsInPlaceEditingAllowed()
		void Register_UnityEngine_TouchScreenKeyboard_IsInPlaceEditingAllowed();
		Register_UnityEngine_TouchScreenKeyboard_IsInPlaceEditingAllowed();

		//System.Boolean UnityEngine.TouchScreenKeyboard::get_active_Injected(System.IntPtr)
		void Register_UnityEngine_TouchScreenKeyboard_get_active_Injected();
		Register_UnityEngine_TouchScreenKeyboard_get_active_Injected();

		//System.Boolean UnityEngine.TouchScreenKeyboard::get_canGetSelection_Injected(System.IntPtr)
		void Register_UnityEngine_TouchScreenKeyboard_get_canGetSelection_Injected();
		Register_UnityEngine_TouchScreenKeyboard_get_canGetSelection_Injected();

		//System.Boolean UnityEngine.TouchScreenKeyboard::get_canSetSelection_Injected(System.IntPtr)
		void Register_UnityEngine_TouchScreenKeyboard_get_canSetSelection_Injected();
		Register_UnityEngine_TouchScreenKeyboard_get_canSetSelection_Injected();

		//System.IntPtr UnityEngine.TouchScreenKeyboard::TouchScreenKeyboard_InternalConstructorHelper_Injected(UnityEngine.TouchScreenKeyboard_InternalConstructorHelperArguments&,UnityEngine.Bindings.ManagedSpanWrapper&,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_TouchScreenKeyboard_TouchScreenKeyboard_InternalConstructorHelper_Injected();
		Register_UnityEngine_TouchScreenKeyboard_TouchScreenKeyboard_InternalConstructorHelper_Injected();

		//System.Void UnityEngine.TouchScreenKeyboard::GetSelection(System.Int32&,System.Int32&)
		void Register_UnityEngine_TouchScreenKeyboard_GetSelection();
		Register_UnityEngine_TouchScreenKeyboard_GetSelection();

		//System.Void UnityEngine.TouchScreenKeyboard::Internal_Destroy(System.IntPtr)
		void Register_UnityEngine_TouchScreenKeyboard_Internal_Destroy();
		Register_UnityEngine_TouchScreenKeyboard_Internal_Destroy();

		//System.Void UnityEngine.TouchScreenKeyboard::SetSelection(System.Int32,System.Int32)
		void Register_UnityEngine_TouchScreenKeyboard_SetSelection();
		Register_UnityEngine_TouchScreenKeyboard_SetSelection();

		//System.Void UnityEngine.TouchScreenKeyboard::get_text_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_TouchScreenKeyboard_get_text_Injected();
		Register_UnityEngine_TouchScreenKeyboard_get_text_Injected();

		//System.Void UnityEngine.TouchScreenKeyboard::set_active_Injected(System.IntPtr,System.Boolean)
		void Register_UnityEngine_TouchScreenKeyboard_set_active_Injected();
		Register_UnityEngine_TouchScreenKeyboard_set_active_Injected();

		//System.Void UnityEngine.TouchScreenKeyboard::set_characterLimit_Injected(System.IntPtr,System.Int32)
		void Register_UnityEngine_TouchScreenKeyboard_set_characterLimit_Injected();
		Register_UnityEngine_TouchScreenKeyboard_set_characterLimit_Injected();

		//System.Void UnityEngine.TouchScreenKeyboard::set_hideInput(System.Boolean)
		void Register_UnityEngine_TouchScreenKeyboard_set_hideInput();
		Register_UnityEngine_TouchScreenKeyboard_set_hideInput();

		//System.Void UnityEngine.TouchScreenKeyboard::set_text_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_TouchScreenKeyboard_set_text_Injected();
		Register_UnityEngine_TouchScreenKeyboard_set_text_Injected();

		//UnityEngine.TouchScreenKeyboard/InputFieldAppearance UnityEngine.TouchScreenKeyboard::get_inputFieldAppearance()
		void Register_UnityEngine_TouchScreenKeyboard_get_inputFieldAppearance();
		Register_UnityEngine_TouchScreenKeyboard_get_inputFieldAppearance();

		//UnityEngine.TouchScreenKeyboard/Status UnityEngine.TouchScreenKeyboard::get_status_Injected(System.IntPtr)
		void Register_UnityEngine_TouchScreenKeyboard_get_status_Injected();
		Register_UnityEngine_TouchScreenKeyboard_get_status_Injected();

	//End Registrations for type : UnityEngine.TouchScreenKeyboard

	//Start Registrations for type : UnityEngine.Transform

		//System.Boolean UnityEngine.Transform::IsChildOf_Injected(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_Transform_IsChildOf_Injected();
		Register_UnityEngine_Transform_IsChildOf_Injected();

		//System.Int32 UnityEngine.Transform::get_childCount_Injected(System.IntPtr)
		void Register_UnityEngine_Transform_get_childCount_Injected();
		Register_UnityEngine_Transform_get_childCount_Injected();

		//System.IntPtr UnityEngine.Transform::FindRelativeTransformWithPath_Injected(System.IntPtr,UnityEngine.Bindings.ManagedSpanWrapper&,System.Boolean)
		void Register_UnityEngine_Transform_FindRelativeTransformWithPath_Injected();
		Register_UnityEngine_Transform_FindRelativeTransformWithPath_Injected();

		//System.IntPtr UnityEngine.Transform::GetChild_Injected(System.IntPtr,System.Int32)
		void Register_UnityEngine_Transform_GetChild_Injected();
		Register_UnityEngine_Transform_GetChild_Injected();

		//System.IntPtr UnityEngine.Transform::GetParent_Injected(System.IntPtr)
		void Register_UnityEngine_Transform_GetParent_Injected();
		Register_UnityEngine_Transform_GetParent_Injected();

		//System.IntPtr UnityEngine.Transform::GetRoot_Injected(System.IntPtr)
		void Register_UnityEngine_Transform_GetRoot_Injected();
		Register_UnityEngine_Transform_GetRoot_Injected();

		//System.Void UnityEngine.Transform::InverseTransformDirection_Injected(System.IntPtr,UnityEngine.Vector3&,UnityEngine.Vector3&)
		void Register_UnityEngine_Transform_InverseTransformDirection_Injected();
		Register_UnityEngine_Transform_InverseTransformDirection_Injected();

		//System.Void UnityEngine.Transform::InverseTransformPoint_Injected(System.IntPtr,UnityEngine.Vector3&,UnityEngine.Vector3&)
		void Register_UnityEngine_Transform_InverseTransformPoint_Injected();
		Register_UnityEngine_Transform_InverseTransformPoint_Injected();

		//System.Void UnityEngine.Transform::SetAsFirstSibling_Injected(System.IntPtr)
		void Register_UnityEngine_Transform_SetAsFirstSibling_Injected();
		Register_UnityEngine_Transform_SetAsFirstSibling_Injected();

		//System.Void UnityEngine.Transform::SetLocalPositionAndRotation_Injected(System.IntPtr,UnityEngine.Vector3&,UnityEngine.Quaternion&)
		void Register_UnityEngine_Transform_SetLocalPositionAndRotation_Injected();
		Register_UnityEngine_Transform_SetLocalPositionAndRotation_Injected();

		//System.Void UnityEngine.Transform::SetParent_Injected(System.IntPtr,System.IntPtr,System.Boolean)
		void Register_UnityEngine_Transform_SetParent_Injected();
		Register_UnityEngine_Transform_SetParent_Injected();

		//System.Void UnityEngine.Transform::TransformPoint_Injected(System.IntPtr,UnityEngine.Vector3&,UnityEngine.Vector3&)
		void Register_UnityEngine_Transform_TransformPoint_Injected();
		Register_UnityEngine_Transform_TransformPoint_Injected();

		//System.Void UnityEngine.Transform::get_localPosition_Injected(System.IntPtr,UnityEngine.Vector3&)
		void Register_UnityEngine_Transform_get_localPosition_Injected();
		Register_UnityEngine_Transform_get_localPosition_Injected();

		//System.Void UnityEngine.Transform::get_localRotation_Injected(System.IntPtr,UnityEngine.Quaternion&)
		void Register_UnityEngine_Transform_get_localRotation_Injected();
		Register_UnityEngine_Transform_get_localRotation_Injected();

		//System.Void UnityEngine.Transform::get_localScale_Injected(System.IntPtr,UnityEngine.Vector3&)
		void Register_UnityEngine_Transform_get_localScale_Injected();
		Register_UnityEngine_Transform_get_localScale_Injected();

		//System.Void UnityEngine.Transform::get_localToWorldMatrix_Injected(System.IntPtr,UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Transform_get_localToWorldMatrix_Injected();
		Register_UnityEngine_Transform_get_localToWorldMatrix_Injected();

		//System.Void UnityEngine.Transform::get_position_Injected(System.IntPtr,UnityEngine.Vector3&)
		void Register_UnityEngine_Transform_get_position_Injected();
		Register_UnityEngine_Transform_get_position_Injected();

		//System.Void UnityEngine.Transform::get_rotation_Injected(System.IntPtr,UnityEngine.Quaternion&)
		void Register_UnityEngine_Transform_get_rotation_Injected();
		Register_UnityEngine_Transform_get_rotation_Injected();

		//System.Void UnityEngine.Transform::get_worldToLocalMatrix_Injected(System.IntPtr,UnityEngine.Matrix4x4&)
		void Register_UnityEngine_Transform_get_worldToLocalMatrix_Injected();
		Register_UnityEngine_Transform_get_worldToLocalMatrix_Injected();

		//System.Void UnityEngine.Transform::set_localPosition_Injected(System.IntPtr,UnityEngine.Vector3&)
		void Register_UnityEngine_Transform_set_localPosition_Injected();
		Register_UnityEngine_Transform_set_localPosition_Injected();

		//System.Void UnityEngine.Transform::set_localRotation_Injected(System.IntPtr,UnityEngine.Quaternion&)
		void Register_UnityEngine_Transform_set_localRotation_Injected();
		Register_UnityEngine_Transform_set_localRotation_Injected();

		//System.Void UnityEngine.Transform::set_localScale_Injected(System.IntPtr,UnityEngine.Vector3&)
		void Register_UnityEngine_Transform_set_localScale_Injected();
		Register_UnityEngine_Transform_set_localScale_Injected();

		//System.Void UnityEngine.Transform::set_position_Injected(System.IntPtr,UnityEngine.Vector3&)
		void Register_UnityEngine_Transform_set_position_Injected();
		Register_UnityEngine_Transform_set_position_Injected();

		//System.Void UnityEngine.Transform::set_rotation_Injected(System.IntPtr,UnityEngine.Quaternion&)
		void Register_UnityEngine_Transform_set_rotation_Injected();
		Register_UnityEngine_Transform_set_rotation_Injected();

	//End Registrations for type : UnityEngine.Transform

	//Start Registrations for type : UnityEngine.U2D.SpriteAtlas

		//System.Boolean UnityEngine.U2D.SpriteAtlas::CanBindTo_Injected(System.IntPtr,System.IntPtr)
		void Register_UnityEngine_U2D_SpriteAtlas_CanBindTo_Injected();
		Register_UnityEngine_U2D_SpriteAtlas_CanBindTo_Injected();

	//End Registrations for type : UnityEngine.U2D.SpriteAtlas

	//Start Registrations for type : UnityEngine.U2D.SpriteAtlasManager

		//System.Void UnityEngine.U2D.SpriteAtlasManager::Register_Injected(System.IntPtr)
		void Register_UnityEngine_U2D_SpriteAtlasManager_Register_Injected();
		Register_UnityEngine_U2D_SpriteAtlasManager_Register_Injected();

	//End Registrations for type : UnityEngine.U2D.SpriteAtlasManager

	//Start Registrations for type : UnityEngine.UIElements.Layout.LayoutNative

		//System.Void UnityEngine.UIElements.Layout.LayoutNative::CalculateLayout(System.IntPtr,System.Single,System.Single,System.Int32,System.IntPtr,System.IntPtr)
		void Register_UnityEngine_UIElements_Layout_LayoutNative_CalculateLayout();
		Register_UnityEngine_UIElements_Layout_LayoutNative_CalculateLayout();

	//End Registrations for type : UnityEngine.UIElements.Layout.LayoutNative

	//Start Registrations for type : UnityEngine.UIElements.MeshBuilderNative

		//System.Void UnityEngine.UIElements.MeshBuilderNative::MakeBorder_Injected(UnityEngine.UIElements.MeshBuilderNative/NativeBorderParams&,System.Single,UnityEngine.UIElements.MeshWriteDataInterface&)
		void Register_UnityEngine_UIElements_MeshBuilderNative_MakeBorder_Injected();
		Register_UnityEngine_UIElements_MeshBuilderNative_MakeBorder_Injected();

		//System.Void UnityEngine.UIElements.MeshBuilderNative::MakeSolidRect_Injected(UnityEngine.UIElements.MeshBuilderNative/NativeRectParams&,System.Single,UnityEngine.UIElements.MeshWriteDataInterface&)
		void Register_UnityEngine_UIElements_MeshBuilderNative_MakeSolidRect_Injected();
		Register_UnityEngine_UIElements_MeshBuilderNative_MakeSolidRect_Injected();

		//System.Void UnityEngine.UIElements.MeshBuilderNative::MakeTexturedRect_Injected(UnityEngine.UIElements.MeshBuilderNative/NativeRectParams&,System.Single,UnityEngine.UIElements.MeshWriteDataInterface&)
		void Register_UnityEngine_UIElements_MeshBuilderNative_MakeTexturedRect_Injected();
		Register_UnityEngine_UIElements_MeshBuilderNative_MakeTexturedRect_Injected();

		//System.Void UnityEngine.UIElements.MeshBuilderNative::MakeVectorGraphics9SliceBackground_Injected(UnityEngine.Bindings.ManagedSpanWrapper&,UnityEngine.Bindings.ManagedSpanWrapper&,System.Single,System.Single,UnityEngine.Rect&,UnityEngine.Vector4&,UnityEngine.Color&,UnityEngine.UIElements.MeshBuilderNative/NativeColorPage&,UnityEngine.UIElements.MeshWriteDataInterface&)
		void Register_UnityEngine_UIElements_MeshBuilderNative_MakeVectorGraphics9SliceBackground_Injected();
		Register_UnityEngine_UIElements_MeshBuilderNative_MakeVectorGraphics9SliceBackground_Injected();

		//System.Void UnityEngine.UIElements.MeshBuilderNative::MakeVectorGraphicsStretchBackground_Injected(UnityEngine.Bindings.ManagedSpanWrapper&,UnityEngine.Bindings.ManagedSpanWrapper&,System.Single,System.Single,UnityEngine.Rect&,UnityEngine.Rect&,UnityEngine.ScaleMode,UnityEngine.Color&,UnityEngine.UIElements.MeshBuilderNative/NativeColorPage&,UnityEngine.UIElements.MeshWriteDataInterface&)
		void Register_UnityEngine_UIElements_MeshBuilderNative_MakeVectorGraphicsStretchBackground_Injected();
		Register_UnityEngine_UIElements_MeshBuilderNative_MakeVectorGraphicsStretchBackground_Injected();

	//End Registrations for type : UnityEngine.UIElements.MeshBuilderNative

	//Start Registrations for type : UnityEngine.UIElements.UIElementsRuntimeUtilityNative

		//System.Void UnityEngine.UIElements.UIElementsRuntimeUtilityNative::RegisterPlayerloopCallback()
		void Register_UnityEngine_UIElements_UIElementsRuntimeUtilityNative_RegisterPlayerloopCallback();
		Register_UnityEngine_UIElements_UIElementsRuntimeUtilityNative_RegisterPlayerloopCallback();

		//System.Void UnityEngine.UIElements.UIElementsRuntimeUtilityNative::UnregisterPlayerloopCallback()
		void Register_UnityEngine_UIElements_UIElementsRuntimeUtilityNative_UnregisterPlayerloopCallback();
		Register_UnityEngine_UIElements_UIElementsRuntimeUtilityNative_UnregisterPlayerloopCallback();

		//System.Void UnityEngine.UIElements.UIElementsRuntimeUtilityNative::VisualElementCreation()
		void Register_UnityEngine_UIElements_UIElementsRuntimeUtilityNative_VisualElementCreation();
		Register_UnityEngine_UIElements_UIElementsRuntimeUtilityNative_VisualElementCreation();

	//End Registrations for type : UnityEngine.UIElements.UIElementsRuntimeUtilityNative

	//Start Registrations for type : UnityEngine.UIElements.UIPainter2D

		//System.IntPtr UnityEngine.UIElements.UIPainter2D::Create(System.Boolean)
		void Register_UnityEngine_UIElements_UIPainter2D_Create();
		Register_UnityEngine_UIElements_UIPainter2D_Create();

		//System.Void UnityEngine.UIElements.UIPainter2D::ClearSnapshots(System.IntPtr)
		void Register_UnityEngine_UIElements_UIPainter2D_ClearSnapshots();
		Register_UnityEngine_UIElements_UIPainter2D_ClearSnapshots();

		//System.Void UnityEngine.UIElements.UIPainter2D::Destroy(System.IntPtr)
		void Register_UnityEngine_UIElements_UIPainter2D_Destroy();
		Register_UnityEngine_UIElements_UIPainter2D_Destroy();

		//System.Void UnityEngine.UIElements.UIPainter2D::ExecuteSnapshotFromJob_Injected(System.IntPtr,System.Int32,UnityEngine.UIElements.MeshWriteDataInterface&)
		void Register_UnityEngine_UIElements_UIPainter2D_ExecuteSnapshotFromJob_Injected();
		Register_UnityEngine_UIElements_UIPainter2D_ExecuteSnapshotFromJob_Injected();

		//System.Void UnityEngine.UIElements.UIPainter2D::Reset(System.IntPtr)
		void Register_UnityEngine_UIElements_UIPainter2D_Reset();
		Register_UnityEngine_UIElements_UIPainter2D_Reset();

	//End Registrations for type : UnityEngine.UIElements.UIPainter2D

	//Start Registrations for type : UnityEngine.UIElements.UIR.JobProcessor

		//System.Void UnityEngine.UIElements.UIR.JobProcessor::ScheduleConvertMeshJobs_Injected(System.IntPtr,System.Int32,Unity.Jobs.JobHandle&)
		void Register_UnityEngine_UIElements_UIR_JobProcessor_ScheduleConvertMeshJobs_Injected();
		Register_UnityEngine_UIElements_UIR_JobProcessor_ScheduleConvertMeshJobs_Injected();

		//System.Void UnityEngine.UIElements.UIR.JobProcessor::ScheduleCopyMeshJobs_Injected(System.IntPtr,System.Int32,Unity.Jobs.JobHandle&)
		void Register_UnityEngine_UIElements_UIR_JobProcessor_ScheduleCopyMeshJobs_Injected();
		Register_UnityEngine_UIElements_UIR_JobProcessor_ScheduleCopyMeshJobs_Injected();

		//System.Void UnityEngine.UIElements.UIR.JobProcessor::ScheduleNudgeJobs_Injected(System.IntPtr,System.Int32,Unity.Jobs.JobHandle&)
		void Register_UnityEngine_UIElements_UIR_JobProcessor_ScheduleNudgeJobs_Injected();
		Register_UnityEngine_UIElements_UIR_JobProcessor_ScheduleNudgeJobs_Injected();

	//End Registrations for type : UnityEngine.UIElements.UIR.JobProcessor

	//Start Registrations for type : UnityEngine.UIElements.UIR.Utility

		//System.Boolean UnityEngine.UIElements.UIR.Utility::CPUFencePassed(System.UInt32)
		void Register_UnityEngine_UIElements_UIR_Utility_CPUFencePassed();
		Register_UnityEngine_UIElements_UIR_Utility_CPUFencePassed();

		//System.Boolean UnityEngine.UIElements.UIR.Utility::HasMappedBufferRange()
		void Register_UnityEngine_UIElements_UIR_Utility_HasMappedBufferRange();
		Register_UnityEngine_UIElements_UIR_Utility_HasMappedBufferRange();

		//System.IntPtr UnityEngine.UIElements.UIR.Utility::AllocateBuffer(System.Int32,System.Int32,System.Boolean)
		void Register_UnityEngine_UIElements_UIR_Utility_AllocateBuffer();
		Register_UnityEngine_UIElements_UIR_Utility_AllocateBuffer();

		//System.IntPtr UnityEngine.UIElements.UIR.Utility::CreateStencilState_Injected(UnityEngine.Rendering.StencilState&)
		void Register_UnityEngine_UIElements_UIR_Utility_CreateStencilState_Injected();
		Register_UnityEngine_UIElements_UIR_Utility_CreateStencilState_Injected();

		//System.IntPtr UnityEngine.UIElements.UIR.Utility::GetVertexDeclaration_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_UIElements_UIR_Utility_GetVertexDeclaration_Injected();
		Register_UnityEngine_UIElements_UIR_Utility_GetVertexDeclaration_Injected();

		//System.UInt32 UnityEngine.UIElements.UIR.Utility::InsertCPUFence()
		void Register_UnityEngine_UIElements_UIR_Utility_InsertCPUFence();
		Register_UnityEngine_UIElements_UIR_Utility_InsertCPUFence();

		//System.Void UnityEngine.UIElements.UIR.Utility::DisableScissor()
		void Register_UnityEngine_UIElements_UIR_Utility_DisableScissor();
		Register_UnityEngine_UIElements_UIR_Utility_DisableScissor();

		//System.Void UnityEngine.UIElements.UIR.Utility::DrawRanges(System.IntPtr,System.IntPtr*,System.Int32,System.IntPtr,System.Int32,System.IntPtr)
		void Register_UnityEngine_UIElements_UIR_Utility_DrawRanges();
		Register_UnityEngine_UIElements_UIR_Utility_DrawRanges();

		//System.Void UnityEngine.UIElements.UIR.Utility::FreeBuffer(System.IntPtr)
		void Register_UnityEngine_UIElements_UIR_Utility_FreeBuffer();
		Register_UnityEngine_UIElements_UIR_Utility_FreeBuffer();

		//System.Void UnityEngine.UIElements.UIR.Utility::GetActiveViewport_Injected(UnityEngine.RectInt&)
		void Register_UnityEngine_UIElements_UIR_Utility_GetActiveViewport_Injected();
		Register_UnityEngine_UIElements_UIR_Utility_GetActiveViewport_Injected();

		//System.Void UnityEngine.UIElements.UIR.Utility::GetUnityProjectionMatrix_Injected(UnityEngine.Matrix4x4&)
		void Register_UnityEngine_UIElements_UIR_Utility_GetUnityProjectionMatrix_Injected();
		Register_UnityEngine_UIElements_UIR_Utility_GetUnityProjectionMatrix_Injected();

		//System.Void UnityEngine.UIElements.UIR.Utility::NotifyOfUIREvents(System.Boolean)
		void Register_UnityEngine_UIElements_UIR_Utility_NotifyOfUIREvents();
		Register_UnityEngine_UIElements_UIR_Utility_NotifyOfUIREvents();

		//System.Void UnityEngine.UIElements.UIR.Utility::ProfileDrawChainBegin()
		void Register_UnityEngine_UIElements_UIR_Utility_ProfileDrawChainBegin();
		Register_UnityEngine_UIElements_UIR_Utility_ProfileDrawChainBegin();

		//System.Void UnityEngine.UIElements.UIR.Utility::ProfileDrawChainEnd()
		void Register_UnityEngine_UIElements_UIR_Utility_ProfileDrawChainEnd();
		Register_UnityEngine_UIElements_UIR_Utility_ProfileDrawChainEnd();

		//System.Void UnityEngine.UIElements.UIR.Utility::SetPropertyBlock_Injected(System.IntPtr)
		void Register_UnityEngine_UIElements_UIR_Utility_SetPropertyBlock_Injected();
		Register_UnityEngine_UIElements_UIR_Utility_SetPropertyBlock_Injected();

		//System.Void UnityEngine.UIElements.UIR.Utility::SetScissorRect_Injected(UnityEngine.RectInt&)
		void Register_UnityEngine_UIElements_UIR_Utility_SetScissorRect_Injected();
		Register_UnityEngine_UIElements_UIR_Utility_SetScissorRect_Injected();

		//System.Void UnityEngine.UIElements.UIR.Utility::SetStencilState(System.IntPtr,System.Int32)
		void Register_UnityEngine_UIElements_UIR_Utility_SetStencilState();
		Register_UnityEngine_UIElements_UIR_Utility_SetStencilState();

		//System.Void UnityEngine.UIElements.UIR.Utility::SyncRenderThread()
		void Register_UnityEngine_UIElements_UIR_Utility_SyncRenderThread();
		Register_UnityEngine_UIElements_UIR_Utility_SyncRenderThread();

		//System.Void UnityEngine.UIElements.UIR.Utility::UpdateBufferRanges(System.IntPtr,System.IntPtr,System.Int32,System.Int32,System.Int32)
		void Register_UnityEngine_UIElements_UIR_Utility_UpdateBufferRanges();
		Register_UnityEngine_UIElements_UIR_Utility_UpdateBufferRanges();

		//System.Void UnityEngine.UIElements.UIR.Utility::WaitForCPUFencePassed(System.UInt32)
		void Register_UnityEngine_UIElements_UIR_Utility_WaitForCPUFencePassed();
		Register_UnityEngine_UIElements_UIR_Utility_WaitForCPUFencePassed();

	//End Registrations for type : UnityEngine.UIElements.UIR.Utility

	//Start Registrations for type : UnityEngine.UIElements.UIRenderer

		//System.Void UnityEngine.UIElements.UIRenderer::SetNativeData_Injected(System.IntPtr,System.Int32,System.Int32,System.IntPtr)
		void Register_UnityEngine_UIElements_UIRenderer_SetNativeData_Injected();
		Register_UnityEngine_UIElements_UIRenderer_SetNativeData_Injected();

	//End Registrations for type : UnityEngine.UIElements.UIRenderer

	//Start Registrations for type : UnityEngine.UISystemProfilerApi

		//System.Void UnityEngine.UISystemProfilerApi::AddMarker_Injected(UnityEngine.Bindings.ManagedSpanWrapper&,System.IntPtr)
		void Register_UnityEngine_UISystemProfilerApi_AddMarker_Injected();
		Register_UnityEngine_UISystemProfilerApi_AddMarker_Injected();

		//System.Void UnityEngine.UISystemProfilerApi::BeginSample(UnityEngine.UISystemProfilerApi/SampleType)
		void Register_UnityEngine_UISystemProfilerApi_BeginSample();
		Register_UnityEngine_UISystemProfilerApi_BeginSample();

		//System.Void UnityEngine.UISystemProfilerApi::EndSample(UnityEngine.UISystemProfilerApi/SampleType)
		void Register_UnityEngine_UISystemProfilerApi_EndSample();
		Register_UnityEngine_UISystemProfilerApi_EndSample();

	//End Registrations for type : UnityEngine.UISystemProfilerApi

	//Start Registrations for type : UnityEngine.UnityLogWriter

		//System.Void UnityEngine.UnityLogWriter::WriteStringToUnityLogImpl_Injected(UnityEngine.Bindings.ManagedSpanWrapper&)
		void Register_UnityEngine_UnityLogWriter_WriteStringToUnityLogImpl_Injected();
		Register_UnityEngine_UnityLogWriter_WriteStringToUnityLogImpl_Injected();

	//End Registrations for type : UnityEngine.UnityLogWriter

	//Start Registrations for type : UnityEngine.Vector3

		//System.Void UnityEngine.Vector3::RotateTowards_Injected(UnityEngine.Vector3&,UnityEngine.Vector3&,System.Single,System.Single,UnityEngine.Vector3&)
		void Register_UnityEngine_Vector3_RotateTowards_Injected();
		Register_UnityEngine_Vector3_RotateTowards_Injected();

	//End Registrations for type : UnityEngine.Vector3

	//Start Registrations for type : UnityEngine.XR.XRDevice

		//System.Void UnityEngine.XR.XRDevice::DisableAutoXRCameraTracking_Injected(System.IntPtr,System.Boolean)
		void Register_UnityEngine_XR_XRDevice_DisableAutoXRCameraTracking_Injected();
		Register_UnityEngine_XR_XRDevice_DisableAutoXRCameraTracking_Injected();

	//End Registrations for type : UnityEngine.XR.XRDevice

	//Start Registrations for type : UnityEngine.XR.XRMeshSubsystem/MeshTransformList

		//System.Void UnityEngine.XR.XRMeshSubsystem/MeshTransformList::Dispose(System.IntPtr)
		void Register_UnityEngine_XR_XRMeshSubsystem_MeshTransformList_Dispose();
		Register_UnityEngine_XR_XRMeshSubsystem_MeshTransformList_Dispose();

	//End Registrations for type : UnityEngine.XR.XRMeshSubsystem/MeshTransformList

	//Start Registrations for type : UnityEngineInternal.Input.NativeInputSystem

		//System.Boolean UnityEngineInternal.Input.NativeInputSystem::get_normalizeScrollWheelDelta()
		void Register_UnityEngineInternal_Input_NativeInputSystem_get_normalizeScrollWheelDelta();
		Register_UnityEngineInternal_Input_NativeInputSystem_get_normalizeScrollWheelDelta();

		//System.Double UnityEngineInternal.Input.NativeInputSystem::get_currentTime()
		void Register_UnityEngineInternal_Input_NativeInputSystem_get_currentTime();
		Register_UnityEngineInternal_Input_NativeInputSystem_get_currentTime();

		//System.Double UnityEngineInternal.Input.NativeInputSystem::get_currentTimeOffsetToRealtimeSinceStartup()
		void Register_UnityEngineInternal_Input_NativeInputSystem_get_currentTimeOffsetToRealtimeSinceStartup();
		Register_UnityEngineInternal_Input_NativeInputSystem_get_currentTimeOffsetToRealtimeSinceStartup();

		//System.Int32 UnityEngineInternal.Input.NativeInputSystem::AllocateDeviceId()
		void Register_UnityEngineInternal_Input_NativeInputSystem_AllocateDeviceId();
		Register_UnityEngineInternal_Input_NativeInputSystem_AllocateDeviceId();

		//System.Int64 UnityEngineInternal.Input.NativeInputSystem::IOCTL(System.Int32,System.Int32,System.IntPtr,System.Int32)
		void Register_UnityEngineInternal_Input_NativeInputSystem_IOCTL();
		Register_UnityEngineInternal_Input_NativeInputSystem_IOCTL();

		//System.Single UnityEngineInternal.Input.NativeInputSystem::GetScrollWheelDeltaPerTick()
		void Register_UnityEngineInternal_Input_NativeInputSystem_GetScrollWheelDeltaPerTick();
		Register_UnityEngineInternal_Input_NativeInputSystem_GetScrollWheelDeltaPerTick();

		//System.Void UnityEngineInternal.Input.NativeInputSystem::QueueInputEvent(System.IntPtr)
		void Register_UnityEngineInternal_Input_NativeInputSystem_QueueInputEvent();
		Register_UnityEngineInternal_Input_NativeInputSystem_QueueInputEvent();

		//System.Void UnityEngineInternal.Input.NativeInputSystem::SetPollingFrequency(System.Single)
		void Register_UnityEngineInternal_Input_NativeInputSystem_SetPollingFrequency();
		Register_UnityEngineInternal_Input_NativeInputSystem_SetPollingFrequency();

		//System.Void UnityEngineInternal.Input.NativeInputSystem::Update(UnityEngineInternal.Input.NativeInputUpdateType)
		void Register_UnityEngineInternal_Input_NativeInputSystem_Update();
		Register_UnityEngineInternal_Input_NativeInputSystem_Update();

		//System.Void UnityEngineInternal.Input.NativeInputSystem::set_hasDeviceDiscoveredCallback(System.Boolean)
		void Register_UnityEngineInternal_Input_NativeInputSystem_set_hasDeviceDiscoveredCallback();
		Register_UnityEngineInternal_Input_NativeInputSystem_set_hasDeviceDiscoveredCallback();

		//System.Void UnityEngineInternal.Input.NativeInputSystem::set_normalizeScrollWheelDelta(System.Boolean)
		void Register_UnityEngineInternal_Input_NativeInputSystem_set_normalizeScrollWheelDelta();
		Register_UnityEngineInternal_Input_NativeInputSystem_set_normalizeScrollWheelDelta();

	//End Registrations for type : UnityEngineInternal.Input.NativeInputSystem

}

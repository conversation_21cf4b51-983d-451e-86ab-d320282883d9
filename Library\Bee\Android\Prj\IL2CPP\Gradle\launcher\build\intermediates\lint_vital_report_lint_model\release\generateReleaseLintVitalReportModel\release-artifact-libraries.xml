<libraries>
  <library
      name=":@@:unityLibrary::release"
      project=":unityLibrary"/>
  <library
      name="__local_aars__:D:\My Project\Driving Simulator Game Z TEC\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\libs\unity-classes.jar:unspecified@jar"
      jars="D:\My Project\Driving Simulator Game Z TEC\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\libs\unity-classes.jar"
      resolved="__local_aars__:D:\My Project\Driving Simulator Game Z TEC\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\libs\unity-classes.jar:unspecified"/>
  <library
      name="androidx.constraintlayout:constraintlayout:2.1.4@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\87addd2f9ef0ee0ee44e96c1653f474c\transformed\constraintlayout-2.1.4\jars\classes.jar"
      resolved="androidx.constraintlayout:constraintlayout:2.1.4"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\87addd2f9ef0ee0ee44e96c1653f474c\transformed\constraintlayout-2.1.4"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\54c0f2eccff70b60327a003626be6751\transformed\jetified-appcompat-resources-1.6.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\54c0f2eccff70b60327a003626be6751\transformed\jetified-appcompat-resources-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\84aca059a4004f02173e8cda25f28e03\transformed\appcompat-1.6.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\84aca059a4004f02173e8cda25f28e03\transformed\appcompat-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.3.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\707b970f905cba1fe2bdfd46cf720eb2\transformed\fragment-1.3.6\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.3.6"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\707b970f905cba1fe2bdfd46cf720eb2\transformed\fragment-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.6.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\c6937f02c4ce4ebe37bf6ec16ca201bb\transformed\jetified-activity-1.6.0\jars\classes.jar"
      resolved="androidx.activity:activity:1.6.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\c6937f02c4ce4ebe37bf6ec16ca201bb\transformed\jetified-activity-1.6.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\5144baee0f27d1b8aa76fe70b8152c51\transformed\drawerlayout-1.0.0\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\5144baee0f27d1b8aa76fe70b8152c51\transformed\drawerlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2-views-helper:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\02cf7af6750c838618c10ba1da360be7\transformed\jetified-emoji2-views-helper-1.2.0\jars\classes.jar"
      resolved="androidx.emoji2:emoji2-views-helper:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\02cf7af6750c838618c10ba1da360be7\transformed\jetified-emoji2-views-helper-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.emoji2:emoji2:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\e60bf0ab8a1217670124be7e52d524ab\transformed\jetified-emoji2-1.2.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\transforms-3\e60bf0ab8a1217670124be7e52d524ab\transformed\jetified-emoji2-1.2.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\e60bf0ab8a1217670124be7e52d524ab\transformed\jetified-emoji2-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\d7f56ef502d3e707b7dacad6a73ecb5b\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\d7f56ef502d3e707b7dacad6a73ecb5b\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\13da52d0bac11d06edd43ed7c52899b1\transformed\lifecycle-viewmodel-2.5.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.5.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\13da52d0bac11d06edd43ed7c52899b1\transformed\lifecycle-viewmodel-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\b5b61e68606215eb0bb8405dab104f00\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\b5b61e68606215eb0bb8405dab104f00\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\487c782739117da342dc717e0b18b42c\transformed\jetified-core-ktx-1.9.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\487c782739117da342dc717e0b18b42c\transformed\jetified-core-ktx-1.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\6b6cb48cfd30d4cb27a314c4879233f7\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\6b6cb48cfd30d4cb27a314c4879233f7\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\45430eae659bbf14711a5dcec7aa2a82\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\45430eae659bbf14711a5dcec7aa2a82\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\0574e832f76940327917dce8e1778a2a\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\0574e832f76940327917dce8e1778a2a\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\76611d920500b973cccb4d152c00ef14\transformed\customview-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\76611d920500b973cccb4d152c00ef14\transformed\customview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\c919e84cf21a5073ea88f45ff1a29b85\transformed\core-1.9.0\jars\classes.jar"
      resolved="androidx.core:core:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\c919e84cf21a5073ea88f45ff1a29b85\transformed\core-1.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.games:games-activity:3.0.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\4533d298259fc52a43021fce53f5e4a9\transformed\jetified-games-activity-3.0.5\jars\classes.jar"
      resolved="androidx.games:games-activity:3.0.5"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\4533d298259fc52a43021fce53f5e4a9\transformed\jetified-games-activity-3.0.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.games:games-frame-pacing:1.10.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\268849a49ea9eb2bba6f4e0ac95bfd63\transformed\jetified-games-frame-pacing-1.10.0\jars\classes.jar"
      resolved="androidx.games:games-frame-pacing:1.10.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\268849a49ea9eb2bba6f4e0ac95bfd63\transformed\jetified-games-frame-pacing-1.10.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\dcd0115cb306f25300a8dec488daa2cb\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\dcd0115cb306f25300a8dec488daa2cb\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\ca632e50827689b4d45330738d8735e2\transformed\jetified-lifecycle-process-2.4.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.4.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\ca632e50827689b4d45330738d8735e2\transformed\jetified-lifecycle-process-2.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\27942ce17933cf6f7beb206e8b90d6a9\transformed\lifecycle-runtime-2.5.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.5.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\27942ce17933cf6f7beb206e8b90d6a9\transformed\lifecycle-runtime-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.resourceinspection:resourceinspection-annotation:1.0.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.resourceinspection\resourceinspection-annotation\1.0.1\8c21f8ff5d96d5d52c948707f7e4d6ca6773feef\resourceinspection-annotation-1.0.1.jar"
      resolved="androidx.resourceinspection:resourceinspection-annotation:1.0.1"/>
  <library
      name="androidx.savedstate:savedstate:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\d36609a5e22262209ba9a6f854e3617a\transformed\jetified-savedstate-1.2.0\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\d36609a5e22262209ba9a6f854e3617a\transformed\jetified-savedstate-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.0.0\c1e77e3ee6f4643b77496a1ddf7a2eef1aefdaa1\concurrent-futures-1.0.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.0.0"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\7f5f88422e856bdc432c06a6c434fdca\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\7f5f88422e856bdc432c06a6c434fdca\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\f98f388a29837a9685a9f8b5cf1e3c6c\transformed\jetified-startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\f98f388a29837a9685a9f8b5cf1e3c6c\transformed\jetified-startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\9402879b5194b9b5315868de08898505\transformed\jetified-tracing-1.0.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\9402879b5194b9b5315868de08898505\transformed\jetified-tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection\1.1.0\1f27220b47669781457de0d600849a5de0e89909\collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\274051d7e7bcc37b57bc95fe96b47d29\transformed\lifecycle-livedata-2.0.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\274051d7e7bcc37b57bc95fe96b47d29\transformed\lifecycle-livedata-2.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\f73f2b7795a3e3d38f16e2ca7ea0d2d9\transformed\lifecycle-livedata-core-2.5.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.5.1"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\f73f2b7795a3e3d38f16e2ca7ea0d2d9\transformed\lifecycle-livedata-core-2.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-runtime:2.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\3d121897c12f1c2e0c3ce31a7236b976\transformed\core-runtime-2.1.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.1.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\3d121897c12f1c2e0c3ce31a7236b976\transformed\core-runtime-2.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.1.0\b3152fc64428c9354344bd89848ecddc09b6f07e\core-common-2.1.0.jar"
      resolved="androidx.arch.core:core-common:2.1.0"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.5.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.5.1\1fdb7349701e9cf2f0a69fc10642b6fef6bb3e12\lifecycle-common-2.5.1.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.5.1"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\babee2642c585096c98e632164904c6d\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\babee2642c585096c98e632164904c6d\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation:1.3.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation\1.3.0\21f49f5f9b85fc49de712539f79123119740595\annotation-1.3.0.jar"
      resolved="androidx.annotation:annotation:1.3.0"/>
  <library
      name="androidx.annotation:annotation-experimental:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\transforms-3\e2b5bb5c8c98117d619938d8d7eab80a\transformed\jetified-annotation-experimental-1.3.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\transforms-3\e2b5bb5c8c98117d619938d8d7eab80a\transformed\jetified-annotation-experimental-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.6.1\97fd74ccf54a863d221956ffcd21835e168e2aaa\kotlinx-coroutines-core-jvm-1.6.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.6.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.6.1\4e61fcdcc508cbaa37c4a284a50205d7c7767e37\kotlinx-coroutines-android-1.6.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.1"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.6.0\baf82c475e9372c25407f3d132439e4aa803b8b8\kotlin-stdlib-jdk8-1.6.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.6.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.6.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.6.0\da6bdc87391322974a43ccc00a25536ae74dad51\kotlin-stdlib-jdk7-1.6.0.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.6.0"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.7.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.7.10\d2abf9e77736acc4450dc4a3f707fa2c10f5099d\kotlin-stdlib-1.7.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.7.10"/>
  <library
      name="androidx.constraintlayout:constraintlayout-core:1.0.4@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.constraintlayout\constraintlayout-core\1.0.4\29cdbe03ded6b0980f63fa5da2579a430e911c40\constraintlayout-core-1.0.4.jar"
      resolved="androidx.constraintlayout:constraintlayout-core:1.0.4"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-common:1.7.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-common\1.7.10\bac80c520d0a9e3f3673bc2658c6ed02ef45a76a\kotlin-stdlib-common-1.7.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-common:1.7.10"/>
  <library
      name="org.jetbrains:annotations:13.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\13.0\919f0dfe192fb4e063e7dacadee7f8bb9a2672a9\annotations-13.0.jar"
      resolved="org.jetbrains:annotations:13.0"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\1.0\c949a840a6acbc5268d088e47b04177bf90b3cad\listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
</libraries>

Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.30f1 (62b05ba0686a) revision 6467675'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker8
-projectPath
D:/My Project/Driving Simulator Game Z TEC
-logFile
Logs/AssetImportWorker8.log
-srvPort
53742
-job-worker-count
11
-background-job-worker-count
8
-asset-garbage-collectors
1
Successfully changed project path to: D:/My Project/Driving Simulator Game Z TEC
D:/My Project/Driving Simulator Game Z TEC
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [33608]  Target information:

Player connection [33608]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1672350996 [EditorId] 1672350996 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [33608]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1672350996 [EditorId] 1672350996 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [33608] Host joined multi-casting on [***********:54997]...
Player connection [33608] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 11
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.97 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.30f1 (62b05ba0686a)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Driving Simulator Game Z TEC/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56604
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.011620 seconds.
- Loaded All Assemblies, in  1.001 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 552 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.469 seconds
Domain Reload Profiling: 2465ms
	BeginReloadAssembly (319ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (112ms)
	RebuildNativeTypeToScriptingClass (41ms)
	initialDomainReloadingComplete (143ms)
	LoadAllAssembliesAndSetupDomain (380ms)
		LoadAssemblies (313ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (372ms)
			TypeCache.Refresh (369ms)
				TypeCache.ScanAssembly (338ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1470ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1344ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (782ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (147ms)
			ProcessInitializeOnLoadAttributes (280ms)
			ProcessInitializeOnLoadMethodAttributes (127ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.984 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.20 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.365 seconds
Domain Reload Profiling: 3343ms
	BeginReloadAssembly (481ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (73ms)
	RebuildCommonClasses (103ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (93ms)
	LoadAllAssembliesAndSetupDomain (1276ms)
		LoadAssemblies (913ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (661ms)
			TypeCache.Refresh (540ms)
				TypeCache.ScanAssembly (493ms)
			BuildScriptInfoCaches (100ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1366ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1071ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (23ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (193ms)
			ProcessInitializeOnLoadAttributes (778ms)
			ProcessInitializeOnLoadMethodAttributes (59ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 2.49 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 66 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4568 unused Assets / (1.8 MB). Loaded Objects now: 5129.
Memory consumption went from 117.6 MB to 115.9 MB.
Total: 16.180200 ms (FindLiveObjects: 1.009000 ms CreateObjectMapping: 0.527100 ms MarkObjects: 10.921000 ms  DeleteObjects: 3.718900 ms)

========================================================================
Received Import Request.
  Time since last request: 14584.920486 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Cable Car Rails/Models/CableCar_RailSegment_Collider.fbx
  artifactKey: Guid(ad621fdfc724a8049a1772f19455004c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Cable Car Rails/Models/CableCar_RailSegment_Collider.fbx using Guid(ad621fdfc724a8049a1772f19455004c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dc3b95b244585f669b32e45e7d8fed2f') in 0.353381 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/AdvancedHelicopterController/Prefabs/Collectables/Collectable_Gas.prefab
  artifactKey: Guid(919a15be9197c4fe78500ac3df125f1c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Prefabs/Collectables/Collectable_Gas.prefab using Guid(919a15be9197c4fe78500ac3df125f1c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6cd659bda0f0badb8a803b9fc3d97189') in 0.3633619 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/Models/Cargo_01_Gravel.fbx
  artifactKey: Guid(277789986c94f2048a76fb4cf0f0aaff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/Models/Cargo_01_Gravel.fbx using Guid(277789986c94f2048a76fb4cf0f0aaff) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'da7b3cbb6ebf232af35091314385878b') in 0.2340142 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/_Prefabs/CargoWagon02_Brown_Gravel.prefab
  artifactKey: Guid(9cbfaf819e10df3439e727dda443e889) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/_Prefabs/CargoWagon02_Brown_Gravel.prefab using Guid(9cbfaf819e10df3439e727dda443e889) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '91bbb5534e910a50b600b462f4b3f7b4') in 0.8942025 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 255

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Controls/Control_Panel.prefab
  artifactKey: Guid(35976a221d818ae42b5f60ee8037d3e6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Controls/Control_Panel.prefab using Guid(35976a221d818ae42b5f60ee8037d3e6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4abceb19c7895b4ac7ac8fca31f19ecc') in 0.2594692 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 221

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/Invector-3rdPersonController_LITE/3D Models/Others/NewAssets/door.prefab
  artifactKey: Guid(3fef0d0a73e72b543b52178ed33c4ccc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Invector-3rdPersonController_LITE/3D Models/Others/NewAssets/door.prefab using Guid(3fef0d0a73e72b543b52178ed33c4ccc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f2dc329e2e937863bae099e6e96c6311') in 0.167884 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/UnityTechnologies/EffectExamples/Misc Effects/Prefabs/ElectricalSparksEffect.prefab
  artifactKey: Guid(96b2ec0239d11fa4b9792311efceb8ed) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/Misc Effects/Prefabs/ElectricalSparksEffect.prefab using Guid(96b2ec0239d11fa4b9792311efceb8ed) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7b17f6d6be7abcfe3c8474138c69b14e') in 0.0591821 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/AdvancedHelicopterController/Prefabs/Enemy/Enemy.prefab
  artifactKey: Guid(30617dda6a36e4c7aaad66c17cb3d1ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Prefabs/Enemy/Enemy.prefab using Guid(30617dda6a36e4c7aaad66c17cb3d1ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '615be580ee174aefe33ab78c35adce9b') in 0.1590014 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 143

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/AdvancedHelicopterController/Prefabs/Environment/Environment.prefab
  artifactKey: Guid(dece1b43a486e4d759e694c397fa6f67) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Prefabs/Environment/Environment.prefab using Guid(dece1b43a486e4d759e694c397fa6f67) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '50a5a90debc3c43277e5148548521fc4') in 0.6874458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9499

========================================================================
Received Import Request.
  Time since last request: 0.000060 seconds.
  path: Assets/AdvancedHelicopterController/Prefabs/Manager/GameManager.prefab
  artifactKey: Guid(3a2ca99e31fac4b1993fff0490270211) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Prefabs/Manager/GameManager.prefab using Guid(3a2ca99e31fac4b1993fff0490270211) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bb6a1bf2cc1425df0756954fb2e6ce1b') in 0.0042369 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/UnityTechnologies/EffectExamples/Shared/Models/FPSWeapon.fbx
  artifactKey: Guid(47509307932d1e140a2b4d9024d228e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/Shared/Models/FPSWeapon.fbx using Guid(47509307932d1e140a2b4d9024d228e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '287464bf7d5d49abb095138d42907f4a') in 0.0747471 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/UnityTechnologies/EffectExamples/Shared/Prefabs/Flesh.prefab
  artifactKey: Guid(a7034287e2bd6a141ab896f71d0b1357) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/Shared/Prefabs/Flesh.prefab using Guid(a7034287e2bd6a141ab896f71d0b1357) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '54942b688cd1615b55139b2f25d4ca4c') in 0.0486181 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/AdvancedHelicopterController/Prefabs/UI/Helicopter_CocpitView.prefab
  artifactKey: Guid(f5d84487d224744b5a89d6feca133c7a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Prefabs/UI/Helicopter_CocpitView.prefab using Guid(f5d84487d224744b5a89d6feca133c7a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e49c150d0989cc8220eb659809d679b5') in 0.0608375 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 51

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/Invector-3rdPersonController_LITE/3D Models/Others/NewAssets/Icosphere.prefab
  artifactKey: Guid(ecf8c4fb42504754cba503641e324366) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Invector-3rdPersonController_LITE/3D Models/Others/NewAssets/Icosphere.prefab using Guid(ecf8c4fb42504754cba503641e324366) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e02ab5742b0737d7752f2ac0f34caa4a') in 0.1121751 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/AdvancedHelicopterController/Prefabs/UI/GameCanvas.prefab
  artifactKey: Guid(7a801fd3ffa3049db8efdc86baa92d53) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Prefabs/UI/GameCanvas.prefab using Guid(7a801fd3ffa3049db8efdc86baa92d53) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '30317e65e48ec3bb17169c7d2e2f3af1') in 0.025588 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 205

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/UnityTechnologies/EffectExamples/WaterEffects/Models/ImpactSplash.FBX
  artifactKey: Guid(8f8a4cd20bdf7f845ab3768cae8aceed) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/WaterEffects/Models/ImpactSplash.FBX using Guid(8f8a4cd20bdf7f845ab3768cae8aceed) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '54e1dd4b68c5de462c6f51d55063bbed') in 0.0775548 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_ACW_7_prefab.prefab
  artifactKey: Guid(ea08227346e7c7d4ab8064c4cbbd3574) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_ACW_7_prefab.prefab using Guid(ea08227346e7c7d4ab8064c4cbbd3574) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e61b8f7e731a1e6dee9d25ec2309a0e9') in 0.3935158 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 64

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/MSK 2.2/Perfabs/Perfabs/Motorbike1.prefab
  artifactKey: Guid(4edd712ab121be4478332281d718a598) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MSK 2.2/Perfabs/Perfabs/Motorbike1.prefab using Guid(4edd712ab121be4478332281d718a598) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 1.35 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: '057a7122a7f38e08ec116539b4b6ffcf') in 1.0558331 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 304

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/AdvancedHelicopterController/Models/House/Model_House.fbx
  artifactKey: Guid(29b880764f1d74b22872545bbc7a3f08) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Models/House/Model_House.fbx using Guid(29b880764f1d74b22872545bbc7a3f08) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b6b6a8a5fa1ad0f20695a960302d1766') in 1.4168406 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 827

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/UnityTechnologies/EffectExamples/WaterEffects/Prefabs/StormCloudEffect.prefab
  artifactKey: Guid(ca55dd9d7d291014a8b13aaf95318a3a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/WaterEffects/Prefabs/StormCloudEffect.prefab using Guid(ca55dd9d7d291014a8b13aaf95318a3a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5f72aab432ee0f13f42b91cd96d85441') in 0.2481541 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 20

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_CW_4_prefab.prefab
  artifactKey: Guid(d96b42f22fd125d46839713f8abe2e3e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_CW_4_prefab.prefab using Guid(d96b42f22fd125d46839713f8abe2e3e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '54a6e8b04168b231135a79f1afe90e6a') in 0.5256281 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 64

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/Colliders/SuspensionCollider_Small.prefab
  artifactKey: Guid(930aba7a642a34b4e853b6e0d56712b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/Colliders/SuspensionCollider_Small.prefab using Guid(930aba7a642a34b4e853b6e0d56712b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ae657adae02d921e57c01ead46273a91') in 0.0031726 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Monitor_01_Large.fbx
  artifactKey: Guid(bbb6c7a64b0a3594bb7ec9a0ce161ff1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Monitor_01_Large.fbx using Guid(bbb6c7a64b0a3594bb7ec9a0ce161ff1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9bf7e02787081c5af4be0ce262f1084c') in 0.1194714 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Models/Stopblock_01_low.fbx
  artifactKey: Guid(c39a7cdea27fc46418ac24f10fd6f073) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Models/Stopblock_01_low.fbx using Guid(c39a7cdea27fc46418ac24f10fd6f073) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2ef9b0f0bad7918d0f33ff1c25763bb6') in 0.102044 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Subway_01_Door_Double_Right.fbx
  artifactKey: Guid(5d74f6012f0c5b24ab4deb7242e6437f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Subway_01_Door_Double_Right.fbx using Guid(5d74f6012f0c5b24ab4deb7242e6437f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aa963010d2371c99c9147d97c6460160') in 0.1169438 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Sinalization/StopblockStartingCap.prefab
  artifactKey: Guid(4b4d6c4522f08d54b8217b93e020b85f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Sinalization/StopblockStartingCap.prefab using Guid(4b4d6c4522f08d54b8217b93e020b85f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd217a30233825a7b518fac0f2a973299') in 0.1120414 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 47

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/Models/TenderWagon_01_LOD1.fbx
  artifactKey: Guid(abb6f8739a737494bbf64155d01e2238) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/Models/TenderWagon_01_LOD1.fbx using Guid(abb6f8739a737494bbf64155d01e2238) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4691e7419c338be22c079d56fb7ecabe') in 2.6865928 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000072 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Locomotive_01/Locomotive_01_LOD_0.fbx
  artifactKey: Guid(f9e6c7704e5182c4d9f4214cd958766f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Locomotive_01/Locomotive_01_LOD_0.fbx using Guid(f9e6c7704e5182c4d9f4214cd958766f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '37c051622a70307805ecd4341ca13932') in 2.6200816 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000063 seconds.
  path: Assets/AdvancedHelicopterController/Prefabs/Environment/Stone.prefab
  artifactKey: Guid(15c76898e53484f1c878f40837709f59) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Prefabs/Environment/Stone.prefab using Guid(15c76898e53484f1c878f40837709f59) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '971f1441904305e8b5877a9b29563b55') in 0.1660671 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/MSK 2.2/Models/Motorbikes/Motorbike (Cross)/motorbike2.FBX
  artifactKey: Guid(efdda8706221d314aa634631062c73a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MSK 2.2/Models/Motorbikes/Motorbike (Cross)/motorbike2.FBX using Guid(efdda8706221d314aa634631062c73a4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a3472723a67854086f62831167b92c28') in 0.231747 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 48

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Subway_01_Window_Large.fbx
  artifactKey: Guid(7515f859f06a3dd489659306019d218d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Subway_01_Window_Large.fbx using Guid(7515f859f06a3dd489659306019d218d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8fdb13e0ea36c82e6f47141e28d0c5b6') in 0.1329494 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/AdvancedHelicopterController/Prefabs/Bullets/Particles/Particle_Missile_Explosion.prefab
  artifactKey: Guid(515f3cd3ad9c041ab8cf0468d5c83d90) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Prefabs/Bullets/Particles/Particle_Missile_Explosion.prefab using Guid(515f3cd3ad9c041ab8cf0468d5c83d90) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6b99e65af2333e33112f85104f63af49') in 0.0410726 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 40

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/AdvancedHelicopterController/Models/Enemy/Model_EnemyVehicle.obj
  artifactKey: Guid(171853532ce244635893c5c5c1906d65) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Models/Enemy/Model_EnemyVehicle.obj using Guid(171853532ce244635893c5c5c1906d65) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '26a8ce933086dac2e0065364095c5219') in 0.3520113 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 58

========================================================================
Received Import Request.
  Time since last request: 0.000076 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Subway_01_Glass_Doors.fbx
  artifactKey: Guid(4d836b643eefb244daa4bba53301dc9f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Subway_01_Glass_Doors.fbx using Guid(4d836b643eefb244daa4bba53301dc9f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '04a98aa6bd72e97c839e0a4fa708a9b4') in 0.4398204 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/Colliders/SuspensionCollider_Big.prefab
  artifactKey: Guid(ec8983e26717a1b42849966cd7026f2b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/Colliders/SuspensionCollider_Big.prefab using Guid(ec8983e26717a1b42849966cd7026f2b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9e02136b9db43eb8aa252618cafea859') in 0.0046481 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/UnityTechnologies/EffectExamples/WeaponEffects/Prefabs/MuzzleFlashEffect.prefab
  artifactKey: Guid(9defeac88977c0d448aa4541073ee9e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/WeaponEffects/Prefabs/MuzzleFlashEffect.prefab using Guid(9defeac88977c0d448aa4541073ee9e4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'daa7ec62f9a6fc2fdac7dbac3b6126a2') in 0.0615567 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000079 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/Models/SteamLocomotive_01_WheelsTruck.fbx
  artifactKey: Guid(c4ee5b66a52b35a40bf87f3b9d9152b7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/Models/SteamLocomotive_01_WheelsTruck.fbx using Guid(c4ee5b66a52b35a40bf87f3b9d9152b7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '869de1012a474fbd2afd0830af9f7cd6') in 0.450744 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/MSK 2.2/Perfabs/Perfabs/Main Camera.prefab
  artifactKey: Guid(e842ecb5084391947ad1433ce6598b92) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MSK 2.2/Perfabs/Perfabs/Main Camera.prefab using Guid(e842ecb5084391947ad1433ce6598b92) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bc4e7d0a5390f8ca46405b94cecbdcb5') in 0.0045362 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000151 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/Models/SteamLocomotive_01_Wheels_02.fbx
  artifactKey: Guid(48680a1ba75f89646b82a5feb278e369) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/Models/SteamLocomotive_01_Wheels_02.fbx using Guid(48680a1ba75f89646b82a5feb278e369) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '758191df477b46678cb5d8f033fc74ab') in 0.5210929 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/TenderWagon_01_Green Variant.prefab
  artifactKey: Guid(9464119c623f3634e96ade4d926e8e2b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/TenderWagon_01_Green Variant.prefab using Guid(9464119c623f3634e96ade4d926e8e2b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '802c100c5894338e9ff180f13e5ab6d7') in 3.9975703 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 479

========================================================================
Received Import Request.
  Time since last request: 42.683167 seconds.
  path: Assets/UnityTechnologies/EffectExamples/WeaponEffects/Prefabs/RocketTrailEffect.prefab
  artifactKey: Guid(20c0f7e297297fc468f9740f6bb08cb1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/WeaponEffects/Prefabs/RocketTrailEffect.prefab using Guid(20c0f7e297297fc468f9740f6bb08cb1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b3ec3e5e03604809254ce66465d153bb') in 0.5966177 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Deprecated/TrainJoint_Front.fbx
  artifactKey: Guid(1d163e7a0551e4b4cba7d2651d4c4fed) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Deprecated/TrainJoint_Front.fbx using Guid(1d163e7a0551e4b4cba7d2651d4c4fed) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a0ae10fa5805ce9778129986ea34b883') in 0.1539051 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/Couplers/TrainJoint_Front.prefab
  artifactKey: Guid(eebf1f0d4b2739d41b960a6e2e320117) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/Couplers/TrainJoint_Front.prefab using Guid(eebf1f0d4b2739d41b960a6e2e320117) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '78a654f71d919ef2ac4c3fe140b6d93e') in 0.0952539 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/TrainWheels.fbx
  artifactKey: Guid(6087c874647d43949acaf9f176701273) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/TrainWheels.fbx using Guid(6087c874647d43949acaf9f176701273) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '77217f74d3fdbbe3773778bb8a29b5aa') in 0.1328491 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/Bumpers/TrainBumper_Small.prefab
  artifactKey: Guid(9b092a1dfc453de459e6111a8a3249b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/Bumpers/TrainBumper_Small.prefab using Guid(9b092a1dfc453de459e6111a8a3249b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '13399db999cd8ecb2de0f13859432d4e') in 0.130194 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000068 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Parts/Wheels/SteamLocomotive_01_RearWheelsTruck_visual.prefab
  artifactKey: Guid(f7396cac421c86f4a91265c12de181fa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Parts/Wheels/SteamLocomotive_01_RearWheelsTruck_visual.prefab using Guid(f7396cac421c86f4a91265c12de181fa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ad71ae2a9fa3c092e8e8aa02bf06f39d') in 3.61251 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 84

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Sample Trains/Train_Sample_2.prefab
  artifactKey: Guid(41d506e3e24722f4bbb74c8edf6ce71c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Sample Trains/Train_Sample_2.prefab using Guid(41d506e3e24722f4bbb74c8edf6ce71c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9b2cb926417968e28bcaab87cd671eca') in 4.197706 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2054

========================================================================
Received Import Request.
  Time since last request: 0.000090 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Colliders/Train_Collider.fbx
  artifactKey: Guid(2b5917b3052b186448390fb1c1468a08) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Colliders/Train_Collider.fbx using Guid(2b5917b3052b186448390fb1c1468a08) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dff41866d6c8b60a1c45c0951c1d0720') in 0.0365884 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Sample Trains/Train_Sample_1.prefab
  artifactKey: Guid(eaecaf513a67a1840b8b20523a2c6c29) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Sample Trains/Train_Sample_1.prefab using Guid(eaecaf513a67a1840b8b20523a2c6c29) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fcb6f8746280235905637540715b3da7') in 3.8817672 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2054

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0
{ "pid": 22768, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "<PERSON><PERSON>ink<PERSON>" } },
{ "pid": 22768, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 22768, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen2" } },
{ "pid": 22768, "tid": 12884901888, "ts": 1751353280736142, "dur": 271935, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "LOH allocation", "Type": "Background GC"} },
{ "pid": 22768, "tid": 1, "ts": 1751353281247534, "dur": 4732, "ph": "X", "name": "GC - Gen2", "args": {} },
{ "pid": 22768, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen1" } },
{ "pid": 22768, "tid": 1, "ts": 1751353281252269, "dur": 1, "ph": "X", "name": "GC - Gen1", "args": {} },
{ "pid": 22768, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen0" } },
{ "pid": 22768, "tid": 4294967296, "ts": 1751353278084810, "dur": 173158, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 22768, "tid": 1, "ts": 1751353281252271, "dur": 91, "ph": "X", "name": "GC - Gen0", "args": {} },
{ "pid": 22768, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 22768, "tid": 1, "ts": 1751353274921183, "dur": 6299670, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 22768, "tid": 1, "ts": 1751353274924270, "dur": 118080, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353274933454, "dur": 66125, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275042353, "dur": 20599, "ph": "X", "name": "RegisterRuntimeEventListeners", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275104220, "dur": 54773, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275162976, "dur": 186468, "ph": "X", "name": "SetupAndRegisterUnityEngineSteps", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275349527, "dur": 57983, "ph": "X", "name": "ResolveUnityEngine", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275407535, "dur": 67011, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275474565, "dur": 331435, "ph": "X", "name": "InitializeEngineStrippingStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275806033, "dur": 10555, "ph": "X", "name": "ResolveForEngineModuleStrippingEnabledStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275816595, "dur": 3032, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275819630, "dur": 6021, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275825654, "dur": 1429, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275827092, "dur": 22731, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275849830, "dur": 1415, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275851249, "dur": 821, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275852073, "dur": 1489, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275853565, "dur": 1830, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275855407, "dur": 10520, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275865940, "dur": 18112, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275884058, "dur": 11106, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275895168, "dur": 2700, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275897886, "dur": 3030, "ph": "X", "name": "UnityLoadReferencesStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275900924, "dur": 2017, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps2", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275902949, "dur": 1701, "ph": "X", "name": "SetupI18N", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275904658, "dur": 8156, "ph": "X", "name": "ResolveFromDescriptorsStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275912819, "dur": 200, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275913020, "dur": 257, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275913279, "dur": 17863, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275931146, "dur": 3866, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275935017, "dur": 1684, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275936721, "dur": 41990, "ph": "X", "name": "UnityBlacklistStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275978720, "dur": 14157, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353275992890, "dur": 247477, "ph": "X", "name": "DynamicDependencyLookupStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353276240397, "dur": 840, "ph": "X", "name": "EarlyReportGenerationStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353276241246, "dur": 1146, "ph": "X", "name": "ResolveTestsStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353276242402, "dur": 478, "ph": "X", "name": "LoadI18nAssemblies", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353276242891, "dur": 1725, "ph": "X", "name": "ResolveMonoBehaviourItselfStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353276244626, "dur": 1187, "ph": "X", "name": "ResolveFromAllUserMonoBehaviours", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353276245822, "dur": 8515, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353276254342, "dur": 533, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353276254884, "dur": 136231, "ph": "X", "name": "ResolveFromPreserveAttribute", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353276391136, "dur": 28776, "ph": "X", "name": "EngineStrippingAnnotationStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353276419934, "dur": 241207, "ph": "X", "name": "UnityTypeMapStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353276661160, "dur": 441, "ph": "X", "name": "BeforeMarkReportGenerationStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353276661608, "dur": 173786, "ph": "X", "name": "BeforeMarkAnalyticsStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353276835421, "dur": 46078, "ph": "X", "name": "RemoveSecurityStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353276881519, "dur": 7190, "ph": "X", "name": "RemoveSecurityFromCopyAssemblies", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353276888727, "dur": 32499, "ph": "X", "name": "RemoveFeaturesStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353276921256, "dur": 30265, "ph": "X", "name": "RemoveUnreachableBlocksStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353276951547, "dur": 2926137, "ph": "X", "name": "UnityMarkStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353279877712, "dur": 1502, "ph": "X", "name": "ValidateVirtualMethodAnnotationsStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353279879222, "dur": 10125, "ph": "X", "name": "ProcessWarningsStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353279889357, "dur": 112341, "ph": "X", "name": "UnitySweepStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353280001715, "dur": 6455, "ph": "X", "name": "UnityCodeRewriterStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353280008185, "dur": 7235, "ph": "X", "name": "CleanStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353280015435, "dur": 5179, "ph": "X", "name": "StubifyStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353280020624, "dur": 6016, "ph": "X", "name": "AddUnresolvedStubsStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353280026650, "dur": 7185, "ph": "X", "name": "BeforeOutputAnalyticsStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353280033844, "dur": 343, "ph": "X", "name": "BeforeOutputReportGenerationStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353280034196, "dur": 757, "ph": "X", "name": "ProcessCopiedAssembliesRequiringSecurityAttributesRemoved", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353280034962, "dur": 856, "ph": "X", "name": "SealerStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353280035834, "dur": 1091317, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353281127179, "dur": 42574, "ph": "X", "name": "LinkerToEditorDataGenerationStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353281169769, "dur": 398, "ph": "X", "name": "ReportGenerationStep", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353281175010, "dur": 45535, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353281220856, "dur": 13490, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353281252364, "dur": 208, "ph": "X", "name": "", "args": {} },
{ "pid": 22768, "tid": 1, "ts": 1751353281246564, "dur": 6640, "ph": "X", "name": "Write chrome-trace events", "args": {} },

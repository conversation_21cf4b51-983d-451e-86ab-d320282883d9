{ "pid": 26156, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "<PERSON><PERSON>ink<PERSON>" } },
{ "pid": 26156, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 26156, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen2" } },
{ "pid": 26156, "tid": 12884901888, "ts": 1751348746207784, "dur": 276576, "ph": "X", "name": "GC - Gen 2", "args": {"Reason": "LOH allocation", "Type": "Background GC"} },
{ "pid": 26156, "tid": 1, "ts": 1751348747314892, "dur": 4966, "ph": "X", "name": "GC - Gen2", "args": {} },
{ "pid": 26156, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen1" } },
{ "pid": 26156, "tid": 1, "ts": 1751348747319861, "dur": 2, "ph": "X", "name": "GC - Gen1", "args": {} },
{ "pid": 26156, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "GC - Gen0" } },
{ "pid": 26156, "tid": 4294967296, "ts": 1751348743726821, "dur": 160578, "ph": "X", "name": "GC - Gen 0", "args": {"Reason": "SOH allocation", "Type": "Blocking GC"} },
{ "pid": 26156, "tid": 1, "ts": 1751348747319864, "dur": 96, "ph": "X", "name": "GC - Gen0", "args": {} },
{ "pid": 26156, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 26156, "tid": 1, "ts": 1751348740757479, "dur": 6529659, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 26156, "tid": 1, "ts": 1751348740760555, "dur": 116745, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348740769505, "dur": 65122, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348740877302, "dur": 20170, "ph": "X", "name": "RegisterRuntimeEventListeners", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348740938259, "dur": 53308, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348740995406, "dur": 179013, "ph": "X", "name": "SetupAndRegisterUnityEngineSteps", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741174485, "dur": 50848, "ph": "X", "name": "ResolveUnityEngine", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741225351, "dur": 58956, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741284321, "dur": 269597, "ph": "X", "name": "InitializeEngineStrippingStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741553931, "dur": 9231, "ph": "X", "name": "ResolveForEngineModuleStrippingEnabledStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741563167, "dur": 2743, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741565912, "dur": 5139, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741571054, "dur": 735, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741571796, "dur": 17587, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741589386, "dur": 872, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741590260, "dur": 505, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741590767, "dur": 864, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741591633, "dur": 999, "ph": "X", "name": "ResolveAssemblyDirectoryStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741592640, "dur": 6985, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741599633, "dur": 16420, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741616056, "dur": 9181, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741625240, "dur": 2283, "ph": "X", "name": "ResolveFromAssemblyStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741627532, "dur": 2729, "ph": "X", "name": "UnityLoadReferencesStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741630267, "dur": 1830, "ph": "X", "name": "SetupAndRegisterUnityRootsSteps2", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741632105, "dur": 1797, "ph": "X", "name": "SetupI18N", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741633917, "dur": 7644, "ph": "X", "name": "ResolveFromDescriptorsStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741641565, "dur": 156, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741641723, "dur": 207, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741641932, "dur": 16529, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741658463, "dur": 3550, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741662015, "dur": 1657, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741663680, "dur": 39882, "ph": "X", "name": "UnityBlacklistStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741703565, "dur": 13129, "ph": "X", "name": "UnityResolveFromXmlStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741716702, "dur": 253641, "ph": "X", "name": "DynamicDependencyLookupStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741970373, "dur": 869, "ph": "X", "name": "EarlyReportGenerationStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741971261, "dur": 1259, "ph": "X", "name": "ResolveTestsStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741972532, "dur": 551, "ph": "X", "name": "LoadI18nAssemblies", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741973094, "dur": 1925, "ph": "X", "name": "ResolveMonoBehaviourItselfStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741975031, "dur": 1319, "ph": "X", "name": "ResolveFromAllUserMonoBehaviours", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741976361, "dur": 9718, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741986084, "dur": 564, "ph": "X", "name": "ResolveFromMonoBehaviours", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348741986660, "dur": 143521, "ph": "X", "name": "ResolveFromPreserveAttribute", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348742130201, "dur": 32179, "ph": "X", "name": "EngineStrippingAnnotationStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348742162419, "dur": 262397, "ph": "X", "name": "UnityTypeMapStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348742424837, "dur": 478, "ph": "X", "name": "BeforeMarkReportGenerationStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348742425324, "dur": 166407, "ph": "X", "name": "BeforeMarkAnalyticsStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348742591748, "dur": 43798, "ph": "X", "name": "RemoveSecurityStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348742635566, "dur": 7288, "ph": "X", "name": "RemoveSecurityFromCopyAssemblies", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348742642864, "dur": 30483, "ph": "X", "name": "RemoveFeaturesStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348742673399, "dur": 28286, "ph": "X", "name": "RemoveUnreachableBlocksStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348742701707, "dur": 2715279, "ph": "X", "name": "UnityMarkStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348745417011, "dur": 1503, "ph": "X", "name": "ValidateVirtualMethodAnnotationsStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348745418522, "dur": 10063, "ph": "X", "name": "ProcessWarningsStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348745428593, "dur": 102396, "ph": "X", "name": "UnitySweepStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348745531005, "dur": 5964, "ph": "X", "name": "UnityCodeRewriterStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348745536978, "dur": 7541, "ph": "X", "name": "CleanStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348745544533, "dur": 4812, "ph": "X", "name": "StubifyStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348745549353, "dur": 5473, "ph": "X", "name": "AddUnresolvedStubsStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348745554834, "dur": 6664, "ph": "X", "name": "BeforeOutputAnalyticsStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348745561507, "dur": 356, "ph": "X", "name": "BeforeOutputReportGenerationStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348745561871, "dur": 672, "ph": "X", "name": "ProcessCopiedAssembliesRequiringSecurityAttributesRemoved", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348745562551, "dur": 860, "ph": "X", "name": "SealerStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348745563440, "dur": 1623622, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348747187090, "dur": 44485, "ph": "X", "name": "LinkerToEditorDataGenerationStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348747231588, "dur": 423, "ph": "X", "name": "ReportGenerationStep", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348747236755, "dur": 50042, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348747287141, "dur": 14506, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348747319962, "dur": 208, "ph": "X", "name": "", "args": {} },
{ "pid": 26156, "tid": 1, "ts": 1751348747313871, "dur": 6981, "ph": "X", "name": "Write chrome-trace events", "args": {} },

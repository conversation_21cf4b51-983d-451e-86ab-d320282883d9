%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &6858477438835373524
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1557536155345784938}
  - component: {fileID: 4472390660611109663}
  - component: {fileID: 5821973734564209136}
  - component: {fileID: 4756273002540648780}
  m_Layer: 0
  m_Name: Drone camera
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1557536155345784938
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6858477438835373524}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 91.28774, y: 31.6, z: -20.848618}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!20 &4472390660611109663
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6858477438835373524}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.3
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!81 &5821973734564209136
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6858477438835373524}
  m_Enabled: 1
--- !u!114 &4756273002540648780
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6858477438835373524}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d68e38c24b855d04d8a9f397563bffb7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  joystick: {fileID: 0}
  Dronecameraa: {fileID: 4472390660611109663}
  upHeightButton: {fileID: 0}
  downHeightButton: {fileID: 0}
  horizontalSpeed: 10
  verticalSpeed: 10
  heightSpeed: 3
  smoothTime: 0.1
  limitHorizontalMovement: 0
  minX: -50
  maxX: 50
  limitVerticalMovement: 0
  minZ: -50
  maxZ: 50
  limitHeightMovement: 1
  minY: 0.5
  maxY: 50
  moveRelativeToRotation: 1
  enableMouseDragRotation: 1
  mouseSensitivity: 2
  touchSensitivity: 1.5
  rotationSmoothTime: 0.1
  useUIExclusionZones: 1
  bottomUIZone: 0.297
  topUIZone: 0.094
  leftUIZone: 0.3
  rightUIZone: 0.3
  limitVerticalRotation: 1
  minVerticalAngle: 0
  maxVerticalAngle: 0
  limitHorizontalRotation: 0
  minHorizontalAngle: -180
  maxHorizontalAngle: 180

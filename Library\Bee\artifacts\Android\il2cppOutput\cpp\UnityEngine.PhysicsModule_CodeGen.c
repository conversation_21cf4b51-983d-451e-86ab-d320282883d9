﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void Collision_set_Flipped_m5AF63260E99357BB87655DDAD6316568BE65F3A1 (void);
extern void Collision__ctor_mC3F14BC1026130B6B0E6BB83D7431753C3484912 (void);
extern void Collision__ctor_m6A02AD9B6F96A755B3A3A3A280CC7D2533228DA7 (void);
extern void Collision_Reuse_mC2E21A6480EE1DCEAF71F2EAF3E0CAEFD42EA90C (void);
extern void CapsuleCollider_get_center_mC12CE0A66A1104CEB7D23F39596D0E45578419C2 (void);
extern void CapsuleCollider_get_radius_m2462B43ECAC92386AAED85AA1DFD66440972D9D5 (void);
extern void CapsuleCollider_get_height_m63A31072F296AEE6222DC9C88704882BB6A54A24 (void);
extern void CapsuleCollider_get_center_Injected_m13CFD076EB23DD354C06A7D555B3C02265E1A39F (void);
extern void CapsuleCollider_get_radius_Injected_mFD3AB35080826BE2C362AAE718514CDA803F4F83 (void);
extern void CapsuleCollider_get_height_Injected_m31ED5345D908D193A2AB0947EBC50E901454FEF4 (void);
extern void Collider_get_isTrigger_mFF457F6AA71D173F9A11BAF00C35E5AE12952F87 (void);
extern void Collider_set_material_m2E6C7BEE5FE88DD839FD0BBA83DD501E38B85499 (void);
extern void Collider_get_isTrigger_Injected_mCA6BD3736CB8FC95F6BD3100CF978D4C4436F8DD (void);
extern void Collider_set_material_Injected_mE7116C7A74F7B0BFA011D5E2B21DF99E2798597F (void);
extern void Physics_OnSceneContactModify_m52106C18952BF4768B05F67FAF2B7F6F6F7D0C9D (void);
extern void Physics_PhysXOnSceneContactModify_m249A4E5A6A2967EA3CE7FD32ADD5851890D77AEE (void);
extern void Physics_get_invokeCollisionCallbacks_m73AE9C988EC57467D9B8699B376759D8E44133C8 (void);
extern void Physics_get_defaultPhysicsScene_mC5D2BC20734D32FB421163F066BD5FB4118C633A (void);
extern void Physics_Raycast_m453681A406AADE0A30227D955279F5E7050B790D (void);
extern void Physics_Raycast_m0679FB03C9AFC1E803B8F8AE6CAB409670D31377 (void);
extern void Physics_Raycast_mCFF84927BE3EC1780DBA34CCED374E7FF12ABCBE (void);
extern void Physics_Raycast_mCAA46C95211C7BB95697A347B036C012D26EB028 (void);
extern void Physics_Raycast_mA782767AD4F149FBEA32C71460DFF061B7563688 (void);
extern void Physics_Raycast_m56120FFEF0D4F0A44CCA505B5C946E6FB8742F12 (void);
extern void Physics_Raycast_m011EA7022C33B2C499EF744E5AF3E01EEB8FBD33 (void);
extern void Physics_Raycast_m1B27F500505FFB57D78548B9F5A540A2AD092903 (void);
extern void Physics_Raycast_m9879C28DFF6CD3048F2365BC01C855565EE141F8 (void);
extern void Physics_Raycast_m5CAA0AEDB2A6FB26E5F42A8EA560A61CAAF12E50 (void);
extern void Physics_Raycast_m7A0FEA813B93A82713C06D8466F0A21325743488 (void);
extern void Physics_Raycast_mDB89EB287ED040E534F6A933683A070D29DC14D3 (void);
extern void Physics_Raycast_mCCD2542138D11E665A5D4F413C1547EE7D794DEB (void);
extern void Physics_Raycast_m34AC1210E893A9EF969BD2C7104B10BE5B580025 (void);
extern void Physics_Raycast_m839BA104A76B928A03F075C622923C6FCD2F8685 (void);
extern void Physics_Raycast_mCAC9F02A1AAB49E16B384EBC8318E2DF30F4B0E5 (void);
extern void Physics_Linecast_m399C6C11AD7ECE11241A37C08BAB4D97CF3CB925 (void);
extern void Physics_Linecast_mF9E3896E84ACD675E71363ADE30A8418C14C59C6 (void);
extern void Physics_SphereCast_mDB2140FE8561D0CE870037527DACC44AB18A346D (void);
extern void Physics_SphereCast_m2A41FD7023EC5B89B69E0A8948325BEF46D9597C (void);
extern void Physics_SphereCast_mE7656F9355B33AED9095D8A0301734611EC95B05 (void);
extern void Physics_SphereCast_mF6538C6C4E3A9BBD81B686437CC91F3A93C1F3E7 (void);
extern void Physics_Internal_RaycastAll_mC128593FD48E6F237BE59CFCDC7DDE7A4E8CB074 (void);
extern void Physics_RaycastAll_m8B7FB8419A65BEE78927D0EE84916E8DBE7ECD34 (void);
extern void Physics_RaycastAll_m69ED0FF0B70ADBC45B907783C87B308E786F6D51 (void);
extern void Physics_RaycastAll_mDCBE530EF2ACD21EAADEA829259291D7327BC80E (void);
extern void Physics_RaycastAll_mE56962F670046BE618FFE8D9B19595A896922789 (void);
extern void Physics_RaycastAll_mD1643DB52C4E415083E215B154FEB9DFA3AD6D74 (void);
extern void Physics_RaycastAll_m4055619E0F7EFA04620EAA0517F8393C4EBCFE87 (void);
extern void Physics_RaycastAll_m1BBD4E474814BEC9B52B015081A256AE2FE00468 (void);
extern void Physics_RaycastAll_mE94864EF8243F7D3A26C8666CEB02166C3742CB2 (void);
extern void Physics_RaycastNonAlloc_mB37DE98E8C9407C3DB2FB488BAB1CF3A7C6FFFCE (void);
extern void Physics_RaycastNonAlloc_m2BFEE9072E390ED6ACD500FD0AE4E714DE9549BC (void);
extern void Physics_RaycastNonAlloc_m1908CB5E0D0570E9C88B6C259041520DD4D3169C (void);
extern void Physics_RaycastNonAlloc_m1961CFCDB7631C7FF4D12F88904CF1BEB24A6C3E (void);
extern void Physics_RaycastNonAlloc_mB8FE279E06CE87D77387AA9A10562B8052DC8836 (void);
extern void Physics_RaycastNonAlloc_m4CFAA8CA088502DA71D748D276BDAAEF234B12B0 (void);
extern void Physics_RaycastNonAlloc_m3EEB10539C49FEAD9533142FEE6578148A48FFA9 (void);
extern void Physics_RaycastNonAlloc_mBDC9E19F4E3C82DCE03D799FDD41FB3314209460 (void);
extern void Physics_Query_CapsuleCastAll_m874A33B3421181746FC7662648E0306620F7D522 (void);
extern void Physics_CapsuleCastAll_m367017D9CB85D5F7AA7448F70E16E94578C09214 (void);
extern void Physics_CapsuleCastAll_m9467728846F780021AF7D40168E7CA0D6A76F2AE (void);
extern void Physics_get_reuseCollisionCallbacks_mB23A11C02B893238B5631A38F2FBB4C63A3B7541 (void);
extern void Physics_GetColliderByInstanceID_m0318A1C3CEC5AC6B42AB1F541EC3EE8909712220 (void);
extern void Physics_GetBodyByInstanceID_mC45F93E518D6F1FC136DD3FB4377B3CC9F244725 (void);
extern void Physics_SendOnCollisionEnter_mA48BA0630EE3D28320C602A15B5BDD887FA24144 (void);
extern void Physics_SendOnCollisionStay_m2CC0293E4757CF89183A125270047A5054515590 (void);
extern void Physics_SendOnCollisionExit_m6E96913C80E49A77FD367FBA5EF63755A81AA7D7 (void);
extern void Physics_OnSceneContact_mFE8673EF13DD22B5C727BF5E2EFC4CF5E410C73A (void);
extern void Physics_ReportContacts_m9E7B0F448F534C8DEBBA6E33D86506C2500C919D (void);
extern void Physics_GetCollisionToReport_m7D22E5DD29678C65830A05A249650C49947A5D4E (void);
extern void Physics__cctor_m1E2D4816C77050D34F6D01F43E63D7969410AE2A (void);
extern void Physics_get_defaultPhysicsScene_Injected_mE86AE6A398435C1754A824B2B35DF13126A6C5D6 (void);
extern void Physics_Internal_RaycastAll_Injected_m0100C5BF359DDE45F69B973D243D5B3CC0132754 (void);
extern void Physics_Query_CapsuleCastAll_Injected_m509C782F8F40FFDEC8FB30DF7FB39E8E37FE3BF9 (void);
extern void Physics_GetColliderByInstanceID_Injected_m450D0994A6A8609B16027C585D392343F6211B8B (void);
extern void Physics_GetBodyByInstanceID_Injected_mB05F094BD1E39D3BB58ACFB771DA941EAB0B20D8 (void);
extern void Physics_SendOnCollisionEnter_Injected_m6E860F99570F9228DE3CD5D9AA7D8A87E189E960 (void);
extern void Physics_SendOnCollisionStay_Injected_m4300A15E1F6881C0191E3A407A9296662661951C (void);
extern void Physics_SendOnCollisionExit_Injected_m935DC6B1CA45455BE752CEB6F4C6C42FC9B72E71 (void);
extern void ContactEventDelegate__ctor_mF0844AC2AA36D48C199DDDBFA55627E43980CEEE (void);
extern void ContactEventDelegate_Invoke_m84BF3B9092BD4F6D43A870421F8389BC7B0E0769 (void);
extern void ContactPairHeader_get_body_m8B8DC4B023CB7078B425E3CE764CFF3088D37FC8 (void);
extern void ContactPairHeader_get_otherBody_m76BD1BDDE46A87839F214F741ECB0E0857EFAE0A (void);
extern void ContactPairHeader_get_hasRemovedBody_mE79EF42C5CB9FCDB476AC9437861F71CF7FC442A (void);
extern void ContactPairHeader_GetContactPair_m3DD517F464EDB35C4B0933854D95BE735DD2AC09 (void);
extern void ContactPairHeader_GetContactPair_Internal_mCED67397346C23F3ABC5063AFFCF1F099AF5FC27 (void);
extern void ContactPair_get_collider_mC74190F4919E0DAC5E8994C975BE36244900BD6C (void);
extern void ContactPair_get_otherCollider_mA99232AB090D27DCC1561094DC20621F3DA764A8 (void);
extern void ContactPair_get_isCollisionEnter_m901546CD819E7891F6594A72AAEAE72FCB5515B1 (void);
extern void ContactPair_get_isCollisionExit_mD54D019EABA4B209633B41803D6EE0CA1B978DF1 (void);
extern void ContactPair_get_isCollisionStay_m62946484C4BD866D9AD91B23FE1C18336048E5F8 (void);
extern void ContactPair_get_hasRemovedCollider_m3DFB259C3645FA874C7AD304494222472B5184AA (void);
extern void ContactPair_ExtractContactsArray_mB82D786FF9A04BC4B5A4C10EA5DC400AB6D655EC (void);
extern void ContactPair_GetContactPoint_mB31DB006460758A191A7D5CE7155523CFB62C454 (void);
extern void ContactPair_GetContactPoint_Internal_m121E9C831FE1A673C1D76E00B731D3F5D558E565 (void);
extern void ContactPairPoint_get_position_mD7D5690B579C6DF348EBBAD5523DAB1B90253C10 (void);
extern void ContactPairPoint_get_separation_m5DF7D504E1EC95B2E44D37EAF78C1DCEC49DA12E (void);
extern void ContactPairPoint_get_normal_m019987EB1D8883A212596E8BB418EB787050FECF (void);
extern void ContactPairPoint_get_impulse_m982A7672DBBC6C7D8636D0FC9D1A8D75CA313D1A (void);
extern void PhysicsMaterial__ctor_m43CF5D49C73297C52A4EFC327E7FD29137E75FC7 (void);
extern void PhysicsMaterial_Internal_CreateDynamicsMaterial_m6766ACF352256A5DEA2DF928958006AAA4136389 (void);
extern void PhysicsMaterial_set_dynamicFriction_m59BE25A0D523F1C16E8E5EA3605D5D9C068D6AAE (void);
extern void PhysicsMaterial_set_staticFriction_m67DE5F3AD796C8AB92871E3A0B7338862BB27B9B (void);
extern void PhysicsMaterial_set_frictionCombine_m5095E28E658737F90616372006D6FA093DA875DF (void);
extern void PhysicsMaterial_Internal_CreateDynamicsMaterial_Injected_m81FBE209E2CE0CC055F3D5CE695AD1B951EA2FF0 (void);
extern void PhysicsMaterial_set_dynamicFriction_Injected_mA206F4C8595D78CDD49B7502C085A671138B873E (void);
extern void PhysicsMaterial_set_staticFriction_Injected_mC15C61162DDC0DF3060AF13F0ABDD0E92203F1C6 (void);
extern void PhysicsMaterial_set_frictionCombine_Injected_mD50FF725DAC858C568C694790335C4AA09D4F66F (void);
extern void PhysicsScene_ToString_mA4E28A3068A823D16D96BBA45115A2C457FC57C7 (void);
extern void PhysicsScene_GetHashCode_m368888FB861F994FADEEDD281BD02B090C561814 (void);
extern void PhysicsScene_Equals_mE3A11329AB6C2F4F76D2321D8BAE52671A2EDDA3 (void);
extern void PhysicsScene_Equals_m81E4A78FC3644FDC44044B3A5F19F1C4283648A1 (void);
extern void PhysicsScene_Raycast_m68D255133E274C5DDF33102EAAE70990C2A0A730 (void);
extern void PhysicsScene_Internal_RaycastTest_m729F4A577F5DD911131C5321EC28E44F98A60BA0 (void);
extern void PhysicsScene_Raycast_m6EE0783D1B113CAD5450A2CB876F6CA305BAD2CE (void);
extern void PhysicsScene_Internal_Raycast_m0211A7BDE011181718838F063296D51F88D92E74 (void);
extern void PhysicsScene_Raycast_m3BD571CF6901C59C286D7B58ED9D15D836BC54C3 (void);
extern void PhysicsScene_Internal_RaycastNonAlloc_mC339255AAFC484588C813D7BE2BDAE03797D26DB (void);
extern void PhysicsScene_Query_SphereCast_m8E6770FE64FB74157199217381AA1A99B3CF580B (void);
extern void PhysicsScene_Internal_SphereCast_mE4B0FBE790E2A7309F7807F5F1EFB909D21E07BF (void);
extern void PhysicsScene_SphereCast_mEB124233FFEA3BD179C9DE22E410290D7EB247C4 (void);
extern void PhysicsScene_Internal_RaycastTest_Injected_m7633DAED691C6CFE296418FDBCE2E5E630456C62 (void);
extern void PhysicsScene_Internal_Raycast_Injected_m09A18038A5A35901A6825B805600525583FD404D (void);
extern void PhysicsScene_Internal_RaycastNonAlloc_Injected_m5C535BFE6635BE6E66150B9560A14A5922580934 (void);
extern void PhysicsScene_Query_SphereCast_Injected_m660DCB273A7D7AC02A4CACC69BBC38DF397E0D9A (void);
extern void RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D (void);
extern void RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39 (void);
extern void RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5 (void);
extern void RaycastHit_get_distance_m035194B0E9BB6229259CFC43B095A9C8E5011C78 (void);
extern void Rigidbody_get_linearVelocity_m367F5415641E5FB968440A91BAE2FCB8CAEA2C8C (void);
extern void Rigidbody_set_linearVelocity_m29AE03D5FC079EAD4202FCF72E2AEBDC19363985 (void);
extern void Rigidbody_get_position_m4ECB79BDBBF8FD1EA572EDB792D3330DDED24691 (void);
extern void Rigidbody_AddForce_mBDBC288D0E266BC1B62E3649B4FCE46E7EA9CCBC (void);
extern void Rigidbody_get_linearVelocity_Injected_m0230F76BD25355A9762C9C5E32C60BF8249F4F6D (void);
extern void Rigidbody_set_linearVelocity_Injected_mCAC97A87C8F0FAFA7007C394AF7974DD37073467 (void);
extern void Rigidbody_get_position_Injected_mD3AC6CDFE76252AAE5F4862BC7D18E0C479DDC8E (void);
extern void Rigidbody_AddForce_Injected_m0063728ED4AA2811701ED30CDFC9765816402152 (void);
static Il2CppMethodPointer s_methodPointers[136] = 
{
	Collision_set_Flipped_m5AF63260E99357BB87655DDAD6316568BE65F3A1,
	Collision__ctor_mC3F14BC1026130B6B0E6BB83D7431753C3484912,
	Collision__ctor_m6A02AD9B6F96A755B3A3A3A280CC7D2533228DA7,
	Collision_Reuse_mC2E21A6480EE1DCEAF71F2EAF3E0CAEFD42EA90C,
	CapsuleCollider_get_center_mC12CE0A66A1104CEB7D23F39596D0E45578419C2,
	CapsuleCollider_get_radius_m2462B43ECAC92386AAED85AA1DFD66440972D9D5,
	CapsuleCollider_get_height_m63A31072F296AEE6222DC9C88704882BB6A54A24,
	CapsuleCollider_get_center_Injected_m13CFD076EB23DD354C06A7D555B3C02265E1A39F,
	CapsuleCollider_get_radius_Injected_mFD3AB35080826BE2C362AAE718514CDA803F4F83,
	CapsuleCollider_get_height_Injected_m31ED5345D908D193A2AB0947EBC50E901454FEF4,
	Collider_get_isTrigger_mFF457F6AA71D173F9A11BAF00C35E5AE12952F87,
	Collider_set_material_m2E6C7BEE5FE88DD839FD0BBA83DD501E38B85499,
	Collider_get_isTrigger_Injected_mCA6BD3736CB8FC95F6BD3100CF978D4C4436F8DD,
	Collider_set_material_Injected_mE7116C7A74F7B0BFA011D5E2B21DF99E2798597F,
	Physics_OnSceneContactModify_m52106C18952BF4768B05F67FAF2B7F6F6F7D0C9D,
	Physics_PhysXOnSceneContactModify_m249A4E5A6A2967EA3CE7FD32ADD5851890D77AEE,
	Physics_get_invokeCollisionCallbacks_m73AE9C988EC57467D9B8699B376759D8E44133C8,
	Physics_get_defaultPhysicsScene_mC5D2BC20734D32FB421163F066BD5FB4118C633A,
	Physics_Raycast_m453681A406AADE0A30227D955279F5E7050B790D,
	Physics_Raycast_m0679FB03C9AFC1E803B8F8AE6CAB409670D31377,
	Physics_Raycast_mCFF84927BE3EC1780DBA34CCED374E7FF12ABCBE,
	Physics_Raycast_mCAA46C95211C7BB95697A347B036C012D26EB028,
	Physics_Raycast_mA782767AD4F149FBEA32C71460DFF061B7563688,
	Physics_Raycast_m56120FFEF0D4F0A44CCA505B5C946E6FB8742F12,
	Physics_Raycast_m011EA7022C33B2C499EF744E5AF3E01EEB8FBD33,
	Physics_Raycast_m1B27F500505FFB57D78548B9F5A540A2AD092903,
	Physics_Raycast_m9879C28DFF6CD3048F2365BC01C855565EE141F8,
	Physics_Raycast_m5CAA0AEDB2A6FB26E5F42A8EA560A61CAAF12E50,
	Physics_Raycast_m7A0FEA813B93A82713C06D8466F0A21325743488,
	Physics_Raycast_mDB89EB287ED040E534F6A933683A070D29DC14D3,
	Physics_Raycast_mCCD2542138D11E665A5D4F413C1547EE7D794DEB,
	Physics_Raycast_m34AC1210E893A9EF969BD2C7104B10BE5B580025,
	Physics_Raycast_m839BA104A76B928A03F075C622923C6FCD2F8685,
	Physics_Raycast_mCAC9F02A1AAB49E16B384EBC8318E2DF30F4B0E5,
	Physics_Linecast_m399C6C11AD7ECE11241A37C08BAB4D97CF3CB925,
	Physics_Linecast_mF9E3896E84ACD675E71363ADE30A8418C14C59C6,
	Physics_SphereCast_mDB2140FE8561D0CE870037527DACC44AB18A346D,
	Physics_SphereCast_m2A41FD7023EC5B89B69E0A8948325BEF46D9597C,
	Physics_SphereCast_mE7656F9355B33AED9095D8A0301734611EC95B05,
	Physics_SphereCast_mF6538C6C4E3A9BBD81B686437CC91F3A93C1F3E7,
	Physics_Internal_RaycastAll_mC128593FD48E6F237BE59CFCDC7DDE7A4E8CB074,
	Physics_RaycastAll_m8B7FB8419A65BEE78927D0EE84916E8DBE7ECD34,
	Physics_RaycastAll_m69ED0FF0B70ADBC45B907783C87B308E786F6D51,
	Physics_RaycastAll_mDCBE530EF2ACD21EAADEA829259291D7327BC80E,
	Physics_RaycastAll_mE56962F670046BE618FFE8D9B19595A896922789,
	Physics_RaycastAll_mD1643DB52C4E415083E215B154FEB9DFA3AD6D74,
	Physics_RaycastAll_m4055619E0F7EFA04620EAA0517F8393C4EBCFE87,
	Physics_RaycastAll_m1BBD4E474814BEC9B52B015081A256AE2FE00468,
	Physics_RaycastAll_mE94864EF8243F7D3A26C8666CEB02166C3742CB2,
	Physics_RaycastNonAlloc_mB37DE98E8C9407C3DB2FB488BAB1CF3A7C6FFFCE,
	Physics_RaycastNonAlloc_m2BFEE9072E390ED6ACD500FD0AE4E714DE9549BC,
	Physics_RaycastNonAlloc_m1908CB5E0D0570E9C88B6C259041520DD4D3169C,
	Physics_RaycastNonAlloc_m1961CFCDB7631C7FF4D12F88904CF1BEB24A6C3E,
	Physics_RaycastNonAlloc_mB8FE279E06CE87D77387AA9A10562B8052DC8836,
	Physics_RaycastNonAlloc_m4CFAA8CA088502DA71D748D276BDAAEF234B12B0,
	Physics_RaycastNonAlloc_m3EEB10539C49FEAD9533142FEE6578148A48FFA9,
	Physics_RaycastNonAlloc_mBDC9E19F4E3C82DCE03D799FDD41FB3314209460,
	Physics_Query_CapsuleCastAll_m874A33B3421181746FC7662648E0306620F7D522,
	Physics_CapsuleCastAll_m367017D9CB85D5F7AA7448F70E16E94578C09214,
	Physics_CapsuleCastAll_m9467728846F780021AF7D40168E7CA0D6A76F2AE,
	Physics_get_reuseCollisionCallbacks_mB23A11C02B893238B5631A38F2FBB4C63A3B7541,
	Physics_GetColliderByInstanceID_m0318A1C3CEC5AC6B42AB1F541EC3EE8909712220,
	Physics_GetBodyByInstanceID_mC45F93E518D6F1FC136DD3FB4377B3CC9F244725,
	Physics_SendOnCollisionEnter_mA48BA0630EE3D28320C602A15B5BDD887FA24144,
	Physics_SendOnCollisionStay_m2CC0293E4757CF89183A125270047A5054515590,
	Physics_SendOnCollisionExit_m6E96913C80E49A77FD367FBA5EF63755A81AA7D7,
	Physics_OnSceneContact_mFE8673EF13DD22B5C727BF5E2EFC4CF5E410C73A,
	Physics_ReportContacts_m9E7B0F448F534C8DEBBA6E33D86506C2500C919D,
	Physics_GetCollisionToReport_m7D22E5DD29678C65830A05A249650C49947A5D4E,
	Physics__cctor_m1E2D4816C77050D34F6D01F43E63D7969410AE2A,
	Physics_get_defaultPhysicsScene_Injected_mE86AE6A398435C1754A824B2B35DF13126A6C5D6,
	Physics_Internal_RaycastAll_Injected_m0100C5BF359DDE45F69B973D243D5B3CC0132754,
	Physics_Query_CapsuleCastAll_Injected_m509C782F8F40FFDEC8FB30DF7FB39E8E37FE3BF9,
	Physics_GetColliderByInstanceID_Injected_m450D0994A6A8609B16027C585D392343F6211B8B,
	Physics_GetBodyByInstanceID_Injected_mB05F094BD1E39D3BB58ACFB771DA941EAB0B20D8,
	Physics_SendOnCollisionEnter_Injected_m6E860F99570F9228DE3CD5D9AA7D8A87E189E960,
	Physics_SendOnCollisionStay_Injected_m4300A15E1F6881C0191E3A407A9296662661951C,
	Physics_SendOnCollisionExit_Injected_m935DC6B1CA45455BE752CEB6F4C6C42FC9B72E71,
	ContactEventDelegate__ctor_mF0844AC2AA36D48C199DDDBFA55627E43980CEEE,
	ContactEventDelegate_Invoke_m84BF3B9092BD4F6D43A870421F8389BC7B0E0769,
	ContactPairHeader_get_body_m8B8DC4B023CB7078B425E3CE764CFF3088D37FC8,
	ContactPairHeader_get_otherBody_m76BD1BDDE46A87839F214F741ECB0E0857EFAE0A,
	ContactPairHeader_get_hasRemovedBody_mE79EF42C5CB9FCDB476AC9437861F71CF7FC442A,
	ContactPairHeader_GetContactPair_m3DD517F464EDB35C4B0933854D95BE735DD2AC09,
	ContactPairHeader_GetContactPair_Internal_mCED67397346C23F3ABC5063AFFCF1F099AF5FC27,
	ContactPair_get_collider_mC74190F4919E0DAC5E8994C975BE36244900BD6C,
	ContactPair_get_otherCollider_mA99232AB090D27DCC1561094DC20621F3DA764A8,
	ContactPair_get_isCollisionEnter_m901546CD819E7891F6594A72AAEAE72FCB5515B1,
	ContactPair_get_isCollisionExit_mD54D019EABA4B209633B41803D6EE0CA1B978DF1,
	ContactPair_get_isCollisionStay_m62946484C4BD866D9AD91B23FE1C18336048E5F8,
	ContactPair_get_hasRemovedCollider_m3DFB259C3645FA874C7AD304494222472B5184AA,
	ContactPair_ExtractContactsArray_mB82D786FF9A04BC4B5A4C10EA5DC400AB6D655EC,
	ContactPair_GetContactPoint_mB31DB006460758A191A7D5CE7155523CFB62C454,
	ContactPair_GetContactPoint_Internal_m121E9C831FE1A673C1D76E00B731D3F5D558E565,
	ContactPairPoint_get_position_mD7D5690B579C6DF348EBBAD5523DAB1B90253C10,
	ContactPairPoint_get_separation_m5DF7D504E1EC95B2E44D37EAF78C1DCEC49DA12E,
	ContactPairPoint_get_normal_m019987EB1D8883A212596E8BB418EB787050FECF,
	ContactPairPoint_get_impulse_m982A7672DBBC6C7D8636D0FC9D1A8D75CA313D1A,
	PhysicsMaterial__ctor_m43CF5D49C73297C52A4EFC327E7FD29137E75FC7,
	PhysicsMaterial_Internal_CreateDynamicsMaterial_m6766ACF352256A5DEA2DF928958006AAA4136389,
	PhysicsMaterial_set_dynamicFriction_m59BE25A0D523F1C16E8E5EA3605D5D9C068D6AAE,
	PhysicsMaterial_set_staticFriction_m67DE5F3AD796C8AB92871E3A0B7338862BB27B9B,
	PhysicsMaterial_set_frictionCombine_m5095E28E658737F90616372006D6FA093DA875DF,
	PhysicsMaterial_Internal_CreateDynamicsMaterial_Injected_m81FBE209E2CE0CC055F3D5CE695AD1B951EA2FF0,
	PhysicsMaterial_set_dynamicFriction_Injected_mA206F4C8595D78CDD49B7502C085A671138B873E,
	PhysicsMaterial_set_staticFriction_Injected_mC15C61162DDC0DF3060AF13F0ABDD0E92203F1C6,
	PhysicsMaterial_set_frictionCombine_Injected_mD50FF725DAC858C568C694790335C4AA09D4F66F,
	PhysicsScene_ToString_mA4E28A3068A823D16D96BBA45115A2C457FC57C7,
	PhysicsScene_GetHashCode_m368888FB861F994FADEEDD281BD02B090C561814,
	PhysicsScene_Equals_mE3A11329AB6C2F4F76D2321D8BAE52671A2EDDA3,
	PhysicsScene_Equals_m81E4A78FC3644FDC44044B3A5F19F1C4283648A1,
	PhysicsScene_Raycast_m68D255133E274C5DDF33102EAAE70990C2A0A730,
	PhysicsScene_Internal_RaycastTest_m729F4A577F5DD911131C5321EC28E44F98A60BA0,
	PhysicsScene_Raycast_m6EE0783D1B113CAD5450A2CB876F6CA305BAD2CE,
	PhysicsScene_Internal_Raycast_m0211A7BDE011181718838F063296D51F88D92E74,
	PhysicsScene_Raycast_m3BD571CF6901C59C286D7B58ED9D15D836BC54C3,
	PhysicsScene_Internal_RaycastNonAlloc_mC339255AAFC484588C813D7BE2BDAE03797D26DB,
	PhysicsScene_Query_SphereCast_m8E6770FE64FB74157199217381AA1A99B3CF580B,
	PhysicsScene_Internal_SphereCast_mE4B0FBE790E2A7309F7807F5F1EFB909D21E07BF,
	PhysicsScene_SphereCast_mEB124233FFEA3BD179C9DE22E410290D7EB247C4,
	PhysicsScene_Internal_RaycastTest_Injected_m7633DAED691C6CFE296418FDBCE2E5E630456C62,
	PhysicsScene_Internal_Raycast_Injected_m09A18038A5A35901A6825B805600525583FD404D,
	PhysicsScene_Internal_RaycastNonAlloc_Injected_m5C535BFE6635BE6E66150B9560A14A5922580934,
	PhysicsScene_Query_SphereCast_Injected_m660DCB273A7D7AC02A4CACC69BBC38DF397E0D9A,
	RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D,
	RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39,
	RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5,
	RaycastHit_get_distance_m035194B0E9BB6229259CFC43B095A9C8E5011C78,
	Rigidbody_get_linearVelocity_m367F5415641E5FB968440A91BAE2FCB8CAEA2C8C,
	Rigidbody_set_linearVelocity_m29AE03D5FC079EAD4202FCF72E2AEBDC19363985,
	Rigidbody_get_position_m4ECB79BDBBF8FD1EA572EDB792D3330DDED24691,
	Rigidbody_AddForce_mBDBC288D0E266BC1B62E3649B4FCE46E7EA9CCBC,
	Rigidbody_get_linearVelocity_Injected_m0230F76BD25355A9762C9C5E32C60BF8249F4F6D,
	Rigidbody_set_linearVelocity_Injected_mCAC97A87C8F0FAFA7007C394AF7974DD37073467,
	Rigidbody_get_position_Injected_mD3AC6CDFE76252AAE5F4862BC7D18E0C479DDC8E,
	Rigidbody_AddForce_Injected_m0063728ED4AA2811701ED30CDFC9765816402152,
};
extern void ContactPairHeader_get_body_m8B8DC4B023CB7078B425E3CE764CFF3088D37FC8_AdjustorThunk (void);
extern void ContactPairHeader_get_otherBody_m76BD1BDDE46A87839F214F741ECB0E0857EFAE0A_AdjustorThunk (void);
extern void ContactPairHeader_get_hasRemovedBody_mE79EF42C5CB9FCDB476AC9437861F71CF7FC442A_AdjustorThunk (void);
extern void ContactPairHeader_GetContactPair_m3DD517F464EDB35C4B0933854D95BE735DD2AC09_AdjustorThunk (void);
extern void ContactPairHeader_GetContactPair_Internal_mCED67397346C23F3ABC5063AFFCF1F099AF5FC27_AdjustorThunk (void);
extern void ContactPair_get_collider_mC74190F4919E0DAC5E8994C975BE36244900BD6C_AdjustorThunk (void);
extern void ContactPair_get_otherCollider_mA99232AB090D27DCC1561094DC20621F3DA764A8_AdjustorThunk (void);
extern void ContactPair_get_isCollisionEnter_m901546CD819E7891F6594A72AAEAE72FCB5515B1_AdjustorThunk (void);
extern void ContactPair_get_isCollisionExit_mD54D019EABA4B209633B41803D6EE0CA1B978DF1_AdjustorThunk (void);
extern void ContactPair_get_isCollisionStay_m62946484C4BD866D9AD91B23FE1C18336048E5F8_AdjustorThunk (void);
extern void ContactPair_get_hasRemovedCollider_m3DFB259C3645FA874C7AD304494222472B5184AA_AdjustorThunk (void);
extern void ContactPair_ExtractContactsArray_mB82D786FF9A04BC4B5A4C10EA5DC400AB6D655EC_AdjustorThunk (void);
extern void ContactPair_GetContactPoint_mB31DB006460758A191A7D5CE7155523CFB62C454_AdjustorThunk (void);
extern void ContactPair_GetContactPoint_Internal_m121E9C831FE1A673C1D76E00B731D3F5D558E565_AdjustorThunk (void);
extern void ContactPairPoint_get_position_mD7D5690B579C6DF348EBBAD5523DAB1B90253C10_AdjustorThunk (void);
extern void ContactPairPoint_get_separation_m5DF7D504E1EC95B2E44D37EAF78C1DCEC49DA12E_AdjustorThunk (void);
extern void ContactPairPoint_get_normal_m019987EB1D8883A212596E8BB418EB787050FECF_AdjustorThunk (void);
extern void ContactPairPoint_get_impulse_m982A7672DBBC6C7D8636D0FC9D1A8D75CA313D1A_AdjustorThunk (void);
extern void PhysicsScene_ToString_mA4E28A3068A823D16D96BBA45115A2C457FC57C7_AdjustorThunk (void);
extern void PhysicsScene_GetHashCode_m368888FB861F994FADEEDD281BD02B090C561814_AdjustorThunk (void);
extern void PhysicsScene_Equals_mE3A11329AB6C2F4F76D2321D8BAE52671A2EDDA3_AdjustorThunk (void);
extern void PhysicsScene_Equals_m81E4A78FC3644FDC44044B3A5F19F1C4283648A1_AdjustorThunk (void);
extern void PhysicsScene_Raycast_m68D255133E274C5DDF33102EAAE70990C2A0A730_AdjustorThunk (void);
extern void PhysicsScene_Raycast_m6EE0783D1B113CAD5450A2CB876F6CA305BAD2CE_AdjustorThunk (void);
extern void PhysicsScene_Raycast_m3BD571CF6901C59C286D7B58ED9D15D836BC54C3_AdjustorThunk (void);
extern void PhysicsScene_SphereCast_mEB124233FFEA3BD179C9DE22E410290D7EB247C4_AdjustorThunk (void);
extern void RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D_AdjustorThunk (void);
extern void RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39_AdjustorThunk (void);
extern void RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5_AdjustorThunk (void);
extern void RaycastHit_get_distance_m035194B0E9BB6229259CFC43B095A9C8E5011C78_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[30] = 
{
	{ 0x06000051, ContactPairHeader_get_body_m8B8DC4B023CB7078B425E3CE764CFF3088D37FC8_AdjustorThunk },
	{ 0x06000052, ContactPairHeader_get_otherBody_m76BD1BDDE46A87839F214F741ECB0E0857EFAE0A_AdjustorThunk },
	{ 0x06000053, ContactPairHeader_get_hasRemovedBody_mE79EF42C5CB9FCDB476AC9437861F71CF7FC442A_AdjustorThunk },
	{ 0x06000054, ContactPairHeader_GetContactPair_m3DD517F464EDB35C4B0933854D95BE735DD2AC09_AdjustorThunk },
	{ 0x06000055, ContactPairHeader_GetContactPair_Internal_mCED67397346C23F3ABC5063AFFCF1F099AF5FC27_AdjustorThunk },
	{ 0x06000056, ContactPair_get_collider_mC74190F4919E0DAC5E8994C975BE36244900BD6C_AdjustorThunk },
	{ 0x06000057, ContactPair_get_otherCollider_mA99232AB090D27DCC1561094DC20621F3DA764A8_AdjustorThunk },
	{ 0x06000058, ContactPair_get_isCollisionEnter_m901546CD819E7891F6594A72AAEAE72FCB5515B1_AdjustorThunk },
	{ 0x06000059, ContactPair_get_isCollisionExit_mD54D019EABA4B209633B41803D6EE0CA1B978DF1_AdjustorThunk },
	{ 0x0600005A, ContactPair_get_isCollisionStay_m62946484C4BD866D9AD91B23FE1C18336048E5F8_AdjustorThunk },
	{ 0x0600005B, ContactPair_get_hasRemovedCollider_m3DFB259C3645FA874C7AD304494222472B5184AA_AdjustorThunk },
	{ 0x0600005C, ContactPair_ExtractContactsArray_mB82D786FF9A04BC4B5A4C10EA5DC400AB6D655EC_AdjustorThunk },
	{ 0x0600005D, ContactPair_GetContactPoint_mB31DB006460758A191A7D5CE7155523CFB62C454_AdjustorThunk },
	{ 0x0600005E, ContactPair_GetContactPoint_Internal_m121E9C831FE1A673C1D76E00B731D3F5D558E565_AdjustorThunk },
	{ 0x0600005F, ContactPairPoint_get_position_mD7D5690B579C6DF348EBBAD5523DAB1B90253C10_AdjustorThunk },
	{ 0x06000060, ContactPairPoint_get_separation_m5DF7D504E1EC95B2E44D37EAF78C1DCEC49DA12E_AdjustorThunk },
	{ 0x06000061, ContactPairPoint_get_normal_m019987EB1D8883A212596E8BB418EB787050FECF_AdjustorThunk },
	{ 0x06000062, ContactPairPoint_get_impulse_m982A7672DBBC6C7D8636D0FC9D1A8D75CA313D1A_AdjustorThunk },
	{ 0x0600006C, PhysicsScene_ToString_mA4E28A3068A823D16D96BBA45115A2C457FC57C7_AdjustorThunk },
	{ 0x0600006D, PhysicsScene_GetHashCode_m368888FB861F994FADEEDD281BD02B090C561814_AdjustorThunk },
	{ 0x0600006E, PhysicsScene_Equals_mE3A11329AB6C2F4F76D2321D8BAE52671A2EDDA3_AdjustorThunk },
	{ 0x0600006F, PhysicsScene_Equals_m81E4A78FC3644FDC44044B3A5F19F1C4283648A1_AdjustorThunk },
	{ 0x06000070, PhysicsScene_Raycast_m68D255133E274C5DDF33102EAAE70990C2A0A730_AdjustorThunk },
	{ 0x06000072, PhysicsScene_Raycast_m6EE0783D1B113CAD5450A2CB876F6CA305BAD2CE_AdjustorThunk },
	{ 0x06000074, PhysicsScene_Raycast_m3BD571CF6901C59C286D7B58ED9D15D836BC54C3_AdjustorThunk },
	{ 0x06000078, PhysicsScene_SphereCast_mEB124233FFEA3BD179C9DE22E410290D7EB247C4_AdjustorThunk },
	{ 0x0600007D, RaycastHit_get_collider_m84B160439BBEAB6D9E94B799F720E25C9E2D444D_AdjustorThunk },
	{ 0x0600007E, RaycastHit_get_point_m02B764612562AFE0F998CC7CFB2EEDE41BA47F39_AdjustorThunk },
	{ 0x0600007F, RaycastHit_get_normal_mD8741B70D2039C5CAFC4368D4CE59D89562040B5_AdjustorThunk },
	{ 0x06000080, RaycastHit_get_distance_m035194B0E9BB6229259CFC43B095A9C8E5011C78_AdjustorThunk },
};
static const int32_t s_InvokerIndices[136] = 
{
	5484,
	7102,
	1223,
	2247,
	7094,
	7033,
	7033,
	9311,
	9884,
	9884,
	6869,
	5600,
	9607,
	9315,
	8178,
	8178,
	10194,
	10238,
	7502,
	7826,
	8320,
	8916,
	7373,
	7501,
	7825,
	8319,
	7817,
	8311,
	8871,
	9610,
	7493,
	7816,
	8310,
	8870,
	7500,
	7824,
	7323,
	7372,
	7371,
	7494,
	7664,
	7667,
	8057,
	8509,
	9140,
	8052,
	8498,
	9118,
	9811,
	7628,
	7993,
	8411,
	9022,
	7389,
	7630,
	7996,
	8415,
	7306,
	7339,
	7418,
	10194,
	9805,
	9805,
	9340,
	9340,
	9340,
	8709,
	9989,
	8451,
	10294,
	9991,
	7434,
	7278,
	9741,
	9741,
	9316,
	9316,
	9316,
	2866,
	2923,
	6985,
	6985,
	6869,
	3380,
	3380,
	6985,
	6985,
	6869,
	6869,
	6869,
	6869,
	1949,
	3380,
	3380,
	7094,
	7033,
	7094,
	7094,
	7102,
	9340,
	5639,
	5639,
	5562,
	9325,
	9317,
	9317,
	9314,
	6985,
	6946,
	3852,
	3855,
	231,
	7492,
	161,
	7370,
	167,
	7386,
	7293,
	7292,
	91,
	7467,
	7358,
	7377,
	7287,
	6985,
	7094,
	7094,
	7033,
	7094,
	5699,
	7094,
	2977,
	9311,
	9311,
	9311,
	8639,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_PhysicsModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_PhysicsModule_CodeGenModule = 
{
	"UnityEngine.PhysicsModule.dll",
	136,
	s_methodPointers,
	30,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(Dronecamera))]
public class DronecameraEditor : Editor
{
    private Dronecamera dronecamera;
    private GUIStyle headerStyle;
    private GUIStyle subHeaderStyle;
    private GUIStyle boxStyle;
    private GUIStyle developerStyle;
    private bool showJoystickSettings = true;
    private bool showHeightButtons = true;
    private bool showMovementSettings = true;
    private bool showMovementLimits = true;
    private bool showMovementType = true;
    private bool showRotationSettings = true;
    private bool showRotationLimits = true;

    void OnEnable()
    {
        dronecamera = (Dronecamera)target;
    }

    public override void OnInspectorGUI()
    {
        InitializeStyles();
        
        // Developer Header
        DrawDeveloperHeader();
        
        EditorGUILayout.Space(10);
        
        // Main sections
        DrawJoystickSettings();
        DrawHeightButtonSettings();
        DrawMovementSettings();
        DrawMovementLimits();
        DrawMovementType();
        DrawRotationSettings();
        DrawRotationLimits();
        
        EditorGUILayout.Space(10);
        
        // Runtime Controls
        DrawRuntimeControls();
        
        // Apply changes
        if (GUI.changed)
        {
            EditorUtility.SetDirty(dronecamera);
        }
        
        serializedObject.ApplyModifiedProperties();
    }

    void InitializeStyles()
    {
        if (headerStyle == null)
        {
            headerStyle = new GUIStyle(EditorStyles.boldLabel);
            headerStyle.fontSize = 16;
            headerStyle.normal.textColor = new Color(0.2f, 0.8f, 1f);
            headerStyle.alignment = TextAnchor.MiddleCenter;
        }

        if (subHeaderStyle == null)
        {
            subHeaderStyle = new GUIStyle(EditorStyles.boldLabel);
            subHeaderStyle.fontSize = 12;
            subHeaderStyle.normal.textColor = new Color(0.3f, 0.7f, 0.9f);
        }

        if (boxStyle == null)
        {
            boxStyle = new GUIStyle(GUI.skin.box);
            boxStyle.padding = new RectOffset(10, 10, 10, 10);
            boxStyle.margin = new RectOffset(5, 5, 5, 5);
        }

        if (developerStyle == null)
        {
            developerStyle = new GUIStyle(EditorStyles.boldLabel);
            developerStyle.fontSize = 14;
            developerStyle.normal.textColor = new Color(1f, 0.7f, 0.2f);
            developerStyle.alignment = TextAnchor.MiddleCenter;
            developerStyle.fontStyle = FontStyle.BoldAndItalic;
        }
    }

    void DrawDeveloperHeader()
    {
        EditorGUILayout.BeginVertical(boxStyle);
        
        // Drone Camera Title with Icon
        GUILayout.BeginHorizontal();
        GUILayout.FlexibleSpace();
        GUILayout.Label("🚁", GUILayout.Width(30), GUILayout.Height(30));
        GUILayout.Label("DRONE CAMERA CONTROLLER", headerStyle);
        GUILayout.Label("🎮", GUILayout.Width(30), GUILayout.Height(30));
        GUILayout.FlexibleSpace();
        GUILayout.EndHorizontal();
        
        EditorGUILayout.Space(5);
        
        // Developer Credit
        GUILayout.BeginHorizontal();
        GUILayout.FlexibleSpace();
        GUILayout.Label("⭐ Developed by Ali Taj ⭐", developerStyle);
        GUILayout.FlexibleSpace();
        GUILayout.EndHorizontal();
        
        EditorGUILayout.Space(3);
        
        // Version and Features
        GUILayout.BeginHorizontal();
        GUILayout.FlexibleSpace();
        GUILayout.Label("🔥 Advanced Camera Control System 🔥", EditorStyles.miniLabel);
        GUILayout.FlexibleSpace();
        GUILayout.EndHorizontal();
        
        EditorGUILayout.EndVertical();
    }

    void DrawJoystickSettings()
    {
        EditorGUILayout.BeginVertical(boxStyle);
        
        showJoystickSettings = EditorGUILayout.Foldout(showJoystickSettings, "🕹️ Joystick Settings", true, subHeaderStyle);
        
        if (showJoystickSettings)
        {
            EditorGUILayout.Space(5);
            
            SerializedProperty joystickProp = serializedObject.FindProperty("joystick");
            EditorGUILayout.PropertyField(joystickProp, new GUIContent("🎯 Joystick", "Main joystick for camera movement"));
            
            SerializedProperty cameraProp = serializedObject.FindProperty("Dronecameraa");
            EditorGUILayout.PropertyField(cameraProp, new GUIContent("📷 Drone Camera", "Camera reference (optional)"));
            
            if (joystickProp.objectReferenceValue == null)
            {
                EditorGUILayout.HelpBox("⚠️ Joystick reference required for movement control!", MessageType.Warning);
            }
        }
        
        EditorGUILayout.EndVertical();
    }

    void DrawHeightButtonSettings()
    {
        EditorGUILayout.BeginVertical(boxStyle);
        
        showHeightButtons = EditorGUILayout.Foldout(showHeightButtons, "🔼🔽 Height Control Buttons", true, subHeaderStyle);
        
        if (showHeightButtons)
        {
            EditorGUILayout.Space(5);
            
            SerializedProperty upButtonProp = serializedObject.FindProperty("upHeightButton");
            EditorGUILayout.PropertyField(upButtonProp, new GUIContent("⬆️ Up Height Button", "UI Button for moving camera up"));
            
            SerializedProperty downButtonProp = serializedObject.FindProperty("downHeightButton");
            EditorGUILayout.PropertyField(downButtonProp, new GUIContent("⬇️ Down Height Button", "UI Button for moving camera down"));
            
            if (upButtonProp.objectReferenceValue == null || downButtonProp.objectReferenceValue == null)
            {
                EditorGUILayout.HelpBox("💡 Assign UI buttons for height control functionality!", MessageType.Info);
            }
        }
        
        EditorGUILayout.EndVertical();
    }

    void DrawMovementSettings()
    {
        EditorGUILayout.BeginVertical(boxStyle);
        
        showMovementSettings = EditorGUILayout.Foldout(showMovementSettings, "⚡ Movement Settings", true, subHeaderStyle);
        
        if (showMovementSettings)
        {
            EditorGUILayout.Space(5);
            
            SerializedProperty horizontalSpeedProp = serializedObject.FindProperty("horizontalSpeed");
            EditorGUILayout.PropertyField(horizontalSpeedProp, new GUIContent("↔️ Horizontal Speed", "Speed of left/right movement"));
            
            SerializedProperty verticalSpeedProp = serializedObject.FindProperty("verticalSpeed");
            EditorGUILayout.PropertyField(verticalSpeedProp, new GUIContent("↕️ Vertical Speed", "Speed of forward/backward movement"));
            
            SerializedProperty heightSpeedProp = serializedObject.FindProperty("heightSpeed");
            EditorGUILayout.PropertyField(heightSpeedProp, new GUIContent("🔺 Height Speed", "Speed of up/down height movement"));
            
            SerializedProperty smoothTimeProp = serializedObject.FindProperty("smoothTime");
            EditorGUILayout.PropertyField(smoothTimeProp, new GUIContent("🌊 Smooth Time", "Camera movement smoothness"));
        }
        
        EditorGUILayout.EndVertical();
    }

    void DrawMovementLimits()
    {
        EditorGUILayout.BeginVertical(boxStyle);
        
        showMovementLimits = EditorGUILayout.Foldout(showMovementLimits, "🚧 Movement Limits", true, subHeaderStyle);
        
        if (showMovementLimits)
        {
            EditorGUILayout.Space(5);
            
            // Horizontal Limits
            SerializedProperty limitHorizontalProp = serializedObject.FindProperty("limitHorizontalMovement");
            EditorGUILayout.PropertyField(limitHorizontalProp, new GUIContent("🔒 Limit Horizontal Movement"));
            
            if (limitHorizontalProp.boolValue)
            {
                EditorGUI.indentLevel++;
                SerializedProperty minXProp = serializedObject.FindProperty("minX");
                SerializedProperty maxXProp = serializedObject.FindProperty("maxX");
                EditorGUILayout.PropertyField(minXProp, new GUIContent("⬅️ Min X"));
                EditorGUILayout.PropertyField(maxXProp, new GUIContent("➡️ Max X"));
                EditorGUI.indentLevel--;
            }
            
            // Vertical Limits
            SerializedProperty limitVerticalProp = serializedObject.FindProperty("limitVerticalMovement");
            EditorGUILayout.PropertyField(limitVerticalProp, new GUIContent("🔒 Limit Vertical Movement"));
            
            if (limitVerticalProp.boolValue)
            {
                EditorGUI.indentLevel++;
                SerializedProperty minZProp = serializedObject.FindProperty("minZ");
                SerializedProperty maxZProp = serializedObject.FindProperty("maxZ");
                EditorGUILayout.PropertyField(minZProp, new GUIContent("⬇️ Min Z"));
                EditorGUILayout.PropertyField(maxZProp, new GUIContent("⬆️ Max Z"));
                EditorGUI.indentLevel--;
            }
            
            // Height Limits
            SerializedProperty limitHeightProp = serializedObject.FindProperty("limitHeightMovement");
            EditorGUILayout.PropertyField(limitHeightProp, new GUIContent("🔒 Limit Height Movement"));
            
            if (limitHeightProp.boolValue)
            {
                EditorGUI.indentLevel++;
                SerializedProperty minYProp = serializedObject.FindProperty("minY");
                SerializedProperty maxYProp = serializedObject.FindProperty("maxY");
                EditorGUILayout.PropertyField(minYProp, new GUIContent("🔻 Min Y"));
                EditorGUILayout.PropertyField(maxYProp, new GUIContent("🔺 Max Y"));
                EditorGUI.indentLevel--;
            }
        }
        
        EditorGUILayout.EndVertical();
    }

    void DrawMovementType()
    {
        EditorGUILayout.BeginVertical(boxStyle);
        
        showMovementType = EditorGUILayout.Foldout(showMovementType, "🌍 Movement Type", true, subHeaderStyle);
        
        if (showMovementType)
        {
            EditorGUILayout.Space(5);
            
            SerializedProperty moveRelativeProp = serializedObject.FindProperty("moveRelativeToRotation");
            EditorGUILayout.PropertyField(moveRelativeProp, new GUIContent("🔄 Move Relative To Rotation", "Move relative to camera's rotation or world space"));
            
            if (moveRelativeProp.boolValue)
            {
                EditorGUILayout.HelpBox("🎯 Camera moves relative to its current rotation", MessageType.Info);
            }
            else
            {
                EditorGUILayout.HelpBox("🌐 Camera moves in world space coordinates", MessageType.Info);
            }
        }
        
        EditorGUILayout.EndVertical();
    }

    void DrawRotationSettings()
    {
        EditorGUILayout.BeginVertical(boxStyle);

        showRotationSettings = EditorGUILayout.Foldout(showRotationSettings, "🔄 Mouse Drag Rotation", true, subHeaderStyle);

        if (showRotationSettings)
        {
            EditorGUILayout.Space(5);

            SerializedProperty enableRotationProp = serializedObject.FindProperty("enableMouseDragRotation");
            EditorGUILayout.PropertyField(enableRotationProp, new GUIContent("🖱️ Enable Mouse Drag Rotation", "Enable mouse drag rotation for PC and mobile"));

            if (enableRotationProp.boolValue)
            {
                EditorGUI.indentLevel++;

                SerializedProperty mouseSensitivityProp = serializedObject.FindProperty("mouseSensitivity");
                EditorGUILayout.PropertyField(mouseSensitivityProp, new GUIContent("🖱️ Mouse Sensitivity", "Mouse sensitivity for PC rotation"));

                SerializedProperty touchSensitivityProp = serializedObject.FindProperty("touchSensitivity");
                EditorGUILayout.PropertyField(touchSensitivityProp, new GUIContent("📱 Touch Sensitivity", "Touch sensitivity for mobile rotation"));

                SerializedProperty rotationSmoothProp = serializedObject.FindProperty("rotationSmoothTime");
                EditorGUILayout.PropertyField(rotationSmoothProp, new GUIContent("🌊 Rotation Smooth Time", "Smooth rotation interpolation"));

                EditorGUI.indentLevel--;

                EditorGUILayout.HelpBox("🎯 Left click + drag (PC) or touch + drag (Mobile) to rotate camera", MessageType.Info);
            }
            else
            {
                EditorGUILayout.HelpBox("⚠️ Mouse drag rotation is disabled", MessageType.Warning);
            }
        }

        EditorGUILayout.EndVertical();
    }

    void DrawRotationLimits()
    {
        EditorGUILayout.BeginVertical(boxStyle);

        showRotationLimits = EditorGUILayout.Foldout(showRotationLimits, "🚧 Rotation Limits", true, subHeaderStyle);

        if (showRotationLimits)
        {
            EditorGUILayout.Space(5);

            // Vertical Rotation Limits
            SerializedProperty limitVerticalProp = serializedObject.FindProperty("limitVerticalRotation");
            EditorGUILayout.PropertyField(limitVerticalProp, new GUIContent("🔒 Limit Vertical Rotation", "Limit up/down rotation"));

            if (limitVerticalProp.boolValue)
            {
                EditorGUI.indentLevel++;
                SerializedProperty minVerticalProp = serializedObject.FindProperty("minVerticalAngle");
                SerializedProperty maxVerticalProp = serializedObject.FindProperty("maxVerticalAngle");
                EditorGUILayout.PropertyField(minVerticalProp, new GUIContent("⬇️ Min Vertical Angle"));
                EditorGUILayout.PropertyField(maxVerticalProp, new GUIContent("⬆️ Max Vertical Angle"));
                EditorGUI.indentLevel--;
            }

            // Horizontal Rotation Limits
            SerializedProperty limitHorizontalProp = serializedObject.FindProperty("limitHorizontalRotation");
            EditorGUILayout.PropertyField(limitHorizontalProp, new GUIContent("🔒 Limit Horizontal Rotation", "Limit left/right rotation"));

            if (limitHorizontalProp.boolValue)
            {
                EditorGUI.indentLevel++;
                SerializedProperty minHorizontalProp = serializedObject.FindProperty("minHorizontalAngle");
                SerializedProperty maxHorizontalProp = serializedObject.FindProperty("maxHorizontalAngle");
                EditorGUILayout.PropertyField(minHorizontalProp, new GUIContent("⬅️ Min Horizontal Angle"));
                EditorGUILayout.PropertyField(maxHorizontalProp, new GUIContent("➡️ Max Horizontal Angle"));
                EditorGUI.indentLevel--;
            }
        }

        EditorGUILayout.EndVertical();
    }

    void DrawRuntimeControls()
    {
        if (Application.isPlaying)
        {
            EditorGUILayout.BeginVertical(boxStyle);
            
            GUILayout.Label("🎮 Runtime Controls", subHeaderStyle);
            
            EditorGUILayout.Space(5);
            
            GUILayout.BeginHorizontal();
            if (GUILayout.Button("⬆️ Move Up", GUILayout.Height(30)))
            {
                dronecamera.MoveUp();
            }
            if (GUILayout.Button("⬇️ Move Down", GUILayout.Height(30)))
            {
                dronecamera.MoveDown();
            }
            GUILayout.EndHorizontal();
            
            EditorGUILayout.Space(5);
            
            if (GUILayout.Button("🔄 Reset Position", GUILayout.Height(25)))
            {
                dronecamera.ResetCameraPosition();
            }

            EditorGUILayout.Space(5);

            GUILayout.BeginHorizontal();
            if (GUILayout.Button("🔄 Reset Rotation", GUILayout.Height(25)))
            {
                dronecamera.ResetCameraRotation();
            }
            if (GUILayout.Button("🎯 Get Rotation", GUILayout.Height(25)))
            {
                Vector3 currentRotation = dronecamera.GetCurrentRotation();
                Debug.Log($"Current Camera Rotation: {currentRotation}");
            }
            GUILayout.EndHorizontal();
            
            EditorGUILayout.EndVertical();
        }
    }
}

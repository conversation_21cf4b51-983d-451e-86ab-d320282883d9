﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void SubsystemsAnalyticBase__ctor_mFA87BE5A4B341D60F40E9B6456CC117CAA88A3DD (void);
extern void SubsystemsAnalyticStart__ctor_mC3C1CC998F390C94755BFF43527612BBFE1C87A5 (void);
extern void SubsystemsAnalyticStart_CreateSubsystemsAnalyticStart_m1492EA04566B9E1570F881E854EE71C834D71850 (void);
extern void SubsystemsAnalyticStop__ctor_m85029015CA138C93218DF1D5D480B1388C6C2B4F (void);
extern void SubsystemsAnalyticStop_CreateSubsystemsAnalyticStop_m8E598E75B2E4999AE3D1416E847061E6283FA04A (void);
extern void SubsystemsAnalyticInfo__ctor_m4D07AF96CDDDA9A343135313D6FEFC3FF91642D3 (void);
extern void SubsystemsAnalyticInfo_CreateSubsystemsAnalyticInfo_mF09B54E448C2C2ED4E5CC40B4CBB11B3E6403A73 (void);
extern void VRDeviceAnalyticBase__ctor_m6477E0459113D3D49F9A0296628C354A895EBB46 (void);
extern void VRDeviceAnalyticAspect_CreateVRDeviceAnalyticAspect_m805C2E29A626B39096D71914C9C470AE39E2017B (void);
extern void VRDeviceAnalyticAspect__ctor_m671165C1F80107A6598880540DC41D23DC7A7C6B (void);
extern void VRDeviceMirrorAnalytic_CreateVRDeviceMirrorAnalytic_m9E011D51CA97DB1D0B52DCBADB8B1390A2EE0E36 (void);
extern void VRDeviceMirrorAnalytic__ctor_mC5C237A49C47BBCF9E16B70A0E8FF82FFFBF2B16 (void);
extern void VRDeviceUserAnalytic_CreateVRDeviceUserAnalytic_m731282C5872A2B2C91542CCBAA4DE9A2AE37BD06 (void);
extern void VRDeviceUserAnalytic__ctor_m99FBEFC61AE64B7CEC51C3AC01672C05A9BBF7CA (void);
extern void VRDeviceActiveControllersAnalytic_CreateVRDeviceActiveControllersAnalytic_mD99AB60F3CB4B467D8B372C4FC6B5A9143E07FC4 (void);
extern void VRDeviceActiveControllersAnalytic__ctor_m341D57A0E94CAAC620CC027D7367A5ED5017DC71 (void);
extern void AssetDatabaseRefreshAnalytic__ctor_m559257B329AB842DDB2A66CB266045CA181636C4 (void);
extern void AssetDatabaseRefreshAnalytic_CreateAssetDatabaseRefreshAnalytic_m4A02F6C332508D5E96618368B7421DFE37627E10 (void);
extern void BuildAssetBundleAnalytic__ctor_m0D9A558EF518A3B188840D8D64E6AAB30525191D (void);
extern void BuildAssetBundleAnalytic_CreateBuildAssetBundleAnalytic_mAE56D0D00202410C4FF1BA41F82D4BFAE8B7C70E (void);
extern void CollabOperationAnalytic__ctor_m01AF147512BE1565F7E7475DB5D15A4ABAE4C239 (void);
extern void CollabOperationAnalytic_CreateCollabOperationAnalytic_m6FBFACAF2DEE6BD311454FF336B741AED79622CB (void);
extern void LicensingErrorAnalytic__ctor_m50E0E3CA0DEC01E1E1B74FE3A13A0DE8C2C1663E (void);
extern void LicensingErrorAnalytic_CreateLicensingErrorAnalytic_mEE6198C9D2D65DD4B9F6E9B317BE4F78B9FC8381 (void);
extern void LicensingInitAnalytic__ctor_mB3C254A9AFD36AF4AE91D579C61D307034604473 (void);
extern void LicensingInitAnalytic_CreateLicensingInitAnalytic_m29818225A42E7CFB22E25EE8751C7FD188DA7A4D (void);
extern void MetalPatchShaderComputeBufferAnalytic__ctor_mF2C9B825FCFAA669CF856634F1E402EEDB66323D (void);
extern void MetalPatchShaderComputeBufferAnalytic_CreateMetalPatchShaderComputeBufferAnalytic_m6B50AD1434AA6241B640CEEFE2D55829D8648A12 (void);
extern void NavmeshBakingAnalytic__ctor_m872F2389C82EF622FCF27149331047433154AA32 (void);
extern void NavmeshBakingAnalytic_CreateNavmeshBakingAnalytic_mEF48522BC8576FA30098A18A611BEACF460E628E (void);
extern void ProjectSettingsInformationAnalytic__ctor_mEA9A84C9D71CF4B1F76DB9531502141C5B7B0CEB (void);
extern void ProjectSettingsInformationAnalytic_CreateProjectSettingsInformationAnalytic_mCE60054939F431E43E5B6B965CE3CD8F6EC4F4B1 (void);
extern void SendGameBuildAnalytic__ctor_mDDB217836CD885FB873B778F13F29F7BC22F8ED2 (void);
extern void SendGameBuildAnalytic_CreateSendGameBuildAnalytic_mEA87ADD9783785897361B678C8CA93CA960D10A1 (void);
extern void PackageManagerBaseAnalytic__ctor_m8842E6D52829AE842954EED1931BA41566FEE4D4 (void);
extern void PackageManagerAddPackageAnalytic__ctor_mC7B55AD6D65BE8A787B19CF07420D808FCACCA56 (void);
extern void PackageManagerAddPackageAnalytic_CreatePackageManagerAddPackageAnalytic_mBF5409B80E7D898224269B20D025D314DB5EEFD2 (void);
extern void PackageManagerTestAnalytic__ctor_m065BD6F0F104CFD8F8D60FA2A1108FACC34809A8 (void);
extern void PackageManagerTestAnalytic_CreatePackageManagerTestAnalytic_m8004FBBD9A6841B1B789E5308FA5E0127400BF67 (void);
extern void PackageManagerRemovePackageAnalytic__ctor_mE39C68F34D0C1661EC5862020BFF8DD208E811B0 (void);
extern void PackageManagerRemovePackageAnalytic_CreatePackageManagerRemovePackageAnalytic_m55416B2732EB5F1748823E35F462A35AE885FA51 (void);
extern void PackageManagerResolvePackageAnalytic__ctor_mADD6E37DF222E0313F7C44B2DFBBA98377C5865A (void);
extern void PackageManagerResolvePackageAnalytic_CreatePackageManagerResolvePackageAnalytic_m175B998CCE3C2E099260B2F3C09B0E0FD9931394 (void);
extern void PackageManagerEmbedPackageAnalytic__ctor_mD731FFB58F114E225C407460802778276FFF0100 (void);
extern void PackageManagerEmbedPackageAnalytic_CreatePackageManagerEmbedPackageAnalytic_m87F042406568B28D7CDE4775CD7AF3F9871392AD (void);
extern void PackageManagerResetPackageAnalytic__ctor_m79287E2503C6705DA9453FB6D74B32C65A0BB39A (void);
extern void PackageManagerResetPackageAnalytic_CreatePackageManagerResetPackageAnalytic_m1D2A3CF6D1E069E64B29682AA15ADCA147ED7463 (void);
extern void PackageManagerResolveErrorPackageAnalytic__ctor_mE8212E958A8DF7E28D734ECD89ECAA68C0100643 (void);
extern void PackageManagerResolveErrorPackageAnalytic_CreatePackageManagerResolveErrorPackageAnalytic_mF0038A5A5A6D14DD0D4820E73822D6455DBE0E20 (void);
extern void PackageManagerStartServerPackageAnalytic__ctor_mE67ABB0A919D9524E4C174FA7BD84873373D2FB7 (void);
extern void PackageManagerStartServerPackageAnalytic_CreatePackageManagerStartServerPackageAnalytic_mF48D7729D5B9116CEFC04445E33255EDF714C088 (void);
extern void AssetImportStatusAnalytic__ctor_m08D8DB74F18BFAF3461FF93FA9ACE057C516F928 (void);
extern void AssetImportStatusAnalytic_CreateAssetImportStatusAnalytic_m9681846BC2D461E7857CBAA087CF1F193EAA705F (void);
extern void AssetImportAnalytic__ctor_m5335AA39762B1606364B586E09B66490EB8A3042 (void);
extern void AssetImportAnalytic_CreateAssetImportAnalytic_mA2184CAF20B72A5BC5443B6BF39E86C246CD1037 (void);
extern void AssetExportAnalytic__ctor_m7987504F06FAC24A6C0EF8EF978DD31F453B48F9 (void);
extern void AssetExportAnalytic_CreateAssetExportAnalytic_m1C5A57996B7C717E018DE5C4261B7B4087608206 (void);
extern void StallSummaryAnalytic__ctor_m6F569DB918D3775A5024D9A36999BF1E81B47605 (void);
extern void StallSummaryAnalytic_CreateStallSummaryAnalytic_mAF825EC0FF5C6EFFE678385E3492403C3F3EB7EA (void);
extern void StallMarkerAnalytic__ctor_m1248F3FC87B6DB3D32A70EBEE14859942A476CDA (void);
extern void StallMarkerAnalytic_CreateStallMarkerAnalytic_m2FD60E6349F04CC44DFC6FFEEC766294EEFF6C1B (void);
extern void TestAnalytic__ctor_mB6C1AABD2CA785D03984294A1813326B40CCB373 (void);
extern void TestAnalytic_CreateTestAnalytic_m30D0AFC187AC0F0CEDF63DA35EB92D08F6A024B9 (void);
static Il2CppMethodPointer s_methodPointers[63] = 
{
	SubsystemsAnalyticBase__ctor_mFA87BE5A4B341D60F40E9B6456CC117CAA88A3DD,
	SubsystemsAnalyticStart__ctor_mC3C1CC998F390C94755BFF43527612BBFE1C87A5,
	SubsystemsAnalyticStart_CreateSubsystemsAnalyticStart_m1492EA04566B9E1570F881E854EE71C834D71850,
	SubsystemsAnalyticStop__ctor_m85029015CA138C93218DF1D5D480B1388C6C2B4F,
	SubsystemsAnalyticStop_CreateSubsystemsAnalyticStop_m8E598E75B2E4999AE3D1416E847061E6283FA04A,
	SubsystemsAnalyticInfo__ctor_m4D07AF96CDDDA9A343135313D6FEFC3FF91642D3,
	SubsystemsAnalyticInfo_CreateSubsystemsAnalyticInfo_mF09B54E448C2C2ED4E5CC40B4CBB11B3E6403A73,
	VRDeviceAnalyticBase__ctor_m6477E0459113D3D49F9A0296628C354A895EBB46,
	VRDeviceAnalyticAspect_CreateVRDeviceAnalyticAspect_m805C2E29A626B39096D71914C9C470AE39E2017B,
	VRDeviceAnalyticAspect__ctor_m671165C1F80107A6598880540DC41D23DC7A7C6B,
	VRDeviceMirrorAnalytic_CreateVRDeviceMirrorAnalytic_m9E011D51CA97DB1D0B52DCBADB8B1390A2EE0E36,
	VRDeviceMirrorAnalytic__ctor_mC5C237A49C47BBCF9E16B70A0E8FF82FFFBF2B16,
	VRDeviceUserAnalytic_CreateVRDeviceUserAnalytic_m731282C5872A2B2C91542CCBAA4DE9A2AE37BD06,
	VRDeviceUserAnalytic__ctor_m99FBEFC61AE64B7CEC51C3AC01672C05A9BBF7CA,
	VRDeviceActiveControllersAnalytic_CreateVRDeviceActiveControllersAnalytic_mD99AB60F3CB4B467D8B372C4FC6B5A9143E07FC4,
	VRDeviceActiveControllersAnalytic__ctor_m341D57A0E94CAAC620CC027D7367A5ED5017DC71,
	AssetDatabaseRefreshAnalytic__ctor_m559257B329AB842DDB2A66CB266045CA181636C4,
	AssetDatabaseRefreshAnalytic_CreateAssetDatabaseRefreshAnalytic_m4A02F6C332508D5E96618368B7421DFE37627E10,
	BuildAssetBundleAnalytic__ctor_m0D9A558EF518A3B188840D8D64E6AAB30525191D,
	BuildAssetBundleAnalytic_CreateBuildAssetBundleAnalytic_mAE56D0D00202410C4FF1BA41F82D4BFAE8B7C70E,
	CollabOperationAnalytic__ctor_m01AF147512BE1565F7E7475DB5D15A4ABAE4C239,
	CollabOperationAnalytic_CreateCollabOperationAnalytic_m6FBFACAF2DEE6BD311454FF336B741AED79622CB,
	LicensingErrorAnalytic__ctor_m50E0E3CA0DEC01E1E1B74FE3A13A0DE8C2C1663E,
	LicensingErrorAnalytic_CreateLicensingErrorAnalytic_mEE6198C9D2D65DD4B9F6E9B317BE4F78B9FC8381,
	LicensingInitAnalytic__ctor_mB3C254A9AFD36AF4AE91D579C61D307034604473,
	LicensingInitAnalytic_CreateLicensingInitAnalytic_m29818225A42E7CFB22E25EE8751C7FD188DA7A4D,
	MetalPatchShaderComputeBufferAnalytic__ctor_mF2C9B825FCFAA669CF856634F1E402EEDB66323D,
	MetalPatchShaderComputeBufferAnalytic_CreateMetalPatchShaderComputeBufferAnalytic_m6B50AD1434AA6241B640CEEFE2D55829D8648A12,
	NavmeshBakingAnalytic__ctor_m872F2389C82EF622FCF27149331047433154AA32,
	NavmeshBakingAnalytic_CreateNavmeshBakingAnalytic_mEF48522BC8576FA30098A18A611BEACF460E628E,
	ProjectSettingsInformationAnalytic__ctor_mEA9A84C9D71CF4B1F76DB9531502141C5B7B0CEB,
	ProjectSettingsInformationAnalytic_CreateProjectSettingsInformationAnalytic_mCE60054939F431E43E5B6B965CE3CD8F6EC4F4B1,
	SendGameBuildAnalytic__ctor_mDDB217836CD885FB873B778F13F29F7BC22F8ED2,
	SendGameBuildAnalytic_CreateSendGameBuildAnalytic_mEA87ADD9783785897361B678C8CA93CA960D10A1,
	PackageManagerBaseAnalytic__ctor_m8842E6D52829AE842954EED1931BA41566FEE4D4,
	PackageManagerAddPackageAnalytic__ctor_mC7B55AD6D65BE8A787B19CF07420D808FCACCA56,
	PackageManagerAddPackageAnalytic_CreatePackageManagerAddPackageAnalytic_mBF5409B80E7D898224269B20D025D314DB5EEFD2,
	PackageManagerTestAnalytic__ctor_m065BD6F0F104CFD8F8D60FA2A1108FACC34809A8,
	PackageManagerTestAnalytic_CreatePackageManagerTestAnalytic_m8004FBBD9A6841B1B789E5308FA5E0127400BF67,
	PackageManagerRemovePackageAnalytic__ctor_mE39C68F34D0C1661EC5862020BFF8DD208E811B0,
	PackageManagerRemovePackageAnalytic_CreatePackageManagerRemovePackageAnalytic_m55416B2732EB5F1748823E35F462A35AE885FA51,
	PackageManagerResolvePackageAnalytic__ctor_mADD6E37DF222E0313F7C44B2DFBBA98377C5865A,
	PackageManagerResolvePackageAnalytic_CreatePackageManagerResolvePackageAnalytic_m175B998CCE3C2E099260B2F3C09B0E0FD9931394,
	PackageManagerEmbedPackageAnalytic__ctor_mD731FFB58F114E225C407460802778276FFF0100,
	PackageManagerEmbedPackageAnalytic_CreatePackageManagerEmbedPackageAnalytic_m87F042406568B28D7CDE4775CD7AF3F9871392AD,
	PackageManagerResetPackageAnalytic__ctor_m79287E2503C6705DA9453FB6D74B32C65A0BB39A,
	PackageManagerResetPackageAnalytic_CreatePackageManagerResetPackageAnalytic_m1D2A3CF6D1E069E64B29682AA15ADCA147ED7463,
	PackageManagerResolveErrorPackageAnalytic__ctor_mE8212E958A8DF7E28D734ECD89ECAA68C0100643,
	PackageManagerResolveErrorPackageAnalytic_CreatePackageManagerResolveErrorPackageAnalytic_mF0038A5A5A6D14DD0D4820E73822D6455DBE0E20,
	PackageManagerStartServerPackageAnalytic__ctor_mE67ABB0A919D9524E4C174FA7BD84873373D2FB7,
	PackageManagerStartServerPackageAnalytic_CreatePackageManagerStartServerPackageAnalytic_mF48D7729D5B9116CEFC04445E33255EDF714C088,
	AssetImportStatusAnalytic__ctor_m08D8DB74F18BFAF3461FF93FA9ACE057C516F928,
	AssetImportStatusAnalytic_CreateAssetImportStatusAnalytic_m9681846BC2D461E7857CBAA087CF1F193EAA705F,
	AssetImportAnalytic__ctor_m5335AA39762B1606364B586E09B66490EB8A3042,
	AssetImportAnalytic_CreateAssetImportAnalytic_mA2184CAF20B72A5BC5443B6BF39E86C246CD1037,
	AssetExportAnalytic__ctor_m7987504F06FAC24A6C0EF8EF978DD31F453B48F9,
	AssetExportAnalytic_CreateAssetExportAnalytic_m1C5A57996B7C717E018DE5C4261B7B4087608206,
	StallSummaryAnalytic__ctor_m6F569DB918D3775A5024D9A36999BF1E81B47605,
	StallSummaryAnalytic_CreateStallSummaryAnalytic_mAF825EC0FF5C6EFFE678385E3492403C3F3EB7EA,
	StallMarkerAnalytic__ctor_m1248F3FC87B6DB3D32A70EBEE14859942A476CDA,
	StallMarkerAnalytic_CreateStallMarkerAnalytic_m2FD60E6349F04CC44DFC6FFEEC766294EEFF6C1B,
	TestAnalytic__ctor_mB6C1AABD2CA785D03984294A1813326B40CCB373,
	TestAnalytic_CreateTestAnalytic_m30D0AFC187AC0F0CEDF63DA35EB92D08F6A024B9,
};
static const int32_t s_InvokerIndices[63] = 
{
	5600,
	7102,
	10235,
	7102,
	10235,
	7102,
	10235,
	7102,
	10235,
	7102,
	10235,
	7102,
	10235,
	7102,
	10235,
	7102,
	7102,
	10235,
	7102,
	10235,
	7102,
	10235,
	7102,
	10235,
	7102,
	10235,
	7102,
	10235,
	7102,
	10235,
	7102,
	10235,
	7102,
	10235,
	5600,
	7102,
	10235,
	7102,
	10235,
	7102,
	10235,
	7102,
	10235,
	7102,
	10235,
	7102,
	10235,
	7102,
	10235,
	7102,
	10235,
	7102,
	10235,
	7102,
	10235,
	7102,
	10235,
	7102,
	10235,
	7102,
	10235,
	7102,
	10235,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_UnityAnalyticsCommonModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_UnityAnalyticsCommonModule_CodeGenModule = 
{
	"UnityEngine.UnityAnalyticsCommonModule.dll",
	63,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

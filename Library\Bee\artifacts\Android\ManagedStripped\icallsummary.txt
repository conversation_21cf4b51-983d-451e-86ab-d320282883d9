Unity.Burst.LowLevel.BurstCompilerService::GetOrCreateSharedMemory
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::MemCmp
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::Free
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::FreeTracked
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::MemCpy
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::MemCpyStride
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::MemMove
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::MemSet
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::Malloc
Unity.Collections.LowLevel.Unsafe.UnsafeUtility::MallocTracked
Unity.Hierarchy.Hierarchy::Exists_Injected
Unity.Hierarchy.Hierarchy::SetParent_Injected
Unity.Hierarchy.Hierarchy::get_UpdateNeeded_Injected
Unity.Hierarchy.Hierarchy::GetChildrenCount_Injected
Unity.Hierarchy.Hierarchy::GetNodeTypeHandlersBaseCount_Injected
Unity.Hierarchy.Hierarchy::GetNodeTypeHandlersBaseSpan_Injected
Unity.Hierarchy.Hierarchy::Create
Unity.Hierarchy.Hierarchy::EnumerateChildrenPtr_Injected
Unity.Hierarchy.Hierarchy::AddNode_Injected
Unity.Hierarchy.Hierarchy::Destroy
Unity.Hierarchy.Hierarchy::GetChildren_Injected
Unity.Hierarchy.Hierarchy::GetOrCreateProperty_Injected
Unity.Hierarchy.Hierarchy::GetParent_Injected
Unity.Hierarchy.Hierarchy::SetPropertyRaw_Injected
Unity.Hierarchy.Hierarchy::SetSortIndex_Injected
Unity.Hierarchy.Hierarchy::SortChildren_Injected
Unity.Hierarchy.Hierarchy::Update_Injected
Unity.Hierarchy.Hierarchy::GetPropertyRaw_Injected
Unity.Hierarchy.HierarchyCommandList::Destroy
Unity.Hierarchy.HierarchyFlattened::Contains_Injected
Unity.Hierarchy.HierarchyFlattened::get_UpdateNeeded_Injected
Unity.Hierarchy.HierarchyFlattened::GetChildrenCount_Injected
Unity.Hierarchy.HierarchyFlattened::IndexOf_Injected
Unity.Hierarchy.HierarchyFlattened::Create_Injected
Unity.Hierarchy.HierarchyFlattened::Destroy
Unity.Hierarchy.HierarchyFlattened::Update_Injected
Unity.Hierarchy.HierarchyNodeTypeHandlerBase::ChangesPending_Injected
Unity.Hierarchy.HierarchyNodeTypeHandlerBase::IntegrateChanges_Injected
Unity.Hierarchy.HierarchyNodeTypeHandlerBase::SearchMatch_Injected
Unity.Hierarchy.HierarchyNodeTypeHandlerBase::GetNodeTypeName_Injected
Unity.Hierarchy.HierarchyNodeTypeHandlerBase::SearchBegin_Injected
Unity.Hierarchy.HierarchyNodeTypeHandlerBase::SearchEnd_Injected
Unity.Hierarchy.HierarchyNodeTypeHandlerBase::GetDefaultNodeFlags_Injected
Unity.Hierarchy.HierarchyViewModel::Contains_Injected
Unity.Hierarchy.HierarchyViewModel::HasAllFlagsNode_Injected
Unity.Hierarchy.HierarchyViewModel::get_UpdateNeeded_Injected
Unity.Hierarchy.HierarchyViewModel::GetChildrenCount_Injected
Unity.Hierarchy.HierarchyViewModel::IndexOf_Injected
Unity.Hierarchy.HierarchyViewModel::Create_Injected
Unity.Hierarchy.HierarchyViewModel::ClearFlagsNode_Injected
Unity.Hierarchy.HierarchyViewModel::Destroy
Unity.Hierarchy.HierarchyViewModel::SetFlagsNode_Injected
Unity.Hierarchy.HierarchyViewModel::Update_Injected
Unity.Hierarchy.HierarchyViewModel::get_Query_Injected
Unity.IntegerTime.RationalTimeExtensions::Convert_Injected
Unity.Jobs.JobHandle::CombineDependenciesInternalPtr_Injected
Unity.Jobs.JobHandle::ScheduleBatchedJobs
Unity.Jobs.JobHandle::ScheduleBatchedJobsAndComplete
Unity.Jobs.LowLevel.Unsafe.JobsUtility::GetWorkStealingRange
Unity.Jobs.LowLevel.Unsafe.JobsUtility::get_IsExecutingJob
Unity.Jobs.LowLevel.Unsafe.JobsUtility::get_ThreadIndex
Unity.Jobs.LowLevel.Unsafe.JobsUtility::get_ThreadIndexCount
Unity.Jobs.LowLevel.Unsafe.JobsUtility::CreateJobReflectionData
Unity.Jobs.LowLevel.Unsafe.JobsUtility::ScheduleParallelFor_Injected
Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::CreateMarker_Injected
Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::CreateMarker__Unmanaged
Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::CreateCategory__Unmanaged
Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::BeginSample
Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::EndSample
Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::GetCategoryDescription_Injected
Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::SetMarkerMetadata__Unmanaged
Unity.Profiling.LowLevel.Unsafe.ProfilerUnsafeUtility::CreateCounterValue__Unmanaged
UnityEngine.Android.AndroidApplication::get_UnityPlayerRaw
UnityEngine.Android.AndroidGame::StopLoading
UnityEngine.AndroidJNI::CallBooleanMethodUnsafe
UnityEngine.AndroidJNI::CallStaticBooleanMethodUnsafe
UnityEngine.AndroidJNI::GetBooleanArrayElement
UnityEngine.AndroidJNI::GetBooleanField
UnityEngine.AndroidJNI::GetStaticBooleanField
UnityEngine.AndroidJNI::IsAssignableFrom
UnityEngine.AndroidJNI::IsInstanceOf
UnityEngine.AndroidJNI::IsSameObject
UnityEngine.AndroidJNI::CallCharMethodUnsafe
UnityEngine.AndroidJNI::CallStaticCharMethodUnsafe
UnityEngine.AndroidJNI::GetCharArrayElement
UnityEngine.AndroidJNI::GetCharField
UnityEngine.AndroidJNI::GetStaticCharField
UnityEngine.AndroidJNI::FromCharArray
UnityEngine.AndroidJNI::CallDoubleMethodUnsafe
UnityEngine.AndroidJNI::CallStaticDoubleMethodUnsafe
UnityEngine.AndroidJNI::GetDoubleArrayElement
UnityEngine.AndroidJNI::GetDoubleField
UnityEngine.AndroidJNI::GetStaticDoubleField
UnityEngine.AndroidJNI::FromDoubleArray
UnityEngine.AndroidJNI::CallShortMethodUnsafe
UnityEngine.AndroidJNI::CallStaticShortMethodUnsafe
UnityEngine.AndroidJNI::GetShortArrayElement
UnityEngine.AndroidJNI::GetShortField
UnityEngine.AndroidJNI::GetStaticShortField
UnityEngine.AndroidJNI::FromShortArray
UnityEngine.AndroidJNI::AttachCurrentThread
UnityEngine.AndroidJNI::CallIntMethodUnsafe
UnityEngine.AndroidJNI::CallStaticIntMethodUnsafe
UnityEngine.AndroidJNI::DetachCurrentThread
UnityEngine.AndroidJNI::EnsureLocalCapacity
UnityEngine.AndroidJNI::GetArrayLength
UnityEngine.AndroidJNI::GetIntArrayElement
UnityEngine.AndroidJNI::GetIntField
UnityEngine.AndroidJNI::GetStaticIntField
UnityEngine.AndroidJNI::GetStringLength
UnityEngine.AndroidJNI::GetStringUTFLength
UnityEngine.AndroidJNI::GetVersion
UnityEngine.AndroidJNI::PushLocalFrame
UnityEngine.AndroidJNI::RegisterNativesAndFree
UnityEngine.AndroidJNI::Throw
UnityEngine.AndroidJNI::ThrowNew_Injected
UnityEngine.AndroidJNI::UnregisterNatives
UnityEngine.AndroidJNI::FromIntArray
UnityEngine.AndroidJNI::CallLongMethodUnsafe
UnityEngine.AndroidJNI::CallStaticLongMethodUnsafe
UnityEngine.AndroidJNI::GetDirectBufferCapacity
UnityEngine.AndroidJNI::GetLongArrayElement
UnityEngine.AndroidJNI::GetLongField
UnityEngine.AndroidJNI::GetStaticLongField
UnityEngine.AndroidJNI::FromLongArray
UnityEngine.AndroidJNI::AllocObject
UnityEngine.AndroidJNI::CallObjectMethodUnsafe
UnityEngine.AndroidJNI::CallStaticObjectMethodUnsafe
UnityEngine.AndroidJNI::ExceptionOccurred
UnityEngine.AndroidJNI::FindClass_Injected
UnityEngine.AndroidJNI::FromReflectedField
UnityEngine.AndroidJNI::FromReflectedMethod
UnityEngine.AndroidJNI::GetFieldID_Injected
UnityEngine.AndroidJNI::GetJavaVM
UnityEngine.AndroidJNI::GetMethodID_Injected
UnityEngine.AndroidJNI::GetObjectArrayElement
UnityEngine.AndroidJNI::GetObjectClass
UnityEngine.AndroidJNI::GetObjectField
UnityEngine.AndroidJNI::GetStaticFieldID_Injected
UnityEngine.AndroidJNI::GetStaticMethodID_Injected
UnityEngine.AndroidJNI::GetStaticObjectField
UnityEngine.AndroidJNI::GetSuperclass
UnityEngine.AndroidJNI::NewBooleanArray
UnityEngine.AndroidJNI::NewCharArray
UnityEngine.AndroidJNI::NewDirectByteBuffer
UnityEngine.AndroidJNI::NewDoubleArray
UnityEngine.AndroidJNI::NewFloatArray
UnityEngine.AndroidJNI::NewGlobalRef
UnityEngine.AndroidJNI::NewIntArray
UnityEngine.AndroidJNI::NewLocalRef
UnityEngine.AndroidJNI::NewLongArray
UnityEngine.AndroidJNI::NewObjectA
UnityEngine.AndroidJNI::NewObjectArray
UnityEngine.AndroidJNI::NewSByteArray
UnityEngine.AndroidJNI::NewShortArray
UnityEngine.AndroidJNI::NewStringFromStr_Injected
UnityEngine.AndroidJNI::NewStringUTF_Injected
UnityEngine.AndroidJNI::NewString_Injected
UnityEngine.AndroidJNI::NewWeakGlobalRef
UnityEngine.AndroidJNI::PopLocalFrame
UnityEngine.AndroidJNI::RegisterNativesAllocate
UnityEngine.AndroidJNI::ToBooleanArray_Injected
UnityEngine.AndroidJNI::ToByteArray_Injected
UnityEngine.AndroidJNI::ToCharArray
UnityEngine.AndroidJNI::ToDoubleArray
UnityEngine.AndroidJNI::ToFloatArray
UnityEngine.AndroidJNI::ToIntArray
UnityEngine.AndroidJNI::ToLongArray
UnityEngine.AndroidJNI::ToObjectArray
UnityEngine.AndroidJNI::ToReflectedField
UnityEngine.AndroidJNI::ToReflectedMethod
UnityEngine.AndroidJNI::ToSByteArray
UnityEngine.AndroidJNI::ToShortArray
UnityEngine.AndroidJNI::CallSByteMethodUnsafe
UnityEngine.AndroidJNI::CallStaticSByteMethodUnsafe
UnityEngine.AndroidJNI::GetSByteArrayElement
UnityEngine.AndroidJNI::GetSByteField
UnityEngine.AndroidJNI::GetStaticSByteField
UnityEngine.AndroidJNI::GetDirectBufferAddress
UnityEngine.AndroidJNI::FromSByteArray
UnityEngine.AndroidJNI::CallFloatMethodUnsafe
UnityEngine.AndroidJNI::CallStaticFloatMethodUnsafe
UnityEngine.AndroidJNI::GetFloatArrayElement
UnityEngine.AndroidJNI::GetFloatField
UnityEngine.AndroidJNI::GetStaticFloatField
UnityEngine.AndroidJNI::FromFloatArray
UnityEngine.AndroidJNI::GetQueueGlobalRefsCount
UnityEngine.AndroidJNI::CallStaticStringMethodUnsafeInternal_Injected
UnityEngine.AndroidJNI::CallStaticVoidMethodUnsafe
UnityEngine.AndroidJNI::CallStringMethodUnsafeInternal_Injected
UnityEngine.AndroidJNI::CallVoidMethodUnsafe
UnityEngine.AndroidJNI::DeleteGlobalRef
UnityEngine.AndroidJNI::DeleteLocalRef
UnityEngine.AndroidJNI::DeleteWeakGlobalRef
UnityEngine.AndroidJNI::ExceptionClear
UnityEngine.AndroidJNI::ExceptionDescribe
UnityEngine.AndroidJNI::FatalError_Injected
UnityEngine.AndroidJNI::FromBooleanArray_Injected
UnityEngine.AndroidJNI::FromByteArray_Injected
UnityEngine.AndroidJNI::FromObjectArray_Injected
UnityEngine.AndroidJNI::GetStaticStringFieldInternal_Injected
UnityEngine.AndroidJNI::GetStringCharsInternal_Injected
UnityEngine.AndroidJNI::GetStringFieldInternal_Injected
UnityEngine.AndroidJNI::GetStringUTFChars_Injected
UnityEngine.AndroidJNI::InvokeAttached
UnityEngine.AndroidJNI::QueueDeleteGlobalRef
UnityEngine.AndroidJNI::RegisterNativesSet_Injected
UnityEngine.AndroidJNI::ReleaseStringChars_Injected
UnityEngine.AndroidJNI::SetBooleanArrayElement
UnityEngine.AndroidJNI::SetBooleanField
UnityEngine.AndroidJNI::SetCharArrayElement
UnityEngine.AndroidJNI::SetCharField
UnityEngine.AndroidJNI::SetDoubleArrayElement
UnityEngine.AndroidJNI::SetDoubleField
UnityEngine.AndroidJNI::SetFloatArrayElement
UnityEngine.AndroidJNI::SetFloatField
UnityEngine.AndroidJNI::SetIntArrayElement
UnityEngine.AndroidJNI::SetIntField
UnityEngine.AndroidJNI::SetLongArrayElement
UnityEngine.AndroidJNI::SetLongField
UnityEngine.AndroidJNI::SetObjectArrayElement
UnityEngine.AndroidJNI::SetObjectField
UnityEngine.AndroidJNI::SetSByteArrayElement
UnityEngine.AndroidJNI::SetSByteField
UnityEngine.AndroidJNI::SetShortArrayElement
UnityEngine.AndroidJNI::SetShortField
UnityEngine.AndroidJNI::SetStaticBooleanField
UnityEngine.AndroidJNI::SetStaticCharField
UnityEngine.AndroidJNI::SetStaticDoubleField
UnityEngine.AndroidJNI::SetStaticFloatField
UnityEngine.AndroidJNI::SetStaticIntField
UnityEngine.AndroidJNI::SetStaticLongField
UnityEngine.AndroidJNI::SetStaticObjectField
UnityEngine.AndroidJNI::SetStaticSByteField
UnityEngine.AndroidJNI::SetStaticShortField
UnityEngine.AndroidJNI::SetStaticStringField_Injected
UnityEngine.AndroidJNI::SetStringField_Injected
UnityEngine.AndroidJNIHelper::get_debug
UnityEngine.AndroidJNIHelper::set_debug
UnityEngine.AnimationCurve::Internal_Equals_Injected
UnityEngine.AnimationCurve::GetHashCode_Injected
UnityEngine.AnimationCurve::Internal_Create_Injected
UnityEngine.AnimationCurve::Internal_Destroy
UnityEngine.Animations.AnimationLayerMixerPlayable::SetSingleLayerOptimizationInternal
UnityEngine.Animator::get_hasBoundPlayables_Injected
UnityEngine.Animator::StringToHash_Injected
UnityEngine.Animator::CrossFadeInFixedTime_Injected
UnityEngine.Animator::ResetTriggerString_Injected
UnityEngine.Animator::SetBoolID_Injected
UnityEngine.Animator::SetFloatIDDamp_Injected
UnityEngine.Animator::SetFloatID_Injected
UnityEngine.Animator::SetTriggerString_Injected
UnityEngine.Animator::get_rootPosition_Injected
UnityEngine.Animator::get_rootRotation_Injected
UnityEngine.Animator::set_updateMode_Injected
UnityEngine.Application::get_isBatchMode
UnityEngine.Application::get_isFocused
UnityEngine.Application::get_isPlaying
UnityEngine.Application::get_runInBackground
UnityEngine.Application::OpenURL_Injected
UnityEngine.Application::get_platform
UnityEngine.AudioClip::get_length_Injected
UnityEngine.AudioSettings::StartAudioOutput
UnityEngine.AudioSettings::StopAudioOutput
UnityEngine.AudioSource::PlayOneShotHelper_Injected
UnityEngine.Awaitable::IsNativeAwaitableCompleted
UnityEngine.Awaitable::ReleaseNativeAwaitable
UnityEngine.Behaviour::get_enabled_Injected
UnityEngine.Behaviour::get_isActiveAndEnabled_Injected
UnityEngine.Behaviour::set_enabled_Injected
UnityEngine.Bindings.BindingsAllocator::Free
UnityEngine.Bindings.BindingsAllocator::FreeNativeOwnedMemory
UnityEngine.Camera::get_stereoEnabled_Injected
UnityEngine.Camera::GetAllCamerasCount
UnityEngine.Camera::GetAllCamerasImpl_Injected
UnityEngine.Camera::get_cullingMask_Injected
UnityEngine.Camera::get_eventMask_Injected
UnityEngine.Camera::get_targetDisplay_Injected
UnityEngine.Camera::get_currentInternal_Injected
UnityEngine.Camera::get_main_Injected
UnityEngine.Camera::get_targetTexture_Injected
UnityEngine.Camera::get_aspect_Injected
UnityEngine.Camera::get_depth_Injected
UnityEngine.Camera::get_farClipPlane_Injected
UnityEngine.Camera::get_fieldOfView_Injected
UnityEngine.Camera::get_nearClipPlane_Injected
UnityEngine.Camera::ScreenPointToRay_Injected
UnityEngine.Camera::ScreenToViewportPoint_Injected
UnityEngine.Camera::SetupCurrent_Injected
UnityEngine.Camera::WorldToScreenPoint_Injected
UnityEngine.Camera::get_pixelRect_Injected
UnityEngine.Camera::set_rect_Injected
UnityEngine.Camera::get_clearFlags_Injected
UnityEngine.CameraRaycastHelper::RaycastTry2D_Injected
UnityEngine.CameraRaycastHelper::RaycastTry_Injected
UnityEngine.Canvas::get_isRootCanvas_Injected
UnityEngine.Canvas::get_overrideSorting_Injected
UnityEngine.Canvas::get_pixelPerfect_Injected
UnityEngine.Canvas::get_renderOrder_Injected
UnityEngine.Canvas::get_sortingLayerID_Injected
UnityEngine.Canvas::get_sortingOrder_Injected
UnityEngine.Canvas::get_targetDisplay_Injected
UnityEngine.Canvas::GetDefaultCanvasMaterial_Injected
UnityEngine.Canvas::GetETC1SupportedCanvasMaterial_Injected
UnityEngine.Canvas::get_rootCanvas_Injected
UnityEngine.Canvas::get_worldCamera_Injected
UnityEngine.Canvas::get_referencePixelsPerUnit_Injected
UnityEngine.Canvas::get_scaleFactor_Injected
UnityEngine.Canvas::SetExternalCanvasEnabled
UnityEngine.Canvas::get_pixelRect_Injected
UnityEngine.Canvas::get_renderingDisplaySize_Injected
UnityEngine.Canvas::set_overrideSorting_Injected
UnityEngine.Canvas::set_referencePixelsPerUnit_Injected
UnityEngine.Canvas::set_renderMode_Injected
UnityEngine.Canvas::set_scaleFactor_Injected
UnityEngine.Canvas::set_sortingLayerID_Injected
UnityEngine.Canvas::set_sortingOrder_Injected
UnityEngine.Canvas::get_renderMode_Injected
UnityEngine.CanvasGroup::get_blocksRaycasts_Injected
UnityEngine.CanvasGroup::get_ignoreParentGroups_Injected
UnityEngine.CanvasGroup::get_interactable_Injected
UnityEngine.CanvasGroup::get_alpha_Injected
UnityEngine.CanvasGroup::set_alpha_Injected
UnityEngine.CanvasGroup::set_ignoreParentGroups_Injected
UnityEngine.CanvasRenderer::get_cull_Injected
UnityEngine.CanvasRenderer::get_hasMoved_Injected
UnityEngine.CanvasRenderer::get_absoluteDepth_Injected
UnityEngine.CanvasRenderer::get_materialCount_Injected
UnityEngine.CanvasRenderer::Clear_Injected
UnityEngine.CanvasRenderer::CreateUIVertexStreamInternal
UnityEngine.CanvasRenderer::DisableRectClipping_Injected
UnityEngine.CanvasRenderer::EnableRectClipping_Injected
UnityEngine.CanvasRenderer::GetColor_Injected
UnityEngine.CanvasRenderer::SetAlphaTexture_Injected
UnityEngine.CanvasRenderer::SetColor_Injected
UnityEngine.CanvasRenderer::SetMaterial_Injected
UnityEngine.CanvasRenderer::SetMesh_Injected
UnityEngine.CanvasRenderer::SetPopMaterial_Injected
UnityEngine.CanvasRenderer::SetTexture_Injected
UnityEngine.CanvasRenderer::SplitIndicesStreamsInternal
UnityEngine.CanvasRenderer::SplitUIVertexStreamsInternal
UnityEngine.CanvasRenderer::set_clippingSoftness_Injected
UnityEngine.CanvasRenderer::set_cull_Injected
UnityEngine.CanvasRenderer::set_hasPopInstruction_Injected
UnityEngine.CanvasRenderer::set_materialCount_Injected
UnityEngine.CanvasRenderer::set_popMaterialCount_Injected
UnityEngine.CapsuleCollider::get_height_Injected
UnityEngine.CapsuleCollider::get_radius_Injected
UnityEngine.CapsuleCollider::get_center_Injected
UnityEngine.Collider::get_isTrigger_Injected
UnityEngine.Collider::set_material_Injected
UnityEngine.ColorUtility::DoTryParseHtmlColor_Injected
UnityEngine.Component::get_gameObject_Injected
UnityEngine.Component::get_transform_Injected
UnityEngine.Component::BroadcastMessage_Injected
UnityEngine.Component::GetComponentFastPath_Injected
UnityEngine.Component::GetComponentsForListInternal_Injected
UnityEngine.Component::SendMessage_Injected
UnityEngine.ComputeShader::FindKernel_Injected
UnityEngine.ContactFilter2D::CheckConsistency
UnityEngine.Coroutine::ReleaseCoroutine
UnityEngine.Cubemap::Internal_CreateImpl
UnityEngine.Cubemap::get_isReadable_Injected
UnityEngine.CubemapArray::Internal_CreateImpl
UnityEngine.CubemapArray::get_isReadable_Injected
UnityEngine.Cursor::SetCursor_Injected
UnityEngine.Cursor::get_lockState
UnityEngine.Debug::get_isDebugBuild
UnityEngine.Debug::ExtractStackTraceNoAlloc_Injected
UnityEngine.DebugLogHandler::Internal_LogException_Injected
UnityEngine.DebugLogHandler::Internal_Log_Injected
UnityEngine.Display::RelativeMouseAtImpl
UnityEngine.Display::GetRenderingExtImpl
UnityEngine.Display::GetSystemExtImpl
UnityEngine.Event::PopEvent_Injected
UnityEngine.Event::get_character_Injected
UnityEngine.Event::GetDoubleClickTime
UnityEngine.Event::GetEventCount
UnityEngine.Event::get_button_Injected
UnityEngine.Event::get_clickCount_Injected
UnityEngine.Event::get_displayIndex_Injected
UnityEngine.Event::Internal_Create
UnityEngine.Event::get_pressure_Injected
UnityEngine.Event::get_twist_Injected
UnityEngine.Event::CopyFromPtr_Injected
UnityEngine.Event::GetEventAtIndex_Injected
UnityEngine.Event::Internal_Destroy
UnityEngine.Event::Internal_SetNativeEvent
UnityEngine.Event::Internal_Use_Injected
UnityEngine.Event::get_commandName_Injected
UnityEngine.Event::get_delta_Injected
UnityEngine.Event::get_mousePosition_Injected
UnityEngine.Event::get_tilt_Injected
UnityEngine.Event::set_Internal_keyCode_Injected
UnityEngine.Event::set_character_Injected
UnityEngine.Event::set_commandName_Injected
UnityEngine.Event::set_delta_Injected
UnityEngine.Event::set_displayIndex_Injected
UnityEngine.Event::set_modifiers_Injected
UnityEngine.Event::set_mousePosition_Injected
UnityEngine.Event::set_type_Injected
UnityEngine.Event::get_modifiers_Injected
UnityEngine.Event::get_rawType_Injected
UnityEngine.Event::get_type_Injected
UnityEngine.Event::get_Internal_keyCode_Injected
UnityEngine.Event::get_penStatus_Injected
UnityEngine.Event::get_pointerType_Injected
UnityEngine.Experimental.Rendering.BuiltinRuntimeReflectionSystem::BuiltinUpdate
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::CanDecompressFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsCompressedFormat_Native_TextureFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsCrunchFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsDepthStencilFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsPVRTCFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::IsSRGBFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetDepthStencilFormatFromBitsLegacy_Native
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetGraphicsFormat_Native_RenderTextureFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetGraphicsFormat_Native_TextureFormat
UnityEngine.Experimental.Rendering.GraphicsFormatUtility::GetRenderTextureFormat
UnityEngine.Experimental.Rendering.ScriptableRuntimeReflectionSystemSettings::ScriptingDirtyReflectionSystemInstance
UnityEngine.Font::HasCharacter_Injected
UnityEngine.Font::get_dynamic_Injected
UnityEngine.Font::get_fontSize_Injected
UnityEngine.Font::get_material_Injected
UnityEngine.Font::GetOSFallbacks
UnityEngine.GameObject::GetComponentsInternal_Injected
UnityEngine.GameObject::CompareTag_Internal_Injected
UnityEngine.GameObject::get_activeInHierarchy_Injected
UnityEngine.GameObject::get_activeSelf_Injected
UnityEngine.GameObject::get_layer_Injected
UnityEngine.GameObject::Find_Injected
UnityEngine.GameObject::GetComponentInChildren_Injected
UnityEngine.GameObject::GetComponentInParent_Injected
UnityEngine.GameObject::GetComponent_Injected
UnityEngine.GameObject::Internal_AddComponentWithType_Injected
UnityEngine.GameObject::TryGetComponentInternal_Injected
UnityEngine.GameObject::get_transform_Injected
UnityEngine.GameObject::GetComponentFastPath_Injected
UnityEngine.GameObject::Internal_CreateGameObject_Injected
UnityEngine.GameObject::SendMessage_Injected
UnityEngine.GameObject::SetActive_Injected
UnityEngine.GameObject::TryGetComponentFastPath_Injected
UnityEngine.GameObject::set_layer_Injected
UnityEngine.Gizmos::DrawLine_Injected
UnityEngine.Gizmos::set_color_Injected
UnityEngine.Gizmos::set_matrix_Injected
UnityEngine.GL::Begin
UnityEngine.GL::End
UnityEngine.GL::GLClear_Injected
UnityEngine.GL::GLLoadPixelMatrixScript
UnityEngine.GL::ImmediateColor
UnityEngine.GL::LoadOrtho
UnityEngine.GL::LoadProjectionMatrix_Injected
UnityEngine.GL::PopMatrix
UnityEngine.GL::PushMatrix
UnityEngine.GL::SetViewMatrix_Injected
UnityEngine.GL::TexCoord3
UnityEngine.GL::Vertex3
UnityEngine.GL::Viewport_Injected
UnityEngine.Gradient::Internal_Equals_Injected
UnityEngine.Gradient::Init
UnityEngine.Gradient::Cleanup_Injected
UnityEngine.Graphics::Internal_GetMaxDrawMeshInstanceCount
UnityEngine.Graphics::Internal_SetNullRT
UnityEngine.Graphics::Internal_SetRTSimple_Injected
UnityEngine.GUI::get_changed
UnityEngine.GUI::get_enabled
UnityEngine.GUI::get_backgroundColor_Injected
UnityEngine.GUI::get_color_Injected
UnityEngine.GUI::get_contentColor_Injected
UnityEngine.GUI::set_backgroundColor_Injected
UnityEngine.GUI::set_changed
UnityEngine.GUI::set_color_Injected
UnityEngine.GUI::set_contentColor_Injected
UnityEngine.GUI::set_enabled
UnityEngine.GUIClip::Internal_GetCount
UnityEngine.GUIClip::GetMatrix_Injected
UnityEngine.GUIClip::Internal_Pop
UnityEngine.GUIClip::Internal_PopParentClip
UnityEngine.GUIClip::Internal_PushParentClip_Injected
UnityEngine.GUIClip::SetMatrix_Injected
UnityEngine.GUIClip::get_visibleRect_Injected
UnityEngine.GUILayoutUtility::Internal_GetWindowRect_Injected
UnityEngine.GUILayoutUtility::Internal_MoveWindow_Injected
UnityEngine.GUIStyle::IsTooltipActive_Injected
UnityEngine.GUIStyle::get_richText_Injected
UnityEngine.GUIStyle::get_stretchHeight_Injected
UnityEngine.GUIStyle::get_stretchWidth_Injected
UnityEngine.GUIStyle::get_wordWrap_Injected
UnityEngine.GUIStyle::get_fontSize_Injected
UnityEngine.GUIStyle::GetDefaultFont_Injected
UnityEngine.GUIStyle::GetRectOffsetPtr_Injected
UnityEngine.GUIStyle::GetStyleStatePtr_Injected
UnityEngine.GUIStyle::Internal_Create
UnityEngine.GUIStyle::get_font_Injected
UnityEngine.GUIStyle::get_fixedHeight_Injected
UnityEngine.GUIStyle::get_fixedWidth_Injected
UnityEngine.GUIStyle::Internal_Destroy
UnityEngine.GUIStyle::Internal_DestroyTextGenerator
UnityEngine.GUIStyle::Internal_Draw2_Injected
UnityEngine.GUIStyle::Internal_Draw_Injected
UnityEngine.GUIStyle::Internal_GetTextRectOffset_Injected
UnityEngine.GUIStyle::SetDefaultFont_Injected
UnityEngine.GUIStyle::SetMouseTooltip_Injected
UnityEngine.GUIStyle::get_contentOffset_Injected
UnityEngine.GUIStyle::get_rawName_Injected
UnityEngine.GUIStyle::set_rawName_Injected
UnityEngine.GUIStyle::set_stretchHeight_Injected
UnityEngine.GUIStyle::get_fontStyle_Injected
UnityEngine.GUIStyle::get_imagePosition_Injected
UnityEngine.GUIStyle::get_alignment_Injected
UnityEngine.GUIStyle::get_clipping_Injected
UnityEngine.GUIStyleState::Init
UnityEngine.GUIStyleState::Cleanup_Injected
UnityEngine.GUIStyleState::set_textColor_Injected
UnityEngine.GUIUtility::HasFocusableControls
UnityEngine.GUIUtility::OwnsId
UnityEngine.GUIUtility::get_textFieldInput
UnityEngine.GUIUtility::CheckForTabEvent_Injected
UnityEngine.GUIUtility::Internal_GetControlID_Injected
UnityEngine.GUIUtility::Internal_GetHotControl
UnityEngine.GUIUtility::Internal_GetKeyboardControl
UnityEngine.GUIUtility::get_guiDepth
UnityEngine.GUIUtility::Internal_GetDefaultSkin
UnityEngine.GUIUtility::get_pixelsPerPoint
UnityEngine.GUIUtility::AlignRectToDevice_Injected
UnityEngine.GUIUtility::BeginContainerFromOwner_Injected
UnityEngine.GUIUtility::BeginContainer_Injected
UnityEngine.GUIUtility::Internal_EndContainer
UnityEngine.GUIUtility::Internal_ExitGUI
UnityEngine.GUIUtility::Internal_SetHotControl
UnityEngine.GUIUtility::Internal_SetKeyboardControl
UnityEngine.GUIUtility::SetKeyboardControlToFirstControlId
UnityEngine.GUIUtility::SetKeyboardControlToLastControlId
UnityEngine.GUIUtility::get_compositionString_Injected
UnityEngine.GUIUtility::get_systemCopyBuffer_Injected
UnityEngine.GUIUtility::set_compositionCursorPos_Injected
UnityEngine.GUIUtility::set_imeCompositionMode
UnityEngine.GUIUtility::set_pixelsPerPoint
UnityEngine.GUIUtility::set_systemCopyBuffer_Injected
UnityEngine.Hash128::Hash128ToStringImpl_Injected
UnityEngine.Hash128::Parse_Injected
UnityEngine.Input::CheckDisabled
UnityEngine.Input::GetKeyDownInt
UnityEngine.Input::GetKeyInt
UnityEngine.Input::GetKeyUpInt
UnityEngine.Input::GetMouseButton
UnityEngine.Input::GetMouseButtonDown
UnityEngine.Input::GetMouseButtonUp
UnityEngine.Input::GetMousePresentInternal
UnityEngine.Input::GetTouchSupportedInternal
UnityEngine.Input::get_anyKey
UnityEngine.Input::get_touchCount
UnityEngine.Input::ClearLastPenContactEvent
UnityEngine.Input::GetLastPenContactEvent_Injected
UnityEngine.Input::GetTouch_Injected
UnityEngine.Input::get_compositionCursorPos_Injected
UnityEngine.Input::get_compositionString_Injected
UnityEngine.Input::get_mousePosition_Injected
UnityEngine.Input::get_mouseScrollDelta_Injected
UnityEngine.Input::set_compositionCursorPos_Injected
UnityEngine.Input::set_imeCompositionMode
UnityEngine.Input::get_imeCompositionMode
UnityEngine.IntegratedSubsystem::SetHandle_Injected
UnityEngine.Internal.InputUnsafeUtility::GetButtonDown_Injected
UnityEngine.Internal.InputUnsafeUtility::GetButtonUp__Unmanaged
UnityEngine.Internal.InputUnsafeUtility::GetButton__Unmanaged
UnityEngine.Internal.InputUnsafeUtility::GetKeyDownString__Unmanaged
UnityEngine.Internal.InputUnsafeUtility::GetKeyString__Unmanaged
UnityEngine.Internal.InputUnsafeUtility::GetKeyUpString__Unmanaged
UnityEngine.Internal.InputUnsafeUtility::GetButtonDown__Unmanaged
UnityEngine.Internal.InputUnsafeUtility::GetAxisRaw_Injected
UnityEngine.Internal.InputUnsafeUtility::GetAxisRaw__Unmanaged
UnityEngine.Internal.InputUnsafeUtility::GetAxis_Injected
UnityEngine.Internal.InputUnsafeUtility::GetAxis__Unmanaged
UnityEngine.JsonUtility::FromJsonInternal_Injected
UnityEngine.JsonUtility::ToJsonInternal_Injected
UnityEngine.Light::get_useColorTemperature_Injected
UnityEngine.Light::get_cookie_Injected
UnityEngine.Light::get_bounceIntensity_Injected
UnityEngine.Light::get_colorTemperature_Injected
UnityEngine.Light::get_cookieSize_Injected
UnityEngine.Light::get_dilatedRange_Injected
UnityEngine.Light::get_intensity_Injected
UnityEngine.Light::get_range_Injected
UnityEngine.Light::get_spotAngle_Injected
UnityEngine.Light::get_bakingOutput_Injected
UnityEngine.Light::get_color_Injected
UnityEngine.Light::get_shadows_Injected
UnityEngine.Light::get_type_Injected
UnityEngine.Material::HasFloatImpl_Injected
UnityEngine.Material::HasProperty_Injected
UnityEngine.Material::SetPass_Injected
UnityEngine.Material::ComputeCRC_Injected
UnityEngine.Material::GetFirstPropertyNameIdByAttribute_Injected
UnityEngine.Material::GetTextureImpl_Injected
UnityEngine.Material::get_shader_Injected
UnityEngine.Material::GetFloatImpl_Injected
UnityEngine.Material::GetShaderKeywords_Injected
UnityEngine.Material::CopyPropertiesFromMaterial_Injected
UnityEngine.Material::CreateWithMaterial_Injected
UnityEngine.Material::CreateWithShader_Injected
UnityEngine.Material::CreateWithString
UnityEngine.Material::DisableKeyword_Injected
UnityEngine.Material::EnableKeyword_Injected
UnityEngine.Material::SetFloatImpl_Injected
UnityEngine.Material::SetShaderKeywords_Injected
UnityEngine.Material::SetTextureImpl_Injected
UnityEngine.Material::SetTextureOffsetImpl_Injected
UnityEngine.Material::set_shader_Injected
UnityEngine.MaterialPropertyBlock::CreateImpl
UnityEngine.MaterialPropertyBlock::Clear_Injected
UnityEngine.MaterialPropertyBlock::DestroyImpl
UnityEngine.MaterialPropertyBlock::SetTextureImpl_Injected
UnityEngine.MaterialPropertyBlock::SetVectorArrayImpl_Injected
UnityEngine.Mathf::GammaToLinearSpace
UnityEngine.Mathf::CorrelatedColorTemperatureToRGB_Injected
UnityEngine.Matrix4x4::Inverse3DAffine_Injected
UnityEngine.Matrix4x4::GetLossyScale_Injected
UnityEngine.Matrix4x4::GetRotation_Injected
UnityEngine.Matrix4x4::Inverse_Injected
UnityEngine.Matrix4x4::TRS_Injected
UnityEngine.Mesh::GetAllocArrayFromChannelImpl_Injected
UnityEngine.Mesh::HasVertexAttribute_Injected
UnityEngine.Mesh::get_canAccess_Injected
UnityEngine.Mesh::get_subMeshCount_Injected
UnityEngine.Mesh::get_vertexCount_Injected
UnityEngine.Mesh::ClearImpl_Injected
UnityEngine.Mesh::GetArrayFromChannelImpl_Injected
UnityEngine.Mesh::GetIndicesImpl_Injected
UnityEngine.Mesh::Internal_Create
UnityEngine.Mesh::PrintErrorCantAccessChannel_Injected
UnityEngine.Mesh::RecalculateBoundsImpl_Injected
UnityEngine.Mesh::SetArrayForChannelImpl_Injected
UnityEngine.Mesh::SetIndicesImpl_Injected
UnityEngine.MonoBehaviour::Internal_IsInvokingAll_Injected
UnityEngine.MonoBehaviour::IsInvoking_Injected
UnityEngine.MonoBehaviour::IsObjectMonoBehaviour_Injected
UnityEngine.MonoBehaviour::get_didAwake_Injected
UnityEngine.MonoBehaviour::get_didStart_Injected
UnityEngine.MonoBehaviour::get_useGUILayout_Injected
UnityEngine.MonoBehaviour::CancelInvoke_Injected
UnityEngine.MonoBehaviour::GetScriptClassName_Injected
UnityEngine.MonoBehaviour::Internal_CancelInvokeAll_Injected
UnityEngine.MonoBehaviour::InvokeDelayed_Injected
UnityEngine.MonoBehaviour::OnCancellationTokenCreated_Injected
UnityEngine.MonoBehaviour::StopAllCoroutines_Injected
UnityEngine.MonoBehaviour::StopCoroutineFromEnumeratorManaged_Injected
UnityEngine.MonoBehaviour::StopCoroutineManaged_Injected
UnityEngine.MonoBehaviour::StopCoroutine_Injected
UnityEngine.MonoBehaviour::set_useGUILayout_Injected
UnityEngine.MonoBehaviour::StartCoroutineManaged2_Injected
UnityEngine.MonoBehaviour::StartCoroutineManaged_Injected
UnityEngine.NameFormatter::FormatVariableName_Injected
UnityEngine.Object::IsPersistent_Injected
UnityEngine.Object::GetOffsetOfInstanceIDInCPlusPlusObject
UnityEngine.Object::FindObjectFromInstanceID_Injected
UnityEngine.Object::ForceLoadFromInstanceID_Injected
UnityEngine.Object::Internal_CloneSingle_Injected
UnityEngine.Object::DestroyImmediate_Injected
UnityEngine.Object::Destroy_Injected
UnityEngine.Object::GetName_Injected
UnityEngine.Object::SetName_Injected
UnityEngine.Object::ToString_Injected
UnityEngine.Object::set_hideFlags_Injected
UnityEngine.Object::get_hideFlags_Injected
UnityEngine.Object::FindObjectsByType
UnityEngine.Object::FindObjectsOfType
UnityEngine.ObjectGUIState::Internal_Create
UnityEngine.ObjectGUIState::Internal_Destroy
UnityEngine.Physics::get_invokeCollisionCallbacks
UnityEngine.Physics::get_reuseCollisionCallbacks
UnityEngine.Physics::GetBodyByInstanceID_Injected
UnityEngine.Physics::GetColliderByInstanceID_Injected
UnityEngine.Physics::Internal_RaycastAll_Injected
UnityEngine.Physics::Query_CapsuleCastAll_Injected
UnityEngine.Physics::SendOnCollisionEnter_Injected
UnityEngine.Physics::SendOnCollisionExit_Injected
UnityEngine.Physics::SendOnCollisionStay_Injected
UnityEngine.Physics::get_defaultPhysicsScene_Injected
UnityEngine.Physics2D::get_queriesHitTriggers
UnityEngine.Physics2D::GetRayIntersectionAll_Internal_Injected
UnityEngine.PhysicsMaterial::Internal_CreateDynamicsMaterial_Injected
UnityEngine.PhysicsMaterial::set_dynamicFriction_Injected
UnityEngine.PhysicsMaterial::set_frictionCombine_Injected
UnityEngine.PhysicsMaterial::set_staticFriction_Injected
UnityEngine.PhysicsScene::Internal_RaycastTest_Injected
UnityEngine.PhysicsScene::Internal_Raycast_Injected
UnityEngine.PhysicsScene::Query_SphereCast_Injected
UnityEngine.PhysicsScene::Internal_RaycastNonAlloc_Injected
UnityEngine.PhysicsScene2D::GetRayIntersectionArray_Internal_Injected
UnityEngine.PhysicsScene2D::RaycastArray_Internal_Injected
UnityEngine.PhysicsScene2D::RaycastList_Internal_Injected
UnityEngine.PhysicsScene2D::GetRayIntersection_Internal_Injected
UnityEngine.PhysicsScene2D::Raycast_Internal_Injected
UnityEngine.Playables.PlayableHandle::IsValid
UnityEngine.Playables.PlayableHandle::GetPlayableType
UnityEngine.PlayerConnectionInternal::IsConnected
UnityEngine.PlayerConnectionInternal::TrySendMessage_Injected
UnityEngine.PlayerConnectionInternal::DisconnectAll
UnityEngine.PlayerConnectionInternal::Initialize
UnityEngine.PlayerConnectionInternal::PollInternal
UnityEngine.PlayerConnectionInternal::RegisterInternal_Injected
UnityEngine.PlayerConnectionInternal::SendMessage_Injected
UnityEngine.PlayerConnectionInternal::UnregisterInternal_Injected
UnityEngine.PropertyNameUtils::PropertyNameFromString_Injected
UnityEngine.QualitySettings::get_activeColorSpace
UnityEngine.Quaternion::AngleAxis_Injected
UnityEngine.Quaternion::Internal_FromEulerRad_Injected
UnityEngine.Quaternion::Internal_ToAxisAngleRad_Injected
UnityEngine.Quaternion::Internal_ToEulerRad_Injected
UnityEngine.Quaternion::Inverse_Injected
UnityEngine.Quaternion::LookRotation_Injected
UnityEngine.Quaternion::Slerp_Injected
UnityEngine.RectOffset::get_bottom_Injected
UnityEngine.RectOffset::get_horizontal_Injected
UnityEngine.RectOffset::get_left_Injected
UnityEngine.RectOffset::get_right_Injected
UnityEngine.RectOffset::get_top_Injected
UnityEngine.RectOffset::get_vertical_Injected
UnityEngine.RectOffset::InternalCreate
UnityEngine.RectOffset::InternalDestroy
UnityEngine.RectOffset::Remove_Injected
UnityEngine.RectTransform::get_anchorMax_Injected
UnityEngine.RectTransform::get_anchorMin_Injected
UnityEngine.RectTransform::get_anchoredPosition_Injected
UnityEngine.RectTransform::get_pivot_Injected
UnityEngine.RectTransform::get_rect_Injected
UnityEngine.RectTransform::get_sizeDelta_Injected
UnityEngine.RectTransform::set_anchorMax_Injected
UnityEngine.RectTransform::set_anchorMin_Injected
UnityEngine.RectTransform::set_anchoredPosition_Injected
UnityEngine.RectTransform::set_pivot_Injected
UnityEngine.RectTransform::set_sizeDelta_Injected
UnityEngine.RectTransformUtility::PointInRectangle_Injected
UnityEngine.RectTransformUtility::PixelAdjustPoint_Injected
UnityEngine.RectTransformUtility::PixelAdjustRect_Injected
UnityEngine.Renderer::get_sortingGroupID_Injected
UnityEngine.Renderer::get_sortingGroupOrder_Injected
UnityEngine.Renderer::get_sortingLayerID_Injected
UnityEngine.Renderer::get_sortingOrder_Injected
UnityEngine.Renderer::GetMaterial_Injected
UnityEngine.Renderer::set_enabled_Injected
UnityEngine.Renderer::set_localBounds_Injected
UnityEngine.Rendering.GraphicsSettings::get_lightsUseLinearIntensity
UnityEngine.Rendering.GraphicsSettings::Internal_GetSettingsForRenderPipeline_Injected
UnityEngine.Rendering.GraphicsSettings::get_INTERNAL_currentRenderPipeline_Injected
UnityEngine.Rendering.ScriptableRenderContext::GetCameras_Internal
UnityEngine.Rendering.SortingGroup::get_invalidSortingGroupID
UnityEngine.Rendering.SortingGroup::get_sortingLayerID_Injected
UnityEngine.Rendering.SortingGroup::get_sortingOrder_Injected
UnityEngine.Rendering.SortingGroup::GetSortingGroupByIndex_Injected
UnityEngine.RenderTexture::get_height_Injected
UnityEngine.RenderTexture::get_width_Injected
UnityEngine.RenderTexture::GetActive_Injected
UnityEngine.RenderTexture::GetTemporary_Internal_Injected
UnityEngine.RenderTexture::GetColorBuffer_Injected
UnityEngine.RenderTexture::GetDepthBuffer_Injected
UnityEngine.RenderTexture::GetDescriptor_Injected
UnityEngine.RenderTexture::Internal_Create
UnityEngine.RenderTexture::ReleaseTemporary_Injected
UnityEngine.RenderTexture::SetActive_Injected
UnityEngine.RenderTexture::SetColorFormat_Injected
UnityEngine.RenderTexture::SetMipMapCount_Injected
UnityEngine.RenderTexture::SetRenderTextureDescriptor_Injected
UnityEngine.RenderTexture::SetSRGBReadWrite_Injected
UnityEngine.RenderTexture::SetShadowSamplingMode_Injected
UnityEngine.RenderTexture::set_depthStencilFormat_Injected
UnityEngine.RenderTexture::set_height_Injected
UnityEngine.RenderTexture::set_width_Injected
UnityEngine.Resources::GetBuiltinResource_Injected
UnityEngine.ResourcesAPIInternal::FindShaderByName_Injected
UnityEngine.ResourcesAPIInternal::Load_Injected
UnityEngine.ResourcesAPIInternal::FindObjectsOfTypeAll
UnityEngine.Rigidbody::AddForce_Injected
UnityEngine.Rigidbody::get_linearVelocity_Injected
UnityEngine.Rigidbody::get_position_Injected
UnityEngine.Rigidbody::set_linearVelocity_Injected
UnityEngine.Screen::get_fullScreen
UnityEngine.Screen::get_height
UnityEngine.Screen::get_width
UnityEngine.Screen::get_dpi
UnityEngine.Screen::GetScreenOrientation
UnityEngine.ScriptableObject::CreateScriptableObjectInstanceFromType_Injected
UnityEngine.ScriptableObject::CreateScriptableObject
UnityEngine.ScriptingRuntime::GetAllUserAssemblies
UnityEngine.Shader::PropertyToID_Injected
UnityEngine.Shader::TagToID_Injected
UnityEngine.Shader::set_globalRenderPipeline_Injected
UnityEngine.SortingLayer::GetLayerValueFromID
UnityEngine.Sprite::GetPacked_Injected
UnityEngine.Sprite::GetPackingRotation_Injected
UnityEngine.Sprite::CreateSprite_Injected
UnityEngine.Sprite::get_associatedAlphaSplitTexture_Injected
UnityEngine.Sprite::get_texture_Injected
UnityEngine.Sprite::get_pixelsPerUnit_Injected
UnityEngine.Sprite::get_triangles_Injected
UnityEngine.Sprite::GetInnerUVs_Injected
UnityEngine.Sprite::GetOuterUVs_Injected
UnityEngine.Sprite::GetPadding_Injected
UnityEngine.Sprite::get_border_Injected
UnityEngine.Sprite::get_bounds_Injected
UnityEngine.Sprite::get_pivot_Injected
UnityEngine.Sprite::get_rect_Injected
UnityEngine.Sprite::get_uv_Injected
UnityEngine.Sprite::get_vertices_Injected
UnityEngine.SubsystemDescriptorBindings::GetId_Injected
UnityEngine.SubsystemManager::StaticConstructScriptingClassMap
UnityEngine.SubsystemsImplementation.SubsystemDescriptorStore::ReportSingleSubsystemAnalytics_Injected
UnityEngine.SystemInfo::IsFormatSupported
UnityEngine.SystemInfo::SupportsTextureFormatNative
UnityEngine.SystemInfo::GetMaxRenderTextureSize
UnityEngine.SystemInfo::GetMaxTextureSize
UnityEngine.SystemInfo::GetDeviceModel_Injected
UnityEngine.SystemInfo::GetCompatibleFormat
UnityEngine.SystemInfo::GetGraphicsFormat
UnityEngine.SystemInfo::GetOperatingSystemFamily
UnityEngine.Terrain::get_allowAutoConnect_Injected
UnityEngine.Terrain::get_groupingID_Injected
UnityEngine.Terrain::get_terrainData_Injected
UnityEngine.Terrain::SetNeighbors_Injected
UnityEngine.Terrain::get_activeTerrains
UnityEngine.TerrainData::GetBoundaryValue
UnityEngine.TerrainData::GetAlphamapResolutionInternal_Injected
UnityEngine.TerrainData::get_size_Injected
UnityEngine.TerrainData::get_users_Injected
UnityEngine.TextAsset::get_bytes_Injected
UnityEngine.TextCore.LowLevel.FontEngine::TryAddGlyphToTexture_Internal_Injected
UnityEngine.TextCore.LowLevel.FontEngine::TryAddGlyphsToTexture_Internal_Injected
UnityEngine.TextCore.LowLevel.FontEngine::TryGetGlyphWithUnicodeValue_Internal
UnityEngine.TextCore.LowLevel.FontEngine::TryGetSystemFontReference_Internal_Injected
UnityEngine.TextCore.LowLevel.FontEngine::GetFaceInfo_Internal
UnityEngine.TextCore.LowLevel.FontEngine::GetLigatureSubstitutionRecordsFromMarshallingArray
UnityEngine.TextCore.LowLevel.FontEngine::GetMarkToBaseAdjustmentRecordsFromMarshallingArray_Injected
UnityEngine.TextCore.LowLevel.FontEngine::GetMarkToMarkAdjustmentRecordsFromMarshallingArray_Injected
UnityEngine.TextCore.LowLevel.FontEngine::GetPairAdjustmentRecordsFromMarshallingArray_Injected
UnityEngine.TextCore.LowLevel.FontEngine::LoadFontFace_With_Size_And_FaceIndex_Internal_Injected
UnityEngine.TextCore.LowLevel.FontEngine::LoadFontFace_With_Size_and_FaceIndex_FromFont_Internal_Injected
UnityEngine.TextCore.LowLevel.FontEngine::LoadFontFace_With_Size_by_FamilyName_and_StyleName_Internal_Injected
UnityEngine.TextCore.LowLevel.FontEngine::PopulateLigatureSubstitutionRecordMarshallingArray_Injected
UnityEngine.TextCore.LowLevel.FontEngine::PopulateMarkToBaseAdjustmentRecordMarshallingArray_Injected
UnityEngine.TextCore.LowLevel.FontEngine::PopulateMarkToMarkAdjustmentRecordMarshallingArray_Injected
UnityEngine.TextCore.LowLevel.FontEngine::PopulatePairAdjustmentRecordMarshallingArray_Injected
UnityEngine.TextCore.LowLevel.FontEngine::GetGlyphIndex
UnityEngine.TextCore.LowLevel.FontEngine::GetVariantGlyphIndex
UnityEngine.TextCore.LowLevel.FontEngine::GetAllMarkToBaseAdjustmentRecords_Injected
UnityEngine.TextCore.LowLevel.FontEngine::GetAllMarkToMarkAdjustmentRecords_Injected
UnityEngine.TextCore.LowLevel.FontEngine::GetAllPairAdjustmentRecords_Injected
UnityEngine.TextCore.LowLevel.FontEngine::ResetAtlasTexture_Injected
UnityEngine.TextCore.LowLevel.FontEngine::SetTextureUploadMode
UnityEngine.TextCore.LowLevel.FontEngine::GetAllLigatureSubstitutionRecords
UnityEngine.TextCore.Text.FontAsset::Create_Injected
UnityEngine.TextCore.Text.FontAsset::Destroy
UnityEngine.TextCore.Text.FontAsset::UpdateFaceInfo_Injected
UnityEngine.TextCore.Text.FontAsset::UpdateFallbacks_Injected
UnityEngine.TextCore.Text.FontAsset::UpdateWeightFallbacks_Injected
UnityEngine.TextCore.Text.TextGenerationInfo::Create
UnityEngine.TextCore.Text.TextGenerationInfo::Destroy
UnityEngine.TextCore.Text.TextLib::FindIntersectingLink_Injected
UnityEngine.TextCore.Text.TextLib::GetInstance_Injected
UnityEngine.TextCore.Text.TextLib::GenerateTextInternal_Injected
UnityEngine.TextCore.Text.TextLib::MeasureText_Injected
UnityEngine.TextCore.Text.TextSelectionService::GetCursorLogicalIndexFromPosition_Injected
UnityEngine.TextCore.Text.TextSelectionService::GetEndOfPreviousWord
UnityEngine.TextCore.Text.TextSelectionService::GetFirstCharacterIndexOnLine
UnityEngine.TextCore.Text.TextSelectionService::GetLastCharacterIndexOnLine
UnityEngine.TextCore.Text.TextSelectionService::GetLineNumber
UnityEngine.TextCore.Text.TextSelectionService::GetStartOfNextWord
UnityEngine.TextCore.Text.TextSelectionService::LineDownCharacterPosition
UnityEngine.TextCore.Text.TextSelectionService::LineUpCharacterPosition
UnityEngine.TextCore.Text.TextSelectionService::NextCodePointIndex
UnityEngine.TextCore.Text.TextSelectionService::PreviousCodePointIndex
UnityEngine.TextCore.Text.TextSelectionService::GetCharacterHeightFromIndex
UnityEngine.TextCore.Text.TextSelectionService::GetLineHeight
UnityEngine.TextCore.Text.TextSelectionService::GetCursorPositionFromLogicalIndex_Injected
UnityEngine.TextCore.Text.TextSelectionService::GetHighlightRectangles_Injected
UnityEngine.TextCore.Text.TextSelectionService::SelectCurrentParagraph
UnityEngine.TextCore.Text.TextSelectionService::SelectCurrentWord
UnityEngine.TextCore.Text.TextSelectionService::SelectToEndOfParagraph
UnityEngine.TextCore.Text.TextSelectionService::SelectToNextParagraph
UnityEngine.TextCore.Text.TextSelectionService::SelectToPreviousParagraph
UnityEngine.TextCore.Text.TextSelectionService::SelectToStartOfParagraph
UnityEngine.TextCore.Text.TextSelectionService::Substring_Injected
UnityEngine.TextGenerator::Populate_Internal_Injected
UnityEngine.TextGenerator::get_characterCount_Injected
UnityEngine.TextGenerator::get_lineCount_Injected
UnityEngine.TextGenerator::Internal_Create
UnityEngine.TextGenerator::GetCharactersInternal_Injected
UnityEngine.TextGenerator::GetLinesInternal_Injected
UnityEngine.TextGenerator::GetVerticesInternal_Injected
UnityEngine.TextGenerator::Internal_Destroy
UnityEngine.TextGenerator::get_rectExtents_Injected
UnityEngine.Texture::get_isReadable_Injected
UnityEngine.Texture::GetDataHeight_Injected
UnityEngine.Texture::GetDataWidth_Injected
UnityEngine.Texture::Internal_GetActiveTextureColorSpace_Injected
UnityEngine.Texture::get_texelSize_Injected
UnityEngine.Texture::set_filterMode_Injected
UnityEngine.Texture::get_filterMode_Injected
UnityEngine.Texture::get_wrapMode_Injected
UnityEngine.Texture2D::Internal_CreateImpl_Injected
UnityEngine.Texture2D::ReinitializeImpl_Injected
UnityEngine.Texture2D::ReinitializeWithTextureFormatImpl_Injected
UnityEngine.Texture2D::get_isReadable_Injected
UnityEngine.Texture2D::GetWritableImageData_Injected
UnityEngine.Texture2D::get_whiteTexture_Injected
UnityEngine.Texture2D::GetImageDataSize_Injected
UnityEngine.Texture2D::ApplyImpl_Injected
UnityEngine.Texture2D::GetPixelBilinearImpl_Injected
UnityEngine.Texture2D::SetAllPixels32_Injected
UnityEngine.Texture2D::SetPixelImpl_Injected
UnityEngine.Texture2D::get_format_Injected
UnityEngine.Texture2DArray::Internal_CreateImpl_Injected
UnityEngine.Texture2DArray::get_isReadable_Injected
UnityEngine.Texture3D::Internal_CreateImpl
UnityEngine.Texture3D::get_isReadable_Injected
UnityEngine.Time::get_frameCount
UnityEngine.Time::get_deltaTime
UnityEngine.Time::get_fixedDeltaTime
UnityEngine.Time::get_fixedUnscaledTime
UnityEngine.Time::get_realtimeSinceStartup
UnityEngine.Time::get_unscaledDeltaTime
UnityEngine.Time::get_unscaledTime
UnityEngine.Time::get_timeAsRational_Injected
UnityEngine.TouchScreenKeyboard::IsInPlaceEditingAllowed
UnityEngine.TouchScreenKeyboard::get_active_Injected
UnityEngine.TouchScreenKeyboard::get_canGetSelection_Injected
UnityEngine.TouchScreenKeyboard::get_canSetSelection_Injected
UnityEngine.TouchScreenKeyboard::TouchScreenKeyboard_InternalConstructorHelper_Injected
UnityEngine.TouchScreenKeyboard::GetSelection
UnityEngine.TouchScreenKeyboard::Internal_Destroy
UnityEngine.TouchScreenKeyboard::SetSelection
UnityEngine.TouchScreenKeyboard::get_text_Injected
UnityEngine.TouchScreenKeyboard::set_active_Injected
UnityEngine.TouchScreenKeyboard::set_characterLimit_Injected
UnityEngine.TouchScreenKeyboard::set_hideInput
UnityEngine.TouchScreenKeyboard::set_text_Injected
UnityEngine.TouchScreenKeyboard::get_inputFieldAppearance
UnityEngine.TouchScreenKeyboard::get_status_Injected
UnityEngine.Transform::IsChildOf_Injected
UnityEngine.Transform::get_childCount_Injected
UnityEngine.Transform::FindRelativeTransformWithPath_Injected
UnityEngine.Transform::GetChild_Injected
UnityEngine.Transform::GetParent_Injected
UnityEngine.Transform::GetRoot_Injected
UnityEngine.Transform::InverseTransformDirection_Injected
UnityEngine.Transform::InverseTransformPoint_Injected
UnityEngine.Transform::SetAsFirstSibling_Injected
UnityEngine.Transform::SetLocalPositionAndRotation_Injected
UnityEngine.Transform::SetParent_Injected
UnityEngine.Transform::TransformPoint_Injected
UnityEngine.Transform::get_localPosition_Injected
UnityEngine.Transform::get_localRotation_Injected
UnityEngine.Transform::get_localScale_Injected
UnityEngine.Transform::get_localToWorldMatrix_Injected
UnityEngine.Transform::get_position_Injected
UnityEngine.Transform::get_rotation_Injected
UnityEngine.Transform::get_worldToLocalMatrix_Injected
UnityEngine.Transform::set_localPosition_Injected
UnityEngine.Transform::set_localRotation_Injected
UnityEngine.Transform::set_localScale_Injected
UnityEngine.Transform::set_position_Injected
UnityEngine.Transform::set_rotation_Injected
UnityEngine.U2D.SpriteAtlas::CanBindTo_Injected
UnityEngine.U2D.SpriteAtlasManager::Register_Injected
UnityEngine.UIElements.Layout.LayoutNative::CalculateLayout
UnityEngine.UIElements.MeshBuilderNative::MakeBorder_Injected
UnityEngine.UIElements.MeshBuilderNative::MakeSolidRect_Injected
UnityEngine.UIElements.MeshBuilderNative::MakeTexturedRect_Injected
UnityEngine.UIElements.MeshBuilderNative::MakeVectorGraphics9SliceBackground_Injected
UnityEngine.UIElements.MeshBuilderNative::MakeVectorGraphicsStretchBackground_Injected
UnityEngine.UIElements.UIElementsRuntimeUtilityNative::RegisterPlayerloopCallback
UnityEngine.UIElements.UIElementsRuntimeUtilityNative::UnregisterPlayerloopCallback
UnityEngine.UIElements.UIElementsRuntimeUtilityNative::VisualElementCreation
UnityEngine.UIElements.UIPainter2D::Create
UnityEngine.UIElements.UIPainter2D::ClearSnapshots
UnityEngine.UIElements.UIPainter2D::Destroy
UnityEngine.UIElements.UIPainter2D::ExecuteSnapshotFromJob_Injected
UnityEngine.UIElements.UIPainter2D::Reset
UnityEngine.UIElements.UIR.JobProcessor::ScheduleConvertMeshJobs_Injected
UnityEngine.UIElements.UIR.JobProcessor::ScheduleCopyMeshJobs_Injected
UnityEngine.UIElements.UIR.JobProcessor::ScheduleNudgeJobs_Injected
UnityEngine.UIElements.UIR.Utility::CPUFencePassed
UnityEngine.UIElements.UIR.Utility::HasMappedBufferRange
UnityEngine.UIElements.UIR.Utility::AllocateBuffer
UnityEngine.UIElements.UIR.Utility::CreateStencilState_Injected
UnityEngine.UIElements.UIR.Utility::GetVertexDeclaration_Injected
UnityEngine.UIElements.UIR.Utility::InsertCPUFence
UnityEngine.UIElements.UIR.Utility::DisableScissor
UnityEngine.UIElements.UIR.Utility::DrawRanges
UnityEngine.UIElements.UIR.Utility::FreeBuffer
UnityEngine.UIElements.UIR.Utility::GetActiveViewport_Injected
UnityEngine.UIElements.UIR.Utility::GetUnityProjectionMatrix_Injected
UnityEngine.UIElements.UIR.Utility::NotifyOfUIREvents
UnityEngine.UIElements.UIR.Utility::ProfileDrawChainBegin
UnityEngine.UIElements.UIR.Utility::ProfileDrawChainEnd
UnityEngine.UIElements.UIR.Utility::SetPropertyBlock_Injected
UnityEngine.UIElements.UIR.Utility::SetScissorRect_Injected
UnityEngine.UIElements.UIR.Utility::SetStencilState
UnityEngine.UIElements.UIR.Utility::SyncRenderThread
UnityEngine.UIElements.UIR.Utility::UpdateBufferRanges
UnityEngine.UIElements.UIR.Utility::WaitForCPUFencePassed
UnityEngine.UIElements.UIRenderer::SetNativeData_Injected
UnityEngine.UISystemProfilerApi::AddMarker_Injected
UnityEngine.UISystemProfilerApi::BeginSample
UnityEngine.UISystemProfilerApi::EndSample
UnityEngine.UnityLogWriter::WriteStringToUnityLogImpl_Injected
UnityEngine.Vector3::RotateTowards_Injected
UnityEngine.XR.XRDevice::DisableAutoXRCameraTracking_Injected
UnityEngine.XR.XRMeshSubsystem/MeshTransformList::Dispose
UnityEngineInternal.Input.NativeInputSystem::get_normalizeScrollWheelDelta
UnityEngineInternal.Input.NativeInputSystem::get_currentTime
UnityEngineInternal.Input.NativeInputSystem::get_currentTimeOffsetToRealtimeSinceStartup
UnityEngineInternal.Input.NativeInputSystem::AllocateDeviceId
UnityEngineInternal.Input.NativeInputSystem::IOCTL
UnityEngineInternal.Input.NativeInputSystem::GetScrollWheelDeltaPerTick
UnityEngineInternal.Input.NativeInputSystem::QueueInputEvent
UnityEngineInternal.Input.NativeInputSystem::SetPollingFrequency
UnityEngineInternal.Input.NativeInputSystem::Update
UnityEngineInternal.Input.NativeInputSystem::set_hasDeviceDiscoveredCallback
UnityEngineInternal.Input.NativeInputSystem::set_normalizeScrollWheelDelta

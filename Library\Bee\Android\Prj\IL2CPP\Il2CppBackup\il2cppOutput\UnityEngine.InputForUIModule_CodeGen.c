﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void CommandEvent_set_timestamp_m62E4D17DC8DDC56E6A98D5904AE2C24C5E0E0D7C (void);
extern void CommandEvent_get_eventSource_mAE9F153B7FBCF387F82996F35E9A181673800CDB (void);
extern void CommandEvent_set_eventSource_m05EB34E98BDAC88C0C63DAEBFB12E0B9D797644C (void);
extern void CommandEvent_set_playerId_mB59C516FBF8583564870DE3644754B4CA7DF3FDF (void);
extern void CommandEvent_get_eventModifiers_mDE813197A28BFBB82027157E446A56BDA43E8AD8 (void);
extern void CommandEvent_set_eventModifiers_m3BA5ECFBD39DCB518239F2063DAA8BA1DCDF8AF4 (void);
extern void CommandEvent_ToString_mDD56EB15CDB87552F3E1E7D9A7A1231DED00794D (void);
extern void Event_CompareType_m784A14AADCBE77F60A81ACD18822B34E808094D9 (void);
extern void Event_get_type_m43E61FACF8BB047BB65240475858AB9B81E26D45 (void);
extern void Event_get_asObject_m305BBFAF86E3918584403DABC85E04BBEACF6DAF (void);
extern void Event_get_eventSource_m3AB10031C9E651873EF4456C93F2FE5EF93FEC63 (void);
extern void Event_get_eventModifiers_mE64227776B41073F39044BAA5E7DF23B81828CEF (void);
extern void Event_Ensure_m74EAA1E0AAD1FBF4C2C5E2DEDDCB678254E9299E (void);
extern void Event_ToString_m40AE120A2A50F4A51EC650CB21E6141EECC7E0EE (void);
extern void Event_From_m809DF3C124959C488183DCE87C73501006491666 (void);
extern void Event_get_asKeyEvent_mD0AA1C05803D90E0A783C9EA73529C320DA078F8 (void);
extern void Event_From_m52D110CDB4E411C0CD087B16307BDE77BC23FF6D (void);
extern void Event_get_asPointerEvent_mC75B2F3DDE603A2B3DE0C60D92B55068225644B8 (void);
extern void Event_From_mF881C0D16DE11006A13C131A3C1993F5D78F6A18 (void);
extern void Event_get_asTextInputEvent_m6E628742A95EFC058021FC9AABAF585BBA30FA8A (void);
extern void Event_From_m331D95D6FD35530CC6BA309CF860C2F7EEE42E09 (void);
extern void Event_get_asIMECompositionEvent_mD5CF3917637A15EB115398E8FD9B91A7BAD369A6 (void);
extern void Event_From_m8995BB56E86E91A662538FE60FDE8C6B20574353 (void);
extern void Event_get_asCommandEvent_m0217C5C5EBDA55BC07434158EF4C314D9D4367D1 (void);
extern void Event_From_m417C3DF8F0CC693B7A6AB6909D21136DA6E0F531 (void);
extern void Event_get_asNavigationEvent_m7514BFFC317A37EA5AC2413A4A08FF1986C84461 (void);
extern void Event__cctor_m6107FDB5923DDD2CF006C1C873302431C98454E3 (void);
extern void EventModifiers_IsPressed_m6BAC9B3EACDECA1760C524F4D763432C2FEC10D7 (void);
extern void EventModifiers_get_isShiftPressed_m744DB3E0E9AB1E8642ADC89753F32FBEA0CDAB75 (void);
extern void EventModifiers_get_isCtrlPressed_mBD0F785EB48BA04709114ADEB4E2A4A13A5A35BF (void);
extern void EventModifiers_get_isAltPressed_m954BB57E4F88201642B1456F6FCA0EA150DACF1A (void);
extern void EventModifiers_get_isMetaPressed_m4DB2C47378F2DC78FEEC776C1BB80DBA683AD8EA (void);
extern void EventModifiers_get_isCapsLockEnabled_m9BA2B54CBA7C6B2A03C2DC5465F7E19E9484C08D (void);
extern void EventModifiers_get_isFunctionKeyPressed_mD221094212B21BA36C9A930253A5C8EC7C822CEE (void);
extern void EventModifiers_get_isNumericPressed_mCEBBBA209E22E1C0DF3F6266DDD39E572189A7DC (void);
extern void EventModifiers_SetPressed_m33664B1E54F26DD03806889C9FE38D6079B00810 (void);
extern void EventModifiers_Reset_mF75724904B151CFD1F05B44D8C9851698A2D54C6 (void);
extern void EventModifiers_Append_m24D4F13EDAC4A80EBFD909692A53B37BE6D90239 (void);
extern void EventModifiers_ToString_m039F852BFE0EE0FAD27908AB1D494B8948BA42CA (void);
extern void IMECompositionEvent_set_timestamp_mCF0C8F7EC4CFDA5B52381D5484A81EB8B4E5D1B8 (void);
extern void IMECompositionEvent_get_eventSource_m5A9262ED18044F734147AB4A65263C7E455B7CAB (void);
extern void IMECompositionEvent_set_eventSource_mFDE0BE60BC1C8AF8EFCC6437C538446E4FC45F8D (void);
extern void IMECompositionEvent_set_playerId_mD2E85CFEC4CCD2DCB0A3A09F73A588374BE27792 (void);
extern void IMECompositionEvent_get_eventModifiers_m4B212B13B5AA27343B784C20FA77266C1D741012 (void);
extern void IMECompositionEvent_set_eventModifiers_m8E58584375A5E3CB51743803911AB4F1779C3035 (void);
extern void IMECompositionEvent_ToString_m0E99A80480D5870682A59747BAD3E63AC45FFE1E (void);
extern void KeyEvent_set_timestamp_mA1AC56751009BD6C8EF3FC6D2892860D6AC03990 (void);
extern void KeyEvent_get_eventSource_m9A74264E7C276EEFA5F603C1EA12BC23034CDE0D (void);
extern void KeyEvent_set_eventSource_m472DC90E7A9CC0B04F163CF606ACEF997493D0C5 (void);
extern void KeyEvent_set_playerId_m013ACE8EB81283F1EA7D201C1AC7E918E422304B (void);
extern void KeyEvent_get_eventModifiers_mF90EB0DF537425D3D09B1E095A2B9D24EE448782 (void);
extern void KeyEvent_set_eventModifiers_mBD0AFAC76018A0CCCDD5338471370A4B36C740E8 (void);
extern void KeyEvent_ToString_mE3FC37D2498578B189857DDF16061937A8BB3EE0 (void);
extern void ButtonsState_ShouldBeProcessed_m126DA2559EADCFC30EEDA11B38CB3AEA6DEA53C9 (void);
extern void ButtonsState_GetUnchecked_mC4D05E5776EC19AF70558CD43A18E50DFAEF4276 (void);
extern void ButtonsState_SetUnchecked_m3DA8C3D711F89AA225C83E5AA3C715B7070090B8 (void);
extern void ButtonsState_ClearUnchecked_mFE649DCAA069241AB35FAD93B54C638CBCA83E1D (void);
extern void ButtonsState_IsPressed_m077940991B13B5E5E8C73997B82358A9022D37C3 (void);
extern void ButtonsState_GetAllPressed_m116B75F1006DB83D41784159F9144CC01160A42E (void);
extern void ButtonsState_SetPressed_m83A234B0D8B922BC8797FB9DB61AF4B26545F9EA (void);
extern void ButtonsState_Reset_m2682450F34125FB2DB313B9F9528F27FCE18F10B (void);
extern void ButtonsState_ToString_m0F4594EDB720B831AF8A808B19FB753687C6D4E0 (void);
extern void U3CGetAllPressedU3Ed__8__ctor_m8C8552F70A8A8587348EA83D7C7FAA761FE2FAFC (void);
extern void U3CGetAllPressedU3Ed__8_System_IDisposable_Dispose_mD67C2F0EAAAEE6926CD4AC4D2734CE54C47A13B0 (void);
extern void U3CGetAllPressedU3Ed__8_MoveNext_mFF4FCA890D6EFCE55889A3ED26B19FBEFA430A26 (void);
extern void U3CGetAllPressedU3Ed__8_System_Collections_Generic_IEnumeratorU3CUnityEngine_KeyCodeU3E_get_Current_m9FA8FB822995457B3CDADEE6C0278F5E2C1067BF (void);
extern void U3CGetAllPressedU3Ed__8_System_Collections_IEnumerator_Reset_mB8C1C41D870E7EFAF0516AE611AAF503709BCB6F (void);
extern void U3CGetAllPressedU3Ed__8_System_Collections_IEnumerator_get_Current_m395E0286389BAE77432556983BFB0C0AB9D3F2A4 (void);
extern void U3CGetAllPressedU3Ed__8_System_Collections_Generic_IEnumerableU3CUnityEngine_KeyCodeU3E_GetEnumerator_m475A854517EAA3EA6300293F7DAD75C8592268C7 (void);
extern void U3CGetAllPressedU3Ed__8_System_Collections_IEnumerable_GetEnumerator_mE300D47FFB199FE609D6FF18294D4A75BD88B7BA (void);
extern void NavigationEvent_set_timestamp_mA6CDDEB6A1C29A64AD4B42203ECAB1A628E88983 (void);
extern void NavigationEvent_get_eventSource_mD35D875FBB7FA557068DF25BB145292A6EF9FBA0 (void);
extern void NavigationEvent_set_eventSource_m74427FF1CC7AA5430F1FEF37F9BA4D50BE5CB2B3 (void);
extern void NavigationEvent_set_playerId_mA2E0FFCDFE0D345A8DFF67E567099CDF1A637F36 (void);
extern void NavigationEvent_get_eventModifiers_mA4BA9D05B5E5E327B9E5A1889202FA12518AB815 (void);
extern void NavigationEvent_set_eventModifiers_m1B4B8E45D1892C2357EF0933D51A613D3156076E (void);
extern void NavigationEvent_ToString_mD59B9C574F8B542874908EF4266F0D9B09E06667 (void);
extern void NavigationEvent_DetermineMoveDirection_m5005119A9F96003CE27FDDAC93357DE82BCB11D7 (void);
extern void PointerEvent_get_isPrimaryPointer_mEA57B41D6996C4107CDBE1133C92EBAE58EFA007 (void);
extern void PointerEvent_get_azimuth_m5FD6788924C58898C59D2D093790102216EA4B6E (void);
extern void PointerEvent_get_altitude_m96D1D73F089B16CDFD1D199A07C310E5456124B4 (void);
extern void PointerEvent_get_timestamp_m5A833B8F7FC3F775DD8D6FA05E7A3AA86A3D5035 (void);
extern void PointerEvent_set_timestamp_m1332F1F26A4457114C873F78D1F8B486D65B5AD6 (void);
extern void PointerEvent_get_eventSource_m433AD39B323BF13AE165F5F9D025444092FAEB33 (void);
extern void PointerEvent_set_eventSource_mE12591C4D25BCF5EB9DD502688B73CC5F59A7AD0 (void);
extern void PointerEvent_set_playerId_m33852BB774BBD9AB5868145E1B1069658FEA4E2B (void);
extern void PointerEvent_get_eventModifiers_m5C238F94AA34FB172F4D7256E5D68E838E3C5A05 (void);
extern void PointerEvent_set_eventModifiers_mECB5C87AE58191D859769627A924B1DF137C168A (void);
extern void PointerEvent_ToString_m7DA27740BA43C97712A917546C18AD0A1C2BCBA3 (void);
extern void PointerEvent_ButtonFromButtonIndex_m4BA98848B3FA71D2A8FA446BEE3162F98EF0F256 (void);
extern void ButtonsState_Set_m52DB679AB8DF80E2E34A72654B66F922DD835032 (void);
extern void ButtonsState_Get_mE6798B0C21BCA75C5CB52BDA200536F9EE3E9893 (void);
extern void ButtonsState_Reset_mD96D8954FBCB9ED51CC3D35A0E8A7BAD6B3088AF (void);
extern void ButtonsState_ToString_mDE2CA355C9C0F9A18F6E52599AF852CAF5B73758 (void);
extern void TextInputEvent_set_timestamp_m44508CDEF51657A9AEAFB68194E0527AFD7F262B (void);
extern void TextInputEvent_get_eventSource_mD08759B60A91AAC1DD74CD97AABA7F47C1332CF2 (void);
extern void TextInputEvent_set_eventSource_m9489D4075B4EF62E68A557B4A569989C11382340 (void);
extern void TextInputEvent_set_playerId_mCEC6B21B1EDC0D9E8244CF4519C5FDE05119AC2D (void);
extern void TextInputEvent_get_eventModifiers_m0C34160E76D9DE9F4D307C6EDF4BBF7319BB6E78 (void);
extern void TextInputEvent_set_eventModifiers_mA544AB38AD48B6538B27806943DA654223501B2E (void);
extern void TextInputEvent_ToString_m14C55E0242789C632CFE6B231E3C5844BB9C8ABB (void);
extern void EventProvider_Subscribe_mDA1DA0F3B3EA77F679ED647F473D4491DD7B5F14 (void);
extern void EventProvider_Unsubscribe_m6F860CB50CC55FADF5727DEDD868F25ED4848244 (void);
extern void EventProvider_SetEnabled_m03844ACF20D941DD6A13C8019B27EF93B1254D8E (void);
extern void EventProvider_Dispatch_m41F4C2A2ECC4B3FC70C0FA8B7BF0E704EC3BE1B2 (void);
extern void EventProvider_Bootstrap_mA2AA0ECBBE7FA4233E0CD4C988B3C3E1419AA130 (void);
extern void EventProvider_Initialize_m4C3C990C40682BA7B3AD847B6A35EA27080FB939 (void);
extern void EventProvider_Shutdown_mC7555B784CCBC1EC859FBD7374E866EC58939D0F (void);
extern void EventProvider_OnFocusChanged_m26FDB6E1AE2841953DCEE2110FF59D477EFC6D9B (void);
extern void EventProvider_NotifyUpdate_m30171685ED8559A5E59D74870573E49D70472608 (void);
extern void EventProvider_SetInputSystemProvider_m6C3F39EF66599573B1E026947568F63F5E64C1DC (void);
extern void EventProvider__cctor_m43251A9D75881B0B1D5C406A64C912C587431E3C (void);
extern void U3CU3Ec__cctor_m58BE846E628A4649079FB42D8AB34515AA282D01 (void);
extern void U3CU3Ec__ctor_mED9C83795F0ABEE40F08C2F85108B8E7D5955E97 (void);
extern void U3CU3Ec_U3CSubscribeU3Eb__5_0_m0D318F3E7BE3B1691D8F88263ADA4A82F07D702B (void);
extern void U3CU3Ec__DisplayClass6_0__ctor_mD5427AD019DAC52CBD09D731B6AB78168EB72A0A (void);
extern void U3CU3Ec__DisplayClass6_0_U3CUnsubscribeU3Eb__0_mA51E8F366E84179C1E842444AC6000835B290F6C (void);
extern void EventConsumer__ctor_m13E509A533F4CE57F96996623C7C0563465FBE65 (void);
extern void EventConsumer_Invoke_mC05E15918C8BD865AE8900A34A4D3CD3869A1FC1 (void);
extern void InputEventPartialProvider_Initialize_m26D2508B2B6EFF4B4A132617F43C48FA59E20A5D (void);
extern void InputEventPartialProvider_Shutdown_m058F84D37E692A94E6FF1CA8AFF19D52090DB91B (void);
extern void InputEventPartialProvider_Update_mFF68FDC8AA0E8ABC536F4C583C98273CEE34EDBF (void);
extern void InputEventPartialProvider_OnFocusChanged_m9CFB7961AE1C60AC5619EC605BD1DF255C76866D (void);
extern void InputEventPartialProvider_RequestCurrentState_m63E18B8DF17D1AD94E4685C2287596C0B7328857 (void);
extern void InputEventPartialProvider_GetTimestamp_m08C36E9210FF2911346E6472570BAEC1DD0C27CE (void);
extern void InputEventPartialProvider_UpdateEventModifiers_mC9202812FD6F7A0C31C3354C8B815850E7394464 (void);
extern void InputEventPartialProvider_ToKeyEvent_m23B05A6C3345993DFF882C6946420637B683427F (void);
extern void InputEventPartialProvider_ToTextInputEvent_m7F83D7D16705B11148556C3A79462EBBECDB0FF7 (void);
extern void InputEventPartialProvider_SendNextOrPreviousNavigationEventOnTabKeyDownEvent_mE5142DD9C59E0277E21522FCDDB9B8AACD4CF970 (void);
extern void InputEventPartialProvider_ToCommandEvent_mA848D18A0F630A577369D6C3B74B4882CC0CCBFC (void);
extern void InputEventPartialProvider__ctor_mAD089CB3ACA29F2E2BC600CB4A20263A58C50AF6 (void);
extern void InputManagerProvider_get__eventModifiers_m6EA95203111D83D273204A989AEEA7BE57182608 (void);
extern void InputManagerProvider__ctor_mC4E650E293572ED9614B1D46AEBF456AE57DE002 (void);
extern void InputManagerProvider_Initialize_m6D43886B1338BD826DF2A90099FD77903F8CEFC3 (void);
extern void InputManagerProvider_Shutdown_mE988584ABA2B1758DAC933141D1F9763202B93D6 (void);
extern void InputManagerProvider_Update_mDAB6B7A932DDFE1089BA5792743919A2B21F85CA (void);
extern void InputManagerProvider_CheckTouchEvents_m2E19A96BC0262F0724541F72B33525FFAAD9B3E3 (void);
extern void InputManagerProvider_DetectPen_mA2B3A042573149D98ECAFAA4E419CC66F5EF2A74 (void);
extern void InputManagerProvider_PenStatusToButton_m69F1EE1E22F7C8FD32FE8EFB0E2839C134EEF9D7 (void);
extern void InputManagerProvider_CheckPenEvent_m013C2084AC06E735B3172C0E7B48E61202BD8DF7 (void);
extern void InputManagerProvider_CheckMouseEvents_mDCBAA0EB5BA6036CFA0AA533751CB533BE7E870D (void);
extern void InputManagerProvider_CheckMouseScroll_m3F1FEB3988B98DA7167CFFEF90A51629243A799C (void);
extern void InputManagerProvider_NextPreviousNavigation_m8D17555565CA6EE1E644E27627B457C757B27634 (void);
extern void InputManagerProvider_SubmitCancelNavigation_m0316CAF1C7F73BAB2CF4E430C44F613EF33C7CCC (void);
extern void InputManagerProvider_DirectionNavigation_mC0DDAD7BAE1018C72947BB0B30D93ACD47560438 (void);
extern void InputManagerProvider_CheckIfIMEChanged_m0180C84089D1614F360CAD91D91ECDB66BDF4DF6 (void);
extern void InputManagerProvider_OnFocusChanged_m8119E167E4487283D9A32651B6AFC2AD687B23FD (void);
extern void InputManagerProvider_GetEventSourceFromPressedKey_mBE5F5992549A1F52995D999907E4151397D27077 (void);
extern void InputManagerProvider_InputManagerJoystickWasPressed_mE8AD1DD4CC38C2C835DA7267CF7ABDCC239D0C08 (void);
extern void InputManagerProvider_InputManagerKeyboardWasPressed_m459F43495C4ABBDA6C5797A7EE59B3CE04F0132E (void);
extern void InputManagerProvider_InputManagerGetAxisRawOrDefault_mCAC1086D4BE5A165891B8278F6CCC98AC936656F (void);
extern void InputManagerProvider_InputManagerGetButtonDownOrDefault_m35FF0B2F0317D280A67BCAE8FBCCA540C77BD992 (void);
extern void InputManagerProvider_ReadCurrentNavigationMoveVector_m90391AFBAB25185A1CD75A061EF9548C63533FCF (void);
extern void InputManagerProvider_ToIMECompositionEvent_m614B50ABA282C650D4B8683ABC3F8E045A95E7C1 (void);
extern void InputManagerProvider_TiltToAzimuth_mC2FB72765B15A126F063508C9D56F63FA7B20FA9 (void);
extern void InputManagerProvider_AzimuthAndAlitutudeToTilt_m10AE8A5FD45782FC0F56618310B1BA386C3EB279 (void);
extern void InputManagerProvider_TiltToAltitude_m817A8B10D8C02599C90079F527B7748F704EF33F (void);
extern void InputManagerProvider_MultiDisplayBottomLeftToPanelPosition_m65D469CB9946C2D86882FC517BBC691C585BA355 (void);
extern void InputManagerProvider_MultiDisplayToLocalScreenPosition_mC429C7E2DC68889715F5FD241095FC6C8F829340 (void);
extern void InputManagerProvider_ScreenBottomLeftToPanelPosition_m3D7C004A055685C3FD686B5EC787D09DCA47F231 (void);
extern void InputManagerProvider_ScreenBottomLeftToPanelDelta_m560324B7D692193DD8589C4C57B3D38A1ADDAA14 (void);
extern void ButtonEventsIterator_get_Current_m86D6845A6F6A4489F303ED060476EF59F67CD6DB (void);
extern void ButtonEventsIterator_MoveNext_m742C90ADEB41243BAA32EE2380C5831ED1792A74 (void);
extern void ButtonEventsIterator_Reset_m178B46B30A239DE2E1A29E7AA98FF1513EDA17F0 (void);
extern void ButtonEventsIterator_System_Collections_IEnumerator_get_Current_mF2064D9D36208EBC155BF0F8150CC36B046AE329 (void);
extern void ButtonEventsIterator_FromState_m3A1648CAFC60259C46054D6F22F07B5430895BA3 (void);
extern void Configuration_GetDefaultConfiguration_m7D9AA0B0BF4CEC1F2EF1710F2F7B2F0F8733E520 (void);
extern void Input_get_compositionString_mB0C5F6EDF4EA61E322409CDA57BA5972EF4CC5FF (void);
extern void Input_GetKey_m143F347C1BF2BCFE2330359D7318E684658B9C48 (void);
extern void Input_GetButtonDown_m6ED6FBEE32B6076F56DB89A4B316444D8994321B (void);
extern void Input_GetAxisRaw_m31C2CC87AFEC5B721423E889A0CECBBB11DFFD9B (void);
extern void Input_GetLastPenContactEvent_mFA79B07FFFB83A5B97B316FB3D54DDAD64C2B5A1 (void);
extern void Input_get_touchSupported_mA03DB20C869A71C62146C06100C48AEDDE613BFC (void);
extern void Input_get_touchCount_mDF791D9E4EB9A794239D1D5299272FE4BFC6DB90 (void);
extern void Input_GetTouch_m4A451AFEFC34B96CF4A85BEA7D9EFA0769D0943C (void);
extern void Input_get_mousePresent_m777773D12B273EBC1CB0B4DC12FE9A16D9BD182D (void);
extern void Input_GetMouseButton_m53E0B9639436CA047C8A5602DAB25FA891F77BA3 (void);
extern void Input_GetMouseButtonDown_m62C31D30523A36490729B45A386D0432A1CD4C85 (void);
extern void Input_GetMouseButtonUp_mE614D01A461910BECEFAF3BD0907B1FA7CB81D21 (void);
extern void Input_get_mousePosition_m7C0B9B1C67FB19C0A6781F90A57FA7FC854D7BED (void);
extern void Input_get_mouseScrollDelta_m058EEC260A2F79171D72B5DA481CCE662FBE3CA4 (void);
extern void Input__ctor_mCDB5543E0B17B8AB6A656EF3712510D72E2ED60C (void);
extern void Time_get_timeAsRational_mE38CB5C8B491BC642E877909F57DB50F3B6D7B6F (void);
extern void Time__ctor_m1EC45264C7D833596A95CE34AD9D709D75A21B96 (void);
extern void NavigationEventRepeatHelper_Reset_m6DDC1E69A71E8F3B3AF2117D02D8C4A327FDD4BD (void);
extern void NavigationEventRepeatHelper_ShouldSendMoveEvent_m2EC3A5BB1F22A0CC12DDB83D820744BA276C8261 (void);
extern void NavigationEventRepeatHelper__ctor_mBDEDDCB73A4F3F8F0AF20E463966BEAE4284C65D (void);
extern void PointerState_get_LastPressedButton_m12CCDCF3247A0A4513EC22E7CFDAFCDF1A12D342 (void);
extern void PointerState_set_LastPressedButton_m4B75F3AD3146CB62D189140DC42A5245DAC37D22 (void);
extern void PointerState_get_ButtonsState_m9C44C7CD402868FA0A4FB2542E98B9E7278C378B (void);
extern void PointerState_get_NextPressTime_m12D2DCC838A9BB0D21B1C3AD3A4580BFB5949FE2 (void);
extern void PointerState_set_NextPressTime_m17CBA6464FE4DE8EB6646AC179F153CF52F9DA8C (void);
extern void PointerState_get_ClickCount_m0378690A745AF06423CF6A01969FDEF06CCC09AA (void);
extern void PointerState_set_ClickCount_m8DC9D4E3E11FB38CA977A6D45DC3431B1798CA17 (void);
extern void PointerState_get_LastPosition_m994D92D93C3633D45DE22B239EB6AE38BFC8608E (void);
extern void PointerState_set_LastPosition_mA58AB07C81F5D5D4D5BB8E2E01BF83F93730684B (void);
extern void PointerState_get_LastDisplayIndex_m3873EF0EB7E68124D270FB3F7A2083F53604AA42 (void);
extern void PointerState_set_LastDisplayIndex_m76B16012DDA718A2E7A8AD333AABA80E1ED56040 (void);
extern void PointerState_get_LastPositionValid_mB3A5D44574F272EA9EC2674A0D408AC8AC200C81 (void);
extern void PointerState_set_LastPositionValid_mFDF1E68670710967C93480FCAF23AEA96DE5D621 (void);
extern void PointerState_OnButtonDown_m79BD7ABD8CD0C0632088E98AD39B160FCC05CE3F (void);
extern void PointerState_OnButtonUp_mCE8DE6B7E8D38FCF45C848F49547998CA39C1EB6 (void);
extern void PointerState_OnButtonChange_m76D1C399434C6B40BB7E4880B0E3109744A91E65 (void);
extern void PointerState_OnMove_mFCF40645372BEA0B69DA5A5C4064BC6A11068972 (void);
extern void PointerState_Reset_m6DD45B949BC56BA9C68497B3F1178BCBFC70462F (void);
extern void PointerState__cctor_m86E5EB0D671A3824A4F65A796DAAB6423822DB64 (void);
extern void EventSanitizer_Reset_m3B7D88555BBA84A9C4EFF2A2770AB161D09F20EB (void);
extern void EventSanitizer_BeforeProviderUpdate_m65F246118381B43BCAD668849185ECEF7C52C971 (void);
extern void EventSanitizer_AfterProviderUpdate_m3CD0CF9D1169DE1B1B9A96A564069E282B48E9AA (void);
extern void EventSanitizer_Inspect_m9B438E50B9AE5E6CAF8E03679C90D02D11ACCF73 (void);
static Il2CppMethodPointer s_methodPointers[241] = 
{
	CommandEvent_set_timestamp_m62E4D17DC8DDC56E6A98D5904AE2C24C5E0E0D7C,
	CommandEvent_get_eventSource_mAE9F153B7FBCF387F82996F35E9A181673800CDB,
	CommandEvent_set_eventSource_m05EB34E98BDAC88C0C63DAEBFB12E0B9D797644C,
	CommandEvent_set_playerId_mB59C516FBF8583564870DE3644754B4CA7DF3FDF,
	CommandEvent_get_eventModifiers_mDE813197A28BFBB82027157E446A56BDA43E8AD8,
	CommandEvent_set_eventModifiers_m3BA5ECFBD39DCB518239F2063DAA8BA1DCDF8AF4,
	CommandEvent_ToString_mDD56EB15CDB87552F3E1E7D9A7A1231DED00794D,
	Event_CompareType_m784A14AADCBE77F60A81ACD18822B34E808094D9,
	Event_get_type_m43E61FACF8BB047BB65240475858AB9B81E26D45,
	Event_get_asObject_m305BBFAF86E3918584403DABC85E04BBEACF6DAF,
	Event_get_eventSource_m3AB10031C9E651873EF4456C93F2FE5EF93FEC63,
	Event_get_eventModifiers_mE64227776B41073F39044BAA5E7DF23B81828CEF,
	Event_Ensure_m74EAA1E0AAD1FBF4C2C5E2DEDDCB678254E9299E,
	Event_ToString_m40AE120A2A50F4A51EC650CB21E6141EECC7E0EE,
	Event_From_m809DF3C124959C488183DCE87C73501006491666,
	Event_get_asKeyEvent_mD0AA1C05803D90E0A783C9EA73529C320DA078F8,
	Event_From_m52D110CDB4E411C0CD087B16307BDE77BC23FF6D,
	Event_get_asPointerEvent_mC75B2F3DDE603A2B3DE0C60D92B55068225644B8,
	Event_From_mF881C0D16DE11006A13C131A3C1993F5D78F6A18,
	Event_get_asTextInputEvent_m6E628742A95EFC058021FC9AABAF585BBA30FA8A,
	Event_From_m331D95D6FD35530CC6BA309CF860C2F7EEE42E09,
	Event_get_asIMECompositionEvent_mD5CF3917637A15EB115398E8FD9B91A7BAD369A6,
	Event_From_m8995BB56E86E91A662538FE60FDE8C6B20574353,
	Event_get_asCommandEvent_m0217C5C5EBDA55BC07434158EF4C314D9D4367D1,
	Event_From_m417C3DF8F0CC693B7A6AB6909D21136DA6E0F531,
	Event_get_asNavigationEvent_m7514BFFC317A37EA5AC2413A4A08FF1986C84461,
	NULL,
	NULL,
	Event__cctor_m6107FDB5923DDD2CF006C1C873302431C98454E3,
	NULL,
	NULL,
	NULL,
	NULL,
	EventModifiers_IsPressed_m6BAC9B3EACDECA1760C524F4D763432C2FEC10D7,
	EventModifiers_get_isShiftPressed_m744DB3E0E9AB1E8642ADC89753F32FBEA0CDAB75,
	EventModifiers_get_isCtrlPressed_mBD0F785EB48BA04709114ADEB4E2A4A13A5A35BF,
	EventModifiers_get_isAltPressed_m954BB57E4F88201642B1456F6FCA0EA150DACF1A,
	EventModifiers_get_isMetaPressed_m4DB2C47378F2DC78FEEC776C1BB80DBA683AD8EA,
	EventModifiers_get_isCapsLockEnabled_m9BA2B54CBA7C6B2A03C2DC5465F7E19E9484C08D,
	EventModifiers_get_isFunctionKeyPressed_mD221094212B21BA36C9A930253A5C8EC7C822CEE,
	EventModifiers_get_isNumericPressed_mCEBBBA209E22E1C0DF3F6266DDD39E572189A7DC,
	EventModifiers_SetPressed_m33664B1E54F26DD03806889C9FE38D6079B00810,
	EventModifiers_Reset_mF75724904B151CFD1F05B44D8C9851698A2D54C6,
	EventModifiers_Append_m24D4F13EDAC4A80EBFD909692A53B37BE6D90239,
	EventModifiers_ToString_m039F852BFE0EE0FAD27908AB1D494B8948BA42CA,
	NULL,
	NULL,
	IMECompositionEvent_set_timestamp_mCF0C8F7EC4CFDA5B52381D5484A81EB8B4E5D1B8,
	IMECompositionEvent_get_eventSource_m5A9262ED18044F734147AB4A65263C7E455B7CAB,
	IMECompositionEvent_set_eventSource_mFDE0BE60BC1C8AF8EFCC6437C538446E4FC45F8D,
	IMECompositionEvent_set_playerId_mD2E85CFEC4CCD2DCB0A3A09F73A588374BE27792,
	IMECompositionEvent_get_eventModifiers_m4B212B13B5AA27343B784C20FA77266C1D741012,
	IMECompositionEvent_set_eventModifiers_m8E58584375A5E3CB51743803911AB4F1779C3035,
	IMECompositionEvent_ToString_m0E99A80480D5870682A59747BAD3E63AC45FFE1E,
	KeyEvent_set_timestamp_mA1AC56751009BD6C8EF3FC6D2892860D6AC03990,
	KeyEvent_get_eventSource_m9A74264E7C276EEFA5F603C1EA12BC23034CDE0D,
	KeyEvent_set_eventSource_m472DC90E7A9CC0B04F163CF606ACEF997493D0C5,
	KeyEvent_set_playerId_m013ACE8EB81283F1EA7D201C1AC7E918E422304B,
	KeyEvent_get_eventModifiers_mF90EB0DF537425D3D09B1E095A2B9D24EE448782,
	KeyEvent_set_eventModifiers_mBD0AFAC76018A0CCCDD5338471370A4B36C740E8,
	KeyEvent_ToString_mE3FC37D2498578B189857DDF16061937A8BB3EE0,
	ButtonsState_ShouldBeProcessed_m126DA2559EADCFC30EEDA11B38CB3AEA6DEA53C9,
	ButtonsState_GetUnchecked_mC4D05E5776EC19AF70558CD43A18E50DFAEF4276,
	ButtonsState_SetUnchecked_m3DA8C3D711F89AA225C83E5AA3C715B7070090B8,
	ButtonsState_ClearUnchecked_mFE649DCAA069241AB35FAD93B54C638CBCA83E1D,
	ButtonsState_IsPressed_m077940991B13B5E5E8C73997B82358A9022D37C3,
	ButtonsState_GetAllPressed_m116B75F1006DB83D41784159F9144CC01160A42E,
	ButtonsState_SetPressed_m83A234B0D8B922BC8797FB9DB61AF4B26545F9EA,
	ButtonsState_Reset_m2682450F34125FB2DB313B9F9528F27FCE18F10B,
	ButtonsState_ToString_m0F4594EDB720B831AF8A808B19FB753687C6D4E0,
	U3CGetAllPressedU3Ed__8__ctor_m8C8552F70A8A8587348EA83D7C7FAA761FE2FAFC,
	U3CGetAllPressedU3Ed__8_System_IDisposable_Dispose_mD67C2F0EAAAEE6926CD4AC4D2734CE54C47A13B0,
	U3CGetAllPressedU3Ed__8_MoveNext_mFF4FCA890D6EFCE55889A3ED26B19FBEFA430A26,
	U3CGetAllPressedU3Ed__8_System_Collections_Generic_IEnumeratorU3CUnityEngine_KeyCodeU3E_get_Current_m9FA8FB822995457B3CDADEE6C0278F5E2C1067BF,
	U3CGetAllPressedU3Ed__8_System_Collections_IEnumerator_Reset_mB8C1C41D870E7EFAF0516AE611AAF503709BCB6F,
	U3CGetAllPressedU3Ed__8_System_Collections_IEnumerator_get_Current_m395E0286389BAE77432556983BFB0C0AB9D3F2A4,
	U3CGetAllPressedU3Ed__8_System_Collections_Generic_IEnumerableU3CUnityEngine_KeyCodeU3E_GetEnumerator_m475A854517EAA3EA6300293F7DAD75C8592268C7,
	U3CGetAllPressedU3Ed__8_System_Collections_IEnumerable_GetEnumerator_mE300D47FFB199FE609D6FF18294D4A75BD88B7BA,
	NavigationEvent_set_timestamp_mA6CDDEB6A1C29A64AD4B42203ECAB1A628E88983,
	NavigationEvent_get_eventSource_mD35D875FBB7FA557068DF25BB145292A6EF9FBA0,
	NavigationEvent_set_eventSource_m74427FF1CC7AA5430F1FEF37F9BA4D50BE5CB2B3,
	NavigationEvent_set_playerId_mA2E0FFCDFE0D345A8DFF67E567099CDF1A637F36,
	NavigationEvent_get_eventModifiers_mA4BA9D05B5E5E327B9E5A1889202FA12518AB815,
	NavigationEvent_set_eventModifiers_m1B4B8E45D1892C2357EF0933D51A613D3156076E,
	NavigationEvent_ToString_mD59B9C574F8B542874908EF4266F0D9B09E06667,
	NavigationEvent_DetermineMoveDirection_m5005119A9F96003CE27FDDAC93357DE82BCB11D7,
	PointerEvent_get_isPrimaryPointer_mEA57B41D6996C4107CDBE1133C92EBAE58EFA007,
	PointerEvent_get_azimuth_m5FD6788924C58898C59D2D093790102216EA4B6E,
	PointerEvent_get_altitude_m96D1D73F089B16CDFD1D199A07C310E5456124B4,
	PointerEvent_get_timestamp_m5A833B8F7FC3F775DD8D6FA05E7A3AA86A3D5035,
	PointerEvent_set_timestamp_m1332F1F26A4457114C873F78D1F8B486D65B5AD6,
	PointerEvent_get_eventSource_m433AD39B323BF13AE165F5F9D025444092FAEB33,
	PointerEvent_set_eventSource_mE12591C4D25BCF5EB9DD502688B73CC5F59A7AD0,
	PointerEvent_set_playerId_m33852BB774BBD9AB5868145E1B1069658FEA4E2B,
	PointerEvent_get_eventModifiers_m5C238F94AA34FB172F4D7256E5D68E838E3C5A05,
	PointerEvent_set_eventModifiers_mECB5C87AE58191D859769627A924B1DF137C168A,
	PointerEvent_ToString_m7DA27740BA43C97712A917546C18AD0A1C2BCBA3,
	PointerEvent_ButtonFromButtonIndex_m4BA98848B3FA71D2A8FA446BEE3162F98EF0F256,
	ButtonsState_Set_m52DB679AB8DF80E2E34A72654B66F922DD835032,
	ButtonsState_Get_mE6798B0C21BCA75C5CB52BDA200536F9EE3E9893,
	ButtonsState_Reset_mD96D8954FBCB9ED51CC3D35A0E8A7BAD6B3088AF,
	ButtonsState_ToString_mDE2CA355C9C0F9A18F6E52599AF852CAF5B73758,
	TextInputEvent_set_timestamp_m44508CDEF51657A9AEAFB68194E0527AFD7F262B,
	TextInputEvent_get_eventSource_mD08759B60A91AAC1DD74CD97AABA7F47C1332CF2,
	TextInputEvent_set_eventSource_m9489D4075B4EF62E68A557B4A569989C11382340,
	TextInputEvent_set_playerId_mCEC6B21B1EDC0D9E8244CF4519C5FDE05119AC2D,
	TextInputEvent_get_eventModifiers_m0C34160E76D9DE9F4D307C6EDF4BBF7319BB6E78,
	TextInputEvent_set_eventModifiers_mA544AB38AD48B6538B27806943DA654223501B2E,
	TextInputEvent_ToString_m14C55E0242789C632CFE6B231E3C5844BB9C8ABB,
	EventProvider_Subscribe_mDA1DA0F3B3EA77F679ED647F473D4491DD7B5F14,
	EventProvider_Unsubscribe_m6F860CB50CC55FADF5727DEDD868F25ED4848244,
	EventProvider_SetEnabled_m03844ACF20D941DD6A13C8019B27EF93B1254D8E,
	EventProvider_Dispatch_m41F4C2A2ECC4B3FC70C0FA8B7BF0E704EC3BE1B2,
	EventProvider_Bootstrap_mA2AA0ECBBE7FA4233E0CD4C988B3C3E1419AA130,
	EventProvider_Initialize_m4C3C990C40682BA7B3AD847B6A35EA27080FB939,
	EventProvider_Shutdown_mC7555B784CCBC1EC859FBD7374E866EC58939D0F,
	EventProvider_OnFocusChanged_m26FDB6E1AE2841953DCEE2110FF59D477EFC6D9B,
	EventProvider_NotifyUpdate_m30171685ED8559A5E59D74870573E49D70472608,
	EventProvider_SetInputSystemProvider_m6C3F39EF66599573B1E026947568F63F5E64C1DC,
	EventProvider__cctor_m43251A9D75881B0B1D5C406A64C912C587431E3C,
	U3CU3Ec__cctor_m58BE846E628A4649079FB42D8AB34515AA282D01,
	U3CU3Ec__ctor_mED9C83795F0ABEE40F08C2F85108B8E7D5955E97,
	U3CU3Ec_U3CSubscribeU3Eb__5_0_m0D318F3E7BE3B1691D8F88263ADA4A82F07D702B,
	U3CU3Ec__DisplayClass6_0__ctor_mD5427AD019DAC52CBD09D731B6AB78168EB72A0A,
	U3CU3Ec__DisplayClass6_0_U3CUnsubscribeU3Eb__0_mA51E8F366E84179C1E842444AC6000835B290F6C,
	EventConsumer__ctor_m13E509A533F4CE57F96996623C7C0563465FBE65,
	EventConsumer_Invoke_mC05E15918C8BD865AE8900A34A4D3CD3869A1FC1,
	NULL,
	NULL,
	NULL,
	NULL,
	InputEventPartialProvider_Initialize_m26D2508B2B6EFF4B4A132617F43C48FA59E20A5D,
	InputEventPartialProvider_Shutdown_m058F84D37E692A94E6FF1CA8AFF19D52090DB91B,
	InputEventPartialProvider_Update_mFF68FDC8AA0E8ABC536F4C583C98273CEE34EDBF,
	InputEventPartialProvider_OnFocusChanged_m9CFB7961AE1C60AC5619EC605BD1DF255C76866D,
	InputEventPartialProvider_RequestCurrentState_m63E18B8DF17D1AD94E4685C2287596C0B7328857,
	InputEventPartialProvider_GetTimestamp_m08C36E9210FF2911346E6472570BAEC1DD0C27CE,
	InputEventPartialProvider_UpdateEventModifiers_mC9202812FD6F7A0C31C3354C8B815850E7394464,
	InputEventPartialProvider_ToKeyEvent_m23B05A6C3345993DFF882C6946420637B683427F,
	InputEventPartialProvider_ToTextInputEvent_m7F83D7D16705B11148556C3A79462EBBECDB0FF7,
	InputEventPartialProvider_SendNextOrPreviousNavigationEventOnTabKeyDownEvent_mE5142DD9C59E0277E21522FCDDB9B8AACD4CF970,
	InputEventPartialProvider_ToCommandEvent_mA848D18A0F630A577369D6C3B74B4882CC0CCBFC,
	InputEventPartialProvider__ctor_mAD089CB3ACA29F2E2BC600CB4A20263A58C50AF6,
	InputManagerProvider_get__eventModifiers_m6EA95203111D83D273204A989AEEA7BE57182608,
	InputManagerProvider__ctor_mC4E650E293572ED9614B1D46AEBF456AE57DE002,
	InputManagerProvider_Initialize_m6D43886B1338BD826DF2A90099FD77903F8CEFC3,
	InputManagerProvider_Shutdown_mE988584ABA2B1758DAC933141D1F9763202B93D6,
	InputManagerProvider_Update_mDAB6B7A932DDFE1089BA5792743919A2B21F85CA,
	InputManagerProvider_CheckTouchEvents_m2E19A96BC0262F0724541F72B33525FFAAD9B3E3,
	InputManagerProvider_DetectPen_mA2B3A042573149D98ECAFAA4E419CC66F5EF2A74,
	InputManagerProvider_PenStatusToButton_m69F1EE1E22F7C8FD32FE8EFB0E2839C134EEF9D7,
	InputManagerProvider_CheckPenEvent_m013C2084AC06E735B3172C0E7B48E61202BD8DF7,
	InputManagerProvider_CheckMouseEvents_mDCBAA0EB5BA6036CFA0AA533751CB533BE7E870D,
	InputManagerProvider_CheckMouseScroll_m3F1FEB3988B98DA7167CFFEF90A51629243A799C,
	InputManagerProvider_NextPreviousNavigation_m8D17555565CA6EE1E644E27627B457C757B27634,
	InputManagerProvider_SubmitCancelNavigation_m0316CAF1C7F73BAB2CF4E430C44F613EF33C7CCC,
	InputManagerProvider_DirectionNavigation_mC0DDAD7BAE1018C72947BB0B30D93ACD47560438,
	InputManagerProvider_CheckIfIMEChanged_m0180C84089D1614F360CAD91D91ECDB66BDF4DF6,
	InputManagerProvider_OnFocusChanged_m8119E167E4487283D9A32651B6AFC2AD687B23FD,
	InputManagerProvider_GetEventSourceFromPressedKey_mBE5F5992549A1F52995D999907E4151397D27077,
	InputManagerProvider_InputManagerJoystickWasPressed_mE8AD1DD4CC38C2C835DA7267CF7ABDCC239D0C08,
	InputManagerProvider_InputManagerKeyboardWasPressed_m459F43495C4ABBDA6C5797A7EE59B3CE04F0132E,
	InputManagerProvider_InputManagerGetAxisRawOrDefault_mCAC1086D4BE5A165891B8278F6CCC98AC936656F,
	InputManagerProvider_InputManagerGetButtonDownOrDefault_m35FF0B2F0317D280A67BCAE8FBCCA540C77BD992,
	InputManagerProvider_ReadCurrentNavigationMoveVector_m90391AFBAB25185A1CD75A061EF9548C63533FCF,
	InputManagerProvider_ToIMECompositionEvent_m614B50ABA282C650D4B8683ABC3F8E045A95E7C1,
	InputManagerProvider_TiltToAzimuth_mC2FB72765B15A126F063508C9D56F63FA7B20FA9,
	InputManagerProvider_AzimuthAndAlitutudeToTilt_m10AE8A5FD45782FC0F56618310B1BA386C3EB279,
	InputManagerProvider_TiltToAltitude_m817A8B10D8C02599C90079F527B7748F704EF33F,
	InputManagerProvider_MultiDisplayBottomLeftToPanelPosition_m65D469CB9946C2D86882FC517BBC691C585BA355,
	InputManagerProvider_MultiDisplayToLocalScreenPosition_mC429C7E2DC68889715F5FD241095FC6C8F829340,
	InputManagerProvider_ScreenBottomLeftToPanelPosition_m3D7C004A055685C3FD686B5EC787D09DCA47F231,
	InputManagerProvider_ScreenBottomLeftToPanelDelta_m560324B7D692193DD8589C4C57B3D38A1ADDAA14,
	ButtonEventsIterator_get_Current_m86D6845A6F6A4489F303ED060476EF59F67CD6DB,
	ButtonEventsIterator_MoveNext_m742C90ADEB41243BAA32EE2380C5831ED1792A74,
	ButtonEventsIterator_Reset_m178B46B30A239DE2E1A29E7AA98FF1513EDA17F0,
	ButtonEventsIterator_System_Collections_IEnumerator_get_Current_mF2064D9D36208EBC155BF0F8150CC36B046AE329,
	ButtonEventsIterator_FromState_m3A1648CAFC60259C46054D6F22F07B5430895BA3,
	Configuration_GetDefaultConfiguration_m7D9AA0B0BF4CEC1F2EF1710F2F7B2F0F8733E520,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	Input_get_compositionString_mB0C5F6EDF4EA61E322409CDA57BA5972EF4CC5FF,
	Input_GetKey_m143F347C1BF2BCFE2330359D7318E684658B9C48,
	Input_GetButtonDown_m6ED6FBEE32B6076F56DB89A4B316444D8994321B,
	Input_GetAxisRaw_m31C2CC87AFEC5B721423E889A0CECBBB11DFFD9B,
	Input_GetLastPenContactEvent_mFA79B07FFFB83A5B97B316FB3D54DDAD64C2B5A1,
	Input_get_touchSupported_mA03DB20C869A71C62146C06100C48AEDDE613BFC,
	Input_get_touchCount_mDF791D9E4EB9A794239D1D5299272FE4BFC6DB90,
	Input_GetTouch_m4A451AFEFC34B96CF4A85BEA7D9EFA0769D0943C,
	Input_get_mousePresent_m777773D12B273EBC1CB0B4DC12FE9A16D9BD182D,
	Input_GetMouseButton_m53E0B9639436CA047C8A5602DAB25FA891F77BA3,
	Input_GetMouseButtonDown_m62C31D30523A36490729B45A386D0432A1CD4C85,
	Input_GetMouseButtonUp_mE614D01A461910BECEFAF3BD0907B1FA7CB81D21,
	Input_get_mousePosition_m7C0B9B1C67FB19C0A6781F90A57FA7FC854D7BED,
	Input_get_mouseScrollDelta_m058EEC260A2F79171D72B5DA481CCE662FBE3CA4,
	Input__ctor_mCDB5543E0B17B8AB6A656EF3712510D72E2ED60C,
	NULL,
	Time_get_timeAsRational_mE38CB5C8B491BC642E877909F57DB50F3B6D7B6F,
	Time__ctor_m1EC45264C7D833596A95CE34AD9D709D75A21B96,
	NavigationEventRepeatHelper_Reset_m6DDC1E69A71E8F3B3AF2117D02D8C4A327FDD4BD,
	NavigationEventRepeatHelper_ShouldSendMoveEvent_m2EC3A5BB1F22A0CC12DDB83D820744BA276C8261,
	NavigationEventRepeatHelper__ctor_mBDEDDCB73A4F3F8F0AF20E463966BEAE4284C65D,
	PointerState_get_LastPressedButton_m12CCDCF3247A0A4513EC22E7CFDAFCDF1A12D342,
	PointerState_set_LastPressedButton_m4B75F3AD3146CB62D189140DC42A5245DAC37D22,
	PointerState_get_ButtonsState_m9C44C7CD402868FA0A4FB2542E98B9E7278C378B,
	PointerState_get_NextPressTime_m12D2DCC838A9BB0D21B1C3AD3A4580BFB5949FE2,
	PointerState_set_NextPressTime_m17CBA6464FE4DE8EB6646AC179F153CF52F9DA8C,
	PointerState_get_ClickCount_m0378690A745AF06423CF6A01969FDEF06CCC09AA,
	PointerState_set_ClickCount_m8DC9D4E3E11FB38CA977A6D45DC3431B1798CA17,
	PointerState_get_LastPosition_m994D92D93C3633D45DE22B239EB6AE38BFC8608E,
	PointerState_set_LastPosition_mA58AB07C81F5D5D4D5BB8E2E01BF83F93730684B,
	PointerState_get_LastDisplayIndex_m3873EF0EB7E68124D270FB3F7A2083F53604AA42,
	PointerState_set_LastDisplayIndex_m76B16012DDA718A2E7A8AD333AABA80E1ED56040,
	PointerState_get_LastPositionValid_mB3A5D44574F272EA9EC2674A0D408AC8AC200C81,
	PointerState_set_LastPositionValid_mFDF1E68670710967C93480FCAF23AEA96DE5D621,
	PointerState_OnButtonDown_m79BD7ABD8CD0C0632088E98AD39B160FCC05CE3F,
	PointerState_OnButtonUp_mCE8DE6B7E8D38FCF45C848F49547998CA39C1EB6,
	PointerState_OnButtonChange_m76D1C399434C6B40BB7E4880B0E3109744A91E65,
	PointerState_OnMove_mFCF40645372BEA0B69DA5A5C4064BC6A11068972,
	PointerState_Reset_m6DD45B949BC56BA9C68497B3F1178BCBFC70462F,
	PointerState__cctor_m86E5EB0D671A3824A4F65A796DAAB6423822DB64,
	EventSanitizer_Reset_m3B7D88555BBA84A9C4EFF2A2770AB161D09F20EB,
	EventSanitizer_BeforeProviderUpdate_m65F246118381B43BCAD668849185ECEF7C52C971,
	EventSanitizer_AfterProviderUpdate_m3CD0CF9D1169DE1B1B9A96A564069E282B48E9AA,
	EventSanitizer_Inspect_m9B438E50B9AE5E6CAF8E03679C90D02D11ACCF73,
	NULL,
	NULL,
	NULL,
	NULL,
};
extern void CommandEvent_set_timestamp_m62E4D17DC8DDC56E6A98D5904AE2C24C5E0E0D7C_AdjustorThunk (void);
extern void CommandEvent_get_eventSource_mAE9F153B7FBCF387F82996F35E9A181673800CDB_AdjustorThunk (void);
extern void CommandEvent_set_eventSource_m05EB34E98BDAC88C0C63DAEBFB12E0B9D797644C_AdjustorThunk (void);
extern void CommandEvent_set_playerId_mB59C516FBF8583564870DE3644754B4CA7DF3FDF_AdjustorThunk (void);
extern void CommandEvent_get_eventModifiers_mDE813197A28BFBB82027157E446A56BDA43E8AD8_AdjustorThunk (void);
extern void CommandEvent_set_eventModifiers_m3BA5ECFBD39DCB518239F2063DAA8BA1DCDF8AF4_AdjustorThunk (void);
extern void CommandEvent_ToString_mDD56EB15CDB87552F3E1E7D9A7A1231DED00794D_AdjustorThunk (void);
extern void Event_get_type_m43E61FACF8BB047BB65240475858AB9B81E26D45_AdjustorThunk (void);
extern void Event_get_asObject_m305BBFAF86E3918584403DABC85E04BBEACF6DAF_AdjustorThunk (void);
extern void Event_get_eventSource_m3AB10031C9E651873EF4456C93F2FE5EF93FEC63_AdjustorThunk (void);
extern void Event_get_eventModifiers_mE64227776B41073F39044BAA5E7DF23B81828CEF_AdjustorThunk (void);
extern void Event_Ensure_m74EAA1E0AAD1FBF4C2C5E2DEDDCB678254E9299E_AdjustorThunk (void);
extern void Event_ToString_m40AE120A2A50F4A51EC650CB21E6141EECC7E0EE_AdjustorThunk (void);
extern void Event_get_asKeyEvent_mD0AA1C05803D90E0A783C9EA73529C320DA078F8_AdjustorThunk (void);
extern void Event_get_asPointerEvent_mC75B2F3DDE603A2B3DE0C60D92B55068225644B8_AdjustorThunk (void);
extern void Event_get_asTextInputEvent_m6E628742A95EFC058021FC9AABAF585BBA30FA8A_AdjustorThunk (void);
extern void Event_get_asIMECompositionEvent_mD5CF3917637A15EB115398E8FD9B91A7BAD369A6_AdjustorThunk (void);
extern void Event_get_asCommandEvent_m0217C5C5EBDA55BC07434158EF4C314D9D4367D1_AdjustorThunk (void);
extern void Event_get_asNavigationEvent_m7514BFFC317A37EA5AC2413A4A08FF1986C84461_AdjustorThunk (void);
extern void EventModifiers_IsPressed_m6BAC9B3EACDECA1760C524F4D763432C2FEC10D7_AdjustorThunk (void);
extern void EventModifiers_get_isShiftPressed_m744DB3E0E9AB1E8642ADC89753F32FBEA0CDAB75_AdjustorThunk (void);
extern void EventModifiers_get_isCtrlPressed_mBD0F785EB48BA04709114ADEB4E2A4A13A5A35BF_AdjustorThunk (void);
extern void EventModifiers_get_isAltPressed_m954BB57E4F88201642B1456F6FCA0EA150DACF1A_AdjustorThunk (void);
extern void EventModifiers_get_isMetaPressed_m4DB2C47378F2DC78FEEC776C1BB80DBA683AD8EA_AdjustorThunk (void);
extern void EventModifiers_get_isCapsLockEnabled_m9BA2B54CBA7C6B2A03C2DC5465F7E19E9484C08D_AdjustorThunk (void);
extern void EventModifiers_get_isFunctionKeyPressed_mD221094212B21BA36C9A930253A5C8EC7C822CEE_AdjustorThunk (void);
extern void EventModifiers_get_isNumericPressed_mCEBBBA209E22E1C0DF3F6266DDD39E572189A7DC_AdjustorThunk (void);
extern void EventModifiers_SetPressed_m33664B1E54F26DD03806889C9FE38D6079B00810_AdjustorThunk (void);
extern void EventModifiers_Reset_mF75724904B151CFD1F05B44D8C9851698A2D54C6_AdjustorThunk (void);
extern void EventModifiers_ToString_m039F852BFE0EE0FAD27908AB1D494B8948BA42CA_AdjustorThunk (void);
extern void IMECompositionEvent_set_timestamp_mCF0C8F7EC4CFDA5B52381D5484A81EB8B4E5D1B8_AdjustorThunk (void);
extern void IMECompositionEvent_get_eventSource_m5A9262ED18044F734147AB4A65263C7E455B7CAB_AdjustorThunk (void);
extern void IMECompositionEvent_set_eventSource_mFDE0BE60BC1C8AF8EFCC6437C538446E4FC45F8D_AdjustorThunk (void);
extern void IMECompositionEvent_set_playerId_mD2E85CFEC4CCD2DCB0A3A09F73A588374BE27792_AdjustorThunk (void);
extern void IMECompositionEvent_get_eventModifiers_m4B212B13B5AA27343B784C20FA77266C1D741012_AdjustorThunk (void);
extern void IMECompositionEvent_set_eventModifiers_m8E58584375A5E3CB51743803911AB4F1779C3035_AdjustorThunk (void);
extern void IMECompositionEvent_ToString_m0E99A80480D5870682A59747BAD3E63AC45FFE1E_AdjustorThunk (void);
extern void KeyEvent_set_timestamp_mA1AC56751009BD6C8EF3FC6D2892860D6AC03990_AdjustorThunk (void);
extern void KeyEvent_get_eventSource_m9A74264E7C276EEFA5F603C1EA12BC23034CDE0D_AdjustorThunk (void);
extern void KeyEvent_set_eventSource_m472DC90E7A9CC0B04F163CF606ACEF997493D0C5_AdjustorThunk (void);
extern void KeyEvent_set_playerId_m013ACE8EB81283F1EA7D201C1AC7E918E422304B_AdjustorThunk (void);
extern void KeyEvent_get_eventModifiers_mF90EB0DF537425D3D09B1E095A2B9D24EE448782_AdjustorThunk (void);
extern void KeyEvent_set_eventModifiers_mBD0AFAC76018A0CCCDD5338471370A4B36C740E8_AdjustorThunk (void);
extern void KeyEvent_ToString_mE3FC37D2498578B189857DDF16061937A8BB3EE0_AdjustorThunk (void);
extern void ButtonsState_GetUnchecked_mC4D05E5776EC19AF70558CD43A18E50DFAEF4276_AdjustorThunk (void);
extern void ButtonsState_SetUnchecked_m3DA8C3D711F89AA225C83E5AA3C715B7070090B8_AdjustorThunk (void);
extern void ButtonsState_ClearUnchecked_mFE649DCAA069241AB35FAD93B54C638CBCA83E1D_AdjustorThunk (void);
extern void ButtonsState_IsPressed_m077940991B13B5E5E8C73997B82358A9022D37C3_AdjustorThunk (void);
extern void ButtonsState_GetAllPressed_m116B75F1006DB83D41784159F9144CC01160A42E_AdjustorThunk (void);
extern void ButtonsState_SetPressed_m83A234B0D8B922BC8797FB9DB61AF4B26545F9EA_AdjustorThunk (void);
extern void ButtonsState_Reset_m2682450F34125FB2DB313B9F9528F27FCE18F10B_AdjustorThunk (void);
extern void ButtonsState_ToString_m0F4594EDB720B831AF8A808B19FB753687C6D4E0_AdjustorThunk (void);
extern void NavigationEvent_set_timestamp_mA6CDDEB6A1C29A64AD4B42203ECAB1A628E88983_AdjustorThunk (void);
extern void NavigationEvent_get_eventSource_mD35D875FBB7FA557068DF25BB145292A6EF9FBA0_AdjustorThunk (void);
extern void NavigationEvent_set_eventSource_m74427FF1CC7AA5430F1FEF37F9BA4D50BE5CB2B3_AdjustorThunk (void);
extern void NavigationEvent_set_playerId_mA2E0FFCDFE0D345A8DFF67E567099CDF1A637F36_AdjustorThunk (void);
extern void NavigationEvent_get_eventModifiers_mA4BA9D05B5E5E327B9E5A1889202FA12518AB815_AdjustorThunk (void);
extern void NavigationEvent_set_eventModifiers_m1B4B8E45D1892C2357EF0933D51A613D3156076E_AdjustorThunk (void);
extern void NavigationEvent_ToString_mD59B9C574F8B542874908EF4266F0D9B09E06667_AdjustorThunk (void);
extern void PointerEvent_get_isPrimaryPointer_mEA57B41D6996C4107CDBE1133C92EBAE58EFA007_AdjustorThunk (void);
extern void PointerEvent_get_azimuth_m5FD6788924C58898C59D2D093790102216EA4B6E_AdjustorThunk (void);
extern void PointerEvent_get_altitude_m96D1D73F089B16CDFD1D199A07C310E5456124B4_AdjustorThunk (void);
extern void PointerEvent_get_timestamp_m5A833B8F7FC3F775DD8D6FA05E7A3AA86A3D5035_AdjustorThunk (void);
extern void PointerEvent_set_timestamp_m1332F1F26A4457114C873F78D1F8B486D65B5AD6_AdjustorThunk (void);
extern void PointerEvent_get_eventSource_m433AD39B323BF13AE165F5F9D025444092FAEB33_AdjustorThunk (void);
extern void PointerEvent_set_eventSource_mE12591C4D25BCF5EB9DD502688B73CC5F59A7AD0_AdjustorThunk (void);
extern void PointerEvent_set_playerId_m33852BB774BBD9AB5868145E1B1069658FEA4E2B_AdjustorThunk (void);
extern void PointerEvent_get_eventModifiers_m5C238F94AA34FB172F4D7256E5D68E838E3C5A05_AdjustorThunk (void);
extern void PointerEvent_set_eventModifiers_mECB5C87AE58191D859769627A924B1DF137C168A_AdjustorThunk (void);
extern void PointerEvent_ToString_m7DA27740BA43C97712A917546C18AD0A1C2BCBA3_AdjustorThunk (void);
extern void ButtonsState_Set_m52DB679AB8DF80E2E34A72654B66F922DD835032_AdjustorThunk (void);
extern void ButtonsState_Get_mE6798B0C21BCA75C5CB52BDA200536F9EE3E9893_AdjustorThunk (void);
extern void ButtonsState_Reset_mD96D8954FBCB9ED51CC3D35A0E8A7BAD6B3088AF_AdjustorThunk (void);
extern void ButtonsState_ToString_mDE2CA355C9C0F9A18F6E52599AF852CAF5B73758_AdjustorThunk (void);
extern void TextInputEvent_set_timestamp_m44508CDEF51657A9AEAFB68194E0527AFD7F262B_AdjustorThunk (void);
extern void TextInputEvent_get_eventSource_mD08759B60A91AAC1DD74CD97AABA7F47C1332CF2_AdjustorThunk (void);
extern void TextInputEvent_set_eventSource_m9489D4075B4EF62E68A557B4A569989C11382340_AdjustorThunk (void);
extern void TextInputEvent_set_playerId_mCEC6B21B1EDC0D9E8244CF4519C5FDE05119AC2D_AdjustorThunk (void);
extern void TextInputEvent_get_eventModifiers_m0C34160E76D9DE9F4D307C6EDF4BBF7319BB6E78_AdjustorThunk (void);
extern void TextInputEvent_set_eventModifiers_mA544AB38AD48B6538B27806943DA654223501B2E_AdjustorThunk (void);
extern void TextInputEvent_ToString_m14C55E0242789C632CFE6B231E3C5844BB9C8ABB_AdjustorThunk (void);
extern void ButtonEventsIterator_get_Current_m86D6845A6F6A4489F303ED060476EF59F67CD6DB_AdjustorThunk (void);
extern void ButtonEventsIterator_MoveNext_m742C90ADEB41243BAA32EE2380C5831ED1792A74_AdjustorThunk (void);
extern void ButtonEventsIterator_Reset_m178B46B30A239DE2E1A29E7AA98FF1513EDA17F0_AdjustorThunk (void);
extern void ButtonEventsIterator_System_Collections_IEnumerator_get_Current_mF2064D9D36208EBC155BF0F8150CC36B046AE329_AdjustorThunk (void);
extern void PointerState_get_LastPressedButton_m12CCDCF3247A0A4513EC22E7CFDAFCDF1A12D342_AdjustorThunk (void);
extern void PointerState_set_LastPressedButton_m4B75F3AD3146CB62D189140DC42A5245DAC37D22_AdjustorThunk (void);
extern void PointerState_get_ButtonsState_m9C44C7CD402868FA0A4FB2542E98B9E7278C378B_AdjustorThunk (void);
extern void PointerState_get_NextPressTime_m12D2DCC838A9BB0D21B1C3AD3A4580BFB5949FE2_AdjustorThunk (void);
extern void PointerState_set_NextPressTime_m17CBA6464FE4DE8EB6646AC179F153CF52F9DA8C_AdjustorThunk (void);
extern void PointerState_get_ClickCount_m0378690A745AF06423CF6A01969FDEF06CCC09AA_AdjustorThunk (void);
extern void PointerState_set_ClickCount_m8DC9D4E3E11FB38CA977A6D45DC3431B1798CA17_AdjustorThunk (void);
extern void PointerState_get_LastPosition_m994D92D93C3633D45DE22B239EB6AE38BFC8608E_AdjustorThunk (void);
extern void PointerState_set_LastPosition_mA58AB07C81F5D5D4D5BB8E2E01BF83F93730684B_AdjustorThunk (void);
extern void PointerState_get_LastDisplayIndex_m3873EF0EB7E68124D270FB3F7A2083F53604AA42_AdjustorThunk (void);
extern void PointerState_set_LastDisplayIndex_m76B16012DDA718A2E7A8AD333AABA80E1ED56040_AdjustorThunk (void);
extern void PointerState_get_LastPositionValid_mB3A5D44574F272EA9EC2674A0D408AC8AC200C81_AdjustorThunk (void);
extern void PointerState_set_LastPositionValid_mFDF1E68670710967C93480FCAF23AEA96DE5D621_AdjustorThunk (void);
extern void PointerState_OnButtonDown_m79BD7ABD8CD0C0632088E98AD39B160FCC05CE3F_AdjustorThunk (void);
extern void PointerState_OnButtonUp_mCE8DE6B7E8D38FCF45C848F49547998CA39C1EB6_AdjustorThunk (void);
extern void PointerState_OnButtonChange_m76D1C399434C6B40BB7E4880B0E3109744A91E65_AdjustorThunk (void);
extern void PointerState_OnMove_mFCF40645372BEA0B69DA5A5C4064BC6A11068972_AdjustorThunk (void);
extern void PointerState_Reset_m6DD45B949BC56BA9C68497B3F1178BCBFC70462F_AdjustorThunk (void);
extern void EventSanitizer_Reset_m3B7D88555BBA84A9C4EFF2A2770AB161D09F20EB_AdjustorThunk (void);
extern void EventSanitizer_BeforeProviderUpdate_m65F246118381B43BCAD668849185ECEF7C52C971_AdjustorThunk (void);
extern void EventSanitizer_AfterProviderUpdate_m3CD0CF9D1169DE1B1B9A96A564069E282B48E9AA_AdjustorThunk (void);
extern void EventSanitizer_Inspect_m9B438E50B9AE5E6CAF8E03679C90D02D11ACCF73_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[107] = 
{
	{ 0x06000001, CommandEvent_set_timestamp_m62E4D17DC8DDC56E6A98D5904AE2C24C5E0E0D7C_AdjustorThunk },
	{ 0x06000002, CommandEvent_get_eventSource_mAE9F153B7FBCF387F82996F35E9A181673800CDB_AdjustorThunk },
	{ 0x06000003, CommandEvent_set_eventSource_m05EB34E98BDAC88C0C63DAEBFB12E0B9D797644C_AdjustorThunk },
	{ 0x06000004, CommandEvent_set_playerId_mB59C516FBF8583564870DE3644754B4CA7DF3FDF_AdjustorThunk },
	{ 0x06000005, CommandEvent_get_eventModifiers_mDE813197A28BFBB82027157E446A56BDA43E8AD8_AdjustorThunk },
	{ 0x06000006, CommandEvent_set_eventModifiers_m3BA5ECFBD39DCB518239F2063DAA8BA1DCDF8AF4_AdjustorThunk },
	{ 0x06000007, CommandEvent_ToString_mDD56EB15CDB87552F3E1E7D9A7A1231DED00794D_AdjustorThunk },
	{ 0x06000009, Event_get_type_m43E61FACF8BB047BB65240475858AB9B81E26D45_AdjustorThunk },
	{ 0x0600000A, Event_get_asObject_m305BBFAF86E3918584403DABC85E04BBEACF6DAF_AdjustorThunk },
	{ 0x0600000B, Event_get_eventSource_m3AB10031C9E651873EF4456C93F2FE5EF93FEC63_AdjustorThunk },
	{ 0x0600000C, Event_get_eventModifiers_mE64227776B41073F39044BAA5E7DF23B81828CEF_AdjustorThunk },
	{ 0x0600000D, Event_Ensure_m74EAA1E0AAD1FBF4C2C5E2DEDDCB678254E9299E_AdjustorThunk },
	{ 0x0600000E, Event_ToString_m40AE120A2A50F4A51EC650CB21E6141EECC7E0EE_AdjustorThunk },
	{ 0x06000010, Event_get_asKeyEvent_mD0AA1C05803D90E0A783C9EA73529C320DA078F8_AdjustorThunk },
	{ 0x06000012, Event_get_asPointerEvent_mC75B2F3DDE603A2B3DE0C60D92B55068225644B8_AdjustorThunk },
	{ 0x06000014, Event_get_asTextInputEvent_m6E628742A95EFC058021FC9AABAF585BBA30FA8A_AdjustorThunk },
	{ 0x06000016, Event_get_asIMECompositionEvent_mD5CF3917637A15EB115398E8FD9B91A7BAD369A6_AdjustorThunk },
	{ 0x06000018, Event_get_asCommandEvent_m0217C5C5EBDA55BC07434158EF4C314D9D4367D1_AdjustorThunk },
	{ 0x0600001A, Event_get_asNavigationEvent_m7514BFFC317A37EA5AC2413A4A08FF1986C84461_AdjustorThunk },
	{ 0x06000022, EventModifiers_IsPressed_m6BAC9B3EACDECA1760C524F4D763432C2FEC10D7_AdjustorThunk },
	{ 0x06000023, EventModifiers_get_isShiftPressed_m744DB3E0E9AB1E8642ADC89753F32FBEA0CDAB75_AdjustorThunk },
	{ 0x06000024, EventModifiers_get_isCtrlPressed_mBD0F785EB48BA04709114ADEB4E2A4A13A5A35BF_AdjustorThunk },
	{ 0x06000025, EventModifiers_get_isAltPressed_m954BB57E4F88201642B1456F6FCA0EA150DACF1A_AdjustorThunk },
	{ 0x06000026, EventModifiers_get_isMetaPressed_m4DB2C47378F2DC78FEEC776C1BB80DBA683AD8EA_AdjustorThunk },
	{ 0x06000027, EventModifiers_get_isCapsLockEnabled_m9BA2B54CBA7C6B2A03C2DC5465F7E19E9484C08D_AdjustorThunk },
	{ 0x06000028, EventModifiers_get_isFunctionKeyPressed_mD221094212B21BA36C9A930253A5C8EC7C822CEE_AdjustorThunk },
	{ 0x06000029, EventModifiers_get_isNumericPressed_mCEBBBA209E22E1C0DF3F6266DDD39E572189A7DC_AdjustorThunk },
	{ 0x0600002A, EventModifiers_SetPressed_m33664B1E54F26DD03806889C9FE38D6079B00810_AdjustorThunk },
	{ 0x0600002B, EventModifiers_Reset_mF75724904B151CFD1F05B44D8C9851698A2D54C6_AdjustorThunk },
	{ 0x0600002D, EventModifiers_ToString_m039F852BFE0EE0FAD27908AB1D494B8948BA42CA_AdjustorThunk },
	{ 0x06000030, IMECompositionEvent_set_timestamp_mCF0C8F7EC4CFDA5B52381D5484A81EB8B4E5D1B8_AdjustorThunk },
	{ 0x06000031, IMECompositionEvent_get_eventSource_m5A9262ED18044F734147AB4A65263C7E455B7CAB_AdjustorThunk },
	{ 0x06000032, IMECompositionEvent_set_eventSource_mFDE0BE60BC1C8AF8EFCC6437C538446E4FC45F8D_AdjustorThunk },
	{ 0x06000033, IMECompositionEvent_set_playerId_mD2E85CFEC4CCD2DCB0A3A09F73A588374BE27792_AdjustorThunk },
	{ 0x06000034, IMECompositionEvent_get_eventModifiers_m4B212B13B5AA27343B784C20FA77266C1D741012_AdjustorThunk },
	{ 0x06000035, IMECompositionEvent_set_eventModifiers_m8E58584375A5E3CB51743803911AB4F1779C3035_AdjustorThunk },
	{ 0x06000036, IMECompositionEvent_ToString_m0E99A80480D5870682A59747BAD3E63AC45FFE1E_AdjustorThunk },
	{ 0x06000037, KeyEvent_set_timestamp_mA1AC56751009BD6C8EF3FC6D2892860D6AC03990_AdjustorThunk },
	{ 0x06000038, KeyEvent_get_eventSource_m9A74264E7C276EEFA5F603C1EA12BC23034CDE0D_AdjustorThunk },
	{ 0x06000039, KeyEvent_set_eventSource_m472DC90E7A9CC0B04F163CF606ACEF997493D0C5_AdjustorThunk },
	{ 0x0600003A, KeyEvent_set_playerId_m013ACE8EB81283F1EA7D201C1AC7E918E422304B_AdjustorThunk },
	{ 0x0600003B, KeyEvent_get_eventModifiers_mF90EB0DF537425D3D09B1E095A2B9D24EE448782_AdjustorThunk },
	{ 0x0600003C, KeyEvent_set_eventModifiers_mBD0AFAC76018A0CCCDD5338471370A4B36C740E8_AdjustorThunk },
	{ 0x0600003D, KeyEvent_ToString_mE3FC37D2498578B189857DDF16061937A8BB3EE0_AdjustorThunk },
	{ 0x0600003F, ButtonsState_GetUnchecked_mC4D05E5776EC19AF70558CD43A18E50DFAEF4276_AdjustorThunk },
	{ 0x06000040, ButtonsState_SetUnchecked_m3DA8C3D711F89AA225C83E5AA3C715B7070090B8_AdjustorThunk },
	{ 0x06000041, ButtonsState_ClearUnchecked_mFE649DCAA069241AB35FAD93B54C638CBCA83E1D_AdjustorThunk },
	{ 0x06000042, ButtonsState_IsPressed_m077940991B13B5E5E8C73997B82358A9022D37C3_AdjustorThunk },
	{ 0x06000043, ButtonsState_GetAllPressed_m116B75F1006DB83D41784159F9144CC01160A42E_AdjustorThunk },
	{ 0x06000044, ButtonsState_SetPressed_m83A234B0D8B922BC8797FB9DB61AF4B26545F9EA_AdjustorThunk },
	{ 0x06000045, ButtonsState_Reset_m2682450F34125FB2DB313B9F9528F27FCE18F10B_AdjustorThunk },
	{ 0x06000046, ButtonsState_ToString_m0F4594EDB720B831AF8A808B19FB753687C6D4E0_AdjustorThunk },
	{ 0x0600004F, NavigationEvent_set_timestamp_mA6CDDEB6A1C29A64AD4B42203ECAB1A628E88983_AdjustorThunk },
	{ 0x06000050, NavigationEvent_get_eventSource_mD35D875FBB7FA557068DF25BB145292A6EF9FBA0_AdjustorThunk },
	{ 0x06000051, NavigationEvent_set_eventSource_m74427FF1CC7AA5430F1FEF37F9BA4D50BE5CB2B3_AdjustorThunk },
	{ 0x06000052, NavigationEvent_set_playerId_mA2E0FFCDFE0D345A8DFF67E567099CDF1A637F36_AdjustorThunk },
	{ 0x06000053, NavigationEvent_get_eventModifiers_mA4BA9D05B5E5E327B9E5A1889202FA12518AB815_AdjustorThunk },
	{ 0x06000054, NavigationEvent_set_eventModifiers_m1B4B8E45D1892C2357EF0933D51A613D3156076E_AdjustorThunk },
	{ 0x06000055, NavigationEvent_ToString_mD59B9C574F8B542874908EF4266F0D9B09E06667_AdjustorThunk },
	{ 0x06000057, PointerEvent_get_isPrimaryPointer_mEA57B41D6996C4107CDBE1133C92EBAE58EFA007_AdjustorThunk },
	{ 0x06000058, PointerEvent_get_azimuth_m5FD6788924C58898C59D2D093790102216EA4B6E_AdjustorThunk },
	{ 0x06000059, PointerEvent_get_altitude_m96D1D73F089B16CDFD1D199A07C310E5456124B4_AdjustorThunk },
	{ 0x0600005A, PointerEvent_get_timestamp_m5A833B8F7FC3F775DD8D6FA05E7A3AA86A3D5035_AdjustorThunk },
	{ 0x0600005B, PointerEvent_set_timestamp_m1332F1F26A4457114C873F78D1F8B486D65B5AD6_AdjustorThunk },
	{ 0x0600005C, PointerEvent_get_eventSource_m433AD39B323BF13AE165F5F9D025444092FAEB33_AdjustorThunk },
	{ 0x0600005D, PointerEvent_set_eventSource_mE12591C4D25BCF5EB9DD502688B73CC5F59A7AD0_AdjustorThunk },
	{ 0x0600005E, PointerEvent_set_playerId_m33852BB774BBD9AB5868145E1B1069658FEA4E2B_AdjustorThunk },
	{ 0x0600005F, PointerEvent_get_eventModifiers_m5C238F94AA34FB172F4D7256E5D68E838E3C5A05_AdjustorThunk },
	{ 0x06000060, PointerEvent_set_eventModifiers_mECB5C87AE58191D859769627A924B1DF137C168A_AdjustorThunk },
	{ 0x06000061, PointerEvent_ToString_m7DA27740BA43C97712A917546C18AD0A1C2BCBA3_AdjustorThunk },
	{ 0x06000063, ButtonsState_Set_m52DB679AB8DF80E2E34A72654B66F922DD835032_AdjustorThunk },
	{ 0x06000064, ButtonsState_Get_mE6798B0C21BCA75C5CB52BDA200536F9EE3E9893_AdjustorThunk },
	{ 0x06000065, ButtonsState_Reset_mD96D8954FBCB9ED51CC3D35A0E8A7BAD6B3088AF_AdjustorThunk },
	{ 0x06000066, ButtonsState_ToString_mDE2CA355C9C0F9A18F6E52599AF852CAF5B73758_AdjustorThunk },
	{ 0x06000067, TextInputEvent_set_timestamp_m44508CDEF51657A9AEAFB68194E0527AFD7F262B_AdjustorThunk },
	{ 0x06000068, TextInputEvent_get_eventSource_mD08759B60A91AAC1DD74CD97AABA7F47C1332CF2_AdjustorThunk },
	{ 0x06000069, TextInputEvent_set_eventSource_m9489D4075B4EF62E68A557B4A569989C11382340_AdjustorThunk },
	{ 0x0600006A, TextInputEvent_set_playerId_mCEC6B21B1EDC0D9E8244CF4519C5FDE05119AC2D_AdjustorThunk },
	{ 0x0600006B, TextInputEvent_get_eventModifiers_m0C34160E76D9DE9F4D307C6EDF4BBF7319BB6E78_AdjustorThunk },
	{ 0x0600006C, TextInputEvent_set_eventModifiers_mA544AB38AD48B6538B27806943DA654223501B2E_AdjustorThunk },
	{ 0x0600006D, TextInputEvent_ToString_m14C55E0242789C632CFE6B231E3C5844BB9C8ABB_AdjustorThunk },
	{ 0x060000AE, ButtonEventsIterator_get_Current_m86D6845A6F6A4489F303ED060476EF59F67CD6DB_AdjustorThunk },
	{ 0x060000AF, ButtonEventsIterator_MoveNext_m742C90ADEB41243BAA32EE2380C5831ED1792A74_AdjustorThunk },
	{ 0x060000B0, ButtonEventsIterator_Reset_m178B46B30A239DE2E1A29E7AA98FF1513EDA17F0_AdjustorThunk },
	{ 0x060000B1, ButtonEventsIterator_System_Collections_IEnumerator_get_Current_mF2064D9D36208EBC155BF0F8150CC36B046AE329_AdjustorThunk },
	{ 0x060000D7, PointerState_get_LastPressedButton_m12CCDCF3247A0A4513EC22E7CFDAFCDF1A12D342_AdjustorThunk },
	{ 0x060000D8, PointerState_set_LastPressedButton_m4B75F3AD3146CB62D189140DC42A5245DAC37D22_AdjustorThunk },
	{ 0x060000D9, PointerState_get_ButtonsState_m9C44C7CD402868FA0A4FB2542E98B9E7278C378B_AdjustorThunk },
	{ 0x060000DA, PointerState_get_NextPressTime_m12D2DCC838A9BB0D21B1C3AD3A4580BFB5949FE2_AdjustorThunk },
	{ 0x060000DB, PointerState_set_NextPressTime_m17CBA6464FE4DE8EB6646AC179F153CF52F9DA8C_AdjustorThunk },
	{ 0x060000DC, PointerState_get_ClickCount_m0378690A745AF06423CF6A01969FDEF06CCC09AA_AdjustorThunk },
	{ 0x060000DD, PointerState_set_ClickCount_m8DC9D4E3E11FB38CA977A6D45DC3431B1798CA17_AdjustorThunk },
	{ 0x060000DE, PointerState_get_LastPosition_m994D92D93C3633D45DE22B239EB6AE38BFC8608E_AdjustorThunk },
	{ 0x060000DF, PointerState_set_LastPosition_mA58AB07C81F5D5D4D5BB8E2E01BF83F93730684B_AdjustorThunk },
	{ 0x060000E0, PointerState_get_LastDisplayIndex_m3873EF0EB7E68124D270FB3F7A2083F53604AA42_AdjustorThunk },
	{ 0x060000E1, PointerState_set_LastDisplayIndex_m76B16012DDA718A2E7A8AD333AABA80E1ED56040_AdjustorThunk },
	{ 0x060000E2, PointerState_get_LastPositionValid_mB3A5D44574F272EA9EC2674A0D408AC8AC200C81_AdjustorThunk },
	{ 0x060000E3, PointerState_set_LastPositionValid_mFDF1E68670710967C93480FCAF23AEA96DE5D621_AdjustorThunk },
	{ 0x060000E4, PointerState_OnButtonDown_m79BD7ABD8CD0C0632088E98AD39B160FCC05CE3F_AdjustorThunk },
	{ 0x060000E5, PointerState_OnButtonUp_mCE8DE6B7E8D38FCF45C848F49547998CA39C1EB6_AdjustorThunk },
	{ 0x060000E6, PointerState_OnButtonChange_m76D1C399434C6B40BB7E4880B0E3109744A91E65_AdjustorThunk },
	{ 0x060000E7, PointerState_OnMove_mFCF40645372BEA0B69DA5A5C4064BC6A11068972_AdjustorThunk },
	{ 0x060000E8, PointerState_Reset_m6DD45B949BC56BA9C68497B3F1178BCBFC70462F_AdjustorThunk },
	{ 0x060000EA, EventSanitizer_Reset_m3B7D88555BBA84A9C4EFF2A2770AB161D09F20EB_AdjustorThunk },
	{ 0x060000EB, EventSanitizer_BeforeProviderUpdate_m65F246118381B43BCAD668849185ECEF7C52C971_AdjustorThunk },
	{ 0x060000EC, EventSanitizer_AfterProviderUpdate_m3CD0CF9D1169DE1B1B9A96A564069E282B48E9AA_AdjustorThunk },
	{ 0x060000ED, EventSanitizer_Inspect_m9B438E50B9AE5E6CAF8E03679C90D02D11ACCF73_AdjustorThunk },
};
static const int32_t s_InvokerIndices[241] = 
{
	5513,
	6946,
	5562,
	5693,
	6903,
	5522,
	6985,
	9004,
	6946,
	6985,
	6946,
	6903,
	5562,
	6985,
	9663,
	6954,
	9665,
	6995,
	9666,
	7066,
	9662,
	6931,
	9661,
	6875,
	9664,
	6983,
	-1,
	-1,
	10294,
	-1,
	-1,
	-1,
	-1,
	3948,
	6869,
	6869,
	6869,
	6869,
	6869,
	6869,
	6869,
	2958,
	7102,
	9291,
	6985,
	-1,
	-1,
	5513,
	6946,
	5562,
	5693,
	6903,
	5522,
	6985,
	5513,
	6946,
	5562,
	5693,
	6903,
	5522,
	6985,
	9605,
	3948,
	5693,
	5693,
	3816,
	6985,
	2534,
	7102,
	6985,
	5562,
	7102,
	6869,
	6946,
	7102,
	6985,
	6985,
	6985,
	5513,
	6946,
	5562,
	5693,
	6903,
	5522,
	6985,
	9036,
	6869,
	7033,
	7033,
	6894,
	5513,
	6946,
	5562,
	5693,
	6903,
	5522,
	6985,
	9948,
	2958,
	3948,
	7102,
	6985,
	5513,
	6946,
	5562,
	5693,
	6903,
	5522,
	6985,
	8147,
	10002,
	9992,
	9991,
	10294,
	10294,
	10294,
	9992,
	10294,
	10002,
	10294,
	10294,
	7102,
	2045,
	7102,
	3987,
	2866,
	3713,
	-1,
	-1,
	-1,
	-1,
	7102,
	7102,
	7102,
	5484,
	3816,
	4121,
	5468,
	4713,
	4933,
	5468,
	4089,
	7102,
	6903,
	7102,
	7102,
	7102,
	7102,
	3770,
	7102,
	9948,
	1569,
	2316,
	5513,
	5513,
	5513,
	5513,
	5513,
	5484,
	6946,
	6869,
	6869,
	4865,
	3852,
	6851,
	1847,
	9891,
	9224,
	9891,
	9226,
	9226,
	9227,
	9976,
	6869,
	6869,
	7102,
	6985,
	8186,
	10302,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	6985,
	3816,
	3852,
	4865,
	6989,
	6869,
	6946,
	4949,
	6869,
	3816,
	3816,
	3816,
	7094,
	7092,
	7102,
	-1,
	7007,
	7102,
	7102,
	1019,
	7102,
	7088,
	5693,
	7197,
	6894,
	5513,
	6946,
	5562,
	7092,
	5697,
	6946,
	5562,
	6869,
	5484,
	2317,
	2317,
	877,
	1251,
	7102,
	10294,
	7102,
	7102,
	7102,
	5468,
	-1,
	-1,
	-1,
	-1,
};
static const Il2CppTokenRangePair s_rgctxIndices[5] = 
{
	{ 0x0600001B, { 0, 9 } },
	{ 0x0600001C, { 9, 4 } },
	{ 0x0600001F, { 13, 2 } },
	{ 0x06000020, { 15, 3 } },
	{ 0x06000021, { 18, 3 } },
};
extern const uint32_t g_rgctx_TOutputType_tAF4888CAC03693F38C314D38D4FAC145904CD8D1;
extern const uint32_t g_rgctx_TMapType_t2539BF2FB400455ECA567C04283D0611DFFB7287;
extern const uint32_t g_rgctx_IMapFn_1_t34C4B47724C005C16C1E2DB5A8A7C04436D97EAE;
extern const Il2CppRGCTXConstrainedData g_rgctx_TMapType_t2539BF2FB400455ECA567C04283D0611DFFB7287_IMapFn_1_Map_TisKeyEvent_tCC737454044642F99DE2CDD2F11E792AE596F931_mD28DDD0F29A051359E249B557097D2A3E9E03664;
extern const Il2CppRGCTXConstrainedData g_rgctx_TMapType_t2539BF2FB400455ECA567C04283D0611DFFB7287_IMapFn_1_Map_TisPointerEvent_tC25A522EBDFCAF2CDEEE1D70428C281F78C1FA05_m82DE231FDE921428E0EDD907BA8D92EA21498F53;
extern const Il2CppRGCTXConstrainedData g_rgctx_TMapType_t2539BF2FB400455ECA567C04283D0611DFFB7287_IMapFn_1_Map_TisTextInputEvent_t178912D1FB7425ABEAA189639883A2966679C87A_m1635D64D0F57D0A2E36D42F481788CAECD8C4EF6;
extern const Il2CppRGCTXConstrainedData g_rgctx_TMapType_t2539BF2FB400455ECA567C04283D0611DFFB7287_IMapFn_1_Map_TisIMECompositionEvent_tDB878E24C29EAF63886AD297892F6408D74D9FE4_mA2B17409A555CE83B08FA02CD1D3411F133DB9A9;
extern const Il2CppRGCTXConstrainedData g_rgctx_TMapType_t2539BF2FB400455ECA567C04283D0611DFFB7287_IMapFn_1_Map_TisCommandEvent_tCA81E54195CF66429904DCE8EE4DFD25BEEAC06F_mF52A86F30B47B0B9B647CEF5F3CE6233F6FD1B52;
extern const Il2CppRGCTXConstrainedData g_rgctx_TMapType_t2539BF2FB400455ECA567C04283D0611DFFB7287_IMapFn_1_Map_TisNavigationEvent_t33FC4003A394B4773A3C23C5B0C134D749E544BD_m0C4078D3E46690F27334102AA8D0FC043DAEA922;
extern const uint32_t g_rgctx_Activator_CreateInstance_TisTMapType_t7B9D42EACE2DE78A8D0BBD55D7A9A3E39DC3EEE3_m136A81600E77AE46CA92D1D41EB15FAD4341FB86;
extern const uint32_t g_rgctx_TMapType_t7B9D42EACE2DE78A8D0BBD55D7A9A3E39DC3EEE3;
extern const uint32_t g_rgctx_Event_Map_TisTOutputType_tDCB476FF48B5F679F4556FE8EB20C214BCF69482_TisTMapType_t7B9D42EACE2DE78A8D0BBD55D7A9A3E39DC3EEE3_m555C9A94B0F88181E9C13E7D2CC7CFA53562ED6C;
extern const uint32_t g_rgctx_TOutputType_tDCB476FF48B5F679F4556FE8EB20C214BCF69482;
extern const uint32_t g_rgctx_TEventTypeU26_t73F15EF4568578F6799B690AC74A055135812E51;
extern const uint32_t g_rgctx_TEventType_tA6204FE5FDF30343C6434FB2DD5D9864B0B26281;
extern const uint32_t g_rgctx_TEventTypeU26_t6AEEEE1E2D93F986A4C24BD675A0185E390A642E;
extern const uint32_t g_rgctx_TEventType_t57A7280BF671DE50785AC4BA95C810E79E4762CC;
extern const Il2CppRGCTXConstrainedData g_rgctx_TEventType_t57A7280BF671DE50785AC4BA95C810E79E4762CC_IEventProperties_get_eventSource_m50023C6BA5D5672D3C7D4B30F4420B27E93E7456;
extern const uint32_t g_rgctx_TEventTypeU26_t5BFA7EC43C8E82E177643DC418010A5B3A5E8A1E;
extern const uint32_t g_rgctx_TEventType_t126E42DAD6F4DEB3B0E49038C26154C2A411FFAE;
extern const Il2CppRGCTXConstrainedData g_rgctx_TEventType_t126E42DAD6F4DEB3B0E49038C26154C2A411FFAE_IEventProperties_get_eventModifiers_mC0A8952528593DB4A53B4129E84DD7D6963EA706;
static const Il2CppRGCTXDefinition s_rgctxValues[21] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TOutputType_tAF4888CAC03693F38C314D38D4FAC145904CD8D1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TMapType_t2539BF2FB400455ECA567C04283D0611DFFB7287 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IMapFn_1_t34C4B47724C005C16C1E2DB5A8A7C04436D97EAE },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TMapType_t2539BF2FB400455ECA567C04283D0611DFFB7287_IMapFn_1_Map_TisKeyEvent_tCC737454044642F99DE2CDD2F11E792AE596F931_mD28DDD0F29A051359E249B557097D2A3E9E03664 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TMapType_t2539BF2FB400455ECA567C04283D0611DFFB7287_IMapFn_1_Map_TisPointerEvent_tC25A522EBDFCAF2CDEEE1D70428C281F78C1FA05_m82DE231FDE921428E0EDD907BA8D92EA21498F53 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TMapType_t2539BF2FB400455ECA567C04283D0611DFFB7287_IMapFn_1_Map_TisTextInputEvent_t178912D1FB7425ABEAA189639883A2966679C87A_m1635D64D0F57D0A2E36D42F481788CAECD8C4EF6 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TMapType_t2539BF2FB400455ECA567C04283D0611DFFB7287_IMapFn_1_Map_TisIMECompositionEvent_tDB878E24C29EAF63886AD297892F6408D74D9FE4_mA2B17409A555CE83B08FA02CD1D3411F133DB9A9 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TMapType_t2539BF2FB400455ECA567C04283D0611DFFB7287_IMapFn_1_Map_TisCommandEvent_tCA81E54195CF66429904DCE8EE4DFD25BEEAC06F_mF52A86F30B47B0B9B647CEF5F3CE6233F6FD1B52 },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TMapType_t2539BF2FB400455ECA567C04283D0611DFFB7287_IMapFn_1_Map_TisNavigationEvent_t33FC4003A394B4773A3C23C5B0C134D749E544BD_m0C4078D3E46690F27334102AA8D0FC043DAEA922 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Activator_CreateInstance_TisTMapType_t7B9D42EACE2DE78A8D0BBD55D7A9A3E39DC3EEE3_m136A81600E77AE46CA92D1D41EB15FAD4341FB86 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TMapType_t7B9D42EACE2DE78A8D0BBD55D7A9A3E39DC3EEE3 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Event_Map_TisTOutputType_tDCB476FF48B5F679F4556FE8EB20C214BCF69482_TisTMapType_t7B9D42EACE2DE78A8D0BBD55D7A9A3E39DC3EEE3_m555C9A94B0F88181E9C13E7D2CC7CFA53562ED6C },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TOutputType_tDCB476FF48B5F679F4556FE8EB20C214BCF69482 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TEventTypeU26_t73F15EF4568578F6799B690AC74A055135812E51 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TEventType_tA6204FE5FDF30343C6434FB2DD5D9864B0B26281 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TEventTypeU26_t6AEEEE1E2D93F986A4C24BD675A0185E390A642E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TEventType_t57A7280BF671DE50785AC4BA95C810E79E4762CC },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TEventType_t57A7280BF671DE50785AC4BA95C810E79E4762CC_IEventProperties_get_eventSource_m50023C6BA5D5672D3C7D4B30F4420B27E93E7456 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TEventTypeU26_t5BFA7EC43C8E82E177643DC418010A5B3A5E8A1E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TEventType_t126E42DAD6F4DEB3B0E49038C26154C2A411FFAE },
	{ (Il2CppRGCTXDataType)5, (const void *)&g_rgctx_TEventType_t126E42DAD6F4DEB3B0E49038C26154C2A411FFAE_IEventProperties_get_eventModifiers_mC0A8952528593DB4A53B4129E84DD7D6963EA706 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_InputForUIModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_InputForUIModule_CodeGenModule = 
{
	"UnityEngine.InputForUIModule.dll",
	241,
	s_methodPointers,
	107,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	5,
	s_rgctxIndices,
	21,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

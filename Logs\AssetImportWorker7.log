Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.30f1 (62b05ba0686a) revision 6467675'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker7
-projectPath
D:/My Project/Driving Simulator Game Z TEC
-logFile
Logs/AssetImportWorker7.log
-srvPort
53742
-job-worker-count
11
-background-job-worker-count
8
-asset-garbage-collectors
1
Successfully changed project path to: D:/My Project/Driving Simulator Game Z TEC
D:/My Project/Driving Simulator Game Z TEC
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [33580]  Target information:

Player connection [33580]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2078307246 [EditorId] 2078307246 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [33580]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 2078307246 [EditorId] 2078307246 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [33580] Host joined multi-casting on [***********:54997]...
Player connection [33580] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 11
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.94 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.30f1 (62b05ba0686a)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Driving Simulator Game Z TEC/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56576
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.011609 seconds.
- Loaded All Assemblies, in  0.984 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 544 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.451 seconds
Domain Reload Profiling: 2432ms
	BeginReloadAssembly (313ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (112ms)
	RebuildNativeTypeToScriptingClass (40ms)
	initialDomainReloadingComplete (137ms)
	LoadAllAssembliesAndSetupDomain (377ms)
		LoadAssemblies (306ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (370ms)
			TypeCache.Refresh (367ms)
				TypeCache.ScanAssembly (330ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1452ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1330ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (775ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (140ms)
			ProcessInitializeOnLoadAttributes (279ms)
			ProcessInitializeOnLoadMethodAttributes (126ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.992 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.38 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.365 seconds
Domain Reload Profiling: 3350ms
	BeginReloadAssembly (467ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (77ms)
	RebuildCommonClasses (120ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (90ms)
	LoadAllAssembliesAndSetupDomain (1283ms)
		LoadAssemblies (906ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (671ms)
			TypeCache.Refresh (550ms)
				TypeCache.ScanAssembly (502ms)
			BuildScriptInfoCaches (100ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1366ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1070ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (25ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (197ms)
			ProcessInitializeOnLoadAttributes (774ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (12ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 1.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 66 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4568 unused Assets / (1.8 MB). Loaded Objects now: 5129.
Memory consumption went from 117.4 MB to 115.6 MB.
Total: 17.708000 ms (FindLiveObjects: 0.815100 ms CreateObjectMapping: 0.575000 ms MarkObjects: 12.301400 ms  DeleteObjects: 4.013200 ms)

========================================================================
Received Import Request.
  Time since last request: 14584.925737 seconds.
  path: Assets/AdvancedHelicopterController/Prefabs/Collectables/Particles/Collectable_Particle.prefab
  artifactKey: Guid(d06a8366d14c045b295eb48280e10317) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Prefabs/Collectables/Particles/Collectable_Particle.prefab using Guid(d06a8366d14c045b295eb48280e10317) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c390399ff951f04f5fb2f3e111311201') in 0.2249298 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/_Prefabs/CargoWagon02_Orange_Gravel.prefab
  artifactKey: Guid(da91e4de5723f774c93157bf27693a37) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/_Prefabs/CargoWagon02_Orange_Gravel.prefab using Guid(da91e4de5723f774c93157bf27693a37) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '18ca8dcbce7e52618a7816a55a4a6406') in 1.0066464 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 255

========================================================================
Received Import Request.
  Time since last request: 0.000065 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/_Prefabs/CableCar_01_Blue Variant.prefab
  artifactKey: Guid(0925c86837fa08c4c85ce68ed46917bd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/_Prefabs/CableCar_01_Blue Variant.prefab using Guid(0925c86837fa08c4c85ce68ed46917bd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '21a178adf44eaa8bf312b62a7adefb8f') in 3.7195971 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1377

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/Invector-3rdPersonController_LITE/3D Models/Others/NewAssets/stepUp.prefab
  artifactKey: Guid(b485a54006d31cc4589985f0b9f75dc6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Invector-3rdPersonController_LITE/3D Models/Others/NewAssets/stepUp.prefab using Guid(b485a54006d31cc4589985f0b9f75dc6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5edce56c3e93b10d791ab2e48b56a10f') in 0.3346162 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Control Zones/SwitchTriggerZone.prefab
  artifactKey: Guid(6296b99258f9e5442813805a9ff22527) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Control Zones/SwitchTriggerZone.prefab using Guid(6296b99258f9e5442813805a9ff22527) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7e990c1c467c01a1da300da550b64358') in 0.0033841 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Parts/Sensors/TenderWagon_01_PassengerSensor.prefab
  artifactKey: Guid(c6b9fa77620604541b6451937026b00e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Parts/Sensors/TenderWagon_01_PassengerSensor.prefab using Guid(c6b9fa77620604541b6451937026b00e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '21ca3808dacdeabd7c2f8608a2616608') in 0.0019549 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Subway_01_Door_Single.fbx
  artifactKey: Guid(a6ecf0544d711f84f86c9ce357b36ee0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Subway_01_Door_Single.fbx using Guid(a6ecf0544d711f84f86c9ce357b36ee0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '54e8f5a0478a090172cd1193c848d489') in 0.1790896 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Deprecated/Locomotive_Red.prefab
  artifactKey: Guid(1d24929dd48830042a3d9771ff2b74e2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Deprecated/Locomotive_Red.prefab using Guid(1d24929dd48830042a3d9771ff2b74e2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '86f08cf678fa24a6ff586538af2e69c6') in 0.3996939 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 229

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Parts/Colliders/TenderWagon_01_Colliders.prefab
  artifactKey: Guid(35df8568d75d8bd4ca7043ed76efb259) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Parts/Colliders/TenderWagon_01_Colliders.prefab using Guid(35df8568d75d8bd4ca7043ed76efb259) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a31862c997079e51f307ac8c96c53a2e') in 0.0025455 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/SubwayCart_01_B.prefab
  artifactKey: Guid(8c3d6bb50a4d2a0408661668cf20b281) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/SubwayCart_01_B.prefab using Guid(8c3d6bb50a4d2a0408661668cf20b281) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4079478aa06435c09bcb2e248617cf44') in 1.7829362 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1256

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/MSK 2.2/Models/Street/Street.FBX
  artifactKey: Guid(d40cda62855726d4e8af62df4770098a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MSK 2.2/Models/Street/Street.FBX using Guid(d40cda62855726d4e8af62df4770098a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '471449e8ad7473502778e96ba629cd11') in 0.0736593 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_ACW_5_prefab.prefab
  artifactKey: Guid(722ba9ccb1a484a4aafc843cef04aa5a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_ACW_5_prefab.prefab using Guid(722ba9ccb1a484a4aafc843cef04aa5a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1ecc88d1745dcc18d8415d68914b18ba') in 0.727274 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 64

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_CW_1_prefab.prefab
  artifactKey: Guid(c461c7846c09d8e4aa6c7da045b50e33) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_CW_1_prefab.prefab using Guid(c461c7846c09d8e4aa6c7da045b50e33) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'defde1ed75f097bd81da9e0b836f11bb') in 0.5261581 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 64

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/UnityTechnologies/EffectExamples/Shared/Prefabs/Metal.prefab
  artifactKey: Guid(560a71b792ff85a4ba8f839081f9a379) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/Shared/Prefabs/Metal.prefab using Guid(560a71b792ff85a4ba8f839081f9a379) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '57c83a4e23837d27e0279a3e1dfceea2') in 0.0331435 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Misc/Switch_01_Red.prefab
  artifactKey: Guid(27d077d3d4ad07c45a633b8f549f203b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Misc/Switch_01_Red.prefab using Guid(27d077d3d4ad07c45a633b8f549f203b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '58ccd16217bba330da5439b8f13a815e') in 0.1412924 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Parts/InternalDetails/TenderWagon_01_Coal.prefab
  artifactKey: Guid(352e9dde82ee97a42b130edfdbbcb829) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Parts/InternalDetails/TenderWagon_01_Coal.prefab using Guid(352e9dde82ee97a42b130edfdbbcb829) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bc39c4d1dd8d5986ecc0d0832923a9e5') in 2.5883074 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000067 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/TenderWagon_01_Red.prefab
  artifactKey: Guid(dbd1ebaf45f6052439362ee6fd134169) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/TenderWagon_01_Red.prefab using Guid(dbd1ebaf45f6052439362ee6fd134169) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2989a7b2d9b5a2365164cf9def2df268') in 2.9508129 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 251

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/SubwayCart_01_B.fbx
  artifactKey: Guid(1a4481b430666db4885786f6bf15787a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/SubwayCart_01_B.fbx using Guid(1a4481b430666db4885786f6bf15787a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6bd858458e68bdc954a7c31a469e91b2') in 0.4925282 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Locomotive_01_White_Black.prefab
  artifactKey: Guid(d856723aff8630749aa27925cb59d052) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Locomotive_01_White_Black.prefab using Guid(d856723aff8630749aa27925cb59d052) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1af406eb1a7454e7c04a917d93bec6ae') in 4.4596129 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1216

========================================================================
Received Import Request.
  Time since last request: 42.499384 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 1/_Prefabs/s_TrainSample_Addon1.prefab
  artifactKey: Guid(9513996852ec0274ca23a6b0934b47fb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 1/_Prefabs/s_TrainSample_Addon1.prefab using Guid(9513996852ec0274ca23a6b0934b47fb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bf2dad11f9a505e27936d0e07f062faf') in 4.5297886 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3209

========================================================================
Received Import Request.
  Time since last request: 0.000052 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Sample Trains/Train_Long.prefab
  artifactKey: Guid(3aa45cbd52e26a44f80bb234cab1a37f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Sample Trains/Train_Long.prefab using Guid(3aa45cbd52e26a44f80bb234cab1a37f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '966f48ed0fa8f1a7faa65f5c7287ca86') in 5.2998582 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2875

========================================================================
Received Import Request.
  Time since last request: 0.000093 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/_Prefabs/TrainSample_Addon2.prefab
  artifactKey: Guid(6fa753c9f32eed2429ab1485ad7a2eec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/_Prefabs/TrainSample_Addon2.prefab using Guid(6fa753c9f32eed2429ab1485ad7a2eec) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8b8211811ec592bed52aa517dcd50f0d') in 3.8381667 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3172

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0
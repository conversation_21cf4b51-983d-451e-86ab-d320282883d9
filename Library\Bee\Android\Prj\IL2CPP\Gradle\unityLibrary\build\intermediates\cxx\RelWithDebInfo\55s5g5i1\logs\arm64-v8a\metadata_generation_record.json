[{"level_": 0, "message_": "Start JSON generation. Platform version: 23 min SDK version: arm64-v8a", "file_": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON D:\\My Project\\Driving Simulator Game Z TEC\\.utmp\\RelWithDebInfo\\55s5g5i1\\arm64-v8a\\android_gradle_build.json due to:", "file_": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Program Files\\\\Unity\\\\Hub\\\\Editor\\\\6000.0.30f1\\\\Editor\\\\Data\\\\PlaybackEngines\\\\AndroidPlayer\\\\OpenJDK\\\\bin\\\\java\" ^\n  --class-path ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\modules-2\\\\files-2.1\\\\com.google.prefab\\\\cli\\\\2.0.0\\\\f2702b5ca13df54e3ca92f29d6b403fb6285d8df\\\\cli-2.0.0-all.jar\" ^\n  com.google.prefab.cli.AppKt ^\n  --build-system ^\n  cmake ^\n  --platform ^\n  android ^\n  --abi ^\n  arm64-v8a ^\n  --os-version ^\n  23 ^\n  --stl ^\n  c++_shared ^\n  --ndk-version ^\n  23 ^\n  --output ^\n  \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\agp-prefab-staging5773296487545117364\\\\staged-cli-output\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\transforms-3\\\\4533d298259fc52a43021fce53f5e4a9\\\\transformed\\\\jetified-games-activity-3.0.5\\\\prefab\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\transforms-3\\\\268849a49ea9eb2bba6f4e0ac95bfd63\\\\transformed\\\\jetified-games-frame-pacing-1.10.0\\\\prefab\"\n", "file_": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'D:\\My Project\\Driving Simulator Game Z TEC\\.utmp\\RelWithDebInfo\\55s5g5i1\\arm64-v8a'", "file_": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'D:\\My Project\\Driving Simulator Game Z TEC\\.utmp\\RelWithDebInfo\\55s5g5i1\\arm64-v8a'", "file_": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"C:\\\\Program Files\\\\Unity\\\\Hub\\\\Editor\\\\6000.0.30f1\\\\Editor\\\\Data\\\\PlaybackEngines\\\\AndroidPlayer\\\\SDK\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HD:\\\\My Project\\\\Driving Simulator Game Z TEC\\\\Library\\\\Bee\\\\Android\\\\Prj\\\\IL2CPP\\\\Gradle\\\\unityLibrary\\\\src\\\\main\\\\cpp\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=23\" ^\n  \"-DANDROID_PLATFORM=android-23\" ^\n  \"-DANDROID_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\" ^\n  \"-DANDROID_NDK=C:\\\\Program Files\\\\Unity\\\\Hub\\\\Editor\\\\6000.0.30f1\\\\Editor\\\\Data\\\\PlaybackEngines\\\\AndroidPlayer\\\\NDK\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Program Files\\\\Unity\\\\Hub\\\\Editor\\\\6000.0.30f1\\\\Editor\\\\Data\\\\PlaybackEngines\\\\AndroidPlayer\\\\NDK\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Program Files\\\\Unity\\\\Hub\\\\Editor\\\\6000.0.30f1\\\\Editor\\\\Data\\\\PlaybackEngines\\\\AndroidPlayer\\\\NDK\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Program Files\\\\Unity\\\\Hub\\\\Editor\\\\6000.0.30f1\\\\Editor\\\\Data\\\\PlaybackEngines\\\\AndroidPlayer\\\\SDK\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\My Project\\\\Driving Simulator Game Z TEC\\\\Library\\\\Bee\\\\Android\\\\Prj\\\\IL2CPP\\\\Gradle\\\\unityLibrary\\\\build\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\55s5g5i1\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\\\My Project\\\\Driving Simulator Game Z TEC\\\\Library\\\\Bee\\\\Android\\\\Prj\\\\IL2CPP\\\\Gradle\\\\unityLibrary\\\\build\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\55s5g5i1\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_BUILD_TYPE=RelWithDebInfo\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=D:\\\\My Project\\\\Driving Simulator Game Z TEC\\\\.utmp\\\\RelWithDebInfo\\\\55s5g5i1\\\\prefab\\\\arm64-v8a\\\\prefab\" ^\n  \"-BD:\\\\My Project\\\\Driving Simulator Game Z TEC\\\\.utmp\\\\RelWithDebInfo\\\\55s5g5i1\\\\arm64-v8a\" ^\n  -GNinja ^\n  \"-DANDROID_STL=c++_shared\"\n", "file_": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Program Files\\\\Unity\\\\Hub\\\\Editor\\\\6000.0.30f1\\\\Editor\\\\Data\\\\PlaybackEngines\\\\AndroidPlayer\\\\SDK\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HD:\\\\My Project\\\\Driving Simulator Game Z TEC\\\\Library\\\\Bee\\\\Android\\\\Prj\\\\IL2CPP\\\\Gradle\\\\unityLibrary\\\\src\\\\main\\\\cpp\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=23\" ^\n  \"-DANDROID_PLATFORM=android-23\" ^\n  \"-DANDROID_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\" ^\n  \"-DANDROID_NDK=C:\\\\Program Files\\\\Unity\\\\Hub\\\\Editor\\\\6000.0.30f1\\\\Editor\\\\Data\\\\PlaybackEngines\\\\AndroidPlayer\\\\NDK\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Program Files\\\\Unity\\\\Hub\\\\Editor\\\\6000.0.30f1\\\\Editor\\\\Data\\\\PlaybackEngines\\\\AndroidPlayer\\\\NDK\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Program Files\\\\Unity\\\\Hub\\\\Editor\\\\6000.0.30f1\\\\Editor\\\\Data\\\\PlaybackEngines\\\\AndroidPlayer\\\\NDK\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Program Files\\\\Unity\\\\Hub\\\\Editor\\\\6000.0.30f1\\\\Editor\\\\Data\\\\PlaybackEngines\\\\AndroidPlayer\\\\SDK\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\My Project\\\\Driving Simulator Game Z TEC\\\\Library\\\\Bee\\\\Android\\\\Prj\\\\IL2CPP\\\\Gradle\\\\unityLibrary\\\\build\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\55s5g5i1\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\\\My Project\\\\Driving Simulator Game Z TEC\\\\Library\\\\Bee\\\\Android\\\\Prj\\\\IL2CPP\\\\Gradle\\\\unityLibrary\\\\build\\\\intermediates\\\\cxx\\\\RelWithDebInfo\\\\55s5g5i1\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_BUILD_TYPE=RelWithDebInfo\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=D:\\\\My Project\\\\Driving Simulator Game Z TEC\\\\.utmp\\\\RelWithDebInfo\\\\55s5g5i1\\\\prefab\\\\arm64-v8a\\\\prefab\" ^\n  \"-BD:\\\\My Project\\\\Driving Simulator Game Z TEC\\\\.utmp\\\\RelWithDebInfo\\\\55s5g5i1\\\\arm64-v8a\" ^\n  -GNinja ^\n  \"-DANDROID_STL=c++_shared\"\n", "file_": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of D:\\My Project\\Driving Simulator Game Z TEC\\.utmp\\RelWithDebInfo\\55s5g5i1\\arm64-v8a\\compile_commands.json.bin normally", "file_": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked D:\\My Project\\Driving Simulator Game Z TEC\\.utmp\\RelWithDebInfo\\55s5g5i1\\arm64-v8a\\compile_commands.json to D:\\My Project\\Driving Simulator Game Z TEC\\.utmp\\tools\\release\\arm64-v8a\\compile_commands.json", "file_": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]
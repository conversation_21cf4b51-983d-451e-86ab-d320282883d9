<linker>
	<assembly fullname="Assembly-CSharp">
		<type fullname="Dronecamera" preserve="nothing"/>
		<type fullname="FixedJoystick" preserve="nothing"/>
		<type fullname="Invector.Utils.vComment" preserve="nothing"/>
		<type fullname="Invector.vCharacterController.vThirdPersonController" preserve="nothing"/>
		<type fullname="Invector.vCharacterController.vThirdPersonInput" preserve="nothing"/>
		<type fullname="vThirdPersonCamera" preserve="nothing"/>
	</assembly>
	<assembly fullname="Unity.InputSystem">
		<type fullname="UnityEngine.InputSystem.InputActionAsset" preserve="nothing"/>
		<type fullname="UnityEngine.InputSystem.InputActionReference" preserve="nothing"/>
		<type fullname="UnityEngine.InputSystem.InputSettings" preserve="nothing"/>
		<type fullname="UnityEngine.InputSystem.InputSystemObject" preserve="nothing"/>
		<type fullname="UnityEngine.InputSystem.RemoteInputPlayerConnection" preserve="nothing"/>
		<type fullname="UnityEngine.InputSystem.UI.InputSystemUIInputModule" preserve="nothing"/>
	</assembly>
	<assembly fullname="UnityEngine">
		<type fullname="UnityEditor.Animations.AnimatorController" preserve="nothing"/>
		<type fullname="UnityEditor.AudioManager" preserve="nothing"/>
		<type fullname="UnityEditor.InputManager" preserve="nothing"/>
		<type fullname="UnityEditor.MonoManager" preserve="nothing"/>
		<type fullname="UnityEditor.MonoScript" preserve="nothing"/>
		<type fullname="UnityEditor.Physics2DSettings" preserve="nothing"/>
		<type fullname="UnityEditor.PlayerSettings" preserve="nothing"/>
		<type fullname="UnityEditor.TagManager" preserve="nothing"/>
		<type fullname="UnityEditor.TimeManager" preserve="nothing"/>
		<type fullname="UnityEditor.VFXManager" preserve="nothing"/>
		<type fullname="UnityEngine.AnimationClip" preserve="nothing"/>
		<type fullname="UnityEngine.Animator" preserve="nothing"/>
		<type fullname="UnityEngine.AudioBehaviour" preserve="nothing"/>
		<type fullname="UnityEngine.AudioListener" preserve="nothing"/>
		<type fullname="UnityEngine.Avatar" preserve="nothing"/>
		<type fullname="UnityEngine.Behaviour" preserve="nothing"/>
		<type fullname="UnityEngine.Camera" preserve="nothing"/>
		<type fullname="UnityEngine.Canvas" preserve="nothing"/>
		<type fullname="UnityEngine.CanvasRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.CapsuleCollider" preserve="nothing"/>
		<type fullname="UnityEngine.Collider" preserve="nothing"/>
		<type fullname="UnityEngine.Component" preserve="nothing"/>
		<type fullname="UnityEngine.Cubemap" preserve="nothing"/>
		<type fullname="UnityEngine.Font" preserve="nothing"/>
		<type fullname="UnityEngine.GameObject" preserve="nothing"/>
		<type fullname="UnityEngine.LightingSettings" preserve="nothing"/>
		<type fullname="UnityEngine.LightmapSettings" preserve="nothing"/>
		<type fullname="UnityEngine.LightProbes" preserve="nothing"/>
		<type fullname="UnityEngine.Material" preserve="nothing"/>
		<type fullname="UnityEngine.Mesh" preserve="nothing"/>
		<type fullname="UnityEngine.MonoBehaviour" preserve="nothing"/>
		<type fullname="UnityEngine.Motion" preserve="nothing"/>
		<type fullname="UnityEngine.Object" preserve="nothing"/>
		<type fullname="UnityEngine.QualitySettings" preserve="nothing"/>
		<type fullname="UnityEngine.RectTransform" preserve="nothing"/>
		<type fullname="UnityEngine.Renderer" preserve="nothing"/>
		<type fullname="UnityEngine.Rendering.GraphicsSettings" preserve="nothing"/>
		<type fullname="UnityEngine.RenderSettings" preserve="nothing"/>
		<type fullname="UnityEngine.Rigidbody" preserve="nothing"/>
		<type fullname="UnityEngine.RuntimeAnimatorController" preserve="nothing"/>
		<type fullname="UnityEngine.Shader" preserve="nothing"/>
		<type fullname="UnityEngine.SkinnedMeshRenderer" preserve="nothing"/>
		<type fullname="UnityEngine.Sprite" preserve="nothing"/>
		<type fullname="UnityEngine.Terrain" preserve="nothing"/>
		<type fullname="UnityEngine.TerrainCollider" preserve="nothing"/>
		<type fullname="UnityEngine.TerrainData" preserve="nothing"/>
		<type fullname="UnityEngine.TextAsset" preserve="nothing"/>
		<type fullname="UnityEngine.Texture" preserve="nothing"/>
		<type fullname="UnityEngine.Texture2D" preserve="nothing"/>
		<type fullname="UnityEngine.Transform" preserve="nothing"/>
		<type fullname="UnityEngine.Video.VideoClip" preserve="nothing"/>
	</assembly>
	<assembly fullname="UnityEngine.UI">
		<type fullname="UnityEngine.EventSystems.EventSystem" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Button" preserve="nothing"/>
		<type fullname="UnityEngine.UI.CanvasScaler" preserve="nothing"/>
		<type fullname="UnityEngine.UI.GraphicRaycaster" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Image" preserve="nothing"/>
		<type fullname="UnityEngine.UI.Text" preserve="nothing"/>
	</assembly>
</linker>

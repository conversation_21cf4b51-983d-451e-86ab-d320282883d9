using UnityEngine;

namespace BoatControllerwithShooting
{
    public class WaterEffect : MonoBehaviour
    {
        public float scrollSpeed = 0.1f;
        public GameObject splashParticle;
        Renderer rend;



        void Start()
        {
            rend = GetComponent<Renderer>();
        }

        void Update()
        {
            float offset = Time.time * scrollSpeed;
            rend.material.SetTextureOffset("_MainTex", new Vector2(offset, 0));
        }

        private void OnCollisionEnter(Collision collision)
        {
            if(collision.collider.CompareTag("Collapsable") || collision.collider.CompareTag("Enemy"))
            {
                collision.collider.GetComponent<Collider>().enabled = false;
                if (collision.relativeVelocity.magnitude > 10)
                {
                    GameObject waterSplash = Instantiate(splashParticle, collision.contacts[0].point, Quaternion.identity);
                    float randomScale = Random.Range(0.5f, 1.25f);
                    waterSplash.transform.localScale = new Vector3(randomScale, randomScale, randomScale);
                    waterSplash.transform.rotation = new Quaternion(Random.Range(0, 360), Random.Range(0, 360), Random.Range(0, 360), 0);
                }
                Destroy(collision.collider.gameObject, 3);
            }
        }
    }
}
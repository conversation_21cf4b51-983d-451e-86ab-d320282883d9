{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1751353290954907, "dur":61, "ph":"X", "name": "IPC_Client_InitializeAndConnectToParent",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353290955028, "dur":113916, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353291068965, "dur":2031, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353291071350, "dur":372, "ph":"X", "name": "<PERSON><PERSON>",  "args": { "detail":"PrepareNodes" }}
,{ "pid":12345, "tid":0, "ts":1751353291071723, "dur":1758, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353291073630, "dur":80, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/RuntimeInitializeOnLoads.json" }}
,{ "pid":12345, "tid":0, "ts":1751353291073778, "dur":73, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"AddBootConfigGUID Library/Bee/artifacts/Android/boot.config" }}
,{ "pid":12345, "tid":0, "ts":1751353291073856, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/boot.config_x4e9.info" }}
,{ "pid":12345, "tid":0, "ts":1751353291074030, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/15538298867967171994.rsp" }}
,{ "pid":12345, "tid":0, "ts":1751353291074202, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Mono.Security-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1751353291074490, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/System.Configuration-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1751353291074721, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System-FeaturesChecked.txt_v90b.info" }}
,{ "pid":12345, "tid":0, "ts":1751353291074820, "dur":73, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System.Xml-FeaturesChecked.txt_wnnz.info" }}
,{ "pid":12345, "tid":0, "ts":1751353291075075, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.AIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1751353291075145, "dur":56, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AIModule-FeaturesChecked.txt_jr17.info" }}
,{ "pid":12345, "tid":0, "ts":1751353291075383, "dur":93, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AnimationModule-FeaturesChecked.txt_3sdh.info" }}
,{ "pid":12345, "tid":0, "ts":1751353291075495, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.AudioModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1751353291075566, "dur":94, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AudioModule-FeaturesChecked.txt_tbfa.info" }}
,{ "pid":12345, "tid":0, "ts":1751353291075674, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.CoreModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1751353291075746, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.CoreModule-FeaturesChecked.txt_uimb.info" }}
,{ "pid":12345, "tid":0, "ts":1751353291075956, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.GridModule-FeaturesChecked.txt_5p9t.info" }}
,{ "pid":12345, "tid":0, "ts":1751353291076209, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.InputForUIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1751353291076313, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.InputLegacyModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1751353291076384, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.InputLegacyModule-FeaturesChecked.txt_y5my.info" }}
,{ "pid":12345, "tid":0, "ts":1751353291076475, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.InputModule-FeaturesChecked.txt_xblj.info" }}
,{ "pid":12345, "tid":0, "ts":1751353291076741, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.PhysicsModule-FeaturesChecked.txt_2l1t.info" }}
,{ "pid":12345, "tid":0, "ts":1751353291076972, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.SharedInternalsModule-FeaturesChecked.txt_sm9d.info" }}
,{ "pid":12345, "tid":0, "ts":1751353291077208, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TerrainModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1751353291077307, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TerrainPhysicsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1751353291077463, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt_4rfo.info" }}
,{ "pid":12345, "tid":0, "ts":1751353291077655, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TextRenderingModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1751353291077805, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TilemapModule-FeaturesChecked.txt_xct4.info" }}
,{ "pid":12345, "tid":0, "ts":1751353291077903, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UI-FeaturesChecked.txt_v0bu.info" }}
,{ "pid":12345, "tid":0, "ts":1751353291078133, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1751353291078380, "dur":82, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.VideoModule-FeaturesChecked.txt_2ixs.info" }}
,{ "pid":12345, "tid":0, "ts":1751353291078535, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.XRModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":0, "ts":1751353291078622, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/Android/il2cppOutput/build/arm64-v8a/libil2cpp.so" }}
,{ "pid":12345, "tid":0, "ts":1751353291078769, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/artifacts/objcopy_5lmi/libil2cpp.dbg.so" }}
,{ "pid":12345, "tid":0, "ts":1751353291078871, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Lib_Android_arm64 Library/Bee/artifacts/Android/87lik/il2cpp.a" }}
,{ "pid":12345, "tid":0, "ts":1751353291079066, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/Android/d8kzr/_dummy_for_header_discovery" }}
,{ "pid":12345, "tid":0, "ts":1751353291079214, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1i84rizu2wfi.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291079279, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mybt7rds5kr1.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291079479, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/u9m2752jj1cz.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291079605, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1cevn4bdtfhh.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291079705, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/e1hme3j3dfum.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291079941, "dur":108, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/fozgg4x4egjv.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291080085, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1bnlgts0l6bu.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291080180, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0o5qesezk5ml.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291080277, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mctveh9urvfk.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291080515, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0j63ziscbd6i.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291080755, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/17ru68ruxfr6.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291080977, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uy6rmwvjekon.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291081124, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/160kzx8hq6lj.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291081221, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nuopzm3dxwei.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291081471, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gl3mfa72d1bz.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291081610, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/6edgv1k7gptl.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291081682, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/o1hwcr0ztpqa.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291081796, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v48osp9s33us.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291081944, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/27zh8nimdiza.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291082091, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9araomrfnw3s.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291082203, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v3hfleqgwup5.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291082277, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gjl30n4dzi1o.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291082382, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/26870cz958tn.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291082525, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/99z0gb82fny5.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291082633, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vxn5g02ymdr4.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291082725, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/s20kvqf3nfmk.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291082823, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3d2eehwv215f.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291082917, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/988q9zoq8dti.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291083061, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/w4gcu4zkj62w.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291083213, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3774928dtk7e.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291083318, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/p0y96u7e2itr.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291083546, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2yweic92h9kc.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291083695, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/pu4z2gkxs1uq.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291083782, "dur":56, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/aa8niqyuvp69.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291083874, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/w2ysfixv4nsm.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291084053, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/olt9aqkmhq8n.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291084200, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cpp1rpcsno71.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291084305, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ajhot4s4ia6f.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291084401, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xe0ee8df9ia4.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291084572, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/eoeljpbnd8zv.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291084708, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v0t4ioo2g7hl.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291084807, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/878swamkk5lx.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291084903, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bz694xc5ywuw.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291085052, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/i2x2kwly8aze.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291085201, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/p5ov0uurip3w.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291085318, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4czkg6x1i6ey.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291085445, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bgqdw46uskjg.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291085544, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hbxprnzpyw6i.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291085641, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3ykurcul5mzh.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291085743, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/lh8sv41vny8b.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291085843, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7sk29ap2btmg.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291085930, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/qxqd40q4j6kr.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291086028, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/rodfxnqmp95i.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291086143, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ghd97xez3wqy.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291086288, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1wgxn7sw6k2h.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291086461, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tfbejfgnjnij.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291086606, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gbjz3jrhufsx.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291086753, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/82lraxe6dit2.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291086952, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/n916fvr6k1u4.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291087049, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/coeqiwfgefg2.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291087120, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cqjhuhw737m8.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291087261, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xqgs9zqegqf7.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291087338, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/j46zdcerhtj0.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291087481, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hmkb5q84uqmc.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291087725, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uj5pvkt515uf.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291087887, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/j4iks9l8kb4u.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291087995, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/egfpd4jb3rvv.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291088064, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/zvidtdy85f7e.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291088120, "dur":95, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kbm18mc683iy.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291088251, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nqp3qp5ad6sw.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291088347, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ibt2f73hq3tq.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291088464, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/i31sr3433hzj.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291088557, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/d9rg6ujesit9.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291088760, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/lk3p68855ye7.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291088858, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/77qu6x31cp75.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291088955, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/d3x61gwwi1u8.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291089128, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/qed2rhuvhykh.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291089315, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ix7inphmu00i.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291089413, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xgnbosvtj371.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291089509, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/j4agohqpqt00.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291089674, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mri45r890jp3.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291089820, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/t4ka0mlsvwx1.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291089937, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4zjw4q9v68e6.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291090078, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8535tzfadii5.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291090175, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/36ou4anq6mij.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291090322, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/t4xx6axaa8uq.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291090600, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/67lvu8eehrja.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291090730, "dur":56, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/75ghpk6z7ukt.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291090984, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kvewo88f22kk.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291091157, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/rj3wjf09purk.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291091402, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hf0tg52bd15c.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291091580, "dur":80, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/psc99vj4y3bm.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291091815, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2nhwchez4c3y.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291091916, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mifvszs7cfcd.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291092068, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/sym525hss9ha.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291092184, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/m41fgba204hj.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291092325, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2mhn039af6b9.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291092422, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"NdkObjCopy Library/Bee/artifacts/objcopy_5lmi/libil2cpp.sym.so" }}
,{ "pid":12345, "tid":0, "ts":1751353291092574, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/Android/il2cppOutput/build/arm64-v8a/Data/Metadata/global-metadata.dat" }}
,{ "pid":12345, "tid":0, "ts":1751353291092836, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/analytics.json" }}
,{ "pid":12345, "tid":0, "ts":1751353291092978, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291093075, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__10.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291093297, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__15.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291093444, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__18.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291093667, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__22.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291093772, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__24.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291093951, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__5.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291094050, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__7.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291094421, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__13.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291094517, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__15.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291094740, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__2.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291094887, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__22.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291094981, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__24.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291095252, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__3.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291095426, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__33.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291095683, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__38.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291095835, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__40.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291095963, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__43.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291096151, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__47.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291096314, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__50.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291096412, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__52.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291096659, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__57.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291096834, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__60.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291096948, "dur":81, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__63.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291097063, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__65.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291097221, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__68.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291097356, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__70.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291097628, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__9.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291097780, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCalculateTypeValues.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291097875, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCFieldValuesTable.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291098067, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppGenericAdjustorThunkTable.c" }}
,{ "pid":12345, "tid":0, "ts":1751353291098218, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppGenericMethodDefinitions.c" }}
,{ "pid":12345, "tid":0, "ts":1751353291098314, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppGenericMethodTable.c" }}
,{ "pid":12345, "tid":0, "ts":1751353291098511, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppMetadataUsage.c" }}
,{ "pid":12345, "tid":0, "ts":1751353291098808, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Mono.Security.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291098954, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1751353291099128, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__12.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291099271, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__15.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291099369, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__17.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291099581, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__5.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291099749, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib__9.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291099894, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Core.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291100041, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Xml.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291100351, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291100528, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem__1.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291100672, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem__2.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291100845, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem__6.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291101024, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem__9.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291101177, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AndroidJNIModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291101402, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AudioModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1751353291101500, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.CoreModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1751353291101596, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.CoreModule__2.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291101708, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.GridModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291101841, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.HierarchyCoreModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1751353291101938, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.IMGUIModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1751353291102034, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.InputForUIModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291102128, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.InputLegacyModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291102228, "dur":102, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.InputModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291102366, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.JSONSerializeModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291102588, "dur":69, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.PhysicsModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1751353291102692, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.PropertiesModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1751353291102780, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.SharedInternalsModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1751353291102911, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.SubsystemsModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291102997, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TerrainModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291103114, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TerrainPhysicsModule.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291103341, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextCoreTextEngineModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1751353291103411, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextCoreTextEngineModule__1.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291103501, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextCoreTextEngineModule__3.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291103789, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UI.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291103925, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__1.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291104184, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__15.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291104341, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__18.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291104489, "dur":136, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__2.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291104712, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__5.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291104854, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule__8.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291105130, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UI__2.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291105290, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UnityAnalyticsCommonModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1751353291105561, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.VRModule_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1751353291105708, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine_CodeGen.c" }}
,{ "pid":12345, "tid":0, "ts":1751353291105996, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/icallsummary.txt" }}
,{ "pid":12345, "tid":0, "ts":1751353291106132, "dur":88, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/System.Configuration.dll" }}
,{ "pid":12345, "tid":0, "ts":1751353291106498, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.InputSystem.pdb" }}
,{ "pid":12345, "tid":0, "ts":1751353291106622, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AndroidJNIModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1751353291106690, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AndroidJNIModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1751353291106907, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.CoreModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1751353291107053, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.GridModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1751353291107387, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputForUIModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1751353291107498, "dur":56, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputLegacyModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1751353291107824, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.Physics2DModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1751353291107920, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.PhysicsModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1751353291108234, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.SpriteShapeModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1751353291108505, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TerrainPhysicsModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1751353291108645, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextCoreTextEngineModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1751353291109022, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UIElementsModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1751353291109217, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UnityAnalyticsCommonModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1751353291109425, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VideoModule.pdb" }}
,{ "pid":12345, "tid":0, "ts":1751353291109665, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityLinkerToEditorData.json" }}
,{ "pid":12345, "tid":0, "ts":1751353291109802, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/IntermediateFiles.txt_ri9j.info" }}
,{ "pid":12345, "tid":0, "ts":1751353291110169, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v21/styles.xml" }}
,{ "pid":12345, "tid":0, "ts":1751353291110239, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v28/styles.xml" }}
,{ "pid":12345, "tid":0, "ts":1751353291110352, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/colors.xml" }}
,{ "pid":12345, "tid":0, "ts":1751353291110622, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-anydpi-v26/app_icon_round.xml" }}
,{ "pid":12345, "tid":0, "ts":1751353291110819, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/libmain.so" }}
,{ "pid":12345, "tid":0, "ts":1751353291111016, "dur":85, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Link libunity Library/Bee/artifacts/Android/libunity/arm64-v8a/unstripped/libunity.so" }}
,{ "pid":12345, "tid":0, "ts":1751353291111122, "dur":80, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Compile UnityClassRegistration Library/Bee/artifacts/Android/libunity/arm64-v8a/UnityClassRegistration.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291111220, "dur":79, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ClassRegistrationGenerator D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/Android/il2cppOutput/UnityClassRegistration.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291111312, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityClassRegistration.cpp_3fxp.info" }}
,{ "pid":12345, "tid":0, "ts":1751353291111400, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Compile UnityICallRegistration Library/Bee/artifacts/Android/libunity/arm64-v8a/UnityICallRegistration.o" }}
,{ "pid":12345, "tid":0, "ts":1751353291111470, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ICallRegistrationGenerator D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/Android/il2cppOutput/UnityICallRegistration.cpp" }}
,{ "pid":12345, "tid":0, "ts":1751353291111550, "dur":65, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Stripping Library/Bee/artifacts/Android/libunity/arm64-v8a/unstripped/libunity.so -> Library/Bee/artifacts/Android/libunity/arm64-v8a/stripped/libunity.so Library/Bee/artifacts/Android/libunity/arm64-v8a/stripped/libunity.so" }}
,{ "pid":12345, "tid":0, "ts":1751353291111624, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"GuidGenerator D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/unity_app_guid" }}
,{ "pid":12345, "tid":0, "ts":1751353291111694, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/unity_app_guid_2cqv.info" }}
,{ "pid":12345, "tid":0, "ts":1751353291111767, "dur":89, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/resources/META-INF/com.android.games.engine.build_fingerprint" }}
,{ "pid":12345, "tid":0, "ts":1751353291111908, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ModifyAndroidProjectCallback D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml (+8 others)" }}
,{ "pid":12345, "tid":0, "ts":1751353291073513, "dur":38456, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353291111985, "dur":142918046, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353434030037, "dur":2193, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353434032231, "dur":123, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353434032433, "dur":72, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353434032505, "dur":252, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353434033028, "dur":116, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353434033506, "dur":21275, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":0, "ts":1751353434054783, "dur":28623, "ph":"X", "name": "Tundra",  "args": { "detail":"SaveScanCache" }}
,{ "pid":12345, "tid":1, "ts":1751353291073477, "dur":38522, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353291117399, "dur":1302, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/build/deploy" }}
,{ "pid":12345, "tid":1, "ts":1751353291118703, "dur":137, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/xamarin-android" }}
,{ "pid":12345, "tid":1, "ts":1751353291118840, "dur":133, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1751353291118973, "dur":141, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1751353291119114, "dur":120, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1751353291119234, "dur":119, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1751353291119353, "dur":132, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1751353291119486, "dur":118, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1751353291119604, "dur":145, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/external/zlib" }}
,{ "pid":12345, "tid":1, "ts":1751353291119749, "dur":8893, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp" }}
,{ "pid":12345, "tid":1, "ts":1751353291128643, "dur":120, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli" }}
,{ "pid":12345, "tid":1, "ts":1751353291128764, "dur":112, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli" }}
,{ "pid":12345, "tid":1, "ts":1751353291128876, "dur":119, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli" }}
,{ "pid":12345, "tid":1, "ts":1751353291128996, "dur":113, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli" }}
,{ "pid":12345, "tid":1, "ts":1751353291129110, "dur":110, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli" }}
,{ "pid":12345, "tid":1, "ts":1751353291129220, "dur":124, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/common" }}
,{ "pid":12345, "tid":1, "ts":1751353291129344, "dur":136, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/common" }}
,{ "pid":12345, "tid":1, "ts":1751353291129480, "dur":125, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/common" }}
,{ "pid":12345, "tid":1, "ts":1751353291129605, "dur":124, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/common" }}
,{ "pid":12345, "tid":1, "ts":1751353291129730, "dur":135, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/common" }}
,{ "pid":12345, "tid":1, "ts":1751353291129865, "dur":120, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/dec" }}
,{ "pid":12345, "tid":1, "ts":1751353291129985, "dur":129, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/dec" }}
,{ "pid":12345, "tid":1, "ts":1751353291130114, "dur":115, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/dec" }}
,{ "pid":12345, "tid":1, "ts":1751353291130230, "dur":115, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/dec" }}
,{ "pid":12345, "tid":1, "ts":1751353291130345, "dur":124, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/dec" }}
,{ "pid":12345, "tid":1, "ts":1751353291130469, "dur":252, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":1, "ts":1751353291130721, "dur":292, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":1, "ts":1751353291131013, "dur":247, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":1, "ts":1751353291131260, "dur":247, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":1, "ts":1751353291131508, "dur":349, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/enc" }}
,{ "pid":12345, "tid":1, "ts":1751353291131857, "dur":113, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" }}
,{ "pid":12345, "tid":1, "ts":1751353291131970, "dur":108, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" }}
,{ "pid":12345, "tid":1, "ts":1751353291132078, "dur":106, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" }}
,{ "pid":12345, "tid":1, "ts":1751353291132185, "dur":104, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" }}
,{ "pid":12345, "tid":1, "ts":1751353291132293, "dur":114, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include" }}
,{ "pid":12345, "tid":1, "ts":1751353291132407, "dur":107, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include/brotli" }}
,{ "pid":12345, "tid":1, "ts":1751353291132514, "dur":110, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include/brotli" }}
,{ "pid":12345, "tid":1, "ts":1751353291132624, "dur":103, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include/brotli" }}
,{ "pid":12345, "tid":1, "ts":1751353291132727, "dur":103, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include/brotli" }}
,{ "pid":12345, "tid":1, "ts":1751353291132830, "dur":106, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/il2cpp/libil2cpp/os/ClassLibraryPAL/brotli/include/brotli" }}
,{ "pid":12345, "tid":1, "ts":1751353291132936, "dur":154, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer" }}
,{ "pid":12345, "tid":1, "ts":1751353291133091, "dur":973, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Apk" }}
,{ "pid":12345, "tid":1, "ts":1751353291134064, "dur":934, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Apk" }}
,{ "pid":12345, "tid":1, "ts":1751353291134998, "dur":948, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Apk" }}
,{ "pid":12345, "tid":1, "ts":1751353291135946, "dur":109, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Apk/res/mipmap-anydpi-v26" }}
,{ "pid":12345, "tid":1, "ts":1751353291136056, "dur":117, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Apk/res/mipmap-mdpi" }}
,{ "pid":12345, "tid":1, "ts":1751353291136174, "dur":113, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/FramePacing" }}
,{ "pid":12345, "tid":1, "ts":1751353291136288, "dur":189, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Source/GameActivity" }}
,{ "pid":12345, "tid":1, "ts":1751353291136477, "dur":120, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/Variations/il2cpp/Release/Libs/arm64-v8a" }}
,{ "pid":12345, "tid":1, "ts":1751353291136598, "dur":386, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1751353291136984, "dur":371, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1751353291137355, "dur":140, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Library/PlayerDataCache/Android/Data" }}
,{ "pid":12345, "tid":1, "ts":1751353291137495, "dur":65, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"Temp/StagingArea/Data/UnitySubsystems" }}
,{ "pid":12345, "tid":1, "ts":1751353291112003, "dur":25645, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353291137664, "dur":267, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/15538298867967171994.rsp" }}
,{ "pid":12345, "tid":1, "ts":1751353291137971, "dur":199, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/15538298867967171994.rsp" }}
,{ "pid":12345, "tid":1, "ts":1751353291138172, "dur":2650, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"UnityLinker D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/unitylinker_xy1a.traceevents" }}
,{ "pid":12345, "tid":1, "ts":1751353291140823, "dur":1513, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353291145121, "dur":1468, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"IL2CPP_CodeGen D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/il2cpp_conv_dqml.traceevents" }}
,{ "pid":12345, "tid":1, "ts":1751353291146591, "dur":4346, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353291154485, "dur":205, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.Core.cpp" }}
,{ "pid":12345, "tid":1, "ts":1751353291154691, "dur":12853, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.Core.cpp" }}
,{ "pid":12345, "tid":1, "ts":1751353291167567, "dur":243, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353291154455, "dur":13355, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/lh8sv41vny8b.o" }}
,{ "pid":12345, "tid":1, "ts":1751353291167903, "dur":3653, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__4.cpp" }}
,{ "pid":12345, "tid":1, "ts":1751353291171558, "dur":3911, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__4.cpp" }}
,{ "pid":12345, "tid":1, "ts":1751353291175489, "dur":231, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353291167876, "dur":7844, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nk41nwnsdbvg.o" }}
,{ "pid":12345, "tid":1, "ts":1751353291175804, "dur":3588, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib_CodeGen.c" }}
,{ "pid":12345, "tid":1, "ts":1751353291179393, "dur":2866, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib_CodeGen.c" }}
,{ "pid":12345, "tid":1, "ts":1751353291182278, "dur":56, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353291175772, "dur":6563, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/c4dabwex14fm.o" }}
,{ "pid":12345, "tid":1, "ts":1751353291182761, "dur":1840094, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/c4dabwex14fm.o" }}
,{ "pid":12345, "tid":1, "ts":1751353293023101, "dur":2280, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__61.cpp" }}
,{ "pid":12345, "tid":1, "ts":1751353293025382, "dur":2423, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__61.cpp" }}
,{ "pid":12345, "tid":1, "ts":1751353293027826, "dur":215, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353293023078, "dur":4964, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ag2xm4lc564a.o" }}
,{ "pid":12345, "tid":1, "ts":1751353293028478, "dur":11560679, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/ag2xm4lc564a.o" }}
,{ "pid":12345, "tid":1, "ts":1751353304589428, "dur":3340, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__35.cpp" }}
,{ "pid":12345, "tid":1, "ts":1751353304592770, "dur":3695, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__35.cpp" }}
,{ "pid":12345, "tid":1, "ts":1751353304596481, "dur":193, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353304589395, "dur":7280, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hswtse4oaunr.o" }}
,{ "pid":12345, "tid":1, "ts":1751353305685885, "dur":26955920, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/hswtse4oaunr.o" }}
,{ "pid":12345, "tid":1, "ts":1751353332641984, "dur":2047, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__64.cpp" }}
,{ "pid":12345, "tid":1, "ts":1751353332644032, "dur":2588, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__64.cpp" }}
,{ "pid":12345, "tid":1, "ts":1751353332646630, "dur":124, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353332641960, "dur":4794, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hjtq22u5el9s.o" }}
,{ "pid":12345, "tid":1, "ts":1751353332910085, "dur":13777202, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/hjtq22u5el9s.o" }}
,{ "pid":12345, "tid":1, "ts":1751353346687569, "dur":3735, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__36.cpp" }}
,{ "pid":12345, "tid":1, "ts":1751353346691307, "dur":4010, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__36.cpp" }}
,{ "pid":12345, "tid":1, "ts":1751353346695335, "dur":222, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353346687514, "dur":8044, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/27zh8nimdiza.o" }}
,{ "pid":12345, "tid":1, "ts":1751353346696081, "dur":11681704, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/27zh8nimdiza.o" }}
,{ "pid":12345, "tid":1, "ts":1751353358378028, "dur":75652045, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353291073634, "dur":38427, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353291112067, "dur":2524, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353291114592, "dur":1478, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353291116071, "dur":21634, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353291137711, "dur":94, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine-FeaturesChecked.txt_x1ug.info" }}
,{ "pid":12345, "tid":2, "ts":1751353291137807, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353291137870, "dur":648, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine-FeaturesChecked.txt_x1ug.info" }}
,{ "pid":12345, "tid":2, "ts":1751353291138521, "dur":6634, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353291145158, "dur":1990, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1751353291147150, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353291147356, "dur":194, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\Android\\Features\\Assembly-CSharp-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1751353291147581, "dur":115, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\Android\\Features\\UnityEngine.AnimationModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1751353291147702, "dur":100, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\Android\\Features\\UnityEngine.CoreModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1751353291147805, "dur":104, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\Android\\Features\\UnityEngine-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1751353291147948, "dur":104, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\Android\\Features\\UnityEngine.PhysicsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1751353291148077, "dur":99, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\Android\\Features\\UnityEngine.TerrainModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1751353291148182, "dur":103, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\Android\\Features\\UnityEngine.TerrainPhysicsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":2, "ts":1751353291147351, "dur":1020, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Generate Manifests Library/Bee/artifacts/Android/Manifest/IntermediateLauncherManifestDiag.txt (+3 others)" }}
,{ "pid":12345, "tid":2, "ts":1751353291150393, "dur":455907, "ph":"X", "name": "Generate",  "args": { "detail":"Manifests Library/Bee/artifacts/Android/Manifest/IntermediateLauncherManifestDiag.txt (+3 others)" }}
,{ "pid":12345, "tid":2, "ts":1751353291607120, "dur":2006, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__59.cpp" }}
,{ "pid":12345, "tid":2, "ts":1751353291609127, "dur":2315, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__59.cpp" }}
,{ "pid":12345, "tid":2, "ts":1751353291611458, "dur":207, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353291607089, "dur":4577, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3774928dtk7e.o" }}
,{ "pid":12345, "tid":2, "ts":1751353291612072, "dur":11497280, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/3774928dtk7e.o" }}
,{ "pid":12345, "tid":2, "ts":1751353303109618, "dur":4489, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppMetadataUsage.c" }}
,{ "pid":12345, "tid":2, "ts":1751353303114109, "dur":4149, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppMetadataUsage.c" }}
,{ "pid":12345, "tid":2, "ts":1751353303118280, "dur":57, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353303109582, "dur":8756, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/pwqgo80hc9gu.o" }}
,{ "pid":12345, "tid":2, "ts":1751353303118861, "dur":2907909, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/pwqgo80hc9gu.o" }}
,{ "pid":12345, "tid":2, "ts":1751353306027028, "dur":2125, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule__3.cpp" }}
,{ "pid":12345, "tid":2, "ts":1751353306029154, "dur":2769, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule__3.cpp" }}
,{ "pid":12345, "tid":2, "ts":1751353306031938, "dur":171, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353306026993, "dur":5117, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kbm18mc683iy.o" }}
,{ "pid":12345, "tid":2, "ts":1751353306032217, "dur":143, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.XRModule_CodeGen.c" }}
,{ "pid":12345, "tid":2, "ts":1751353306032361, "dur":233, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.XRModule_CodeGen.c" }}
,{ "pid":12345, "tid":2, "ts":1751353306032155, "dur":499, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8ijiwlknrkr7.o" }}
,{ "pid":12345, "tid":2, "ts":1751353306452508, "dur":559044, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/8ijiwlknrkr7.o" }}
,{ "pid":12345, "tid":2, "ts":1751353307011863, "dur":1645, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__54.cpp" }}
,{ "pid":12345, "tid":2, "ts":1751353307013510, "dur":2130, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__54.cpp" }}
,{ "pid":12345, "tid":2, "ts":1751353307015657, "dur":203, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353307011825, "dur":4036, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/aht7tf5ocgay.o" }}
,{ "pid":12345, "tid":2, "ts":1751353307016286, "dur":25595234, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/aht7tf5ocgay.o" }}
,{ "pid":12345, "tid":2, "ts":1751353332611988, "dur":1749, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.HierarchyCoreModule.cpp" }}
,{ "pid":12345, "tid":2, "ts":1751353332613738, "dur":1293, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.HierarchyCoreModule.cpp" }}
,{ "pid":12345, "tid":2, "ts":1751353332615048, "dur":395, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353332611945, "dur":3499, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cqjhuhw737m8.o" }}
,{ "pid":12345, "tid":2, "ts":1751353332615538, "dur":434, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.XRModule.cpp" }}
,{ "pid":12345, "tid":2, "ts":1751353332615973, "dur":552, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.XRModule.cpp" }}
,{ "pid":12345, "tid":2, "ts":1751353332616732, "dur":128, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353332615500, "dur":1360, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4qlh7yuo39bz.o" }}
,{ "pid":12345, "tid":2, "ts":1751353332616936, "dur":1961, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__60.cpp" }}
,{ "pid":12345, "tid":2, "ts":1751353332618898, "dur":3283, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__60.cpp" }}
,{ "pid":12345, "tid":2, "ts":1751353332622198, "dur":262, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353332616894, "dur":5567, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/p0y96u7e2itr.o" }}
,{ "pid":12345, "tid":2, "ts":1751353332909764, "dur":7488672, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/p0y96u7e2itr.o" }}
,{ "pid":12345, "tid":2, "ts":1751353340398720, "dur":176, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TilemapModule_CodeGen.c" }}
,{ "pid":12345, "tid":2, "ts":1751353340398897, "dur":246, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TilemapModule_CodeGen.c" }}
,{ "pid":12345, "tid":2, "ts":1751353340399158, "dur":59, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353340398684, "dur":534, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2nhwchez4c3y.o" }}
,{ "pid":12345, "tid":2, "ts":1751353340399295, "dur":560, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UnityAnalyticsCommonModule.cpp" }}
,{ "pid":12345, "tid":2, "ts":1751353340399856, "dur":873, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UnityAnalyticsCommonModule.cpp" }}
,{ "pid":12345, "tid":2, "ts":1751353340400743, "dur":201, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353340399263, "dur":1682, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ykst251hxq25.o" }}
,{ "pid":12345, "tid":2, "ts":1751353340401025, "dur":3120, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__6.cpp" }}
,{ "pid":12345, "tid":2, "ts":1751353340404146, "dur":4213, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__6.cpp" }}
,{ "pid":12345, "tid":2, "ts":1751353340408378, "dur":209, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353340400993, "dur":7595, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bugq7q8skmw0.o" }}
,{ "pid":12345, "tid":2, "ts":1751353340408590, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353340408704, "dur":1968, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__47.cpp" }}
,{ "pid":12345, "tid":2, "ts":1751353340410673, "dur":2519, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__47.cpp" }}
,{ "pid":12345, "tid":2, "ts":1751353340413210, "dur":215, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353340408670, "dur":4755, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/aikh1qo1jpfl.o" }}
,{ "pid":12345, "tid":2, "ts":1751353340414223, "dur":12495693, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/aikh1qo1jpfl.o" }}
,{ "pid":12345, "tid":2, "ts":1751353352910184, "dur":2300, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__19.cpp" }}
,{ "pid":12345, "tid":2, "ts":1751353352912485, "dur":3173, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__19.cpp" }}
,{ "pid":12345, "tid":2, "ts":1751353352915675, "dur":221, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353352910148, "dur":5749, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mshj3dqaw3s0.o" }}
,{ "pid":12345, "tid":2, "ts":1751353352916383, "dur":10420874, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/mshj3dqaw3s0.o" }}
,{ "pid":12345, "tid":2, "ts":1751353363337408, "dur":70692788, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353291073538, "dur":38473, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353291112023, "dur":2379, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353291114403, "dur":1683, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353291116086, "dur":21613, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353291137734, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353291137979, "dur":229, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TerrainModule-FeaturesChecked.txt_wybo.info" }}
,{ "pid":12345, "tid":3, "ts":1751353291138213, "dur":181, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v30/freeformwindow.xml" }}
,{ "pid":12345, "tid":3, "ts":1751353291138442, "dur":245, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v30/freeformwindow.xml" }}
,{ "pid":12345, "tid":3, "ts":1751353291138689, "dur":6453, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353291145146, "dur":1016, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TerrainPhysicsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":3, "ts":1751353291146410, "dur":73, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UnityWebRequestModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1751353291146518, "dur":569, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TilemapModule.dll" }}
,{ "pid":12345, "tid":3, "ts":1751353291147089, "dur":7378, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353291154584, "dur":3441, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__1.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751353291158028, "dur":9352, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__1.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751353291167404, "dur":245, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353291154468, "dur":13182, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tleyyt55ari3.o" }}
,{ "pid":12345, "tid":3, "ts":1751353291167734, "dur":195, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.VideoModule.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751353291167929, "dur":531, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.VideoModule.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751353291168478, "dur":246, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353291167701, "dur":1024, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/zulbjx00y6hz.o" }}
,{ "pid":12345, "tid":3, "ts":1751353291168853, "dur":1263, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.CoreModule_CodeGen.c" }}
,{ "pid":12345, "tid":3, "ts":1751353291170117, "dur":1304, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.CoreModule_CodeGen.c" }}
,{ "pid":12345, "tid":3, "ts":1751353291171442, "dur":58, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353291168770, "dur":2730, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/19kcotfvceq7.o" }}
,{ "pid":12345, "tid":3, "ts":1751353291171865, "dur":960154, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/19kcotfvceq7.o" }}
,{ "pid":12345, "tid":3, "ts":1751353292132401, "dur":1521, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__23.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751353292133923, "dur":2411, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__23.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751353292136351, "dur":201, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353292132365, "dur":4188, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/fozgg4x4egjv.o" }}
,{ "pid":12345, "tid":3, "ts":1751353292137012, "dur":13051627, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/fozgg4x4egjv.o" }}
,{ "pid":12345, "tid":3, "ts":1751353305188881, "dur":3001, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.PropertiesModule.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751353305191884, "dur":3282, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.PropertiesModule.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751353305195185, "dur":224, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353305188849, "dur":6561, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/pgj75bd160pu.o" }}
,{ "pid":12345, "tid":3, "ts":1751353305195509, "dur":745, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule__4.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751353305196255, "dur":1258, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule__4.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751353305197530, "dur":214, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353305195475, "dur":2269, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/lj6ht2s4c6zd.o" }}
,{ "pid":12345, "tid":3, "ts":1751353305197829, "dur":2227, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__24.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751353305200057, "dur":2839, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__24.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751353305202914, "dur":231, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353305197794, "dur":5352, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9hcuzby93m7g.o" }}
,{ "pid":12345, "tid":3, "ts":1751353305203624, "dur":27419420, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/9hcuzby93m7g.o" }}
,{ "pid":12345, "tid":3, "ts":1751353332623312, "dur":2966, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__6.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751353332626286, "dur":4730, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__6.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751353332631204, "dur":685, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353332623276, "dur":8613, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tal1f6v1rh3z.o" }}
,{ "pid":12345, "tid":3, "ts":1751353332631979, "dur":3663, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.CoreModule.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751353332635644, "dur":3257, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.CoreModule.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751353332638921, "dur":242, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353332631945, "dur":7218, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hl1iyi40oidl.o" }}
,{ "pid":12345, "tid":3, "ts":1751353332935693, "dur":14280636, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/hl1iyi40oidl.o" }}
,{ "pid":12345, "tid":3, "ts":1751353347216587, "dur":158, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TilemapModule.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751353347216746, "dur":467, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TilemapModule.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751353347217229, "dur":201, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353347216552, "dur":879, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/m20qes3fkulp.o" }}
,{ "pid":12345, "tid":3, "ts":1751353347217508, "dur":2257, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__10.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751353347219766, "dur":2889, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__10.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751353347222673, "dur":203, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353347217480, "dur":5397, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ac7tbdjsuouj.o" }}
,{ "pid":12345, "tid":3, "ts":1751353347222972, "dur":396, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TerrainModule.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751353347223369, "dur":629, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TerrainModule.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751353347224012, "dur":184, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353347222938, "dur":1259, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/pn5tr6jvsup6.o" }}
,{ "pid":12345, "tid":3, "ts":1751353347224290, "dur":2794148, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/pn5tr6jvsup6.o" }}
,{ "pid":12345, "tid":3, "ts":1751353350018722, "dur":2276, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__7.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751353350021000, "dur":2975, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__7.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751353350023990, "dur":202, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353350018687, "dur":5506, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/eqopvfazu5ej.o" }}
,{ "pid":12345, "tid":3, "ts":1751353350024293, "dur":2075, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__62.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751353350026368, "dur":2463, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__62.cpp" }}
,{ "pid":12345, "tid":3, "ts":1751353350028847, "dur":209, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353350024261, "dur":4796, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vv5l2d097ugu.o" }}
,{ "pid":12345, "tid":3, "ts":1751353350029508, "dur":11975188, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/vv5l2d097ugu.o" }}
,{ "pid":12345, "tid":3, "ts":1751353362004881, "dur":72025197, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353291073622, "dur":38418, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353291112047, "dur":3167, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353291115216, "dur":22497, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353291137755, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353291137890, "dur":175, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.CoreModule-FeaturesChecked.txt_uimb.info" }}
,{ "pid":12345, "tid":4, "ts":1751353291138068, "dur":124, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/libs/unity-classes.jar" }}
,{ "pid":12345, "tid":4, "ts":1751353291138193, "dur":64, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/libs/unity-classes.jar" }}
,{ "pid":12345, "tid":4, "ts":1751353291138274, "dur":134, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/colors.xml" }}
,{ "pid":12345, "tid":4, "ts":1751353291138422, "dur":59, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/colors.xml" }}
,{ "pid":12345, "tid":4, "ts":1751353291138482, "dur":6653, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353291145137, "dur":302, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.CoreModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1751353291145615, "dur":223, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.PropertiesModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1751353291145839, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353291145995, "dur":245, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TextRenderingModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":4, "ts":1751353291146446, "dur":699, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UIModule.pdb" }}
,{ "pid":12345, "tid":4, "ts":1751353291147148, "dur":7311, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353291154582, "dur":1392, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.InputForUIModule_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1751353291155976, "dur":1733, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.InputForUIModule_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1751353291157741, "dur":100, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353291154461, "dur":3381, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/leq6apo4lxfj.o" }}
,{ "pid":12345, "tid":4, "ts":1751353291158593, "dur":586609, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/leq6apo4lxfj.o" }}
,{ "pid":12345, "tid":4, "ts":1751353291745460, "dur":163, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.Physics2DModule_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1751353291745624, "dur":283, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.Physics2DModule_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1751353291745923, "dur":56, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353291745426, "dur":554, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/98j4321vll6v.o" }}
,{ "pid":12345, "tid":4, "ts":1751353291746422, "dur":684073, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/98j4321vll6v.o" }}
,{ "pid":12345, "tid":4, "ts":1751353292430728, "dur":168, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.PhysicsModule_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1751353292430897, "dur":277, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.PhysicsModule_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1751353292431189, "dur":56, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353292430696, "dur":550, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vfm1o9u8rfhk.o" }}
,{ "pid":12345, "tid":4, "ts":1751353292431576, "dur":905759, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/vfm1o9u8rfhk.o" }}
,{ "pid":12345, "tid":4, "ts":1751353293337630, "dur":1693, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__15.cpp" }}
,{ "pid":12345, "tid":4, "ts":1751353293339324, "dur":2483, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__15.cpp" }}
,{ "pid":12345, "tid":4, "ts":1751353293341824, "dur":209, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353293337593, "dur":4441, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4pl7klguf1rd.o" }}
,{ "pid":12345, "tid":4, "ts":1751353293342119, "dur":1665, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__18.cpp" }}
,{ "pid":12345, "tid":4, "ts":1751353293343786, "dur":2281, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__18.cpp" }}
,{ "pid":12345, "tid":4, "ts":1751353293346082, "dur":205, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353293342089, "dur":4199, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uy6rmwvjekon.o" }}
,{ "pid":12345, "tid":4, "ts":1751353293346724, "dur":9860083, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/uy6rmwvjekon.o" }}
,{ "pid":12345, "tid":4, "ts":1751353303207086, "dur":2774, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__22.cpp" }}
,{ "pid":12345, "tid":4, "ts":1751353303209861, "dur":3005, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__22.cpp" }}
,{ "pid":12345, "tid":4, "ts":1751353303212882, "dur":196, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353303207049, "dur":6030, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nuopzm3dxwei.o" }}
,{ "pid":12345, "tid":4, "ts":1751353303754813, "dur":28909346, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/nuopzm3dxwei.o" }}
,{ "pid":12345, "tid":4, "ts":1751353332664401, "dur":155, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AIModule.cpp" }}
,{ "pid":12345, "tid":4, "ts":1751353332664557, "dur":692, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AIModule.cpp" }}
,{ "pid":12345, "tid":4, "ts":1751353332665269, "dur":253, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353332664366, "dur":1157, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/yzxvdadx1wuw.o" }}
,{ "pid":12345, "tid":4, "ts":1751353332665613, "dur":244, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.HierarchyCoreModule_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1751353332665858, "dur":311, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.HierarchyCoreModule_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1751353332666187, "dur":54, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353332665581, "dur":661, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4g4fb246gu9o.o" }}
,{ "pid":12345, "tid":4, "ts":1751353332935834, "dur":547391, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/4g4fb246gu9o.o" }}
,{ "pid":12345, "tid":4, "ts":1751353333483491, "dur":794, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreFontEngineModule.cpp" }}
,{ "pid":12345, "tid":4, "ts":1751353333484286, "dur":1020, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreFontEngineModule.cpp" }}
,{ "pid":12345, "tid":4, "ts":1751353333485321, "dur":205, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353333483454, "dur":2073, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/j4iks9l8kb4u.o" }}
,{ "pid":12345, "tid":4, "ts":1751353333485609, "dur":2119, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__1.cpp" }}
,{ "pid":12345, "tid":4, "ts":1751353333487729, "dur":2536, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__1.cpp" }}
,{ "pid":12345, "tid":4, "ts":1751353333490281, "dur":195, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353333485576, "dur":4902, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ghd97xez3wqy.o" }}
,{ "pid":12345, "tid":4, "ts":1751353333490565, "dur":2483, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__3.cpp" }}
,{ "pid":12345, "tid":4, "ts":1751353333493049, "dur":2977, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__3.cpp" }}
,{ "pid":12345, "tid":4, "ts":1751353333496042, "dur":197, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353333490534, "dur":5706, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/6edgv1k7gptl.o" }}
,{ "pid":12345, "tid":4, "ts":1751353333496326, "dur":138, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppReversePInvokeWrapperTable.cpp" }}
,{ "pid":12345, "tid":4, "ts":1751353333496465, "dur":406, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppReversePInvokeWrapperTable.cpp" }}
,{ "pid":12345, "tid":4, "ts":1751353333496886, "dur":182, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353333496294, "dur":775, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/01zek1xq1lza.o" }}
,{ "pid":12345, "tid":4, "ts":1751353333497144, "dur":137, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.Configuration_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1751353333497282, "dur":221, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.Configuration_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1751353333497115, "dur":447, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2qjvvj8f9f4h.o" }}
,{ "pid":12345, "tid":4, "ts":1751353333581602, "dur":390036, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/2qjvvj8f9f4h.o" }}
,{ "pid":12345, "tid":4, "ts":1751353333971897, "dur":2320, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__4.cpp" }}
,{ "pid":12345, "tid":4, "ts":1751353333974219, "dur":2519, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__4.cpp" }}
,{ "pid":12345, "tid":4, "ts":1751353333976754, "dur":201, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353333971864, "dur":5092, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/p6sl71dxdw31.o" }}
,{ "pid":12345, "tid":4, "ts":1751353333977044, "dur":1977, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__70.cpp" }}
,{ "pid":12345, "tid":4, "ts":1751353333979021, "dur":2395, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__70.cpp" }}
,{ "pid":12345, "tid":4, "ts":1751353333981431, "dur":190, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353333977012, "dur":4610, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/w2ysfixv4nsm.o" }}
,{ "pid":12345, "tid":4, "ts":1751353333982408, "dur":13176004, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/w2ysfixv4nsm.o" }}
,{ "pid":12345, "tid":4, "ts":1751353347158701, "dur":199, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AudioModule_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1751353347158901, "dur":268, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AudioModule_CodeGen.c" }}
,{ "pid":12345, "tid":4, "ts":1751353347159184, "dur":51, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353347158659, "dur":577, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/aggn294o7ldd.o" }}
,{ "pid":12345, "tid":4, "ts":1751353347159699, "dur":268451, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/aggn294o7ldd.o" }}
,{ "pid":12345, "tid":4, "ts":1751353347428429, "dur":147, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.Xml.cpp" }}
,{ "pid":12345, "tid":4, "ts":1751353347428577, "dur":470, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.Xml.cpp" }}
,{ "pid":12345, "tid":4, "ts":1751353347429063, "dur":213, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353347428393, "dur":884, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7sk29ap2btmg.o" }}
,{ "pid":12345, "tid":4, "ts":1751353347429358, "dur":1330, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__16.cpp" }}
,{ "pid":12345, "tid":4, "ts":1751353347430689, "dur":1473, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__16.cpp" }}
,{ "pid":12345, "tid":4, "ts":1751353347432179, "dur":211, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353347429329, "dur":3062, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8ajnm70n7tvo.o" }}
,{ "pid":12345, "tid":4, "ts":1751353347433175, "dur":9322541, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/8ajnm70n7tvo.o" }}
,{ "pid":12345, "tid":4, "ts":1751353356755976, "dur":77041382, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353433797658, "dur":208587, "ph":"X", "name": "NdkObjCopy",  "args": { "detail":"Library/Bee/artifacts/objcopy_5lmi/libil2cpp.so" }}
,{ "pid":12345, "tid":4, "ts":1751353434006347, "dur":23579, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/libil2cpp.so" }}
,{ "pid":12345, "tid":5, "ts":1751353291073744, "dur":38331, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353291112080, "dur":3181, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353291115263, "dur":22454, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353291137735, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AudioModule-FeaturesChecked.txt_tbfa.info" }}
,{ "pid":12345, "tid":5, "ts":1751353291137810, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353291137860, "dur":887, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AudioModule-FeaturesChecked.txt_tbfa.info" }}
,{ "pid":12345, "tid":5, "ts":1751353291138748, "dur":6444, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353291145193, "dur":224, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreFontEngineModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1751353291145498, "dur":169, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TerrainModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1751353291145736, "dur":125, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.AIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1751353291145945, "dur":581, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.VRModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":5, "ts":1751353291146668, "dur":308, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.IMGUIModule.pdb" }}
,{ "pid":12345, "tid":5, "ts":1751353291147004, "dur":217, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/System.Configuration.dll" }}
,{ "pid":12345, "tid":5, "ts":1751353291147225, "dur":7262, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353291154622, "dur":4349, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.CoreModule__1.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353291158973, "dur":8900, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.CoreModule__1.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353291167894, "dur":235, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353291154488, "dur":13642, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2tyi0ld9hdjl.o" }}
,{ "pid":12345, "tid":5, "ts":1751353291168699, "dur":12286780, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/2tyi0ld9hdjl.o" }}
,{ "pid":12345, "tid":5, "ts":1751353303455800, "dur":1149, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353303456949, "dur":1959, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353303458918, "dur":132, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353303455764, "dur":3287, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2jrgs6r4v1zy.o" }}
,{ "pid":12345, "tid":5, "ts":1751353303459138, "dur":16917, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppGenericMethodPointerTable.c" }}
,{ "pid":12345, "tid":5, "ts":1751353303476057, "dur":18885, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppGenericMethodPointerTable.c" }}
,{ "pid":12345, "tid":5, "ts":1751353303494963, "dur":56, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353303459102, "dur":35918, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8535tzfadii5.o" }}
,{ "pid":12345, "tid":5, "ts":1751353303755397, "dur":29060626, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/8535tzfadii5.o" }}
,{ "pid":12345, "tid":5, "ts":1751353332816211, "dur":1683, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__4.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353332817894, "dur":2227, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__4.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353332820130, "dur":132, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353332816186, "dur":4076, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7mxwfh15kra1.o" }}
,{ "pid":12345, "tid":5, "ts":1751353332820317, "dur":874, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__3.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353332821191, "dur":1330, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__3.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353332822530, "dur":111, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353332820300, "dur":2342, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/f912yi01hhb2.o" }}
,{ "pid":12345, "tid":5, "ts":1751353333103739, "dur":9129968, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/f912yi01hhb2.o" }}
,{ "pid":12345, "tid":5, "ts":1751353342234030, "dur":1971, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__14.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353342236003, "dur":2964, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__14.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353342238983, "dur":219, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353342233991, "dur":5212, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3h1q05zvbzay.o" }}
,{ "pid":12345, "tid":5, "ts":1751353342239302, "dur":2175, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__15.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353342241477, "dur":3101, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__15.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353342244596, "dur":233, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353342239266, "dur":5564, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/lk3p68855ye7.o" }}
,{ "pid":12345, "tid":5, "ts":1751353342244841, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353342244957, "dur":642, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UI_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1751353342245600, "dur":789, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UI_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1751353342246405, "dur":51, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353342244922, "dur":1535, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uh35aip7mj9h.o" }}
,{ "pid":12345, "tid":5, "ts":1751353342247265, "dur":646855, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/uh35aip7mj9h.o" }}
,{ "pid":12345, "tid":5, "ts":1751353342894405, "dur":1957, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__17.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353342896363, "dur":3174, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__17.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353342899554, "dur":225, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353342894368, "dur":5411, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/alsjg48oldfg.o" }}
,{ "pid":12345, "tid":5, "ts":1751353342899867, "dur":2146, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__9.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353342902014, "dur":4080, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__9.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353342906111, "dur":235, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353342899838, "dur":6509, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3ykurcul5mzh.o" }}
,{ "pid":12345, "tid":5, "ts":1751353342906437, "dur":2811, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.CoreModule__2.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353342909250, "dur":3496, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.CoreModule__2.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353342912764, "dur":237, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353342906408, "dur":6594, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/n916fvr6k1u4.o" }}
,{ "pid":12345, "tid":5, "ts":1751353342927827, "dur":12667459, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/n916fvr6k1u4.o" }}
,{ "pid":12345, "tid":5, "ts":1751353355595745, "dur":196, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\Android\\Manifest\\launcher\\src\\main\\AndroidManifest.xml" }}
,{ "pid":12345, "tid":5, "ts":1751353355595978, "dur":129, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\Android\\Manifest\\unityLibrary\\src\\main\\AndroidManifest.xml" }}
,{ "pid":12345, "tid":5, "ts":1751353355595539, "dur":570, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ModifyAndroidProjectCallback D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/AndroidManifest.xml (+8 others)" }}
,{ "pid":12345, "tid":5, "ts":1751353355596145, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353355596364, "dur":1199, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TerrainPhysicsModule_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1751353355597701, "dur":130, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\analytics.json" }}
,{ "pid":12345, "tid":5, "ts":1751353355597677, "dur":155, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/analytics.json" }}
,{ "pid":12345, "tid":5, "ts":1751353355597972, "dur":1395, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355599504, "dur":1172, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppRgctxTable.c" }}
,{ "pid":12345, "tid":5, "ts":1751353355600719, "dur":1879, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/mscorlib_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1751353355602799, "dur":912, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Xml_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1751353355603747, "dur":923, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Assembly-CSharp_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1751353355604773, "dur":1531, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppGenericClassTable.c" }}
,{ "pid":12345, "tid":5, "ts":1751353355606340, "dur":5473, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppTypeDefinitions.c" }}
,{ "pid":12345, "tid":5, "ts":1751353355611851, "dur":156, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppToEditorData.json" }}
,{ "pid":12345, "tid":5, "ts":1751353355611820, "dur":189, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppToEditorData.json" }}
,{ "pid":12345, "tid":5, "ts":1751353355612053, "dur":920, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppToEditorData.json" }}
,{ "pid":12345, "tid":5, "ts":1751353355613012, "dur":908, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Mono.Security_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1751353355614987, "dur":1144, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UI_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1751353355616256, "dur":1634, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__18.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355617928, "dur":1845, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__16.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355619811, "dur":7354, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppInvokerTable.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355627217, "dur":1922, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__21.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355629185, "dur":1987, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__25.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355631216, "dur":1720, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__23.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355632977, "dur":1947, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__19.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355634971, "dur":1794, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__12.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355636806, "dur":1626, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__14.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355638472, "dur":1856, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__10.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355640372, "dur":1576, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__17.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355641993, "dur":1480, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__22.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355643513, "dur":1486, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__24.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355645036, "dur":1999, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__13.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355647076, "dur":2056, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__11.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355649172, "dur":1914, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__15.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355651295, "dur":1163, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCTypeValuesTable.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355652496, "dur":1652, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1751353355654234, "dur":2155, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__67.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355656430, "dur":1510, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__45.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355657978, "dur":1462, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__53.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355659478, "dur":1636, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__28.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355661188, "dur":1503, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__73.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355662767, "dur":1890, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__68.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355664695, "dur":1788, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__27.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355666523, "dur":1879, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__38.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355668442, "dur":1808, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__30.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355670290, "dur":1839, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__43.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355672170, "dur":1770, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__62.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355674091, "dur":2681, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__37.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355676822, "dur":1926, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__49.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355678846, "dur":2137, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__23.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355681027, "dur":1723, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__60.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355682794, "dur":2003, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__34.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355684843, "dur":2836, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__32.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355687796, "dur":2288, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__21.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355690128, "dur":2104, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__64.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355692272, "dur":2940, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__35.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355695299, "dur":1883, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__41.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355697265, "dur":2276, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__52.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355699582, "dur":1533, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__17.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355701156, "dur":3526, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__19.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355704730, "dur":2670, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__66.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355707481, "dur":1934, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__59.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355709458, "dur":1967, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__56.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355711470, "dur":1674, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__65.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355713187, "dur":1553, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__46.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355714817, "dur":2147, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__22.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355717006, "dur":1650, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__71.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355718733, "dur":2235, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__51.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355721008, "dur":2047, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__25.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355723105, "dur":1990, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__61.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355725137, "dur":2035, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__57.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355727221, "dur":2233, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__33.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355729542, "dur":1799, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__26.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355731381, "dur":2352, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__63.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355733781, "dur":1718, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__24.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355735537, "dur":1738, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__29.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355737311, "dur":1825, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__55.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355739176, "dur":7688, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__42.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355746921, "dur":2189, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__39.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355749156, "dur":2149, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__20.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355751350, "dur":1640, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__31.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355753037, "dur":1763, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__50.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355754929, "dur":1709, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__44.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355756680, "dur":2401, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__58.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355759143, "dur":1621, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__72.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355760803, "dur":1750, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__48.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355762632, "dur":1659, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__54.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355764331, "dur":1827, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__18.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355766237, "dur":1729, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__74.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355768011, "dur":1947, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__69.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355770206, "dur":2081, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__70.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355772345, "dur":1440, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__16.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355773824, "dur":2354, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__36.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355776219, "dur":1701, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__47.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355777986, "dur":1525, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Generics__40.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355779615, "dur":920, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1751353355780571, "dur":777, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCodeRegistration.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355781371, "dur":75, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353355781456, "dur":2246, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.CoreModule.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355783809, "dur":1861, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppGenericMethodTable.c" }}
,{ "pid":12345, "tid":5, "ts":1751353355785744, "dur":2853, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__4.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355788640, "dur":1442, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__6.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355790127, "dur":2129, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__5.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355792432, "dur":2196, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__9.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355794707, "dur":1359, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__3.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355796104, "dur":1491, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__7.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355797636, "dur":927, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Core_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1751353355798630, "dur":884, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/__Generated_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1751353355799621, "dur":2882, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppMetadataUsage.c" }}
,{ "pid":12345, "tid":5, "ts":1751353355802552, "dur":2044, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__8.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355804644, "dur":2330, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__2.cpp" }}
,{ "pid":12345, "tid":5, "ts":1751353355807128, "dur":1046, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TerrainModule_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1751353355808220, "dur":966, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.PhysicsModule_CodeGen.c" }}
,{ "pid":12345, "tid":5, "ts":1751353355809560, "dur":91, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"GuidGenerator D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/unity_app_guid" }}
,{ "pid":12345, "tid":5, "ts":1751353355810251, "dur":917791, "ph":"X", "name": "GuidGenerator",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/unity_app_guid" }}
,{ "pid":12345, "tid":5, "ts":1751353356728733, "dur":76421528, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353433150377, "dur":314123, "ph":"X", "name": "NdkObjCopy",  "args": { "detail":"Library/Bee/artifacts/objcopy_5lmi/stripped/libil2cpp.so" }}
,{ "pid":12345, "tid":5, "ts":1751353433464628, "dur":565499, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353291073825, "dur":38264, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353291112095, "dur":2398, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353291114494, "dur":1485, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353291115979, "dur":21752, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353291137819, "dur":158, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.InputSystem.ForUI-FeaturesChecked.txt_1qsl.info" }}
,{ "pid":12345, "tid":6, "ts":1751353291138099, "dur":359, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ActionGenerateProjectFiles Library/Bee/artifacts/Android/IntermediateFiles.txt (+46 others)" }}
,{ "pid":12345, "tid":6, "ts":1751353291138489, "dur":679, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353291139177, "dur":5949, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353291145129, "dur":1329, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":6, "ts":1751353291146459, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353291146638, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353291146722, "dur":315, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"Stripping Library/Bee/artifacts/Android/libunity/arm64-v8a/unstripped/libunity.so -> Library/Bee/artifacts/Android/libunity/arm64-v8a/stripped/libunity.so Library/Bee/artifacts/Android/libunity/arm64-v8a/stripped/libunity.so" }}
,{ "pid":12345, "tid":6, "ts":1751353291147124, "dur":7351, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353291154597, "dur":3566, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__12.cpp" }}
,{ "pid":12345, "tid":6, "ts":1751353291158164, "dur":9822, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__12.cpp" }}
,{ "pid":12345, "tid":6, "ts":1751353291168010, "dur":268, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353291154476, "dur":13803, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ehcxqa0cwkap.o" }}
,{ "pid":12345, "tid":6, "ts":1751353291168391, "dur":531, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AnimationModule.cpp" }}
,{ "pid":12345, "tid":6, "ts":1751353291168923, "dur":1099, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AnimationModule.cpp" }}
,{ "pid":12345, "tid":6, "ts":1751353291170041, "dur":237, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353291168356, "dur":1922, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/82lraxe6dit2.o" }}
,{ "pid":12345, "tid":6, "ts":1751353291170721, "dur":3373295, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/82lraxe6dit2.o" }}
,{ "pid":12345, "tid":6, "ts":1751353294544302, "dur":1902, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppGenericMethodDefinitions.c" }}
,{ "pid":12345, "tid":6, "ts":1751353294546205, "dur":2546, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppGenericMethodDefinitions.c" }}
,{ "pid":12345, "tid":6, "ts":1751353294548769, "dur":52, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353294544266, "dur":4555, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/n6qmfnbbo4ko.o" }}
,{ "pid":12345, "tid":6, "ts":1751353294549266, "dur":2652390, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/n6qmfnbbo4ko.o" }}
,{ "pid":12345, "tid":6, "ts":1751353297201947, "dur":2088, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__14.cpp" }}
,{ "pid":12345, "tid":6, "ts":1751353297204036, "dur":2761, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__14.cpp" }}
,{ "pid":12345, "tid":6, "ts":1751353297206815, "dur":212, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353297201908, "dur":5120, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kcj9lts71wxr.o" }}
,{ "pid":12345, "tid":6, "ts":1751353297207128, "dur":1213, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__6.cpp" }}
,{ "pid":12345, "tid":6, "ts":1751353297208342, "dur":1910, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__6.cpp" }}
,{ "pid":12345, "tid":6, "ts":1751353297210269, "dur":196, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353297207092, "dur":3374, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mctveh9urvfk.o" }}
,{ "pid":12345, "tid":6, "ts":1751353298095530, "dur":34441636, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/mctveh9urvfk.o" }}
,{ "pid":12345, "tid":6, "ts":1751353332537681, "dur":4585, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__8.cpp" }}
,{ "pid":12345, "tid":6, "ts":1751353332542268, "dur":4657, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__8.cpp" }}
,{ "pid":12345, "tid":6, "ts":1751353332546951, "dur":271, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353332537635, "dur":9588, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/f3yij4ajqdbi.o" }}
,{ "pid":12345, "tid":6, "ts":1751353332547346, "dur":1966, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__41.cpp" }}
,{ "pid":12345, "tid":6, "ts":1751353332549313, "dur":2987, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__41.cpp" }}
,{ "pid":12345, "tid":6, "ts":1751353332552323, "dur":264, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353332547308, "dur":5280, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gjl30n4dzi1o.o" }}
,{ "pid":12345, "tid":6, "ts":1751353332902110, "dur":11635235, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/gjl30n4dzi1o.o" }}
,{ "pid":12345, "tid":6, "ts":1751353344537611, "dur":2175, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__20.cpp" }}
,{ "pid":12345, "tid":6, "ts":1751353344539787, "dur":2909, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__20.cpp" }}
,{ "pid":12345, "tid":6, "ts":1751353344542721, "dur":215, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353344537575, "dur":5361, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8k8n05ob51ed.o" }}
,{ "pid":12345, "tid":6, "ts":1751353344543391, "dur":13716921, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/8k8n05ob51ed.o" }}
,{ "pid":12345, "tid":6, "ts":1751353358260599, "dur":75769540, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353291073980, "dur":38121, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353291112106, "dur":2341, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353291114447, "dur":1558, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353291116005, "dur":21836, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353291137843, "dur":75, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.VideoModule-FeaturesChecked.txt_2ixs.info" }}
,{ "pid":12345, "tid":7, "ts":1751353291137927, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353291138002, "dur":662, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.VideoModule-FeaturesChecked.txt_2ixs.info" }}
,{ "pid":12345, "tid":7, "ts":1751353291138666, "dur":6464, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353291145132, "dur":478, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.XRModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1751353291145613, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353291145735, "dur":262, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.AndroidJNIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1751353291145998, "dur":294, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353291146293, "dur":71, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.AndroidJNIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":7, "ts":1751353291146701, "dur":148, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VideoModule.dll" }}
,{ "pid":12345, "tid":7, "ts":1751353291146868, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353291147025, "dur":60, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.SharedInternalsModule.pdb" }}
,{ "pid":12345, "tid":7, "ts":1751353291147124, "dur":83, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.SharedInternalsModule.pdb" }}
,{ "pid":12345, "tid":7, "ts":1751353291147209, "dur":7280, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353291154551, "dur":6166, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppGenericMethodTable.c" }}
,{ "pid":12345, "tid":7, "ts":1751353291160720, "dur":5970, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppGenericMethodTable.c" }}
,{ "pid":12345, "tid":7, "ts":1751353291166717, "dur":53, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353291154494, "dur":12276, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hfrwh0iewgoy.o" }}
,{ "pid":12345, "tid":7, "ts":1751353291167229, "dur":2642670, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/hfrwh0iewgoy.o" }}
,{ "pid":12345, "tid":7, "ts":1751353293810145, "dur":176, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.InputLegacyModule_CodeGen.c" }}
,{ "pid":12345, "tid":7, "ts":1751353293810321, "dur":258, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.InputLegacyModule_CodeGen.c" }}
,{ "pid":12345, "tid":7, "ts":1751353293810110, "dur":512, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/rj3wjf09purk.o" }}
,{ "pid":12345, "tid":7, "ts":1751353293810981, "dur":663453, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/rj3wjf09purk.o" }}
,{ "pid":12345, "tid":7, "ts":1751353294474744, "dur":2334, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__7.cpp" }}
,{ "pid":12345, "tid":7, "ts":1751353294477079, "dur":5810, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__7.cpp" }}
,{ "pid":12345, "tid":7, "ts":1751353294482907, "dur":384, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353294474708, "dur":8585, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hbxprnzpyw6i.o" }}
,{ "pid":12345, "tid":7, "ts":1751353294483397, "dur":2078, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__30.cpp" }}
,{ "pid":12345, "tid":7, "ts":1751353294485476, "dur":2603, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__30.cpp" }}
,{ "pid":12345, "tid":7, "ts":1751353294488095, "dur":211, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353294483363, "dur":4944, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/o1hwcr0ztpqa.o" }}
,{ "pid":12345, "tid":7, "ts":1751353294488806, "dur":38078067, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/o1hwcr0ztpqa.o" }}
,{ "pid":12345, "tid":7, "ts":1751353332567156, "dur":12447, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppTypeDefinitions.c" }}
,{ "pid":12345, "tid":7, "ts":1751353332579604, "dur":13158, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppTypeDefinitions.c" }}
,{ "pid":12345, "tid":7, "ts":1751353332592785, "dur":65, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353332567120, "dur":25731, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/t4xx6axaa8uq.o" }}
,{ "pid":12345, "tid":7, "ts":1751353332934758, "dur":7409140, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/t4xx6axaa8uq.o" }}
,{ "pid":12345, "tid":7, "ts":1751353340344186, "dur":2345, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__29.cpp" }}
,{ "pid":12345, "tid":7, "ts":1751353340346533, "dur":3674, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__29.cpp" }}
,{ "pid":12345, "tid":7, "ts":1751353340350226, "dur":211, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353340344148, "dur":6290, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/28qrfz1ykr4x.o" }}
,{ "pid":12345, "tid":7, "ts":1751353340350946, "dur":13185777, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/28qrfz1ykr4x.o" }}
,{ "pid":12345, "tid":7, "ts":1751353353536988, "dur":1388, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__16.cpp" }}
,{ "pid":12345, "tid":7, "ts":1751353353538384, "dur":2887, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__16.cpp" }}
,{ "pid":12345, "tid":7, "ts":1751353353541288, "dur":237, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353353536952, "dur":4575, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/p5ov0uurip3w.o" }}
,{ "pid":12345, "tid":7, "ts":1751353353541616, "dur":304, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AndroidJNIModule_CodeGen.c" }}
,{ "pid":12345, "tid":7, "ts":1751353353541921, "dur":413, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AndroidJNIModule_CodeGen.c" }}
,{ "pid":12345, "tid":7, "ts":1751353353541583, "dur":815, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/w4ivjxtzymil.o" }}
,{ "pid":12345, "tid":7, "ts":1751353353561086, "dur":485722, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/w4ivjxtzymil.o" }}
,{ "pid":12345, "tid":7, "ts":1751353354047080, "dur":2748, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__21.cpp" }}
,{ "pid":12345, "tid":7, "ts":1751353354049829, "dur":3467, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__21.cpp" }}
,{ "pid":12345, "tid":7, "ts":1751353354053312, "dur":217, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353354047043, "dur":6487, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2el1jdpgu82y.o" }}
,{ "pid":12345, "tid":7, "ts":1751353354054020, "dur":9014169, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/2el1jdpgu82y.o" }}
,{ "pid":12345, "tid":7, "ts":1751353363068337, "dur":70961780, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353291074020, "dur":38095, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353291112119, "dur":2615, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353291114735, "dur":2103, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353291116839, "dur":20900, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353291137804, "dur":63, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System-FeaturesChecked.txt_v90b.info" }}
,{ "pid":12345, "tid":8, "ts":1751353291138069, "dur":75, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/java/com/unity3d/player/UnityPlayerGameActivity.java" }}
,{ "pid":12345, "tid":8, "ts":1751353291138179, "dur":103, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-anydpi-v26/app_icon.xml" }}
,{ "pid":12345, "tid":8, "ts":1751353291138338, "dur":116, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/ids.xml" }}
,{ "pid":12345, "tid":8, "ts":1751353291138522, "dur":6640, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353291145164, "dur":387, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.Physics2DModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1751353291145631, "dur":142, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UIElementsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1751353291145844, "dur":177, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":8, "ts":1751353291146022, "dur":272, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353291146389, "dur":217, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\Android\\ManagedStripped\\icallsummary.txt" }}
,{ "pid":12345, "tid":8, "ts":1751353291146351, "dur":257, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/icallsummary.txt" }}
,{ "pid":12345, "tid":8, "ts":1751353291146621, "dur":630, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353291147400, "dur":2387, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/icallsummary.txt" }}
,{ "pid":12345, "tid":8, "ts":1751353291149789, "dur":4667, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353291154506, "dur":3241, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__11.cpp" }}
,{ "pid":12345, "tid":8, "ts":1751353291157749, "dur":9245, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__11.cpp" }}
,{ "pid":12345, "tid":8, "ts":1751353291167018, "dur":220, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353291154458, "dur":12780, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mybt7rds5kr1.o" }}
,{ "pid":12345, "tid":8, "ts":1751353291167684, "dur":15303187, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/mybt7rds5kr1.o" }}
,{ "pid":12345, "tid":8, "ts":1751353306471111, "dur":1270, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__73.cpp" }}
,{ "pid":12345, "tid":8, "ts":1751353306472383, "dur":1715, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__73.cpp" }}
,{ "pid":12345, "tid":8, "ts":1751353306474114, "dur":282, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353306471079, "dur":3318, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/35plvg6oe2w4.o" }}
,{ "pid":12345, "tid":8, "ts":1751353306579797, "dur":26036677, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/35plvg6oe2w4.o" }}
,{ "pid":12345, "tid":8, "ts":1751353332616742, "dur":2859, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__10.cpp" }}
,{ "pid":12345, "tid":8, "ts":1751353332619602, "dur":3436, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__10.cpp" }}
,{ "pid":12345, "tid":8, "ts":1751353332623165, "dur":196, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353332616706, "dur":6656, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1i84rizu2wfi.o" }}
,{ "pid":12345, "tid":8, "ts":1751353332623454, "dur":95, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353332935302, "dur":6893256, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/1i84rizu2wfi.o" }}
,{ "pid":12345, "tid":8, "ts":1751353339828847, "dur":216, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UnityWebRequestModule.cpp" }}
,{ "pid":12345, "tid":8, "ts":1751353339829064, "dur":516, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UnityWebRequestModule.cpp" }}
,{ "pid":12345, "tid":8, "ts":1751353339829596, "dur":218, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353339828809, "dur":1005, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kzwq165kpxqi.o" }}
,{ "pid":12345, "tid":8, "ts":1751353339829894, "dur":2617, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem.cpp" }}
,{ "pid":12345, "tid":8, "ts":1751353339832512, "dur":3173, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem.cpp" }}
,{ "pid":12345, "tid":8, "ts":1751353339835703, "dur":223, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353339829863, "dur":6064, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/rodfxnqmp95i.o" }}
,{ "pid":12345, "tid":8, "ts":1751353339835930, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353339836097, "dur":12399, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppInvokerTable.cpp" }}
,{ "pid":12345, "tid":8, "ts":1751353339848497, "dur":12333, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppInvokerTable.cpp" }}
,{ "pid":12345, "tid":8, "ts":1751353339860850, "dur":212, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353339836019, "dur":25043, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vn7cdxf0399v.o" }}
,{ "pid":12345, "tid":8, "ts":1751353340068771, "dur":32929011, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/vn7cdxf0399v.o" }}
,{ "pid":12345, "tid":8, "ts":1751353372999949, "dur":150, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\rsp\\12438370367218343051.rsp" }}
,{ "pid":12345, "tid":8, "ts":1751353372997990, "dur":2111, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Link_Android_arm64 Library/Bee/artifacts/Android/d8kzr/libil2cpp.so" }}
,{ "pid":12345, "tid":8, "ts":1751353373000433, "dur":59942237, "ph":"X", "name": "Link_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/libil2cpp.so" }}
,{ "pid":12345, "tid":8, "ts":1751353432943101, "dur":207139, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/artifacts/objcopy_5lmi/libil2cpp.dbg.so" }}
,{ "pid":12345, "tid":8, "ts":1751353433150547, "dur":646797, "ph":"X", "name": "NdkObjCopy",  "args": { "detail":"Library/Bee/artifacts/objcopy_5lmi/libil2cpp.sym.so" }}
,{ "pid":12345, "tid":8, "ts":1751353433797400, "dur":16830, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/Android/il2cppOutput/build/arm64-v8a/libil2cpp.sym.so" }}
,{ "pid":12345, "tid":8, "ts":1751353433814235, "dur":215952, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353291074068, "dur":38058, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353291112130, "dur":2893, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353291115023, "dur":22630, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353291137662, "dur":237351, "ph":"X", "name": "ComputeLeafInputSignature",  "args": { "detail":"Lib_Android_arm64 Library/Bee/artifacts/Android/87lik/il2cpp.a" }}
,{ "pid":12345, "tid":9, "ts":1751353291375069, "dur":2483, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__2.cpp" }}
,{ "pid":12345, "tid":9, "ts":1751353291377553, "dur":2369, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__2.cpp" }}
,{ "pid":12345, "tid":9, "ts":1751353291379936, "dur":162, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353291375049, "dur":5049, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jbm9bjl27jgz.o" }}
,{ "pid":12345, "tid":9, "ts":1751353291380168, "dur":2336, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__11.cpp" }}
,{ "pid":12345, "tid":9, "ts":1751353291382504, "dur":2653, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__11.cpp" }}
,{ "pid":12345, "tid":9, "ts":1751353291385169, "dur":140, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353291380148, "dur":5161, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/d9rg6ujesit9.o" }}
,{ "pid":12345, "tid":9, "ts":1751353291385310, "dur":557, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353291385909, "dur":2464, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppGenericAdjustorThunkTable.c" }}
,{ "pid":12345, "tid":9, "ts":1751353291388374, "dur":1920, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppGenericAdjustorThunkTable.c" }}
,{ "pid":12345, "tid":9, "ts":1751353291390306, "dur":53, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353291385882, "dur":4478, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/85e7sibqjg3m.o" }}
,{ "pid":12345, "tid":9, "ts":1751353291390759, "dur":1111690, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/85e7sibqjg3m.o" }}
,{ "pid":12345, "tid":9, "ts":1751353292502672, "dur":158, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UnityAnalyticsCommonModule_CodeGen.c" }}
,{ "pid":12345, "tid":9, "ts":1751353292502831, "dur":260, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UnityAnalyticsCommonModule_CodeGen.c" }}
,{ "pid":12345, "tid":9, "ts":1751353292503107, "dur":53, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353292502640, "dur":521, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a7ezf8z3szds.o" }}
,{ "pid":12345, "tid":9, "ts":1751353292503613, "dur":894701, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/a7ezf8z3szds.o" }}
,{ "pid":12345, "tid":9, "ts":1751353293398566, "dur":1734, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__12.cpp" }}
,{ "pid":12345, "tid":9, "ts":1751353293400301, "dur":2924, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__12.cpp" }}
,{ "pid":12345, "tid":9, "ts":1751353293403261, "dur":532, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353293398535, "dur":5259, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xmue5m705mnv.o" }}
,{ "pid":12345, "tid":9, "ts":1751353293403914, "dur":2265, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__8.cpp" }}
,{ "pid":12345, "tid":9, "ts":1751353293406180, "dur":2814, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__8.cpp" }}
,{ "pid":12345, "tid":9, "ts":1751353293409012, "dur":195, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353293403858, "dur":5350, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xh4u7f3orcpz.o" }}
,{ "pid":12345, "tid":9, "ts":1751353293409291, "dur":149, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\__Generated_CodeGen.c" }}
,{ "pid":12345, "tid":9, "ts":1751353293409440, "dur":213, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\__Generated_CodeGen.c" }}
,{ "pid":12345, "tid":9, "ts":1751353293409258, "dur":455, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2mhn039af6b9.o" }}
,{ "pid":12345, "tid":9, "ts":1751353293410108, "dur":696103, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/2mhn039af6b9.o" }}
,{ "pid":12345, "tid":9, "ts":1751353294106521, "dur":4930, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__19.cpp" }}
,{ "pid":12345, "tid":9, "ts":1751353294111453, "dur":4990, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__19.cpp" }}
,{ "pid":12345, "tid":9, "ts":1751353294116462, "dur":259, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353294106486, "dur":10236, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v6q86bciim52.o" }}
,{ "pid":12345, "tid":9, "ts":1751353294117296, "dur":38541260, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/v6q86bciim52.o" }}
,{ "pid":12345, "tid":9, "ts":1751353332658876, "dur":2711, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UI__3.cpp" }}
,{ "pid":12345, "tid":9, "ts":1751353332661589, "dur":3799, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UI__3.cpp" }}
,{ "pid":12345, "tid":9, "ts":1751353332665406, "dur":209, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353332658841, "dur":6774, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/j4agohqpqt00.o" }}
,{ "pid":12345, "tid":9, "ts":1751353332665688, "dur":2480, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__2.cpp" }}
,{ "pid":12345, "tid":9, "ts":1751353332668174, "dur":2715, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__2.cpp" }}
,{ "pid":12345, "tid":9, "ts":1751353332670906, "dur":233, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353332665658, "dur":5481, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/lz9rfs69d1h2.o" }}
,{ "pid":12345, "tid":9, "ts":1751353332671238, "dur":2011, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__28.cpp" }}
,{ "pid":12345, "tid":9, "ts":1751353332673250, "dur":2104, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__28.cpp" }}
,{ "pid":12345, "tid":9, "ts":1751353332675364, "dur":210, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353332671205, "dur":4369, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/106avjl0gpni.o" }}
,{ "pid":12345, "tid":9, "ts":1751353332937492, "dur":14043462, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/106avjl0gpni.o" }}
,{ "pid":12345, "tid":9, "ts":1751353346981229, "dur":2219, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__21.cpp" }}
,{ "pid":12345, "tid":9, "ts":1751353346983449, "dur":2860, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__21.cpp" }}
,{ "pid":12345, "tid":9, "ts":1751353346986327, "dur":216, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353346981192, "dur":5352, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/t0bcgf387pqw.o" }}
,{ "pid":12345, "tid":9, "ts":1751353346987088, "dur":11976069, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/t0bcgf387pqw.o" }}
,{ "pid":12345, "tid":9, "ts":1751353358963319, "dur":75066775, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353291074115, "dur":38025, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353291114155, "dur":588, "ph":"X", "name": "File",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Net.WebSockets.Client.dll" }}
,{ "pid":12345, "tid":10, "ts":1751353291112146, "dur":3101, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353291115247, "dur":22464, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353291137765, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353291137889, "dur":190, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt_el85.info" }}
,{ "pid":12345, "tid":10, "ts":1751353291138117, "dur":415, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/unity_app_guid_2cqv.info" }}
,{ "pid":12345, "tid":10, "ts":1751353291138533, "dur":6606, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353291145141, "dur":304, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/System.Core-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1751353291145447, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353291145572, "dur":364, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TilemapModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1751353291146030, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.HierarchyCoreModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":10, "ts":1751353291146306, "dur":133, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.HierarchyCoreModule.pdb" }}
,{ "pid":12345, "tid":10, "ts":1751353291146528, "dur":152, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.PhysicsModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1751353291146755, "dur":412, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.InputSystem.ForUI.pdb" }}
,{ "pid":12345, "tid":10, "ts":1751353291147168, "dur":7283, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353291154499, "dur":2531, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__16.cpp" }}
,{ "pid":12345, "tid":10, "ts":1751353291157031, "dur":10478, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__16.cpp" }}
,{ "pid":12345, "tid":10, "ts":1751353291167532, "dur":240, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353291154453, "dur":13320, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/6z6dmin38mqq.o" }}
,{ "pid":12345, "tid":10, "ts":1751353291167883, "dur":2386, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule.cpp" }}
,{ "pid":12345, "tid":10, "ts":1751353291170270, "dur":4176, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule.cpp" }}
,{ "pid":12345, "tid":10, "ts":1751353291174464, "dur":215, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353291167849, "dur":6831, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/91501rpe8hhy.o" }}
,{ "pid":12345, "tid":10, "ts":1751353291174755, "dur":2988, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods.cpp" }}
,{ "pid":12345, "tid":10, "ts":1751353291177744, "dur":3556, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods.cpp" }}
,{ "pid":12345, "tid":10, "ts":1751353291181318, "dur":237, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353291174729, "dur":6827, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/irjo0akm51pw.o" }}
,{ "pid":12345, "tid":10, "ts":1751353291181641, "dur":2686, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__34.cpp" }}
,{ "pid":12345, "tid":10, "ts":1751353291184328, "dur":3210, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__34.cpp" }}
,{ "pid":12345, "tid":10, "ts":1751353291187556, "dur":251, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353291181607, "dur":6201, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gkcd8ynq6r6b.o" }}
,{ "pid":12345, "tid":10, "ts":1751353291188285, "dur":12428999, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/gkcd8ynq6r6b.o" }}
,{ "pid":12345, "tid":10, "ts":1751353303617552, "dur":2080, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__13.cpp" }}
,{ "pid":12345, "tid":10, "ts":1751353303619633, "dur":3698, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__13.cpp" }}
,{ "pid":12345, "tid":10, "ts":1751353303623352, "dur":245, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353303617516, "dur":6082, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8lzx7g8ncbk0.o" }}
,{ "pid":12345, "tid":10, "ts":1751353303755760, "dur":28882862, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/8lzx7g8ncbk0.o" }}
,{ "pid":12345, "tid":10, "ts":1751353332638841, "dur":103, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TerrainPhysicsModule.cpp" }}
,{ "pid":12345, "tid":10, "ts":1751353332638944, "dur":391, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TerrainPhysicsModule.cpp" }}
,{ "pid":12345, "tid":10, "ts":1751353332639350, "dur":201, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353332638813, "dur":739, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/o4pvni6nxhra.o" }}
,{ "pid":12345, "tid":10, "ts":1751353332639629, "dur":1550279, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/o4pvni6nxhra.o" }}
,{ "pid":12345, "tid":10, "ts":1751353334190172, "dur":1829, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.IMGUIModule.cpp" }}
,{ "pid":12345, "tid":10, "ts":1751353334192008, "dur":2386, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.IMGUIModule.cpp" }}
,{ "pid":12345, "tid":10, "ts":1751353334194410, "dur":204, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353334190135, "dur":4481, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jxnazyrk9929.o" }}
,{ "pid":12345, "tid":10, "ts":1751353334194706, "dur":1856, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__25.cpp" }}
,{ "pid":12345, "tid":10, "ts":1751353334196563, "dur":3773, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__25.cpp" }}
,{ "pid":12345, "tid":10, "ts":1751353334200381, "dur":615, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353334194674, "dur":6323, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uxfiflc76aj0.o" }}
,{ "pid":12345, "tid":10, "ts":1751353334201779, "dur":11928348, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/uxfiflc76aj0.o" }}
,{ "pid":12345, "tid":10, "ts":1751353346130420, "dur":231, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TerrainModule_CodeGen.c" }}
,{ "pid":12345, "tid":10, "ts":1751353346130652, "dur":279, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TerrainModule_CodeGen.c" }}
,{ "pid":12345, "tid":10, "ts":1751353346130947, "dur":53, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353346130379, "dur":621, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/psc99vj4y3bm.o" }}
,{ "pid":12345, "tid":10, "ts":1751353346452060, "dur":52, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353346131104, "dur":321066, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/psc99vj4y3bm.o" }}
,{ "pid":12345, "tid":10, "ts":1751353346452582, "dur":2097, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__13.cpp" }}
,{ "pid":12345, "tid":10, "ts":1751353346454682, "dur":4650, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__13.cpp" }}
,{ "pid":12345, "tid":10, "ts":1751353346459379, "dur":642, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353346452522, "dur":7500, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/17ru68ruxfr6.o" }}
,{ "pid":12345, "tid":10, "ts":1751353346460142, "dur":4079, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__8.cpp" }}
,{ "pid":12345, "tid":10, "ts":1751353346464223, "dur":6977, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__8.cpp" }}
,{ "pid":12345, "tid":10, "ts":1751353346471249, "dur":641, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353346460080, "dur":11811, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/z5rdbpowxtq2.o" }}
,{ "pid":12345, "tid":10, "ts":1751353346472018, "dur":362, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Mono.Security.cpp" }}
,{ "pid":12345, "tid":10, "ts":1751353346472382, "dur":1417, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Mono.Security.cpp" }}
,{ "pid":12345, "tid":10, "ts":1751353346473814, "dur":210, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353346471964, "dur":2061, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mcvqv3cujwyv.o" }}
,{ "pid":12345, "tid":10, "ts":1751353346474102, "dur":1851, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__18.cpp" }}
,{ "pid":12345, "tid":10, "ts":1751353346475954, "dur":2865, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__18.cpp" }}
,{ "pid":12345, "tid":10, "ts":1751353346478834, "dur":194, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353346474074, "dur":4955, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/snuim7hzfdjp.o" }}
,{ "pid":12345, "tid":10, "ts":1751353346479120, "dur":1633, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__42.cpp" }}
,{ "pid":12345, "tid":10, "ts":1751353346480754, "dur":2291, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__42.cpp" }}
,{ "pid":12345, "tid":10, "ts":1751353346483062, "dur":216, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353346479092, "dur":4186, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hr5jl3kc3ki4.o" }}
,{ "pid":12345, "tid":10, "ts":1751353346506426, "dur":10715626, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/hr5jl3kc3ki4.o" }}
,{ "pid":12345, "tid":10, "ts":1751353357222281, "dur":76807757, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353291074179, "dur":37977, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353291112161, "dur":2108, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353291114270, "dur":1952, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353291116223, "dur":21452, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353291137862, "dur":95, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/System.Core-FeaturesChecked.txt_k2yd.info" }}
,{ "pid":12345, "tid":11, "ts":1751353291137997, "dur":359, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.SubsystemsModule-FeaturesChecked.txt_vzqa.info" }}
,{ "pid":12345, "tid":11, "ts":1751353291138400, "dur":176, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/Resources/unity default resources" }}
,{ "pid":12345, "tid":11, "ts":1751353291138577, "dur":6612, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353291145189, "dur":1186, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.TextCoreTextEngineModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":11, "ts":1751353291146376, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353291146600, "dur":205, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TerrainModule.pdb" }}
,{ "pid":12345, "tid":11, "ts":1751353291146882, "dur":57, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AnimationModule.pdb" }}
,{ "pid":12345, "tid":11, "ts":1751353291146951, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353291147114, "dur":7339, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353291154495, "dur":305, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AudioModule.cpp" }}
,{ "pid":12345, "tid":11, "ts":1751353291154801, "dur":12929, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AudioModule.cpp" }}
,{ "pid":12345, "tid":11, "ts":1751353291167750, "dur":212, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353291154455, "dur":13508, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2wexl85vs87e.o" }}
,{ "pid":12345, "tid":11, "ts":1751353291168061, "dur":3276, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__58.cpp" }}
,{ "pid":12345, "tid":11, "ts":1751353291171339, "dur":4056, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__58.cpp" }}
,{ "pid":12345, "tid":11, "ts":1751353291175408, "dur":188, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353291168032, "dur":7565, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2znopnsfpiqz.o" }}
,{ "pid":12345, "tid":11, "ts":1751353291176060, "dur":41367436, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/2znopnsfpiqz.o" }}
,{ "pid":12345, "tid":11, "ts":1751353332543876, "dur":1770, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__53.cpp" }}
,{ "pid":12345, "tid":11, "ts":1751353332545648, "dur":2678, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__53.cpp" }}
,{ "pid":12345, "tid":11, "ts":1751353332548340, "dur":212, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353332543833, "dur":4719, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/988q9zoq8dti.o" }}
,{ "pid":12345, "tid":11, "ts":1751353332901415, "dur":9611877, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/988q9zoq8dti.o" }}
,{ "pid":12345, "tid":11, "ts":1751353342513586, "dur":184, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine_CodeGen.c" }}
,{ "pid":12345, "tid":11, "ts":1751353342513771, "dur":249, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine_CodeGen.c" }}
,{ "pid":12345, "tid":11, "ts":1751353342514035, "dur":63, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353342513548, "dur":551, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mw9ositkrg7u.o" }}
,{ "pid":12345, "tid":11, "ts":1751353342514179, "dur":244, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.SharedInternalsModule.cpp" }}
,{ "pid":12345, "tid":11, "ts":1751353342514424, "dur":528, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.SharedInternalsModule.cpp" }}
,{ "pid":12345, "tid":11, "ts":1751353342514968, "dur":204, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353342514147, "dur":1026, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/x88dxcrw5he9.o" }}
,{ "pid":12345, "tid":11, "ts":1751353342515251, "dur":1979, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UI__2.cpp" }}
,{ "pid":12345, "tid":11, "ts":1751353342517230, "dur":2490, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UI__2.cpp" }}
,{ "pid":12345, "tid":11, "ts":1751353342519736, "dur":213, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353342515222, "dur":4728, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/yo7r87bsn5oh.o" }}
,{ "pid":12345, "tid":11, "ts":1751353342520053, "dur":5187, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCCalculateTypeValues.cpp" }}
,{ "pid":12345, "tid":11, "ts":1751353342525241, "dur":6171, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCCalculateTypeValues.cpp" }}
,{ "pid":12345, "tid":11, "ts":1751353342531434, "dur":241, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353342520011, "dur":11665, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ajhot4s4ia6f.o" }}
,{ "pid":12345, "tid":11, "ts":1751353342532237, "dur":3650423, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/ajhot4s4ia6f.o" }}
,{ "pid":12345, "tid":11, "ts":1751353346182953, "dur":1575, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__72.cpp" }}
,{ "pid":12345, "tid":11, "ts":1751353346184536, "dur":2171, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__72.cpp" }}
,{ "pid":12345, "tid":11, "ts":1751353346186733, "dur":216, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353346182916, "dur":4034, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/iqmxf7rrbell.o" }}
,{ "pid":12345, "tid":11, "ts":1751353357497116, "dur":72, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353346187405, "dur":11309819, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/iqmxf7rrbell.o" }}
,{ "pid":12345, "tid":11, "ts":1751353357497471, "dur":76532563, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353291074242, "dur":37930, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353291112778, "dur":541, "ph":"X", "name": "File",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.IO.FileSystem.dll" }}
,{ "pid":12345, "tid":12, "ts":1751353291114280, "dur":650, "ph":"X", "name": "File",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\il2cpp\\build\\deploy\\System.Formats.Tar.dll" }}
,{ "pid":12345, "tid":12, "ts":1751353291112177, "dur":2950, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353291115127, "dur":22533, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353291137670, "dur":54, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityClassRegistration.cpp_3fxp.info" }}
,{ "pid":12345, "tid":12, "ts":1751353291137726, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353291137847, "dur":59, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt_jk15.info" }}
,{ "pid":12345, "tid":12, "ts":1751353291137907, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353291137967, "dur":285, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityWebRequestModule-FeaturesChecked.txt_jk15.info" }}
,{ "pid":12345, "tid":12, "ts":1751353291138258, "dur":107, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/values/strings.xml" }}
,{ "pid":12345, "tid":12, "ts":1751353291138399, "dur":78, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/data.unity3d" }}
,{ "pid":12345, "tid":12, "ts":1751353291138508, "dur":6641, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353291145170, "dur":462, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.InputLegacyModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1751353291145635, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353291145783, "dur":337, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.InputSystem.ForUI-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":12, "ts":1751353291146304, "dur":97, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.HierarchyCoreModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1751353291146463, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353291146524, "dur":188, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.XRModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1751353291146829, "dur":328, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.Physics2DModule.dll" }}
,{ "pid":12345, "tid":12, "ts":1751353291147159, "dur":7412, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353291154641, "dur":2126, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__45.cpp" }}
,{ "pid":12345, "tid":12, "ts":1751353291156768, "dur":9484, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__45.cpp" }}
,{ "pid":12345, "tid":12, "ts":1751353291166288, "dur":210, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353291154572, "dur":11926, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ouwc11t5czmm.o" }}
,{ "pid":12345, "tid":12, "ts":1751353291166966, "dur":12452904, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/ouwc11t5czmm.o" }}
,{ "pid":12345, "tid":12, "ts":1751353303620168, "dur":2982, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__33.cpp" }}
,{ "pid":12345, "tid":12, "ts":1751353303623152, "dur":3370, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__33.cpp" }}
,{ "pid":12345, "tid":12, "ts":1751353303626537, "dur":187, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353303620125, "dur":6600, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v48osp9s33us.o" }}
,{ "pid":12345, "tid":12, "ts":1751353304726962, "dur":27927050, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/v48osp9s33us.o" }}
,{ "pid":12345, "tid":12, "ts":1751353332654323, "dur":2060, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__40.cpp" }}
,{ "pid":12345, "tid":12, "ts":1751353332656384, "dur":2564, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__40.cpp" }}
,{ "pid":12345, "tid":12, "ts":1751353332658967, "dur":250, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353332654282, "dur":4935, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v3hfleqgwup5.o" }}
,{ "pid":12345, "tid":12, "ts":1751353332936641, "dur":11853079, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/v3hfleqgwup5.o" }}
,{ "pid":12345, "tid":12, "ts":1751353344789986, "dur":1875, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__3.cpp" }}
,{ "pid":12345, "tid":12, "ts":1751353344791862, "dur":2331, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__3.cpp" }}
,{ "pid":12345, "tid":12, "ts":1751353344794211, "dur":230, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353344789952, "dur":4489, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mckl3g7t98d1.o" }}
,{ "pid":12345, "tid":12, "ts":1751353344794550, "dur":158, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCodeRegistration.cpp" }}
,{ "pid":12345, "tid":12, "ts":1751353344794709, "dur":453, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCodeRegistration.cpp" }}
,{ "pid":12345, "tid":12, "ts":1751353344795177, "dur":198, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353344794518, "dur":858, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/55wal3sioh2n.o" }}
,{ "pid":12345, "tid":12, "ts":1751353344795820, "dur":2101075, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/55wal3sioh2n.o" }}
,{ "pid":12345, "tid":12, "ts":1751353346897186, "dur":172, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.GridModule.cpp" }}
,{ "pid":12345, "tid":12, "ts":1751353346897367, "dur":540, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.GridModule.cpp" }}
,{ "pid":12345, "tid":12, "ts":1751353346897925, "dur":216, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353346897143, "dur":998, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/coeqiwfgefg2.o" }}
,{ "pid":12345, "tid":12, "ts":1751353346898235, "dur":3424, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__13.cpp" }}
,{ "pid":12345, "tid":12, "ts":1751353346901660, "dur":4225, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__13.cpp" }}
,{ "pid":12345, "tid":12, "ts":1751353346905901, "dur":214, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353346898198, "dur":7918, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/zwfl6jeaz8l8.o" }}
,{ "pid":12345, "tid":12, "ts":1751353346906204, "dur":1631, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__24.cpp" }}
,{ "pid":12345, "tid":12, "ts":1751353346907837, "dur":2361, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__24.cpp" }}
,{ "pid":12345, "tid":12, "ts":1751353346910212, "dur":232, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353346906173, "dur":4272, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0324vdc2h4ue.o" }}
,{ "pid":12345, "tid":12, "ts":1751353346911352, "dur":11442956, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/0324vdc2h4ue.o" }}
,{ "pid":12345, "tid":12, "ts":1751353358354519, "dur":75675698, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353291074338, "dur":37848, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353291112191, "dur":2438, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353291114630, "dur":1192, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353291115823, "dur":21912, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353291137737, "dur":72, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.InputSystem-FeaturesChecked.txt_ivdb.info" }}
,{ "pid":12345, "tid":13, "ts":1751353291137847, "dur":303, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Unity.InputSystem-FeaturesChecked.txt_ivdb.info" }}
,{ "pid":12345, "tid":13, "ts":1751353291138152, "dur":106, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/app_icon.png" }}
,{ "pid":12345, "tid":13, "ts":1751353291138419, "dur":187, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/ScriptingAssemblies.json" }}
,{ "pid":12345, "tid":13, "ts":1751353291138608, "dur":6514, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353291145124, "dur":1295, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ICallRegistrationGenerator D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/Android/il2cppOutput/UnityICallRegistration.cpp" }}
,{ "pid":12345, "tid":13, "ts":1751353291146670, "dur":290, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"Generate Library/Bee/artifacts/Android/libunity/arm64-v8a/libunity.dbg.so from Library/Bee/artifacts/Android/libunity/arm64-v8a/unstripped/libunity.so Library/Bee/artifacts/Android/libunity/arm64-v8a/libunity.dbg.so" }}
,{ "pid":12345, "tid":13, "ts":1751353291146994, "dur":242, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"Generate Library/Bee/artifacts/Android/libunity/arm64-v8a/libunity.sym.so from Library/Bee/artifacts/Android/libunity/arm64-v8a/libunity.dbg.so Library/Bee/artifacts/Android/libunity/arm64-v8a/libunity.sym.so" }}
,{ "pid":12345, "tid":13, "ts":1751353291147292, "dur":7189, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353291154535, "dur":4237, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__63.cpp" }}
,{ "pid":12345, "tid":13, "ts":1751353291158773, "dur":9178, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__63.cpp" }}
,{ "pid":12345, "tid":13, "ts":1751353291167975, "dur":256, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353291154483, "dur":13749, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/w3p2mtg8bxx9.o" }}
,{ "pid":12345, "tid":13, "ts":1751353291168906, "dur":14934566, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/w3p2mtg8bxx9.o" }}
,{ "pid":12345, "tid":13, "ts":1751353306103749, "dur":180, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.VRModule.cpp" }}
,{ "pid":12345, "tid":13, "ts":1751353306103930, "dur":535, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.VRModule.cpp" }}
,{ "pid":12345, "tid":13, "ts":1751353306104494, "dur":209, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353306103713, "dur":990, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mri45r890jp3.o" }}
,{ "pid":12345, "tid":13, "ts":1751353306104786, "dur":1131, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule__2.cpp" }}
,{ "pid":12345, "tid":13, "ts":1751353306105918, "dur":1007, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule__2.cpp" }}
,{ "pid":12345, "tid":13, "ts":1751353306106941, "dur":205, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353306104755, "dur":2392, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/zvidtdy85f7e.o" }}
,{ "pid":12345, "tid":13, "ts":1751353306107231, "dur":186, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.JSONSerializeModule.cpp" }}
,{ "pid":12345, "tid":13, "ts":1751353306107418, "dur":471, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.JSONSerializeModule.cpp" }}
,{ "pid":12345, "tid":13, "ts":1751353306107905, "dur":127, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353306107199, "dur":834, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/c7i60zrowo1v.o" }}
,{ "pid":12345, "tid":13, "ts":1751353306108108, "dur":1979, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__6.cpp" }}
,{ "pid":12345, "tid":13, "ts":1751353306110088, "dur":4472, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__6.cpp" }}
,{ "pid":12345, "tid":13, "ts":1751353306114577, "dur":243, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353306108075, "dur":6746, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/wvt1belsv8uz.o" }}
,{ "pid":12345, "tid":13, "ts":1751353306114913, "dur":2262, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__9.cpp" }}
,{ "pid":12345, "tid":13, "ts":1751353306117176, "dur":2926, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__9.cpp" }}
,{ "pid":12345, "tid":13, "ts":1751353306120118, "dur":202, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353306114881, "dur":5440, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ix7inphmu00i.o" }}
,{ "pid":12345, "tid":13, "ts":1751353306120419, "dur":2328, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__38.cpp" }}
,{ "pid":12345, "tid":13, "ts":1751353306122748, "dur":2830, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__38.cpp" }}
,{ "pid":12345, "tid":13, "ts":1751353306125594, "dur":202, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353306120386, "dur":5411, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ovnm8cdik8r9.o" }}
,{ "pid":12345, "tid":13, "ts":1751353306435931, "dur":26200562, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/ovnm8cdik8r9.o" }}
,{ "pid":12345, "tid":13, "ts":1751353332636706, "dur":718, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp.cpp" }}
,{ "pid":12345, "tid":13, "ts":1751353332637425, "dur":1052, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp.cpp" }}
,{ "pid":12345, "tid":13, "ts":1751353332638501, "dur":170, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353332636679, "dur":1993, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hu59tggcxxy3.o" }}
,{ "pid":12345, "tid":13, "ts":1751353332638738, "dur":70, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353332639133, "dur":4947406, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/hu59tggcxxy3.o" }}
,{ "pid":12345, "tid":13, "ts":1751353337586856, "dur":375, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System_CodeGen.c" }}
,{ "pid":12345, "tid":13, "ts":1751353337587232, "dur":435, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System_CodeGen.c" }}
,{ "pid":12345, "tid":13, "ts":1751353337587681, "dur":54, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353337586814, "dur":922, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/67lvu8eehrja.o" }}
,{ "pid":12345, "tid":13, "ts":1751353337588156, "dur":490687, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/67lvu8eehrja.o" }}
,{ "pid":12345, "tid":13, "ts":1751353338079139, "dur":2085, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__26.cpp" }}
,{ "pid":12345, "tid":13, "ts":1751353338081226, "dur":2627, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__26.cpp" }}
,{ "pid":12345, "tid":13, "ts":1751353338083869, "dur":210, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353338079099, "dur":4981, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v5zyz0s5ad0f.o" }}
,{ "pid":12345, "tid":13, "ts":1751353338084576, "dur":14022487, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/v5zyz0s5ad0f.o" }}
,{ "pid":12345, "tid":13, "ts":1751353352107352, "dur":2215, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__12.cpp" }}
,{ "pid":12345, "tid":13, "ts":1751353352109568, "dur":2909, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__12.cpp" }}
,{ "pid":12345, "tid":13, "ts":1751353352112494, "dur":224, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353352107313, "dur":5406, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7dfhn1rp882k.o" }}
,{ "pid":12345, "tid":13, "ts":1751353352113185, "dur":11730205, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/7dfhn1rp882k.o" }}
,{ "pid":12345, "tid":13, "ts":1751353363843549, "dur":70186513, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353291074472, "dur":37736, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353291112223, "dur":3102, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353291115325, "dur":22496, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353291137829, "dur":110, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.VRModule-FeaturesChecked.txt_nz9x.info" }}
,{ "pid":12345, "tid":14, "ts":1751353291137941, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353291138000, "dur":624, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.VRModule-FeaturesChecked.txt_nz9x.info" }}
,{ "pid":12345, "tid":14, "ts":1751353291138625, "dur":6521, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353291145151, "dur":381, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.SpriteShapeModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":14, "ts":1751353291145636, "dur":143, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.SubsystemsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":14, "ts":1751353291145867, "dur":648, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.JSONSerializeModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":14, "ts":1751353291146558, "dur":176, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.JSONSerializeModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":14, "ts":1751353291146911, "dur":448, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextCoreTextEngineModule.pdb" }}
,{ "pid":12345, "tid":14, "ts":1751353291147362, "dur":7131, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353291154655, "dur":2472, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__14.cpp" }}
,{ "pid":12345, "tid":14, "ts":1751353291157128, "dur":10903, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__14.cpp" }}
,{ "pid":12345, "tid":14, "ts":1751353291168054, "dur":264, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353291154500, "dur":13819, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/t12lnqmlfzvj.o" }}
,{ "pid":12345, "tid":14, "ts":1751353291169571, "dur":12058162, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/t12lnqmlfzvj.o" }}
,{ "pid":12345, "tid":14, "ts":1751353303228076, "dur":3063, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__51.cpp" }}
,{ "pid":12345, "tid":14, "ts":1751353303231140, "dur":3332, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__51.cpp" }}
,{ "pid":12345, "tid":14, "ts":1751353303234488, "dur":198, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353303228036, "dur":6652, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3d2eehwv215f.o" }}
,{ "pid":12345, "tid":14, "ts":1751353303755037, "dur":28896871, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/3d2eehwv215f.o" }}
,{ "pid":12345, "tid":14, "ts":1751353332652163, "dur":130, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Mono.Security_CodeGen.c" }}
,{ "pid":12345, "tid":14, "ts":1751353332652293, "dur":228, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Mono.Security_CodeGen.c" }}
,{ "pid":12345, "tid":14, "ts":1751353332652536, "dur":51, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353332652129, "dur":459, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/e7m8oxn5tfdg.o" }}
,{ "pid":12345, "tid":14, "ts":1751353332910507, "dur":467263, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/e7m8oxn5tfdg.o" }}
,{ "pid":12345, "tid":14, "ts":1751353333378014, "dur":764, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppInteropDataTable.cpp" }}
,{ "pid":12345, "tid":14, "ts":1751353333378778, "dur":1001, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppInteropDataTable.cpp" }}
,{ "pid":12345, "tid":14, "ts":1751353333379795, "dur":227, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353333377983, "dur":2039, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/eoeljpbnd8zv.o" }}
,{ "pid":12345, "tid":14, "ts":1751353333380103, "dur":3754, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__37.cpp" }}
,{ "pid":12345, "tid":14, "ts":1751353333383857, "dur":3905, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__37.cpp" }}
,{ "pid":12345, "tid":14, "ts":1751353333387780, "dur":231, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353333380074, "dur":7938, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nn35nxwjg6au.o" }}
,{ "pid":12345, "tid":14, "ts":1751353333388435, "dur":12728293, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/nn35nxwjg6au.o" }}
,{ "pid":12345, "tid":14, "ts":1751353346117024, "dur":366, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.Core_CodeGen.c" }}
,{ "pid":12345, "tid":14, "ts":1751353346117391, "dur":404, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.Core_CodeGen.c" }}
,{ "pid":12345, "tid":14, "ts":1751353346117810, "dur":54, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353346116983, "dur":881, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9wvonircd98c.o" }}
,{ "pid":12345, "tid":14, "ts":1751353346136160, "dur":321772, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/9wvonircd98c.o" }}
,{ "pid":12345, "tid":14, "ts":1751353346458223, "dur":326, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.IMGUIModule_CodeGen.c" }}
,{ "pid":12345, "tid":14, "ts":1751353346458551, "dur":387, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.IMGUIModule_CodeGen.c" }}
,{ "pid":12345, "tid":14, "ts":1751353346458953, "dur":53, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353346458190, "dur":816, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/n96lhotmyyf2.o" }}
,{ "pid":12345, "tid":14, "ts":1751353346904850, "dur":94, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353346493132, "dur":411834, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/n96lhotmyyf2.o" }}
,{ "pid":12345, "tid":14, "ts":1751353346905497, "dur":2541, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__23.cpp" }}
,{ "pid":12345, "tid":14, "ts":1751353346908040, "dur":2758, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__23.cpp" }}
,{ "pid":12345, "tid":14, "ts":1751353346910814, "dur":201, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353346905457, "dur":5559, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/o285j2jc1yvx.o" }}
,{ "pid":12345, "tid":14, "ts":1751353346911433, "dur":11362407, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/o285j2jc1yvx.o" }}
,{ "pid":12345, "tid":14, "ts":1751353358273996, "dur":75756094, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353291074560, "dur":37670, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353291112235, "dur":2526, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353291114762, "dur":1452, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353291116215, "dur":21465, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353291137862, "dur":251, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/Mono.Security-FeaturesChecked.txt_pcel.info" }}
,{ "pid":12345, "tid":15, "ts":1751353291138174, "dur":120, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-anydpi-v26/app_icon_round.xml" }}
,{ "pid":12345, "tid":15, "ts":1751353291138348, "dur":94, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/freeformwindow.xml" }}
,{ "pid":12345, "tid":15, "ts":1751353291138487, "dur":243, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/freeformwindow.xml" }}
,{ "pid":12345, "tid":15, "ts":1751353291138732, "dur":6444, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353291145178, "dur":325, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/System-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":15, "ts":1751353291145505, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353291145608, "dur":148, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.InputForUIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":15, "ts":1751353291145838, "dur":230, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.AudioModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":15, "ts":1751353291146404, "dur":157, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.dll" }}
,{ "pid":12345, "tid":15, "ts":1751353291146844, "dur":445, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/Unity.InputSystem.pdb" }}
,{ "pid":12345, "tid":15, "ts":1751353291147290, "dur":7190, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353291154541, "dur":1347, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppRgctxTable.c" }}
,{ "pid":12345, "tid":15, "ts":1751353291155888, "dur":1152, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppRgctxTable.c" }}
,{ "pid":12345, "tid":15, "ts":1751353291157059, "dur":60, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353291154481, "dur":2639, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/jov4p39u0xgj.o" }}
,{ "pid":12345, "tid":15, "ts":1751353291157726, "dur":1367273, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/jov4p39u0xgj.o" }}
,{ "pid":12345, "tid":15, "ts":1751353292525238, "dur":132, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.JSONSerializeModule_CodeGen.c" }}
,{ "pid":12345, "tid":15, "ts":1751353292525371, "dur":252, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.JSONSerializeModule_CodeGen.c" }}
,{ "pid":12345, "tid":15, "ts":1751353292525637, "dur":50, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353292525205, "dur":483, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kmha12tx5jhl.o" }}
,{ "pid":12345, "tid":15, "ts":1751353292526080, "dur":878255, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/kmha12tx5jhl.o" }}
,{ "pid":12345, "tid":15, "ts":1751353293404574, "dur":1792, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AndroidJNIModule.cpp" }}
,{ "pid":12345, "tid":15, "ts":1751353293406367, "dur":2444, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AndroidJNIModule.cpp" }}
,{ "pid":12345, "tid":15, "ts":1751353293408828, "dur":219, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353293404544, "dur":4504, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/wzln42kclvsi.o" }}
,{ "pid":12345, "tid":15, "ts":1751353293409123, "dur":2301, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__1.cpp" }}
,{ "pid":12345, "tid":15, "ts":1751353293411425, "dur":2527, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__1.cpp" }}
,{ "pid":12345, "tid":15, "ts":1751353293413969, "dur":205, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353293409099, "dur":5076, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0j63ziscbd6i.o" }}
,{ "pid":12345, "tid":15, "ts":1751353293414252, "dur":1319, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__22.cpp" }}
,{ "pid":12345, "tid":15, "ts":1751353293415572, "dur":1940, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__22.cpp" }}
,{ "pid":12345, "tid":15, "ts":1751353293417528, "dur":209, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353293414226, "dur":3512, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/u8vs0uj7bs7b.o" }}
,{ "pid":12345, "tid":15, "ts":1751353293418158, "dur":12608967, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/u8vs0uj7bs7b.o" }}
,{ "pid":12345, "tid":15, "ts":1751353306027350, "dur":394, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.Physics2DModule.cpp" }}
,{ "pid":12345, "tid":15, "ts":1751353306027744, "dur":788, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.Physics2DModule.cpp" }}
,{ "pid":12345, "tid":15, "ts":1751353306028549, "dur":205, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353306027320, "dur":1435, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hmkb5q84uqmc.o" }}
,{ "pid":12345, "tid":15, "ts":1751353306028831, "dur":636, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System__3.cpp" }}
,{ "pid":12345, "tid":15, "ts":1751353306029473, "dur":802, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System__3.cpp" }}
,{ "pid":12345, "tid":15, "ts":1751353306030291, "dur":204, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353306028803, "dur":1692, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bdu1ja51muwa.o" }}
,{ "pid":12345, "tid":15, "ts":1751353306030567, "dur":492, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCCFieldValuesTable.cpp" }}
,{ "pid":12345, "tid":15, "ts":1751353306031060, "dur":561, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCCFieldValuesTable.cpp" }}
,{ "pid":12345, "tid":15, "ts":1751353306031635, "dur":201, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353306030540, "dur":1297, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xe0ee8df9ia4.o" }}
,{ "pid":12345, "tid":15, "ts":1751353306032243, "dur":26520624, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/xe0ee8df9ia4.o" }}
,{ "pid":12345, "tid":15, "ts":1751353332553199, "dur":2124, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UI__1.cpp" }}
,{ "pid":12345, "tid":15, "ts":1751353332555324, "dur":3134, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UI__1.cpp" }}
,{ "pid":12345, "tid":15, "ts":1751353332558479, "dur":230, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353332553152, "dur":5559, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xgnbosvtj371.o" }}
,{ "pid":12345, "tid":15, "ts":1751353332558828, "dur":1466, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__15.cpp" }}
,{ "pid":12345, "tid":15, "ts":1751353332560295, "dur":2543, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__15.cpp" }}
,{ "pid":12345, "tid":15, "ts":1751353332562857, "dur":242, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353332558788, "dur":4312, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nvfz6xmq45j5.o" }}
,{ "pid":12345, "tid":15, "ts":1751353332563197, "dur":3498, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__2.cpp" }}
,{ "pid":12345, "tid":15, "ts":1751353332566698, "dur":4465, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__2.cpp" }}
,{ "pid":12345, "tid":15, "ts":1751353332571179, "dur":228, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353332563166, "dur":8244, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/e1hme3j3dfum.o" }}
,{ "pid":12345, "tid":15, "ts":1751353332902504, "dur":12792974, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/e1hme3j3dfum.o" }}
,{ "pid":12345, "tid":15, "ts":1751353345695759, "dur":192, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.SubsystemsModule_CodeGen.c" }}
,{ "pid":12345, "tid":15, "ts":1751353345695952, "dur":264, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.SubsystemsModule_CodeGen.c" }}
,{ "pid":12345, "tid":15, "ts":1751353345696231, "dur":52, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353345695722, "dur":561, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ucxyqrog8tbt.o" }}
,{ "pid":12345, "tid":15, "ts":1751353345696699, "dur":385380, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/ucxyqrog8tbt.o" }}
,{ "pid":12345, "tid":15, "ts":1751353346082419, "dur":3113, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__27.cpp" }}
,{ "pid":12345, "tid":15, "ts":1751353346085534, "dur":2735, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__27.cpp" }}
,{ "pid":12345, "tid":15, "ts":1751353346088286, "dur":207, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353346082357, "dur":6137, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gl3mfa72d1bz.o" }}
,{ "pid":12345, "tid":15, "ts":1751353346088925, "dur":12926309, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/gl3mfa72d1bz.o" }}
,{ "pid":12345, "tid":15, "ts":1751353359015404, "dur":75014716, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353291074665, "dur":37585, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353291112257, "dur":3409, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353291115667, "dur":22028, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353291137704, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.HierarchyCoreModule-FeaturesChecked.txt_rzjr.info" }}
,{ "pid":12345, "tid":16, "ts":1751353291137761, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353291137987, "dur":304, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.SharedInternalsModule-FeaturesChecked.txt_sm9d.info" }}
,{ "pid":12345, "tid":16, "ts":1751353291138297, "dur":131, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/styles.xml" }}
,{ "pid":12345, "tid":16, "ts":1751353291138432, "dur":275, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/styles.xml" }}
,{ "pid":12345, "tid":16, "ts":1751353291138709, "dur":6456, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353291145167, "dur":355, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Assembly-CSharp-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":16, "ts":1751353291145523, "dur":304, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353291145827, "dur":67, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Assembly-CSharp-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":16, "ts":1751353291145988, "dur":307, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Mono.Security-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":16, "ts":1751353291146464, "dur":68, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.VRModule.pdb" }}
,{ "pid":12345, "tid":16, "ts":1751353291146543, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353291146608, "dur":713, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TerrainModule.dll" }}
,{ "pid":12345, "tid":16, "ts":1751353291147324, "dur":7160, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353291154576, "dur":3787, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__44.cpp" }}
,{ "pid":12345, "tid":16, "ts":1751353291158365, "dur":7521, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__44.cpp" }}
,{ "pid":12345, "tid":16, "ts":1751353291165930, "dur":320, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353291154486, "dur":11765, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/3etolsf89ab3.o" }}
,{ "pid":12345, "tid":16, "ts":1751353291166764, "dur":12467848, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/3etolsf89ab3.o" }}
,{ "pid":12345, "tid":16, "ts":1751353303634959, "dur":337, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.SubsystemsModule.cpp" }}
,{ "pid":12345, "tid":16, "ts":1751353303635297, "dur":682, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.SubsystemsModule.cpp" }}
,{ "pid":12345, "tid":16, "ts":1751353303635995, "dur":223, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353303634908, "dur":1311, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uj5pvkt515uf.o" }}
,{ "pid":12345, "tid":16, "ts":1751353303636308, "dur":1636, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__46.cpp" }}
,{ "pid":12345, "tid":16, "ts":1751353303637945, "dur":2795, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__46.cpp" }}
,{ "pid":12345, "tid":16, "ts":1751353303640758, "dur":237, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353303636280, "dur":4716, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/99z0gb82fny5.o" }}
,{ "pid":12345, "tid":16, "ts":1751353303756162, "dur":28859157, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/99z0gb82fny5.o" }}
,{ "pid":12345, "tid":16, "ts":1751353332615580, "dur":173, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.Configuration.cpp" }}
,{ "pid":12345, "tid":16, "ts":1751353332615754, "dur":519, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.Configuration.cpp" }}
,{ "pid":12345, "tid":16, "ts":1751353332616291, "dur":228, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353332615545, "dur":975, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/608olej0nq3l.o" }}
,{ "pid":12345, "tid":16, "ts":1751353332616591, "dur":5419, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__32.cpp" }}
,{ "pid":12345, "tid":16, "ts":1751353332622011, "dur":9038, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__32.cpp" }}
,{ "pid":12345, "tid":16, "ts":1751353332631186, "dur":240, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353332616564, "dur":14862, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/uwo879suz1dd.o" }}
,{ "pid":12345, "tid":16, "ts":1751353332902949, "dur":14599289, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/uwo879suz1dd.o" }}
,{ "pid":12345, "tid":16, "ts":1751353347502502, "dur":1908, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__18.cpp" }}
,{ "pid":12345, "tid":16, "ts":1751353347504411, "dur":2958, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__18.cpp" }}
,{ "pid":12345, "tid":16, "ts":1751353347507385, "dur":186, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353347502467, "dur":5105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1cevn4bdtfhh.o" }}
,{ "pid":12345, "tid":16, "ts":1751353347508378, "dur":12174945, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/1cevn4bdtfhh.o" }}
,{ "pid":12345, "tid":16, "ts":1751353359683483, "dur":74346687, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353291074782, "dur":37490, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353291112280, "dur":3167, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353291115448, "dur":22241, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353291137710, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353291137925, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353291138125, "dur":170, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/ic_launcher_foreground.png" }}
,{ "pid":12345, "tid":17, "ts":1751353291138296, "dur":78, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/ic_launcher_foreground.png" }}
,{ "pid":12345, "tid":17, "ts":1751353291138380, "dur":52, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/ic_launcher_foreground.png" }}
,{ "pid":12345, "tid":17, "ts":1751353291138434, "dur":6815, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353291145251, "dur":504, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/mscorlib-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":17, "ts":1751353291145757, "dur":544, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353291146302, "dur":279, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/mscorlib-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":17, "ts":1751353291146725, "dur":103, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputForUIModule.dll" }}
,{ "pid":12345, "tid":17, "ts":1751353291146907, "dur":374, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.AndroidJNIModule.pdb" }}
,{ "pid":12345, "tid":17, "ts":1751353291147282, "dur":7187, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353291154580, "dur":3029, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__17.cpp" }}
,{ "pid":12345, "tid":17, "ts":1751353291157611, "dur":8996, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__17.cpp" }}
,{ "pid":12345, "tid":17, "ts":1751353291166675, "dur":233, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353291154477, "dur":12431, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/04te3ovepd01.o" }}
,{ "pid":12345, "tid":17, "ts":1751353291167453, "dur":13713088, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/04te3ovepd01.o" }}
,{ "pid":12345, "tid":17, "ts":1751353304880819, "dur":169, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextRenderingModule_CodeGen.c" }}
,{ "pid":12345, "tid":17, "ts":1751353304880989, "dur":288, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextRenderingModule_CodeGen.c" }}
,{ "pid":12345, "tid":17, "ts":1751353304881292, "dur":54, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353304880781, "dur":565, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/r2ko7fgfkwo7.o" }}
,{ "pid":12345, "tid":17, "ts":1751353305687687, "dur":337835, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/r2ko7fgfkwo7.o" }}
,{ "pid":12345, "tid":17, "ts":1751353306025794, "dur":151, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\__Generated.cpp" }}
,{ "pid":12345, "tid":17, "ts":1751353306025946, "dur":473, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\__Generated.cpp" }}
,{ "pid":12345, "tid":17, "ts":1751353306026435, "dur":214, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353306025759, "dur":891, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ezd2p1ujqlmu.o" }}
,{ "pid":12345, "tid":17, "ts":1751353306026741, "dur":130, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.SpriteShapeModule_CodeGen.c" }}
,{ "pid":12345, "tid":17, "ts":1751353306026872, "dur":229, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.SpriteShapeModule_CodeGen.c" }}
,{ "pid":12345, "tid":17, "ts":1751353306026706, "dur":455, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/s9o7svgtrjey.o" }}
,{ "pid":12345, "tid":17, "ts":1751353306027192, "dur":60, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/s9o7svgtrjey.o" }}
,{ "pid":12345, "tid":17, "ts":1751353306027291, "dur":389, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextRenderingModule.cpp" }}
,{ "pid":12345, "tid":17, "ts":1751353306027680, "dur":718, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextRenderingModule.cpp" }}
,{ "pid":12345, "tid":17, "ts":1751353306028415, "dur":202, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353306027254, "dur":1364, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nqp3qp5ad6sw.o" }}
,{ "pid":12345, "tid":17, "ts":1751353306028698, "dur":3184, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule.cpp" }}
,{ "pid":12345, "tid":17, "ts":1751353306031883, "dur":3549, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule.cpp" }}
,{ "pid":12345, "tid":17, "ts":1751353306035449, "dur":209, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353306028669, "dur":6990, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/h4q9bzdazs6h.o" }}
,{ "pid":12345, "tid":17, "ts":1751353306035752, "dur":2572, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__1.cpp" }}
,{ "pid":12345, "tid":17, "ts":1751353306038326, "dur":3391, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__1.cpp" }}
,{ "pid":12345, "tid":17, "ts":1751353306041739, "dur":218, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353306035721, "dur":6236, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/i31sr3433hzj.o" }}
,{ "pid":12345, "tid":17, "ts":1751353306042058, "dur":1397, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__14.cpp" }}
,{ "pid":12345, "tid":17, "ts":1751353306043456, "dur":2045, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__14.cpp" }}
,{ "pid":12345, "tid":17, "ts":1751353306045518, "dur":230, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353306042027, "dur":3722, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2fcbro8s1h8l.o" }}
,{ "pid":12345, "tid":17, "ts":1751353306045841, "dur":629, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.PhysicsModule.cpp" }}
,{ "pid":12345, "tid":17, "ts":1751353306046470, "dur":808, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.PhysicsModule.cpp" }}
,{ "pid":12345, "tid":17, "ts":1751353306047294, "dur":207, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353306045812, "dur":1690, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/rjj1gat48zbs.o" }}
,{ "pid":12345, "tid":17, "ts":1751353306453611, "dur":26115551, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/rjj1gat48zbs.o" }}
,{ "pid":12345, "tid":17, "ts":1751353332569438, "dur":3048, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__1.cpp" }}
,{ "pid":12345, "tid":17, "ts":1751353332572487, "dur":4966, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__1.cpp" }}
,{ "pid":12345, "tid":17, "ts":1751353332577472, "dur":265, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353332569402, "dur":8337, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/34e4vqh3e4xi.o" }}
,{ "pid":12345, "tid":17, "ts":1751353332577844, "dur":1630, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__7.cpp" }}
,{ "pid":12345, "tid":17, "ts":1751353332579475, "dur":2619, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__7.cpp" }}
,{ "pid":12345, "tid":17, "ts":1751353332582114, "dur":258, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353332577807, "dur":4566, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/7rwjuqnsujr3.o" }}
,{ "pid":12345, "tid":17, "ts":1751353332902801, "dur":8263414, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/7rwjuqnsujr3.o" }}
,{ "pid":12345, "tid":17, "ts":1751353341166513, "dur":1727, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__43.cpp" }}
,{ "pid":12345, "tid":17, "ts":1751353341168241, "dur":2399, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__43.cpp" }}
,{ "pid":12345, "tid":17, "ts":1751353341170658, "dur":222, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353341166475, "dur":4406, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/26870cz958tn.o" }}
,{ "pid":12345, "tid":17, "ts":1751353341171347, "dur":12800254, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/26870cz958tn.o" }}
,{ "pid":12345, "tid":17, "ts":1751353353971875, "dur":1546, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__2.cpp" }}
,{ "pid":12345, "tid":17, "ts":1751353353973422, "dur":2403, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__2.cpp" }}
,{ "pid":12345, "tid":17, "ts":1751353353975842, "dur":207, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353353971839, "dur":4211, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4czkg6x1i6ey.o" }}
,{ "pid":12345, "tid":17, "ts":1751353353976152, "dur":1989, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib.cpp" }}
,{ "pid":12345, "tid":17, "ts":1751353353978142, "dur":4259, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib.cpp" }}
,{ "pid":12345, "tid":17, "ts":1751353353982419, "dur":226, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353353976120, "dur":6526, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/878swamkk5lx.o" }}
,{ "pid":12345, "tid":17, "ts":1751353353982743, "dur":2550, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__39.cpp" }}
,{ "pid":12345, "tid":17, "ts":1751353353985294, "dur":3269, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__39.cpp" }}
,{ "pid":12345, "tid":17, "ts":1751353353988580, "dur":200, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353353982711, "dur":6071, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9araomrfnw3s.o" }}
,{ "pid":12345, "tid":17, "ts":1751353353989678, "dur":9347602, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/9araomrfnw3s.o" }}
,{ "pid":12345, "tid":17, "ts":1751353363337420, "dur":70692679, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353291074942, "dur":37371, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353291112317, "dur":2501, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353291114818, "dur":22833, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353291137760, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353291138117, "dur":1856, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"AddBootConfigGUID Library/Bee/artifacts/Android/boot.config" }}
,{ "pid":12345, "tid":18, "ts":1751353291140052, "dur":5107, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353291145161, "dur":995, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.SharedInternalsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":18, "ts":1751353291146252, "dur":101, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextRenderingModule.pdb" }}
,{ "pid":12345, "tid":18, "ts":1751353291146451, "dur":161, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.XRModule.pdb" }}
,{ "pid":12345, "tid":18, "ts":1751353291146616, "dur":50, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputModule.pdb" }}
,{ "pid":12345, "tid":18, "ts":1751353291146909, "dur":363, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextCoreFontEngineModule.pdb" }}
,{ "pid":12345, "tid":18, "ts":1751353291147275, "dur":7216, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353291154538, "dur":156, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.SpriteShapeModule.cpp" }}
,{ "pid":12345, "tid":18, "ts":1751353291154694, "dur":10955, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.SpriteShapeModule.cpp" }}
,{ "pid":12345, "tid":18, "ts":1751353291165684, "dur":301, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353291154492, "dur":11494, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/06e9mmocdc22.o" }}
,{ "pid":12345, "tid":18, "ts":1751353291166109, "dur":510, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.PropertiesModule_CodeGen.c" }}
,{ "pid":12345, "tid":18, "ts":1751353291166620, "dur":823, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.PropertiesModule_CodeGen.c" }}
,{ "pid":12345, "tid":18, "ts":1751353291167465, "dur":56, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353291166056, "dur":1466, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hf0tg52bd15c.o" }}
,{ "pid":12345, "tid":18, "ts":1751353291167974, "dur":642040, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/hf0tg52bd15c.o" }}
,{ "pid":12345, "tid":18, "ts":1751353291810283, "dur":1997, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__7.cpp" }}
,{ "pid":12345, "tid":18, "ts":1751353291812281, "dur":2692, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__7.cpp" }}
,{ "pid":12345, "tid":18, "ts":1751353291814990, "dur":229, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353291810247, "dur":4973, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/unvu3uwlnpzy.o" }}
,{ "pid":12345, "tid":18, "ts":1751353291815303, "dur":137, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UnityWebRequestModule_CodeGen.c" }}
,{ "pid":12345, "tid":18, "ts":1751353291815441, "dur":222, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UnityWebRequestModule_CodeGen.c" }}
,{ "pid":12345, "tid":18, "ts":1751353291815679, "dur":57, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353291815273, "dur":464, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/sym525hss9ha.o" }}
,{ "pid":12345, "tid":18, "ts":1751353291816142, "dur":630715, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/sym525hss9ha.o" }}
,{ "pid":12345, "tid":18, "ts":1751353292447209, "dur":1371, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__12.cpp" }}
,{ "pid":12345, "tid":18, "ts":1751353292448582, "dur":1821, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__12.cpp" }}
,{ "pid":12345, "tid":18, "ts":1751353292450420, "dur":217, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353292447171, "dur":3467, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gso6qzdxurfn.o" }}
,{ "pid":12345, "tid":18, "ts":1751353292450722, "dur":2319, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__57.cpp" }}
,{ "pid":12345, "tid":18, "ts":1751353292453042, "dur":2776, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__57.cpp" }}
,{ "pid":12345, "tid":18, "ts":1751353292455838, "dur":204, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353292450692, "dur":5351, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hkk09deimueg.o" }}
,{ "pid":12345, "tid":18, "ts":1751353292456443, "dur":40215620, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/hkk09deimueg.o" }}
,{ "pid":12345, "tid":18, "ts":1751353332672318, "dur":1783, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__69.cpp" }}
,{ "pid":12345, "tid":18, "ts":1751353332674102, "dur":2020, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__69.cpp" }}
,{ "pid":12345, "tid":18, "ts":1751353332676133, "dur":127, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353332672290, "dur":3971, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/aa8niqyuvp69.o" }}
,{ "pid":12345, "tid":18, "ts":1751353332937070, "dur":13438838, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/aa8niqyuvp69.o" }}
,{ "pid":12345, "tid":18, "ts":1751353346376193, "dur":467, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.InputLegacyModule.cpp" }}
,{ "pid":12345, "tid":18, "ts":1751353346376669, "dur":844, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.InputLegacyModule.cpp" }}
,{ "pid":12345, "tid":18, "ts":1751353346377527, "dur":186, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353346376150, "dur":1564, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/j46zdcerhtj0.o" }}
,{ "pid":12345, "tid":18, "ts":1751353346377791, "dur":2393, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__16.cpp" }}
,{ "pid":12345, "tid":18, "ts":1751353346380184, "dur":3140, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__16.cpp" }}
,{ "pid":12345, "tid":18, "ts":1751353346383341, "dur":212, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353346377764, "dur":5790, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/fpqqnfhhmpoi.o" }}
,{ "pid":12345, "tid":18, "ts":1751353346384003, "dur":11968312, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/fpqqnfhhmpoi.o" }}
,{ "pid":12345, "tid":18, "ts":1751353358352534, "dur":75677729, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353291074868, "dur":37428, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353291112304, "dur":2581, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353291114886, "dur":22781, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353291137729, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353291137852, "dur":51, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt_z4m3.info" }}
,{ "pid":12345, "tid":19, "ts":1751353291137905, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353291137970, "dur":340, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.UnityAnalyticsCommonModule-FeaturesChecked.txt_z4m3.info" }}
,{ "pid":12345, "tid":19, "ts":1751353291138314, "dur":68, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values-v31/styles.xml" }}
,{ "pid":12345, "tid":19, "ts":1751353291138401, "dur":6722, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353291145128, "dur":186, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ClassRegistrationGenerator D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/Android/il2cppOutput/UnityClassRegistration.cpp" }}
,{ "pid":12345, "tid":19, "ts":1751353291145315, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353291145521, "dur":156, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.PhysicsModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":19, "ts":1751353291145763, "dur":151, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.InputModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":19, "ts":1751353291145915, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353291146107, "dur":489, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353291146729, "dur":75, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.PropertiesModule.pdb" }}
,{ "pid":12345, "tid":19, "ts":1751353291146805, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353291147030, "dur":69, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UnityWebRequestModule.pdb" }}
,{ "pid":12345, "tid":19, "ts":1751353291147150, "dur":7312, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353291154527, "dur":2840, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__10.cpp" }}
,{ "pid":12345, "tid":19, "ts":1751353291157369, "dur":8433, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__10.cpp" }}
,{ "pid":12345, "tid":19, "ts":1751353291165810, "dur":69, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\NDK\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++" }}
,{ "pid":12345, "tid":19, "ts":1751353291165891, "dur":279, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353291154472, "dur":11699, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bz694xc5ywuw.o" }}
,{ "pid":12345, "tid":19, "ts":1751353291166591, "dur":1898, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__50.cpp" }}
,{ "pid":12345, "tid":19, "ts":1751353291168490, "dur":3052, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__50.cpp" }}
,{ "pid":12345, "tid":19, "ts":1751353291171562, "dur":230, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353291166549, "dur":5243, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/25hyt1fxyzo0.o" }}
,{ "pid":12345, "tid":19, "ts":1751353291172236, "dur":11929634, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/25hyt1fxyzo0.o" }}
,{ "pid":12345, "tid":19, "ts":1751353303102166, "dur":1810, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__65.cpp" }}
,{ "pid":12345, "tid":19, "ts":1751353303103977, "dur":2309, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__65.cpp" }}
,{ "pid":12345, "tid":19, "ts":1751353303106305, "dur":230, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353303102128, "dur":4408, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/2yweic92h9kc.o" }}
,{ "pid":12345, "tid":19, "ts":1751353303107039, "dur":29506243, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/2yweic92h9kc.o" }}
,{ "pid":12345, "tid":19, "ts":1751353332613726, "dur":310, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreFontEngineModule_CodeGen.c" }}
,{ "pid":12345, "tid":19, "ts":1751353332614037, "dur":671, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreFontEngineModule_CodeGen.c" }}
,{ "pid":12345, "tid":19, "ts":1751353332614760, "dur":167, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353332613658, "dur":1270, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/zl6xdwmh20f6.o" }}
,{ "pid":12345, "tid":19, "ts":1751353332935403, "dur":474611, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/zl6xdwmh20f6.o" }}
,{ "pid":12345, "tid":19, "ts":1751353333410289, "dur":570, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.IMGUIModule__1.cpp" }}
,{ "pid":12345, "tid":19, "ts":1751353333410860, "dur":839, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.IMGUIModule__1.cpp" }}
,{ "pid":12345, "tid":19, "ts":1751353333411714, "dur":213, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353333410252, "dur":1676, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kdkmybhldeiu.o" }}
,{ "pid":12345, "tid":19, "ts":1751353333412000, "dur":2774, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__67.cpp" }}
,{ "pid":12345, "tid":19, "ts":1751353333414775, "dur":2948, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__67.cpp" }}
,{ "pid":12345, "tid":19, "ts":1751353333417739, "dur":208, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353333411973, "dur":5976, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/omkji13yozdb.o" }}
,{ "pid":12345, "tid":19, "ts":1751353333559884, "dur":23734320, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/omkji13yozdb.o" }}
,{ "pid":12345, "tid":19, "ts":1751353357294453, "dur":76735594, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353291074994, "dur":37333, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353291112332, "dur":2480, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353291114813, "dur":22857, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353291137738, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353291137998, "dur":380, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.SpriteShapeModule-FeaturesChecked.txt_zrrh.info" }}
,{ "pid":12345, "tid":20, "ts":1751353291138395, "dur":70, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/RuntimeInitializeOnLoads.json" }}
,{ "pid":12345, "tid":20, "ts":1751353291138484, "dur":6684, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353291145180, "dur":467, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.AnimationModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":20, "ts":1751353291145815, "dur":262, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.VideoModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":20, "ts":1751353291146078, "dur":105, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353291146253, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353291146308, "dur":157, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TerrainPhysicsModule.pdb" }}
,{ "pid":12345, "tid":20, "ts":1751353291146499, "dur":252, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":20, "ts":1751353291146908, "dur":427, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.GridModule.pdb" }}
,{ "pid":12345, "tid":20, "ts":1751353291147336, "dur":7141, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353291154536, "dur":486584, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule__1.cpp" }}
,{ "pid":12345, "tid":20, "ts":1751353291641122, "dur":208500, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule__1.cpp" }}
,{ "pid":12345, "tid":20, "ts":1751353291849640, "dur":229, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353291154487, "dur":695383, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/egfpd4jb3rvv.o" }}
,{ "pid":12345, "tid":20, "ts":1751353291849967, "dur":138, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppMetadataRegistration.c" }}
,{ "pid":12345, "tid":20, "ts":1751353291850106, "dur":226, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppMetadataRegistration.c" }}
,{ "pid":12345, "tid":20, "ts":1751353291850347, "dur":58, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353291849935, "dur":470, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/36ou4anq6mij.o" }}
,{ "pid":12345, "tid":20, "ts":1751353291850860, "dur":603458, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/36ou4anq6mij.o" }}
,{ "pid":12345, "tid":20, "ts":1751353292454604, "dur":2427, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__17.cpp" }}
,{ "pid":12345, "tid":20, "ts":1751353292457033, "dur":2494, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__17.cpp" }}
,{ "pid":12345, "tid":20, "ts":1751353292459542, "dur":205, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353292454570, "dur":5179, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/77qu6x31cp75.o" }}
,{ "pid":12345, "tid":20, "ts":1751353292459840, "dur":4232, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppGenericInstDefinitions.c" }}
,{ "pid":12345, "tid":20, "ts":1751353292464073, "dur":3176, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppGenericInstDefinitions.c" }}
,{ "pid":12345, "tid":20, "ts":1751353292467267, "dur":52, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353292459809, "dur":7511, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/yac4a4p3l7bk.o" }}
,{ "pid":12345, "tid":20, "ts":1751353292467767, "dur":1920142, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/yac4a4p3l7bk.o" }}
,{ "pid":12345, "tid":20, "ts":1751353294388201, "dur":536, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__11.cpp" }}
,{ "pid":12345, "tid":20, "ts":1751353294388739, "dur":1152, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__11.cpp" }}
,{ "pid":12345, "tid":20, "ts":1751353294389907, "dur":208, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353294388164, "dur":1952, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vrairmxpxc62.o" }}
,{ "pid":12345, "tid":20, "ts":1751353294390199, "dur":130, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.cpp" }}
,{ "pid":12345, "tid":20, "ts":1751353294390330, "dur":400, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.cpp" }}
,{ "pid":12345, "tid":20, "ts":1751353294390759, "dur":187, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353294390170, "dur":777, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ulhaqys58uf2.o" }}
,{ "pid":12345, "tid":20, "ts":1751353294391025, "dur":125, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.VRModule_CodeGen.c" }}
,{ "pid":12345, "tid":20, "ts":1751353294391150, "dur":212, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.VRModule_CodeGen.c" }}
,{ "pid":12345, "tid":20, "ts":1751353294390994, "dur":431, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/m41fgba204hj.o" }}
,{ "pid":12345, "tid":20, "ts":1751353294391855, "dur":1796502, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/m41fgba204hj.o" }}
,{ "pid":12345, "tid":20, "ts":1751353296188672, "dur":2350, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__68.cpp" }}
,{ "pid":12345, "tid":20, "ts":1751353296191023, "dur":2840, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__68.cpp" }}
,{ "pid":12345, "tid":20, "ts":1751353296193878, "dur":222, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353296188636, "dur":5465, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/pu4z2gkxs1uq.o" }}
,{ "pid":12345, "tid":20, "ts":1751353297269323, "dur":35342952, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/pu4z2gkxs1uq.o" }}
,{ "pid":12345, "tid":20, "ts":1751353332612550, "dur":691, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem.ForUI.cpp" }}
,{ "pid":12345, "tid":20, "ts":1751353332613242, "dur":1101, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem.ForUI.cpp" }}
,{ "pid":12345, "tid":20, "ts":1751353332614369, "dur":243, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353332612514, "dur":2099, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/yms2p2c88w6x.o" }}
,{ "pid":12345, "tid":20, "ts":1751353332614771, "dur":2645, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__10.cpp" }}
,{ "pid":12345, "tid":20, "ts":1751353332617417, "dur":4107, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__10.cpp" }}
,{ "pid":12345, "tid":20, "ts":1751353332621540, "dur":220, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353332614729, "dur":7032, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/u401qaj1n1mo.o" }}
,{ "pid":12345, "tid":20, "ts":1751353332621880, "dur":2864, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__8.cpp" }}
,{ "pid":12345, "tid":20, "ts":1751353332624745, "dur":3974, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__8.cpp" }}
,{ "pid":12345, "tid":20, "ts":1751353332628736, "dur":234, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353332621843, "dur":7128, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ijh6b3fo2yny.o" }}
,{ "pid":12345, "tid":20, "ts":1751353332629058, "dur":2668, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__66.cpp" }}
,{ "pid":12345, "tid":20, "ts":1751353332631728, "dur":3193, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__66.cpp" }}
,{ "pid":12345, "tid":20, "ts":1751353332634940, "dur":239, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353332629033, "dur":6146, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/36gv2rp1lb2r.o" }}
,{ "pid":12345, "tid":20, "ts":1751353332936276, "dur":16112914, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/36gv2rp1lb2r.o" }}
,{ "pid":12345, "tid":20, "ts":1751353349049469, "dur":2320, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppUnresolvedIndirectCallStubs.cpp" }}
,{ "pid":12345, "tid":20, "ts":1751353349051790, "dur":2643, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppUnresolvedIndirectCallStubs.cpp" }}
,{ "pid":12345, "tid":20, "ts":1751353349054449, "dur":218, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353349049432, "dur":5236, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v0t4ioo2g7hl.o" }}
,{ "pid":12345, "tid":20, "ts":1751353349136253, "dur":12924934, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/v0t4ioo2g7hl.o" }}
,{ "pid":12345, "tid":20, "ts":1751353362061351, "dur":71968939, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353291075039, "dur":37312, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353291112355, "dur":2413, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353291116173, "dur":586, "ph":"X", "name": "File",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Variations\\il2cpp\\Managed\\UnityEngine.AssetBundleModule.dll" }}
,{ "pid":12345, "tid":21, "ts":1751353291114769, "dur":2786, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353291117555, "dur":20173, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353291137765, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353291137884, "dur":660, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AIModule-FeaturesChecked.txt_jr17.info" }}
,{ "pid":12345, "tid":21, "ts":1751353291138545, "dur":6627, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353291145174, "dur":960, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/System.Xml-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":21, "ts":1751353291146239, "dur":413, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputLegacyModule.dll" }}
,{ "pid":12345, "tid":21, "ts":1751353291146690, "dur":184, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputModule.dll" }}
,{ "pid":12345, "tid":21, "ts":1751353291146917, "dur":265, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.TextCoreFontEngineModule.dll" }}
,{ "pid":12345, "tid":21, "ts":1751353291147183, "dur":7313, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353291154549, "dur":3816, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__20.cpp" }}
,{ "pid":12345, "tid":21, "ts":1751353291158367, "dur":9095, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__20.cpp" }}
,{ "pid":12345, "tid":21, "ts":1751353291167482, "dur":211, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353291154498, "dur":13196, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/160kzx8hq6lj.o" }}
,{ "pid":12345, "tid":21, "ts":1751353291168132, "dur":13143004, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/160kzx8hq6lj.o" }}
,{ "pid":12345, "tid":21, "ts":1751353304311369, "dur":2657, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__56.cpp" }}
,{ "pid":12345, "tid":21, "ts":1751353304314027, "dur":3083, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__56.cpp" }}
,{ "pid":12345, "tid":21, "ts":1751353304317127, "dur":227, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353304311335, "dur":6020, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/w4gcu4zkj62w.o" }}
,{ "pid":12345, "tid":21, "ts":1751353304317792, "dur":28335841, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/w4gcu4zkj62w.o" }}
,{ "pid":12345, "tid":21, "ts":1751353332653856, "dur":139, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem.ForUI_CodeGen.c" }}
,{ "pid":12345, "tid":21, "ts":1751353332653995, "dur":322, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem.ForUI_CodeGen.c" }}
,{ "pid":12345, "tid":21, "ts":1751353332653826, "dur":538, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/myajzagnq8e9.o" }}
,{ "pid":12345, "tid":21, "ts":1751353332654699, "dur":309639, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/myajzagnq8e9.o" }}
,{ "pid":12345, "tid":21, "ts":1751353332964576, "dur":3009, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__9.cpp" }}
,{ "pid":12345, "tid":21, "ts":1751353332967587, "dur":3750, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__9.cpp" }}
,{ "pid":12345, "tid":21, "ts":1751353332971357, "dur":227, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353332964542, "dur":7043, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gbjz3jrhufsx.o" }}
,{ "pid":12345, "tid":21, "ts":1751353332971684, "dur":2194, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__5.cpp" }}
,{ "pid":12345, "tid":21, "ts":1751353332973879, "dur":4670, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__5.cpp" }}
,{ "pid":12345, "tid":21, "ts":1751353332978569, "dur":247, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353332971653, "dur":7164, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/bgqdw46uskjg.o" }}
,{ "pid":12345, "tid":21, "ts":1751353332978896, "dur":2662, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCCalculateFieldValues1.cpp" }}
,{ "pid":12345, "tid":21, "ts":1751353332981559, "dur":2892, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCCalculateFieldValues1.cpp" }}
,{ "pid":12345, "tid":21, "ts":1751353332984468, "dur":217, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353332978868, "dur":5818, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/v8iwhyj1c5oc.o" }}
,{ "pid":12345, "tid":21, "ts":1751353333351740, "dur":2826340, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/v8iwhyj1c5oc.o" }}
,{ "pid":12345, "tid":21, "ts":1751353336178359, "dur":2116, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__11.cpp" }}
,{ "pid":12345, "tid":21, "ts":1751353336180476, "dur":2574, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__11.cpp" }}
,{ "pid":12345, "tid":21, "ts":1751353336183068, "dur":201, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353336178324, "dur":4946, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/weayk7q21k6f.o" }}
,{ "pid":12345, "tid":21, "ts":1751353336183381, "dur":169, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.VideoModule_CodeGen.c" }}
,{ "pid":12345, "tid":21, "ts":1751353336183556, "dur":235, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.VideoModule_CodeGen.c" }}
,{ "pid":12345, "tid":21, "ts":1751353336183339, "dur":510, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/r1w5t531a50x.o" }}
,{ "pid":12345, "tid":21, "ts":1751353336184302, "dur":412240, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/r1w5t531a50x.o" }}
,{ "pid":12345, "tid":21, "ts":1751353336596869, "dur":2093, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__74.cpp" }}
,{ "pid":12345, "tid":21, "ts":1751353336598963, "dur":2654, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__74.cpp" }}
,{ "pid":12345, "tid":21, "ts":1751353336601636, "dur":241, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353336596830, "dur":5048, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/olt9aqkmhq8n.o" }}
,{ "pid":12345, "tid":21, "ts":1751353336618403, "dur":9934822, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/olt9aqkmhq8n.o" }}
,{ "pid":12345, "tid":21, "ts":1751353346553479, "dur":158, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AIModule_CodeGen.c" }}
,{ "pid":12345, "tid":21, "ts":1751353346553637, "dur":271, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AIModule_CodeGen.c" }}
,{ "pid":12345, "tid":21, "ts":1751353346553921, "dur":84, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353346553444, "dur":562, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/upsfj2k4spt9.o" }}
,{ "pid":12345, "tid":21, "ts":1751353346554397, "dur":438945, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/upsfj2k4spt9.o" }}
,{ "pid":12345, "tid":21, "ts":1751353346993608, "dur":3340, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCCalculateFieldValues.cpp" }}
,{ "pid":12345, "tid":21, "ts":1751353346996949, "dur":3781, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCCalculateFieldValues.cpp" }}
,{ "pid":12345, "tid":21, "ts":1751353347000747, "dur":223, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353346993568, "dur":7404, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/cpp1rpcsno71.o" }}
,{ "pid":12345, "tid":21, "ts":1751353347001064, "dur":1413, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppGenericClassTable.c" }}
,{ "pid":12345, "tid":21, "ts":1751353347002477, "dur":776, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppGenericClassTable.c" }}
,{ "pid":12345, "tid":21, "ts":1751353347003268, "dur":51, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353347001032, "dur":2288, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4zjw4q9v68e6.o" }}
,{ "pid":12345, "tid":21, "ts":1751353347003756, "dur":496462, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/4zjw4q9v68e6.o" }}
,{ "pid":12345, "tid":21, "ts":1751353347500492, "dur":3001, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics.cpp" }}
,{ "pid":12345, "tid":21, "ts":1751353347503494, "dur":3710, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics.cpp" }}
,{ "pid":12345, "tid":21, "ts":1751353347507219, "dur":253, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353347500457, "dur":7016, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vomfw8rdeo41.o" }}
,{ "pid":12345, "tid":21, "ts":1751353347507574, "dur":1734, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__49.cpp" }}
,{ "pid":12345, "tid":21, "ts":1751353347509309, "dur":2531, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__49.cpp" }}
,{ "pid":12345, "tid":21, "ts":1751353347511857, "dur":214, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353347507539, "dur":4533, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/gdrtw9gwp12n.o" }}
,{ "pid":12345, "tid":21, "ts":1751353347512483, "dur":11530949, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/gdrtw9gwp12n.o" }}
,{ "pid":12345, "tid":21, "ts":1751353359043633, "dur":74986474, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353291075084, "dur":37278, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353291112367, "dur":2919, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353291115286, "dur":22439, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353291137729, "dur":56, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AndroidJNIModule-FeaturesChecked.txt_x3ln.info" }}
,{ "pid":12345, "tid":22, "ts":1751353291137785, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353291137912, "dur":181, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AndroidJNIModule-FeaturesChecked.txt_x3ln.info" }}
,{ "pid":12345, "tid":22, "ts":1751353291138095, "dur":81, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/jniLibs/arm64-v8a/libmain.so" }}
,{ "pid":12345, "tid":22, "ts":1751353291138217, "dur":101, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/strings.xml" }}
,{ "pid":12345, "tid":22, "ts":1751353291138319, "dur":76, "ph":"X", "name": "CheckPristineOutputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/res/values/strings.xml" }}
,{ "pid":12345, "tid":22, "ts":1751353291138407, "dur":6730, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353291145142, "dur":240, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":22, "ts":1751353291145383, "dur":141, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353291145576, "dur":163, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.UI-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":22, "ts":1751353291145818, "dur":277, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.IMGUIModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":22, "ts":1751353291146096, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353291146457, "dur":243, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UI.pdb" }}
,{ "pid":12345, "tid":22, "ts":1751353291146777, "dur":289, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.UIElementsModule.dll" }}
,{ "pid":12345, "tid":22, "ts":1751353291147068, "dur":7405, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353291154508, "dur":2531, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__3.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353291157040, "dur":6480, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__3.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353291163542, "dur":254, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353291154474, "dur":9323, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ps28vfczluqh.o" }}
,{ "pid":12345, "tid":22, "ts":1751353291164301, "dur":4238, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353291168540, "dur":5257, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353291173814, "dur":56, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353291164245, "dur":9626, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9up4h199he7v.o" }}
,{ "pid":12345, "tid":22, "ts":1751353291180685, "dur":1846741, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/9up4h199he7v.o" }}
,{ "pid":12345, "tid":22, "ts":1751353293027712, "dur":142, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.GridModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353293027854, "dur":251, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.GridModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353293028121, "dur":70, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353293027675, "dur":517, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kvewo88f22kk.o" }}
,{ "pid":12345, "tid":22, "ts":1751353293028274, "dur":1148, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__19.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353293029423, "dur":1163, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__19.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353293030600, "dur":192, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353293028241, "dur":2552, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/d3x61gwwi1u8.o" }}
,{ "pid":12345, "tid":22, "ts":1751353293030876, "dur":1586, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__48.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353293032463, "dur":2102, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__48.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353293034580, "dur":198, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353293030845, "dur":3934, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vxn5g02ymdr4.o" }}
,{ "pid":12345, "tid":22, "ts":1751353293035277, "dur":11252354, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/vxn5g02ymdr4.o" }}
,{ "pid":12345, "tid":22, "ts":1751353304287908, "dur":161, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.Xml_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353304288070, "dur":259, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System.Xml_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353304288346, "dur":66, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353304287871, "dur":542, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/nyi813yiv8kp.o" }}
,{ "pid":12345, "tid":22, "ts":1751353304288882, "dur":755772, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/nyi813yiv8kp.o" }}
,{ "pid":12345, "tid":22, "ts":1751353305044971, "dur":174, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.SharedInternalsModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353305045146, "dur":342, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.SharedInternalsModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353305045503, "dur":54, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353305044928, "dur":629, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/x1eyvuv02wa1.o" }}
,{ "pid":12345, "tid":22, "ts":1751353306025476, "dur":54, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353305688026, "dur":337520, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/x1eyvuv02wa1.o" }}
,{ "pid":12345, "tid":22, "ts":1751353306025781, "dur":1509, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__71.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353306027291, "dur":2222, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__71.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353306029540, "dur":251, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353306025750, "dur":4042, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/hi2gvrbt7b45.o" }}
,{ "pid":12345, "tid":22, "ts":1751353306403860, "dur":26217992, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/hi2gvrbt7b45.o" }}
,{ "pid":12345, "tid":22, "ts":1751353332622229, "dur":1084, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCCTypeValuesTable.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353332623314, "dur":742, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCCTypeValuesTable.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353332624076, "dur":200, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353332622177, "dur":2100, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/db50b1bh2cpv.o" }}
,{ "pid":12345, "tid":22, "ts":1751353332624781, "dur":1650565, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/db50b1bh2cpv.o" }}
,{ "pid":12345, "tid":22, "ts":1751353334275616, "dur":1360, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__17.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353334276977, "dur":2047, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__17.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353334279041, "dur":204, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353334275580, "dur":3665, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9i336mhmbwc4.o" }}
,{ "pid":12345, "tid":22, "ts":1751353334493427, "dur":9923214, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/9i336mhmbwc4.o" }}
,{ "pid":12345, "tid":22, "ts":1751353344416921, "dur":2964, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__10.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353344419886, "dur":3691, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__10.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353344423595, "dur":210, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353344416885, "dur":6921, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/stosql5gpuhq.o" }}
,{ "pid":12345, "tid":22, "ts":1751353344423911, "dur":2969, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__5.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353344426881, "dur":3359, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__5.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353344430257, "dur":222, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353344423877, "dur":6603, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1wp7y7uxo730.o" }}
,{ "pid":12345, "tid":22, "ts":1751353344431351, "dur":11516342, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/1wp7y7uxo730.o" }}
,{ "pid":12345, "tid":22, "ts":1751353355947963, "dur":1024, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextCoreFontEngineModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353355949024, "dur":878, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextCoreTextEngineModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353355949938, "dur":762, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.SharedInternalsModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353355950736, "dur":909, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UnityWebRequestModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353355951817, "dur":8136, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppGenericMethodPointerTable.c" }}
,{ "pid":12345, "tid":22, "ts":1751353355960045, "dur":2544, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppGenericAdjustorThunkTable.c" }}
,{ "pid":12345, "tid":22, "ts":1751353355962675, "dur":935, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Unity.InputSystem.ForUI_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353355963645, "dur":878, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AudioModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353355964561, "dur":841, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.VideoModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353355965450, "dur":841, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.InputModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353355966358, "dur":968, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.IMGUIModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353355967444, "dur":855, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UnityAnalyticsCommonModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353355968413, "dur":1942, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppUnresolvedIndirectCallStubs.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353355970404, "dur":908, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.Physics2DModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353355971355, "dur":869, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AnimationModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353355972262, "dur":935, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.InputForUIModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353355973236, "dur":995, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AndroidJNIModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353355974308, "dur":1033, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.PropertiesModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353355975381, "dur":860, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.SubsystemsModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353355976279, "dur":2408, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIElementsModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353355978868, "dur":1191, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/Android/il2cppOutput/build/arm64-v8a/Data/Resources/mscorlib.dll-resources.dat" }}
,{ "pid":12345, "tid":22, "ts":1751353355980305, "dur":1040, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TerrainPhysicsModule.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353355981577, "dur":1769, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/GenericMethods__20.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353355983389, "dur":985, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.InputLegacyModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353355984430, "dur":935, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppMetadataRegistration.c" }}
,{ "pid":12345, "tid":22, "ts":1751353355985432, "dur":1087, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCFieldValuesTable.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353355986654, "dur":4456, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/Managed/Metadata/global-metadata.dat" }}
,{ "pid":12345, "tid":22, "ts":1751353355991204, "dur":4183, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/Android/il2cppOutput/build/arm64-v8a/Data/Metadata/global-metadata.dat" }}
,{ "pid":12345, "tid":22, "ts":1751353355995546, "dur":2317, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCalculateFieldValues1.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353355997980, "dur":1879, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppGenericMethodDefinitions.c" }}
,{ "pid":12345, "tid":22, "ts":1751353355999901, "dur":1345, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.CoreModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353356001410, "dur":1245, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/Managed/Resources/mscorlib.dll-resources.dat" }}
,{ "pid":12345, "tid":22, "ts":1751353356002723, "dur":3335, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCalculateTypeValues.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353356006169, "dur":976, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.UIModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353356007183, "dur":928, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/System.Configuration_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353356008148, "dur":857, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.XRModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353356009217, "dur":886, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.VRModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353356010152, "dur":870, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AIModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353356011072, "dur":2716, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppGenericInstDefinitions.c" }}
,{ "pid":12345, "tid":22, "ts":1751353356013842, "dur":1712, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TextRenderingModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353356015648, "dur":991, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.JSONSerializeModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353356016678, "dur":912, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.HierarchyCoreModule_CodeGen.c" }}
,{ "pid":12345, "tid":22, "ts":1751353356017630, "dur":16462, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.AnimationModule.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353356034145, "dur":1065, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/Il2CppCCalculateTypeValues1.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353356035364, "dur":1082, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.TerrainModule.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353356036515, "dur":977, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.PhysicsModule.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353356037527, "dur":1810, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.CoreModule__1.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353356039376, "dur":1934, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/il2cppOutput/UnityEngine.CoreModule__2.cpp" }}
,{ "pid":12345, "tid":22, "ts":1751353356041313, "dur":76901704, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353432943022, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/Android/il2cppOutput/build/arm64-v8a/libil2cpp.dbg.so" }}
,{ "pid":12345, "tid":22, "ts":1751353432943133, "dur":202520, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/Android/il2cppOutput/build/arm64-v8a/libil2cpp.dbg.so" }}
,{ "pid":12345, "tid":22, "ts":1751353433145661, "dur":884394, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353291075144, "dur":37228, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353291112377, "dur":1877, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353291114255, "dur":1604, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353291115859, "dur":21862, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353291137722, "dur":90, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.AnimationModule-FeaturesChecked.txt_3sdh.info" }}
,{ "pid":12345, "tid":23, "ts":1751353291137945, "dur":190, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/csharpactions/UnityEngine.TilemapModule-FeaturesChecked.txt_xct4.info" }}
,{ "pid":12345, "tid":23, "ts":1751353291138138, "dur":132, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/launcher/src/main/res/mipmap-mdpi/ic_launcher_background.png" }}
,{ "pid":12345, "tid":23, "ts":1751353291138423, "dur":212, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/assets/bin/Data/resources.resource" }}
,{ "pid":12345, "tid":23, "ts":1751353291138636, "dur":6497, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353291145137, "dur":1508, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/System.Configuration-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":23, "ts":1751353291146647, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353291146703, "dur":310, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/System.Configuration-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":23, "ts":1751353291147075, "dur":7434, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353291154562, "dur":7016, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__4.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353291161587, "dur":8241, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__4.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353291169848, "dur":200, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353291154510, "dur":15539, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0o5qesezk5ml.o" }}
,{ "pid":12345, "tid":23, "ts":1751353291170651, "dur":11938729, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/0o5qesezk5ml.o" }}
,{ "pid":12345, "tid":23, "ts":1751353303109658, "dur":170, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AnimationModule_CodeGen.c" }}
,{ "pid":12345, "tid":23, "ts":1751353303109828, "dur":295, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.AnimationModule_CodeGen.c" }}
,{ "pid":12345, "tid":23, "ts":1751353303110137, "dur":56, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353303109623, "dur":571, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/kldp9czb34bf.o" }}
,{ "pid":12345, "tid":23, "ts":1751353303285708, "dur":307347, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/kldp9czb34bf.o" }}
,{ "pid":12345, "tid":23, "ts":1751353303593293, "dur":2596, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__5.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353303595890, "dur":3966, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__5.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353303599874, "dur":215, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353303593261, "dur":6829, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/qed2rhuvhykh.o" }}
,{ "pid":12345, "tid":23, "ts":1751353303600092, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353303600207, "dur":1789, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__9.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353303601997, "dur":2629, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__9.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353303604643, "dur":210, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353303600173, "dur":4681, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/0dcuv44u1v7h.o" }}
,{ "pid":12345, "tid":23, "ts":1751353303604950, "dur":172, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIModule_CodeGen.c" }}
,{ "pid":12345, "tid":23, "ts":1751353303605123, "dur":295, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIModule_CodeGen.c" }}
,{ "pid":12345, "tid":23, "ts":1751353303604918, "dur":565, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mifvszs7cfcd.o" }}
,{ "pid":12345, "tid":23, "ts":1751353303606101, "dur":625131, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/mifvszs7cfcd.o" }}
,{ "pid":12345, "tid":23, "ts":1751353304231512, "dur":2097, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__55.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353304233610, "dur":2923, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__55.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353304236555, "dur":221, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353304231475, "dur":5302, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vwwv9ojmf4lh.o" }}
,{ "pid":12345, "tid":23, "ts":1751353304237431, "dur":28393352, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/vwwv9ojmf4lh.o" }}
,{ "pid":12345, "tid":23, "ts":1751353332631123, "dur":2934, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__4.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353332634058, "dur":4859, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__4.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353332638935, "dur":235, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353332631078, "dur":8092, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a85wbpqwoi20.o" }}
,{ "pid":12345, "tid":23, "ts":1751353332639287, "dur":2102, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__7.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353332641396, "dur":2357, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__7.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353332643763, "dur":116, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353332639250, "dur":4629, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/w9jen0mqna8j.o" }}
,{ "pid":12345, "tid":23, "ts":1751353332643952, "dur":141, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCCalculateTypeValues1.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353332644093, "dur":443, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Il2CppCCalculateTypeValues1.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353332644550, "dur":176, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353332643916, "dur":810, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/l5t0o7id251b.o" }}
,{ "pid":12345, "tid":23, "ts":1751353332645083, "dur":1557230, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/l5t0o7id251b.o" }}
,{ "pid":12345, "tid":23, "ts":1751353334202697, "dur":4233, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__5.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353334206932, "dur":3345, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__5.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353334210294, "dur":202, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353334202640, "dur":7857, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8z7p351pgz6z.o" }}
,{ "pid":12345, "tid":23, "ts":1751353334210590, "dur":1041, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.InputForUIModule.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353334211633, "dur":1356, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.InputForUIModule.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353334213004, "dur":189, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353334210557, "dur":2637, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/xqgs9zqegqf7.o" }}
,{ "pid":12345, "tid":23, "ts":1751353334213275, "dur":1561, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__31.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353334214837, "dur":2504, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__31.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353334217358, "dur":230, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353334213245, "dur":4343, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/9glks0exwd2t.o" }}
,{ "pid":12345, "tid":23, "ts":1751353334218067, "dur":9394326, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/9glks0exwd2t.o" }}
,{ "pid":12345, "tid":23, "ts":1751353343612710, "dur":208, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TerrainPhysicsModule_CodeGen.c" }}
,{ "pid":12345, "tid":23, "ts":1751353343612919, "dur":253, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TerrainPhysicsModule_CodeGen.c" }}
,{ "pid":12345, "tid":23, "ts":1751353343613187, "dur":52, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353343612660, "dur":580, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/s8wzl21r6wdv.o" }}
,{ "pid":12345, "tid":23, "ts":1751353343613339, "dur":557601, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/s8wzl21r6wdv.o" }}
,{ "pid":12345, "tid":23, "ts":1751353344171270, "dur":1468, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System__2.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353344172739, "dur":2375, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System__2.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353344175131, "dur":217, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353344171229, "dur":4120, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/qxqd40q4j6kr.o" }}
,{ "pid":12345, "tid":23, "ts":1751353344175442, "dur":2104, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__11.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353344177548, "dur":2781, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__11.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353344180353, "dur":212, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353344175409, "dur":5157, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/vclibqzzr333.o" }}
,{ "pid":12345, "tid":23, "ts":1751353344180654, "dur":2386, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__13.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353344183041, "dur":5783, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\mscorlib__13.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353344188841, "dur":223, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353344180625, "dur":8440, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/i2x2kwly8aze.o" }}
,{ "pid":12345, "tid":23, "ts":1751353344189171, "dur":2723, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__3.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353344191895, "dur":3455, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIElementsModule__3.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353344195371, "dur":235, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353344189125, "dur":6482, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/4qpxrszza8ri.o" }}
,{ "pid":12345, "tid":23, "ts":1751353344195707, "dur":2510, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__8.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353344198224, "dur":3417, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__8.cpp" }}
,{ "pid":12345, "tid":23, "ts":1751353344201658, "dur":280, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353344195675, "dur":6264, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/8zg0e63qyl8i.o" }}
,{ "pid":12345, "tid":23, "ts":1751353344203170, "dur":10785534, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/8zg0e63qyl8i.o" }}
,{ "pid":12345, "tid":23, "ts":1751353354989019, "dur":414, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule_CodeGen.c" }}
,{ "pid":12345, "tid":23, "ts":1751353354989434, "dur":473, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.TextCoreTextEngineModule_CodeGen.c" }}
,{ "pid":12345, "tid":23, "ts":1751353354989928, "dur":55, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353354988973, "dur":1011, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tyrz3lym5pkq.o" }}
,{ "pid":12345, "tid":23, "ts":1751353354990426, "dur":520229, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/tyrz3lym5pkq.o" }}
,{ "pid":12345, "tid":23, "ts":1751353355510954, "dur":2270, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem_CodeGen.c" }}
,{ "pid":12345, "tid":23, "ts":1751353355513226, "dur":2141, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem_CodeGen.c" }}
,{ "pid":12345, "tid":23, "ts":1751353355515384, "dur":54, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353355510916, "dur":4530, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/75ghpk6z7ukt.o" }}
,{ "pid":12345, "tid":23, "ts":1751353355515996, "dur":1247120, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/75ghpk6z7ukt.o" }}
,{ "pid":12345, "tid":23, "ts":1751353356763364, "dur":77242930, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353434006356, "dur":23557, "ph":"X", "name": "CopyFiles",  "args": { "detail":"D:/My Project/Driving Simulator Game Z TEC/Library/Bee/artifacts/Android/il2cppOutput/build/arm64-v8a/libil2cpp.so" }}
,{ "pid":12345, "tid":23, "ts":1751353434029918, "dur":126, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353291075192, "dur":37227, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353291112419, "dur":2449, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353291114869, "dur":22795, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353291137666, "dur":74, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteResponseFile Library/Bee/artifacts/rsp/12438370367218343051.rsp" }}
,{ "pid":12345, "tid":24, "ts":1751353291138696, "dur":463, "ph":"X", "name": "WriteResponseFile",  "args": { "detail":"Library/Bee/artifacts/rsp/12438370367218343051.rsp" }}
,{ "pid":12345, "tid":24, "ts":1751353291139161, "dur":5992, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353291145154, "dur":215, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/UnityEngine.GridModule-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":24, "ts":1751353291145370, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353291145481, "dur":238, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"ExtractUsedFeatures Library/Bee/artifacts/Android/Features/Unity.InputSystem-FeaturesChecked.txt" }}
,{ "pid":12345, "tid":24, "ts":1751353291145720, "dur":256, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353291146297, "dur":88, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.InputLegacyModule.pdb" }}
,{ "pid":12345, "tid":24, "ts":1751353291146513, "dur":137, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityLinkerToEditorData.json" }}
,{ "pid":12345, "tid":24, "ts":1751353291146897, "dur":474, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Il2CppBackup/Managed/UnityEngine.CoreModule.pdb" }}
,{ "pid":12345, "tid":24, "ts":1751353291147373, "dur":7092, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353291154523, "dur":3890, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__2.cpp" }}
,{ "pid":12345, "tid":24, "ts":1751353291158414, "dur":9211, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__2.cpp" }}
,{ "pid":12345, "tid":24, "ts":1751353291167645, "dur":221, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353291154466, "dur":13400, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1wgxn7sw6k2h.o" }}
,{ "pid":12345, "tid":24, "ts":1751353291167942, "dur":2684, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UI.cpp" }}
,{ "pid":12345, "tid":24, "ts":1751353291170627, "dur":3710, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UI.cpp" }}
,{ "pid":12345, "tid":24, "ts":1751353291174359, "dur":251, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353291167912, "dur":6698, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ibt2f73hq3tq.o" }}
,{ "pid":12345, "tid":24, "ts":1751353291174692, "dur":2969, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__5.cpp" }}
,{ "pid":12345, "tid":24, "ts":1751353291177662, "dur":3843, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__5.cpp" }}
,{ "pid":12345, "tid":24, "ts":1751353291181525, "dur":237, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353291174661, "dur":7102, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/s20kvqf3nfmk.o" }}
,{ "pid":12345, "tid":24, "ts":1751353291181845, "dur":2379, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__25.cpp" }}
,{ "pid":12345, "tid":24, "ts":1751353291184225, "dur":4838, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__25.cpp" }}
,{ "pid":12345, "tid":24, "ts":1751353291189080, "dur":214, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353291181815, "dur":7480, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/1bnlgts0l6bu.o" }}
,{ "pid":12345, "tid":24, "ts":1751353291190078, "dur":13672334, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/1bnlgts0l6bu.o" }}
,{ "pid":12345, "tid":24, "ts":1751353304862637, "dur":1684, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__15.cpp" }}
,{ "pid":12345, "tid":24, "ts":1751353304864321, "dur":2814, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__15.cpp" }}
,{ "pid":12345, "tid":24, "ts":1751353304867151, "dur":227, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353304862607, "dur":4772, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/u9m2752jj1cz.o" }}
,{ "pid":12345, "tid":24, "ts":1751353305687406, "dur":26935881, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/u9m2752jj1cz.o" }}
,{ "pid":12345, "tid":24, "ts":1751353332623565, "dur":3737, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__9.cpp" }}
,{ "pid":12345, "tid":24, "ts":1751353332627304, "dur":4117, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\GenericMethods__9.cpp" }}
,{ "pid":12345, "tid":24, "ts":1751353332631438, "dur":204, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353332623525, "dur":8117, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tfkoufio19j2.o" }}
,{ "pid":12345, "tid":24, "ts":1751353332909935, "dur":11021617, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/tfkoufio19j2.o" }}
,{ "pid":12345, "tid":24, "ts":1751353343931816, "dur":2903, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__6.cpp" }}
,{ "pid":12345, "tid":24, "ts":1751353343934721, "dur":3698, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Unity.InputSystem__6.cpp" }}
,{ "pid":12345, "tid":24, "ts":1751353343938437, "dur":211, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353343931783, "dur":6866, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/tfbejfgnjnij.o" }}
,{ "pid":12345, "tid":24, "ts":1751353343938739, "dur":502, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIModule.cpp" }}
,{ "pid":12345, "tid":24, "ts":1751353343939242, "dur":861, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.UIModule.cpp" }}
,{ "pid":12345, "tid":24, "ts":1751353343940126, "dur":206, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353343938712, "dur":1620, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/i6hutl07x9cu.o" }}
,{ "pid":12345, "tid":24, "ts":1751353343940408, "dur":126, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.InputModule_CodeGen.c" }}
,{ "pid":12345, "tid":24, "ts":1751353343940534, "dur":234, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.InputModule_CodeGen.c" }}
,{ "pid":12345, "tid":24, "ts":1751353343940380, "dur":448, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/a951q8orlkl2.o" }}
,{ "pid":12345, "tid":24, "ts":1751353343941276, "dur":394816, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/a951q8orlkl2.o" }}
,{ "pid":12345, "tid":24, "ts":1751353344336350, "dur":1850, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System__1.cpp" }}
,{ "pid":12345, "tid":24, "ts":1751353344338201, "dur":2572, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\System__1.cpp" }}
,{ "pid":12345, "tid":24, "ts":1751353344340791, "dur":212, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353344336310, "dur":4694, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/5hnporc7gi97.o" }}
,{ "pid":12345, "tid":24, "ts":1751353344341101, "dur":210, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp_CodeGen.c" }}
,{ "pid":12345, "tid":24, "ts":1751353344341311, "dur":303, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Assembly-CSharp_CodeGen.c" }}
,{ "pid":12345, "tid":24, "ts":1751353344341066, "dur":611, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/t4ka0mlsvwx1.o" }}
,{ "pid":12345, "tid":24, "ts":1751353344342104, "dur":469165, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/t4ka0mlsvwx1.o" }}
,{ "pid":12345, "tid":24, "ts":1751353344811541, "dur":234, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.InputModule.cpp" }}
,{ "pid":12345, "tid":24, "ts":1751353344811776, "dur":633, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\UnityEngine.InputModule.cpp" }}
,{ "pid":12345, "tid":24, "ts":1751353344812424, "dur":212, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353344811503, "dur":1134, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/mh7prgwf1kbz.o" }}
,{ "pid":12345, "tid":24, "ts":1751353344812708, "dur":2986, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__52.cpp" }}
,{ "pid":12345, "tid":24, "ts":1751353344815695, "dur":3508, "ph":"X", "name": "ScanImplicitDeps",  "args": { "detail":"D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\artifacts\\Android\\il2cppOutput\\cpp\\Generics__52.cpp" }}
,{ "pid":12345, "tid":24, "ts":1751353344819220, "dur":214, "ph":"X", "name": "ImplicitDepsSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353344812681, "dur":6754, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"C_Android_arm64 Library/Bee/artifacts/Android/d8kzr/ot52tqat5phz.o" }}
,{ "pid":12345, "tid":24, "ts":1751353344820172, "dur":14326507, "ph":"X", "name": "C_Android_arm64",  "args": { "detail":"Library/Bee/artifacts/Android/d8kzr/ot52tqat5phz.o" }}
,{ "pid":12345, "tid":24, "ts":1751353359146838, "dur":74883248, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353434089699, "dur":7563, "ph":"X", "name": "ProfilerWriteOutput" }
,
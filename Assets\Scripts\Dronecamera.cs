using UnityEngine;

public class Dronecamera : MonoBehaviour
{
    [<PERSON><PERSON>("Joystick Settings")]
    public Joystick joystick;
    public Camera Dronecameraa;

    [<PERSON><PERSON>("Camera Movement Settings")]
    [Tooltip("Speed of horizontal camera movement")]
    public float horizontalSpeed = 2f;

    [<PERSON>lt<PERSON>("Speed of vertical camera movement")]
    public float verticalSpeed = 2f;

    [<PERSON>lt<PERSON>("Smooth camera movement")]
    public float smoothTime = 0.1f;

    [Header("Movement Limits")]
    [Tooltip("Limit horizontal rotation")]
    public bool limitHorizontalRotation = false;
    public float minHorizontalAngle = -180f;
    public float maxHorizontalAngle = 180f;

    [Tooltip("Limit vertical rotation")]
    public bool limitVerticalRotation = true;
    public float minVerticalAngle = -60f;
    public float maxVerticalAngle = 60f;

    // Private variables
    private float currentHorizontalAngle = 0f;
    private float currentVerticalAngle = 0f;

    void Start()
    {
        // Initialize current angles based on camera's current rotation
        Vector3 currentRotation = transform.eulerAngles;
        currentHorizontalAngle = currentRotation.y;
        currentVerticalAngle = currentRotation.x;

        // Convert to -180 to 180 range for vertical angle
        if (currentVerticalAngle > 180f)
            currentVerticalAngle -= 360f;
    }

    void Update()
    {
        if (joystick != null)
        {
            MoveCameraWithJoystick();
        }
    }

    void MoveCameraWithJoystick()
    {
        // Get joystick input
        float horizontalInput = joystick.Horizontal;
        float verticalInput = joystick.Vertical;

        // Calculate rotation changes
        float horizontalChange = horizontalInput * horizontalSpeed * Time.deltaTime;
        float verticalChange = -verticalInput * verticalSpeed * Time.deltaTime; // Negative for inverted Y

        // Update angles
        currentHorizontalAngle += horizontalChange;
        currentVerticalAngle += verticalChange;

        // Apply limits
        if (limitHorizontalRotation)
        {
            currentHorizontalAngle = Mathf.Clamp(currentHorizontalAngle, minHorizontalAngle, maxHorizontalAngle);
        }

        if (limitVerticalRotation)
        {
            currentVerticalAngle = Mathf.Clamp(currentVerticalAngle, minVerticalAngle, maxVerticalAngle);
        }

        // Apply rotation to camera
        Quaternion targetRotation = Quaternion.Euler(currentVerticalAngle, currentHorizontalAngle, 0f);

        if (smoothTime > 0f)
        {
            // Smooth rotation
            transform.rotation = Quaternion.Slerp(transform.rotation, targetRotation, Time.deltaTime / smoothTime);
        }
        else
        {
            // Direct rotation
            transform.rotation = targetRotation;
        }
    }

    // Public methods for external control
    public void SetHorizontalSpeed(float speed)
    {
        horizontalSpeed = speed;
    }

    public void SetVerticalSpeed(float speed)
    {
        verticalSpeed = speed;
    }

    public void ResetCameraRotation()
    {
        currentHorizontalAngle = 0f;
        currentVerticalAngle = 0f;
        transform.rotation = Quaternion.identity;
    }
}

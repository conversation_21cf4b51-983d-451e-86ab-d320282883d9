using UnityEngine;

public class Dronecamera : MonoBehaviour
{
    [<PERSON><PERSON>("Joystick Settings")]
    public Joystick joystick;
    public Camera Dronecameraa;

    [<PERSON><PERSON>("Camera Movement Settings")]
    [Tooltip("Speed of left/right movement")]
    public float horizontalSpeed = 5f;

    [Toolt<PERSON>("Speed of forward/backward movement")]
    public float verticalSpeed = 5f;

    [Toolt<PERSON>("Smooth camera movement")]
    public float smoothTime = 0.1f;

    [<PERSON><PERSON>("Movement Limits")]
    [Toolt<PERSON>("Limit horizontal movement")]
    public bool limitHorizontalMovement = false;
    public float minX = -50f;
    public float maxX = 50f;

    [Tooltip("Limit vertical movement")]
    public bool limitVerticalMovement = false;
    public float minZ = -50f;
    public float maxZ = 50f;

    [Header("Movement Type")]
    [Tooltip("Move relative to camera's rotation or world space")]
    public bool moveRelativeToRotation = true;

    // Private variables
    private Vector3 targetPosition;
    private Vector3 velocity = Vector3.zero;

    void Start()
    {
        // Initialize target position to current position
        targetPosition = transform.position;
    }

    void Update()
    {
        if (joystick != null)
        {
            MoveCameraWithJoystick();
        }
    }

    void MoveCameraWithJoystick()
    {
        // Get joystick input
        float horizontalInput = joystick.Horizontal; // Left/Right
        float verticalInput = joystick.Vertical;     // Forward/Backward

        // Calculate movement direction
        Vector3 moveDirection = Vector3.zero;

        if (moveRelativeToRotation)
        {
            // Move relative to camera's current rotation
            moveDirection = transform.right * horizontalInput + transform.forward * verticalInput;
        }
        else
        {
            // Move in world space (X and Z axis)
            moveDirection = Vector3.right * horizontalInput + Vector3.forward * verticalInput;
        }

        // Calculate target position
        targetPosition += moveDirection * horizontalSpeed * Time.deltaTime;

        // Apply movement limits
        if (limitHorizontalMovement)
        {
            targetPosition.x = Mathf.Clamp(targetPosition.x, minX, maxX);
        }

        if (limitVerticalMovement)
        {
            targetPosition.z = Mathf.Clamp(targetPosition.z, minZ, maxZ);
        }

        // Apply movement to camera
        if (smoothTime > 0f)
        {
            // Smooth movement
            transform.position = Vector3.SmoothDamp(transform.position, targetPosition, ref velocity, smoothTime);
        }
        else
        {
            // Direct movement
            transform.position = targetPosition;
        }
    }

    // Public methods for external control
    public void SetHorizontalSpeed(float speed)
    {
        horizontalSpeed = speed;
    }

    public void SetVerticalSpeed(float speed)
    {
        verticalSpeed = speed;
    }

    public void ResetCameraPosition()
    {
        targetPosition = Vector3.zero;
        transform.position = Vector3.zero;
        velocity = Vector3.zero;
    }

    public void SetCameraPosition(Vector3 newPosition)
    {
        targetPosition = newPosition;
        transform.position = newPosition;
        velocity = Vector3.zero;
    }
}

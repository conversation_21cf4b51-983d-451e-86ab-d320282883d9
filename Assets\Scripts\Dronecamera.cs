using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using System.Collections.Generic;

public class Dronecamera : MonoBehaviour
{
    [<PERSON><PERSON>("Joystick Settings")]
    public Joystick joystick;
    public Camera Dronecameraa;

    [<PERSON><PERSON>("Height Control Buttons")]
    [Tooltip("UI Button for moving camera up")]
    public Button upHeightButton;
    [<PERSON>lt<PERSON>("UI Button for moving camera down")]
    public Button downHeightButton;

    [<PERSON><PERSON>("Camera Movement Settings")]
    [Tooltip("Speed of left/right movement")]
    public float horizontalSpeed = 5f;

    [Tooltip("Speed of forward/backward movement")]
    public float verticalSpeed = 5f;

    [Tooltip("Speed of up/down height movement")]
    public float heightSpeed = 3f;

    [Tooltip("Smooth camera movement")]
    public float smoothTime = 0.1f;

    [Header("Movement Limits")]
    [Tooltip("Limit horizontal movement")]
    public bool limitHorizontalMovement = false;
    public float minX = -50f;
    public float maxX = 50f;

    [Tooltip("Limit vertical movement")]
    public bool limitVerticalMovement = false;
    public float minZ = -50f;
    public float maxZ = 50f;

    [Tooltip("Limit height movement")]
    public bool limitHeightMovement = true;
    public float minY = 0.5f;
    public float maxY = 50f;

    [Header("Movement Type")]
    [Tooltip("Move relative to camera's rotation or world space")]
    public bool moveRelativeToRotation = true;

    [Header("Mouse Drag Rotation")]
    [Tooltip("Enable mouse drag rotation for PC and mobile")]
    public bool enableMouseDragRotation = true;

    [Tooltip("Mouse sensitivity for rotation")]
    public float mouseSensitivity = 2f;

    [Tooltip("Rotation speed for mobile touch")]
    public float touchSensitivity = 1.5f;

    [Tooltip("Smooth rotation")]
    public float rotationSmoothTime = 0.1f;

    [Header("UI Exclusion Zones")]
    [Tooltip("Exclude UI areas from rotation (mobile friendly)")]
    public bool useUIExclusionZones = true;

    [Tooltip("Bottom UI zone height (percentage of screen)")]
    [Range(0f, 0.5f)]
    public float bottomUIZone = 0.2f;

    [Tooltip("Top UI zone height (percentage of screen)")]
    [Range(0f, 0.5f)]
    public float topUIZone = 0.1f;

    [Tooltip("Left UI zone width (percentage of screen)")]
    [Range(0f, 0.5f)]
    public float leftUIZone = 0.15f;

    [Tooltip("Right UI zone width (percentage of screen)")]
    [Range(0f, 0.5f)]
    public float rightUIZone = 0.15f;

    [Header("Rotation Limits")]
    [Tooltip("Limit vertical rotation")]
    public bool limitVerticalRotation = true;
    public float minVerticalAngle = -80f;
    public float maxVerticalAngle = 80f;

    [Tooltip("Limit horizontal rotation")]
    public bool limitHorizontalRotation = false;
    public float minHorizontalAngle = -180f;
    public float maxHorizontalAngle = 180f;

    // Private variables
    private Vector3 targetPosition;
    private Vector3 velocity = Vector3.zero;

    // Height button states
    private bool isUpButtonPressed = false;
    private bool isDownButtonPressed = false;

    // Mouse drag rotation variables
    private bool isDragging = false;
    private Vector2 lastMousePosition;
    private Vector2 lastTouchPosition;
    private float currentXRotation = 0f;
    private float currentYRotation = 0f;
    private float targetXRotation = 0f;
    private float targetYRotation = 0f;
    private Vector2 rotationVelocity = Vector2.zero;

    void Start()
    {
        // Initialize target position to current position
        targetPosition = transform.position;

        // Initialize rotation values
        currentXRotation = transform.eulerAngles.x;
        currentYRotation = transform.eulerAngles.y;
        targetXRotation = currentXRotation;
        targetYRotation = currentYRotation;

        // Initialize height control buttons
        InitializeHeightButtons();
    }

    void Update()
    {
        if (joystick != null)
        {
            MoveCameraWithJoystick();
        }

        // Handle mouse drag rotation
        if (enableMouseDragRotation)
        {
            HandleMouseDragRotation();
        }

        // Handle height control buttons
        HandleHeightControl();

        // Apply rotation
        ApplyRotation();
    }

    void MoveCameraWithJoystick()
    {
        // Get joystick input
        float horizontalInput = joystick.Horizontal; // Left/Right
        float verticalInput = joystick.Vertical;     // Forward/Backward

        // Calculate movement direction
        Vector3 moveDirection = Vector3.zero;

        if (moveRelativeToRotation)
        {
            // Move relative to camera's current rotation
            moveDirection = transform.right * horizontalInput + transform.forward * verticalInput;
        }
        else
        {
            // Move in world space (X and Z axis)
            moveDirection = Vector3.right * horizontalInput + Vector3.forward * verticalInput;
        }

        // Calculate target position
        targetPosition += moveDirection * horizontalSpeed * Time.deltaTime;

        // Apply movement limits
        if (limitHorizontalMovement)
        {
            targetPosition.x = Mathf.Clamp(targetPosition.x, minX, maxX);
        }

        if (limitVerticalMovement)
        {
            targetPosition.z = Mathf.Clamp(targetPosition.z, minZ, maxZ);
        }

        if (limitHeightMovement)
        {
            targetPosition.y = Mathf.Clamp(targetPosition.y, minY, maxY);
        }

        // Apply movement to camera
        if (smoothTime > 0f)
        {
            // Smooth movement
            transform.position = Vector3.SmoothDamp(transform.position, targetPosition, ref velocity, smoothTime);
        }
        else
        {
            // Direct movement
            transform.position = targetPosition;
        }
    }

    void HandleMouseDragRotation()
    {
        // Check if we're over UI elements
        if (IsPointerOverUIObject())
            return;

        // PC Mouse Input
        if (Application.platform != RuntimePlatform.Android && Application.platform != RuntimePlatform.IPhonePlayer)
        {
            HandlePCMouseInput();
        }
        // Mobile Touch Input
        else
        {
            HandleMobileTouchInput();
        }
    }

    void HandlePCMouseInput()
    {
        // Mouse button down - start dragging
        if (Input.GetMouseButtonDown(0))
        {
            // Check if mouse is not over UI
            if (!IsPointerOverUIObject())
            {
                isDragging = true;
                lastMousePosition = Input.mousePosition;
            }
        }
        // Mouse button up - stop dragging
        else if (Input.GetMouseButtonUp(0))
        {
            isDragging = false;
        }
        // If mouse button is not held down, ensure dragging is false
        else if (!Input.GetMouseButton(0))
        {
            isDragging = false;
        }

        // Handle dragging only if mouse button is still held down
        if (isDragging && Input.GetMouseButton(0))
        {
            Vector2 currentMousePosition = Input.mousePosition;
            Vector2 mouseDelta = currentMousePosition - lastMousePosition;

            // Apply mouse sensitivity
            float deltaX = mouseDelta.x * mouseSensitivity * Time.deltaTime;
            float deltaY = -mouseDelta.y * mouseSensitivity * Time.deltaTime; // Invert Y for natural feel

            // Update target rotation
            targetYRotation += deltaX; // Horizontal rotation (Y-axis)
            targetXRotation += deltaY; // Vertical rotation (X-axis)

            // Apply rotation limits
            ApplyRotationLimits();

            lastMousePosition = currentMousePosition;
        }
    }

    void HandleMobileTouchInput()
    {
        if (Input.touchCount == 1)
        {
            Touch touch = Input.GetTouch(0);

            if (touch.phase == TouchPhase.Began)
            {
                isDragging = true;
                lastTouchPosition = touch.position;
            }
            else if (touch.phase == TouchPhase.Ended || touch.phase == TouchPhase.Canceled)
            {
                isDragging = false;
            }
            else if (touch.phase == TouchPhase.Moved && isDragging)
            {
                Vector2 touchDelta = touch.position - lastTouchPosition;

                // Apply touch sensitivity
                float deltaX = touchDelta.x * touchSensitivity * Time.deltaTime;
                float deltaY = -touchDelta.y * touchSensitivity * Time.deltaTime; // Invert Y for natural feel

                // Update target rotation
                targetYRotation += deltaX; // Horizontal rotation (Y-axis)
                targetXRotation += deltaY; // Vertical rotation (X-axis)

                // Apply rotation limits
                ApplyRotationLimits();

                lastTouchPosition = touch.position;
            }
        }
        else if (Input.touchCount > 1)
        {
            // Stop dragging if multiple touches
            isDragging = false;
        }
    }

    void ApplyRotationLimits()
    {
        // Limit vertical rotation
        if (limitVerticalRotation)
        {
            targetXRotation = Mathf.Clamp(targetXRotation, minVerticalAngle, maxVerticalAngle);
        }

        // Limit horizontal rotation
        if (limitHorizontalRotation)
        {
            targetYRotation = Mathf.Clamp(targetYRotation, minHorizontalAngle, maxHorizontalAngle);
        }
        else
        {
            // Keep Y rotation in 0-360 range
            if (targetYRotation > 360f) targetYRotation -= 360f;
            if (targetYRotation < 0f) targetYRotation += 360f;
        }
    }

    void ApplyRotation()
    {
        // Smooth rotation interpolation
        if (rotationSmoothTime > 0f)
        {
            currentXRotation = Mathf.SmoothDampAngle(currentXRotation, targetXRotation, ref rotationVelocity.x, rotationSmoothTime);
            currentYRotation = Mathf.SmoothDampAngle(currentYRotation, targetYRotation, ref rotationVelocity.y, rotationSmoothTime);
        }
        else
        {
            currentXRotation = targetXRotation;
            currentYRotation = targetYRotation;
        }

        // Apply rotation to camera
        transform.rotation = Quaternion.Euler(currentXRotation, currentYRotation, 0f);
    }

    bool IsPointerOverUIObject()
    {
        // Check if mouse/touch is over UI elements
        if (EventSystem.current == null)
            return false;

        Vector2 inputPosition;

        // Get input position based on platform
        if (Application.platform != RuntimePlatform.Android && Application.platform != RuntimePlatform.IPhonePlayer)
        {
            // PC - use mouse position
            inputPosition = Input.mousePosition;

            // For PC, use traditional UI detection but also check exclusion zones
            bool overUI = EventSystem.current.IsPointerOverGameObject();
            if (overUI) return true;
        }
        else
        {
            // Mobile - use touch position
            if (Input.touchCount > 0)
            {
                inputPosition = Input.GetTouch(0).position;
            }
            else
            {
                return false;
            }
        }

        // Check UI exclusion zones (mobile-friendly approach)
        if (useUIExclusionZones)
        {
            return IsInUIExclusionZone(inputPosition);
        }

        return false;
    }

    bool IsInUIExclusionZone(Vector2 screenPosition)
    {
        // Convert screen position to normalized coordinates (0-1)
        float normalizedX = screenPosition.x / Screen.width;
        float normalizedY = screenPosition.y / Screen.height;

        // Check bottom UI zone (where height buttons usually are)
        if (normalizedY <= bottomUIZone)
        {
            return true;
        }

        // Check top UI zone (where menu buttons usually are)
        if (normalizedY >= (1f - topUIZone))
        {
            return true;
        }

        // Check left UI zone (where joystick usually is)
        if (normalizedX <= leftUIZone)
        {
            return true;
        }

        // Check right UI zone (where other controls usually are)
        if (normalizedX >= (1f - rightUIZone))
        {
            return true;
        }

        // Position is in the center area - allow rotation
        return false;
    }

    void InitializeHeightButtons()
    {
        // Setup Up Height Button
        if (upHeightButton != null)
        {
            // Add event listeners for button press and release
            upHeightButton.onClick.AddListener(() => {
                // This is for single click, but we want continuous press
            });

            // Add EventTrigger for press and hold functionality
            var upEventTrigger = upHeightButton.gameObject.GetComponent<UnityEngine.EventSystems.EventTrigger>();
            if (upEventTrigger == null)
            {
                upEventTrigger = upHeightButton.gameObject.AddComponent<UnityEngine.EventSystems.EventTrigger>();
            }

            // Up button down
            var upPointerDown = new UnityEngine.EventSystems.EventTrigger.Entry();
            upPointerDown.eventID = UnityEngine.EventSystems.EventTriggerType.PointerDown;
            upPointerDown.callback.AddListener((data) => { OnUpHeightButtonDown(); });
            upEventTrigger.triggers.Add(upPointerDown);

            // Up button up
            var upPointerUp = new UnityEngine.EventSystems.EventTrigger.Entry();
            upPointerUp.eventID = UnityEngine.EventSystems.EventTriggerType.PointerUp;
            upPointerUp.callback.AddListener((data) => { OnUpHeightButtonUp(); });
            upEventTrigger.triggers.Add(upPointerUp);
        }

        // Setup Down Height Button
        if (downHeightButton != null)
        {
            // Add event listeners for button press and release
            downHeightButton.onClick.AddListener(() => {
                // This is for single click, but we want continuous press
            });

            // Add EventTrigger for press and hold functionality
            var downEventTrigger = downHeightButton.gameObject.GetComponent<UnityEngine.EventSystems.EventTrigger>();
            if (downEventTrigger == null)
            {
                downEventTrigger = downHeightButton.gameObject.AddComponent<UnityEngine.EventSystems.EventTrigger>();
            }

            // Down button down
            var downPointerDown = new UnityEngine.EventSystems.EventTrigger.Entry();
            downPointerDown.eventID = UnityEngine.EventSystems.EventTriggerType.PointerDown;
            downPointerDown.callback.AddListener((data) => { OnDownHeightButtonDown(); });
            downEventTrigger.triggers.Add(downPointerDown);

            // Down button up
            var downPointerUp = new UnityEngine.EventSystems.EventTrigger.Entry();
            downPointerUp.eventID = UnityEngine.EventSystems.EventTriggerType.PointerUp;
            downPointerUp.callback.AddListener((data) => { OnDownHeightButtonUp(); });
            downEventTrigger.triggers.Add(downPointerUp);
        }
    }

    void HandleHeightControl()
    {
        // Handle up height movement
        if (isUpButtonPressed)
        {
            targetPosition.y += heightSpeed * Time.deltaTime;

            // Apply height limits
            if (limitHeightMovement)
            {
                targetPosition.y = Mathf.Clamp(targetPosition.y, minY, maxY);
            }
        }

        // Handle down height movement
        if (isDownButtonPressed)
        {
            targetPosition.y -= heightSpeed * Time.deltaTime;

            // Apply height limits
            if (limitHeightMovement)
            {
                targetPosition.y = Mathf.Clamp(targetPosition.y, minY, maxY);
            }
        }

        // Apply height movement to camera if any button is pressed
        if (isUpButtonPressed || isDownButtonPressed)
        {
            if (smoothTime > 0f)
            {
                // Smooth movement
                transform.position = Vector3.SmoothDamp(transform.position, targetPosition, ref velocity, smoothTime);
            }
            else
            {
                // Direct movement
                transform.position = targetPosition;
            }
        }
    }

    // Height button event handlers
    public void OnUpHeightButtonDown()
    {
        isUpButtonPressed = true;
    }

    public void OnUpHeightButtonUp()
    {
        isUpButtonPressed = false;
    }

    public void OnDownHeightButtonDown()
    {
        isDownButtonPressed = true;
    }

    public void OnDownHeightButtonUp()
    {
        isDownButtonPressed = false;
    }

    // Public methods for external control
    public void SetHorizontalSpeed(float speed)
    {
        horizontalSpeed = speed;
    }

    public void SetVerticalSpeed(float speed)
    {
        verticalSpeed = speed;
    }

    public void SetHeightSpeed(float speed)
    {
        heightSpeed = speed;
    }

    public void ResetCameraPosition()
    {
        targetPosition = Vector3.zero;
        transform.position = Vector3.zero;
        velocity = Vector3.zero;
    }

    public void SetCameraPosition(Vector3 newPosition)
    {
        targetPosition = newPosition;
        transform.position = newPosition;
        velocity = Vector3.zero;
    }

    // Manual height control methods
    public void MoveUp()
    {
        targetPosition.y += heightSpeed * Time.deltaTime;
        if (limitHeightMovement)
        {
            targetPosition.y = Mathf.Clamp(targetPosition.y, minY, maxY);
        }
    }

    public void MoveDown()
    {
        targetPosition.y -= heightSpeed * Time.deltaTime;
        if (limitHeightMovement)
        {
            targetPosition.y = Mathf.Clamp(targetPosition.y, minY, maxY);
        }
    }

    // Rotation control methods
    public void SetMouseSensitivity(float sensitivity)
    {
        mouseSensitivity = sensitivity;
    }

    public void SetTouchSensitivity(float sensitivity)
    {
        touchSensitivity = sensitivity;
    }

    public void SetRotationSmoothTime(float smoothTime)
    {
        rotationSmoothTime = smoothTime;
    }

    public void ResetCameraRotation()
    {
        currentXRotation = 0f;
        currentYRotation = 0f;
        targetXRotation = 0f;
        targetYRotation = 0f;
        transform.rotation = Quaternion.identity;
        rotationVelocity = Vector2.zero;
    }

    public void SetCameraRotation(Vector3 eulerAngles)
    {
        currentXRotation = eulerAngles.x;
        currentYRotation = eulerAngles.y;
        targetXRotation = eulerAngles.x;
        targetYRotation = eulerAngles.y;
        transform.rotation = Quaternion.Euler(eulerAngles);
        rotationVelocity = Vector2.zero;
    }

    public void EnableMouseDragRotation(bool enable)
    {
        enableMouseDragRotation = enable;
    }

    public Vector3 GetCurrentRotation()
    {
        return new Vector3(currentXRotation, currentYRotation, 0f);
    }
}

using UnityEngine;
using UnityEngine.UI;

public class Dronecamera : MonoB<PERSON><PERSON><PERSON>
{
    [<PERSON><PERSON>("Joystick Settings")]
    public Joystick joystick;
    public Camera Dronecameraa;

    [<PERSON><PERSON>("Height Control Buttons")]
    [Tooltip("UI Button for moving camera up")]
    public Button upHeightButton;
    [Toolt<PERSON>("UI Button for moving camera down")]
    public Button downHeightButton;

    [<PERSON><PERSON>("Camera Movement Settings")]
    [Tooltip("Speed of left/right movement")]
    public float horizontalSpeed = 5f;

    [Tooltip("Speed of forward/backward movement")]
    public float verticalSpeed = 5f;

    [Tooltip("Speed of up/down height movement")]
    public float heightSpeed = 3f;

    [Tooltip("Smooth camera movement")]
    public float smoothTime = 0.1f;

    [Header("Movement Limits")]
    [Tooltip("Limit horizontal movement")]
    public bool limitHorizontalMovement = false;
    public float minX = -50f;
    public float maxX = 50f;

    [Tooltip("Limit vertical movement")]
    public bool limitVerticalMovement = false;
    public float minZ = -50f;
    public float maxZ = 50f;

    [Tooltip("Limit height movement")]
    public bool limitHeightMovement = true;
    public float minY = 0.5f;
    public float maxY = 50f;

    [Header("Movement Type")]
    [Tooltip("Move relative to camera's rotation or world space")]
    public bool moveRelativeToRotation = true;

    // Private variables
    private Vector3 targetPosition;
    private Vector3 velocity = Vector3.zero;

    // Height button states
    private bool isUpButtonPressed = false;
    private bool isDownButtonPressed = false;

    void Start()
    {
        // Initialize target position to current position
        targetPosition = transform.position;

        // Initialize height control buttons
        InitializeHeightButtons();
    }

    void Update()
    {
        if (joystick != null)
        {
            MoveCameraWithJoystick();
        }

        // Handle height control buttons
        HandleHeightControl();
    }

    void MoveCameraWithJoystick()
    {
        // Get joystick input
        float horizontalInput = joystick.Horizontal; // Left/Right
        float verticalInput = joystick.Vertical;     // Forward/Backward

        // Calculate movement direction
        Vector3 moveDirection = Vector3.zero;

        if (moveRelativeToRotation)
        {
            // Move relative to camera's current rotation
            moveDirection = transform.right * horizontalInput + transform.forward * verticalInput;
        }
        else
        {
            // Move in world space (X and Z axis)
            moveDirection = Vector3.right * horizontalInput + Vector3.forward * verticalInput;
        }

        // Calculate target position
        targetPosition += moveDirection * horizontalSpeed * Time.deltaTime;

        // Apply movement limits
        if (limitHorizontalMovement)
        {
            targetPosition.x = Mathf.Clamp(targetPosition.x, minX, maxX);
        }

        if (limitVerticalMovement)
        {
            targetPosition.z = Mathf.Clamp(targetPosition.z, minZ, maxZ);
        }

        if (limitHeightMovement)
        {
            targetPosition.y = Mathf.Clamp(targetPosition.y, minY, maxY);
        }

        // Apply movement to camera
        if (smoothTime > 0f)
        {
            // Smooth movement
            transform.position = Vector3.SmoothDamp(transform.position, targetPosition, ref velocity, smoothTime);
        }
        else
        {
            // Direct movement
            transform.position = targetPosition;
        }
    }

    void InitializeHeightButtons()
    {
        // Setup Up Height Button
        if (upHeightButton != null)
        {
            // Add event listeners for button press and release
            upHeightButton.onClick.AddListener(() => {
                // This is for single click, but we want continuous press
            });

            // Add EventTrigger for press and hold functionality
            var upEventTrigger = upHeightButton.gameObject.GetComponent<UnityEngine.EventSystems.EventTrigger>();
            if (upEventTrigger == null)
            {
                upEventTrigger = upHeightButton.gameObject.AddComponent<UnityEngine.EventSystems.EventTrigger>();
            }

            // Up button down
            var upPointerDown = new UnityEngine.EventSystems.EventTrigger.Entry();
            upPointerDown.eventID = UnityEngine.EventSystems.EventTriggerType.PointerDown;
            upPointerDown.callback.AddListener((data) => { OnUpHeightButtonDown(); });
            upEventTrigger.triggers.Add(upPointerDown);

            // Up button up
            var upPointerUp = new UnityEngine.EventSystems.EventTrigger.Entry();
            upPointerUp.eventID = UnityEngine.EventSystems.EventTriggerType.PointerUp;
            upPointerUp.callback.AddListener((data) => { OnUpHeightButtonUp(); });
            upEventTrigger.triggers.Add(upPointerUp);
        }

        // Setup Down Height Button
        if (downHeightButton != null)
        {
            // Add event listeners for button press and release
            downHeightButton.onClick.AddListener(() => {
                // This is for single click, but we want continuous press
            });

            // Add EventTrigger for press and hold functionality
            var downEventTrigger = downHeightButton.gameObject.GetComponent<UnityEngine.EventSystems.EventTrigger>();
            if (downEventTrigger == null)
            {
                downEventTrigger = downHeightButton.gameObject.AddComponent<UnityEngine.EventSystems.EventTrigger>();
            }

            // Down button down
            var downPointerDown = new UnityEngine.EventSystems.EventTrigger.Entry();
            downPointerDown.eventID = UnityEngine.EventSystems.EventTriggerType.PointerDown;
            downPointerDown.callback.AddListener((data) => { OnDownHeightButtonDown(); });
            downEventTrigger.triggers.Add(downPointerDown);

            // Down button up
            var downPointerUp = new UnityEngine.EventSystems.EventTrigger.Entry();
            downPointerUp.eventID = UnityEngine.EventSystems.EventTriggerType.PointerUp;
            downPointerUp.callback.AddListener((data) => { OnDownHeightButtonUp(); });
            downEventTrigger.triggers.Add(downPointerUp);
        }
    }

    void HandleHeightControl()
    {
        // Handle up height movement
        if (isUpButtonPressed)
        {
            targetPosition.y += heightSpeed * Time.deltaTime;

            // Apply height limits
            if (limitHeightMovement)
            {
                targetPosition.y = Mathf.Clamp(targetPosition.y, minY, maxY);
            }
        }

        // Handle down height movement
        if (isDownButtonPressed)
        {
            targetPosition.y -= heightSpeed * Time.deltaTime;

            // Apply height limits
            if (limitHeightMovement)
            {
                targetPosition.y = Mathf.Clamp(targetPosition.y, minY, maxY);
            }
        }

        // Apply height movement to camera if any button is pressed
        if (isUpButtonPressed || isDownButtonPressed)
        {
            if (smoothTime > 0f)
            {
                // Smooth movement
                transform.position = Vector3.SmoothDamp(transform.position, targetPosition, ref velocity, smoothTime);
            }
            else
            {
                // Direct movement
                transform.position = targetPosition;
            }
        }
    }

    // Height button event handlers
    public void OnUpHeightButtonDown()
    {
        isUpButtonPressed = true;
    }

    public void OnUpHeightButtonUp()
    {
        isUpButtonPressed = false;
    }

    public void OnDownHeightButtonDown()
    {
        isDownButtonPressed = true;
    }

    public void OnDownHeightButtonUp()
    {
        isDownButtonPressed = false;
    }

    // Public methods for external control
    public void SetHorizontalSpeed(float speed)
    {
        horizontalSpeed = speed;
    }

    public void SetVerticalSpeed(float speed)
    {
        verticalSpeed = speed;
    }

    public void SetHeightSpeed(float speed)
    {
        heightSpeed = speed;
    }

    public void ResetCameraPosition()
    {
        targetPosition = Vector3.zero;
        transform.position = Vector3.zero;
        velocity = Vector3.zero;
    }

    public void SetCameraPosition(Vector3 newPosition)
    {
        targetPosition = newPosition;
        transform.position = newPosition;
        velocity = Vector3.zero;
    }

    // Manual height control methods
    public void MoveUp()
    {
        targetPosition.y += heightSpeed * Time.deltaTime;
        if (limitHeightMovement)
        {
            targetPosition.y = Mathf.Clamp(targetPosition.y, minY, maxY);
        }
    }

    public void MoveDown()
    {
        targetPosition.y -= heightSpeed * Time.deltaTime;
        if (limitHeightMovement)
        {
            targetPosition.y = Mathf.Clamp(targetPosition.y, minY, maxY);
        }
    }
}

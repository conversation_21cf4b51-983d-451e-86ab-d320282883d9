using UnityEngine;

[RequireComponent(typeof(Rigidbody))]
public class TrailerHitchSystem : MonoBehaviour
{
    [Header("Hitch Points")]
    public Transform trailerHitchPoint;           // Point on trailer (where it attaches)
    public Transform truckHitchPoint;             // Assigned at runtime (point on truck)

    [Header("Attachment Settings")]
    public bool isAttached = false;
    public float positionLerpSpeed = 5f;
    public float rotationLerpSpeed = 5f;
    public float detachDistance = 2.5f;

    private Rigidbody rb;

    void Start()
    {
        rb = GetComponent<Rigidbody>();
    }

    void FixedUpdate()
    {
        if (isAttached && truckHitchPoint != null)
        {
            // Get the world offset between the trailer body and its hitch
            Vector3 hitchOffset = trailerHitchPoint.position - transform.position;

            // Calculate target position to keep hitch point at truck's hitch point
            Vector3 targetPosition = truckHitchPoint.position - hitchOffset;
            Vector3 velocity = (targetPosition - transform.position) * positionLerpSpeed;

            rb.linearVelocity = velocity;

            // Match trailer's forward direction to truck's forward (smoothly)
            Quaternion targetRotation = Quaternion.LookRotation(truckHitchPoint.forward, Vector3.up);
            rb.MoveRotation(Quaternion.Slerp(rb.rotation, targetRotation, Time.fixedDeltaTime * rotationLerpSpeed));

            // Auto-detach if distance exceeds limit (optional)
            float currentDistance = Vector3.Distance(trailerHitchPoint.position, truckHitchPoint.position);
            if (currentDistance > detachDistance)
            {
                Detach();
            }
        }
    }

    public void AttachToTruck(Transform truckHitch)
    {
        truckHitchPoint = truckHitch;
        isAttached = true;
        rb.useGravity = true;
        rb.isKinematic = false;
    }

    public void Detach()
    {
        isAttached = false;
        truckHitchPoint = null;
        // Optional: leave gravity on so trailer drops
        rb.useGravity = true;
    }
}

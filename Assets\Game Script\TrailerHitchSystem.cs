using UnityEngine;

[RequireComponent(typeof(Rigidbody))]
public class TrailerHitchSystem : MonoBehaviour
{
    [Header("Hitch Points")]
    public Transform trailerHitchPoint;           // Point on trailer (where it attaches)
    public Transform truckHitchPoint;             // Assigned at runtime (point on truck)

    [Header("Attachment Settings")]
    public bool isAttached = false;
    public float connectionForce = 1000f;         // Force to maintain connection
    public float dampingForce = 50f;              // Damping to reduce oscillation
    public float maxConnectionDistance = 0.5f;    // Maximum allowed distance between hitch points
    public float detachDistance = 2.5f;           // Distance at which trailer auto-detaches

    [Header("Rotation Settings")]
    public float rotationForce = 500f;            // Torque for rotation alignment
    public float rotationDamping = 25f;           // Angular damping
    public bool enableRotationAlignment = true;   // Toggle rotation alignment

    private Rigidbody rb;
    private Vector3 lastTruckPosition;
    private bool wasAttachedLastFrame = false;

    void Start()
    {
        rb = GetComponent<Rigidbody>();

        // Set initial physics properties for stability
        rb.centerOfMass = new Vector3(0, -0.5f, 0); // Lower center of mass for stability
        rb.angularDamping = 3f; // Reduce angular momentum buildup
        rb.linearDamping = 0.5f; // Add some linear drag

        if (trailerHitchPoint == null)
        {
            Debug.LogWarning("TrailerHitchPoint not assigned on " + gameObject.name);
        }
    }

    void FixedUpdate()
    {
        if (isAttached && truckHitchPoint != null && trailerHitchPoint != null)
        {
            // Calculate the distance between hitch points
            Vector3 hitchDistance = truckHitchPoint.position - trailerHitchPoint.position;
            float distance = hitchDistance.magnitude;

            // Auto-detach if distance exceeds limit
            if (distance > detachDistance)
            {
                Detach();
                return;
            }

            // Apply connection force only if distance exceeds threshold
            if (distance > maxConnectionDistance)
            {
                // Calculate spring force to pull trailer toward truck
                Vector3 springForce = hitchDistance.normalized * connectionForce * (distance - maxConnectionDistance);

                // Add damping to reduce oscillation
                Vector3 dampingForce = -rb.linearVelocity * dampingForce;

                // Apply combined force
                Vector3 totalForce = springForce + dampingForce;
                rb.AddForce(totalForce);
            }

            // Handle rotation alignment
            if (enableRotationAlignment)
            {
                // Calculate desired rotation based on truck's direction
                Vector3 truckDirection = truckHitchPoint.forward;
                if (truckDirection.magnitude > 0.1f)
                {
                    Quaternion targetRotation = Quaternion.LookRotation(truckDirection, Vector3.up);

                    // Calculate rotation difference
                    Quaternion rotationDiff = targetRotation * Quaternion.Inverse(rb.rotation);
                    rotationDiff.ToAngleAxis(out float angle, out Vector3 axis);

                    // Normalize angle to [-180, 180]
                    if (angle > 180f) angle -= 360f;

                    // Apply torque for smooth rotation
                    if (Mathf.Abs(angle) > 1f) // Only apply if significant difference
                    {
                        Vector3 torque = axis * angle * rotationForce * Mathf.Deg2Rad;
                        rb.AddTorque(torque - rb.angularVelocity * rotationDamping);
                    }
                }
            }
        }

        // Update tracking variables
        if (truckHitchPoint != null)
        {
            lastTruckPosition = truckHitchPoint.position;
        }
        wasAttachedLastFrame = isAttached;
    }

    public void AttachToTruck(Transform truckHitch)
    {
        if (truckHitch == null)
        {
            Debug.LogWarning("Cannot attach to null truck hitch point!");
            return;
        }

        truckHitchPoint = truckHitch;
        isAttached = true;

        // Ensure proper physics settings for attachment
        rb.useGravity = true;
        rb.isKinematic = false;

        // Reset velocities to prevent initial jerking
        rb.linearVelocity = Vector3.zero;
        rb.angularVelocity = Vector3.zero;

        // Adjust physics properties for stable attachment
        rb.linearDamping = 1f; // Increase damping when attached
        rb.angularDamping = 5f;

        Debug.Log($"Trailer {gameObject.name} attached to truck hitch");
    }

    public void Detach()
    {
        if (!isAttached)
        {
            Debug.LogWarning("Trailer is already detached!");
            return;
        }

        isAttached = false;
        truckHitchPoint = null;

        // Restore normal physics settings
        rb.useGravity = true;
        rb.linearDamping = 0.5f; // Restore normal damping
        rb.angularDamping = 3f;

        Debug.Log($"Trailer {gameObject.name} detached from truck");
    }

    // Helper method to check if trailer can be attached
    public bool CanAttachToTruck(Transform truckHitch)
    {
        if (truckHitch == null || trailerHitchPoint == null) return false;

        float distance = Vector3.Distance(trailerHitchPoint.position, truckHitch.position);
        return distance <= detachDistance && !isAttached;
    }

    // Get current connection status info
    public float GetConnectionDistance()
    {
        if (!isAttached || truckHitchPoint == null || trailerHitchPoint == null)
            return float.MaxValue;

        return Vector3.Distance(trailerHitchPoint.position, truckHitchPoint.position);
    }
}

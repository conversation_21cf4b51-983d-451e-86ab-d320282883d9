# Custom settings

Add custom properties and settings to a Scriptable Render Pipeline.

|Page|Description|
|-|-|
|[Adding properties in the menu](adding-properties.md)|Add properties in the **Core Render Pipeline** settings section.|
|[Add custom graphics settings](add-custom-graphics-settings.md)|Add custom graphics settings to the **Edit** > **Project Settings** > **Graphics** window, then use the values of the settings to customize your build.|
|[Get custom graphics settings](get-custom-graphics-settings.md)|Get a custom graphics setting and read its value, or detect when a setting changes.|
|[Include or exclude a setting in your build](choose-whether-unity-includes-a-graphics-setting-in-your-build.md)|Choose whether Unity includes or strips a graphics setting in your build, and check if a build includes a setting.|

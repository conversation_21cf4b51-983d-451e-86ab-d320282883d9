﻿using UnityEngine;
using UnityEngine.UI;

namespace BoatControllerwithShooting
{
    public class GameCanvas : MonoBehaviour
    {
        public Button button_Missile;
        public Button button_Machinegun;
        public GameObject joystick;
        public Button button_CameraChange;
        public int isFiringUpdate = 0;
        public Slider Slider_CurrentFuel;
        public Text Text_CurrentFuel;

        public Text Text_Ammo_Machinegun;
        public Text Text_Ammo_Missile;
        public Text Text_Speed;
        public Text Text_Health;

        public static GameCanvas Instance;
        public GameObject GameUI;
        public GameObject RadarUI;
        public GameObject GasolineUI;

        void Awake()
        {
            Instance = this;
        }

        void Start()
        {
            if (BoatSystemManager.Instance.ShowRadar)
            {
                RadarUI.SetActive(true);
            }
            else
            {
                RadarUI.SetActive(false);
            }
        }

        public void Configure_For_Mobile()
        {
            joystick.gameObject.SetActive(true);
        }

        public void Configure_For_PCConsole()
        {
            joystick.gameObject.SetActive(false);
            button_CameraChange.GetComponentInChildren<Text>().text = "Camera (C)";
        }

        public void Click_Button_CameraSwitch()
        {
            if (button_CameraChange.IsInteractable())
            {
                if (BoatSystemManager.Instance.cameraFPS != null && BoatSystemManager.Instance.cameraFPS.activeSelf)
                {
                    BoatSystemManager.Instance.cameraFPS.SetActive(false);
                    BoatSystemManager.Instance.cameraTPS.SetActive(true);
                }
                else if (BoatSystemManager.Instance.cameraTPS != null)
                {
                    BoatSystemManager.Instance.cameraFPS.SetActive(true);
                    BoatSystemManager.Instance.cameraTPS.SetActive(false);
                }
            }
        }

        private void Update()
        {
            if(Input.GetKeyUp(KeyCode.C) && BoatSystemManager.Instance.controllerType == ControllerType.KeyboardMouse)
            {
                Click_Button_CameraSwitch();
            }
        }

        public void Hide_GameUI()
        {
            GameUI.SetActive(false);
        }

        public void Click_Button_MachineGun_Down()
        {
            isFiringUpdate = 1;
        }

        public void Click_Button_Guns_Up()
        {
            isFiringUpdate = 0;
        }

        public void Click_Button_Missle_Down()
        {
            isFiringUpdate = -1;
        }
    }
}
%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: RCC Cinematic Animation 3
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: {x: 6.3840003, y: 116.884, z: 10}
        inSlope: {x: -1.5957501, y: 12.901501, z: -5}
        outSlope: {x: -1.5957501, y: 12.901501, z: -5}
        tangentMode: 0
      - serializedVersion: 2
        time: 4
        value: {x: 0.001, y: 168.49, z: -10}
        inSlope: {x: -0.79793566, y: 6.4037633, z: -2.5}
        outSlope: {x: -0.79793566, y: 6.4037633, z: -2.5}
        tangentMode: 0
      - serializedVersion: 2
        time: 7
        value: {x: 0.0006361403, y: 168.20808, z: -10}
        inSlope: {x: -0.00012128658, y: -0.09397379, z: 0}
        outSlope: {x: -0.00012128658, y: -0.09397379, z: 0}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_PositionCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: {x: -2.5, y: 0.2, z: 13.092}
        inSlope: {x: 0.16128571, y: -0.06771429, z: -0.17885712}
        outSlope: {x: 0.25, y: -0.08, z: -0.023000002}
        tangentMode: 0
      - serializedVersion: 2
        time: 4
        value: {x: -1.5, y: -0.12, z: 13}
        inSlope: {x: 0.10635714, y: -0.033857144, z: -0.06915926}
        outSlope: {x: 0.10635714, y: -0.033857144, z: -0.06915926}
        tangentMode: 0
      - serializedVersion: 2
        time: 7
        value: {x: -1.5, y: -0.11999997, z: 12}
        inSlope: {x: 0, y: 0, z: -0.33333334}
        outSlope: {x: 0, y: 0, z: 0}
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    path: 
  m_ScaleCurves: []
  m_FloatCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 30
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - serializedVersion: 2
        time: 1
        value: 30
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - serializedVersion: 2
        time: 4
        value: 60
        inSlope: 5
        outSlope: 5
        tangentMode: 34
      - serializedVersion: 2
        time: 7
        value: 60
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: FOV
    path: 
    classID: 114
    script: {fileID: 11500000, guid: df78f1d5dd5160441ab435e4fcc6981b, type: 3}
  m_PPtrCurves: []
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 1
      script: {fileID: 0}
      typeID: 4
      customType: 0
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 0
      attribute: 4
      script: {fileID: 0}
      typeID: 4
      customType: 14
      isPPtrCurve: 0
    - serializedVersion: 2
      path: 0
      attribute: 2126483307
      script: {fileID: 11500000, guid: df78f1d5dd5160441ab435e4fcc6981b, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 0
    pptrCurveMapping: []
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 7
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 1
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 6.3840003
        inSlope: -1.5957501
        outSlope: -1.5957501
        tangentMode: 34
      - serializedVersion: 2
        time: 4
        value: 0.001
        inSlope: -0.79793566
        outSlope: -0.79793566
        tangentMode: 34
      - serializedVersion: 2
        time: 7
        value: 0.0006361403
        inSlope: -0.00012128658
        outSlope: -0.00012128658
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.x
    path: 
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 116.884
        inSlope: 12.901501
        outSlope: 12.901501
        tangentMode: 34
      - serializedVersion: 2
        time: 4
        value: 168.49
        inSlope: 6.4037633
        outSlope: 6.4037633
        tangentMode: 34
      - serializedVersion: 2
        time: 7
        value: 168.20808
        inSlope: -0.09397379
        outSlope: -0.09397379
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.y
    path: 
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 10
        inSlope: -5
        outSlope: -5
        tangentMode: 34
      - serializedVersion: 2
        time: 4
        value: -10
        inSlope: -2.5
        outSlope: -2.5
        tangentMode: 34
      - serializedVersion: 2
        time: 7
        value: -10
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: localEulerAnglesRaw.z
    path: 
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: -2.5
        inSlope: 0.16128571
        outSlope: 0.25
        tangentMode: 69
      - serializedVersion: 2
        time: 4
        value: -1.5
        inSlope: 0.10635714
        outSlope: 0.10635714
        tangentMode: 0
      - serializedVersion: 2
        time: 7
        value: -1.5
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.x
    path: 
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 0.2
        inSlope: -0.06771429
        outSlope: -0.08
        tangentMode: 69
      - serializedVersion: 2
        time: 4
        value: -0.12
        inSlope: -0.033857144
        outSlope: -0.033857144
        tangentMode: 0
      - serializedVersion: 2
        time: 7
        value: -0.11999997
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.y
    path: 
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 13.092
        inSlope: -0.17885712
        outSlope: -0.023000002
        tangentMode: 69
      - serializedVersion: 2
        time: 4
        value: 13
        inSlope: -0.06915926
        outSlope: -0.06915926
        tangentMode: 0
      - serializedVersion: 2
        time: 7
        value: 12
        inSlope: -0.33333334
        outSlope: 0
        tangentMode: 69
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalPosition.z
    path: 
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 2
        time: 0
        value: 30
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - serializedVersion: 2
        time: 1
        value: 30
        inSlope: 0
        outSlope: 0
        tangentMode: 0
      - serializedVersion: 2
        time: 4
        value: 60
        inSlope: 5
        outSlope: 5
        tangentMode: 34
      - serializedVersion: 2
        time: 7
        value: 60
        inSlope: 0
        outSlope: 0
        tangentMode: 34
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: FOV
    path: 
    classID: 114
    script: {fileID: 11500000, guid: df78f1d5dd5160441ab435e4fcc6981b, type: 3}
  m_EulerEditorCurves:
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.x
    path: 
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.y
    path: 
    classID: 4
    script: {fileID: 0}
  - curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    attribute: m_LocalEulerAngles.z
    path: 
    classID: 4
    script: {fileID: 0}
  m_HasGenericRootTransform: 1
  m_HasMotionFloatCurves: 0
  m_GenerateMotionCurves: 0
  m_Events: []

{ "pid": 13400, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 13400, "tid": 1, "ts": 1751348734769202, "dur": 2390, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 13400, "tid": 1, "ts": 1751348734771595, "dur": 100072, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 13400, "tid": 1, "ts": 1751348734871669, "dur": 10190, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 13400, "tid": 702, "ts": 1751348736666155, "dur": 46, "ph": "X", "name": "", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734769166, "dur": 48363, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734817530, "dur": 1847737, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734817547, "dur": 125, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734817683, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734817688, "dur": 466, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734818160, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734818163, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734818236, "dur": 13, "ph": "X", "name": "ProcessMessages 41", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734818252, "dur": 4346, "ph": "X", "name": "ReadAsync 41", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734822608, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734822614, "dur": 117, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734822741, "dur": 6, "ph": "X", "name": "ProcessMessages 515", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734822750, "dur": 108, "ph": "X", "name": "ReadAsync 515", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734822864, "dur": 2, "ph": "X", "name": "ProcessMessages 874", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734822868, "dur": 78, "ph": "X", "name": "ReadAsync 874", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734822953, "dur": 4, "ph": "X", "name": "ProcessMessages 536", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734822959, "dur": 103, "ph": "X", "name": "ReadAsync 536", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734823069, "dur": 4, "ph": "X", "name": "ProcessMessages 617", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734823076, "dur": 98, "ph": "X", "name": "ReadAsync 617", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734823183, "dur": 5, "ph": "X", "name": "ProcessMessages 603", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734823191, "dur": 93, "ph": "X", "name": "ReadAsync 603", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734823291, "dur": 3, "ph": "X", "name": "ProcessMessages 316", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734823297, "dur": 70, "ph": "X", "name": "ReadAsync 316", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734823373, "dur": 2, "ph": "X", "name": "ProcessMessages 407", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734823377, "dur": 53, "ph": "X", "name": "ReadAsync 407", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734823435, "dur": 2, "ph": "X", "name": "ProcessMessages 147", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734823438, "dur": 63, "ph": "X", "name": "ReadAsync 147", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734823508, "dur": 3, "ph": "X", "name": "ProcessMessages 256", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734823513, "dur": 118, "ph": "X", "name": "ReadAsync 256", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734823642, "dur": 4, "ph": "X", "name": "ProcessMessages 340", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734823649, "dur": 52, "ph": "X", "name": "ReadAsync 340", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734823705, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734823708, "dur": 55, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734823768, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734823770, "dur": 80, "ph": "X", "name": "ReadAsync 44", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734823858, "dur": 3, "ph": "X", "name": "ProcessMessages 240", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734823864, "dur": 104, "ph": "X", "name": "ReadAsync 240", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734823975, "dur": 3, "ph": "X", "name": "ProcessMessages 581", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734823981, "dur": 91, "ph": "X", "name": "ReadAsync 581", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734824079, "dur": 3, "ph": "X", "name": "ProcessMessages 305", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734824085, "dur": 78, "ph": "X", "name": "ReadAsync 305", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734824167, "dur": 2, "ph": "X", "name": "ProcessMessages 303", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734824170, "dur": 52, "ph": "X", "name": "ReadAsync 303", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734824228, "dur": 2, "ph": "X", "name": "ProcessMessages 187", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734824231, "dur": 66, "ph": "X", "name": "ReadAsync 187", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734824311, "dur": 5, "ph": "X", "name": "ProcessMessages 295", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734824320, "dur": 95, "ph": "X", "name": "ReadAsync 295", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734824422, "dur": 3, "ph": "X", "name": "ProcessMessages 296", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734824428, "dur": 63, "ph": "X", "name": "ReadAsync 296", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734824495, "dur": 101, "ph": "X", "name": "ProcessMessages 215", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734824599, "dur": 73, "ph": "X", "name": "ReadAsync 215", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734824679, "dur": 4, "ph": "X", "name": "ProcessMessages 680", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734824685, "dur": 108, "ph": "X", "name": "ReadAsync 680", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734824802, "dur": 12, "ph": "X", "name": "ProcessMessages 218", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734824818, "dur": 93, "ph": "X", "name": "ReadAsync 218", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734824918, "dur": 3, "ph": "X", "name": "ProcessMessages 541", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734824925, "dur": 77, "ph": "X", "name": "ReadAsync 541", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734825006, "dur": 2, "ph": "X", "name": "ProcessMessages 303", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734825009, "dur": 58, "ph": "X", "name": "ReadAsync 303", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734825074, "dur": 3, "ph": "X", "name": "ProcessMessages 157", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734825080, "dur": 80, "ph": "X", "name": "ReadAsync 157", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734825167, "dur": 3, "ph": "X", "name": "ProcessMessages 432", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734825173, "dur": 67, "ph": "X", "name": "ReadAsync 432", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734825247, "dur": 3, "ph": "X", "name": "ProcessMessages 59", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734825253, "dur": 48, "ph": "X", "name": "ReadAsync 59", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734825305, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734825307, "dur": 58, "ph": "X", "name": "ReadAsync 44", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734825373, "dur": 3, "ph": "X", "name": "ProcessMessages 246", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734825378, "dur": 77, "ph": "X", "name": "ReadAsync 246", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734825462, "dur": 4, "ph": "X", "name": "ProcessMessages 302", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734825468, "dur": 75, "ph": "X", "name": "ReadAsync 302", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734825550, "dur": 3, "ph": "X", "name": "ProcessMessages 292", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734825555, "dur": 61, "ph": "X", "name": "ReadAsync 292", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734825620, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734825623, "dur": 54, "ph": "X", "name": "ReadAsync 322", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734825683, "dur": 3, "ph": "X", "name": "ProcessMessages 60", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734825689, "dur": 81, "ph": "X", "name": "ReadAsync 60", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734825776, "dur": 3, "ph": "X", "name": "ProcessMessages 338", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734825782, "dur": 78, "ph": "X", "name": "ReadAsync 338", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734825868, "dur": 3, "ph": "X", "name": "ProcessMessages 280", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734825873, "dur": 85, "ph": "X", "name": "ReadAsync 280", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734825966, "dur": 3, "ph": "X", "name": "ProcessMessages 443", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734825972, "dur": 77, "ph": "X", "name": "ReadAsync 443", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734826058, "dur": 3, "ph": "X", "name": "ProcessMessages 296", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734826064, "dur": 113, "ph": "X", "name": "ReadAsync 296", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734826185, "dur": 4, "ph": "X", "name": "ProcessMessages 185", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734826192, "dur": 111, "ph": "X", "name": "ReadAsync 185", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734826312, "dur": 4, "ph": "X", "name": "ProcessMessages 628", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734826319, "dur": 83, "ph": "X", "name": "ReadAsync 628", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734826406, "dur": 2, "ph": "X", "name": "ProcessMessages 268", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734826410, "dur": 62, "ph": "X", "name": "ReadAsync 268", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734826479, "dur": 3, "ph": "X", "name": "ProcessMessages 282", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734826485, "dur": 78, "ph": "X", "name": "ReadAsync 282", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734826570, "dur": 3, "ph": "X", "name": "ProcessMessages 352", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734826576, "dur": 73, "ph": "X", "name": "ReadAsync 352", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734826656, "dur": 4, "ph": "X", "name": "ProcessMessages 284", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734826663, "dur": 74, "ph": "X", "name": "ReadAsync 284", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734826741, "dur": 2, "ph": "X", "name": "ProcessMessages 441", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734826744, "dur": 34, "ph": "X", "name": "ReadAsync 441", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734826781, "dur": 92, "ph": "X", "name": "ReadAsync 148", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734826880, "dur": 3, "ph": "X", "name": "ProcessMessages 21", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734826885, "dur": 93, "ph": "X", "name": "ReadAsync 21", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734826985, "dur": 3, "ph": "X", "name": "ProcessMessages 425", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734826991, "dur": 72, "ph": "X", "name": "ReadAsync 425", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734827067, "dur": 2, "ph": "X", "name": "ProcessMessages 288", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734827071, "dur": 73, "ph": "X", "name": "ReadAsync 288", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734827152, "dur": 3, "ph": "X", "name": "ProcessMessages 167", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734827159, "dur": 131, "ph": "X", "name": "ReadAsync 167", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734827298, "dur": 4, "ph": "X", "name": "ProcessMessages 516", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734827305, "dur": 134, "ph": "X", "name": "ReadAsync 516", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734827448, "dur": 5, "ph": "X", "name": "ProcessMessages 422", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734827456, "dur": 119, "ph": "X", "name": "ReadAsync 422", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734827580, "dur": 2, "ph": "X", "name": "ProcessMessages 568", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734827584, "dur": 81, "ph": "X", "name": "ReadAsync 568", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734827673, "dur": 4, "ph": "X", "name": "ProcessMessages 310", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734827680, "dur": 88, "ph": "X", "name": "ReadAsync 310", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734827772, "dur": 2, "ph": "X", "name": "ProcessMessages 580", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734827775, "dur": 68, "ph": "X", "name": "ReadAsync 580", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734827850, "dur": 3, "ph": "X", "name": "ProcessMessages 98", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734827857, "dur": 87, "ph": "X", "name": "ReadAsync 98", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734827948, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734827951, "dur": 74, "ph": "X", "name": "ReadAsync 533", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734828032, "dur": 3, "ph": "X", "name": "ProcessMessages 193", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734828039, "dur": 142, "ph": "X", "name": "ReadAsync 193", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734828189, "dur": 4, "ph": "X", "name": "ProcessMessages 479", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734828196, "dur": 89, "ph": "X", "name": "ReadAsync 479", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734828291, "dur": 4, "ph": "X", "name": "ProcessMessages 317", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734828298, "dur": 64, "ph": "X", "name": "ReadAsync 317", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734828366, "dur": 1, "ph": "X", "name": "ProcessMessages 127", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734828368, "dur": 107, "ph": "X", "name": "ReadAsync 127", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734828483, "dur": 4, "ph": "X", "name": "ProcessMessages 282", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734828490, "dur": 138, "ph": "X", "name": "ReadAsync 282", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734828636, "dur": 5, "ph": "X", "name": "ProcessMessages 574", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734828644, "dur": 142, "ph": "X", "name": "ReadAsync 574", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734828794, "dur": 4, "ph": "X", "name": "ProcessMessages 449", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734828802, "dur": 104, "ph": "X", "name": "ReadAsync 449", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734828910, "dur": 2, "ph": "X", "name": "ProcessMessages 596", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734828915, "dur": 73, "ph": "X", "name": "ReadAsync 596", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734828996, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734829001, "dur": 123, "ph": "X", "name": "ReadAsync 240", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734829132, "dur": 4, "ph": "X", "name": "ProcessMessages 346", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734829139, "dur": 105, "ph": "X", "name": "ReadAsync 346", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734829251, "dur": 4, "ph": "X", "name": "ProcessMessages 479", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734829258, "dur": 98, "ph": "X", "name": "ReadAsync 479", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734829360, "dur": 2, "ph": "X", "name": "ProcessMessages 246", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734829364, "dur": 106, "ph": "X", "name": "ReadAsync 246", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734829478, "dur": 4, "ph": "X", "name": "ProcessMessages 262", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734829485, "dur": 128, "ph": "X", "name": "ReadAsync 262", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734829621, "dur": 3, "ph": "X", "name": "ProcessMessages 286", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734829628, "dur": 105, "ph": "X", "name": "ReadAsync 286", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734829740, "dur": 4, "ph": "X", "name": "ProcessMessages 607", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734829747, "dur": 115, "ph": "X", "name": "ReadAsync 607", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734829870, "dur": 4, "ph": "X", "name": "ProcessMessages 419", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734829877, "dur": 93, "ph": "X", "name": "ReadAsync 419", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734829977, "dur": 3, "ph": "X", "name": "ProcessMessages 277", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734829983, "dur": 125, "ph": "X", "name": "ReadAsync 277", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734830116, "dur": 12, "ph": "X", "name": "ProcessMessages 255", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734830131, "dur": 115, "ph": "X", "name": "ReadAsync 255", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734830255, "dur": 4, "ph": "X", "name": "ProcessMessages 69", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734830261, "dur": 179, "ph": "X", "name": "ReadAsync 69", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734830444, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734830447, "dur": 106, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734830561, "dur": 4, "ph": "X", "name": "ProcessMessages 402", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734830568, "dur": 141, "ph": "X", "name": "ReadAsync 402", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734830722, "dur": 5, "ph": "X", "name": "ProcessMessages 600", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734830730, "dur": 112, "ph": "X", "name": "ReadAsync 600", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734830850, "dur": 4, "ph": "X", "name": "ProcessMessages 499", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734830858, "dur": 91, "ph": "X", "name": "ReadAsync 499", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734830956, "dur": 3, "ph": "X", "name": "ProcessMessages 322", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734830963, "dur": 116, "ph": "X", "name": "ReadAsync 322", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734831087, "dur": 4, "ph": "X", "name": "ProcessMessages 258", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734831094, "dur": 134, "ph": "X", "name": "ReadAsync 258", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734831236, "dur": 2, "ph": "X", "name": "ProcessMessages 283", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734831241, "dur": 100, "ph": "X", "name": "ReadAsync 283", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734831348, "dur": 4, "ph": "X", "name": "ProcessMessages 602", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734831355, "dur": 89, "ph": "X", "name": "ReadAsync 602", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734831449, "dur": 2, "ph": "X", "name": "ProcessMessages 89", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734831452, "dur": 112, "ph": "X", "name": "ReadAsync 89", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734831572, "dur": 4, "ph": "X", "name": "ProcessMessages 384", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734831579, "dur": 140, "ph": "X", "name": "ReadAsync 384", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734831728, "dur": 5, "ph": "X", "name": "ProcessMessages 562", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734831736, "dur": 90, "ph": "X", "name": "ReadAsync 562", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734831832, "dur": 3, "ph": "X", "name": "ProcessMessages 173", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734831838, "dur": 88, "ph": "X", "name": "ReadAsync 173", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734831930, "dur": 2, "ph": "X", "name": "ProcessMessages 414", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734831933, "dur": 114, "ph": "X", "name": "ReadAsync 414", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734832056, "dur": 3, "ph": "X", "name": "ProcessMessages 214", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734832062, "dur": 131, "ph": "X", "name": "ReadAsync 214", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734832203, "dur": 5, "ph": "X", "name": "ProcessMessages 590", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734832212, "dur": 67, "ph": "X", "name": "ReadAsync 590", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734832283, "dur": 2, "ph": "X", "name": "ProcessMessages 477", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734832288, "dur": 58, "ph": "X", "name": "ReadAsync 477", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734832353, "dur": 3, "ph": "X", "name": "ProcessMessages 49", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734832359, "dur": 76, "ph": "X", "name": "ReadAsync 49", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734832444, "dur": 4, "ph": "X", "name": "ProcessMessages 289", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734832451, "dur": 63, "ph": "X", "name": "ReadAsync 289", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734832518, "dur": 2, "ph": "X", "name": "ProcessMessages 314", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734832526, "dur": 139, "ph": "X", "name": "ReadAsync 314", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734832673, "dur": 2, "ph": "X", "name": "ProcessMessages 291", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734832678, "dur": 100, "ph": "X", "name": "ReadAsync 291", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734832783, "dur": 2, "ph": "X", "name": "ProcessMessages 822", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734832787, "dur": 62, "ph": "X", "name": "ReadAsync 822", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734832853, "dur": 2, "ph": "X", "name": "ProcessMessages 433", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734832857, "dur": 75, "ph": "X", "name": "ReadAsync 433", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734832938, "dur": 4, "ph": "X", "name": "ProcessMessages 333", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734832945, "dur": 117, "ph": "X", "name": "ReadAsync 333", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734836231, "dur": 7, "ph": "X", "name": "ProcessMessages 584", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734836242, "dur": 1106, "ph": "X", "name": "ReadAsync 584", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734837356, "dur": 138, "ph": "X", "name": "ProcessMessages 13911", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734837500, "dur": 61, "ph": "X", "name": "ReadAsync 13911", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734837571, "dur": 6, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734837582, "dur": 1262, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734838954, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734838959, "dur": 1060, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734840025, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734840030, "dur": 2574, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734843023, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734843032, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734843226, "dur": 16, "ph": "X", "name": "ProcessMessages 104", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734843293, "dur": 398, "ph": "X", "name": "ReadAsync 104", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734843803, "dur": 5, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734843915, "dur": 484, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734844404, "dur": 6, "ph": "X", "name": "ProcessMessages 156", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734844413, "dur": 303, "ph": "X", "name": "ReadAsync 156", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734844802, "dur": 3, "ph": "X", "name": "ProcessMessages 60", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734845042, "dur": 268, "ph": "X", "name": "ReadAsync 60", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734845422, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734845428, "dur": 178, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734845611, "dur": 4, "ph": "X", "name": "ProcessMessages 56", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734845617, "dur": 1492, "ph": "X", "name": "ReadAsync 56", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734847209, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734847313, "dur": 278, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734847915, "dur": 506, "ph": "X", "name": "ProcessMessages 198", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734848426, "dur": 296, "ph": "X", "name": "ReadAsync 198", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734849033, "dur": 7956, "ph": "X", "name": "ProcessMessages 364", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734856999, "dur": 115, "ph": "X", "name": "ReadAsync 364", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734857120, "dur": 1364, "ph": "X", "name": "ProcessMessages 610", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348734858494, "dur": 277168, "ph": "X", "name": "ReadAsync 610", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348735135676, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348735135683, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348735135739, "dur": 34, "ph": "X", "name": "ProcessMessages 679", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348735135776, "dur": 13560, "ph": "X", "name": "ReadAsync 679", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348735149342, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348735149346, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348735149421, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348735149428, "dur": 1047, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348735150480, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348735150483, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348735150541, "dur": 29, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348735150573, "dur": 97365, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348735247947, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348735247954, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348735248050, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348735248058, "dur": 1249, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348735249316, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348735249321, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348735249404, "dur": 41, "ph": "X", "name": "ProcessMessages 50", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348735249449, "dur": 729627, "ph": "X", "name": "ReadAsync 50", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348735979097, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348735979104, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348735979208, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348735979218, "dur": 665116, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348736644343, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348736644348, "dur": 115, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348736644482, "dur": 56, "ph": "X", "name": "ProcessMessages 2633", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348736644544, "dur": 55, "ph": "X", "name": "ReadAsync 2633", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348736644605, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348736644610, "dur": 1856, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348736646476, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348736646482, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348736646524, "dur": 2, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 13400, "tid": 25769803776, "ts": 1751348736646529, "dur": 18715, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 13400, "tid": 702, "ts": 1751348736666208, "dur": 11812, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 13400, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 13400, "tid": 21474836480, "ts": 1751348734769104, "dur": 112778, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 13400, "tid": 21474836480, "ts": 1751348734881884, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 13400, "tid": 21474836480, "ts": 1751348734881886, "dur": 63, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 13400, "tid": 702, "ts": 1751348736678025, "dur": 78, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 13400, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 13400, "tid": 17179869184, "ts": 1751348734765118, "dur": 1900218, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 13400, "tid": 17179869184, "ts": 1751348734765360, "dur": 3709, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 13400, "tid": 17179869184, "ts": 1751348736665349, "dur": 87, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 13400, "tid": 17179869184, "ts": 1751348736665369, "dur": 23, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 13400, "tid": 17179869184, "ts": 1751348736665438, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 13400, "tid": 702, "ts": 1751348736678113, "dur": 69, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1751348734817559, "dur":84, "ph":"X", "name": "IPC_Client_InitializeAndConnectToParent",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751348734817743, "dur":2898, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751348734820655, "dur":676, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751348734821444, "dur":1226, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751348734822845, "dur":75, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_3F615B2CD094388F.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734822960, "dur":77, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_DD1A64662AB2EEE1.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734823078, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1F4164359B0F1C2F.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734823167, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_6691A7F2FCA50E77.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734823277, "dur":84, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E131416B0BD83AC6.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734823375, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_40CA959D70D4461E.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734823444, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_AA29B6A11E640A63.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734823517, "dur":98, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_DC97BFF5203733EF.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734823625, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_AF7B180A90C4DB84.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734823696, "dur":94, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_618B3886BAD936BE.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734823920, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_D79DA43A6C4320A9.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734823995, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_0DD02120343DEAE3.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734824065, "dur":102, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_BEA25D59242CD0BD.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734824178, "dur":88, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_9B070CD48E121FF1.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734824277, "dur":89, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_5E63226C05E60A63.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734824376, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_5052AD45BBA3738E.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734824445, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3808EA5EE8B9C374.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734824533, "dur":106, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_643D92A01AF9D37A.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734824650, "dur":88, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_B0867F543300D220.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734824749, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_661F12A898C05437.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734824809, "dur":87, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_8403F9361EA02785.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734824906, "dur":87, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_387FC8ADF0601190.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734825004, "dur":103, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C7AA02CA7F374981.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734825117, "dur":90, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_9EE980C253D6A9A7.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734825217, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_93DCDEE9DEE5D1AC.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734825285, "dur":87, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6D05EFF427F01585.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734825471, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_29C05456CBAFE676.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734825551, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_0BCEDB16477CDAC4.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734825611, "dur":120, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_8EC677C52DC49AFA.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734825742, "dur":91, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_BF626035856B433F.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734825843, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_9DEBA6BB330C53E7.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734825916, "dur":89, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_59C0F0D9CA68870D.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734826019, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_D463EC1B32D16EDE.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734826093, "dur":88, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_4AB7AB16DC48CC38.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734826192, "dur":87, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_38A2919050B15439.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734826290, "dur":71, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_91AF51B67A3AA61F.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734826369, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_6E5A65FFC58DA42D.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734826427, "dur":106, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B5651178AA90B44B.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734826544, "dur":88, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_261556851D3B23B2.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734826642, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_945787BCF828CC39.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734826706, "dur":86, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C9C29C66E9057584.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734826803, "dur":72, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_11AAD2D878892EA2.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734826882, "dur":102, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_C72B18C5EA43D3DC.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734827029, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_C511BE0634F804BB.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734827094, "dur":98, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_280AA007BEACDA98.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734827202, "dur":88, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_B9A372E0ADBCF042.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734827300, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_3AD4F95A2CDB3E3B.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734827362, "dur":96, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_7A7DF710AFAEDB06.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734827469, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_0844AD3841D8B3E3.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734827530, "dur":98, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B37C4FB38B0D23A8.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734827638, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_67F60D54A1E44C7C.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734827699, "dur":98, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_103705979AC6E0CE.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734827807, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_F825EB11843D5C3C.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734827880, "dur":100, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_0D96B1407E3DC479.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734827991, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E50243D2AE5AE01D.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734828059, "dur":96, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_61F1B4EFC6980E0D.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734828166, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_9906D3291E9C4B92.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734828233, "dur":98, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_DC6D18346848EB0E.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734828342, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_21ECB020988DD9FE.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734828507, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_A8E93876951A4405.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734828580, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_24B2CF62E51A4B38.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734828650, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_8DA71FDFCE95C3B3.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734828709, "dur":103, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_57801C939D0EB92F.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734828823, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_C065E2E457D01111.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734828890, "dur":90, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734828990, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_AFC2E85BC2EDBC0B.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734829054, "dur":110, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1751348734829176, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":0, "ts":1751348734829237, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.rsp" }}
,{ "pid":12345, "tid":0, "ts":1751348734829310, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1751348734829370, "dur":103, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734829496, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":0, "ts":1751348734829661, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ref.dll_A6FF0182FCD80C46.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734829723, "dur":73, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1751348734829804, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":0, "ts":1751348734829896, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.rsp" }}
,{ "pid":12345, "tid":0, "ts":1751348734829961, "dur":101, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1751348734830074, "dur":95, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734830182, "dur":89, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":0, "ts":1751348734830581, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_0FD4FE2CDCC99BDF.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734830651, "dur":75, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1751348734830734, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":0, "ts":1751348734830794, "dur":109, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.rsp" }}
,{ "pid":12345, "tid":0, "ts":1751348734830914, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1751348734830981, "dur":88, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734831081, "dur":88, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":0, "ts":1751348734831285, "dur":77, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.ref.dll_56E5DE835ED3108E.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734831370, "dur":62, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1751348734831443, "dur":102, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":0, "ts":1751348734831589, "dur":60, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.rsp" }}
,{ "pid":12345, "tid":0, "ts":1751348734831657, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1751348734831731, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.dll.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734831792, "dur":103, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":0, "ts":1751348734831979, "dur":63, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_F2CC0B57F8094615.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734832051, "dur":102, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1751348734832165, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":0, "ts":1751348734832226, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.rsp" }}
,{ "pid":12345, "tid":0, "ts":1751348734832292, "dur":104, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1751348734832408, "dur":101, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734832521, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":0, "ts":1751348734832634, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_A2E97B59907D8CED.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734832701, "dur":92, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1751348734832898, "dur":79, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1751348734832984, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.dll.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751348734833149, "dur":70, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1751348734822732, "dur":12229, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751348734834964, "dur":1809810, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751348736644783, "dur":838, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751348736645621, "dur":69, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751348736645757, "dur":80, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751348736645864, "dur":65, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751348736646151, "dur":90, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751348736646510, "dur":93, "ph":"X", "name": "BuildQueueDestroyTail",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751348736646653, "dur":12477, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1751348734823341, "dur":11716, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751348734835060, "dur":149, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_0BCEDB16477CDAC4.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1751348734835262, "dur":113, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_5E63226C05E60A63.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1751348734835461, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751348734835532, "dur":205, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_C065E2E457D01111.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1751348734835781, "dur":119, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_261556851D3B23B2.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1751348734836001, "dur":65, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":1, "ts":1751348734836069, "dur":244, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751348734836314, "dur":143, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":1, "ts":1751348734836458, "dur":691, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751348734837467, "dur":546, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Control\\Sequence.cs" }}
,{ "pid":12345, "tid":1, "ts":1751348734837150, "dur":1165, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751348734838316, "dur":832, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751348734839149, "dur":743, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751348734839893, "dur":354, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751348734840248, "dur":2565, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751348734842814, "dur":84, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751348734842898, "dur":1017, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751348734843916, "dur":380, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751348734844299, "dur":771, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751348734845082, "dur":404, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751348734845552, "dur":2596, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.pdb" }}
,{ "pid":12345, "tid":1, "ts":1751348734848150, "dur":3259, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751348734851418, "dur":103, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751348734851555, "dur":1311, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.pdb" }}
,{ "pid":12345, "tid":1, "ts":1751348734852867, "dur":68, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751348734852936, "dur":1791769, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751348734823235, "dur":11776, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751348734835018, "dur":209, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_9DEBA6BB330C53E7.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1751348734835230, "dur":116, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751348734835366, "dur":179, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_DC97BFF5203733EF.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1751348734835547, "dur":106, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751348734835669, "dur":169, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_AF7B180A90C4DB84.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1751348734835840, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751348734835930, "dur":195, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.rsp" }}
,{ "pid":12345, "tid":2, "ts":1751348734836126, "dur":196, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751348734836340, "dur":940, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751348734837472, "dur":550, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Connections\\UnitRelation.cs" }}
,{ "pid":12345, "tid":2, "ts":1751348734837281, "dur":1183, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751348734838807, "dur":663, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Internal\\fsOption.cs" }}
,{ "pid":12345, "tid":2, "ts":1751348734838465, "dur":1431, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751348734839897, "dur":89, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751348734839987, "dur":146, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751348734840140, "dur":2677, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751348734842817, "dur":76, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751348734842894, "dur":1007, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751348734843930, "dur":232, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751348734844222, "dur":3032, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751348734847262, "dur":853, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751348734848217, "dur":1924, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.pdb" }}
,{ "pid":12345, "tid":2, "ts":1751348734850143, "dur":1331, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751348734851474, "dur":1469, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751348734852943, "dur":1791839, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751348734823370, "dur":11703, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751348734835081, "dur":191, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_29C05456CBAFE676.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1751348734835274, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751348734835381, "dur":210, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_AA29B6A11E640A63.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1751348734835593, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751348734835681, "dur":173, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B37C4FB38B0D23A8.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1751348734835856, "dur":252, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751348734836155, "dur":244, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751348734837451, "dur":543, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector3\\Vector3Sum.cs" }}
,{ "pid":12345, "tid":3, "ts":1751348734836439, "dur":1556, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751348734837996, "dur":1113, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751348734839110, "dur":1210, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751348734840321, "dur":2482, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751348734842804, "dur":91, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751348734842896, "dur":1031, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751348734843928, "dur":367, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751348734844295, "dur":785, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751348734845081, "dur":1940, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751348734847029, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751348734847103, "dur":184, "ph":"X", "name": "EmitNodeStart",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751348734847291, "dur":2015, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.dll" }}
,{ "pid":12345, "tid":3, "ts":1751348734849309, "dur":2135, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751348734851444, "dur":1472, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751348734852917, "dur":29228, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751348734883703, "dur":396, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":3, "ts":1751348734882147, "dur":1956, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751348734884103, "dur":1760661, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751348734823610, "dur":11548, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751348734835167, "dur":373, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C7AA02CA7F374981.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1751348734835543, "dur":79, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751348734835638, "dur":115, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_103705979AC6E0CE.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1751348734835791, "dur":94, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B5651178AA90B44B.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1751348734836058, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751348734836131, "dur":152, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":4, "ts":1751348734836288, "dur":126, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751348734836416, "dur":1149, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.rsp2" }}
,{ "pid":12345, "tid":4, "ts":1751348734837567, "dur":572, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\StaticFunctionInvokerBase.cs" }}
,{ "pid":12345, "tid":4, "ts":1751348734838440, "dur":711, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\StaticActionInvoker_1.cs" }}
,{ "pid":12345, "tid":4, "ts":1751348734837567, "dur":2267, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751348734839835, "dur":969, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751348734840805, "dur":2010, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751348734842816, "dur":69, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751348734842886, "dur":1036, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751348734843924, "dur":381, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751348734844307, "dur":782, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751348734845091, "dur":2168, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751348734847261, "dur":947, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751348734848208, "dur":3270, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751348734851480, "dur":1459, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751348734852939, "dur":1791782, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751348734823141, "dur":11853, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751348734834998, "dur":183, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_59C0F0D9CA68870D.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1751348734835235, "dur":154, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_643D92A01AF9D37A.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1751348734835391, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751348734835492, "dur":121, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1751348734835615, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751348734835700, "dur":58, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1751348734835761, "dur":152, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_11AAD2D878892EA2.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1751348734835914, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751348734836033, "dur":80, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_11AAD2D878892EA2.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1751348734836120, "dur":177, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751348734836305, "dur":1097, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751348734837464, "dur":579, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Variables\\VariableDeclarationCollection.cs" }}
,{ "pid":12345, "tid":5, "ts":1751348734837403, "dur":1518, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751348734838922, "dur":1169, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751348734840093, "dur":1102, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751348734841196, "dur":1706, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751348734842902, "dur":1031, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751348734843933, "dur":351, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751348734844285, "dur":802, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751348734845088, "dur":2205, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751348734847294, "dur":928, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751348734848222, "dur":3269, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751348734851492, "dur":1449, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751348734852941, "dur":1791769, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751348734823226, "dur":11805, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751348734835038, "dur":260, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_BF626035856B433F.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1751348734835300, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751348734835402, "dur":226, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B857BD766991C41C.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1751348734835630, "dur":62, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751348734835729, "dur":193, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_280AA007BEACDA98.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1751348734835996, "dur":219, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.rsp" }}
,{ "pid":12345, "tid":6, "ts":1751348734836217, "dur":230, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751348734836962, "dur":1403, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Generic\\GenericSum.cs" }}
,{ "pid":12345, "tid":6, "ts":1751348734836482, "dur":2237, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751348734838719, "dur":858, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751348734839578, "dur":1140, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751348734840719, "dur":2090, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751348734842810, "dur":63, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751348734842875, "dur":176, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1751348734843107, "dur":1030, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1751348734844138, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751348734844301, "dur":772, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751348734845074, "dur":64, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751348734845141, "dur":202, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1751348734845402, "dur":1429, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1751348734846834, "dur":125, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751348734847084, "dur":192, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_F2CC0B57F8094615.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1751348734847278, "dur":934, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751348734848213, "dur":3212, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751348734851426, "dur":1520, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751348734852947, "dur":1791802, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751348734823328, "dur":11717, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751348734835049, "dur":132, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_8EC677C52DC49AFA.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1751348734835234, "dur":111, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_B0867F543300D220.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1751348734835418, "dur":499, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1F4164359B0F1C2F.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1751348734835919, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751348734835987, "dur":93, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":7, "ts":1751348734836081, "dur":238, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751348734836332, "dur":609, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751348734837487, "dur":551, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Logic\\And.cs" }}
,{ "pid":12345, "tid":7, "ts":1751348734836942, "dur":1390, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751348734838333, "dur":1073, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751348734839407, "dur":1384, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751348734840792, "dur":2019, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751348734842812, "dur":95, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751348734842908, "dur":1022, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751348734843931, "dur":333, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751348734844275, "dur":105, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751348734844426, "dur":3560, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll" }}
,{ "pid":12345, "tid":7, "ts":1751348734847990, "dur":132, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751348734848122, "dur":3284, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751348734851413, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751348734851510, "dur":1428, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":7, "ts":1751348734852940, "dur":1791788, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751348734823073, "dur":11902, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751348734834989, "dur":192, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_D463EC1B32D16EDE.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751348734835240, "dur":112, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3808EA5EE8B9C374.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751348734835409, "dur":127, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_FFA94AF822882F93.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751348734835571, "dur":60, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_FFA94AF822882F93.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751348734835634, "dur":172, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_F825EB11843D5C3C.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751348734835808, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751348734835919, "dur":62, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":8, "ts":1751348734835983, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751348734836048, "dur":248, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":8, "ts":1751348734836297, "dur":57, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.rsp" }}
,{ "pid":12345, "tid":8, "ts":1751348734837447, "dur":521, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Variables\\Obsolete\\IApplicationVariableUnit.cs" }}
,{ "pid":12345, "tid":8, "ts":1751348734836416, "dur":2125, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751348734838541, "dur":664, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751348734839206, "dur":835, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751348734840089, "dur":1268, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751348734841358, "dur":1461, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751348734842821, "dur":79, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751348734842901, "dur":1009, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751348734843912, "dur":366, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751348734844279, "dur":788, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751348734845072, "dur":461, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751348734849116, "dur":111, "ph":"X", "name": "EmitNodeFinish",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751348734845593, "dur":3651, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.dll" }}
,{ "pid":12345, "tid":8, "ts":1751348734849247, "dur":2198, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751348734851445, "dur":1483, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751348734852928, "dur":1791771, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751348734823410, "dur":11683, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751348734835101, "dur":285, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6D05EFF427F01585.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1751348734835388, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751348734835489, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751348734835569, "dur":214, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_A8E93876951A4405.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1751348734835835, "dur":102, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_38A2919050B15439.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1751348734835938, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751348734836019, "dur":103, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751348734836123, "dur":105, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":9, "ts":1751348734836246, "dur":149, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751348734836411, "dur":733, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751348734837466, "dur":554, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Application\\OnApplicationResume.cs" }}
,{ "pid":12345, "tid":9, "ts":1751348734837145, "dur":1117, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751348734838262, "dur":1130, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751348734839393, "dur":1204, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751348734840598, "dur":2197, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751348734842801, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751348734843553, "dur":4153, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.pdb" }}
,{ "pid":12345, "tid":9, "ts":1751348734847709, "dur":603, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751348734848314, "dur":3193, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751348734851508, "dur":1423, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751348734852931, "dur":1791786, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751348734823483, "dur":11630, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751348734835118, "dur":163, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_93DCDEE9DEE5D1AC.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1751348734835339, "dur":121, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_618B3886BAD936BE.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1751348734835462, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751348734835561, "dur":195, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_24B2CF62E51A4B38.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1751348734835758, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751348734835829, "dur":180, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_91AF51B67A3AA61F.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1751348734836011, "dur":134, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751348734836188, "dur":81, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.rsp" }}
,{ "pid":12345, "tid":10, "ts":1751348734836271, "dur":161, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751348734837470, "dur":571, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector3\\DeprecatedVector3Add.cs" }}
,{ "pid":12345, "tid":10, "ts":1751348734836455, "dur":1682, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751348734838138, "dur":1089, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751348734839228, "dur":1270, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751348734840499, "dur":2389, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751348734842889, "dur":1035, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751348734843925, "dur":348, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751348734844283, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751348734844442, "dur":3237, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.pdb" }}
,{ "pid":12345, "tid":10, "ts":1751348734847683, "dur":447, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751348734848131, "dur":3288, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751348734851421, "dur":1508, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751348734852930, "dur":1791783, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751348734823526, "dur":11608, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751348734835141, "dur":238, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_9EE980C253D6A9A7.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1751348734835381, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751348734835466, "dur":295, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_23EC6FFA1F64C6FB.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1751348734835815, "dur":106, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_6E5A65FFC58DA42D.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1751348734835922, "dur":116, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751348734836161, "dur":57, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.rsp" }}
,{ "pid":12345, "tid":11, "ts":1751348734836219, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751348734836400, "dur":785, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751348734837484, "dur":547, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Collections\\Lists\\MergeLists.cs" }}
,{ "pid":12345, "tid":11, "ts":1751348734837186, "dur":1393, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751348734838579, "dur":719, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751348734839299, "dur":1513, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751348734840812, "dur":2021, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751348734842833, "dur":58, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751348734842892, "dur":1021, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751348734843914, "dur":362, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751348734844277, "dur":806, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751348734845083, "dur":2292, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751348734847377, "dur":740, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751348734848118, "dur":3296, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751348734851415, "dur":1503, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751348734852918, "dur":31193, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751348734884112, "dur":1760650, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751348734823657, "dur":11521, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751348734835184, "dur":179, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_387FC8ADF0601190.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1751348734835433, "dur":217, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_DD1A64662AB2EEE1.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1751348734835697, "dur":289, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_7A7DF710AFAEDB06.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1751348734835987, "dur":147, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751348734836134, "dur":111, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_7A7DF710AFAEDB06.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1751348734836247, "dur":156, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751348734836404, "dur":1175, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":12, "ts":1751348734837581, "dur":831, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\InstanceFunctionInvoker_3.cs" }}
,{ "pid":12345, "tid":12, "ts":1751348734837581, "dur":1546, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751348734839128, "dur":743, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751348734839872, "dur":761, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751348734840634, "dur":2249, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751348734842884, "dur":1036, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751348734843921, "dur":368, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751348734844289, "dur":795, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751348734845084, "dur":2179, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751348734847263, "dur":899, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751348734848163, "dur":3272, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751348734851436, "dur":1498, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751348734852934, "dur":1791797, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751348734823742, "dur":11451, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751348734835198, "dur":140, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_8403F9361EA02785.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1751348734835404, "dur":139, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_6691A7F2FCA50E77.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1751348734835592, "dur":246, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_9906D3291E9C4B92.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1751348734835839, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751348734836000, "dur":92, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":13, "ts":1751348734836151, "dur":61, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.rsp" }}
,{ "pid":12345, "tid":13, "ts":1751348734836213, "dur":324, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751348734836538, "dur":999, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.rsp" }}
,{ "pid":12345, "tid":13, "ts":1751348734837539, "dur":582, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Unity\\ISingleton.cs" }}
,{ "pid":12345, "tid":13, "ts":1751348734837539, "dur":1632, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751348734839172, "dur":945, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751348734840118, "dur":505, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751348734840624, "dur":2207, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751348734842832, "dur":58, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751348734842890, "dur":1008, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751348734843912, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751348734844045, "dur":3176, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":13, "ts":1751348734847234, "dur":949, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751348734848184, "dur":3256, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751348734851441, "dur":1485, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751348734852927, "dur":1791774, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751348734823830, "dur":11377, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751348734835212, "dur":142, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_67F60D54A1E44C7C.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1751348734835356, "dur":169, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751348734835540, "dur":118, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_57801C939D0EB92F.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1751348734835741, "dur":112, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_C511BE0634F804BB.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1751348734835973, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751348734836046, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751348734836138, "dur":288, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.rsp2" }}
,{ "pid":12345, "tid":14, "ts":1751348734836428, "dur":798, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751348734837467, "dur":596, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Codebase\\MemberUnit.cs" }}
,{ "pid":12345, "tid":14, "ts":1751348734837226, "dur":1340, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751348734838567, "dur":746, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751348734839313, "dur":1307, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751348734840620, "dur":2207, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751348734842827, "dur":85, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751348734842913, "dur":991, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751348734843905, "dur":362, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751348734844274, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751348734844362, "dur":3721, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":14, "ts":1751348734848126, "dur":3290, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751348734851417, "dur":1498, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751348734852920, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751348734852996, "dur":953, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.pdb" }}
,{ "pid":12345, "tid":14, "ts":1751348734853951, "dur":1790784, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751348734823901, "dur":11329, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751348734835237, "dur":284, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_661F12A898C05437.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1751348734835576, "dur":224, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_21ECB020988DD9FE.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1751348734835843, "dur":115, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_4AB7AB16DC48CC38.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1751348734836030, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751348734836157, "dur":75, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.rsp" }}
,{ "pid":12345, "tid":15, "ts":1751348734836233, "dur":148, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751348734836389, "dur":876, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751348734837469, "dur":536, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\EditorBinding\\PortKeyAttribute.cs" }}
,{ "pid":12345, "tid":15, "ts":1751348734837265, "dur":1259, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751348734838525, "dur":769, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsJsonParser.cs" }}
,{ "pid":12345, "tid":15, "ts":1751348734838525, "dur":1517, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751348734840043, "dur":1293, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751348734841336, "dur":1592, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751348734842929, "dur":996, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751348734843926, "dur":356, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751348734844283, "dur":794, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751348734845078, "dur":1945, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751348734847029, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751348734847145, "dur":1980, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.pdb" }}
,{ "pid":12345, "tid":15, "ts":1751348734849127, "dur":2304, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751348734851432, "dur":1481, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751348734852919, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751348734853021, "dur":979, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":15, "ts":1751348734854002, "dur":1790754, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751348734823976, "dur":11270, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751348734835251, "dur":142, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_5052AD45BBA3738E.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1751348734835444, "dur":115, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_3F615B2CD094388F.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1751348734835608, "dur":99, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E50243D2AE5AE01D.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1751348734835775, "dur":109, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_945787BCF828CC39.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1751348734835885, "dur":228, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751348734836159, "dur":232, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751348734836392, "dur":1161, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":16, "ts":1751348734837555, "dur":590, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Serialization\\Converters\\NamespaceConverter.cs" }}
,{ "pid":12345, "tid":16, "ts":1751348734837555, "dur":1846, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751348734839403, "dur":922, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751348734840326, "dur":2471, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751348734842798, "dur":79, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751348734842882, "dur":129, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.dll.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1751348734843053, "dur":721, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.dll (+2 others)" }}
,{ "pid":12345, "tid":16, "ts":1751348734843776, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751348734843949, "dur":409, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751348734844363, "dur":730, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751348734845094, "dur":1931, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751348734847025, "dur":1194, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751348734848220, "dur":3228, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751348734851449, "dur":1489, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751348734852938, "dur":1791799, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751348734824048, "dur":11220, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751348734835275, "dur":277, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_9B070CD48E121FF1.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751348734835614, "dur":238, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_0D96B1407E3DC479.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751348734835854, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751348734836036, "dur":165, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":17, "ts":1751348734836253, "dur":188, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751348734837485, "dur":564, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Round.cs" }}
,{ "pid":12345, "tid":17, "ts":1751348734836468, "dur":1661, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751348734838129, "dur":695, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751348734838825, "dur":1435, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751348734840260, "dur":2546, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751348734842807, "dur":68, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751348734842876, "dur":185, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.dll.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751348734843102, "dur":1083, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.dll (+2 others)" }}
,{ "pid":12345, "tid":17, "ts":1751348734844186, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751348734844308, "dur":787, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751348734845096, "dur":2257, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751348734847354, "dur":759, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751348734848126, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751348734848248, "dur":2344, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":17, "ts":1751348734850595, "dur":893, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751348734851489, "dur":1455, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751348734852945, "dur":1791799, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751348734824143, "dur":11141, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751348734835289, "dur":147, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_BEA25D59242CD0BD.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751348734835437, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751348734835548, "dur":120, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_8DA71FDFCE95C3B3.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751348734835705, "dur":113, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_3AD4F95A2CDB3E3B.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751348734836061, "dur":259, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.rsp2" }}
,{ "pid":12345, "tid":18, "ts":1751348734836322, "dur":641, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751348734837462, "dur":928, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Events\\ManualEventUnit.cs" }}
,{ "pid":12345, "tid":18, "ts":1751348734836963, "dur":1618, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751348734838581, "dur":763, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751348734839344, "dur":1166, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751348734840511, "dur":2281, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751348734843580, "dur":4325, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":18, "ts":1751348734847908, "dur":211, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751348734848121, "dur":3306, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751348734851429, "dur":1491, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751348734852920, "dur":296257, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751348735149235, "dur":239, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\1300b0aP.dag\\Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":18, "ts":1751348735149180, "dur":296, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":18, "ts":1751348735149519, "dur":1139, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":18, "ts":1751348735150662, "dur":1494108, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751348734824180, "dur":11127, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751348734835315, "dur":271, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_0DD02120343DEAE3.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1751348734835588, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751348734835690, "dur":146, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_0844AD3841D8B3E3.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1751348734835882, "dur":80, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.rsp" }}
,{ "pid":12345, "tid":19, "ts":1751348734835963, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751348734836075, "dur":260, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751348734837456, "dur":560, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\FontFeatureCommonGSUB.cs" }}
,{ "pid":12345, "tid":19, "ts":1751348734836389, "dur":1666, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751348734838056, "dur":1219, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751348734839275, "dur":1435, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751348734840711, "dur":2114, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751348734842826, "dur":80, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751348734842906, "dur":1001, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751348734843908, "dur":362, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751348734844278, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751348734844407, "dur":3062, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Timeline.pdb" }}
,{ "pid":12345, "tid":19, "ts":1751348734847471, "dur":722, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751348734848194, "dur":3275, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751348734851470, "dur":1481, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751348734852951, "dur":1791774, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751348734824237, "dur":11086, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751348734835328, "dur":201, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_D79DA43A6C4320A9.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1751348734835583, "dur":422, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_DC6D18346848EB0E.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1751348734836006, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751348734836072, "dur":243, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751348734836323, "dur":803, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751348734837486, "dur":541, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnPointerClick.cs" }}
,{ "pid":12345, "tid":20, "ts":1751348734837127, "dur":1274, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751348734838401, "dur":1644, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751348734840047, "dur":1801, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751348734841849, "dur":979, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751348734842830, "dur":51, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751348734842882, "dur":1052, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751348734843934, "dur":390, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751348734844325, "dur":773, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751348734845099, "dur":2130, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751348734847231, "dur":1077, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751348734848310, "dur":3162, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751348734851472, "dur":1464, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751348734852937, "dur":1791803, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751348734824324, "dur":11059, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751348734835387, "dur":224, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_40CA959D70D4461E.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1751348734835613, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751348734835724, "dur":205, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_B9A372E0ADBCF042.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1751348734835932, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751348734836034, "dur":1556, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_B9A372E0ADBCF042.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1751348734837593, "dur":1366, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1751348734839011, "dur":3644, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":21, "ts":1751348734842657, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751348734842885, "dur":167, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1751348734843095, "dur":1884, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll (+2 others)" }}
,{ "pid":12345, "tid":21, "ts":1751348734845120, "dur":95, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1751348734845244, "dur":2748, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll (+2 others)" }}
,{ "pid":12345, "tid":21, "ts":1751348734847994, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751348734848161, "dur":178, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.dll.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1751348734848375, "dur":2838, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.dll (+2 others)" }}
,{ "pid":12345, "tid":21, "ts":1751348734851215, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751348734851423, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751348734851503, "dur":199, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.dll.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1751348734851755, "dur":1018, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.dll (+2 others)" }}
,{ "pid":12345, "tid":21, "ts":1751348734852774, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751348734852958, "dur":147, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1751348734853136, "dur":555, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll (+2 others)" }}
,{ "pid":12345, "tid":21, "ts":1751348734854227, "dur":86, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751348734854899, "dur":280928, "ph":"X", "name": "Csc",  "args": { "detail":"Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll (+2 others)" }}
,{ "pid":12345, "tid":21, "ts":1751348735149164, "dur":98817, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\1300b0aP.dag\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":21, "ts":1751348735149136, "dur":98848, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":21, "ts":1751348735248031, "dur":1475, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":21, "ts":1751348735249567, "dur":138979, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":21, "ts":1751348735388603, "dur":165583, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.InputSystem.dll" }}
,{ "pid":12345, "tid":21, "ts":1751348735554239, "dur":177, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.InputSystem.ForUI.dll" }}
,{ "pid":12345, "tid":21, "ts":1751348735554455, "dur":149, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":21, "ts":1751348735554638, "dur":735, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":21, "ts":1751348735555407, "dur":17890, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":21, "ts":1751348735573347, "dur":108075, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":21, "ts":1751348735681488, "dur":47728, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":21, "ts":1751348735729276, "dur":73230, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":21, "ts":1751348735802544, "dur":176445, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":21, "ts":1751348735249529, "dur":729473, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Player/RuntimeInitializeOnLoads.json (+1 other)" }}
,{ "pid":12345, "tid":21, "ts":1751348735979985, "dur":664464, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Player/RuntimeInitializeOnLoads.json (+1 other)" }}
,{ "pid":12345, "tid":22, "ts":1751348734824363, "dur":11033, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751348734835401, "dur":747, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E131416B0BD83AC6.mvfrm" }}
,{ "pid":12345, "tid":22, "ts":1751348734836149, "dur":197, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751348734836362, "dur":585, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751348734837483, "dur":541, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Physics2D\\OnTriggerEnter2D.cs" }}
,{ "pid":12345, "tid":22, "ts":1751348734836948, "dur":1205, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751348734838154, "dur":760, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751348734838915, "dur":692, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751348734839608, "dur":928, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751348734840537, "dur":2286, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751348734842824, "dur":79, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751348734842904, "dur":1025, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751348734843929, "dur":380, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751348734844309, "dur":782, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751348734845092, "dur":1935, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751348734847028, "dur":1100, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751348734848129, "dur":3283, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751348734851413, "dur":1519, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751348734852933, "dur":1791868, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751348734824474, "dur":10956, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751348734835435, "dur":239, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_F8D420302DB19758.mvfrm" }}
,{ "pid":12345, "tid":23, "ts":1751348734835675, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751348734835751, "dur":201, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_C72B18C5EA43D3DC.mvfrm" }}
,{ "pid":12345, "tid":23, "ts":1751348734835954, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751348734836084, "dur":248, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751348734836359, "dur":1085, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751348734837445, "dur":600, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Utilities\\IIdentifiable.cs" }}
,{ "pid":12345, "tid":23, "ts":1751348734837445, "dur":2180, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751348734839626, "dur":1004, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751348734840630, "dur":2210, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751348734842840, "dur":69, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751348734842910, "dur":1007, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751348734843918, "dur":396, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751348734844314, "dur":761, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751348734845076, "dur":2324, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751348734847401, "dur":731, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751348734848132, "dur":3290, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751348734851423, "dur":1499, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751348734852923, "dur":396608, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751348735249533, "dur":1395214, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751348734824564, "dur":10874, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751348734835439, "dur":125, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_373DEE6BF49845CD.mvfrm" }}
,{ "pid":12345, "tid":24, "ts":1751348734835600, "dur":98, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_61F1B4EFC6980E0D.mvfrm" }}
,{ "pid":12345, "tid":24, "ts":1751348734835700, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751348734835767, "dur":102, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C9C29C66E9057584.mvfrm" }}
,{ "pid":12345, "tid":24, "ts":1751348734835958, "dur":150, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.rsp" }}
,{ "pid":12345, "tid":24, "ts":1751348734836110, "dur":180, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751348734836300, "dur":814, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751348734837465, "dur":516, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Input\\OnKeyboardInput.cs" }}
,{ "pid":12345, "tid":24, "ts":1751348734837115, "dur":1279, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751348734838394, "dur":782, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751348734839176, "dur":911, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751348734840088, "dur":1338, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751348734841427, "dur":1373, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751348734842801, "dur":79, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751348734842880, "dur":1052, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751348734843932, "dur":401, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751348734844334, "dur":752, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751348734845086, "dur":2159, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751348734847246, "dur":927, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751348734848173, "dur":3264, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751348734851438, "dur":1486, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751348734852925, "dur":1791834, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751348736662961, "dur":1893, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 13400, "tid": 702, "ts": 1751348736687060, "dur": 46, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 13400, "tid": 702, "ts": 1751348736687176, "dur": 1002, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 13400, "tid": 702, "ts": 1751348736666148, "dur": 22078, "ph": "X", "name": "Write chrome-trace events", "args": {} },

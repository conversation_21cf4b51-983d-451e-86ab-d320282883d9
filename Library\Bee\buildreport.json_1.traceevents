{ "pid": 41616, "tid": 34359738368, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157011009, "dur": 33682, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157044693, "dur": 2119947, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157044711, "dur": 63, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157044781, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157044785, "dur": 40256, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157085051, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157085056, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157085091, "dur": 8, "ph": "X", "name": "ProcessMessages 41", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157085100, "dur": 3661, "ph": "X", "name": "ReadAsync 41", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157088767, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157088770, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157088833, "dur": 3, "ph": "X", "name": "ProcessMessages 209", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157088837, "dur": 56, "ph": "X", "name": "ReadAsync 209", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157088900, "dur": 2, "ph": "X", "name": "ProcessMessages 431", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157088904, "dur": 59, "ph": "X", "name": "ReadAsync 431", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157088968, "dur": 2, "ph": "X", "name": "ProcessMessages 438", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157088971, "dur": 59, "ph": "X", "name": "ReadAsync 438", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089034, "dur": 2, "ph": "X", "name": "ProcessMessages 439", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089038, "dur": 52, "ph": "X", "name": "ReadAsync 439", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089096, "dur": 2, "ph": "X", "name": "ProcessMessages 417", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089100, "dur": 53, "ph": "X", "name": "ReadAsync 417", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089157, "dur": 2, "ph": "X", "name": "ProcessMessages 351", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089160, "dur": 54, "ph": "X", "name": "ReadAsync 351", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089219, "dur": 2, "ph": "X", "name": "ProcessMessages 407", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089222, "dur": 55, "ph": "X", "name": "ReadAsync 407", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089282, "dur": 1, "ph": "X", "name": "ProcessMessages 417", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089285, "dur": 50, "ph": "X", "name": "ReadAsync 417", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089339, "dur": 2, "ph": "X", "name": "ProcessMessages 352", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089342, "dur": 57, "ph": "X", "name": "ReadAsync 352", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089404, "dur": 2, "ph": "X", "name": "ProcessMessages 413", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089407, "dur": 55, "ph": "X", "name": "ReadAsync 413", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089466, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089469, "dur": 49, "ph": "X", "name": "ReadAsync 425", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089522, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089525, "dur": 51, "ph": "X", "name": "ReadAsync 320", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089580, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089582, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089644, "dur": 2, "ph": "X", "name": "ProcessMessages 564", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089647, "dur": 50, "ph": "X", "name": "ReadAsync 564", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089701, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089704, "dur": 46, "ph": "X", "name": "ReadAsync 321", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089756, "dur": 2, "ph": "X", "name": "ProcessMessages 327", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089759, "dur": 44, "ph": "X", "name": "ReadAsync 327", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089807, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089810, "dur": 47, "ph": "X", "name": "ReadAsync 432", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089861, "dur": 2, "ph": "X", "name": "ProcessMessages 299", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089864, "dur": 48, "ph": "X", "name": "ReadAsync 299", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089917, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089919, "dur": 47, "ph": "X", "name": "ReadAsync 408", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089970, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157089973, "dur": 48, "ph": "X", "name": "ReadAsync 307", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090025, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090028, "dur": 48, "ph": "X", "name": "ReadAsync 324", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090080, "dur": 4, "ph": "X", "name": "ProcessMessages 326", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090085, "dur": 63, "ph": "X", "name": "ReadAsync 326", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090152, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090155, "dur": 49, "ph": "X", "name": "ReadAsync 434", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090210, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090213, "dur": 48, "ph": "X", "name": "ReadAsync 416", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090265, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090268, "dur": 48, "ph": "X", "name": "ReadAsync 313", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090320, "dur": 2, "ph": "X", "name": "ProcessMessages 329", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090323, "dur": 39, "ph": "X", "name": "ReadAsync 329", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090366, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090369, "dur": 39, "ph": "X", "name": "ReadAsync 44", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090412, "dur": 1, "ph": "X", "name": "ProcessMessages 127", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090414, "dur": 46, "ph": "X", "name": "ReadAsync 127", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090464, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090467, "dur": 46, "ph": "X", "name": "ReadAsync 407", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090517, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090521, "dur": 50, "ph": "X", "name": "ReadAsync 300", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090575, "dur": 2, "ph": "X", "name": "ProcessMessages 400", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090578, "dur": 43, "ph": "X", "name": "ReadAsync 400", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090625, "dur": 2, "ph": "X", "name": "ProcessMessages 326", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090637, "dur": 51, "ph": "X", "name": "ReadAsync 326", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090692, "dur": 2, "ph": "X", "name": "ProcessMessages 614", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090695, "dur": 50, "ph": "X", "name": "ReadAsync 614", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090749, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090752, "dur": 49, "ph": "X", "name": "ReadAsync 320", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090805, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090808, "dur": 45, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090857, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090859, "dur": 51, "ph": "X", "name": "ReadAsync 302", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090915, "dur": 4, "ph": "X", "name": "ProcessMessages 388", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090921, "dur": 55, "ph": "X", "name": "ReadAsync 388", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090981, "dur": 2, "ph": "X", "name": "ProcessMessages 416", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157090985, "dur": 48, "ph": "X", "name": "ReadAsync 416", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091037, "dur": 2, "ph": "X", "name": "ProcessMessages 364", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091040, "dur": 46, "ph": "X", "name": "ReadAsync 364", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091090, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091093, "dur": 46, "ph": "X", "name": "ReadAsync 440", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091143, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091146, "dur": 44, "ph": "X", "name": "ReadAsync 316", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091194, "dur": 1, "ph": "X", "name": "ProcessMessages 170", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091197, "dur": 51, "ph": "X", "name": "ReadAsync 170", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091252, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091255, "dur": 50, "ph": "X", "name": "ReadAsync 527", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091309, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091312, "dur": 46, "ph": "X", "name": "ReadAsync 309", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091361, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091364, "dur": 46, "ph": "X", "name": "ReadAsync 320", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091414, "dur": 2, "ph": "X", "name": "ProcessMessages 298", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091418, "dur": 57, "ph": "X", "name": "ReadAsync 298", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091479, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091482, "dur": 49, "ph": "X", "name": "ReadAsync 453", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091535, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091538, "dur": 43, "ph": "X", "name": "ReadAsync 458", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091586, "dur": 2, "ph": "X", "name": "ProcessMessages 319", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091590, "dur": 50, "ph": "X", "name": "ReadAsync 319", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091644, "dur": 2, "ph": "X", "name": "ProcessMessages 592", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091647, "dur": 47, "ph": "X", "name": "ReadAsync 592", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091698, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091701, "dur": 46, "ph": "X", "name": "ReadAsync 330", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091751, "dur": 2, "ph": "X", "name": "ProcessMessages 346", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091753, "dur": 45, "ph": "X", "name": "ReadAsync 346", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091802, "dur": 2, "ph": "X", "name": "ProcessMessages 332", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091805, "dur": 55, "ph": "X", "name": "ReadAsync 332", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091864, "dur": 2, "ph": "X", "name": "ProcessMessages 448", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091867, "dur": 45, "ph": "X", "name": "ReadAsync 448", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091955, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157091958, "dur": 55, "ph": "X", "name": "ReadAsync 164", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092017, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092022, "dur": 53, "ph": "X", "name": "ReadAsync 517", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092079, "dur": 2, "ph": "X", "name": "ProcessMessages 343", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092082, "dur": 54, "ph": "X", "name": "ReadAsync 343", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092141, "dur": 2, "ph": "X", "name": "ProcessMessages 513", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092144, "dur": 51, "ph": "X", "name": "ReadAsync 513", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092199, "dur": 2, "ph": "X", "name": "ProcessMessages 355", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092203, "dur": 50, "ph": "X", "name": "ReadAsync 355", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092256, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092260, "dur": 53, "ph": "X", "name": "ReadAsync 300", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092317, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092319, "dur": 48, "ph": "X", "name": "ReadAsync 514", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092372, "dur": 2, "ph": "X", "name": "ProcessMessages 283", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092375, "dur": 46, "ph": "X", "name": "ReadAsync 283", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092425, "dur": 7, "ph": "X", "name": "ProcessMessages 256", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092433, "dur": 49, "ph": "X", "name": "ReadAsync 256", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092487, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092490, "dur": 50, "ph": "X", "name": "ReadAsync 258", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092544, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092547, "dur": 46, "ph": "X", "name": "ReadAsync 262", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092597, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092600, "dur": 53, "ph": "X", "name": "ReadAsync 266", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092657, "dur": 2, "ph": "X", "name": "ProcessMessages 326", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092661, "dur": 46, "ph": "X", "name": "ReadAsync 326", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092710, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092713, "dur": 49, "ph": "X", "name": "ReadAsync 301", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092766, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092769, "dur": 48, "ph": "X", "name": "ReadAsync 424", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092822, "dur": 2, "ph": "X", "name": "ProcessMessages 267", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092825, "dur": 51, "ph": "X", "name": "ReadAsync 267", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092881, "dur": 2, "ph": "X", "name": "ProcessMessages 308", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092884, "dur": 45, "ph": "X", "name": "ReadAsync 308", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092933, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157092936, "dur": 83, "ph": "X", "name": "ReadAsync 21", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093025, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093027, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093081, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093083, "dur": 50, "ph": "X", "name": "ReadAsync 502", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093138, "dur": 2, "ph": "X", "name": "ProcessMessages 367", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093141, "dur": 60, "ph": "X", "name": "ReadAsync 367", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093205, "dur": 2, "ph": "X", "name": "ProcessMessages 361", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093208, "dur": 52, "ph": "X", "name": "ReadAsync 361", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093265, "dur": 2, "ph": "X", "name": "ProcessMessages 392", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093269, "dur": 58, "ph": "X", "name": "ReadAsync 392", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093331, "dur": 2, "ph": "X", "name": "ProcessMessages 438", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093334, "dur": 45, "ph": "X", "name": "ReadAsync 438", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093383, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093386, "dur": 43, "ph": "X", "name": "ReadAsync 21", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093434, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093437, "dur": 46, "ph": "X", "name": "ReadAsync 316", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093487, "dur": 2, "ph": "X", "name": "ProcessMessages 342", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093490, "dur": 46, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093541, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093544, "dur": 50, "ph": "X", "name": "ReadAsync 316", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093598, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093601, "dur": 46, "ph": "X", "name": "ReadAsync 383", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093652, "dur": 2, "ph": "X", "name": "ProcessMessages 281", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093655, "dur": 47, "ph": "X", "name": "ReadAsync 281", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093706, "dur": 2, "ph": "X", "name": "ProcessMessages 371", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093709, "dur": 42, "ph": "X", "name": "ReadAsync 371", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093755, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093758, "dur": 47, "ph": "X", "name": "ReadAsync 84", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093809, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093813, "dur": 51, "ph": "X", "name": "ReadAsync 410", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093869, "dur": 2, "ph": "X", "name": "ProcessMessages 350", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093872, "dur": 58, "ph": "X", "name": "ReadAsync 350", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093935, "dur": 2, "ph": "X", "name": "ProcessMessages 426", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093938, "dur": 54, "ph": "X", "name": "ReadAsync 426", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093996, "dur": 2, "ph": "X", "name": "ProcessMessages 394", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157093999, "dur": 50, "ph": "X", "name": "ReadAsync 394", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094053, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094056, "dur": 46, "ph": "X", "name": "ReadAsync 377", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094106, "dur": 2, "ph": "X", "name": "ProcessMessages 211", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094109, "dur": 47, "ph": "X", "name": "ReadAsync 211", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094160, "dur": 1, "ph": "X", "name": "ProcessMessages 223", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094163, "dur": 60, "ph": "X", "name": "ReadAsync 223", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094229, "dur": 2, "ph": "X", "name": "ProcessMessages 567", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094232, "dur": 45, "ph": "X", "name": "ReadAsync 567", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094281, "dur": 1, "ph": "X", "name": "ProcessMessages 209", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094284, "dur": 49, "ph": "X", "name": "ReadAsync 209", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094337, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094339, "dur": 46, "ph": "X", "name": "ReadAsync 385", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094389, "dur": 2, "ph": "X", "name": "ProcessMessages 313", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094392, "dur": 45, "ph": "X", "name": "ReadAsync 313", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094441, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094444, "dur": 41, "ph": "X", "name": "ReadAsync 283", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094488, "dur": 1, "ph": "X", "name": "ProcessMessages 118", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094491, "dur": 47, "ph": "X", "name": "ReadAsync 118", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094542, "dur": 1, "ph": "X", "name": "ProcessMessages 433", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094545, "dur": 47, "ph": "X", "name": "ReadAsync 433", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094596, "dur": 2, "ph": "X", "name": "ProcessMessages 341", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094599, "dur": 47, "ph": "X", "name": "ReadAsync 341", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094650, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094653, "dur": 47, "ph": "X", "name": "ReadAsync 299", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094703, "dur": 2, "ph": "X", "name": "ProcessMessages 265", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094706, "dur": 49, "ph": "X", "name": "ReadAsync 265", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094759, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094762, "dur": 40, "ph": "X", "name": "ReadAsync 466", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094806, "dur": 1, "ph": "X", "name": "ProcessMessages 69", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094808, "dur": 63, "ph": "X", "name": "ReadAsync 69", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094876, "dur": 2, "ph": "X", "name": "ProcessMessages 457", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094880, "dur": 49, "ph": "X", "name": "ReadAsync 457", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094933, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094935, "dur": 45, "ph": "X", "name": "ReadAsync 244", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094986, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157094989, "dur": 41, "ph": "X", "name": "ReadAsync 335", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095035, "dur": 2, "ph": "X", "name": "ProcessMessages 62", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095038, "dur": 41, "ph": "X", "name": "ReadAsync 62", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095082, "dur": 3, "ph": "X", "name": "ProcessMessages 104", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095087, "dur": 47, "ph": "X", "name": "ReadAsync 104", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095137, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095140, "dur": 43, "ph": "X", "name": "ReadAsync 345", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095187, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095190, "dur": 42, "ph": "X", "name": "ReadAsync 245", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095236, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095239, "dur": 55, "ph": "X", "name": "ReadAsync 192", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095298, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095301, "dur": 44, "ph": "X", "name": "ReadAsync 458", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095348, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095351, "dur": 48, "ph": "X", "name": "ReadAsync 373", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095403, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095406, "dur": 45, "ph": "X", "name": "ReadAsync 292", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095456, "dur": 2, "ph": "X", "name": "ProcessMessages 286", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095459, "dur": 48, "ph": "X", "name": "ReadAsync 286", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095514, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095516, "dur": 40, "ph": "X", "name": "ReadAsync 393", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095560, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095563, "dur": 47, "ph": "X", "name": "ReadAsync 180", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095614, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095616, "dur": 74, "ph": "X", "name": "ReadAsync 335", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095694, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095697, "dur": 59, "ph": "X", "name": "ReadAsync 306", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095761, "dur": 2, "ph": "X", "name": "ProcessMessages 515", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095764, "dur": 54, "ph": "X", "name": "ReadAsync 515", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095826, "dur": 2, "ph": "X", "name": "ProcessMessages 193", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095833, "dur": 65, "ph": "X", "name": "ReadAsync 193", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095903, "dur": 2, "ph": "X", "name": "ProcessMessages 416", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095906, "dur": 54, "ph": "X", "name": "ReadAsync 416", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095964, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157095967, "dur": 59, "ph": "X", "name": "ReadAsync 332", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096030, "dur": 2, "ph": "X", "name": "ProcessMessages 310", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096033, "dur": 59, "ph": "X", "name": "ReadAsync 310", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096096, "dur": 2, "ph": "X", "name": "ProcessMessages 354", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096099, "dur": 52, "ph": "X", "name": "ReadAsync 354", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096155, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096158, "dur": 51, "ph": "X", "name": "ReadAsync 414", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096213, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096216, "dur": 46, "ph": "X", "name": "ReadAsync 316", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096266, "dur": 2, "ph": "X", "name": "ProcessMessages 289", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096270, "dur": 49, "ph": "X", "name": "ReadAsync 289", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096322, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096325, "dur": 50, "ph": "X", "name": "ReadAsync 308", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096380, "dur": 2, "ph": "X", "name": "ProcessMessages 347", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096383, "dur": 54, "ph": "X", "name": "ReadAsync 347", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096442, "dur": 1, "ph": "X", "name": "ProcessMessages 333", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096445, "dur": 52, "ph": "X", "name": "ReadAsync 333", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096501, "dur": 2, "ph": "X", "name": "ProcessMessages 355", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096504, "dur": 50, "ph": "X", "name": "ReadAsync 355", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096557, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096560, "dur": 46, "ph": "X", "name": "ReadAsync 398", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096611, "dur": 2, "ph": "X", "name": "ProcessMessages 307", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096614, "dur": 42, "ph": "X", "name": "ReadAsync 307", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096660, "dur": 1, "ph": "X", "name": "ProcessMessages 75", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096663, "dur": 48, "ph": "X", "name": "ReadAsync 75", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096715, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096718, "dur": 39, "ph": "X", "name": "ReadAsync 440", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096761, "dur": 1, "ph": "X", "name": "ProcessMessages 73", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096764, "dur": 52, "ph": "X", "name": "ReadAsync 73", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096820, "dur": 2, "ph": "X", "name": "ProcessMessages 445", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096823, "dur": 45, "ph": "X", "name": "ReadAsync 445", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096873, "dur": 2, "ph": "X", "name": "ProcessMessages 75", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157096876, "dur": 290, "ph": "X", "name": "ReadAsync 75", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157097171, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157097175, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157097237, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157097242, "dur": 59, "ph": "X", "name": "ReadAsync 84", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157097307, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157097311, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157097369, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157097374, "dur": 57, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157097436, "dur": 3, "ph": "X", "name": "ProcessMessages 140", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157097442, "dur": 56, "ph": "X", "name": "ReadAsync 140", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157097505, "dur": 3, "ph": "X", "name": "ProcessMessages 116", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157097511, "dur": 63, "ph": "X", "name": "ReadAsync 116", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157097580, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157097585, "dur": 69, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157097659, "dur": 4, "ph": "X", "name": "ProcessMessages 140", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157097665, "dur": 65, "ph": "X", "name": "ReadAsync 140", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157097736, "dur": 4, "ph": "X", "name": "ProcessMessages 100", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157097742, "dur": 77, "ph": "X", "name": "ReadAsync 100", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157097824, "dur": 3, "ph": "X", "name": "ProcessMessages 140", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157097829, "dur": 57, "ph": "X", "name": "ReadAsync 140", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157097891, "dur": 3, "ph": "X", "name": "ProcessMessages 148", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157097896, "dur": 45, "ph": "X", "name": "ReadAsync 148", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157097945, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157097948, "dur": 56, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157098010, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157098014, "dur": 63, "ph": "X", "name": "ReadAsync 60", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157098083, "dur": 4, "ph": "X", "name": "ProcessMessages 148", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157098090, "dur": 61, "ph": "X", "name": "ReadAsync 148", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157098156, "dur": 4, "ph": "X", "name": "ProcessMessages 156", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157098163, "dur": 54, "ph": "X", "name": "ReadAsync 156", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157098222, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157098226, "dur": 49, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157098279, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157098286, "dur": 51, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157098344, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157098349, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157098402, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157098406, "dur": 51, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157098463, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157098469, "dur": 123, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157098597, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157098600, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157098652, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157098656, "dur": 605, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157099265, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157099267, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157099317, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157099321, "dur": 2052, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157101379, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157101383, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157101441, "dur": 31, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157101475, "dur": 1448, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157102928, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157102932, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157102992, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157102999, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157103062, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157103068, "dur": 169, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157103246, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157103250, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157103315, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157103320, "dur": 442, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157103767, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157103770, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157103824, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157103828, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157103894, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157103898, "dur": 65, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157103968, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157103971, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157104028, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157104033, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157104085, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157104089, "dur": 50, "ph": "X", "name": "ReadAsync 44", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157104145, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157104148, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157104242, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157104245, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157104300, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157104304, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157104365, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157104368, "dur": 535, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157104908, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157104912, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157104970, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157104974, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157105026, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157105029, "dur": 43, "ph": "X", "name": "ReadAsync 44", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157105077, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157105080, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157105173, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157105175, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157105220, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157105223, "dur": 740, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157105969, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157105972, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157106030, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157106032, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157106087, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157106090, "dur": 513, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157106608, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157106612, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157106681, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157106683, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157106731, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157106733, "dur": 64, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157106802, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157106805, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157106850, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157106853, "dur": 1154, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157108013, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157108017, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157108071, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157108073, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157108120, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157108123, "dur": 49, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157108177, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157108180, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157108235, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157108237, "dur": 430, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157108673, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157108677, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157108727, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157108730, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157108777, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157108780, "dur": 74, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157108859, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157108861, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157108903, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157108905, "dur": 8427, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157117340, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157117344, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157117406, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353157117413, "dur": 987485, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353158104912, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353158104918, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353158105007, "dur": 27, "ph": "X", "name": "ProcessMessages 1224", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353158105036, "dur": 7492, "ph": "X", "name": "ReadAsync 1224", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353158112534, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353158112537, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353158112600, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353158112604, "dur": 2775, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353158115384, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353158115386, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353158115444, "dur": 23, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353158115468, "dur": 105188, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353158220674, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353158220681, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353158220742, "dur": 11, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353158220759, "dur": 692, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353158221461, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353158221466, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353158221567, "dur": 34, "ph": "X", "name": "ProcessMessages 50", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353158221604, "dur": 267881, "ph": "X", "name": "ReadAsync 50", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353158489498, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353158489504, "dur": 135, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353158489648, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353158489657, "dur": 658821, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353159148503, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353159148514, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353159148610, "dur": 51, "ph": "X", "name": "ProcessMessages 2633", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353159148664, "dur": 41, "ph": "X", "name": "ReadAsync 2633", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353159148711, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353159148721, "dur": 1275, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353159150001, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353159150004, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353159150059, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 41616, "tid": 34359738368, "ts": 1751353159150062, "dur": 14573, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 41616, "tid": 1750, "ts": 1751353159165151, "dur": 1221, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 41616, "tid": 30064771072, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 41616, "tid": 30064771072, "ts": 1751353157010938, "dur": 8, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 41616, "tid": 30064771072, "ts": 1751353157010947, "dur": 33742, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 41616, "tid": 30064771072, "ts": 1751353157044691, "dur": 65, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 41616, "tid": 1750, "ts": 1751353159166375, "dur": 11, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 41616, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 41616, "tid": 1, "ts": 1751353156324727, "dur": 2415, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 41616, "tid": 1, "ts": 1751353156327145, "dur": 98622, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 41616, "tid": 1, "ts": 1751353156425769, "dur": 12938, "ph": "X", "name": "WriteJson", "args": {} },
{ "pid": 41616, "tid": 1750, "ts": 1751353159166388, "dur": 15, "ph": "X", "name": "", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156324695, "dur": 28248, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156352945, "dur": 227886, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156352955, "dur": 37, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156352994, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156352997, "dur": 375, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156353377, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156353380, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156353431, "dur": 6, "ph": "X", "name": "ProcessMessages 41", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156353439, "dur": 4092, "ph": "X", "name": "ReadAsync 41", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156357538, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156357540, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156357602, "dur": 2, "ph": "X", "name": "ProcessMessages 188", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156357606, "dur": 55, "ph": "X", "name": "ReadAsync 188", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156357664, "dur": 2, "ph": "X", "name": "ProcessMessages 327", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156357667, "dur": 58, "ph": "X", "name": "ReadAsync 327", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156357731, "dur": 1, "ph": "X", "name": "ProcessMessages 422", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156357735, "dur": 56, "ph": "X", "name": "ReadAsync 422", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156357795, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156357798, "dur": 55, "ph": "X", "name": "ReadAsync 460", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156357861, "dur": 2, "ph": "X", "name": "ProcessMessages 528", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156357865, "dur": 75, "ph": "X", "name": "ReadAsync 528", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156357945, "dur": 2, "ph": "X", "name": "ProcessMessages 348", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156357948, "dur": 91, "ph": "X", "name": "ReadAsync 348", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156358048, "dur": 4, "ph": "X", "name": "ProcessMessages 592", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156358055, "dur": 107, "ph": "X", "name": "ReadAsync 592", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156358167, "dur": 3, "ph": "X", "name": "ProcessMessages 892", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156358172, "dur": 119, "ph": "X", "name": "ReadAsync 892", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156358307, "dur": 8, "ph": "X", "name": "ProcessMessages 862", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156358320, "dur": 99, "ph": "X", "name": "ReadAsync 862", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156358424, "dur": 2, "ph": "X", "name": "ProcessMessages 642", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156358428, "dur": 67, "ph": "X", "name": "ReadAsync 642", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156358502, "dur": 3, "ph": "X", "name": "ProcessMessages 417", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156358510, "dur": 71, "ph": "X", "name": "ReadAsync 417", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156358585, "dur": 3, "ph": "X", "name": "ProcessMessages 719", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156358590, "dur": 81, "ph": "X", "name": "ReadAsync 719", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156358681, "dur": 3, "ph": "X", "name": "ProcessMessages 291", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156358687, "dur": 68, "ph": "X", "name": "ReadAsync 291", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156358758, "dur": 2, "ph": "X", "name": "ProcessMessages 635", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156358763, "dur": 51, "ph": "X", "name": "ReadAsync 635", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156358818, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156358821, "dur": 47, "ph": "X", "name": "ReadAsync 289", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156358871, "dur": 2, "ph": "X", "name": "ProcessMessages 327", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156358875, "dur": 54, "ph": "X", "name": "ReadAsync 327", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156358932, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156358935, "dur": 60, "ph": "X", "name": "ReadAsync 558", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359004, "dur": 3, "ph": "X", "name": "ProcessMessages 342", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359010, "dur": 72, "ph": "X", "name": "ReadAsync 342", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359086, "dur": 2, "ph": "X", "name": "ProcessMessages 636", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359090, "dur": 54, "ph": "X", "name": "ReadAsync 636", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359151, "dur": 5, "ph": "X", "name": "ProcessMessages 44", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359160, "dur": 76, "ph": "X", "name": "ReadAsync 44", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359239, "dur": 3, "ph": "X", "name": "ProcessMessages 827", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359245, "dur": 52, "ph": "X", "name": "ReadAsync 827", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359302, "dur": 2, "ph": "X", "name": "ProcessMessages 324", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359308, "dur": 56, "ph": "X", "name": "ReadAsync 324", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359368, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359371, "dur": 64, "ph": "X", "name": "ReadAsync 344", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359440, "dur": 2, "ph": "X", "name": "ProcessMessages 606", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359445, "dur": 53, "ph": "X", "name": "ReadAsync 606", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359502, "dur": 2, "ph": "X", "name": "ProcessMessages 349", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359506, "dur": 55, "ph": "X", "name": "ReadAsync 349", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359565, "dur": 2, "ph": "X", "name": "ProcessMessages 546", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359569, "dur": 49, "ph": "X", "name": "ReadAsync 546", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359621, "dur": 2, "ph": "X", "name": "ProcessMessages 332", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359624, "dur": 46, "ph": "X", "name": "ReadAsync 332", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359675, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359678, "dur": 53, "ph": "X", "name": "ReadAsync 320", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359738, "dur": 2, "ph": "X", "name": "ProcessMessages 427", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359741, "dur": 62, "ph": "X", "name": "ReadAsync 427", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359807, "dur": 2, "ph": "X", "name": "ProcessMessages 447", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359811, "dur": 51, "ph": "X", "name": "ReadAsync 447", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359867, "dur": 2, "ph": "X", "name": "ProcessMessages 404", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359871, "dur": 52, "ph": "X", "name": "ReadAsync 404", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359926, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359929, "dur": 55, "ph": "X", "name": "ReadAsync 262", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359989, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156359992, "dur": 62, "ph": "X", "name": "ReadAsync 536", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360059, "dur": 4, "ph": "X", "name": "ProcessMessages 356", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360064, "dur": 51, "ph": "X", "name": "ReadAsync 356", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360119, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360123, "dur": 52, "ph": "X", "name": "ReadAsync 503", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360179, "dur": 2, "ph": "X", "name": "ProcessMessages 331", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360182, "dur": 49, "ph": "X", "name": "ReadAsync 331", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360234, "dur": 2, "ph": "X", "name": "ProcessMessages 356", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360238, "dur": 52, "ph": "X", "name": "ReadAsync 356", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360294, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360297, "dur": 48, "ph": "X", "name": "ReadAsync 432", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360348, "dur": 1, "ph": "X", "name": "ProcessMessages 210", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360351, "dur": 54, "ph": "X", "name": "ReadAsync 210", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360409, "dur": 2, "ph": "X", "name": "ProcessMessages 318", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360412, "dur": 62, "ph": "X", "name": "ReadAsync 318", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360478, "dur": 2, "ph": "X", "name": "ProcessMessages 600", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360482, "dur": 64, "ph": "X", "name": "ReadAsync 600", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360553, "dur": 3, "ph": "X", "name": "ProcessMessages 358", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360559, "dur": 67, "ph": "X", "name": "ReadAsync 358", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360631, "dur": 2, "ph": "X", "name": "ProcessMessages 612", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360634, "dur": 58, "ph": "X", "name": "ReadAsync 612", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360699, "dur": 3, "ph": "X", "name": "ProcessMessages 284", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360705, "dur": 56, "ph": "X", "name": "ReadAsync 284", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360765, "dur": 2, "ph": "X", "name": "ProcessMessages 286", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360769, "dur": 49, "ph": "X", "name": "ReadAsync 286", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360822, "dur": 1, "ph": "X", "name": "ProcessMessages 241", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360825, "dur": 52, "ph": "X", "name": "ReadAsync 241", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360880, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360883, "dur": 49, "ph": "X", "name": "ReadAsync 284", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360936, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360940, "dur": 48, "ph": "X", "name": "ReadAsync 293", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360992, "dur": 2, "ph": "X", "name": "ProcessMessages 308", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156360995, "dur": 49, "ph": "X", "name": "ReadAsync 308", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361048, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361051, "dur": 49, "ph": "X", "name": "ReadAsync 336", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361104, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361107, "dur": 47, "ph": "X", "name": "ReadAsync 383", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361158, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361161, "dur": 51, "ph": "X", "name": "ReadAsync 309", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361216, "dur": 2, "ph": "X", "name": "ProcessMessages 272", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361221, "dur": 48, "ph": "X", "name": "ReadAsync 272", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361273, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361276, "dur": 52, "ph": "X", "name": "ReadAsync 307", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361336, "dur": 2, "ph": "X", "name": "ProcessMessages 271", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361341, "dur": 65, "ph": "X", "name": "ReadAsync 271", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361410, "dur": 2, "ph": "X", "name": "ProcessMessages 311", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361414, "dur": 44, "ph": "X", "name": "ReadAsync 311", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361463, "dur": 1, "ph": "X", "name": "ProcessMessages 121", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361466, "dur": 53, "ph": "X", "name": "ReadAsync 121", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361522, "dur": 2, "ph": "X", "name": "ProcessMessages 352", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361525, "dur": 48, "ph": "X", "name": "ReadAsync 352", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361577, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361580, "dur": 45, "ph": "X", "name": "ReadAsync 239", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361629, "dur": 1, "ph": "X", "name": "ProcessMessages 181", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361632, "dur": 118, "ph": "X", "name": "ReadAsync 181", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361756, "dur": 3, "ph": "X", "name": "ProcessMessages 347", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361761, "dur": 103, "ph": "X", "name": "ReadAsync 347", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361867, "dur": 2, "ph": "X", "name": "ProcessMessages 608", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361870, "dur": 63, "ph": "X", "name": "ReadAsync 608", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361938, "dur": 2, "ph": "X", "name": "ProcessMessages 65", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156361942, "dur": 72, "ph": "X", "name": "ReadAsync 65", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362019, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362021, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362098, "dur": 2, "ph": "X", "name": "ProcessMessages 634", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362102, "dur": 58, "ph": "X", "name": "ReadAsync 634", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362164, "dur": 2, "ph": "X", "name": "ProcessMessages 576", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362170, "dur": 51, "ph": "X", "name": "ReadAsync 576", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362225, "dur": 2, "ph": "X", "name": "ProcessMessages 407", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362230, "dur": 48, "ph": "X", "name": "ReadAsync 407", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362282, "dur": 2, "ph": "X", "name": "ProcessMessages 381", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362285, "dur": 42, "ph": "X", "name": "ReadAsync 381", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362332, "dur": 1, "ph": "X", "name": "ProcessMessages 83", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362335, "dur": 40, "ph": "X", "name": "ReadAsync 83", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362378, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362381, "dur": 50, "ph": "X", "name": "ReadAsync 245", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362435, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362438, "dur": 49, "ph": "X", "name": "ReadAsync 380", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362494, "dur": 3, "ph": "X", "name": "ProcessMessages 341", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362499, "dur": 52, "ph": "X", "name": "ReadAsync 341", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362555, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362557, "dur": 85, "ph": "X", "name": "ReadAsync 255", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362647, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362650, "dur": 56, "ph": "X", "name": "ReadAsync 292", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362710, "dur": 2, "ph": "X", "name": "ProcessMessages 580", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362714, "dur": 56, "ph": "X", "name": "ReadAsync 580", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362774, "dur": 2, "ph": "X", "name": "ProcessMessages 569", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362777, "dur": 50, "ph": "X", "name": "ReadAsync 569", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362832, "dur": 2, "ph": "X", "name": "ProcessMessages 358", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362835, "dur": 54, "ph": "X", "name": "ReadAsync 358", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362894, "dur": 2, "ph": "X", "name": "ProcessMessages 279", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362898, "dur": 51, "ph": "X", "name": "ReadAsync 279", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362953, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156362955, "dur": 53, "ph": "X", "name": "ReadAsync 379", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363013, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363016, "dur": 43, "ph": "X", "name": "ReadAsync 372", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363062, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363065, "dur": 50, "ph": "X", "name": "ReadAsync 76", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363119, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363122, "dur": 59, "ph": "X", "name": "ReadAsync 358", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363185, "dur": 2, "ph": "X", "name": "ProcessMessages 462", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363188, "dur": 43, "ph": "X", "name": "ReadAsync 462", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363235, "dur": 2, "ph": "X", "name": "ProcessMessages 199", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363239, "dur": 58, "ph": "X", "name": "ReadAsync 199", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363300, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363305, "dur": 55, "ph": "X", "name": "ReadAsync 490", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363364, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363366, "dur": 50, "ph": "X", "name": "ReadAsync 327", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363421, "dur": 2, "ph": "X", "name": "ProcessMessages 357", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363424, "dur": 58, "ph": "X", "name": "ReadAsync 357", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363488, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363491, "dur": 53, "ph": "X", "name": "ReadAsync 278", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363552, "dur": 3, "ph": "X", "name": "ProcessMessages 314", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363557, "dur": 58, "ph": "X", "name": "ReadAsync 314", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363623, "dur": 2, "ph": "X", "name": "ProcessMessages 406", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363628, "dur": 68, "ph": "X", "name": "ReadAsync 406", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363700, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363703, "dur": 53, "ph": "X", "name": "ReadAsync 325", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363760, "dur": 2, "ph": "X", "name": "ProcessMessages 331", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363764, "dur": 48, "ph": "X", "name": "ReadAsync 331", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363816, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363819, "dur": 51, "ph": "X", "name": "ReadAsync 259", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363873, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363877, "dur": 48, "ph": "X", "name": "ReadAsync 324", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363929, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363932, "dur": 47, "ph": "X", "name": "ReadAsync 367", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363983, "dur": 1, "ph": "X", "name": "ProcessMessages 273", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156363987, "dur": 38, "ph": "X", "name": "ReadAsync 273", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364029, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364032, "dur": 41, "ph": "X", "name": "ReadAsync 134", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364077, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364080, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364138, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364141, "dur": 47, "ph": "X", "name": "ReadAsync 427", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364191, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364194, "dur": 37, "ph": "X", "name": "ReadAsync 255", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364235, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364238, "dur": 56, "ph": "X", "name": "ReadAsync 61", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364298, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364301, "dur": 58, "ph": "X", "name": "ReadAsync 306", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364363, "dur": 2, "ph": "X", "name": "ProcessMessages 677", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364366, "dur": 48, "ph": "X", "name": "ReadAsync 677", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364418, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364421, "dur": 49, "ph": "X", "name": "ReadAsync 311", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364473, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364477, "dur": 47, "ph": "X", "name": "ReadAsync 360", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364528, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364531, "dur": 44, "ph": "X", "name": "ReadAsync 375", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364579, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364582, "dur": 50, "ph": "X", "name": "ReadAsync 84", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364636, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364639, "dur": 47, "ph": "X", "name": "ReadAsync 402", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364691, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364693, "dur": 46, "ph": "X", "name": "ReadAsync 244", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364743, "dur": 1, "ph": "X", "name": "ProcessMessages 250", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364746, "dur": 53, "ph": "X", "name": "ReadAsync 250", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364803, "dur": 2, "ph": "X", "name": "ProcessMessages 333", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364806, "dur": 46, "ph": "X", "name": "ReadAsync 333", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364855, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364858, "dur": 49, "ph": "X", "name": "ReadAsync 388", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364911, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364914, "dur": 49, "ph": "X", "name": "ReadAsync 242", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364967, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156364970, "dur": 46, "ph": "X", "name": "ReadAsync 315", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365020, "dur": 2, "ph": "X", "name": "ProcessMessages 284", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365023, "dur": 72, "ph": "X", "name": "ReadAsync 284", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365098, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365101, "dur": 48, "ph": "X", "name": "ReadAsync 498", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365153, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365156, "dur": 47, "ph": "X", "name": "ReadAsync 300", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365207, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365210, "dur": 46, "ph": "X", "name": "ReadAsync 370", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365264, "dur": 2, "ph": "X", "name": "ProcessMessages 348", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365269, "dur": 74, "ph": "X", "name": "ReadAsync 348", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365346, "dur": 2, "ph": "X", "name": "ProcessMessages 345", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365349, "dur": 49, "ph": "X", "name": "ReadAsync 345", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365403, "dur": 2, "ph": "X", "name": "ProcessMessages 315", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365406, "dur": 47, "ph": "X", "name": "ReadAsync 315", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365457, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365460, "dur": 65, "ph": "X", "name": "ReadAsync 294", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365534, "dur": 4, "ph": "X", "name": "ProcessMessages 323", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365542, "dur": 76, "ph": "X", "name": "ReadAsync 323", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365621, "dur": 3, "ph": "X", "name": "ProcessMessages 587", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365626, "dur": 45, "ph": "X", "name": "ReadAsync 587", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365675, "dur": 2, "ph": "X", "name": "ProcessMessages 53", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365678, "dur": 60, "ph": "X", "name": "ReadAsync 53", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365742, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365745, "dur": 42, "ph": "X", "name": "ReadAsync 463", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365791, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365794, "dur": 65, "ph": "X", "name": "ReadAsync 62", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365867, "dur": 3, "ph": "X", "name": "ProcessMessages 450", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365873, "dur": 85, "ph": "X", "name": "ReadAsync 450", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365964, "dur": 2, "ph": "X", "name": "ProcessMessages 70", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156365969, "dur": 296, "ph": "X", "name": "ReadAsync 70", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156366279, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156366283, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156366344, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156366349, "dur": 57, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156366411, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156366417, "dur": 90, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156366512, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156366515, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156366584, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156366590, "dur": 415, "ph": "X", "name": "ReadAsync 128", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156367010, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156367014, "dur": 92, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156367116, "dur": 14, "ph": "X", "name": "ProcessMessages 736", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156367133, "dur": 84, "ph": "X", "name": "ReadAsync 736", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156367224, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156367230, "dur": 73, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156367307, "dur": 4, "ph": "X", "name": "ProcessMessages 220", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156367314, "dur": 55, "ph": "X", "name": "ReadAsync 220", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156367375, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156367381, "dur": 47, "ph": "X", "name": "ReadAsync 112", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156367433, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156367438, "dur": 52, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156367495, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156367500, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156367550, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156367555, "dur": 193, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156367753, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156367756, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156367798, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156367801, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156367852, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156367856, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156367902, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156367907, "dur": 121, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156368032, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156368035, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156368081, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156368086, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156368137, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156368141, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156368195, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156368199, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156368254, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156368257, "dur": 225, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156368488, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156368491, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156368544, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156368547, "dur": 4757, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156373311, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156373315, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156373375, "dur": 7, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156373387, "dur": 159, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156373555, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156373559, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156373618, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156373621, "dur": 152, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156373782, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156373786, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156373842, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156373848, "dur": 191, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156374047, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156374051, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156374100, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156374104, "dur": 145, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156374256, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156374262, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156374325, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156374329, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156374391, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156374396, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156374455, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156374459, "dur": 54, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156374519, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156374523, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156374587, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156374591, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156374634, "dur": 4, "ph": "X", "name": "ProcessMessages 60", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156374640, "dur": 45, "ph": "X", "name": "ReadAsync 60", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156374691, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156374697, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156374759, "dur": 4, "ph": "X", "name": "ProcessMessages 40", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156374769, "dur": 425, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156375200, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156375203, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156375241, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156375244, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156375301, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156375305, "dur": 145, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156375456, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156375461, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156375521, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156375526, "dur": 65, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156375598, "dur": 3, "ph": "X", "name": "ProcessMessages 40", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156375604, "dur": 752, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156376361, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156376365, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156376427, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156376432, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156376488, "dur": 4, "ph": "X", "name": "ProcessMessages 56", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156376494, "dur": 787, "ph": "X", "name": "ReadAsync 56", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156377287, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156377291, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156377372, "dur": 36, "ph": "X", "name": "ProcessMessages 75", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156377411, "dur": 46, "ph": "X", "name": "ReadAsync 75", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156377462, "dur": 38, "ph": "X", "name": "ProcessMessages 83", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156377503, "dur": 46, "ph": "X", "name": "ReadAsync 83", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156377555, "dur": 3, "ph": "X", "name": "ProcessMessages 40", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156377560, "dur": 76, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156377641, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156377645, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156377705, "dur": 23, "ph": "X", "name": "ProcessMessages 50", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156377730, "dur": 64, "ph": "X", "name": "ReadAsync 50", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156377799, "dur": 22, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156377824, "dur": 51, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156377880, "dur": 36, "ph": "X", "name": "ProcessMessages 92", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156377920, "dur": 142, "ph": "X", "name": "ReadAsync 92", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156378068, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156378072, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156378142, "dur": 20, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156378165, "dur": 80, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156378251, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156378255, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156378318, "dur": 20, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156378342, "dur": 44, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156378391, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156378395, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156378447, "dur": 20, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156378470, "dur": 41, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156378515, "dur": 20, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156378537, "dur": 825, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156379368, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156379371, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156379443, "dur": 27, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156379476, "dur": 278, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156379760, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156379765, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156379825, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156379830, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156379892, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156379896, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156379955, "dur": 4, "ph": "X", "name": "ProcessMessages 40", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156379961, "dur": 71, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156380037, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156380040, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156380095, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156380098, "dur": 719, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156380823, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156380826, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156380881, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156380887, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156380944, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156380949, "dur": 49, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156381003, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156381008, "dur": 95, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156381108, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156381112, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156381173, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156381178, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156381243, "dur": 19, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156381264, "dur": 128, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156381398, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156381401, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156381462, "dur": 17, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156381482, "dur": 270, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156381758, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156381761, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156381818, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156381821, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156381885, "dur": 6, "ph": "X", "name": "ProcessMessages 40", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156381893, "dur": 224, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156382123, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156382127, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156382193, "dur": 33, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156382229, "dur": 653, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156382887, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156382889, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156382939, "dur": 22, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156382964, "dur": 169089, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156552061, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156552066, "dur": 348, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156552422, "dur": 40, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156552465, "dur": 82, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156552553, "dur": 1, "ph": "X", "name": "ProcessMessages 25", "args": {} },
{ "pid": 41616, "tid": 25769803776, "ts": 1751353156552555, "dur": 28268, "ph": "X", "name": "ReadAsync 25", "args": {} },
{ "pid": 41616, "tid": 1750, "ts": 1751353159166405, "dur": 1069, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 41616, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 41616, "tid": 21474836480, "ts": 1751353156324653, "dur": 114077, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 41616, "tid": 21474836480, "ts": 1751353156438731, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 41616, "tid": 21474836480, "ts": 1751353156438733, "dur": 61, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 41616, "tid": 1750, "ts": 1751353159167477, "dur": 16, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 41616, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 41616, "tid": 17179869184, "ts": 1751353156322052, "dur": 258849, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 41616, "tid": 17179869184, "ts": 1751353156322204, "dur": 2414, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 41616, "tid": 17179869184, "ts": 1751353156580968, "dur": 426052, "ph": "X", "name": "await ExecuteBuildProgram", "args": {} },
{ "pid": 41616, "tid": 17179869184, "ts": 1751353157007205, "dur": 2157475, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 41616, "tid": 17179869184, "ts": 1751353157007376, "dur": 3519, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 41616, "tid": 17179869184, "ts": 1751353159164685, "dur": 73, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 41616, "tid": 17179869184, "ts": 1751353159164699, "dur": 21, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 41616, "tid": 17179869184, "ts": 1751353159164761, "dur": 1, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 41616, "tid": 1750, "ts": 1751353159167496, "dur": 20, "ph": "X", "name": "BuildAsync", "args": {} },
{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "netcorerun.dll" } },
{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "-1" } },
{ "pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 35942, "tid": 1, "ts": 1751353156703387, "dur": 277251, "ph": "X", "name": "BuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751353156705108, "dur": 82462, "ph": "X", "name": "BuildProgramContextConstructor", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751353156940227, "dur": 6636, "ph": "X", "name": "OutputData.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751353156946867, "dur": 33760, "ph": "X", "name": "Backend.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751353156948740, "dur": 28947, "ph": "X", "name": "JsonToString", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751353156991185, "dur": 2005, "ph": "X", "name": "", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751353156990450, "dur": 3112, "ph": "X", "name": "Write chrome-trace events", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1751353157044943, "dur":57, "ph":"X", "name": "IPC_Client_InitializeAndConnectToParent",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353157045051, "dur":42251, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353157087313, "dur":516, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353157087930, "dur":1101, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353157089141, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_23EC6FFA1F64C6FB.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751353157089249, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_373DEE6BF49845CD.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751353157089935, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_0DD02120343DEAE3.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751353157090447, "dur":57, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C7AA02CA7F374981.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751353157093377, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1751353157093619, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":0, "ts":1751353157094116, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1751353157095162, "dur":66, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1751353157095588, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1751353157096050, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":0, "ts":1751353157096203, "dur":59, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.pdb" }}
,{ "pid":12345, "tid":0, "ts":1751353157097119, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Player/RuntimeInitializeOnLoads.json (+1 other)" }}
,{ "pid":12345, "tid":0, "ts":1751353157089065, "dur":8105, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353157097174, "dur":2051816, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353159148994, "dur":614, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353159149608, "dur":113, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353159149722, "dur":74, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353159149796, "dur":71, "ph":"X", "name": "ThreadStateDestroy",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353159149867, "dur":111, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353159150311, "dur":8912, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1751353157089075, "dur":8108, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353157101528, "dur":612, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":1, "ts":1751353157097187, "dur":4958, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353157102146, "dur":1163, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353157103309, "dur":94, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353157103403, "dur":719, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353157104182, "dur":204, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353157104386, "dur":248, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353157104635, "dur":654, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353157105290, "dur":1061, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353157106352, "dur":664, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353157107016, "dur":1339, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353157108356, "dur":669, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353157109070, "dur":2039949, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353157089275, "dur":7996, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353157097275, "dur":441, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_0BCEDB16477CDAC4.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1751353157097717, "dur":151, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353157097880, "dur":85, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_7A7DF710AFAEDB06.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1751353157098000, "dur":100, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_6E5A65FFC58DA42D.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1751353157098131, "dur":182, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_6E5A65FFC58DA42D.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1751353157098361, "dur":60, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.rsp" }}
,{ "pid":12345, "tid":2, "ts":1751353157098494, "dur":646, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353157099141, "dur":1056, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353157100198, "dur":790, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353157100988, "dur":727, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353157101715, "dur":1591, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353157103307, "dur":132, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353157103440, "dur":692, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353157104133, "dur":217, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353157104350, "dur":260, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353157104610, "dur":662, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353157105272, "dur":1071, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353157106344, "dur":631, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353157106976, "dur":1397, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353157108373, "dur":658, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353157109031, "dur":1112771, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353158221803, "dur":927196, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353157089084, "dur":8110, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353157097209, "dur":221, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_D463EC1B32D16EDE.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1751353157097437, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353157097520, "dur":163, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_AA29B6A11E640A63.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1751353157097730, "dur":106, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_8DA71FDFCE95C3B3.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1751353157097888, "dur":102, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_3AD4F95A2CDB3E3B.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1751353157098022, "dur":262, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_3AD4F95A2CDB3E3B.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1751353157098293, "dur":101, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353157098394, "dur":196, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":3, "ts":1751353157098592, "dur":758, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353157099351, "dur":1021, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353157100373, "dur":605, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353157100979, "dur":678, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353157101658, "dur":1640, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353157103298, "dur":83, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353157103387, "dur":137, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.dll.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1751353157103563, "dur":460, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.dll (+2 others)" }}
,{ "pid":12345, "tid":3, "ts":1751353157104024, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353157104168, "dur":193, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353157104362, "dur":257, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353157104619, "dur":654, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353157105274, "dur":1061, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353157106336, "dur":641, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353157106978, "dur":1373, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353157108400, "dur":655, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353157109056, "dur":2039999, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353157089264, "dur":7996, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353157097264, "dur":400, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_8EC677C52DC49AFA.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1751353157097725, "dur":113, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_57801C939D0EB92F.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1751353157097942, "dur":132, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_280AA007BEACDA98.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1751353157098202, "dur":135, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353157098338, "dur":350, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":4, "ts":1751353157098690, "dur":662, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353157099353, "dur":876, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353157100229, "dur":636, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353157100866, "dur":610, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353157101477, "dur":1063, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353157102541, "dur":788, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353157103330, "dur":64, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353157103394, "dur":739, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353157104134, "dur":214, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353157104349, "dur":254, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353157104659, "dur":626, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353157105286, "dur":1060, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353157106346, "dur":651, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353157106998, "dur":1389, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353157108388, "dur":654, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353157109042, "dur":2039950, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353157089190, "dur":8042, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353157097236, "dur":322, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_59C0F0D9CA68870D.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1751353157097598, "dur":74, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_59C0F0D9CA68870D.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1751353157097674, "dur":97, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_3F615B2CD094388F.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1751353157097772, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353157097862, "dur":109, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B37C4FB38B0D23A8.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1751353157098068, "dur":159, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":5, "ts":1751353157098230, "dur":167, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353157098398, "dur":211, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":5, "ts":1751353157098611, "dur":854, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353157099465, "dur":948, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353157100414, "dur":657, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353157101072, "dur":660, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353157101732, "dur":1580, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353157103312, "dur":87, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353157103399, "dur":729, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353157104129, "dur":218, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353157104348, "dur":280, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353157104628, "dur":664, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353157105293, "dur":1072, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353157106365, "dur":629, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353157106995, "dur":1354, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353157108355, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353157108415, "dur":635, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353157109051, "dur":2039988, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353157089254, "dur":7999, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353157097257, "dur":309, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_BF626035856B433F.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1751353157097628, "dur":200, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1F4164359B0F1C2F.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1751353157097875, "dur":164, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1F4164359B0F1C2F.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1751353157098041, "dur":77, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.rsp" }}
,{ "pid":12345, "tid":6, "ts":1751353157098202, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353157098425, "dur":675, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353157099101, "dur":910, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353157100012, "dur":630, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353157100643, "dur":659, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353157101303, "dur":401, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353157101705, "dur":1586, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353157103292, "dur":94, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353157103387, "dur":751, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353157104139, "dur":197, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353157104391, "dur":230, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353157104621, "dur":658, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353157105279, "dur":1068, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353157106348, "dur":647, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353157106995, "dur":1375, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353157108370, "dur":659, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353157109030, "dur":1003412, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353158112501, "dur":255, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\1300b0aP.dag\\Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":6, "ts":1751353158112445, "dur":313, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":6, "ts":1751353158112879, "dur":2847, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":6, "ts":1751353158115730, "dur":1033321, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353157089289, "dur":7992, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353157097285, "dur":368, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_29C05456CBAFE676.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1751353157097703, "dur":826, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_29C05456CBAFE676.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1751353157098533, "dur":732, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353157099266, "dur":1056, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353157100323, "dur":592, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353157100916, "dur":644, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353157101560, "dur":1111, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353157102671, "dur":652, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353157103323, "dur":90, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353157103414, "dur":721, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353157104136, "dur":209, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353157104346, "dur":262, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353157104609, "dur":661, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353157105270, "dur":1069, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353157106339, "dur":650, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353157106990, "dur":1385, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353157108376, "dur":663, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353157109039, "dur":2039962, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353157089995, "dur":7483, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353157097480, "dur":180, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_0DD02120343DEAE3.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751353157097661, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353157097770, "dur":91, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_DC6D18346848EB0E.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751353157097953, "dur":101, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_945787BCF828CC39.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751353157098089, "dur":552, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_945787BCF828CC39.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751353157098645, "dur":237, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751353157098922, "dur":4268, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1751353157103377, "dur":154, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751353157103582, "dur":1590, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1751353157105311, "dur":151, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751353157105511, "dur":1352, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1751353157106864, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353157107022, "dur":81, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751353157107129, "dur":1139, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1751353157108396, "dur":80, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751353157108503, "dur":442, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1751353157109067, "dur":94, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751353157109619, "dur":7429, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\1300b0aP.dag\\Assembly-CSharp.rsp" }}
,{ "pid":12345, "tid":8, "ts":1751353157109190, "dur":7860, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1751353157117488, "dur":94, "ph":"X", "name": "StoreTimestampsOfNonGeneratedInputFiles",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353157118145, "dur":987068, "ph":"X", "name": "Csc",  "args": { "detail":"Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll (+2 others)" }}
,{ "pid":12345, "tid":8, "ts":1751353158112443, "dur":108410, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\1300b0aP.dag\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":8, "ts":1751353158112428, "dur":108428, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":8, "ts":1751353158220889, "dur":892, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":8, "ts":1751353158221819, "dur":149298, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":8, "ts":1751353158371122, "dur":976, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.InputSystem.dll" }}
,{ "pid":12345, "tid":8, "ts":1751353158372100, "dur":89, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.InputSystem.ForUI.dll" }}
,{ "pid":12345, "tid":8, "ts":1751353158372191, "dur":85, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":8, "ts":1751353158372277, "dur":402, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":8, "ts":1751353158372680, "dur":171, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":8, "ts":1751353158372853, "dur":115988, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":8, "ts":1751353158488844, "dur":393, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":8, "ts":1751353158489239, "dur":98, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":8, "ts":1751353158489340, "dur":255, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":8, "ts":1751353158221797, "dur":267804, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Player/RuntimeInitializeOnLoads.json (+1 other)" }}
,{ "pid":12345, "tid":8, "ts":1751353158490150, "dur":658557, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Player/RuntimeInitializeOnLoads.json (+1 other)" }}
,{ "pid":12345, "tid":9, "ts":1751353157089239, "dur":8004, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353157097247, "dur":478, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_9DEBA6BB330C53E7.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1751353157097785, "dur":103, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_61F1B4EFC6980E0D.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1751353157097939, "dur":97, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_C511BE0634F804BB.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1751353157098088, "dur":134, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.rsp" }}
,{ "pid":12345, "tid":9, "ts":1751353157098223, "dur":156, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353157098380, "dur":380, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.rsp" }}
,{ "pid":12345, "tid":9, "ts":1751353157098762, "dur":642, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353157099405, "dur":1011, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353157100417, "dur":644, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353157101062, "dur":581, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353157101649, "dur":503, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353157102153, "dur":1160, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353157103313, "dur":97, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353157103411, "dur":719, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353157104131, "dur":213, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353157104344, "dur":271, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353157104615, "dur":687, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353157105302, "dur":1073, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353157106375, "dur":631, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353157107007, "dur":1370, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353157108378, "dur":666, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353157109044, "dur":2040025, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353157089298, "dur":7995, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353157097297, "dur":157, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6D05EFF427F01585.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1751353157097504, "dur":100, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_618B3886BAD936BE.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1751353157097606, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353157097696, "dur":83, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_618B3886BAD936BE.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1751353157097781, "dur":93, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_9906D3291E9C4B92.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1751353157097942, "dur":88, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_11AAD2D878892EA2.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1751353157098132, "dur":199, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":10, "ts":1751353157101157, "dur":591, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.rsp" }}
,{ "pid":12345, "tid":10, "ts":1751353157101750, "dur":1543, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353157103293, "dur":97, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353157103391, "dur":746, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353157104138, "dur":195, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353157104343, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353157104408, "dur":221, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353157104630, "dur":661, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353157105291, "dur":1046, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353157106338, "dur":648, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353157106986, "dur":1388, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353157108375, "dur":660, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353157109036, "dur":2039947, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353157089358, "dur":7946, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353157097308, "dur":129, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_93DCDEE9DEE5D1AC.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1751353157097501, "dur":102, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_D79DA43A6C4320A9.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1751353157097657, "dur":119, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_DD1A64662AB2EEE1.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1751353157097807, "dur":50, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_DD1A64662AB2EEE1.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1751353157097859, "dur":104, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_AF7B180A90C4DB84.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1751353157098016, "dur":122, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_38A2919050B15439.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1751353157098192, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353157098345, "dur":91, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.rsp" }}
,{ "pid":12345, "tid":11, "ts":1751353157098471, "dur":498, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.rsp" }}
,{ "pid":12345, "tid":11, "ts":1751353157098971, "dur":1109, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353157100081, "dur":606, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353157100688, "dur":572, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353157101261, "dur":539, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353157101801, "dur":1516, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353157103318, "dur":109, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353157103428, "dur":723, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353157104152, "dur":211, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353157104364, "dur":242, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353157104607, "dur":674, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353157105281, "dur":1072, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353157106353, "dur":639, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353157106993, "dur":1404, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353157108397, "dur":648, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353157109045, "dur":2039990, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353157089403, "dur":7913, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353157097320, "dur":127, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_9EE980C253D6A9A7.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1751353157097511, "dur":165, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_DC97BFF5203733EF.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1751353157097713, "dur":81, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_DC97BFF5203733EF.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1751353157097796, "dur":123, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E50243D2AE5AE01D.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1751353157097991, "dur":109, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B5651178AA90B44B.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1751353157098134, "dur":217, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B5651178AA90B44B.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1751353157098354, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.rsp" }}
,{ "pid":12345, "tid":12, "ts":1751353157098447, "dur":268, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.rsp" }}
,{ "pid":12345, "tid":12, "ts":1751353157098716, "dur":746, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353157099915, "dur":1453, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\TypeNameDetail.cs" }}
,{ "pid":12345, "tid":12, "ts":1751353157099462, "dur":2273, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353157101736, "dur":1574, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353157103311, "dur":106, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353157103417, "dur":768, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353157104186, "dur":154, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353157104341, "dur":282, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353157104623, "dur":653, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353157105277, "dur":1065, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353157106342, "dur":628, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353157106976, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353157107038, "dur":1346, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353157108385, "dur":684, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353157109069, "dur":2039978, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353157089443, "dur":7887, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353157097335, "dur":142, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C7AA02CA7F374981.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1751353157097527, "dur":142, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_40CA959D70D4461E.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1751353157097745, "dur":110, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_A8E93876951A4405.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1751353157097856, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353157097932, "dur":138, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_A8E93876951A4405.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1751353157098122, "dur":123, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.rsp" }}
,{ "pid":12345, "tid":13, "ts":1751353157098246, "dur":154, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353157098415, "dur":686, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353157099102, "dur":811, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353157099914, "dur":607, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353157100998, "dur":780, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Plugins\\UI\\UISupport.cs" }}
,{ "pid":12345, "tid":13, "ts":1751353157100522, "dur":1384, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353157101907, "dur":1412, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353157103320, "dur":86, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353157103406, "dur":714, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353157104173, "dur":215, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353157104389, "dur":253, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353157104642, "dur":669, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353157105313, "dur":148, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1751353157105505, "dur":718, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll (+2 others)" }}
,{ "pid":12345, "tid":13, "ts":1751353157106224, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353157106367, "dur":656, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353157107023, "dur":1343, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353157108367, "dur":685, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353157109052, "dur":2040008, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353157089509, "dur":7834, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353157097347, "dur":140, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_387FC8ADF0601190.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1751353157097489, "dur":194, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353157097742, "dur":206, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":14, "ts":1751353157097950, "dur":95, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C9C29C66E9057584.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1751353157098164, "dur":84, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.rsp" }}
,{ "pid":12345, "tid":14, "ts":1751353157098248, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353157098381, "dur":129, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.rsp" }}
,{ "pid":12345, "tid":14, "ts":1751353157098511, "dur":599, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353157099111, "dur":983, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353157100094, "dur":587, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353157100682, "dur":605, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353157101287, "dur":537, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353157101825, "dur":1477, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353157103302, "dur":118, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353157103421, "dur":754, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353157104175, "dur":205, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353157104381, "dur":244, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353157104626, "dur":688, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353157105317, "dur":1041, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353157106358, "dur":641, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353157107000, "dur":1372, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353157108372, "dur":691, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353157109063, "dur":2040004, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353157089537, "dur":7819, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353157097362, "dur":136, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_8403F9361EA02785.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1751353157097547, "dur":166, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E131416B0BD83AC6.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1751353157097748, "dur":127, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E131416B0BD83AC6.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1751353157097877, "dur":100, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_0844AD3841D8B3E3.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1751353157098026, "dur":146, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_0844AD3841D8B3E3.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1751353157098176, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353157098400, "dur":226, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":15, "ts":1751353157098627, "dur":575, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353157099203, "dur":879, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353157100082, "dur":641, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353157100724, "dur":572, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353157101297, "dur":519, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353157101816, "dur":1509, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353157103325, "dur":109, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353157103435, "dur":727, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353157104162, "dur":206, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353157104368, "dur":237, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353157104654, "dur":643, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353157105297, "dur":1057, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353157106355, "dur":629, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353157106985, "dur":1372, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353157108358, "dur":696, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353157109054, "dur":2039967, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353157089582, "dur":7787, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353157097374, "dur":138, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_67F60D54A1E44C7C.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1751353157097513, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353157097607, "dur":158, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_6691A7F2FCA50E77.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1751353157097812, "dur":102, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_103705979AC6E0CE.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1751353157097951, "dur":164, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_103705979AC6E0CE.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1751353157098174, "dur":281, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":16, "ts":1751353157098457, "dur":731, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353157099189, "dur":903, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353157100093, "dur":586, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353157100680, "dur":626, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353157101342, "dur":54, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353157101436, "dur":1217, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353157102654, "dur":664, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353157103319, "dur":106, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353157103425, "dur":746, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353157104171, "dur":184, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353157104356, "dur":268, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353157104624, "dur":641, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353157105325, "dur":1027, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353157106353, "dur":619, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353157107032, "dur":1332, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353157108365, "dur":672, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353157109038, "dur":2039947, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353157089653, "dur":7731, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353157097389, "dur":156, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_661F12A898C05437.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751353157097547, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353157097650, "dur":109, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_F8D420302DB19758.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751353157097800, "dur":122, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_0D96B1407E3DC479.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751353157097958, "dur":283, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_0D96B1407E3DC479.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751353157098245, "dur":162, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353157099029, "dur":720, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.State\\AnyState.cs" }}
,{ "pid":12345, "tid":17, "ts":1751353157098426, "dur":1798, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353157100225, "dur":675, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353157100901, "dur":724, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353157101626, "dur":61, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353157101688, "dur":1606, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353157103295, "dur":81, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353157103377, "dur":155, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.dll.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751353157103596, "dur":901, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.dll (+2 others)" }}
,{ "pid":12345, "tid":17, "ts":1751353157104497, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353157104665, "dur":619, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353157105284, "dur":1045, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353157106382, "dur":626, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353157107009, "dur":1371, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353157108381, "dur":667, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353157109048, "dur":2039994, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353157089697, "dur":7700, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353157097401, "dur":131, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_B0867F543300D220.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751353157097580, "dur":110, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B857BD766991C41C.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751353157097766, "dur":83, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_21ECB020988DD9FE.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751353157097886, "dur":71, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_21ECB020988DD9FE.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751353157097959, "dur":122, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_261556851D3B23B2.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751353157098121, "dur":178, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_261556851D3B23B2.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751353157098305, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353157098394, "dur":176, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":18, "ts":1751353157098572, "dur":857, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353157099430, "dur":973, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353157100982, "dur":726, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Cloning\\Cloners\\FakeSerializationCloner.cs" }}
,{ "pid":12345, "tid":18, "ts":1751353157100404, "dur":1305, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353157101709, "dur":1587, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353157103297, "dur":81, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353157103383, "dur":140, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.dll.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751353157103571, "dur":650, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.dll (+2 others)" }}
,{ "pid":12345, "tid":18, "ts":1751353157104222, "dur":63, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353157104383, "dur":250, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353157104634, "dur":670, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353157105305, "dur":1058, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353157106364, "dur":626, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353157106991, "dur":1371, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353157108363, "dur":664, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353157109080, "dur":2039951, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353157089737, "dur":7673, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353157097415, "dur":150, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_643D92A01AF9D37A.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1751353157097618, "dur":153, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_FFA94AF822882F93.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1751353157097805, "dur":132, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_FFA94AF822882F93.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1751353157097941, "dur":103, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_C72B18C5EA43D3DC.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1751353157098183, "dur":203, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.rsp2" }}
,{ "pid":12345, "tid":19, "ts":1751353157098438, "dur":1427, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353157099866, "dur":622, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353157100489, "dur":1256, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353157101745, "dur":1541, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353157103333, "dur":55, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353157103389, "dur":735, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353157104125, "dur":231, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353157104357, "dur":256, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353157104614, "dur":654, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353157105313, "dur":1036, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353157106350, "dur":629, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353157106980, "dur":1381, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353157108361, "dur":679, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353157109041, "dur":2039966, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353157089783, "dur":7640, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353157097428, "dur":142, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3808EA5EE8B9C374.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1751353157097607, "dur":87, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3808EA5EE8B9C374.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1751353157097695, "dur":100, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_23EC6FFA1F64C6FB.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1751353157097830, "dur":361, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_23EC6FFA1F64C6FB.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1751353157098195, "dur":151, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353157098359, "dur":55, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.rsp" }}
,{ "pid":12345, "tid":20, "ts":1751353157098459, "dur":680, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353157099294, "dur":671, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\GenericGuiEventUnit.cs" }}
,{ "pid":12345, "tid":20, "ts":1751353157099140, "dur":1824, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353157100985, "dur":732, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353157101717, "dur":1571, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353157103396, "dur":743, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353157104140, "dur":202, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353157104343, "dur":286, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353157104629, "dur":653, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353157105283, "dur":1050, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353157106334, "dur":653, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353157106988, "dur":1365, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353157108353, "dur":693, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353157109047, "dur":2039997, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353157089869, "dur":7578, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353157097451, "dur":158, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_5E63226C05E60A63.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1751353157097610, "dur":94, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353157097717, "dur":90, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_C065E2E457D01111.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1751353157097835, "dur":176, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_C065E2E457D01111.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1751353157098014, "dur":123, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_91AF51B67A3AA61F.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1751353157098168, "dur":203, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_91AF51B67A3AA61F.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1751353157100364, "dur":554, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.timeline\\Runtime\\Animation\\ICurvesOwner.cs" }}
,{ "pid":12345, "tid":21, "ts":1751353157098446, "dur":2658, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353157101105, "dur":781, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353157101887, "dur":1417, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353157103304, "dur":89, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353157103393, "dur":748, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353157104142, "dur":216, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353157104359, "dur":252, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353157104612, "dur":663, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353157105276, "dur":1055, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353157106386, "dur":595, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353157106982, "dur":1377, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353157108360, "dur":673, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353157109033, "dur":2039954, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353157089826, "dur":7610, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353157097441, "dur":154, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_5052AD45BBA3738E.mvfrm" }}
,{ "pid":12345, "tid":22, "ts":1751353157097596, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353157097739, "dur":104, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_24B2CF62E51A4B38.mvfrm" }}
,{ "pid":12345, "tid":22, "ts":1751353157097900, "dur":87, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_B9A372E0ADBCF042.mvfrm" }}
,{ "pid":12345, "tid":22, "ts":1751353157098020, "dur":110, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_4AB7AB16DC48CC38.mvfrm" }}
,{ "pid":12345, "tid":22, "ts":1751353157098199, "dur":235, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":22, "ts":1751353157098436, "dur":1894, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353157100330, "dur":655, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353157100986, "dur":590, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353157101577, "dur":715, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353157102292, "dur":1036, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353157103328, "dur":95, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353157103424, "dur":775, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353157104199, "dur":152, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353157104351, "dur":275, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353157104626, "dur":661, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353157105287, "dur":1053, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353157106341, "dur":642, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353157106984, "dur":1384, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353157108369, "dur":698, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353157109068, "dur":2039936, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353157089908, "dur":7550, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353157097462, "dur":142, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_9B070CD48E121FF1.mvfrm" }}
,{ "pid":12345, "tid":23, "ts":1751353157097662, "dur":95, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_373DEE6BF49845CD.mvfrm" }}
,{ "pid":12345, "tid":23, "ts":1751353157097804, "dur":170, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_F825EB11843D5C3C.mvfrm" }}
,{ "pid":12345, "tid":23, "ts":1751353157097975, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353157098097, "dur":157, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":23, "ts":1751353157098257, "dur":155, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353157098413, "dur":261, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":23, "ts":1751353157098675, "dur":1253, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353157099929, "dur":893, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353157100823, "dur":1048, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353157101871, "dur":1429, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353157103300, "dur":85, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353157103385, "dur":762, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353157104148, "dur":228, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353157104376, "dur":251, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353157104627, "dur":651, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353157105278, "dur":1077, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353157106356, "dur":638, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353157106994, "dur":1398, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353157108392, "dur":667, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353157109060, "dur":2039989, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353157089955, "dur":7513, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353157097473, "dur":184, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_BEA25D59242CD0BD.mvfrm" }}
,{ "pid":12345, "tid":24, "ts":1751353157097709, "dur":90, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm" }}
,{ "pid":12345, "tid":24, "ts":1751353157097825, "dur":445, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm" }}
,{ "pid":12345, "tid":24, "ts":1751353157098273, "dur":110, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353157098384, "dur":169, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.rsp2" }}
,{ "pid":12345, "tid":24, "ts":1751353157098555, "dur":1308, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353157099864, "dur":733, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353157100598, "dur":826, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353157101424, "dur":1144, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353157102569, "dur":738, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353157103308, "dur":93, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353157103401, "dur":725, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353157104127, "dur":211, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353157104339, "dur":278, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353157104617, "dur":646, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353157105358, "dur":1015, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353157106374, "dur":652, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353157107027, "dur":1352, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353157108380, "dur":669, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353157109049, "dur":2039946, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353159162793, "dur":1584, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1751353156353211, "dur":2575, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353156355805, "dur":579, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353156356494, "dur":1211, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353156358228, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E131416B0BD83AC6.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751353156358345, "dur":78, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_DC97BFF5203733EF.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751353156358612, "dur":72, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_BEA25D59242CD0BD.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751353156358775, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_5052AD45BBA3738E.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751353156358848, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3808EA5EE8B9C374.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751353156358940, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_B0867F543300D220.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751353156359419, "dur":76, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_8EC677C52DC49AFA.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751353156359635, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_D463EC1B32D16EDE.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751353156359995, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C9C29C66E9057584.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751353156360181, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_280AA007BEACDA98.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751353156360266, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_3AD4F95A2CDB3E3B.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751353156360504, "dur":53, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_103705979AC6E0CE.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751353156360750, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_9906D3291E9C4B92.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751353156360835, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_21ECB020988DD9FE.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751353156360984, "dur":79, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_24B2CF62E51A4B38.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751353156361076, "dur":51, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_8DA71FDFCE95C3B3.mvfrm" }}
,{ "pid":12345, "tid":0, "ts":1751353156361311, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1751353156361718, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1751353156361897, "dur":84, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1751353156362017, "dur":61, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":0, "ts":1751353156362295, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":0, "ts":1751353156362629, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1751353156362968, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1751353156363332, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1751353156363388, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":0, "ts":1751353156363496, "dur":58, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1751353156363763, "dur":55, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":0, "ts":1751353156363824, "dur":56, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.rsp" }}
,{ "pid":12345, "tid":0, "ts":1751353156363886, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1751353156364089, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.dll (+2 others)" }}
,{ "pid":12345, "tid":0, "ts":1751353156364581, "dur":50, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.rsp" }}
,{ "pid":12345, "tid":0, "ts":1751353156365545, "dur":67, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.pdb" }}
,{ "pid":12345, "tid":0, "ts":1751353156365805, "dur":68, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.rsp2" }}
,{ "pid":12345, "tid":0, "ts":1751353156365936, "dur":52, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":0, "ts":1751353156357745, "dur":8358, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353156366106, "dur":74330, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353156440438, "dur":745, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353156441385, "dur":111070, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353156552622, "dur":57, "ph":"X", "name": "BuildQueueDestroyTail",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353156552702, "dur":18807, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1751353156357329, "dur":8790, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353156366135, "dur":298, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_D463EC1B32D16EDE.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1751353156366504, "dur":127, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_DC97BFF5203733EF.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1751353156366633, "dur":123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353156366774, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353156366844, "dur":177, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":1, "ts":1751353156367023, "dur":89, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_C511BE0634F804BB.mvfrm" }}
,{ "pid":12345, "tid":1, "ts":1751353156367185, "dur":116, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.rsp" }}
,{ "pid":12345, "tid":1, "ts":1751353156367301, "dur":158, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353156367471, "dur":79, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.rsp" }}
,{ "pid":12345, "tid":1, "ts":1751353156367612, "dur":639, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353156368252, "dur":1456, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353156369709, "dur":737, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353156370446, "dur":1132, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353156371579, "dur":1129, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353156372709, "dur":859, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353156373569, "dur":78, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353156373647, "dur":889, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353156374536, "dur":229, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353156374771, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353156374847, "dur":2707, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Timeline.dll" }}
,{ "pid":12345, "tid":1, "ts":1751353156377556, "dur":79, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353156377635, "dur":2453, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353156380088, "dur":1033, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1751353156381122, "dur":59330, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353156357452, "dur":8707, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353156366163, "dur":326, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_BF626035856B433F.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1751353156366544, "dur":196, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_40CA959D70D4461E.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1751353156366742, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353156366821, "dur":153, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_40CA959D70D4461E.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1751353156366977, "dur":111, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_7A7DF710AFAEDB06.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1751353156367137, "dur":64, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.rsp" }}
,{ "pid":12345, "tid":2, "ts":1751353156367242, "dur":1481, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.rsp" }}
,{ "pid":12345, "tid":2, "ts":1751353156368725, "dur":4724, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1751353156373623, "dur":119, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1751353156373774, "dur":1585, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1751353156375510, "dur":120, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1751353156375678, "dur":1827, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1751353156377506, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353156377676, "dur":156, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1751353156377873, "dur":2028, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1751353156379903, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353156380068, "dur":157, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1751353156380261, "dur":727, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1751353156380989, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353156381130, "dur":168, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll.mvfrm" }}
,{ "pid":12345, "tid":2, "ts":1751353156381341, "dur":572, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.dll (+2 others)" }}
,{ "pid":12345, "tid":2, "ts":1751353156381914, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1751353156382072, "dur":1070, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":2, "ts":1751353156383144, "dur":57288, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353156357387, "dur":8751, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353156366142, "dur":291, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_59C0F0D9CA68870D.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1751353156366502, "dur":103, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_618B3886BAD936BE.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1751353156366606, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353156366737, "dur":141, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_373DEE6BF49845CD.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1751353156366922, "dur":660, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_373DEE6BF49845CD.mvfrm" }}
,{ "pid":12345, "tid":3, "ts":1751353156367585, "dur":646, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353156368232, "dur":1253, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353156369486, "dur":682, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353156370170, "dur":1857, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353156372027, "dur":1523, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353156373551, "dur":85, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353156373636, "dur":875, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353156374522, "dur":118, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353156374663, "dur":3326, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.dll" }}
,{ "pid":12345, "tid":3, "ts":1751353156377991, "dur":2038, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353156380036, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1751353156380116, "dur":1321, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.pdb" }}
,{ "pid":12345, "tid":3, "ts":1751353156381439, "dur":58995, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353156357445, "dur":8703, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353156366152, "dur":303, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_9DEBA6BB330C53E7.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1751353156366456, "dur":306, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353156366772, "dur":56, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1751353156366829, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353156366892, "dur":100, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_DC6D18346848EB0E.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1751353156367032, "dur":130, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_C72B18C5EA43D3DC.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1751353156367277, "dur":451, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":4, "ts":1751353156367730, "dur":993, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353156368724, "dur":813, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\StaticFunctionInvoker_1.cs" }}
,{ "pid":12345, "tid":4, "ts":1751353156368723, "dur":2384, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353156371109, "dur":878, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353156371988, "dur":1564, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353156373553, "dur":71, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353156373626, "dur":118, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.dll.mvfrm" }}
,{ "pid":12345, "tid":4, "ts":1751353156373793, "dur":857, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.dll (+2 others)" }}
,{ "pid":12345, "tid":4, "ts":1751353156374651, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353156374815, "dur":57, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353156374873, "dur":590, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353156375464, "dur":1163, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353156376627, "dur":1001, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353156377706, "dur":1904, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":4, "ts":1751353156379612, "dur":939, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353156380552, "dur":563, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1751353156381115, "dur":59330, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353156358263, "dur":8138, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353156366406, "dur":178, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_5052AD45BBA3738E.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1751353156366626, "dur":154, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_DD1A64662AB2EEE1.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1751353156366814, "dur":447, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_DD1A64662AB2EEE1.mvfrm" }}
,{ "pid":12345, "tid":5, "ts":1751353156367286, "dur":803, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.rsp2" }}
,{ "pid":12345, "tid":5, "ts":1751353156368090, "dur":640, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353156368730, "dur":1521, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353156370839, "dur":552, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Plugins\\EnhancedTouch\\EnhancedTouchSupport.cs" }}
,{ "pid":12345, "tid":5, "ts":1751353156371392, "dur":709, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Plugins\\DualShock\\IDualShockHaptics.cs" }}
,{ "pid":12345, "tid":5, "ts":1751353156370252, "dur":2392, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353156372644, "dur":925, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353156373570, "dur":80, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353156373650, "dur":863, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353156374528, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353156374668, "dur":2967, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Multiplayer.Center.Common.pdb" }}
,{ "pid":12345, "tid":5, "ts":1751353156377637, "dur":2399, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353156380036, "dur":1051, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353156381096, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1751353156381179, "dur":1190, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.pdb" }}
,{ "pid":12345, "tid":5, "ts":1751353156382371, "dur":58057, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353156357550, "dur":8651, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353156366207, "dur":668, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_0BCEDB16477CDAC4.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1751353156366915, "dur":164, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_E50243D2AE5AE01D.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1751353156367118, "dur":101, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_4AB7AB16DC48CC38.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1751353156367283, "dur":149, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353156367451, "dur":104, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.rsp" }}
,{ "pid":12345, "tid":6, "ts":1751353156367592, "dur":834, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.rsp" }}
,{ "pid":12345, "tid":6, "ts":1751353156368428, "dur":1281, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353156369710, "dur":788, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353156370499, "dur":1869, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353156372369, "dur":1187, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353156373556, "dur":73, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353156373629, "dur":889, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353156374519, "dur":294, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353156374814, "dur":72, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353156374887, "dur":572, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353156375460, "dur":51, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353156375513, "dur":175, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll.mvfrm" }}
,{ "pid":12345, "tid":6, "ts":1751353156375735, "dur":764, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll (+2 others)" }}
,{ "pid":12345, "tid":6, "ts":1751353156376500, "dur":72, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353156376648, "dur":1033, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353156377681, "dur":2383, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353156380064, "dur":1030, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1751353156381095, "dur":59351, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353156357599, "dur":8617, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353156366221, "dur":714, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_29C05456CBAFE676.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1751353156366980, "dur":267, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_3AD4F95A2CDB3E3B.mvfrm" }}
,{ "pid":12345, "tid":7, "ts":1751353156367248, "dur":161, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353156367505, "dur":145, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.rsp" }}
,{ "pid":12345, "tid":7, "ts":1751353156367711, "dur":1457, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353156369169, "dur":1089, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353156370258, "dur":1665, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353156371945, "dur":1621, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353156373567, "dur":81, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353156373649, "dur":878, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353156374527, "dur":281, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353156374808, "dur":84, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353156374892, "dur":565, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353156375467, "dur":223, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353156375761, "dur":2981, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.pdb" }}
,{ "pid":12345, "tid":7, "ts":1751353156378743, "dur":1318, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353156380062, "dur":1034, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1751353156381096, "dur":59328, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353156358442, "dur":8009, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353156366456, "dur":306, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_BEA25D59242CD0BD.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751353156366803, "dur":886, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_BEA25D59242CD0BD.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751353156367694, "dur":241, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.dll.mvfrm" }}
,{ "pid":12345, "tid":8, "ts":1751353156367985, "dur":579, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353156369918, "dur":525, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Serialization\\ISerializationDepender.cs" }}
,{ "pid":12345, "tid":8, "ts":1751353156368565, "dur":1974, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353156371536, "dur":635, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Controls\\InputControlExtensions.cs" }}
,{ "pid":12345, "tid":8, "ts":1751353156370540, "dur":1690, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353156372230, "dur":1318, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353156373548, "dur":92, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353156373640, "dur":897, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353156374537, "dur":247, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353156374784, "dur":80, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353156374945, "dur":3362, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":8, "ts":1751353156378309, "dur":2280, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353156380589, "dur":511, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1751353156381100, "dur":59330, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353156357688, "dur":8557, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353156366250, "dur":606, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_93DCDEE9DEE5D1AC.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1751353156366888, "dur":552, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_93DCDEE9DEE5D1AC.mvfrm" }}
,{ "pid":12345, "tid":9, "ts":1751353156367484, "dur":183, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":9, "ts":1751353156367669, "dur":774, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353156368444, "dur":946, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353156369391, "dur":985, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353156371168, "dur":941, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Editor\\PropertyDrawers\\GamepadButtonPropertyDrawer.cs" }}
,{ "pid":12345, "tid":9, "ts":1751353156370377, "dur":2099, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353156372476, "dur":1167, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353156373644, "dur":891, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353156374535, "dur":247, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353156374782, "dur":96, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353156374879, "dur":619, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353156375498, "dur":1115, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353156376614, "dur":1042, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353156377657, "dur":2369, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353156380033, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1751353156380130, "dur":1513, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":9, "ts":1751353156381645, "dur":58816, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353156357731, "dur":8534, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353156366273, "dur":296, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_9EE980C253D6A9A7.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1751353156366572, "dur":160, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353156366745, "dur":136, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_3F615B2CD094388F.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1751353156366883, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353156366968, "dur":177, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_0844AD3841D8B3E3.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1751353156367147, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353156367216, "dur":78, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_0844AD3841D8B3E3.mvfrm" }}
,{ "pid":12345, "tid":10, "ts":1751353156367299, "dur":164, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353156367559, "dur":559, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353156368306, "dur":1565, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Logic\\NotApproximatelyEqual.cs" }}
,{ "pid":12345, "tid":10, "ts":1751353156368119, "dur":2242, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353156371537, "dur":658, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Editor\\UITKAssetEditor\\InputActionsEditorSettingsProvider.cs" }}
,{ "pid":12345, "tid":10, "ts":1751353156370362, "dur":2167, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353156372530, "dur":1033, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353156373563, "dur":79, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353156373643, "dur":901, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353156374544, "dur":288, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353156374832, "dur":101, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353156374933, "dur":569, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353156375502, "dur":1106, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353156376688, "dur":1243, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.pdb" }}
,{ "pid":12345, "tid":10, "ts":1751353156377933, "dur":2665, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353156380598, "dur":520, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1751353156381119, "dur":59332, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353156357794, "dur":8487, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353156366286, "dur":601, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C7AA02CA7F374981.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1751353156366929, "dur":121, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_0D96B1407E3DC479.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1751353156367099, "dur":93, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_6E5A65FFC58DA42D.mvfrm" }}
,{ "pid":12345, "tid":11, "ts":1751353156367292, "dur":163, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353156367464, "dur":89, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.rsp" }}
,{ "pid":12345, "tid":11, "ts":1751353156367586, "dur":524, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.State.rsp" }}
,{ "pid":12345, "tid":11, "ts":1751353156368111, "dur":1086, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353156369198, "dur":611, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353156370851, "dur":783, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Utilities\\PrimitiveValue.cs" }}
,{ "pid":12345, "tid":11, "ts":1751353156369810, "dur":2370, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353156372181, "dur":1386, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353156373568, "dur":83, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353156373651, "dur":869, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353156374521, "dur":252, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353156374773, "dur":158, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353156374932, "dur":583, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353156375516, "dur":1114, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353156376631, "dur":1023, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353156377655, "dur":2419, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353156380074, "dur":1035, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1751353156381109, "dur":59329, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353156357844, "dur":8452, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353156366301, "dur":132, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_387FC8ADF0601190.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1751353156366438, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353156366521, "dur":105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_AA29B6A11E640A63.mvfrm" }}
,{ "pid":12345, "tid":12, "ts":1751353156366627, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353156366808, "dur":779, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/UnityEngine.UI.rsp2" }}
,{ "pid":12345, "tid":12, "ts":1751353156367589, "dur":939, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353156369196, "dur":536, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Utilities\\ExceptionUtility.cs" }}
,{ "pid":12345, "tid":12, "ts":1751353156368529, "dur":1928, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353156370458, "dur":1470, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353156371935, "dur":1626, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353156373562, "dur":72, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353156373634, "dur":888, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353156374523, "dur":278, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353156374802, "dur":78, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353156374880, "dur":574, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353156375459, "dur":223, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1751353156375748, "dur":176517, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.dll" }}
,{ "pid":12345, "tid":13, "ts":1751353156357901, "dur":8411, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353156366318, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_8403F9361EA02785.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1751353156366454, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353156366597, "dur":265, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1F4164359B0F1C2F.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1751353156366908, "dur":90, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_61F1B4EFC6980E0D.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1751353156367037, "dur":94, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_11AAD2D878892EA2.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1751353156367160, "dur":224, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_11AAD2D878892EA2.mvfrm" }}
,{ "pid":12345, "tid":13, "ts":1751353156367386, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353156367479, "dur":75, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.rsp2" }}
,{ "pid":12345, "tid":13, "ts":1751353156367556, "dur":756, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353156369078, "dur":697, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Application\\OnApplicationQuit.cs" }}
,{ "pid":12345, "tid":13, "ts":1751353156368313, "dur":1884, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353156370731, "dur":694, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Plugins\\Switch\\SwitchSupportHID.cs" }}
,{ "pid":12345, "tid":13, "ts":1751353156370198, "dur":2327, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353156372525, "dur":1054, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353156373579, "dur":74, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353156373656, "dur":889, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353156374545, "dur":265, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353156374810, "dur":72, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353156374882, "dur":584, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353156375467, "dur":1148, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353156376616, "dur":1017, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353156377633, "dur":2450, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353156380084, "dur":1040, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1751353156381125, "dur":59318, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353156357954, "dur":8380, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353156366342, "dur":253, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_67F60D54A1E44C7C.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1751353156366597, "dur":146, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353156366761, "dur":121, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_23EC6FFA1F64C6FB.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1751353156366883, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353156366954, "dur":108, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B37C4FB38B0D23A8.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1751353156367105, "dur":102, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_91AF51B67A3AA61F.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1751353156367257, "dur":113, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.rsp" }}
,{ "pid":12345, "tid":14, "ts":1751353156367370, "dur":105, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353156367475, "dur":296, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.rsp" }}
,{ "pid":12345, "tid":14, "ts":1751353156367772, "dur":633, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353156368405, "dur":1172, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353156369578, "dur":846, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353156371584, "dur":592, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Editor\\Analytics\\InputComponentEditorAnalytic.cs" }}
,{ "pid":12345, "tid":14, "ts":1751353156370424, "dur":1906, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353156372331, "dur":1234, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353156373565, "dur":61, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353156373628, "dur":110, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.dll.mvfrm" }}
,{ "pid":12345, "tid":14, "ts":1751353156373779, "dur":615, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.dll (+2 others)" }}
,{ "pid":12345, "tid":14, "ts":1751353156374395, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353156374563, "dur":234, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353156374797, "dur":69, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353156374871, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353156374960, "dur":3548, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.TextMeshPro.pdb" }}
,{ "pid":12345, "tid":14, "ts":1751353156378509, "dur":2064, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353156380573, "dur":622, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1751353156381195, "dur":59224, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353156358006, "dur":8344, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353156366354, "dur":145, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_661F12A898C05437.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1751353156366501, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353156366581, "dur":198, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_6691A7F2FCA50E77.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1751353156366781, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353156366883, "dur":180, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_21ECB020988DD9FE.mvfrm" }}
,{ "pid":12345, "tid":15, "ts":1751353156367228, "dur":194, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.rsp" }}
,{ "pid":12345, "tid":15, "ts":1751353156367424, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353156367489, "dur":1264, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.rsp" }}
,{ "pid":12345, "tid":15, "ts":1751353156368755, "dur":992, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353156369748, "dur":979, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353156370729, "dur":1481, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353156372210, "dur":1344, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353156373555, "dur":75, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353156373631, "dur":909, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353156374540, "dur":228, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353156374773, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353156374856, "dur":3295, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.Timeline.pdb" }}
,{ "pid":12345, "tid":15, "ts":1751353156378153, "dur":1878, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353156380032, "dur":1165, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1751353156381198, "dur":59267, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353156358106, "dur":8256, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353156366367, "dur":160, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_B0867F543300D220.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1751353156366589, "dur":177, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_FFA94AF822882F93.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1751353156366768, "dur":83, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353156366869, "dur":88, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_A8E93876951A4405.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1751353156366991, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_B9A372E0ADBCF042.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1751353156367163, "dur":239, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_B9A372E0ADBCF042.mvfrm" }}
,{ "pid":12345, "tid":16, "ts":1751353156367412, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353156367477, "dur":99, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Multiplayer.Center.Common.rsp" }}
,{ "pid":12345, "tid":16, "ts":1751353156367621, "dur":658, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353156368280, "dur":1392, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353156369673, "dur":719, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353156370392, "dur":1843, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353156372235, "dur":1325, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353156373561, "dur":117, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353156373678, "dur":851, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353156374529, "dur":257, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353156374786, "dur":83, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353156374870, "dur":599, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353156375469, "dur":1148, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353156376617, "dur":1029, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353156377646, "dur":2388, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353156380035, "dur":1057, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353156381093, "dur":57946, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1751353156439041, "dur":1309, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353156358172, "dur":8204, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353156366381, "dur":384, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_643D92A01AF9D37A.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751353156366801, "dur":100, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_643D92A01AF9D37A.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751353156366903, "dur":116, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_9906D3291E9C4B92.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751353156367065, "dur":92, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_945787BCF828CC39.mvfrm" }}
,{ "pid":12345, "tid":17, "ts":1751353156367251, "dur":120, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Core.rsp2" }}
,{ "pid":12345, "tid":17, "ts":1751353156367373, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353156367521, "dur":118, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Assembly-CSharp.rsp2" }}
,{ "pid":12345, "tid":17, "ts":1751353156369102, "dur":631, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\UnitPreservation.cs" }}
,{ "pid":12345, "tid":17, "ts":1751353156367642, "dur":2567, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353156370210, "dur":1791, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353156372002, "dur":1540, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353156374267, "dur":3775, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":17, "ts":1751353156378044, "dur":2538, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353156380583, "dur":522, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1751353156381106, "dur":59315, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353156358226, "dur":8164, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353156366394, "dur":123, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3808EA5EE8B9C374.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751353156366574, "dur":185, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B857BD766991C41C.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751353156366804, "dur":206, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B857BD766991C41C.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751353156367013, "dur":100, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_280AA007BEACDA98.mvfrm" }}
,{ "pid":12345, "tid":18, "ts":1751353156367266, "dur":152, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353156367549, "dur":871, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353156369408, "dur":1328, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Codebase\\Expose.cs" }}
,{ "pid":12345, "tid":18, "ts":1751353156368421, "dur":2483, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353156370905, "dur":438, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353156371343, "dur":338, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353156371682, "dur":1278, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353156372961, "dur":596, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353156373558, "dur":80, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353156373639, "dur":877, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353156374516, "dur":307, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353156374823, "dur":60, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353156374883, "dur":585, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353156375469, "dur":1150, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353156376619, "dur":1373, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353156377993, "dur":2053, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353156380046, "dur":1058, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1751353156381104, "dur":59313, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353156357504, "dur":8678, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353156366192, "dur":259, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_8EC677C52DC49AFA.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1751353156366453, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353156366563, "dur":198, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_E131416B0BD83AC6.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1751353156366763, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353156366867, "dur":230, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_24B2CF62E51A4B38.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1751353156367099, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353156367224, "dur":124, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.InputSystem.rsp2" }}
,{ "pid":12345, "tid":19, "ts":1751353156367351, "dur":181, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353156367575, "dur":1071, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353156369783, "dur":752, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\RenamedFromAttribute.cs" }}
,{ "pid":12345, "tid":19, "ts":1751353156368647, "dur":2071, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353156370719, "dur":718, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Actions\\InputBinding.cs" }}
,{ "pid":12345, "tid":19, "ts":1751353156370718, "dur":1938, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353156372657, "dur":902, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353156373559, "dur":62, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353156373626, "dur":168, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.dll.mvfrm" }}
,{ "pid":12345, "tid":19, "ts":1751353156373837, "dur":913, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"Csc Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.dll (+2 others)" }}
,{ "pid":12345, "tid":19, "ts":1751353156374751, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353156374913, "dur":581, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353156375494, "dur":1117, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353156376612, "dur":1434, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353156378047, "dur":1998, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353156380045, "dur":1061, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1751353156381106, "dur":59308, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353156358315, "dur":8095, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353156366413, "dur":139, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_5E63226C05E60A63.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1751353156366604, "dur":210, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_F8D420302DB19758.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1751353156366846, "dur":266, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_F8D420302DB19758.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1751353156367114, "dur":98, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_38A2919050B15439.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1751353156367245, "dur":116, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_38A2919050B15439.mvfrm" }}
,{ "pid":12345, "tid":20, "ts":1751353156367364, "dur":133, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353156367546, "dur":1704, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353156369251, "dur":789, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353156370815, "dur":557, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Plugins\\XR\\XRLayoutBuilder.cs" }}
,{ "pid":12345, "tid":20, "ts":1751353156370042, "dur":2124, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353156372167, "dur":1379, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353156373546, "dur":86, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353156373633, "dur":892, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353156374526, "dur":248, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353156374775, "dur":116, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353156374891, "dur":574, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353156375466, "dur":1156, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353156376622, "dur":1008, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353156377637, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353156377717, "dur":1907, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.Core.pdb" }}
,{ "pid":12345, "tid":20, "ts":1751353156379626, "dur":407, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353156380033, "dur":1057, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353156381090, "dur":895, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353156381991, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1751353156382079, "dur":1085, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Assembly-CSharp.pdb" }}
,{ "pid":12345, "tid":20, "ts":1751353156383166, "dur":57284, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353156358395, "dur":8040, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353156366440, "dur":310, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_9B070CD48E121FF1.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1751353156366788, "dur":61, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_9B070CD48E121FF1.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1751353156366852, "dur":146, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_8DA71FDFCE95C3B3.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1751353156367045, "dur":105, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C9C29C66E9057584.mvfrm" }}
,{ "pid":12345, "tid":21, "ts":1751353156367289, "dur":469, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":21, "ts":1751353156367760, "dur":693, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353156368454, "dur":996, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353156369451, "dur":876, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353156370328, "dur":1162, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353156371595, "dur":1223, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353156372818, "dur":766, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353156373585, "dur":52, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353156373638, "dur":886, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353156374525, "dur":245, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353156374771, "dur":125, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353156374896, "dur":580, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353156375477, "dur":1129, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353156376681, "dur":1965, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.InputSystem.ForUI.dll" }}
,{ "pid":12345, "tid":21, "ts":1751353156378648, "dur":1914, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353156380563, "dur":548, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":21, "ts":1751353156381111, "dur":59328, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353156357638, "dur":8592, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353156366236, "dur":646, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_6D05EFF427F01585.mvfrm" }}
,{ "pid":12345, "tid":22, "ts":1751353156366935, "dur":107, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_F825EB11843D5C3C.mvfrm" }}
,{ "pid":12345, "tid":22, "ts":1751353156367086, "dur":99, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_261556851D3B23B2.mvfrm" }}
,{ "pid":12345, "tid":22, "ts":1751353156367221, "dur":202, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_261556851D3B23B2.mvfrm" }}
,{ "pid":12345, "tid":22, "ts":1751353156367430, "dur":77, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353156367507, "dur":760, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.dll.mvfrm.rsp" }}
,{ "pid":12345, "tid":22, "ts":1751353156368269, "dur":947, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353156369217, "dur":1407, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353156370625, "dur":1834, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353156372459, "dur":1114, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353156373573, "dur":79, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353156373653, "dur":886, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353156374539, "dur":255, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353156374795, "dur":127, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353156374923, "dur":581, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353156375505, "dur":1133, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353156376639, "dur":1006, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353156377646, "dur":2433, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353156380080, "dur":1005, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353156381091, "dur":51, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":22, "ts":1751353156381166, "dur":1218, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":22, "ts":1751353156382386, "dur":58062, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353156358482, "dur":7985, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353156366472, "dur":260, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_0DD02120343DEAE3.mvfrm" }}
,{ "pid":12345, "tid":23, "ts":1751353156366782, "dur":108, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_C065E2E457D01111.mvfrm" }}
,{ "pid":12345, "tid":23, "ts":1751353156366940, "dur":330, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_103705979AC6E0CE.mvfrm" }}
,{ "pid":12345, "tid":23, "ts":1751353156367271, "dur":163, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353156367457, "dur":120, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.Timeline.rsp" }}
,{ "pid":12345, "tid":23, "ts":1751353156368412, "dur":731, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Ports\\IUnitOutputPort.cs" }}
,{ "pid":12345, "tid":23, "ts":1751353156367643, "dur":1528, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353156369172, "dur":648, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353156369820, "dur":1404, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353156371225, "dur":489, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353156371715, "dur":689, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353156372404, "dur":1140, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353156374160, "dur":3450, "ph":"X", "name": "CopyFiles",  "args": { "detail":"Library/Bee/PlayerScriptAssemblies/UnityEngine.UI.pdb" }}
,{ "pid":12345, "tid":23, "ts":1751353156377664, "dur":2412, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353156380077, "dur":1022, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":23, "ts":1751353156381100, "dur":59326, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353156358540, "dur":7936, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353156366477, "dur":149, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_D79DA43A6C4320A9.mvfrm" }}
,{ "pid":12345, "tid":24, "ts":1751353156366627, "dur":148, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353156366789, "dur":106, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_57801C939D0EB92F.mvfrm" }}
,{ "pid":12345, "tid":24, "ts":1751353156366949, "dur":107, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_AF7B180A90C4DB84.mvfrm" }}
,{ "pid":12345, "tid":24, "ts":1751353156367092, "dur":92, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B5651178AA90B44B.mvfrm" }}
,{ "pid":12345, "tid":24, "ts":1751353156367275, "dur":150, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353156367426, "dur":911, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"WriteText Library/Bee/artifacts/1300b0aP.dag/Unity.TextMeshPro.UnityAdditionalFile.txt" }}
,{ "pid":12345, "tid":24, "ts":1751353156368355, "dur":703, "ph":"X", "name": "File",  "args": { "detail":"Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Animation\\BoltNamedAnimationEvent.cs" }}
,{ "pid":12345, "tid":24, "ts":1751353156368355, "dur":1375, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353156369730, "dur":665, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353156370396, "dur":1303, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353156371699, "dur":1129, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353156372828, "dur":747, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353156373576, "dur":92, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353156373668, "dur":862, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353156374531, "dur":261, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353156374793, "dur":75, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353156374868, "dur":593, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353156375462, "dur":1169, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353156376632, "dur":1015, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353156377647, "dur":2424, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353156380072, "dur":1068, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":24, "ts":1751353156381141, "dur":59300, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1751353156578905, "dur":1547, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 41616, "tid": 1750, "ts": 1751353159167611, "dur": 27, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"} },
{ "pid": 41616, "tid": 1750, "ts": 1751353159167861, "dur": 29, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"} },
{ "pid": 41616, "tid": 1750, "ts": 1751353159185598, "dur": 74, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 41616, "tid": 1750, "ts": 1751353159167681, "dur": 179, "ph": "X", "name": "buildprogram0.traceevents", "args": {} },
{ "pid": 41616, "tid": 1750, "ts": 1751353159167924, "dur": 17672, "ph": "X", "name": "backend2.traceevents", "args": {} },
{ "pid": 41616, "tid": 1750, "ts": 1751353159185736, "dur": 735, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 41616, "tid": 1750, "ts": 1751353159165146, "dur": 21379, "ph": "X", "name": "Write chrome-trace events", "args": {} },

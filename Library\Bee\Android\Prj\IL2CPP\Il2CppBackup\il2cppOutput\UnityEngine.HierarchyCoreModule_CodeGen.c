﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void EmbeddedAttribute__ctor_mBEB579BA6CE1E5989F6BCD6A92FB20B1BE6CE1A9 (void);
extern void IsUnmanagedAttribute__ctor_m4900A280630A806FFA80D5A66584F13E382D47BD (void);
extern void HierarchyFlattenedNodeChildren__ctor_m8AC3BF871970BAF681EE66B85B5C0B50D4AFFD80 (void);
extern void HierarchyFlattenedNodeChildren_GetEnumerator_m27413EAA1D7D76843DEE1E7290A438C8F88CF6B8 (void);
extern void HierarchyFlattenedNodeChildren_ThrowIfVersionChanged_m198A54234217FDB3617705CAB0F3A27FF6CC4CB1 (void);
extern void Enumerator__ctor_m30A17079B94E7221C4186553270AE22E21662496 (void);
extern void Enumerator_get_Current_mC1D8E1674A4DE5C07188471B1835A94679785951 (void);
extern void Enumerator_MoveNext_m690204CD89156C7C0ED6D6E92B96AF95DAE1F670 (void);
extern void HierarchyNodeChildren__ctor_m0F0E21CB9BA58AF8054362B1CA0C5834927FA9BE (void);
extern void HierarchyNodeChildren_GetEnumerator_mD659B18C89F3476A6AF3480911B6CEF424548DEF (void);
extern void HierarchyNodeChildren_ThrowIfVersionChanged_m2E94CB53F2BC5A4249D7AAA537CDD31A230E3BB3 (void);
extern void Enumerator__ctor_mA99EF7E777CC41161AE2596F2D48A879B00CCDF4 (void);
extern void Enumerator_get_Current_mAAF88AAE73FCBE51FD11CB6AD0FE0AFCDD0C6DBC (void);
extern void Enumerator_MoveNext_m3D954DD94C21904424A7B3EB23277A1BE30D8E68 (void);
extern void HierarchyNodeTypeHandlerBase_Initialize_mBCC2B7409E2EA40D1FF40BA2D9359F07D7E87E8A (void);
extern void HierarchyNodeTypeHandlerBase_Dispose_m8D478D9F37EF17AD35A806D798C1307ED3FF7829 (void);
extern void HierarchyNodeTypeHandlerBase_GetNodeTypeName_m241DDF39580C2AD29F9BBF6B39314799DACB0524 (void);
extern void HierarchyNodeTypeHandlerBase_GetDefaultNodeFlags_m47E9A691B588C6AC4270012A68BDB708D5D2398D (void);
extern void HierarchyNodeTypeHandlerBase_SearchBegin_m421AF9A267AAD8CCD7F881AA6064A84213590BF9 (void);
extern void HierarchyNodeTypeHandlerBase_SearchMatch_m699075573AC2EF98E0500AC25FB987FF952E4DFB (void);
extern void HierarchyNodeTypeHandlerBase_SearchEnd_mADFAE1C0BCB7E5FEA7BC0D5438B5B3480D269B68 (void);
extern void HierarchyNodeTypeHandlerBase_FromIntPtr_m267C97E11A9C666435EC622B34524D5C51040057 (void);
extern void HierarchyNodeTypeHandlerBase_Internal_SearchBegin_m0DD5CECFE4A707872E0493EF6947E82FF83BD1FD (void);
extern void HierarchyNodeTypeHandlerBase_CreateNodeTypeHandlerFromType_mCD0807A5478C9DB9602E575DF2018906059C8DCA (void);
extern void HierarchyNodeTypeHandlerBase_TryGetStaticNodeType_mC2806FA44F0055F7A303FD9251F920E0BD03D6B3 (void);
extern void HierarchyNodeTypeHandlerBase_InvokeInitialize_mC35776E8018BC80D2359F7B55C877099DBA2EBF2 (void);
extern void HierarchyNodeTypeHandlerBase_InvokeDispose_mD93A95E912C04DD3E2A4AB8579CD089FEF3432A2 (void);
extern void HierarchyNodeTypeHandlerBase_InvokeGetNodeTypeName_mDC3E4800AC4842DD651424CB4E6E44AE2BD08C41 (void);
extern void HierarchyNodeTypeHandlerBase_InvokeGetDefaultNodeFlags_m49A37F71FA6A9AFCA584F4B0E0020019B94DAF9D (void);
extern void HierarchyNodeTypeHandlerBase_InvokeChangesPending_mDDECBCDB826A8E14031FC2F68D17141B5998A990 (void);
extern void HierarchyNodeTypeHandlerBase_InvokeIntegrateChanges_m69BCC2EF97E3DEAE019538B1FC2F42047652CE0F (void);
extern void HierarchyNodeTypeHandlerBase_InvokeSearchMatch_mFE4B3E143D4B0B9AAC5EE7089A7B1F11BC95DAD5 (void);
extern void HierarchyNodeTypeHandlerBase_InvokeSearchEnd_m52015ED0E86A21E98CAF84067CB86E3141E9B18E (void);
extern void HierarchyNodeTypeHandlerBase_ChangesPending_m37A728892FE051DD5A0F91ED4B7489BF3F27F530 (void);
extern void HierarchyNodeTypeHandlerBase_IntegrateChanges_m96242FFEEAA20B9F642FCC1008B3ED4F7C92C096 (void);
extern void HierarchyNodeTypeHandlerBase__cctor_mC2F99B7C9238AF39EEDDA4F8B8F8DB6CD5DE8E04 (void);
extern void HierarchyNodeTypeHandlerBase_GetNodeTypeName_Injected_m47338B91637B89723433E4D66B745645CDEBCF8D (void);
extern void HierarchyNodeTypeHandlerBase_GetDefaultNodeFlags_Injected_m4485DF09CD5C4F15C7FAE4C3FB935191B9BBE403 (void);
extern void HierarchyNodeTypeHandlerBase_SearchBegin_Injected_m1567770AE816DC10E3E48BBE9CB2F92FE646230E (void);
extern void HierarchyNodeTypeHandlerBase_SearchMatch_Injected_m054E12E09618D8A3C6E6B93338749DE12DCCA12D (void);
extern void HierarchyNodeTypeHandlerBase_SearchEnd_Injected_mE137C402C2C9CBBD30B18660B3CB3210EE26E873 (void);
extern void HierarchyNodeTypeHandlerBase_ChangesPending_Injected_m044E1FA0E5ADA90030391C5D7E2A94AACDB11A4D (void);
extern void HierarchyNodeTypeHandlerBase_IntegrateChanges_Injected_mA46B56EC872E0A36F77BB2DC6589149A2D73C2E9 (void);
extern void BindingsMarshaller_ConvertToNative_m8CD657DAE26D9C5F3C6ACD5BC16C3530B29ABE57 (void);
extern void ConstructorScope_set_Ptr_m8D6008088DEE97BDE40CE073816B08843054999E (void);
extern void ConstructorScope_set_Hierarchy_m288E75AB12EA1E687120E6B4E982BDF0709F5414 (void);
extern void ConstructorScope_set_CommandList_mDCAC16E11D8320D299A51DAE39487FB3380E3FF9 (void);
extern void ConstructorScope__ctor_m5206FF1B0BC7B354ADDBB032498DECC069DCBF60 (void);
extern void ConstructorScope_Dispose_m6C9839EFB1F77810EA89AB6FA98CE076D508889C (void);
extern void HierarchyNodeTypeHandlerBaseEnumerable__ctor_m74A6F14CF8E83EF2018C457BDD801C6BCCC0BCBF (void);
extern void HierarchyNodeTypeHandlerBaseEnumerable_GetEnumerator_mC4238937FE4C1B682BC937ECDF43E8B55C3A5782 (void);
extern void Enumerator__ctor_m483CE9122BDCE84FCD5256E925C0F2B760524683 (void);
extern void Enumerator_Dispose_m4279DC1C5317D178525B7AC9A0768DE254C547F5 (void);
extern void Enumerator_get_Current_m7D70CAF47D0A1020791233A20ED007CBC386DD64 (void);
extern void Enumerator_MoveNext_mF41149CF855F3F56B56FACE2CE8AB69C6393DC1A (void);
extern void DefaultHierarchySearchQueryParser__ctor_m34B870A817DCCCE6D243F045E5C83774B7054F8C (void);
extern void DefaultHierarchySearchQueryParser__cctor_m97BD1DDF1CEFB6A082B927C3D110373761BED2E0 (void);
extern void HierarchyViewNodesEnumerable__ctor_m53EC593453B8CD808154886919E7061699B3AB25 (void);
extern void HierarchyViewNodesEnumerable_GetEnumerator_m22661C940333831D65E62A7C175A4DADB08933A7 (void);
extern void Predicate__ctor_mB6FF1341DD3FF5652BD5E8FC22B1F1BBADED1AC7 (void);
extern void Predicate_Invoke_m2EED29629820B41DD3751A6378D39DB6B3D95E85 (void);
extern void Enumerator__ctor_m7269ECB66FCD4A00C97288056F3E0E91C38F141A (void);
extern void Enumerator_get_Current_mDF04B16F11D35CAB17C557FFEBE67C43E57FC18D (void);
extern void Enumerator_MoveNext_mD1B75EB7609DBA7FDAFA568CDA78CF08F56CFA6D (void);
extern void Enumerator_ThrowIfVersionChanged_mAF3FC9F0ED9368565A6D7F83A7F0082B1EC8DF8B (void);
extern void Hierarchy_get_IsCreated_m08695FC5EC92DF183FFC8F2C808D1BB9F4AD15F0 (void);
extern void Hierarchy_get_Root_mCEEC9C5FA53B148812E905135F01FEF2BD51B5A8 (void);
extern void Hierarchy_get_UpdateNeeded_m96098C1BDC054B19EAF6ED467F0E3586093BA85A (void);
extern void Hierarchy_get_Version_m2E5ADDBED3F506E46A1B69B1FC384E4B216EDE21 (void);
extern void Hierarchy__ctor_m16B00B937EBF9234239D2A1BDA6A681B86A37B44 (void);
extern void Hierarchy__ctor_m8F2658812447A8376BAF5D8D67CBFF3683E1BBEC (void);
extern void Hierarchy_Finalize_m66E78AF7356EF9E9D3C3C3C0D127B88DC256341E (void);
extern void Hierarchy_Dispose_m6FB935EC693289B6CB873B375CD4BCE0C725FB9D (void);
extern void Hierarchy_Dispose_m4AF286C061ED1F8B5241F2E0732B95B91AF551C4 (void);
extern void Hierarchy_EnumerateNodeTypeHandlersBase_mDFE78F2FB5594A507A528850C08C9B1458C89EB2 (void);
extern void Hierarchy_Exists_m6FA63D652D9FD377648E57DBF9FC4A477ABA5549 (void);
extern void Hierarchy_Add_m5D3006C6FBB0C535B72CC7D8CB5FADE6C8D0DF30 (void);
extern void Hierarchy_SetParent_m37CBAEECCE0192E9DEBD41D80C045D98F8CE686B (void);
extern void Hierarchy_GetParent_m700B07D32B7368F9824897E6181ABD90936D9E3C (void);
extern void Hierarchy_GetChildren_m739EEB74733BD4371A074C1D268B5665C1E80B3A (void);
extern void Hierarchy_EnumerateChildren_mDA2C2FAE3532B95F44B02020386340E5B8E5506D (void);
extern void Hierarchy_GetChildrenCount_mA43B855E1310FF421FB4259CB3247300A35E271A (void);
extern void Hierarchy_SetSortIndex_m4A82DB026BAE482EABF5E2EADEE93CB0BAB51608 (void);
extern void Hierarchy_SortChildren_m82A3832EBCCE3ECD6652892516599BCD33C85199 (void);
extern void Hierarchy_Update_m24FC5A02802B2A03CC4B5B7B404C6B7F5D8D9F02 (void);
extern void Hierarchy_FromIntPtr_m8E935E3D910488B516DD7B087EB778458136D8C7 (void);
extern void Hierarchy_Create_mCFE1642188047D39C5119ECCFF49CBC8112013AF (void);
extern void Hierarchy_Destroy_m2E764C18383AB7C8840D3EAF672DAE9CF317D701 (void);
extern void Hierarchy_GetNodeTypeHandlersBaseCount_m4C2585D0ABD80E9984DED8A5BCA614B970248BC1 (void);
extern void Hierarchy_GetNodeTypeHandlersBaseSpan_m1F0E2D06E09CE7C7FAFC9265E8026CC616ADF655 (void);
extern void Hierarchy_AddNode_m800867B92E4D645D4B1D95D56540F71F28BF5068 (void);
extern void Hierarchy_EnumerateChildrenPtr_m1E65A48D1031E5383715204CBE121A8CDDF07D0D (void);
extern void Hierarchy_GetOrCreateProperty_m0126839A6A29D0B4A9B0D667FAD2A6656E72F51B (void);
extern void Hierarchy_SetPropertyRaw_mA332C6BD28DF5169E69B73F779928D94B839E518 (void);
extern void Hierarchy_GetPropertyRaw_mABF313A6578AEDF732A2850314BF4D05FF5BE4C6 (void);
extern void Hierarchy_CreateHierarchy_m5D652213629F06002F238A103F87856DAEC9DECE (void);
extern void Hierarchy_get_UpdateNeeded_Injected_mD3A9410997B238FAED439DAD548A535E03AE2B49 (void);
extern void Hierarchy_Exists_Injected_mCEA544DFC36BC08791BFD9F773272F4C5EBF6E86 (void);
extern void Hierarchy_SetParent_Injected_m8D89D1CBC4D8654CBBC3270E295AD70CBA341DDD (void);
extern void Hierarchy_GetParent_Injected_mE0E66144220BF2293332FF2B0DE94DC530181CBB (void);
extern void Hierarchy_GetChildren_Injected_mE658C6F51B03B7769C179D9DEA9EA8DD30A048DD (void);
extern void Hierarchy_GetChildrenCount_Injected_m17613CDF61F41CE802BB395844AFE06B5C8B73DF (void);
extern void Hierarchy_SetSortIndex_Injected_mBE76D5DEA17365606C3B96BB6165F7E98324075B (void);
extern void Hierarchy_SortChildren_Injected_mF8CF1D82F7C3683C8848E57CD9E61AED6BE06B89 (void);
extern void Hierarchy_Update_Injected_m0C6F04654ADBF455EFFCE4067AE76C893D4C353B (void);
extern void Hierarchy_GetNodeTypeHandlersBaseCount_Injected_mF3DB3C1CDB0AD663307E6618AAFB91210B86ECCD (void);
extern void Hierarchy_GetNodeTypeHandlersBaseSpan_Injected_mDD8493B82887F376623407AB056CD64292A76CD7 (void);
extern void Hierarchy_AddNode_Injected_m90DBC3C5781BFF44DCDEB22E63F0B1A3A295AA8E (void);
extern void Hierarchy_EnumerateChildrenPtr_Injected_m1AA51F8A51B1F43DFFF2DAEFFB5A1F19342AFA2E (void);
extern void Hierarchy_GetOrCreateProperty_Injected_m99278E78633C6708CC4B76D04E07D3ACB1B66FF1 (void);
extern void Hierarchy_SetPropertyRaw_Injected_mDB38A73EA5B521FFC2ECB2F9ABC2A186F0EF2182 (void);
extern void Hierarchy_GetPropertyRaw_Injected_m99CFBA475DD3B5729034DE7641691CACC18AAF9F (void);
extern void BindingsMarshaller_ConvertToNative_m0F58FF94D3F6D41B04651DB80089086F3510EB51 (void);
extern void HierarchyCommandList__ctor_m24496A8E3B7E98108D78D8D5A9FC6B7FCE2FEDF2 (void);
extern void HierarchyCommandList_Finalize_mD5A4446A3126075000424DF400AD6D1C15C33FD8 (void);
extern void HierarchyCommandList_Dispose_mFCCDB6883C8645E3859178B3BE5DE32E9495B0E3 (void);
extern void HierarchyCommandList_Dispose_mFA65FE8E02BEE9CA04C626E68E4CD80EA891C09E (void);
extern void HierarchyCommandList_FromIntPtr_m3FA698CC8809DA9CAC1571CE27AA2F5CBB404518 (void);
extern void HierarchyCommandList_Destroy_mC8A8EBF8AA2C14819F9CAE01B2B2EA276606AA42 (void);
extern void HierarchyCommandList_CreateCommandList_m3B3A1407ACE8093E22B8B7A13022DEB47AEEFA51 (void);
extern void BindingsMarshaller_ConvertToNative_m151B8CE983BB122B1CDEFCF05DA8635B94BF470E (void);
extern void HierarchyFlattened_get_IsCreated_mF9F0EB95D7EED9B222255869606FDB4A8DBA5F19 (void);
extern void HierarchyFlattened_get_Count_mDD854A0A55467ED5F2B9E9899B569956EE3F520E (void);
extern void HierarchyFlattened_get_UpdateNeeded_m941C326B46A61F397DAF450EC6138218B588785A (void);
extern void HierarchyFlattened_get_Hierarchy_mC621A9310F8FACD94F6C403DF34DA36DD3EB15D7 (void);
extern void HierarchyFlattened_get_NodesPtr_m82D74456C3698CFB95BA48376B7C21D121EB2C94 (void);
extern void HierarchyFlattened_get_Version_m4BF021EF6FEA9ADCB90432B3CD4B67179FF1B3A0 (void);
extern void HierarchyFlattened__ctor_mC762879AD3157E8EEE63AF5962807F3051F8602C (void);
extern void HierarchyFlattened__ctor_m4323F7293F0FB0A013D78DF9BA4EA9367317F451 (void);
extern void HierarchyFlattened_Finalize_mAF96882DFEB0B43F80BF842ABEA05931DA093EDA (void);
extern void HierarchyFlattened_Dispose_m1314C20974F2460774D8DDD134A7F025E01F732F (void);
extern void HierarchyFlattened_Dispose_m9E4DA3F7853EFA0288877576909A4DA2D0D3844B (void);
extern void HierarchyFlattened_get_Item_m9159A2651AFF2E08D4715B01A161BF97D8417E24 (void);
extern void HierarchyFlattened_IndexOf_m4BC24AEFB11B5E76FF0A17F1F7052230747EF7C8 (void);
extern void HierarchyFlattened_Contains_mA5555DD1EE4AC081AEFF11C9313C34E52F55B00A (void);
extern void HierarchyFlattened_EnumerateChildren_m056609BC6A39D8EE73E0CD5FBABBE35EC768B277 (void);
extern void HierarchyFlattened_GetChildrenCount_m7DFFA9156EB8107D6E7AD57E2959CEA98E69B6BC (void);
extern void HierarchyFlattened_Update_m8F47D4B1AECB5D59C515852BE9D0ABBDB689C763 (void);
extern void HierarchyFlattened_GetEnumerator_mD927AFAF72F8006645E85F33546B211ABC42E281 (void);
extern void HierarchyFlattened_FromIntPtr_m056568C851831B34FDEF288EB6283D9412D0C150 (void);
extern void HierarchyFlattened_Create_m68795D8EBB1C83DB21DA816C88F2A05123C8D137 (void);
extern void HierarchyFlattened_Destroy_m342950E43F6BC7FC0F8EC41B9C0630DFB056F9FA (void);
extern void HierarchyFlattened_CreateHierarchyFlattened_m7A6AD21A6C19FBB8EF48C5B0C16D2FEB9924052A (void);
extern void HierarchyFlattened_UpdateHierarchyFlattened_m8437C2DB424424839933EEFCBBC04603CB343982 (void);
extern void HierarchyFlattened_get_UpdateNeeded_Injected_mF639A9BA0A0AC054ABCF7DF5C1B590B52858BB6E (void);
extern void HierarchyFlattened_IndexOf_Injected_m3F9AC09637D1FA63994E770FC238420B1EE448FA (void);
extern void HierarchyFlattened_Contains_Injected_m8C4D81B534A9843CB0435F8D59D8B6F178477A80 (void);
extern void HierarchyFlattened_GetChildrenCount_Injected_mD58CCF497644595E7F1F052E94D8A434219C696A (void);
extern void HierarchyFlattened_Update_Injected_mD8EB38E51DCFB046355A5F095C5FCB1CCCF2ABC6 (void);
extern void HierarchyFlattened_Create_Injected_mBE6D383EDCC79EDC1E1FDF399EDFEB344DCE0A71 (void);
extern void BindingsMarshaller_ConvertToNative_m603AB6888881E37AF9CB17D6734ABC855F2EDD8F (void);
extern void Enumerator__ctor_mD9F50F6DFED8781B165E659C74C2BDAAC6423ECA (void);
extern void Enumerator_get_Current_m76B49BD7F00C5079CDC0BB1ACE611018B820FE29 (void);
extern void Enumerator_MoveNext_m7EFD96EC5FF7E669EA4720311A0337C37471B208 (void);
extern void HierarchyFlattenedNode_get_Null_mB560C4A856498FD30CEEB6E332CAE07C203D035C (void);
extern void HierarchyFlattenedNode_get_Node_m3300E071E1C9AE0841FF14B78F87529A3F0270A7 (void);
extern void HierarchyFlattenedNode_get_NextSiblingOffset_m3264B96FAC2A80B541CC7A25EA6A157E233E7026 (void);
extern void HierarchyFlattenedNode_get_ChildrenCount_mC424A4AF7C6639A7B95FA45F610CAE6C6E5B77AD (void);
extern void HierarchyFlattenedNode_op_Equality_m9B962D1F7DD49ABA2007173EDEA58C088D100146 (void);
extern void HierarchyFlattenedNode_Equals_m84AF827EDEE1DF08FB68E4A246E13D2D6E49FFCC (void);
extern void HierarchyFlattenedNode_ToString_m5DB9A1AE3A137B8A693A65BF67E4629000833560 (void);
extern void HierarchyFlattenedNode_Equals_mD7CCECE4687CAB017FF646FB26A63118B3E8EF1F (void);
extern void HierarchyFlattenedNode_GetHashCode_mC788C5FBAEA281CB55C888350D7F74B8FA4ACE55 (void);
extern void HierarchyFlattenedNode_GetNodeByRef_m2963B04C52D523366F8D97169A429753D09A1AB1 (void);
extern void HierarchyNode_get_Null_mD35B2ECC1C0479D2621B81F92A3816ACDB5D2DAB (void);
extern void HierarchyNode_get_Id_mF2B5D36F53FAD484A8B5BCF36BB70BD85B0D06C8 (void);
extern void HierarchyNode_get_Version_m0A17AB68CE616656EFF72AB94A5B02824CCD0DEF (void);
extern void HierarchyNode__ctor_mD501006228CB122DDF2CAA27F940D1BAADB000B9 (void);
extern void HierarchyNode_op_Equality_mCEB92910CAB26F3D4BCC81AD2DA49AFCDD8DDA59 (void);
extern void HierarchyNode_op_Inequality_m41B2944C7B6E47D2DE06DEDCE160AE652ABC342C (void);
extern void HierarchyNode_Equals_m21AA316B1263A399682DD3DF95419B0EE699B9AC (void);
extern void HierarchyNode_ToString_m393B16519275D2F761A5664F62F1F89C7576EE83 (void);
extern void HierarchyNode_Equals_m0FF62873EFA0A0B67F63D93DAA27F370B7460AA2 (void);
extern void HierarchyNode_GetHashCode_m4942E49DB4DAABA2353958AB4BE213B7FA546574 (void);
extern void HierarchyNodeType_get_Null_m953BE84EE135D1E9A33A4F5609510A151FF60DF1 (void);
extern void HierarchyNodeType_get_Id_mB584AF3C7EE66323EE1C3CFDEFF400B5E30CFB2E (void);
extern void HierarchyNodeType_op_Equality_mE4D552A0E85E1869F846FA7146DA80340A75B585 (void);
extern void HierarchyNodeType_Equals_mC1ABF19667AE18B967EAA0087E8ACCB00FB09B86 (void);
extern void HierarchyNodeType_ToString_mF1B22CBC191CD587C3C6744430C6B88CA1CBD6A9 (void);
extern void HierarchyNodeType_Equals_mC28897DB00E62C8F91EF555751869853AA42568B (void);
extern void HierarchyNodeType_GetHashCode_mD56E6A6C18B5602DD3EB1E77DDF0C4ADB8BA04CA (void);
extern void HierarchyPropertyDescriptor_set_Size_mA64BDAED69EE9895DCFF64C44002E3761F799FEA (void);
extern void HierarchyPropertyDescriptor_set_Type_m857827AE0E2F766B6A6DA81B1B46D8CFDF72B993 (void);
extern void HierarchyPropertyId_get_Null_m3DC815C8477AEBD8BE81015FF68D915AC04ACB49 (void);
extern void HierarchyPropertyId_get_Id_m672DD9BDD736D4A11EE17934F2BD1A5FB594B15F (void);
extern void HierarchyPropertyId__ctor_mFD8B1B7B5760F60CFE8D4ED92477D3BB5B26781C (void);
extern void HierarchyPropertyId_op_Equality_mE9B18ABE7318AB6592DE11BCDDF173A4E4397B22 (void);
extern void HierarchyPropertyId_Equals_m59AA71C4377B78891640E4F12F601B08E50C9889 (void);
extern void HierarchyPropertyId_ToString_m855C9258A3754F31D1D435F360917F785CBAF79F (void);
extern void HierarchyPropertyId_Equals_mE6D8F146E12F12F18EE0C1B2347A5D9AA83DFE7E (void);
extern void HierarchyPropertyId_GetHashCode_mAE3558EA8116702BD990773B56BE749B6D839207 (void);
extern void HierarchySearchFilter_get_Invalid_m70ED0C85C26D95074AA1346B22472AA1682EF05E (void);
extern void HierarchySearchFilter_get_IsValid_m57196B68CAD2B3A2F36094A71BD2D8510B4EDF29 (void);
extern void HierarchySearchFilter_get_Name_m0669ABA00F5156E06B704544AE30B70FF059CF2D (void);
extern void HierarchySearchFilter_get_Value_mD9D097264FAFD84679DFF24EBB7A5DDAF0422CE9 (void);
extern void HierarchySearchFilter_get_NumValue_mC2707817899B5D9635393269CFA9886F7B7E276E (void);
extern void HierarchySearchFilter_get_Op_m6075B3B7DC8D0E011EC76F929D7CB05E01E16A94 (void);
extern void HierarchySearchFilter_ToString_mC3ACC2548C0E6810E70400920B69FC4CF5C6BF87 (void);
extern void HierarchySearchFilter_ToString_mE726A902E60DDDD1E51502ECB14F64171B9CD2A3 (void);
extern void HierarchySearchFilter_QuoteStringIfNeeded_m56A25E25CBD369AAE246D5FA8F7B35C22AA82729 (void);
extern void HierarchySearchFilter__cctor_mB8B44ADFCAC006A798CC15253EEB8030878A390B (void);
extern void HierarchySearchQueryDescriptor_get_SystemFilters_m2EE3E917292F2FFBE7BE3947825D96835ADEDC6E (void);
extern void HierarchySearchQueryDescriptor_set_SystemFilters_m6968284953A186AEAC1DA6C04526EE41A173F600 (void);
extern void HierarchySearchQueryDescriptor_get_Filters_m7D26A9BFC776A25580D0D3E0204D29596CE6D1BE (void);
extern void HierarchySearchQueryDescriptor_set_Filters_m71FBF5269CD15DB9C64D9ACD31F70E476CD00A8D (void);
extern void HierarchySearchQueryDescriptor_get_TextValues_mC857F86AE79D60D505D14D6789F6C6B3B5FD0DA5 (void);
extern void HierarchySearchQueryDescriptor_set_TextValues_m017B6DB0FF641AFE378EA2ECAE545975F4964471 (void);
extern void HierarchySearchQueryDescriptor_set_Strict_m7239912F9481C8EC715361DDB68D220E6762868F (void);
extern void HierarchySearchQueryDescriptor_set_Invalid_mB39F85C58907EA8D6E5B81A172D3F8296FD1EB94 (void);
extern void HierarchySearchQueryDescriptor__ctor_m7C24033C1656B46818ED2DF492D08ACEB590B3DF (void);
extern void HierarchySearchQueryDescriptor_ToString_m93930794C65D2F97EB21E143D18373E2F763F4F3 (void);
extern void HierarchySearchQueryDescriptor_BuildFilterQuery_mEA76C67D0718F63CD633E2B708B0B0174A6211A7 (void);
extern void HierarchySearchQueryDescriptor_BuildSystemFilterQuery_mCA55E38ED2C160F00E9168365D727F1AEF1804A3 (void);
extern void HierarchySearchQueryDescriptor_BuildTextQuery_m8EC7ABED08D68C37BB2C264027F6D8EAAAAF3636 (void);
extern void HierarchySearchQueryDescriptor_BuildQuery_m64EAC5227F72467FCCD0EA9D34B87842C1BD6CC2 (void);
extern void HierarchySearchQueryDescriptor__cctor_m8EF8E8298F1EA50C2AE1C4069DD7780A1035BDFB (void);
extern void U3CU3Ec__cctor_m9D4224C3FD337D1F8712605036E3BF92F37A3959 (void);
extern void U3CU3Ec__ctor_m63E6DDFB1EC4295E0603D4F0CC0AA9365B345117 (void);
extern void U3CU3Ec_U3C_ctorU3Eb__33_0_mA8AC5EAC6B1F4BFF4490BEA7F8F4055590572EEC (void);
extern void U3CU3Ec_U3C_ctorU3Eb__33_1_m9E22E0718515F3351C6C68A00F1C860F3EB1EB54 (void);
extern void HierarchyViewModel_get_IsCreated_m6373B153869C3224129527BD441AFFC8752FEF92 (void);
extern void HierarchyViewModel_get_Count_mCBA57E1783F3EFC942A04ABC550BFE5FE8762F43 (void);
extern void HierarchyViewModel_get_UpdateNeeded_m1E805F2CCAB472AC619E6586C8DC3D3F437FF5D1 (void);
extern void HierarchyViewModel_get_HierarchyFlattened_m7274791CBD27225D248900D66322DFCE74E9CD71 (void);
extern void HierarchyViewModel_get_Version_m62676BD287B35A63EB39D21ADB121F5CDB3340F1 (void);
extern void HierarchyViewModel_set_QueryParser_mB43B16415C0D0208DD5C4CEDE4BFE5A7F9CFA708 (void);
extern void HierarchyViewModel_get_Query_mDA3284F8F7587BB8F1EE1B4EB10649622DB51E8E (void);
extern void HierarchyViewModel__ctor_m4D77037698BC715A8C9F7C72179741135E162172 (void);
extern void HierarchyViewModel__ctor_mCF4165CC9FCB57C7CD3568705B3626C3B75DA15F (void);
extern void HierarchyViewModel_Finalize_m8C8B86BC9957F69A15265E29D1F8DD0667C41704 (void);
extern void HierarchyViewModel_Dispose_m04F833515685BDDE10B411655AB31BF96FB48E1B (void);
extern void HierarchyViewModel_Dispose_mC4D49DBFE5C0AF4AD436E84FD4FF66DC8F039CA0 (void);
extern void HierarchyViewModel_get_Item_m136B770458540B95AF2C6B9EC041A06FD67998AF (void);
extern void HierarchyViewModel_IndexOf_m2547755D71FB38A32C5E6652AF8ABFC0932E1871 (void);
extern void HierarchyViewModel_Contains_m9ED39E8D14D50E85A06E1BF003C5C35CFFBF9A6D (void);
extern void HierarchyViewModel_GetChildrenCount_mDA41A437C9C3B81F491759C6FABD6064DE01CA8F (void);
extern void HierarchyViewModel_SetFlags_m8ACF05C9B80E2A614E35FEBBB5CC796328BFDEAC (void);
extern void HierarchyViewModel_HasAllFlags_m7255BF14683B037484CCAC743743D594C11C576D (void);
extern void HierarchyViewModel_ClearFlags_m01130A019C62F03552920060C386E9C002B3CE61 (void);
extern void HierarchyViewModel_EnumerateNodesWithAllFlags_m61A518F8817D46BFDB6EE7DA58E0EE10EB4271B4 (void);
extern void HierarchyViewModel_Update_mECA75CE00981EC0FAD15E68FCE973EC168D080F1 (void);
extern void HierarchyViewModel_GetEnumerator_m6DCD97D82AF9079DA61FBD0329AD4D400A65257C (void);
extern void HierarchyViewModel_FromIntPtr_m10B188DEE8064F7B36E08E3AAEB5F846CAD7C89D (void);
extern void HierarchyViewModel_Create_m28CDEDAEC5106133E4F9300E808297AB39C13F35 (void);
extern void HierarchyViewModel_Destroy_m98A8D09575380A0F312FBA377F7049926BB00ECE (void);
extern void HierarchyViewModel_SetFlagsNode_mDBD06C6F1F34E509193616D1DBE847164C1EB5DB (void);
extern void HierarchyViewModel_HasAllFlagsNode_m6735138F172E7305AD53AEF8E6844C088DBE1AB7 (void);
extern void HierarchyViewModel_ClearFlagsNode_m499231173B54A5AFE911D2211FEE5C4B7AF2ED42 (void);
extern void HierarchyViewModel_CreateHierarchyViewModel_m64C3EF266CD7462D45F236AC3C10CFB4C28FBD8A (void);
extern void HierarchyViewModel_UpdateHierarchyViewModel_m04A7814544BAB3CA352EFEBE645503C0B5421DDA (void);
extern void HierarchyViewModel_SearchBegin_mC3201E10D659CD658BE6AFFC1F680FE4F9395813 (void);
extern void HierarchyViewModel_get_UpdateNeeded_Injected_m455F0662343F338549C2F827A7A59485A4A80C84 (void);
extern void HierarchyViewModel_get_Query_Injected_m9E2068EA6BEFE23A6EBEB3572862597E62D20A40 (void);
extern void HierarchyViewModel_IndexOf_Injected_mB55FD8D854ADF92F571B58B6B942162087C7DF67 (void);
extern void HierarchyViewModel_Contains_Injected_mF077F41FF42704DC1B3EB88B541C0F228202B9AC (void);
extern void HierarchyViewModel_GetChildrenCount_Injected_m4B80A1D550BF50535394344F45C7053EBE969A6A (void);
extern void HierarchyViewModel_Update_Injected_mF54D16567A1B0AFCB8B2C3765298946F1966432B (void);
extern void HierarchyViewModel_Create_Injected_m99A253762279A6AC35CDAB9AC4F872D174C0E80A (void);
extern void HierarchyViewModel_SetFlagsNode_Injected_m336A711E3C9F127BE98613F6B11BA569E04735A0 (void);
extern void HierarchyViewModel_HasAllFlagsNode_Injected_mF7F62FA75F49596DBCF0997BAAD7B7FE12671C50 (void);
extern void HierarchyViewModel_ClearFlagsNode_Injected_mF463A7C45AD5250E510B4250D68C7616879B2661 (void);
extern void BindingsMarshaller_ConvertToNative_mC9215547DB5A62A0AE63A44AEA82D898A7C477A8 (void);
extern void Enumerator__ctor_m92AD996B2BC7A1DF6BEF8439EB170DA6B312DFAA (void);
extern void Enumerator_get_Current_mB05DA6A7C28996C4C1DD3ABC40170DC8E163A23E (void);
extern void Enumerator_MoveNext_m7E7EB4465D2111E7970D54B950EB6572E1352254 (void);
static Il2CppMethodPointer s_methodPointers[278] = 
{
	EmbeddedAttribute__ctor_mBEB579BA6CE1E5989F6BCD6A92FB20B1BE6CE1A9,
	IsUnmanagedAttribute__ctor_m4900A280630A806FFA80D5A66584F13E382D47BD,
	HierarchyFlattenedNodeChildren__ctor_m8AC3BF871970BAF681EE66B85B5C0B50D4AFFD80,
	HierarchyFlattenedNodeChildren_GetEnumerator_m27413EAA1D7D76843DEE1E7290A438C8F88CF6B8,
	HierarchyFlattenedNodeChildren_ThrowIfVersionChanged_m198A54234217FDB3617705CAB0F3A27FF6CC4CB1,
	Enumerator__ctor_m30A17079B94E7221C4186553270AE22E21662496,
	Enumerator_get_Current_mC1D8E1674A4DE5C07188471B1835A94679785951,
	Enumerator_MoveNext_m690204CD89156C7C0ED6D6E92B96AF95DAE1F670,
	HierarchyNodeChildren__ctor_m0F0E21CB9BA58AF8054362B1CA0C5834927FA9BE,
	HierarchyNodeChildren_GetEnumerator_mD659B18C89F3476A6AF3480911B6CEF424548DEF,
	HierarchyNodeChildren_ThrowIfVersionChanged_m2E94CB53F2BC5A4249D7AAA537CDD31A230E3BB3,
	Enumerator__ctor_mA99EF7E777CC41161AE2596F2D48A879B00CCDF4,
	Enumerator_get_Current_mAAF88AAE73FCBE51FD11CB6AD0FE0AFCDD0C6DBC,
	Enumerator_MoveNext_m3D954DD94C21904424A7B3EB23277A1BE30D8E68,
	HierarchyNodeTypeHandlerBase_Initialize_mBCC2B7409E2EA40D1FF40BA2D9359F07D7E87E8A,
	HierarchyNodeTypeHandlerBase_Dispose_m8D478D9F37EF17AD35A806D798C1307ED3FF7829,
	HierarchyNodeTypeHandlerBase_GetNodeTypeName_m241DDF39580C2AD29F9BBF6B39314799DACB0524,
	HierarchyNodeTypeHandlerBase_GetDefaultNodeFlags_m47E9A691B588C6AC4270012A68BDB708D5D2398D,
	HierarchyNodeTypeHandlerBase_SearchBegin_m421AF9A267AAD8CCD7F881AA6064A84213590BF9,
	HierarchyNodeTypeHandlerBase_SearchMatch_m699075573AC2EF98E0500AC25FB987FF952E4DFB,
	HierarchyNodeTypeHandlerBase_SearchEnd_mADFAE1C0BCB7E5FEA7BC0D5438B5B3480D269B68,
	HierarchyNodeTypeHandlerBase_FromIntPtr_m267C97E11A9C666435EC622B34524D5C51040057,
	HierarchyNodeTypeHandlerBase_Internal_SearchBegin_m0DD5CECFE4A707872E0493EF6947E82FF83BD1FD,
	HierarchyNodeTypeHandlerBase_CreateNodeTypeHandlerFromType_mCD0807A5478C9DB9602E575DF2018906059C8DCA,
	HierarchyNodeTypeHandlerBase_TryGetStaticNodeType_mC2806FA44F0055F7A303FD9251F920E0BD03D6B3,
	HierarchyNodeTypeHandlerBase_InvokeInitialize_mC35776E8018BC80D2359F7B55C877099DBA2EBF2,
	HierarchyNodeTypeHandlerBase_InvokeDispose_mD93A95E912C04DD3E2A4AB8579CD089FEF3432A2,
	HierarchyNodeTypeHandlerBase_InvokeGetNodeTypeName_mDC3E4800AC4842DD651424CB4E6E44AE2BD08C41,
	HierarchyNodeTypeHandlerBase_InvokeGetDefaultNodeFlags_m49A37F71FA6A9AFCA584F4B0E0020019B94DAF9D,
	HierarchyNodeTypeHandlerBase_InvokeChangesPending_mDDECBCDB826A8E14031FC2F68D17141B5998A990,
	HierarchyNodeTypeHandlerBase_InvokeIntegrateChanges_m69BCC2EF97E3DEAE019538B1FC2F42047652CE0F,
	HierarchyNodeTypeHandlerBase_InvokeSearchMatch_mFE4B3E143D4B0B9AAC5EE7089A7B1F11BC95DAD5,
	HierarchyNodeTypeHandlerBase_InvokeSearchEnd_m52015ED0E86A21E98CAF84067CB86E3141E9B18E,
	HierarchyNodeTypeHandlerBase_ChangesPending_m37A728892FE051DD5A0F91ED4B7489BF3F27F530,
	HierarchyNodeTypeHandlerBase_IntegrateChanges_m96242FFEEAA20B9F642FCC1008B3ED4F7C92C096,
	HierarchyNodeTypeHandlerBase__cctor_mC2F99B7C9238AF39EEDDA4F8B8F8DB6CD5DE8E04,
	HierarchyNodeTypeHandlerBase_GetNodeTypeName_Injected_m47338B91637B89723433E4D66B745645CDEBCF8D,
	HierarchyNodeTypeHandlerBase_GetDefaultNodeFlags_Injected_m4485DF09CD5C4F15C7FAE4C3FB935191B9BBE403,
	HierarchyNodeTypeHandlerBase_SearchBegin_Injected_m1567770AE816DC10E3E48BBE9CB2F92FE646230E,
	HierarchyNodeTypeHandlerBase_SearchMatch_Injected_m054E12E09618D8A3C6E6B93338749DE12DCCA12D,
	HierarchyNodeTypeHandlerBase_SearchEnd_Injected_mE137C402C2C9CBBD30B18660B3CB3210EE26E873,
	HierarchyNodeTypeHandlerBase_ChangesPending_Injected_m044E1FA0E5ADA90030391C5D7E2A94AACDB11A4D,
	HierarchyNodeTypeHandlerBase_IntegrateChanges_Injected_mA46B56EC872E0A36F77BB2DC6589149A2D73C2E9,
	BindingsMarshaller_ConvertToNative_m8CD657DAE26D9C5F3C6ACD5BC16C3530B29ABE57,
	ConstructorScope_set_Ptr_m8D6008088DEE97BDE40CE073816B08843054999E,
	ConstructorScope_set_Hierarchy_m288E75AB12EA1E687120E6B4E982BDF0709F5414,
	ConstructorScope_set_CommandList_mDCAC16E11D8320D299A51DAE39487FB3380E3FF9,
	ConstructorScope__ctor_m5206FF1B0BC7B354ADDBB032498DECC069DCBF60,
	ConstructorScope_Dispose_m6C9839EFB1F77810EA89AB6FA98CE076D508889C,
	HierarchyNodeTypeHandlerBaseEnumerable__ctor_m74A6F14CF8E83EF2018C457BDD801C6BCCC0BCBF,
	HierarchyNodeTypeHandlerBaseEnumerable_GetEnumerator_mC4238937FE4C1B682BC937ECDF43E8B55C3A5782,
	Enumerator__ctor_m483CE9122BDCE84FCD5256E925C0F2B760524683,
	Enumerator_Dispose_m4279DC1C5317D178525B7AC9A0768DE254C547F5,
	Enumerator_get_Current_m7D70CAF47D0A1020791233A20ED007CBC386DD64,
	Enumerator_MoveNext_mF41149CF855F3F56B56FACE2CE8AB69C6393DC1A,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	DefaultHierarchySearchQueryParser__ctor_m34B870A817DCCCE6D243F045E5C83774B7054F8C,
	DefaultHierarchySearchQueryParser__cctor_m97BD1DDF1CEFB6A082B927C3D110373761BED2E0,
	HierarchyViewNodesEnumerable__ctor_m53EC593453B8CD808154886919E7061699B3AB25,
	HierarchyViewNodesEnumerable_GetEnumerator_m22661C940333831D65E62A7C175A4DADB08933A7,
	Predicate__ctor_mB6FF1341DD3FF5652BD5E8FC22B1F1BBADED1AC7,
	Predicate_Invoke_m2EED29629820B41DD3751A6378D39DB6B3D95E85,
	Enumerator__ctor_m7269ECB66FCD4A00C97288056F3E0E91C38F141A,
	Enumerator_get_Current_mDF04B16F11D35CAB17C557FFEBE67C43E57FC18D,
	Enumerator_MoveNext_mD1B75EB7609DBA7FDAFA568CDA78CF08F56CFA6D,
	Enumerator_ThrowIfVersionChanged_mAF3FC9F0ED9368565A6D7F83A7F0082B1EC8DF8B,
	NULL,
	NULL,
	Hierarchy_get_IsCreated_m08695FC5EC92DF183FFC8F2C808D1BB9F4AD15F0,
	Hierarchy_get_Root_mCEEC9C5FA53B148812E905135F01FEF2BD51B5A8,
	Hierarchy_get_UpdateNeeded_m96098C1BDC054B19EAF6ED467F0E3586093BA85A,
	Hierarchy_get_Version_m2E5ADDBED3F506E46A1B69B1FC384E4B216EDE21,
	Hierarchy__ctor_m16B00B937EBF9234239D2A1BDA6A681B86A37B44,
	Hierarchy__ctor_m8F2658812447A8376BAF5D8D67CBFF3683E1BBEC,
	Hierarchy_Finalize_m66E78AF7356EF9E9D3C3C3C0D127B88DC256341E,
	Hierarchy_Dispose_m6FB935EC693289B6CB873B375CD4BCE0C725FB9D,
	Hierarchy_Dispose_m4AF286C061ED1F8B5241F2E0732B95B91AF551C4,
	Hierarchy_EnumerateNodeTypeHandlersBase_mDFE78F2FB5594A507A528850C08C9B1458C89EB2,
	Hierarchy_Exists_m6FA63D652D9FD377648E57DBF9FC4A477ABA5549,
	Hierarchy_Add_m5D3006C6FBB0C535B72CC7D8CB5FADE6C8D0DF30,
	Hierarchy_SetParent_m37CBAEECCE0192E9DEBD41D80C045D98F8CE686B,
	Hierarchy_GetParent_m700B07D32B7368F9824897E6181ABD90936D9E3C,
	Hierarchy_GetChildren_m739EEB74733BD4371A074C1D268B5665C1E80B3A,
	Hierarchy_EnumerateChildren_mDA2C2FAE3532B95F44B02020386340E5B8E5506D,
	Hierarchy_GetChildrenCount_mA43B855E1310FF421FB4259CB3247300A35E271A,
	Hierarchy_SetSortIndex_m4A82DB026BAE482EABF5E2EADEE93CB0BAB51608,
	Hierarchy_SortChildren_m82A3832EBCCE3ECD6652892516599BCD33C85199,
	NULL,
	Hierarchy_Update_m24FC5A02802B2A03CC4B5B7B404C6B7F5D8D9F02,
	Hierarchy_FromIntPtr_m8E935E3D910488B516DD7B087EB778458136D8C7,
	Hierarchy_Create_mCFE1642188047D39C5119ECCFF49CBC8112013AF,
	Hierarchy_Destroy_m2E764C18383AB7C8840D3EAF672DAE9CF317D701,
	Hierarchy_GetNodeTypeHandlersBaseCount_m4C2585D0ABD80E9984DED8A5BCA614B970248BC1,
	Hierarchy_GetNodeTypeHandlersBaseSpan_m1F0E2D06E09CE7C7FAFC9265E8026CC616ADF655,
	Hierarchy_AddNode_m800867B92E4D645D4B1D95D56540F71F28BF5068,
	Hierarchy_EnumerateChildrenPtr_m1E65A48D1031E5383715204CBE121A8CDDF07D0D,
	Hierarchy_GetOrCreateProperty_m0126839A6A29D0B4A9B0D667FAD2A6656E72F51B,
	Hierarchy_SetPropertyRaw_mA332C6BD28DF5169E69B73F779928D94B839E518,
	Hierarchy_GetPropertyRaw_mABF313A6578AEDF732A2850314BF4D05FF5BE4C6,
	Hierarchy_CreateHierarchy_m5D652213629F06002F238A103F87856DAEC9DECE,
	Hierarchy_get_UpdateNeeded_Injected_mD3A9410997B238FAED439DAD548A535E03AE2B49,
	Hierarchy_Exists_Injected_mCEA544DFC36BC08791BFD9F773272F4C5EBF6E86,
	Hierarchy_SetParent_Injected_m8D89D1CBC4D8654CBBC3270E295AD70CBA341DDD,
	Hierarchy_GetParent_Injected_mE0E66144220BF2293332FF2B0DE94DC530181CBB,
	Hierarchy_GetChildren_Injected_mE658C6F51B03B7769C179D9DEA9EA8DD30A048DD,
	Hierarchy_GetChildrenCount_Injected_m17613CDF61F41CE802BB395844AFE06B5C8B73DF,
	Hierarchy_SetSortIndex_Injected_mBE76D5DEA17365606C3B96BB6165F7E98324075B,
	Hierarchy_SortChildren_Injected_mF8CF1D82F7C3683C8848E57CD9E61AED6BE06B89,
	Hierarchy_Update_Injected_m0C6F04654ADBF455EFFCE4067AE76C893D4C353B,
	Hierarchy_GetNodeTypeHandlersBaseCount_Injected_mF3DB3C1CDB0AD663307E6618AAFB91210B86ECCD,
	Hierarchy_GetNodeTypeHandlersBaseSpan_Injected_mDD8493B82887F376623407AB056CD64292A76CD7,
	Hierarchy_AddNode_Injected_m90DBC3C5781BFF44DCDEB22E63F0B1A3A295AA8E,
	Hierarchy_EnumerateChildrenPtr_Injected_m1AA51F8A51B1F43DFFF2DAEFFB5A1F19342AFA2E,
	Hierarchy_GetOrCreateProperty_Injected_m99278E78633C6708CC4B76D04E07D3ACB1B66FF1,
	Hierarchy_SetPropertyRaw_Injected_mDB38A73EA5B521FFC2ECB2F9ABC2A186F0EF2182,
	Hierarchy_GetPropertyRaw_Injected_m99CFBA475DD3B5729034DE7641691CACC18AAF9F,
	BindingsMarshaller_ConvertToNative_m0F58FF94D3F6D41B04651DB80089086F3510EB51,
	HierarchyCommandList__ctor_m24496A8E3B7E98108D78D8D5A9FC6B7FCE2FEDF2,
	HierarchyCommandList_Finalize_mD5A4446A3126075000424DF400AD6D1C15C33FD8,
	HierarchyCommandList_Dispose_mFCCDB6883C8645E3859178B3BE5DE32E9495B0E3,
	HierarchyCommandList_Dispose_mFA65FE8E02BEE9CA04C626E68E4CD80EA891C09E,
	HierarchyCommandList_FromIntPtr_m3FA698CC8809DA9CAC1571CE27AA2F5CBB404518,
	HierarchyCommandList_Destroy_mC8A8EBF8AA2C14819F9CAE01B2B2EA276606AA42,
	HierarchyCommandList_CreateCommandList_m3B3A1407ACE8093E22B8B7A13022DEB47AEEFA51,
	BindingsMarshaller_ConvertToNative_m151B8CE983BB122B1CDEFCF05DA8635B94BF470E,
	HierarchyFlattened_get_IsCreated_mF9F0EB95D7EED9B222255869606FDB4A8DBA5F19,
	HierarchyFlattened_get_Count_mDD854A0A55467ED5F2B9E9899B569956EE3F520E,
	HierarchyFlattened_get_UpdateNeeded_m941C326B46A61F397DAF450EC6138218B588785A,
	HierarchyFlattened_get_Hierarchy_mC621A9310F8FACD94F6C403DF34DA36DD3EB15D7,
	HierarchyFlattened_get_NodesPtr_m82D74456C3698CFB95BA48376B7C21D121EB2C94,
	HierarchyFlattened_get_Version_m4BF021EF6FEA9ADCB90432B3CD4B67179FF1B3A0,
	HierarchyFlattened__ctor_mC762879AD3157E8EEE63AF5962807F3051F8602C,
	HierarchyFlattened__ctor_m4323F7293F0FB0A013D78DF9BA4EA9367317F451,
	HierarchyFlattened_Finalize_mAF96882DFEB0B43F80BF842ABEA05931DA093EDA,
	HierarchyFlattened_Dispose_m1314C20974F2460774D8DDD134A7F025E01F732F,
	HierarchyFlattened_Dispose_m9E4DA3F7853EFA0288877576909A4DA2D0D3844B,
	HierarchyFlattened_get_Item_m9159A2651AFF2E08D4715B01A161BF97D8417E24,
	HierarchyFlattened_IndexOf_m4BC24AEFB11B5E76FF0A17F1F7052230747EF7C8,
	HierarchyFlattened_Contains_mA5555DD1EE4AC081AEFF11C9313C34E52F55B00A,
	HierarchyFlattened_EnumerateChildren_m056609BC6A39D8EE73E0CD5FBABBE35EC768B277,
	HierarchyFlattened_GetChildrenCount_m7DFFA9156EB8107D6E7AD57E2959CEA98E69B6BC,
	HierarchyFlattened_Update_m8F47D4B1AECB5D59C515852BE9D0ABBDB689C763,
	HierarchyFlattened_GetEnumerator_mD927AFAF72F8006645E85F33546B211ABC42E281,
	HierarchyFlattened_FromIntPtr_m056568C851831B34FDEF288EB6283D9412D0C150,
	HierarchyFlattened_Create_m68795D8EBB1C83DB21DA816C88F2A05123C8D137,
	HierarchyFlattened_Destroy_m342950E43F6BC7FC0F8EC41B9C0630DFB056F9FA,
	HierarchyFlattened_CreateHierarchyFlattened_m7A6AD21A6C19FBB8EF48C5B0C16D2FEB9924052A,
	HierarchyFlattened_UpdateHierarchyFlattened_m8437C2DB424424839933EEFCBBC04603CB343982,
	HierarchyFlattened_get_UpdateNeeded_Injected_mF639A9BA0A0AC054ABCF7DF5C1B590B52858BB6E,
	HierarchyFlattened_IndexOf_Injected_m3F9AC09637D1FA63994E770FC238420B1EE448FA,
	HierarchyFlattened_Contains_Injected_m8C4D81B534A9843CB0435F8D59D8B6F178477A80,
	HierarchyFlattened_GetChildrenCount_Injected_mD58CCF497644595E7F1F052E94D8A434219C696A,
	HierarchyFlattened_Update_Injected_mD8EB38E51DCFB046355A5F095C5FCB1CCCF2ABC6,
	HierarchyFlattened_Create_Injected_mBE6D383EDCC79EDC1E1FDF399EDFEB344DCE0A71,
	BindingsMarshaller_ConvertToNative_m603AB6888881E37AF9CB17D6734ABC855F2EDD8F,
	Enumerator__ctor_mD9F50F6DFED8781B165E659C74C2BDAAC6423ECA,
	Enumerator_get_Current_m76B49BD7F00C5079CDC0BB1ACE611018B820FE29,
	Enumerator_MoveNext_m7EFD96EC5FF7E669EA4720311A0337C37471B208,
	HierarchyFlattenedNode_get_Null_mB560C4A856498FD30CEEB6E332CAE07C203D035C,
	HierarchyFlattenedNode_get_Node_m3300E071E1C9AE0841FF14B78F87529A3F0270A7,
	HierarchyFlattenedNode_get_NextSiblingOffset_m3264B96FAC2A80B541CC7A25EA6A157E233E7026,
	HierarchyFlattenedNode_get_ChildrenCount_mC424A4AF7C6639A7B95FA45F610CAE6C6E5B77AD,
	HierarchyFlattenedNode_op_Equality_m9B962D1F7DD49ABA2007173EDEA58C088D100146,
	HierarchyFlattenedNode_Equals_m84AF827EDEE1DF08FB68E4A246E13D2D6E49FFCC,
	HierarchyFlattenedNode_ToString_m5DB9A1AE3A137B8A693A65BF67E4629000833560,
	HierarchyFlattenedNode_Equals_mD7CCECE4687CAB017FF646FB26A63118B3E8EF1F,
	HierarchyFlattenedNode_GetHashCode_mC788C5FBAEA281CB55C888350D7F74B8FA4ACE55,
	HierarchyFlattenedNode_GetNodeByRef_m2963B04C52D523366F8D97169A429753D09A1AB1,
	HierarchyNode_get_Null_mD35B2ECC1C0479D2621B81F92A3816ACDB5D2DAB,
	HierarchyNode_get_Id_mF2B5D36F53FAD484A8B5BCF36BB70BD85B0D06C8,
	HierarchyNode_get_Version_m0A17AB68CE616656EFF72AB94A5B02824CCD0DEF,
	HierarchyNode__ctor_mD501006228CB122DDF2CAA27F940D1BAADB000B9,
	HierarchyNode_op_Equality_mCEB92910CAB26F3D4BCC81AD2DA49AFCDD8DDA59,
	HierarchyNode_op_Inequality_m41B2944C7B6E47D2DE06DEDCE160AE652ABC342C,
	HierarchyNode_Equals_m21AA316B1263A399682DD3DF95419B0EE699B9AC,
	HierarchyNode_ToString_m393B16519275D2F761A5664F62F1F89C7576EE83,
	HierarchyNode_Equals_m0FF62873EFA0A0B67F63D93DAA27F370B7460AA2,
	HierarchyNode_GetHashCode_m4942E49DB4DAABA2353958AB4BE213B7FA546574,
	HierarchyNodeType_get_Null_m953BE84EE135D1E9A33A4F5609510A151FF60DF1,
	HierarchyNodeType_get_Id_mB584AF3C7EE66323EE1C3CFDEFF400B5E30CFB2E,
	HierarchyNodeType_op_Equality_mE4D552A0E85E1869F846FA7146DA80340A75B585,
	HierarchyNodeType_Equals_mC1ABF19667AE18B967EAA0087E8ACCB00FB09B86,
	HierarchyNodeType_ToString_mF1B22CBC191CD587C3C6744430C6B88CA1CBD6A9,
	HierarchyNodeType_Equals_mC28897DB00E62C8F91EF555751869853AA42568B,
	HierarchyNodeType_GetHashCode_mD56E6A6C18B5602DD3EB1E77DDF0C4ADB8BA04CA,
	HierarchyPropertyDescriptor_set_Size_mA64BDAED69EE9895DCFF64C44002E3761F799FEA,
	HierarchyPropertyDescriptor_set_Type_m857827AE0E2F766B6A6DA81B1B46D8CFDF72B993,
	HierarchyPropertyId_get_Null_m3DC815C8477AEBD8BE81015FF68D915AC04ACB49,
	HierarchyPropertyId_get_Id_m672DD9BDD736D4A11EE17934F2BD1A5FB594B15F,
	HierarchyPropertyId__ctor_mFD8B1B7B5760F60CFE8D4ED92477D3BB5B26781C,
	HierarchyPropertyId_op_Equality_mE9B18ABE7318AB6592DE11BCDDF173A4E4397B22,
	HierarchyPropertyId_Equals_m59AA71C4377B78891640E4F12F601B08E50C9889,
	HierarchyPropertyId_ToString_m855C9258A3754F31D1D435F360917F785CBAF79F,
	HierarchyPropertyId_Equals_mE6D8F146E12F12F18EE0C1B2347A5D9AA83DFE7E,
	HierarchyPropertyId_GetHashCode_mAE3558EA8116702BD990773B56BE749B6D839207,
	HierarchySearchFilter_get_Invalid_m70ED0C85C26D95074AA1346B22472AA1682EF05E,
	HierarchySearchFilter_get_IsValid_m57196B68CAD2B3A2F36094A71BD2D8510B4EDF29,
	HierarchySearchFilter_get_Name_m0669ABA00F5156E06B704544AE30B70FF059CF2D,
	HierarchySearchFilter_get_Value_mD9D097264FAFD84679DFF24EBB7A5DDAF0422CE9,
	HierarchySearchFilter_get_NumValue_mC2707817899B5D9635393269CFA9886F7B7E276E,
	HierarchySearchFilter_get_Op_m6075B3B7DC8D0E011EC76F929D7CB05E01E16A94,
	HierarchySearchFilter_ToString_mC3ACC2548C0E6810E70400920B69FC4CF5C6BF87,
	HierarchySearchFilter_ToString_mE726A902E60DDDD1E51502ECB14F64171B9CD2A3,
	HierarchySearchFilter_QuoteStringIfNeeded_m56A25E25CBD369AAE246D5FA8F7B35C22AA82729,
	HierarchySearchFilter__cctor_mB8B44ADFCAC006A798CC15253EEB8030878A390B,
	HierarchySearchQueryDescriptor_get_SystemFilters_m2EE3E917292F2FFBE7BE3947825D96835ADEDC6E,
	HierarchySearchQueryDescriptor_set_SystemFilters_m6968284953A186AEAC1DA6C04526EE41A173F600,
	HierarchySearchQueryDescriptor_get_Filters_m7D26A9BFC776A25580D0D3E0204D29596CE6D1BE,
	HierarchySearchQueryDescriptor_set_Filters_m71FBF5269CD15DB9C64D9ACD31F70E476CD00A8D,
	HierarchySearchQueryDescriptor_get_TextValues_mC857F86AE79D60D505D14D6789F6C6B3B5FD0DA5,
	HierarchySearchQueryDescriptor_set_TextValues_m017B6DB0FF641AFE378EA2ECAE545975F4964471,
	HierarchySearchQueryDescriptor_set_Strict_m7239912F9481C8EC715361DDB68D220E6762868F,
	HierarchySearchQueryDescriptor_set_Invalid_mB39F85C58907EA8D6E5B81A172D3F8296FD1EB94,
	HierarchySearchQueryDescriptor__ctor_m7C24033C1656B46818ED2DF492D08ACEB590B3DF,
	HierarchySearchQueryDescriptor_ToString_m93930794C65D2F97EB21E143D18373E2F763F4F3,
	HierarchySearchQueryDescriptor_BuildFilterQuery_mEA76C67D0718F63CD633E2B708B0B0174A6211A7,
	HierarchySearchQueryDescriptor_BuildSystemFilterQuery_mCA55E38ED2C160F00E9168365D727F1AEF1804A3,
	HierarchySearchQueryDescriptor_BuildTextQuery_m8EC7ABED08D68C37BB2C264027F6D8EAAAAF3636,
	HierarchySearchQueryDescriptor_BuildQuery_m64EAC5227F72467FCCD0EA9D34B87842C1BD6CC2,
	NULL,
	HierarchySearchQueryDescriptor__cctor_m8EF8E8298F1EA50C2AE1C4069DD7780A1035BDFB,
	U3CU3Ec__cctor_m9D4224C3FD337D1F8712605036E3BF92F37A3959,
	U3CU3Ec__ctor_m63E6DDFB1EC4295E0603D4F0CC0AA9365B345117,
	U3CU3Ec_U3C_ctorU3Eb__33_0_mA8AC5EAC6B1F4BFF4490BEA7F8F4055590572EEC,
	U3CU3Ec_U3C_ctorU3Eb__33_1_m9E22E0718515F3351C6C68A00F1C860F3EB1EB54,
	HierarchyViewModel_get_IsCreated_m6373B153869C3224129527BD441AFFC8752FEF92,
	HierarchyViewModel_get_Count_mCBA57E1783F3EFC942A04ABC550BFE5FE8762F43,
	HierarchyViewModel_get_UpdateNeeded_m1E805F2CCAB472AC619E6586C8DC3D3F437FF5D1,
	HierarchyViewModel_get_HierarchyFlattened_m7274791CBD27225D248900D66322DFCE74E9CD71,
	HierarchyViewModel_get_Version_m62676BD287B35A63EB39D21ADB121F5CDB3340F1,
	HierarchyViewModel_set_QueryParser_mB43B16415C0D0208DD5C4CEDE4BFE5A7F9CFA708,
	HierarchyViewModel_get_Query_mDA3284F8F7587BB8F1EE1B4EB10649622DB51E8E,
	HierarchyViewModel__ctor_m4D77037698BC715A8C9F7C72179741135E162172,
	HierarchyViewModel__ctor_mCF4165CC9FCB57C7CD3568705B3626C3B75DA15F,
	HierarchyViewModel_Finalize_m8C8B86BC9957F69A15265E29D1F8DD0667C41704,
	HierarchyViewModel_Dispose_m04F833515685BDDE10B411655AB31BF96FB48E1B,
	HierarchyViewModel_Dispose_mC4D49DBFE5C0AF4AD436E84FD4FF66DC8F039CA0,
	HierarchyViewModel_get_Item_m136B770458540B95AF2C6B9EC041A06FD67998AF,
	HierarchyViewModel_IndexOf_m2547755D71FB38A32C5E6652AF8ABFC0932E1871,
	HierarchyViewModel_Contains_m9ED39E8D14D50E85A06E1BF003C5C35CFFBF9A6D,
	HierarchyViewModel_GetChildrenCount_mDA41A437C9C3B81F491759C6FABD6064DE01CA8F,
	HierarchyViewModel_SetFlags_m8ACF05C9B80E2A614E35FEBBB5CC796328BFDEAC,
	HierarchyViewModel_HasAllFlags_m7255BF14683B037484CCAC743743D594C11C576D,
	HierarchyViewModel_ClearFlags_m01130A019C62F03552920060C386E9C002B3CE61,
	HierarchyViewModel_EnumerateNodesWithAllFlags_m61A518F8817D46BFDB6EE7DA58E0EE10EB4271B4,
	HierarchyViewModel_Update_mECA75CE00981EC0FAD15E68FCE973EC168D080F1,
	HierarchyViewModel_GetEnumerator_m6DCD97D82AF9079DA61FBD0329AD4D400A65257C,
	HierarchyViewModel_FromIntPtr_m10B188DEE8064F7B36E08E3AAEB5F846CAD7C89D,
	HierarchyViewModel_Create_m28CDEDAEC5106133E4F9300E808297AB39C13F35,
	HierarchyViewModel_Destroy_m98A8D09575380A0F312FBA377F7049926BB00ECE,
	HierarchyViewModel_SetFlagsNode_mDBD06C6F1F34E509193616D1DBE847164C1EB5DB,
	HierarchyViewModel_HasAllFlagsNode_m6735138F172E7305AD53AEF8E6844C088DBE1AB7,
	HierarchyViewModel_ClearFlagsNode_m499231173B54A5AFE911D2211FEE5C4B7AF2ED42,
	HierarchyViewModel_CreateHierarchyViewModel_m64C3EF266CD7462D45F236AC3C10CFB4C28FBD8A,
	HierarchyViewModel_UpdateHierarchyViewModel_m04A7814544BAB3CA352EFEBE645503C0B5421DDA,
	HierarchyViewModel_SearchBegin_mC3201E10D659CD658BE6AFFC1F680FE4F9395813,
	HierarchyViewModel_get_UpdateNeeded_Injected_m455F0662343F338549C2F827A7A59485A4A80C84,
	HierarchyViewModel_get_Query_Injected_m9E2068EA6BEFE23A6EBEB3572862597E62D20A40,
	HierarchyViewModel_IndexOf_Injected_mB55FD8D854ADF92F571B58B6B942162087C7DF67,
	HierarchyViewModel_Contains_Injected_mF077F41FF42704DC1B3EB88B541C0F228202B9AC,
	HierarchyViewModel_GetChildrenCount_Injected_m4B80A1D550BF50535394344F45C7053EBE969A6A,
	HierarchyViewModel_Update_Injected_mF54D16567A1B0AFCB8B2C3765298946F1966432B,
	HierarchyViewModel_Create_Injected_m99A253762279A6AC35CDAB9AC4F872D174C0E80A,
	HierarchyViewModel_SetFlagsNode_Injected_m336A711E3C9F127BE98613F6B11BA569E04735A0,
	HierarchyViewModel_HasAllFlagsNode_Injected_mF7F62FA75F49596DBCF0997BAAD7B7FE12671C50,
	HierarchyViewModel_ClearFlagsNode_Injected_mF463A7C45AD5250E510B4250D68C7616879B2661,
	BindingsMarshaller_ConvertToNative_mC9215547DB5A62A0AE63A44AEA82D898A7C477A8,
	Enumerator__ctor_m92AD996B2BC7A1DF6BEF8439EB170DA6B312DFAA,
	Enumerator_get_Current_mB05DA6A7C28996C4C1DD3ABC40170DC8E163A23E,
	Enumerator_MoveNext_m7E7EB4465D2111E7970D54B950EB6572E1352254,
};
extern void HierarchyFlattenedNodeChildren__ctor_m8AC3BF871970BAF681EE66B85B5C0B50D4AFFD80_AdjustorThunk (void);
extern void HierarchyFlattenedNodeChildren_GetEnumerator_m27413EAA1D7D76843DEE1E7290A438C8F88CF6B8_AdjustorThunk (void);
extern void HierarchyFlattenedNodeChildren_ThrowIfVersionChanged_m198A54234217FDB3617705CAB0F3A27FF6CC4CB1_AdjustorThunk (void);
extern void Enumerator__ctor_m30A17079B94E7221C4186553270AE22E21662496_AdjustorThunk (void);
extern void Enumerator_get_Current_mC1D8E1674A4DE5C07188471B1835A94679785951_AdjustorThunk (void);
extern void Enumerator_MoveNext_m690204CD89156C7C0ED6D6E92B96AF95DAE1F670_AdjustorThunk (void);
extern void HierarchyNodeChildren__ctor_m0F0E21CB9BA58AF8054362B1CA0C5834927FA9BE_AdjustorThunk (void);
extern void HierarchyNodeChildren_GetEnumerator_mD659B18C89F3476A6AF3480911B6CEF424548DEF_AdjustorThunk (void);
extern void HierarchyNodeChildren_ThrowIfVersionChanged_m2E94CB53F2BC5A4249D7AAA537CDD31A230E3BB3_AdjustorThunk (void);
extern void Enumerator__ctor_mA99EF7E777CC41161AE2596F2D48A879B00CCDF4_AdjustorThunk (void);
extern void Enumerator_get_Current_mAAF88AAE73FCBE51FD11CB6AD0FE0AFCDD0C6DBC_AdjustorThunk (void);
extern void Enumerator_MoveNext_m3D954DD94C21904424A7B3EB23277A1BE30D8E68_AdjustorThunk (void);
extern void ConstructorScope__ctor_m5206FF1B0BC7B354ADDBB032498DECC069DCBF60_AdjustorThunk (void);
extern void ConstructorScope_Dispose_m6C9839EFB1F77810EA89AB6FA98CE076D508889C_AdjustorThunk (void);
extern void HierarchyNodeTypeHandlerBaseEnumerable__ctor_m74A6F14CF8E83EF2018C457BDD801C6BCCC0BCBF_AdjustorThunk (void);
extern void HierarchyNodeTypeHandlerBaseEnumerable_GetEnumerator_mC4238937FE4C1B682BC937ECDF43E8B55C3A5782_AdjustorThunk (void);
extern void Enumerator__ctor_m483CE9122BDCE84FCD5256E925C0F2B760524683_AdjustorThunk (void);
extern void Enumerator_Dispose_m4279DC1C5317D178525B7AC9A0768DE254C547F5_AdjustorThunk (void);
extern void Enumerator_get_Current_m7D70CAF47D0A1020791233A20ED007CBC386DD64_AdjustorThunk (void);
extern void Enumerator_MoveNext_mF41149CF855F3F56B56FACE2CE8AB69C6393DC1A_AdjustorThunk (void);
extern void HierarchyViewNodesEnumerable__ctor_m53EC593453B8CD808154886919E7061699B3AB25_AdjustorThunk (void);
extern void HierarchyViewNodesEnumerable_GetEnumerator_m22661C940333831D65E62A7C175A4DADB08933A7_AdjustorThunk (void);
extern void Enumerator__ctor_m7269ECB66FCD4A00C97288056F3E0E91C38F141A_AdjustorThunk (void);
extern void Enumerator_get_Current_mDF04B16F11D35CAB17C557FFEBE67C43E57FC18D_AdjustorThunk (void);
extern void Enumerator_MoveNext_mD1B75EB7609DBA7FDAFA568CDA78CF08F56CFA6D_AdjustorThunk (void);
extern void Enumerator_ThrowIfVersionChanged_mAF3FC9F0ED9368565A6D7F83A7F0082B1EC8DF8B_AdjustorThunk (void);
extern void Enumerator__ctor_mD9F50F6DFED8781B165E659C74C2BDAAC6423ECA_AdjustorThunk (void);
extern void Enumerator_get_Current_m76B49BD7F00C5079CDC0BB1ACE611018B820FE29_AdjustorThunk (void);
extern void Enumerator_MoveNext_m7EFD96EC5FF7E669EA4720311A0337C37471B208_AdjustorThunk (void);
extern void HierarchyFlattenedNode_get_Node_m3300E071E1C9AE0841FF14B78F87529A3F0270A7_AdjustorThunk (void);
extern void HierarchyFlattenedNode_get_NextSiblingOffset_m3264B96FAC2A80B541CC7A25EA6A157E233E7026_AdjustorThunk (void);
extern void HierarchyFlattenedNode_get_ChildrenCount_mC424A4AF7C6639A7B95FA45F610CAE6C6E5B77AD_AdjustorThunk (void);
extern void HierarchyFlattenedNode_Equals_m84AF827EDEE1DF08FB68E4A246E13D2D6E49FFCC_AdjustorThunk (void);
extern void HierarchyFlattenedNode_ToString_m5DB9A1AE3A137B8A693A65BF67E4629000833560_AdjustorThunk (void);
extern void HierarchyFlattenedNode_Equals_mD7CCECE4687CAB017FF646FB26A63118B3E8EF1F_AdjustorThunk (void);
extern void HierarchyFlattenedNode_GetHashCode_mC788C5FBAEA281CB55C888350D7F74B8FA4ACE55_AdjustorThunk (void);
extern void HierarchyNode_get_Id_mF2B5D36F53FAD484A8B5BCF36BB70BD85B0D06C8_AdjustorThunk (void);
extern void HierarchyNode_get_Version_m0A17AB68CE616656EFF72AB94A5B02824CCD0DEF_AdjustorThunk (void);
extern void HierarchyNode__ctor_mD501006228CB122DDF2CAA27F940D1BAADB000B9_AdjustorThunk (void);
extern void HierarchyNode_Equals_m21AA316B1263A399682DD3DF95419B0EE699B9AC_AdjustorThunk (void);
extern void HierarchyNode_ToString_m393B16519275D2F761A5664F62F1F89C7576EE83_AdjustorThunk (void);
extern void HierarchyNode_Equals_m0FF62873EFA0A0B67F63D93DAA27F370B7460AA2_AdjustorThunk (void);
extern void HierarchyNode_GetHashCode_m4942E49DB4DAABA2353958AB4BE213B7FA546574_AdjustorThunk (void);
extern void HierarchyNodeType_get_Id_mB584AF3C7EE66323EE1C3CFDEFF400B5E30CFB2E_AdjustorThunk (void);
extern void HierarchyNodeType_Equals_mC1ABF19667AE18B967EAA0087E8ACCB00FB09B86_AdjustorThunk (void);
extern void HierarchyNodeType_ToString_mF1B22CBC191CD587C3C6744430C6B88CA1CBD6A9_AdjustorThunk (void);
extern void HierarchyNodeType_Equals_mC28897DB00E62C8F91EF555751869853AA42568B_AdjustorThunk (void);
extern void HierarchyNodeType_GetHashCode_mD56E6A6C18B5602DD3EB1E77DDF0C4ADB8BA04CA_AdjustorThunk (void);
extern void HierarchyPropertyDescriptor_set_Size_mA64BDAED69EE9895DCFF64C44002E3761F799FEA_AdjustorThunk (void);
extern void HierarchyPropertyDescriptor_set_Type_m857827AE0E2F766B6A6DA81B1B46D8CFDF72B993_AdjustorThunk (void);
extern void HierarchyPropertyId_get_Id_m672DD9BDD736D4A11EE17934F2BD1A5FB594B15F_AdjustorThunk (void);
extern void HierarchyPropertyId__ctor_mFD8B1B7B5760F60CFE8D4ED92477D3BB5B26781C_AdjustorThunk (void);
extern void HierarchyPropertyId_Equals_m59AA71C4377B78891640E4F12F601B08E50C9889_AdjustorThunk (void);
extern void HierarchyPropertyId_ToString_m855C9258A3754F31D1D435F360917F785CBAF79F_AdjustorThunk (void);
extern void HierarchyPropertyId_Equals_mE6D8F146E12F12F18EE0C1B2347A5D9AA83DFE7E_AdjustorThunk (void);
extern void HierarchyPropertyId_GetHashCode_mAE3558EA8116702BD990773B56BE749B6D839207_AdjustorThunk (void);
extern void HierarchySearchFilter_get_IsValid_m57196B68CAD2B3A2F36094A71BD2D8510B4EDF29_AdjustorThunk (void);
extern void HierarchySearchFilter_get_Name_m0669ABA00F5156E06B704544AE30B70FF059CF2D_AdjustorThunk (void);
extern void HierarchySearchFilter_get_Value_mD9D097264FAFD84679DFF24EBB7A5DDAF0422CE9_AdjustorThunk (void);
extern void HierarchySearchFilter_get_NumValue_mC2707817899B5D9635393269CFA9886F7B7E276E_AdjustorThunk (void);
extern void HierarchySearchFilter_get_Op_m6075B3B7DC8D0E011EC76F929D7CB05E01E16A94_AdjustorThunk (void);
extern void HierarchySearchFilter_ToString_mE726A902E60DDDD1E51502ECB14F64171B9CD2A3_AdjustorThunk (void);
extern void Enumerator__ctor_m92AD996B2BC7A1DF6BEF8439EB170DA6B312DFAA_AdjustorThunk (void);
extern void Enumerator_get_Current_mB05DA6A7C28996C4C1DD3ABC40170DC8E163A23E_AdjustorThunk (void);
extern void Enumerator_MoveNext_m7E7EB4465D2111E7970D54B950EB6572E1352254_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[65] = 
{
	{ 0x06000003, HierarchyFlattenedNodeChildren__ctor_m8AC3BF871970BAF681EE66B85B5C0B50D4AFFD80_AdjustorThunk },
	{ 0x06000004, HierarchyFlattenedNodeChildren_GetEnumerator_m27413EAA1D7D76843DEE1E7290A438C8F88CF6B8_AdjustorThunk },
	{ 0x06000005, HierarchyFlattenedNodeChildren_ThrowIfVersionChanged_m198A54234217FDB3617705CAB0F3A27FF6CC4CB1_AdjustorThunk },
	{ 0x06000006, Enumerator__ctor_m30A17079B94E7221C4186553270AE22E21662496_AdjustorThunk },
	{ 0x06000007, Enumerator_get_Current_mC1D8E1674A4DE5C07188471B1835A94679785951_AdjustorThunk },
	{ 0x06000008, Enumerator_MoveNext_m690204CD89156C7C0ED6D6E92B96AF95DAE1F670_AdjustorThunk },
	{ 0x06000009, HierarchyNodeChildren__ctor_m0F0E21CB9BA58AF8054362B1CA0C5834927FA9BE_AdjustorThunk },
	{ 0x0600000A, HierarchyNodeChildren_GetEnumerator_mD659B18C89F3476A6AF3480911B6CEF424548DEF_AdjustorThunk },
	{ 0x0600000B, HierarchyNodeChildren_ThrowIfVersionChanged_m2E94CB53F2BC5A4249D7AAA537CDD31A230E3BB3_AdjustorThunk },
	{ 0x0600000C, Enumerator__ctor_mA99EF7E777CC41161AE2596F2D48A879B00CCDF4_AdjustorThunk },
	{ 0x0600000D, Enumerator_get_Current_mAAF88AAE73FCBE51FD11CB6AD0FE0AFCDD0C6DBC_AdjustorThunk },
	{ 0x0600000E, Enumerator_MoveNext_m3D954DD94C21904424A7B3EB23277A1BE30D8E68_AdjustorThunk },
	{ 0x06000030, ConstructorScope__ctor_m5206FF1B0BC7B354ADDBB032498DECC069DCBF60_AdjustorThunk },
	{ 0x06000031, ConstructorScope_Dispose_m6C9839EFB1F77810EA89AB6FA98CE076D508889C_AdjustorThunk },
	{ 0x06000032, HierarchyNodeTypeHandlerBaseEnumerable__ctor_m74A6F14CF8E83EF2018C457BDD801C6BCCC0BCBF_AdjustorThunk },
	{ 0x06000033, HierarchyNodeTypeHandlerBaseEnumerable_GetEnumerator_mC4238937FE4C1B682BC937ECDF43E8B55C3A5782_AdjustorThunk },
	{ 0x06000034, Enumerator__ctor_m483CE9122BDCE84FCD5256E925C0F2B760524683_AdjustorThunk },
	{ 0x06000035, Enumerator_Dispose_m4279DC1C5317D178525B7AC9A0768DE254C547F5_AdjustorThunk },
	{ 0x06000036, Enumerator_get_Current_m7D70CAF47D0A1020791233A20ED007CBC386DD64_AdjustorThunk },
	{ 0x06000037, Enumerator_MoveNext_mF41149CF855F3F56B56FACE2CE8AB69C6393DC1A_AdjustorThunk },
	{ 0x06000043, HierarchyViewNodesEnumerable__ctor_m53EC593453B8CD808154886919E7061699B3AB25_AdjustorThunk },
	{ 0x06000044, HierarchyViewNodesEnumerable_GetEnumerator_m22661C940333831D65E62A7C175A4DADB08933A7_AdjustorThunk },
	{ 0x06000047, Enumerator__ctor_m7269ECB66FCD4A00C97288056F3E0E91C38F141A_AdjustorThunk },
	{ 0x06000048, Enumerator_get_Current_mDF04B16F11D35CAB17C557FFEBE67C43E57FC18D_AdjustorThunk },
	{ 0x06000049, Enumerator_MoveNext_mD1B75EB7609DBA7FDAFA568CDA78CF08F56CFA6D_AdjustorThunk },
	{ 0x0600004A, Enumerator_ThrowIfVersionChanged_mAF3FC9F0ED9368565A6D7F83A7F0082B1EC8DF8B_AdjustorThunk },
	{ 0x060000A4, Enumerator__ctor_mD9F50F6DFED8781B165E659C74C2BDAAC6423ECA_AdjustorThunk },
	{ 0x060000A5, Enumerator_get_Current_m76B49BD7F00C5079CDC0BB1ACE611018B820FE29_AdjustorThunk },
	{ 0x060000A6, Enumerator_MoveNext_m7EFD96EC5FF7E669EA4720311A0337C37471B208_AdjustorThunk },
	{ 0x060000A8, HierarchyFlattenedNode_get_Node_m3300E071E1C9AE0841FF14B78F87529A3F0270A7_AdjustorThunk },
	{ 0x060000A9, HierarchyFlattenedNode_get_NextSiblingOffset_m3264B96FAC2A80B541CC7A25EA6A157E233E7026_AdjustorThunk },
	{ 0x060000AA, HierarchyFlattenedNode_get_ChildrenCount_mC424A4AF7C6639A7B95FA45F610CAE6C6E5B77AD_AdjustorThunk },
	{ 0x060000AC, HierarchyFlattenedNode_Equals_m84AF827EDEE1DF08FB68E4A246E13D2D6E49FFCC_AdjustorThunk },
	{ 0x060000AD, HierarchyFlattenedNode_ToString_m5DB9A1AE3A137B8A693A65BF67E4629000833560_AdjustorThunk },
	{ 0x060000AE, HierarchyFlattenedNode_Equals_mD7CCECE4687CAB017FF646FB26A63118B3E8EF1F_AdjustorThunk },
	{ 0x060000AF, HierarchyFlattenedNode_GetHashCode_mC788C5FBAEA281CB55C888350D7F74B8FA4ACE55_AdjustorThunk },
	{ 0x060000B2, HierarchyNode_get_Id_mF2B5D36F53FAD484A8B5BCF36BB70BD85B0D06C8_AdjustorThunk },
	{ 0x060000B3, HierarchyNode_get_Version_m0A17AB68CE616656EFF72AB94A5B02824CCD0DEF_AdjustorThunk },
	{ 0x060000B4, HierarchyNode__ctor_mD501006228CB122DDF2CAA27F940D1BAADB000B9_AdjustorThunk },
	{ 0x060000B7, HierarchyNode_Equals_m21AA316B1263A399682DD3DF95419B0EE699B9AC_AdjustorThunk },
	{ 0x060000B8, HierarchyNode_ToString_m393B16519275D2F761A5664F62F1F89C7576EE83_AdjustorThunk },
	{ 0x060000B9, HierarchyNode_Equals_m0FF62873EFA0A0B67F63D93DAA27F370B7460AA2_AdjustorThunk },
	{ 0x060000BA, HierarchyNode_GetHashCode_m4942E49DB4DAABA2353958AB4BE213B7FA546574_AdjustorThunk },
	{ 0x060000BC, HierarchyNodeType_get_Id_mB584AF3C7EE66323EE1C3CFDEFF400B5E30CFB2E_AdjustorThunk },
	{ 0x060000BE, HierarchyNodeType_Equals_mC1ABF19667AE18B967EAA0087E8ACCB00FB09B86_AdjustorThunk },
	{ 0x060000BF, HierarchyNodeType_ToString_mF1B22CBC191CD587C3C6744430C6B88CA1CBD6A9_AdjustorThunk },
	{ 0x060000C0, HierarchyNodeType_Equals_mC28897DB00E62C8F91EF555751869853AA42568B_AdjustorThunk },
	{ 0x060000C1, HierarchyNodeType_GetHashCode_mD56E6A6C18B5602DD3EB1E77DDF0C4ADB8BA04CA_AdjustorThunk },
	{ 0x060000C2, HierarchyPropertyDescriptor_set_Size_mA64BDAED69EE9895DCFF64C44002E3761F799FEA_AdjustorThunk },
	{ 0x060000C3, HierarchyPropertyDescriptor_set_Type_m857827AE0E2F766B6A6DA81B1B46D8CFDF72B993_AdjustorThunk },
	{ 0x060000C5, HierarchyPropertyId_get_Id_m672DD9BDD736D4A11EE17934F2BD1A5FB594B15F_AdjustorThunk },
	{ 0x060000C6, HierarchyPropertyId__ctor_mFD8B1B7B5760F60CFE8D4ED92477D3BB5B26781C_AdjustorThunk },
	{ 0x060000C8, HierarchyPropertyId_Equals_m59AA71C4377B78891640E4F12F601B08E50C9889_AdjustorThunk },
	{ 0x060000C9, HierarchyPropertyId_ToString_m855C9258A3754F31D1D435F360917F785CBAF79F_AdjustorThunk },
	{ 0x060000CA, HierarchyPropertyId_Equals_mE6D8F146E12F12F18EE0C1B2347A5D9AA83DFE7E_AdjustorThunk },
	{ 0x060000CB, HierarchyPropertyId_GetHashCode_mAE3558EA8116702BD990773B56BE749B6D839207_AdjustorThunk },
	{ 0x060000CD, HierarchySearchFilter_get_IsValid_m57196B68CAD2B3A2F36094A71BD2D8510B4EDF29_AdjustorThunk },
	{ 0x060000CE, HierarchySearchFilter_get_Name_m0669ABA00F5156E06B704544AE30B70FF059CF2D_AdjustorThunk },
	{ 0x060000CF, HierarchySearchFilter_get_Value_mD9D097264FAFD84679DFF24EBB7A5DDAF0422CE9_AdjustorThunk },
	{ 0x060000D0, HierarchySearchFilter_get_NumValue_mC2707817899B5D9635393269CFA9886F7B7E276E_AdjustorThunk },
	{ 0x060000D1, HierarchySearchFilter_get_Op_m6075B3B7DC8D0E011EC76F929D7CB05E01E16A94_AdjustorThunk },
	{ 0x060000D3, HierarchySearchFilter_ToString_mE726A902E60DDDD1E51502ECB14F64171B9CD2A3_AdjustorThunk },
	{ 0x06000114, Enumerator__ctor_m92AD996B2BC7A1DF6BEF8439EB170DA6B312DFAA_AdjustorThunk },
	{ 0x06000115, Enumerator_get_Current_mB05DA6A7C28996C4C1DD3ABC40170DC8E163A23E_AdjustorThunk },
	{ 0x06000116, Enumerator_MoveNext_m7E7EB4465D2111E7970D54B950EB6572E1352254_AdjustorThunk },
};
static const int32_t s_InvokerIndices[278] = 
{
	7102,
	7102,
	2839,
	7141,
	7102,
	2324,
	6854,
	6869,
	2866,
	7142,
	7102,
	5468,
	6854,
	6869,
	7102,
	5484,
	6985,
	2194,
	5600,
	3713,
	7102,
	9807,
	5600,
	8009,
	8851,
	10000,
	10000,
	9807,
	8556,
	9607,
	8842,
	8840,
	10000,
	6869,
	3852,
	10294,
	9311,
	8556,
	9316,
	8840,
	10000,
	9607,
	8842,
	9744,
	10000,
	10002,
	10002,
	1314,
	7102,
	5600,
	7143,
	5600,
	7102,
	6985,
	6869,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	7102,
	10294,
	1375,
	7145,
	2866,
	1545,
	5545,
	6854,
	6869,
	7102,
	-1,
	-1,
	6869,
	6854,
	6869,
	6946,
	7102,
	1312,
	7102,
	7102,
	5484,
	6928,
	3713,
	4163,
	1541,
	4163,
	4767,
	4166,
	4399,
	2259,
	2253,
	-1,
	7102,
	9807,
	8432,
	10000,
	6946,
	4331,
	4163,
	4696,
	1846,
	850,
	1004,
	8437,
	9607,
	8840,
	8283,
	8637,
	8637,
	9009,
	8639,
	8638,
	10000,
	9709,
	9009,
	8637,
	9052,
	8125,
	7728,
	7773,
	9744,
	5564,
	7102,
	7102,
	5484,
	9807,
	10000,
	9743,
	9744,
	6869,
	6946,
	6869,
	6985,
	6854,
	6946,
	5600,
	399,
	7102,
	7102,
	5484,
	3380,
	4399,
	3713,
	4162,
	4399,
	7102,
	7140,
	9807,
	7635,
	10000,
	7634,
	8138,
	9607,
	9009,
	8840,
	9009,
	10000,
	7633,
	9744,
	5600,
	6854,
	6869,
	10186,
	6927,
	6946,
	6946,
	8787,
	3798,
	6985,
	3852,
	6946,
	9580,
	10186,
	6946,
	6946,
	7102,
	8787,
	8787,
	3799,
	6985,
	3852,
	6946,
	10186,
	6946,
	8787,
	3800,
	6985,
	3852,
	6946,
	5562,
	5562,
	10186,
	6946,
	7102,
	8787,
	3801,
	6985,
	3852,
	6946,
	10186,
	6869,
	6985,
	6985,
	7033,
	6946,
	9805,
	6985,
	9810,
	10294,
	6985,
	5600,
	6985,
	5600,
	6985,
	5600,
	5484,
	5484,
	2870,
	6985,
	6985,
	6985,
	6985,
	6985,
	-1,
	10294,
	10294,
	7102,
	3802,
	3802,
	6869,
	6946,
	6869,
	6985,
	6946,
	5600,
	6985,
	2905,
	399,
	7102,
	7102,
	5484,
	3380,
	4399,
	3713,
	4399,
	1236,
	1545,
	1236,
	4168,
	7102,
	7144,
	9807,
	7393,
	10000,
	1236,
	1545,
	1236,
	7634,
	8138,
	10000,
	9607,
	9807,
	9009,
	8840,
	9009,
	10000,
	7392,
	8130,
	8284,
	8130,
	9744,
	5600,
	6854,
	6869,
};
static const Il2CppTokenRangePair s_rgctxIndices[3] = 
{
	{ 0x0200000F, { 0, 8 } },
	{ 0x06000060, { 8, 3 } },
	{ 0x060000E4, { 11, 9 } },
};
extern const uint32_t g_rgctx_HierarchyPropertyUnmanaged_1_t41BC5EECBBB9C1DA26A5A1C8AFF8C6C00523D3DA;
extern const uint32_t g_rgctx_T_t4E8426E01F3560F4572B33158F0BE04FDA7C6B23;
extern const uint32_t g_rgctx_UnsafeUtility_AsRef_TisT_t4E8426E01F3560F4572B33158F0BE04FDA7C6B23_mF03AB2390D9A4C116D5F290E605A2E280FC4E217;
extern const uint32_t g_rgctx_TU26_tA9054EE04E8F4D2F3DA3AEFB57AEDCC634CD2B5B;
extern const uint32_t g_rgctx_HierarchyPropertyUnmanaged_1_Equals_m9A58498D1840320E1FFC288B4A9DFACD6D1BA1FC;
extern const uint32_t g_rgctx_HierarchyPropertyUnmanaged_1_t41BC5EECBBB9C1DA26A5A1C8AFF8C6C00523D3DA;
extern const uint32_t g_rgctx_HierarchyPropertyUnmanaged_1_GetValue_m998875E20ED4BEC554E7941C7DA11A6EE5A7B0E6;
extern const uint32_t g_rgctx_HierarchyPropertyUnmanaged_1_SetValue_m170D7EACB5BD6D787AA277644503454F473BFED6;
extern const uint32_t g_rgctx_UnsafeUtility_SizeOf_TisT_tFFA05A5FDF883E002FE3079CBBF477AF446841A1_m7FA5DFD9E2C74078391C94C5B1BFC570BADD72D6;
extern const uint32_t g_rgctx_HierarchyPropertyUnmanaged_1_t28A1049AE1D7724640EB8BB746B9901840387403;
extern const uint32_t g_rgctx_HierarchyPropertyUnmanaged_1__ctor_m7568EF8DC30D2C2ECC7B9E5F8391E99A0EC3070F;
extern const uint32_t g_rgctx_IEnumerable_1_t8612489969C1D1C7CCF269C73B6019AAA2A73746;
extern const uint32_t g_rgctx_IEnumerable_1_GetEnumerator_m57E377F5F9CFA73DF1FEE3F8FE4C5D6C1BB99010;
extern const uint32_t g_rgctx_IEnumerator_1_t3A70EC462149467EF2C21615B0F7CD98D55D9817;
extern const uint32_t g_rgctx_IEnumerator_1_get_Current_mE62A6243D1984ADB3436F6F5D1D61FCA7CFEABD4;
extern const uint32_t g_rgctx_T_t05BBECD276BBCC62C106EE88DFB2778BB64235A3;
extern const uint32_t g_rgctx_Func_2_t749796035C3FF846915024CB5E7E6AC9DA727202;
extern const uint32_t g_rgctx_Func_2_Invoke_mD5B1D2D30B36CD768B644A35CE3F0466E739F882;
extern const uint32_t g_rgctx_TU5BU5D_tE6D0DD8F2770CEAF1176362CA7055592C6293DF5;
extern const uint32_t g_rgctx_TU5BU5D_tE6D0DD8F2770CEAF1176362CA7055592C6293DF5;
static const Il2CppRGCTXDefinition s_rgctxValues[20] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HierarchyPropertyUnmanaged_1_t41BC5EECBBB9C1DA26A5A1C8AFF8C6C00523D3DA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t4E8426E01F3560F4572B33158F0BE04FDA7C6B23 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_AsRef_TisT_t4E8426E01F3560F4572B33158F0BE04FDA7C6B23_mF03AB2390D9A4C116D5F290E605A2E280FC4E217 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_tA9054EE04E8F4D2F3DA3AEFB57AEDCC634CD2B5B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HierarchyPropertyUnmanaged_1_Equals_m9A58498D1840320E1FFC288B4A9DFACD6D1BA1FC },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HierarchyPropertyUnmanaged_1_t41BC5EECBBB9C1DA26A5A1C8AFF8C6C00523D3DA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HierarchyPropertyUnmanaged_1_GetValue_m998875E20ED4BEC554E7941C7DA11A6EE5A7B0E6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HierarchyPropertyUnmanaged_1_SetValue_m170D7EACB5BD6D787AA277644503454F473BFED6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_UnsafeUtility_SizeOf_TisT_tFFA05A5FDF883E002FE3079CBBF477AF446841A1_m7FA5DFD9E2C74078391C94C5B1BFC570BADD72D6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_HierarchyPropertyUnmanaged_1_t28A1049AE1D7724640EB8BB746B9901840387403 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_HierarchyPropertyUnmanaged_1__ctor_m7568EF8DC30D2C2ECC7B9E5F8391E99A0EC3070F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerable_1_t8612489969C1D1C7CCF269C73B6019AAA2A73746 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerable_1_GetEnumerator_m57E377F5F9CFA73DF1FEE3F8FE4C5D6C1BB99010 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_t3A70EC462149467EF2C21615B0F7CD98D55D9817 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_IEnumerator_1_get_Current_mE62A6243D1984ADB3436F6F5D1D61FCA7CFEABD4 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t05BBECD276BBCC62C106EE88DFB2778BB64235A3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_2_t749796035C3FF846915024CB5E7E6AC9DA727202 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_2_Invoke_mD5B1D2D30B36CD768B644A35CE3F0466E739F882 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tE6D0DD8F2770CEAF1176362CA7055592C6293DF5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tE6D0DD8F2770CEAF1176362CA7055592C6293DF5 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_HierarchyCoreModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_HierarchyCoreModule_CodeGenModule = 
{
	"UnityEngine.HierarchyCoreModule.dll",
	278,
	s_methodPointers,
	65,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	3,
	s_rgctxIndices,
	20,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

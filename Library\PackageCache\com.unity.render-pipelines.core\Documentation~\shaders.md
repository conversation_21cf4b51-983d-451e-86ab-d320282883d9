# Shaders

Work with shader code in the Scriptable Render Pipeline (SRP).

|**Page**|**Description**|
|-|-|
|[Use shader methods from the SRP Core shader library](built-in-shader-methods.md)|SRP Core has a library of High-Level Shader Language (HLSL) shader files that contain helper methods. You can import these files into your custom shader files and use the helper methods.|
|[Synchronizing shader code and C#](generating-shader-includes.md)|Generate HLSL code based on C# structs to synchronize data and constants between shaders and C#.|

## Additional resources

- [HLSL in Unity](https://docs.unity3d.com/Manual/SL-ShaderPrograms.html)

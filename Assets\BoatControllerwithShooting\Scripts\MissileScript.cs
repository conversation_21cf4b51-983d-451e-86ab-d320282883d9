using UnityEngine;

namespace BoatControllerwithShooting
{
    public class MissileScript : MonoBehaviour
    {
        public GameObject explosionPrefab;
        public GameObject waterSplashPrefab;
        public int DamagePower = 25;
        public Transform particle_following;
        public bool isEnemyMissile = false;

        private void OnCollisionEnter(Collision collision)
        {
            if(isEnemyMissile)
            {
                if (collision.collider.CompareTag("Ground") || collision.collider.CompareTag("Boat"))
                {
                    Instantiate(explosionPrefab, transform.position, Quaternion.identity);
                    if (particle_following != null)
                    {
                        particle_following.parent = null;
                    }
                    if(collision.collider.CompareTag("Boat"))
                    {
                        BoatController.Instance.GetDamage(DamagePower);
                    }
                    Destroy(gameObject);
                }
                else if (collision.collider.CompareTag("Water"))
                {
                    Instantiate(waterSplashPrefab, new Vector3(transform.position.x, transform.position.y + 1, transform.position.z), Quaternion.identity);
                    if (particle_following != null)
                    {
                        particle_following.parent = null;
                    }
                    Destroy(gameObject);
                }
            }
            else
            {
                if (collision.collider.CompareTag("Ground") || collision.collider.CompareTag("Enemy") || collision.collider.CompareTag("Natural"))
                {
                    Instantiate(explosionPrefab, transform.position, Quaternion.identity);
                    if (particle_following != null)
                    {
                        particle_following.parent = null;
                    }
                    Vector3 explosionPos = transform.position;
                    Collider[] colliders = Physics.OverlapSphere(explosionPos, 5);
                    foreach (Collider hit in colliders)
                    {
                        if (hit.CompareTag("Enemy"))
                        {
                            hit.GetComponent<EnemyAI>()?.GetDamage(DamagePower);
                        }
                        else if (hit.CompareTag("Natural"))
                        {
                            hit.GetComponent<NaturalAI>().GetDamage(DamagePower);
                        }
                    }
                    Destroy(gameObject);
                }
                else if (collision.collider.CompareTag("Collapsable"))
                {
                    Instantiate(explosionPrefab, transform.position, Quaternion.identity);
                    if (particle_following != null)
                    {
                        particle_following.parent = null;
                    }
                    Vector3 explosionPos = transform.position;
                    Collider[] colliders = Physics.OverlapSphere(explosionPos, 10);
                    foreach (Collider hit in colliders)
                    {
                        Rigidbody rb = hit.GetComponent<Rigidbody>();
                        if (rb != null)
                        {
                            rb.isKinematic = false;
                            rb.useGravity = true;
                            rb.AddExplosionForce(100, explosionPos, 10, 3.0F);
                            Destroy(hit.gameObject, 10);
                        }
                    }
                    Destroy(gameObject);
                }
                else if (collision.collider.CompareTag("Water"))
                {
                    Instantiate(waterSplashPrefab, new Vector3(transform.position.x, transform.position.y + 1, transform.position.z), Quaternion.identity);
                    if (particle_following != null)
                    {
                        particle_following.parent = null;
                    }
                    Destroy(gameObject);
                }
            }
        }
    }
}

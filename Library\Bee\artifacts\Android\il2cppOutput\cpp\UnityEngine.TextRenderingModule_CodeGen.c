﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void TextGenerationSettings_CompareColors_m29E454405FB3871CA77CC7F94DAB1B4320BD5E53 (void);
extern void TextGenerationSettings_CompareVector2_mF3EE374922B2ECE13BA7B01B26199494A30C2E2E (void);
extern void TextGenerationSettings_Equals_m04DE9655555622E3A4EECC2EAB3BC36B38E20840 (void);
extern void TextGenerator__ctor_m2BFF25B11D7FD0FA4F8E1B04D107219A30E8A187 (void);
extern void TextGenerator__ctor_m05E7CFDC682F880A590F4AA840F81647844A0A32 (void);
extern void TextGenerator_Finalize_mD3C40A16FD4661E4712C85586430041C72E914F7 (void);
extern void TextGenerator_System_IDisposable_Dispose_m5B2B52DA9C73CE736E908030738002D6D860227F (void);
extern void TextGenerator_get_characterCountVisible_m407C344C5A13A26471315449899B927FF3D3CDFC (void);
extern void TextGenerator_ValidatedSettings_mAE5128305E953EE41E5E655675323896EF1154AE (void);
extern void TextGenerator_Invalidate_m999CE96E2F02E326092DFEB53EE8BC08CAAF8260 (void);
extern void TextGenerator_GetCharacters_mB7956402E0C66D9F9D51A3ADFCA84BE83BB54D90 (void);
extern void TextGenerator_GetLines_mFA8508172C251E1E4BBB1FBB734C82C81EF9330F (void);
extern void TextGenerator_GetVertices_m97C7D4CB9B122A10BA5921BC38F83A245EB1B97E (void);
extern void TextGenerator_GetPreferredWidth_mDDE43A5C8BDD7BBD235AE740C4C2A293A92BD28E (void);
extern void TextGenerator_GetPreferredHeight_m0EAF7CE0F503C97F4D35D0EE4A3AD923B472F89E (void);
extern void TextGenerator_PopulateWithErrors_m0E56C12E9D7714F4B8772422AAB2CAC9EA10F834 (void);
extern void TextGenerator_Populate_m5620AF6DBC180FC3FBE3E5F5930CF7CB65DD3CE2 (void);
extern void TextGenerator_PopulateWithError_m0F7C1CE45F372D80603C28F1E01F6FF796B95E02 (void);
extern void TextGenerator_PopulateAlways_mA99FFC0276B51F8DED3EABD1FF660130BE803EDA (void);
extern void TextGenerator_get_verts_mA197E8944ABE4831B93275662BB66EC53DE349D8 (void);
extern void TextGenerator_get_characters_mDA07F7108C6EF6F5DF61C478FDF3EB18DCE6B34A (void);
extern void TextGenerator_get_lines_m52DAB15921DC96120CA6C21A3FEB2406D4A76064 (void);
extern void TextGenerator_get_rectExtents_m55100375428EFAA89F3AC1B528E8716E615F9F2C (void);
extern void TextGenerator_get_characterCount_mD4FD13BBBEDA2AB495C045160693754E4C106FFF (void);
extern void TextGenerator_get_lineCount_mC568C375C85695478DD054B296CBEAB11D3C4108 (void);
extern void TextGenerator_Internal_Create_m66701C08DAE160892766930F831D2AB1EB4E998C (void);
extern void TextGenerator_Internal_Destroy_m0AE3865A6886503BAE5798E6D9899EF59A0285A7 (void);
extern void TextGenerator_Populate_Internal_m74A216D58183B4069BFBD6CDF624CB97A9EC4CAA (void);
extern void TextGenerator_Populate_Internal_m4DF908756A1AC26B04AC8AB12A004203366B294B (void);
extern void TextGenerator_GetVerticesInternal_m8BB7AB760D1071709A584FD9371D7549DA2A1B21 (void);
extern void TextGenerator_GetCharactersInternal_mB5157BBD4E1C42ACEDCC696D718E656BDD5E2398 (void);
extern void TextGenerator_GetLinesInternal_mDAC83E88FD1FD79CCCE294E49E02ED748CFC8E12 (void);
extern void TextGenerator_get_rectExtents_Injected_mE70DFB2720C23AD997733158D7BD220BFC30C06E (void);
extern void TextGenerator_get_characterCount_Injected_m85C7A016C1F680A2BD6585235CC8858B0C718CB8 (void);
extern void TextGenerator_get_lineCount_Injected_mF2C9067B7A2E5466BE99F6A9104F2FBCB36761AF (void);
extern void TextGenerator_Populate_Internal_Injected_m79206EFC5017F0450703B7B246C0C0DE1F23B8E7 (void);
extern void TextGenerator_GetVerticesInternal_Injected_mDF2ACA7C58E4055EA2A1C696C7B5D321B36D6E95 (void);
extern void TextGenerator_GetCharactersInternal_Injected_m007C3E062501293E5D4969D51689224CD6C5AB65 (void);
extern void TextGenerator_GetLinesInternal_Injected_m6B6CFC076A64DF1E7250EB5C32A0A126FDD6AF2E (void);
extern void BindingsMarshaller_ConvertToNative_m442CA777D48EC9F618E6EDACB4F95565D8717650 (void);
extern void UIVertex__cctor_mF98CC8F21D6CD8703B47E168E976BBE15E0F6C62 (void);
extern void Font_add_textureRebuilt_m0BBB44846C17A580B078599DA5AE231DA9D6DAD7 (void);
extern void Font_remove_textureRebuilt_mB7EF9EEE803E9C70AF4217190B49C83FE287904A (void);
extern void Font_get_material_m61ABDEC14C6D659DDC5A4F080023699116C17364 (void);
extern void Font_get_dynamic_m5231258BFEAABA3B3EA6AB5D8C2FDCBC2AD1371C (void);
extern void Font_get_fontSize_m02C2F5C7A7C89A878F04C2087248DD46BBF9E26B (void);
extern void Font_InvokeTextureRebuilt_Internal_m874D1025267C908E5FCD437B41929E4DE248B01B (void);
extern void Font_HasCharacter_m71A84FE036055880E1543D79A38FEFA495AD200B (void);
extern void Font_HasCharacter_mAB838A26F002CB5E4B4DB297F7D6836A28625B18 (void);
extern void Font_GetOSFallbacks_mB3FE14E050081E45B0855EF5699B0A1D93B17A78 (void);
extern void Font_get_material_Injected_mFD3477FFA6CA61152AC98B142DFE004137D231F7 (void);
extern void Font_get_dynamic_Injected_mE1610EDFBB9092E562D21E9FC5A4AAFA89696690 (void);
extern void Font_get_fontSize_Injected_m34A9893F1D744CC3CE823AE7BF0902957C8A268E (void);
extern void Font_HasCharacter_Injected_mCCBE7842CC556C3C1693FA8148B1928B5321151B (void);
extern void FontTextureRebuildCallback__ctor_m1AF27FC83F3136E493F47015F99CE7A4E6BCA0BC (void);
extern void FontTextureRebuildCallback_Invoke_m8B52C3F4823ADBB80062209E6BA2B33202AE958D (void);
static Il2CppMethodPointer s_methodPointers[56] = 
{
	TextGenerationSettings_CompareColors_m29E454405FB3871CA77CC7F94DAB1B4320BD5E53,
	TextGenerationSettings_CompareVector2_mF3EE374922B2ECE13BA7B01B26199494A30C2E2E,
	TextGenerationSettings_Equals_m04DE9655555622E3A4EECC2EAB3BC36B38E20840,
	TextGenerator__ctor_m2BFF25B11D7FD0FA4F8E1B04D107219A30E8A187,
	TextGenerator__ctor_m05E7CFDC682F880A590F4AA840F81647844A0A32,
	TextGenerator_Finalize_mD3C40A16FD4661E4712C85586430041C72E914F7,
	TextGenerator_System_IDisposable_Dispose_m5B2B52DA9C73CE736E908030738002D6D860227F,
	TextGenerator_get_characterCountVisible_m407C344C5A13A26471315449899B927FF3D3CDFC,
	TextGenerator_ValidatedSettings_mAE5128305E953EE41E5E655675323896EF1154AE,
	TextGenerator_Invalidate_m999CE96E2F02E326092DFEB53EE8BC08CAAF8260,
	TextGenerator_GetCharacters_mB7956402E0C66D9F9D51A3ADFCA84BE83BB54D90,
	TextGenerator_GetLines_mFA8508172C251E1E4BBB1FBB734C82C81EF9330F,
	TextGenerator_GetVertices_m97C7D4CB9B122A10BA5921BC38F83A245EB1B97E,
	TextGenerator_GetPreferredWidth_mDDE43A5C8BDD7BBD235AE740C4C2A293A92BD28E,
	TextGenerator_GetPreferredHeight_m0EAF7CE0F503C97F4D35D0EE4A3AD923B472F89E,
	TextGenerator_PopulateWithErrors_m0E56C12E9D7714F4B8772422AAB2CAC9EA10F834,
	TextGenerator_Populate_m5620AF6DBC180FC3FBE3E5F5930CF7CB65DD3CE2,
	TextGenerator_PopulateWithError_m0F7C1CE45F372D80603C28F1E01F6FF796B95E02,
	TextGenerator_PopulateAlways_mA99FFC0276B51F8DED3EABD1FF660130BE803EDA,
	TextGenerator_get_verts_mA197E8944ABE4831B93275662BB66EC53DE349D8,
	TextGenerator_get_characters_mDA07F7108C6EF6F5DF61C478FDF3EB18DCE6B34A,
	TextGenerator_get_lines_m52DAB15921DC96120CA6C21A3FEB2406D4A76064,
	TextGenerator_get_rectExtents_m55100375428EFAA89F3AC1B528E8716E615F9F2C,
	TextGenerator_get_characterCount_mD4FD13BBBEDA2AB495C045160693754E4C106FFF,
	TextGenerator_get_lineCount_mC568C375C85695478DD054B296CBEAB11D3C4108,
	TextGenerator_Internal_Create_m66701C08DAE160892766930F831D2AB1EB4E998C,
	TextGenerator_Internal_Destroy_m0AE3865A6886503BAE5798E6D9899EF59A0285A7,
	TextGenerator_Populate_Internal_m74A216D58183B4069BFBD6CDF624CB97A9EC4CAA,
	TextGenerator_Populate_Internal_m4DF908756A1AC26B04AC8AB12A004203366B294B,
	TextGenerator_GetVerticesInternal_m8BB7AB760D1071709A584FD9371D7549DA2A1B21,
	TextGenerator_GetCharactersInternal_mB5157BBD4E1C42ACEDCC696D718E656BDD5E2398,
	TextGenerator_GetLinesInternal_mDAC83E88FD1FD79CCCE294E49E02ED748CFC8E12,
	TextGenerator_get_rectExtents_Injected_mE70DFB2720C23AD997733158D7BD220BFC30C06E,
	TextGenerator_get_characterCount_Injected_m85C7A016C1F680A2BD6585235CC8858B0C718CB8,
	TextGenerator_get_lineCount_Injected_mF2C9067B7A2E5466BE99F6A9104F2FBCB36761AF,
	TextGenerator_Populate_Internal_Injected_m79206EFC5017F0450703B7B246C0C0DE1F23B8E7,
	TextGenerator_GetVerticesInternal_Injected_mDF2ACA7C58E4055EA2A1C696C7B5D321B36D6E95,
	TextGenerator_GetCharactersInternal_Injected_m007C3E062501293E5D4969D51689224CD6C5AB65,
	TextGenerator_GetLinesInternal_Injected_m6B6CFC076A64DF1E7250EB5C32A0A126FDD6AF2E,
	BindingsMarshaller_ConvertToNative_m442CA777D48EC9F618E6EDACB4F95565D8717650,
	UIVertex__cctor_mF98CC8F21D6CD8703B47E168E976BBE15E0F6C62,
	Font_add_textureRebuilt_m0BBB44846C17A580B078599DA5AE231DA9D6DAD7,
	Font_remove_textureRebuilt_mB7EF9EEE803E9C70AF4217190B49C83FE287904A,
	Font_get_material_m61ABDEC14C6D659DDC5A4F080023699116C17364,
	Font_get_dynamic_m5231258BFEAABA3B3EA6AB5D8C2FDCBC2AD1371C,
	Font_get_fontSize_m02C2F5C7A7C89A878F04C2087248DD46BBF9E26B,
	Font_InvokeTextureRebuilt_Internal_m874D1025267C908E5FCD437B41929E4DE248B01B,
	Font_HasCharacter_m71A84FE036055880E1543D79A38FEFA495AD200B,
	Font_HasCharacter_mAB838A26F002CB5E4B4DB297F7D6836A28625B18,
	Font_GetOSFallbacks_mB3FE14E050081E45B0855EF5699B0A1D93B17A78,
	Font_get_material_Injected_mFD3477FFA6CA61152AC98B142DFE004137D231F7,
	Font_get_dynamic_Injected_mE1610EDFBB9092E562D21E9FC5A4AAFA89696690,
	Font_get_fontSize_Injected_m34A9893F1D744CC3CE823AE7BF0902957C8A268E,
	Font_HasCharacter_Injected_mCCBE7842CC556C3C1693FA8148B1928B5321151B,
	FontTextureRebuildCallback__ctor_m1AF27FC83F3136E493F47015F99CE7A4E6BCA0BC,
	FontTextureRebuildCallback_Invoke_m8B52C3F4823ADBB80062209E6BA2B33202AE958D,
};
extern void TextGenerationSettings_CompareColors_m29E454405FB3871CA77CC7F94DAB1B4320BD5E53_AdjustorThunk (void);
extern void TextGenerationSettings_CompareVector2_mF3EE374922B2ECE13BA7B01B26199494A30C2E2E_AdjustorThunk (void);
extern void TextGenerationSettings_Equals_m04DE9655555622E3A4EECC2EAB3BC36B38E20840_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[3] = 
{
	{ 0x06000001, TextGenerationSettings_CompareColors_m29E454405FB3871CA77CC7F94DAB1B4320BD5E53_AdjustorThunk },
	{ 0x06000002, TextGenerationSettings_CompareVector2_mF3EE374922B2ECE13BA7B01B26199494A30C2E2E_AdjustorThunk },
	{ 0x06000003, TextGenerationSettings_Equals_m04DE9655555622E3A4EECC2EAB3BC36B38E20840_AdjustorThunk },
};
static const int32_t s_InvokerIndices[56] = 
{
	1561,
	1746,
	3928,
	7102,
	5562,
	7102,
	7102,
	6946,
	4931,
	7102,
	5600,
	5600,
	5600,
	2180,
	2180,
	1068,
	1653,
	1954,
	1954,
	6985,
	6985,
	6985,
	7011,
	6946,
	6946,
	10221,
	10000,
	0,
	1,
	5600,
	5600,
	5600,
	9311,
	9709,
	9709,
	7246,
	9316,
	9316,
	9316,
	9744,
	10294,
	10002,
	10002,
	6985,
	6869,
	6946,
	10002,
	3947,
	3816,
	10235,
	9743,
	9607,
	9709,
	8841,
	2866,
	7102,
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_TextRenderingModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_TextRenderingModule_CodeGenModule = 
{
	"UnityEngine.TextRenderingModule.dll",
	56,
	s_methodPointers,
	3,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	0,
	NULL,
	0,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void OidLookup_ToOid_m084AE2948BA1D784EAC148B97B6D5EF94F0D58B0 (void);
extern void OidLookup_ShouldUseCache_mAF605BA00A22EA400B99D24286837F6904C84ED0 (void);
extern void OidLookup_NativeFriendlyNameToOid_m383F983E8333B3DAB97C9278B3B0EDF09E61758D (void);
extern void OidLookup__cctor_mBF99F75584EA62215139C76A1BBF57BCEAD2ADBC (void);
extern void U3CU3Ec__cctor_m63CA89B172417DB40229C01C86A24DAAD5EC9963 (void);
extern void U3CU3Ec__ctor_m381C6259C7B5579501CBFB1E4D1DF201D5928470 (void);
extern void U3CU3Ec_U3C_cctorU3Eb__10_0_m6CCA3946EAC08544EB57D764DBF2D23E12DE30A1 (void);
extern void U3CU3Ec_U3C_cctorU3Eb__10_1_m7F86444F2B083758A76759BD54D31A3538101A4E (void);
extern void IPv4AddressHelper_ParseHostNumber_m3ECD734470D95F75F0A7475BB69A53D48A924179 (void);
extern void IPv4AddressHelper_IsValid_mB71D85892F391896358B6C19C6E4899ED31EFCF8 (void);
extern void IPv4AddressHelper_ParseCanonical_m38827B1F002AC415916FF17D5C7DA3279486A91C (void);
extern void IPv4AddressHelper_IsValidCanonical_m157BF3179C5A511A8915C572111A7014D7E5DB84 (void);
extern void IPv4AddressHelper_ParseNonCanonical_m8A508ED61556F4182AAEBE3E7D43C738A57C984D (void);
extern void IPv4AddressHelper_ParseCanonicalName_mF74A625E0C85FC8C5CAE3A47785606BF93777FF1 (void);
extern void IPv4AddressHelper_Parse_m0F43F114BC6D0C8D85D29D3CA2CE24CA19714026 (void);
extern void IPv6AddressHelper_FindCompressionRange_m5BA0C004FA7BE7CB3C520AB47C4D6B6FA6C13C56 (void);
extern void IPv6AddressHelper_ShouldHaveIpv4Embedded_m08BEFD0452A0145779E70931B00F2D6EA15A1E70 (void);
extern void IPv6AddressHelper_Parse_m6F68528ADBA8B6ECED3060135FC1DF6CCD2B0543 (void);
extern void IPv6AddressHelper_ParseCanonicalName_m5C5161CE7D6F7D7C7D81A808F51107C85A6CA21B (void);
extern void IPv6AddressHelper_IsLoopback_mAE6B2C6CFAD787337C6ABB41F2D2942A4C191C84 (void);
extern void IPv6AddressHelper_InternalIsValid_mDF9ADFBBDDD76F8B77EC140686B6280F4BD53478 (void);
extern void IPv6AddressHelper_IsValid_mDEE6D566114425592CA0A067775A2E0C771CA5DA (void);
extern void NotImplemented_ByDesignWithMessage_m9A8A83C6B994C16CA1CFA80F5AD748ABA3510535 (void);
extern void IriHelper_CheckIriUnicodeRange_m584D1B233AB9DB3642640CDBD787B0E51D2AC625 (void);
extern void IriHelper_CheckIriUnicodeRange_m0989646614B5D6CD18622388A069BEA8A755F013 (void);
extern void IriHelper_CheckIsReserved_m0F5942330B329B65D738A398854399FD43A5589C (void);
extern void IriHelper_EscapeUnescapeIri_m311CD9679869740C6A303485944EF6DAE96FB169 (void);
extern void Uri_get_IsImplicitFile_m8D1A522158E4EB8AD8C077376D023ED1262998F6 (void);
extern void Uri_get_IsUncOrDosPath_m33F2D8808AB44816518C1C464152D4B7872374F0 (void);
extern void Uri_get_IsDosPath_m16357B61F5D111C45EF7D40276B96429E414F0D2 (void);
extern void Uri_get_IsUncPath_m2D9E7E2DB4EA57EF268B9BA167A1389E18AE9835 (void);
extern void Uri_get_HostType_mA30DC51045EE5FE94B3CB84B0D361A9C28EE572E (void);
extern void Uri_get_Syntax_mCBAC18B1DC71D269BF6C98417D923674DE258DC2 (void);
extern void Uri_get_IsNotAbsoluteUri_m374185EB37A209B6102F955427913168C5543028 (void);
extern void Uri_IriParsingStatic_mF071497F10CC1CC81177D6F4BCE9D2007D8FDA16 (void);
extern void Uri_get_AllowIdn_m612B747CE2F03FCA867D81F14710758B561209E7 (void);
extern void Uri_AllowIdnStatic_m0B61B2A586F2BF870D185A1BD65151F52EFBC457 (void);
extern void Uri_IsIntranet_m3AC6DBC7C35F309DFAD0951ABC7D95A8000548EE (void);
extern void Uri_get_UserDrivenParsing_mF2426529046B3DF3292A7AA2F4885F8D6591BBBD (void);
extern void Uri_SetUserDrivenParsing_mDE80D352D6343F50F152EB65EF142E84F7F79D69 (void);
extern void Uri_get_SecuredPathIndex_m575B7B28B17F8F7EE3FA563ACF6390B0926732AD (void);
extern void Uri_NotAny_mB88A38A602D4A784CADBD43FDF4CDA0235371E81 (void);
extern void Uri_InFact_mEEE8ADE0780C0712A3DEB8935BD6FC7B23F38A7B (void);
extern void Uri_StaticNotAny_mDC2D7341AE3679E5750481E7B793F4E3636ACD6C (void);
extern void Uri_StaticInFact_m8F3964455197F6C2CB588843D71DDAD3B2F2AB77 (void);
extern void Uri_EnsureUriInfo_m6FE387306AF82CDCFC56E2E9C2171C406D1FA982 (void);
extern void Uri_EnsureParseRemaining_mDF7D4313A56047EB22457E12330C138156D54783 (void);
extern void Uri_EnsureHostString_m7F5F3ED8488F83170A857CD51F0CFEB8322AA055 (void);
extern void Uri__ctor_m6CA436E6AD2768A121FA851CBEEFA3623E849D3A (void);
extern void Uri__ctor_m24549041BC5661EAC10BA8CB35B60AD6512AF69B (void);
extern void Uri__ctor_m5AA91BCDE21E8AD62B9A48948CF3661B2F00E38F (void);
extern void Uri_GetCombinedString_m7D6A3DEB5DB42F79271CEC67DA2AEAD0E05BCEEF (void);
extern void Uri_GetException_m80C3194C72CA8F86F95A6DFC8D5C29BE31D749A8 (void);
extern void Uri__ctor_mE1AB9FB0563073B8DFB381A69A54E9E0459D583F (void);
extern void Uri_System_Runtime_Serialization_ISerializable_GetObjectData_m39D9B260E68B8715E9B693A125A2116CE14D760D (void);
extern void Uri_GetObjectData_m0D16C2BC060F0B88503DCC0EEACB151D43FA16CA (void);
extern void Uri_get_AbsoluteUri_m080934F4F2E2160EBEABDF00F8B6D59888EA63AE (void);
extern void Uri_get_IsUnc_mB190B452D6A7B09FC8BDD967A5C38F32E87E0FCA (void);
extern void Uri_StaticIsFile_m8BD582D32BB053DCB5BADA29C1DD807AACCCE7E1 (void);
extern void Uri_get_InitializeLock_m29D252F7183FE4C205E618018E8C42B56BC2B091 (void);
extern void Uri_InitializeUriConfig_mCD4E00EDCA27459C7B005DDFFAD9D54396BF7DCB (void);
extern void Uri_get_Port_m7A1413AC9D9A2FC5DC8A7F89DF4A01ACA0241A02 (void);
extern void Uri_get_Scheme_m29106D5109538220B22FC49DE7B44040E51B0F6F (void);
extern void Uri_get_OriginalStringSwitched_mBE7152D5DA7A1D922B728B9EA8C64815B0469FB7 (void);
extern void Uri_get_OriginalString_m3031F9054CA10F2C55C0E2415CC19810D360A5D6 (void);
extern void Uri_get_DnsSafeHost_mE5001E7AE7F1879A2059024EA06C9031F6320A2B (void);
extern void Uri_get_IsAbsoluteUri_m2F9B759B85D295639D7959A616E1FBC203D756DF (void);
extern void Uri_get_UserEscaped_m5A376B8E2DDB815CAA2E34AF8890FBA943DC2085 (void);
extern void Uri_IsGenDelim_mED487423251FBC7A3AA85A70023D531DED88B0D3 (void);
extern void Uri_IsHexDigit_m89270CD137F8D5AAC9953CB367BF194D0D0EEDBC (void);
extern void Uri_FromHex_m30578BD6ADBA0FE875257A8296ED979DC7849020 (void);
extern void Uri_GetHashCode_m41959A208E51019C57C20014B2505993FB94B70C (void);
extern void Uri_ToString_mDD98375BF8CEBEF6E3708BBCA6C4AB0B54165DA2 (void);
extern void Uri_op_Inequality_m64BE981CDF88B751677B80D883B201BF3CFD83F7 (void);
extern void Uri_Equals_m1BD19791DD8A42FEFF5844C29623626C2849C452 (void);
extern void Uri_CheckForColonInFirstPathSegment_m3A849DB35C397D05D788FF50CBA2B107CF729399 (void);
extern void Uri_ParseScheme_m8E14D8CA20935DF4A14DC0C7DFE2330C734F1AD6 (void);
extern void Uri_ParseMinimal_m24D824EC77216C64F4B8B9175915CD6C3C6DB7B5 (void);
extern void Uri_PrivateParseMinimal_m4D4C3CB26AC0DE8B0E1F7DC4143F2004D9D25B17 (void);
extern void Uri_PrivateParseMinimalIri_mD8C7CA7EE14D994E915B48FB6A2CDE62253D4F1E (void);
extern void Uri_CreateUriInfo_m3B4F804AB6CB010246F2A52F123D6E44DECDE998 (void);
extern void Uri_CreateHostString_mBBAF0ACD4E2EE673F8F7F4E416D87517D94245CF (void);
extern void Uri_CreateHostStringHelper_m196C6DAB9F26F08459220AC87C0A74785A61E9C3 (void);
extern void Uri_GetHostViaCustomSyntax_m6632DAD1F66901E82C60A47D626A9140A586C802 (void);
extern void Uri_GetParts_m432C93EAC8684C7410585D36DA00B04F6DC34357 (void);
extern void Uri_GetEscapedParts_m0C33275AFEA210E5CAD22649E8B61B49FB43971E (void);
extern void Uri_GetUnescapedParts_m57F83B75B05C5D8EAB6B8ACCB6C8E4F0922CB7ED (void);
extern void Uri_ReCreateParts_mB622FFDF74676902D184FD96C4E6B740B65140AF (void);
extern void Uri_GetUriPartsFromUserString_m3D6EEB76609CCC54A0ABE65911CC24603A184B29 (void);
extern void Uri_ParseRemaining_m9134D26360C9EEDFAFAFEE1ECA1A5CEE66BE5A3A (void);
extern void Uri_ParseSchemeCheckImplicitFile_m3577C77315714DF277763F7DE3BAAD8D94D2CA93 (void);
extern void Uri_CheckKnownSchemes_m598B70C53F57AB20E2406763BAA5636A3B57195A (void);
extern void Uri_CheckSchemeSyntax_m37B81B236F58066982C17FF76058B3ECB0740647 (void);
extern void Uri_CheckAuthorityHelper_m7946931160B7EC50E7167A11E570A4AF8AEB5B2E (void);
extern void Uri_CheckAuthorityHelperHandleDnsIri_mB737EC35D3A477668E6E289D09DFB43EAD4D4D62 (void);
extern void Uri_CheckAuthorityHelperHandleAnyHostIri_mE38845CEFA41AB52AB949CAE2A43D77FB397C08D (void);
extern void Uri_FindEndOfComponent_m59CB1EFDA410B42014EA7DCDBA0C9FC9F5996E02 (void);
extern void Uri_FindEndOfComponent_m198F7AC80BEA08E8DAA6B4577D8A872B41C519B1 (void);
extern void Uri_CheckCanonical_m54AFA85AA00A9909C2EC01F026873F12532AD7F7 (void);
extern void Uri_GetCanonicalPath_m66FA22F6E85D79E360A7EE86C0E03F1A009F6F99 (void);
extern void Uri_UnescapeOnly_mBED4CBBC30D19BA6BA801BCF9B33CD681771102B (void);
extern void Uri_Compress_m22981FC433505DEE0F230312B93B062096607CCD (void);
extern void Uri_CalculateCaseInsensitiveHashCode_m635E27F73B39946637685A19CD09741D14A359EE (void);
extern void Uri_CombineUri_m92FB418ECA7D55262958241C8A274715C58C6424 (void);
extern void Uri_IsLWS_mE00B6D5A24D07A5EA80278F981F1516343C0846D (void);
extern void Uri_IsAsciiLetter_m54D66D3D390C364A49529D304526152E2339A140 (void);
extern void Uri_IsAsciiLetterOrDigit_m9033B2053D679076DEADF04748CE5E983B25A9E0 (void);
extern void Uri_IsBidiControlCharacter_m37BFE363354928FB34D016950008D1FB29DFBB0F (void);
extern void Uri_StripBidiControlCharacter_mB8C52362A2A248EB33737D1A4792B3FB57975912 (void);
extern void Uri_CreateThis_m522F36D576D9A8803410439127DB32837331BA3A (void);
extern void Uri_InitializeUri_m044EA7C6188B132874B4522D5CA41EEFDB9A1718 (void);
extern void Uri_CheckForConfigLoad_mC7297EBC00884B1793FDB16F79BF0FCFD53E9FA7 (void);
extern void Uri_CheckForUnicode_mC763EF0DDC61EE359A6256B95209736EC0353AA1 (void);
extern void Uri_CheckForEscapedUnreserved_mCEF702F733C72BE825E9FEBBEA751540A6A78D11 (void);
extern void Uri_TryCreate_m183077985BD7B1BE8DBDBCC4604F775C44723EC9 (void);
extern void Uri_GetComponents_m30E3EAD4E9A39939456E764FEF94AFD4D6B7532A (void);
extern void Uri_IsWellFormedOriginalString_mD71C404996C187FB7CB38474B7E4DC4DD760A9DF (void);
extern void Uri_IsWellFormedUriString_m35D88312036B391850B73BD0F09779D9D47ECCE8 (void);
extern void Uri_InternalIsWellFormedOriginalString_m273089E57528670A95D845618A5C3CCD028A3098 (void);
extern void Uri_UnescapeDataString_m095AF55EE0F237A44FC8A53D2EE6FA66A567634D (void);
extern void Uri_EscapeUnescapeIri_m14C0CD1A4C14E2458685E5A10049204B1A15C16C (void);
extern void Uri__ctor_m6EA3AF49C103A3D56EBBB24BCAEE8DE7E8676AF4 (void);
extern void Uri_CreateHelper_mB015D0E87C2F9401D7C9B929C194AA5E432DA224 (void);
extern void Uri_ResolveHelper_mCF316B7B35401CFE251F64CCE66AF24F687E7E04 (void);
extern void Uri_GetRelativeSerializationString_mDEB0AD1094EC2842C2F0830C1E43FF2FFDEB6F5B (void);
extern void Uri_GetComponentsHelper_m83D8E4A027E180AFB34BA5290A8FF381EBCBCB75 (void);
extern void Uri_CreateThisFromUri_m986BB8F1CF8D75EEFA12D9D0963BA927FA394BE5 (void);
extern void Uri__cctor_mF9A6F75B43C4652D9B89180A998B3D86B61F5594 (void);
extern void UriInfo__ctor_m88A6E3E7164B78B18C70D83CA5AD734517D308B3 (void);
extern void MoreInfo__ctor_m4AC1286A4D4DAD0AB0EE99A2C952D54BFA5F44A7 (void);
extern void UriFormatException__ctor_m4D218C92C093CD0CDDDC012911A1E10CB327BB70 (void);
extern void UriFormatException__ctor_m718A5DEB020FAAD1F654CD05E63C3988AA7712D3 (void);
extern void UriFormatException__ctor_m15F8C1205D9A648F72DD5D94DB92FD1FA1397FE0 (void);
extern void UriFormatException_System_Runtime_Serialization_ISerializable_GetObjectData_mF5C6CA057915A5F7F4E9D8FBE5D6EB2595A4FD80 (void);
extern void UriHelper_EscapeString_mA9075A39AFEE45E310AA6E2977B46D74F2FE38D3 (void);
extern void UriHelper_EnsureDestinationSize_m0A91CAD854571386F0490A975BA25CEEA93614C0 (void);
extern void UriHelper_UnescapeString_m37D24870CC3AF00984435A567B7AB28E7225D7B3 (void);
extern void UriHelper_UnescapeString_mAB62B336BC58C7697B4CA7355405F3C1FDBBB7E0 (void);
extern void UriHelper_MatchUTF8Sequence_mD118AEEE32C6BCBA4ED2E41E2B008EB83412D54B (void);
extern void UriHelper_EscapeAsciiChar_mFA21BF7A2306446A8E552111DA06160A41CD07D1 (void);
extern void UriHelper_EscapedAscii_m93D49C2D42E1E5E292199FACF5B4D6CC770D3DA1 (void);
extern void UriHelper_IsNotSafeForUnescape_m79864E996F8F46D4A1C00B965E2EB4EE7AFE63ED (void);
extern void UriHelper_IsReservedUnreservedOrHash_m7B622D953837E4F0FEF44B114718B75B846512A1 (void);
extern void UriHelper_IsUnreserved_mED7CF4BB5F43C337685396D5F99D7EFABAD5E5C9 (void);
extern void UriHelper_Is3986Unreserved_mCDBDBB83B68181182A6AD07747084B3204C7BDCA (void);
extern void UriHelper__cctor_mB25AC23398D53149F4C33FB739BC6010324D0D81 (void);
extern void UriParser_get_SchemeName_m3BD969B4E8EEA3769D070FA42CE340CDC766F47D (void);
extern void UriParser_get_DefaultPort_m38684C86B40F59960533D8F812B311ACBBEB7AA5 (void);
extern void UriParser_OnNewUri_m752BC0174A2E3F7AA99F1088E1D539E5B8F7B5B2 (void);
extern void UriParser_InitializeAndValidate_m5B38A10B57DC49B1CE13F361ABD78D7269EDA886 (void);
extern void UriParser_Resolve_m7CC03524A2848FBD24BAB57CC62A19C751937182 (void);
extern void UriParser_GetComponents_m230F3341901B4B90DC4650093272BC15A1161FAF (void);
extern void UriParser_IsWellFormedOriginalString_m8C48DF0E4CF7684AE739F1CCA139CB09D9758839 (void);
extern void UriParser_get_ShouldUseLegacyV2Quirks_m8EC9AB4BB2AD4FAAC4CD7C921C7A61AA4FC914D2 (void);
extern void UriParser__cctor_m0F0B1F5441AF97DDBE797061C6CABA2CD4CDBE69 (void);
extern void UriParser_get_Flags_mBE183D7CBEEEBEC670CCF1DF21D56B354796D6C3 (void);
extern void UriParser_NotAny_mCE6CF6736414BE547215F6E44C3E1283DEA05F56 (void);
extern void UriParser_InFact_m3C07C3050DE1E69F6B0BC816EC34AF610373E532 (void);
extern void UriParser_IsAllSet_mEE811F1E7BBCC34D7A32BC30A18AD5D34E690813 (void);
extern void UriParser_IsFullMatch_mFC1C9EEDD31F040997931B26EF54CE7988495550 (void);
extern void UriParser__ctor_m4FA54685DFA00FC6AEA73E94CD59C5FF2872E55A (void);
extern void UriParser_FindOrFetchAsUnknownV1Syntax_m780CA32D27A4F1D8B7798FC4D3CC28A6FC4ACCE0 (void);
extern void UriParser_get_IsSimple_m8ED850D585BA3BA0FDC239496065E6D2D973C7E5 (void);
extern void UriParser_InternalOnNewUri_m93B84E04949FAE8FE3B6919DA9F933B682D4B2F8 (void);
extern void UriParser_InternalValidate_m69157B3E80381F3FC104788160F2213B3FE8F39D (void);
extern void UriParser_InternalResolve_mD1963080EBCF493DA78656AA5AEA458BF5138F5B (void);
extern void UriParser_InternalGetComponents_m52F450C5283A1820D5AFD07505E5FE82D4F476B0 (void);
extern void UriParser_InternalIsWellFormedOriginalString_m8721FCB51EB53C8B322E0A8909B181D6CF0D108F (void);
extern void BuiltInUriParser__ctor_m6E9775075E7360DD524B7E3E456A3FFA58F89E79 (void);
extern void DomainNameHelper_ParseCanonicalName_mCDD58AA526B15176E4468ABA169068B279F3817F (void);
extern void DomainNameHelper_IsValid_mD977C4425926C44931F21A478C861D1041AF5B67 (void);
extern void DomainNameHelper_IsValidByIri_m94490F7D0A09B0C35C89E2CD478B935EECEC5F96 (void);
extern void DomainNameHelper_IdnEquivalent_m797D2222F0F6786FF059095B73ACF47A3374E8DC (void);
extern void DomainNameHelper_IdnEquivalent_mEFD473FCBC1D7F515577673838569560777F5D2B (void);
extern void DomainNameHelper_IsIdnAce_m95A0515B49C9679EE331F830D05C2C12853919BC (void);
extern void DomainNameHelper_IsIdnAce_mC02E0A64A83B0C99CF26AD5D49AA541E33143947 (void);
extern void DomainNameHelper_UnicodeEquivalent_m390DFCD943A3716158026F5E1AF6558E56461F12 (void);
extern void DomainNameHelper_UnicodeEquivalent_m9C7E79F0172D7AC0DEF7FC5236F1B9766C735517 (void);
extern void DomainNameHelper_IsASCIILetterOrDigit_m5C30C4CDEF0ECE647B2C5E714A595B08A8009B3A (void);
extern void DomainNameHelper_IsValidDomainLabelCharacter_m9BBE29F6DEACCA6864574A1B6C8BCBE6732BF5CF (void);
extern void UncNameHelper_ParseCanonicalName_m938F53305ABCF1840011969B589F0D53A1028C79 (void);
extern void UncNameHelper_IsValid_mCF30A9D7038A73997B8B198D7EB606B171E00526 (void);
extern void IOAsyncCallback__ctor_m58A7C732444005CB72C5B352096C2DB252A6F113 (void);
extern void IOAsyncCallback_Invoke_m70CC21061AD0965DBC87CB382AD395A35E1788B2 (void);
extern void UriTypeConverter__ctor_m02CB01E1305031BE5A45FB54601E520748B6EDB6 (void);
extern void UriTypeConverter_CanConvert_m970377ED30FF1B6875CCDA64FDC87EFE7D381DE3 (void);
extern void Capture__ctor_mDC1197B1E29487F3D086091CF3D5B1029D593402 (void);
extern void Capture_get_Index_m35E8D105E5E2142415F85116BFD4BA369AF7A17A (void);
extern void Capture_set_Index_mD5E578A0833A03602FBDAA2ABF77C9CC849CDABE (void);
extern void Capture_get_Length_mA3E97C9DF116CD4B4C20EBFB70A1312F33DA7A92 (void);
extern void Capture_set_Length_mF9069396819D5D0282F38E70F347CDC625BE016E (void);
extern void Capture_get_Text_mDD394681E5FC39FFDCCD9552C0BF09DF8A7054ED (void);
extern void Capture_set_Text_m99AE1BB9AF9D7744D0E6F747DCF5A378591C353C (void);
extern void Capture_get_Value_m1AB4193C2FC4B0D08AA34FECF10D03876D848BDC (void);
extern void Capture_ToString_mC843EDDC990DBE2696BA08D08B1081CA12228F2C (void);
extern void Group__ctor_mFDE74CF068520CBE797B563B419B8817E60CB58C (void);
extern void Group_get_Success_m4E0238EE4B1E7F927E2AF13E2E5901BCA92BE62F (void);
extern void Group__cctor_mCEC94034CDEE90B4E5B77AE639965DA426C49161 (void);
extern void Group__ctor_mEB6936CDFD191A4C92766CE3F3301161D7505C5E (void);
extern void GroupCollection_get_IsReadOnly_mF1DE2BBB4E573C6EDE85B787281F90951AAB8C4A (void);
extern void GroupCollection_get_Count_mE8775E53B4F5DA6D520BEF5ECEED2E0C08A26B39 (void);
extern void GroupCollection_get_Item_m40EC174D4AC8FDD68F8819C35B779C79A44322F3 (void);
extern void GroupCollection_GetEnumerator_m47E269C5A8C902948812AF4F52D0C96759550EE9 (void);
extern void GroupCollection_System_Collections_Generic_IEnumerableU3CSystem_Text_RegularExpressions_GroupU3E_GetEnumerator_m593F2B44A71E46606D2E8C0816E626675EE2087D (void);
extern void GroupCollection_GetGroup_m8EDFDB8D85D8BEC6EB98B68CA69EE0022FC16B13 (void);
extern void GroupCollection_GetGroupImpl_m27CE16949BE0A9CFC61CC6CBF3FB179F63654D92 (void);
extern void GroupCollection_get_SyncRoot_m7505B29FFFE40E5319BAD3F3B3332F8A6299570A (void);
extern void GroupCollection_CopyTo_m122BEB82372AC34C575FCB8C71188A2A49CCC482 (void);
extern void GroupCollection_CopyTo_mCA848040E42611E6A969C9E4F632FD81FC27B52A (void);
extern void GroupCollection_System_Collections_Generic_IListU3CSystem_Text_RegularExpressions_GroupU3E_IndexOf_m63AB69A8D37D49B1AFFE8CDEED6442D25A25F94A (void);
extern void GroupCollection_System_Collections_Generic_IListU3CSystem_Text_RegularExpressions_GroupU3E_Insert_m515A74F3095C972A28657B89FE6B65A3EC5324FA (void);
extern void GroupCollection_System_Collections_Generic_IListU3CSystem_Text_RegularExpressions_GroupU3E_RemoveAt_m981151BB2C68E7F2DC0BF4DD61D4AD4AB5B5AE3A (void);
extern void GroupCollection_System_Collections_Generic_IListU3CSystem_Text_RegularExpressions_GroupU3E_get_Item_m7FD6BE83AFD3DCC2F9ABB2AB4B938F552DF1D45D (void);
extern void GroupCollection_System_Collections_Generic_IListU3CSystem_Text_RegularExpressions_GroupU3E_set_Item_mB5DBB39B4E6F360B8105064E6659EFC49E8B1C85 (void);
extern void GroupCollection_System_Collections_Generic_ICollectionU3CSystem_Text_RegularExpressions_GroupU3E_Add_m96D9B7FCEC33784ED88BAC3C291146E64A921D8D (void);
extern void GroupCollection_System_Collections_Generic_ICollectionU3CSystem_Text_RegularExpressions_GroupU3E_Clear_m67DAFAB787617D8A9B720AAF612436587E2C3172 (void);
extern void GroupCollection_System_Collections_Generic_ICollectionU3CSystem_Text_RegularExpressions_GroupU3E_Contains_m260CC86A4FD94F5682B73C9C8BE03551C14B3568 (void);
extern void GroupCollection_System_Collections_Generic_ICollectionU3CSystem_Text_RegularExpressions_GroupU3E_Remove_m008F559DB848823129DDCB7B832C731F0D4AFF83 (void);
extern void GroupCollection_System_Collections_IList_Add_m08D9178D05AE66397CBCF2184448AE712741E4D1 (void);
extern void GroupCollection_System_Collections_IList_Clear_mCFF90C2303D7106D722E8B574ED3261C80F250CE (void);
extern void GroupCollection_System_Collections_IList_Contains_m9E34B033D0BB903B2491336D7F70BE5A59D46208 (void);
extern void GroupCollection_System_Collections_IList_IndexOf_mA974FC7CFC23310A1CAAD8A750823D5E0ED762AC (void);
extern void GroupCollection_System_Collections_IList_Insert_m3CDAAB43F292FD0ABD2D9595F02233FB55577623 (void);
extern void GroupCollection_System_Collections_IList_get_IsFixedSize_m5AA2CBFB17E2617597BDFBB56CCA1E435A763014 (void);
extern void GroupCollection_System_Collections_IList_Remove_m32982B763D442200E71B8A5E6C85C9501A555F23 (void);
extern void GroupCollection_System_Collections_IList_RemoveAt_m32BF4A16212F4849AD46F680E5D0D14268CB7C58 (void);
extern void GroupCollection_System_Collections_IList_get_Item_m969EA1758BE453F49B700FABF29940C5C9800573 (void);
extern void GroupCollection_System_Collections_IList_set_Item_mDDFE5D350953DB61758FE64D556670EA5A402C48 (void);
extern void GroupCollection__ctor_mB178F93E8DB52AA6412FF28E5FE50943E2BABFFB (void);
extern void Enumerator__ctor_m17B823D35315CED0509F6B4C5F04C148B9388DDD (void);
extern void Enumerator_MoveNext_m1DDDE3FC6B83FF15ED8AA895DE1357C37B7241C5 (void);
extern void Enumerator_get_Current_m6201905A1315214B04F5001307F52887F9161F43 (void);
extern void Enumerator_System_Collections_IEnumerator_get_Current_m16F94921184B09EDC3D45E9CFE3F3FAA1E5D862D (void);
extern void Enumerator_System_Collections_IEnumerator_Reset_m5B7067CCF9E8139F34641618492A2DAACA5419B0 (void);
extern void Enumerator_System_IDisposable_Dispose_m1C77D8E3DFF1A034E9329E11854F11012F20AA69 (void);
extern void Match__ctor_m2A6BD37E98680890114AC62CDD23E1821E80A53A (void);
extern void Match_get_Empty_m50E10899C5A892532ED484CDA11DE27CC6A7B546 (void);
extern void Match_Reset_m53A78F16760B7CD5CC8A104F6CD95CA255D888AA (void);
extern void Match_AddMatch_m5F28F3255C64D67D56C1542CE32A9D324F96780E (void);
extern void Match_BalanceMatch_m85EA44453E637DEBE24ABC7562BF91EE1222DBFD (void);
extern void Match_RemoveMatch_m154DB10030027B3738903FFB50DE511D71656B04 (void);
extern void Match_IsMatched_m9D6E0C0AB1F3E1E8E49E77E3A0A0ECCD7C2C4FBA (void);
extern void Match_MatchIndex_m97F06DECDDA8BD3E4D8DF6B9224B8E24C0764F35 (void);
extern void Match_MatchLength_m5C93EA60B018A79D9D05C8E69D25835848471B01 (void);
extern void Match_Tidy_mF0A46109EAF0E91CBCA732985265E1DC20CA3F51 (void);
extern void Match__cctor_mB241D90D9F6B1311AC606248B3CCA821F8CC9855 (void);
extern void Match__ctor_mBABEF8F11A3BC6DB713519819D135CC87AC1B4CD (void);
extern void MatchSparse__ctor_m7CEA8A7F538C706527778E8E75003DD74AC2FD41 (void);
extern void ExclusiveReference_Get_mCC2B9161946C9532001438C65A38E8D8FD56FB3C (void);
extern void ExclusiveReference_Release_m7A911FBF52436AA832188BF89CC558155BE09018 (void);
extern void ExclusiveReference__ctor_m41EB96FC1FA261CB792752B89F15E6513FEA1722 (void);
extern void Regex_GetCachedCode_m2039E73548B61FE55B6C8D85C00076DBA50332E6 (void);
extern void Regex_GetCachedCodeEntryInternal_m9FE175C47BF909F7F223013004C0EA0DAD660C06 (void);
extern void Regex_FillCacheDictionary_mC47F317967E556D1F03A344B78B215356D604755 (void);
extern void Regex_TryGetCacheValue_mA2B41F91C84CB02DA59E18DB80B0A49CBC600A8B (void);
extern void Regex_TryGetCacheValueSmall_mA62E6520886883BC787412F588CF79B7A02B9A28 (void);
extern void Regex_LookupCachedAndPromote_m2FE5584043609F1616158AF8621FC3ED48BCD900 (void);
extern void Regex_IsMatch_m7E96E666FBE7259D7638A3A6A21BE824D2406F49 (void);
extern void Regex_IsMatch_mC358D80573E765ECC19F9F5882664E3B1FA0966E (void);
extern void Regex_Match_m58565ECF23ACCD2CA77D6F10A6A182B03CF0FF84 (void);
extern void Regex_Match_mDF110993CAA26CD41AE9F5DB7F0C307B2680CED3 (void);
extern void Regex__cctor_m689E73750FECC607BB4E1BF503660C16BE0F40E8 (void);
extern void Regex_ValidateMatchTimeout_m530D1B5EBC69BEAA11CCB7053A96C4AA9869C591 (void);
extern void Regex_InitDefaultMatchTimeout_m8EEBEF239D034DCF29E50E681B318B517202854E (void);
extern void Regex__ctor_m082970AA73B8236360F0CA651FA24A8D1EBF89CD (void);
extern void Regex__ctor_mE3996C71B04A4A6845745D01C93B1D27423D0621 (void);
extern void Regex_System_Runtime_Serialization_ISerializable_GetObjectData_mD788EE38607BF5D618FDA920048707DF4CA4B99C (void);
extern void Regex__ctor_mA8EBF2F80CC00D741BFD2FFD7DC9DB26FBCEE2A4 (void);
extern void Regex_get_RightToLeft_m52C4DEE1DC94FC9531785B74111022F3BCC1DF4B (void);
extern void Regex_ToString_m4982DE4C344F7D529666314646F071580A450D5B (void);
extern void Regex_GroupNameFromNumber_mC99A37419C4C0C9C3869BCDF055EDC72782D53FF (void);
extern void Regex_InitializeReferences_m55385AD102411A38BF4426C08F6FEC6E55E4D4D6 (void);
extern void Regex_Run_m4DCD2202C599273C00D6052A77BDD2F39E2F5BF8 (void);
extern void Regex_UseOptionR_mFAB8780E9F998BE60C4B1127D57DD627A3B5629C (void);
extern void Regex_UseOptionInvariant_mD51BCBA0BF4C1B5CEBE9423B4AAA93CD83569B60 (void);
extern void CachedCodeEntryKey__ctor_mE505E40C97820B3FD140F6212F0B4ECF8745585F (void);
extern void CachedCodeEntryKey_Equals_m59F40708B97179EA5C9019EA6A958D7AB8BC8CA2 (void);
extern void CachedCodeEntryKey_Equals_m55F9ED97CB9AF357034A75C2FDB8498FC55D3AF0 (void);
extern void CachedCodeEntryKey_op_Equality_mE9E3CA5933F8AE24BCE5AD52D6C4EE014C085E40 (void);
extern void CachedCodeEntryKey_GetHashCode_m253B53C100F840B987A487020F02184019D776DE (void);
extern void CachedCodeEntry__ctor_m14145ADFF67BA7C888673D806C24E28C96B83B2A (void);
extern void RegexBoyerMoore__ctor_mFDCB1AB420E718548B67DE42C233402955BBB189 (void);
extern void RegexBoyerMoore_MatchPattern_m0E84EAABBFCDA85484466A41A7B90187D3489E8C (void);
extern void RegexBoyerMoore_IsMatch_mC09692237823CC6C74D4F5D8900D82CEC840FB47 (void);
extern void RegexBoyerMoore_Scan_m86EB5A209D33DA3F4C7335F8FE133238666FA57B (void);
extern void RegexCharClass__ctor_m165680E6EA10035E0630220C967BD772F5D6F52E (void);
extern void RegexCharClass__ctor_mFAD2AF3D7B0D4D3DC8BC850B8899C5C4D55F8B49 (void);
extern void RegexCharClass_get_CanMerge_mC093FE4BE7C1933BA9F146CF2A33CD729A965172 (void);
extern void RegexCharClass_set_Negate_m8EA71CB8F671E99EF0B388CB520F44B8B6653A6B (void);
extern void RegexCharClass_AddChar_mED269B31D5C606032B3394A9B9305ED172D16746 (void);
extern void RegexCharClass_AddCharClass_mDE8C70A6A99B2223DE4EA36A4753A13B5EEDE7BB (void);
extern void RegexCharClass_AddSet_mFC202CC46631017C7D64ED17EAA9C786C5C7CE83 (void);
extern void RegexCharClass_AddSubtraction_m4EF34AFB93C001C6A4521E1F1C5F4B54833E8A12 (void);
extern void RegexCharClass_AddRange_mB1188A62712FE4ABB7F5DB28645351E375B5C6A7 (void);
extern void RegexCharClass_AddCategoryFromName_m032AED661665AE30D7F3F712914E2937F6E4FF97 (void);
extern void RegexCharClass_AddCategory_m3A3C78511F279FD0CF67A3EE36F59A548A37269A (void);
extern void RegexCharClass_AddLowercase_mB87FC245CC837E582370E5410A6B40B23BEAC0BE (void);
extern void RegexCharClass_AddLowercaseRange_m75E07C55A082FE5B2926997F2564B611DF61A343 (void);
extern void RegexCharClass_AddWord_mAED995BB6613893E0950C5895515204F1E95D152 (void);
extern void RegexCharClass_AddSpace_mD1BF3AD215D69EC6CF0E49A359F7FC709FD6FBB6 (void);
extern void RegexCharClass_AddDigit_m04C1001F7E8DA85CB2D262ED403BA9BD215A7A8E (void);
extern void RegexCharClass_SingletonChar_m4B0E4CE38D80141C013BE74B01C58FB81B0DE630 (void);
extern void RegexCharClass_IsMergeable_m986CE5853CF227EC275D0F16E91472A3FA903531 (void);
extern void RegexCharClass_IsEmpty_m10A0C981C0200FFA5211CF9EEC8EE1FFECE27545 (void);
extern void RegexCharClass_IsSingleton_mA3970BE305000CAE9576642E3C6751249767A2B4 (void);
extern void RegexCharClass_IsSingletonInverse_m04C0F39C52377F64FA6B7D93AF2B89D203E9A108 (void);
extern void RegexCharClass_IsSubtraction_mE9E413F4B753B830F3091897DB20C4E6E88EA5F9 (void);
extern void RegexCharClass_IsNegated_m7F52D93AA1B63D6C1AACB04DE2CA8ECFB901BA47 (void);
extern void RegexCharClass_IsECMAWordChar_m95AFA872BB22C901C60DFCC9C8735BA78EE1EC84 (void);
extern void RegexCharClass_IsWordChar_m2912E5148F0F89512DDF7D629188ADA82961080C (void);
extern void RegexCharClass_CharInClass_m635FAC50DE8EF6690ECA98FADD367716A5A54BEC (void);
extern void RegexCharClass_CharInClassRecursive_mEC6F84D9CFEAD5A3F0091FC05296AA53FF188232 (void);
extern void RegexCharClass_CharInClassInternal_m013394FB15A790E1BF02D518C3F7485359A4E3E3 (void);
extern void RegexCharClass_CharInCategory_m18B64CFDDA01F24D468ED85E35E72D7B71203957 (void);
extern void RegexCharClass_CharInCategoryGroup_mE20D51D1FD31B839D900136C42B8A7F014032C82 (void);
extern void RegexCharClass_NegateCategory_m6BE8AAA97753BF3F6A9577E4EB5D8A0590ADF638 (void);
extern void RegexCharClass_Parse_mE586DA457874C3808D48C2C514AED5B147221D27 (void);
extern void RegexCharClass_ParseRecursive_m04198C9230D295E6B3261040CA9DD37DE03EE79F (void);
extern void RegexCharClass_RangeCount_m36A38A50CD2E3E61B5F55D95562BD92923F981F1 (void);
extern void RegexCharClass_ToStringClass_m6B0B92A0A525001CD78A6C2A766295268E0FED57 (void);
extern void RegexCharClass_GetRangeAt_m67B8198B09F344044D8F742C5C9869034C4A3128 (void);
extern void RegexCharClass_Canonicalize_m77EEBAD046B3EAD5110DD472434BDE16851C7A9E (void);
extern void RegexCharClass_SetFromProperty_mD4411181E13EEB6F8D96972AF4564698999E9DF1 (void);
extern void RegexCharClass__cctor_m15D71DF95CE4C3DEF5BDF06B03E6521A07E58296 (void);
extern void LowerCaseMapping__ctor_m06D0A21D5D72E39535F82076EF046F98C62D7D5A (void);
extern void SingleRangeComparer__ctor_m9B430EFE70974D964D73913D81E7C6C9236BBCCE (void);
extern void SingleRangeComparer_Compare_m917FDE86E5F31A755D07077EA095F8B7E27E130B (void);
extern void SingleRangeComparer__cctor_m649382AF0493818C7B5D453010FE22A3523F1A90 (void);
extern void SingleRange__ctor_m14FD490AFB119E412298554D31FADDCB9465511E (void);
extern void RegexCode__ctor_mA50EE1CFA1A050BFC45E135DAB7E2704049512E0 (void);
extern void RegexCode_OpcodeBacktracks_m44FC8B177CFCB191B72C109984A707744FD7B2BC (void);
extern void RegexFCD__ctor_mE1C0A21180069D25EEF6F3702938ABB087F35C0C (void);
extern void RegexFCD_FirstChars_m3BAEF0481AF78F4FC40C35CEDA9100017A6D794B (void);
extern void RegexFCD_Prefix_mE4F5ABEA5443B6940F83E59C60D0A9A201CC4BD6 (void);
extern void RegexFCD_Anchors_m9211098DA08795B8459B918CA3D9A1A4733365D7 (void);
extern void RegexFCD_AnchorFromType_m825872EAB7A0322BBD677DF34281A3ACEE8808D4 (void);
extern void RegexFCD_PushInt_m0B38AFDAF0D14FF1E41AC90838B40C4C866D8875 (void);
extern void RegexFCD_IntIsEmpty_m05E3C6FF40B69E98D90CF1C12DF3F32AEC800B5E (void);
extern void RegexFCD_PopInt_m1DF4F5E6B4BA9E15AFF2B3C44D1A233EA46EFCD0 (void);
extern void RegexFCD_PushFC_m1396C7FD5ACE3D28076FAFF667D9A0D169F8B675 (void);
extern void RegexFCD_FCIsEmpty_m749E6423466EAD51E38445AC91E76DE31258574F (void);
extern void RegexFCD_PopFC_mB83F2100E8EBACF17904BD5CACB57525D2966628 (void);
extern void RegexFCD_TopFC_mAE41171B96938FD879E0C0AE7C0D06645AAA84CD (void);
extern void RegexFCD_Dispose_m0B04A2F3B819FA7BFAF0AECCBF6D22E6D9789C07 (void);
extern void RegexFCD_RegexFCFromRegexTree_mD5A53219905969B914CE27404553A4360DA78500 (void);
extern void RegexFCD_SkipChild_m5ACE619BAC10DD5DFE755655DB6151E7DB0885FD (void);
extern void RegexFCD_CalculateFC_m16F7EB523C02B530A741A7F6364F2E46865DCC91 (void);
extern void RegexFC__ctor_m18E889B95C8C6014582FD2C6991F789190519F0A (void);
extern void RegexFC__ctor_m073D88DF033828EB626DBEA191FE78296CADB57D (void);
extern void RegexFC__ctor_m69E0058CD2F4A36F14589250B3F5CBAD7025D6F5 (void);
extern void RegexFC_AddFC_m61D0F8CDE635ED3A48AEA069A7700B100D2AE851 (void);
extern void RegexFC_get_CaseInsensitive_m4FCD7C7545925463ADA4055E9FE62D5A452E2102 (void);
extern void RegexFC_set_CaseInsensitive_mF9A3745D1575160C2F2B1F7F9EB5541BD0C9FF4B (void);
extern void RegexFC_GetFirstChars_m6BAC398ABDCA5AF58CBE678143C9792611A2A3E8 (void);
extern void RegexInterpreter__ctor_mB81896A8292574F7532CF707F74A1AC1DECEA9FF (void);
extern void RegexInterpreter_InitTrackCount_mFFFBE9294C6FCB854D4502362473C40A5ABBBFC4 (void);
extern void RegexInterpreter_Advance_m31E2455F84B61536DE535BE51D4D0B42DC33857B (void);
extern void RegexInterpreter_Goto_m9BF559085E75D4AA7457FAD60E879AB96BB4AEC9 (void);
extern void RegexInterpreter_Textto_m66DE4B949DBAA02672CFDE27162BCED542038850 (void);
extern void RegexInterpreter_Trackto_mDFDFF0401BC8F2B2D26AEC65B54B963F84A089AE (void);
extern void RegexInterpreter_Textstart_mDF185C6B3E245A2307A8B0B9F5FE5DF8C8C9936E (void);
extern void RegexInterpreter_Textpos_m6E20619B1C7F8B77D7A57A8A6939C24011808DEC (void);
extern void RegexInterpreter_Trackpos_m6BDE792A7333FADB2533591B88211FD77E8F0401 (void);
extern void RegexInterpreter_TrackPush_mC82959F407482A4713C419296455A18F5D861E93 (void);
extern void RegexInterpreter_TrackPush_mA225A4A0302F57CE82842189DCB8895A9AAEDB3D (void);
extern void RegexInterpreter_TrackPush_m94D69F6CC8E122478F4EADE04FB7FC9482CEC878 (void);
extern void RegexInterpreter_TrackPush_m606F28BD60B479F0FD3073F8B5627836DB89230E (void);
extern void RegexInterpreter_TrackPush2_m5131F1DCBCA4D4D2231B44A85B08DE319BE31C9F (void);
extern void RegexInterpreter_TrackPush2_m18FF10B704A6586A18D9AB21D28737DC32DA2797 (void);
extern void RegexInterpreter_Backtrack_m8D227FAE1EFD91174D43FF070D9200291941E593 (void);
extern void RegexInterpreter_SetOperator_m693B374C6827D36CE755EFFDA53E245FB04652EC (void);
extern void RegexInterpreter_TrackPop_m2FE356634C8337D8B657D30790588E4A4D7DECC1 (void);
extern void RegexInterpreter_TrackPop_mD3125258D278581E337C822F3C9847233E06224B (void);
extern void RegexInterpreter_TrackPeek_m8D2CDC071ADBBC028FBAA364E2C18BD19FD6D861 (void);
extern void RegexInterpreter_TrackPeek_m45C4F44554EC086C01B03C054BF6A94105D39699 (void);
extern void RegexInterpreter_StackPush_m9BC14D7DD0FAF99AF230B436F32557E056F8FB60 (void);
extern void RegexInterpreter_StackPush_m7741DC1BAEE69DFB68E1D60A9FC39C718829DF0F (void);
extern void RegexInterpreter_StackPop_m3284B40674B96F317D05BD2616AAA80181E0AEFF (void);
extern void RegexInterpreter_StackPop_mAEBB71FEDEADC065C5FA19E7C2C0FF0E82CEE681 (void);
extern void RegexInterpreter_StackPeek_m16057890EE30C3383227F21632C02C6BEF981991 (void);
extern void RegexInterpreter_StackPeek_mAFBD38869C8A829D6ACF534643B8A6C9863A10D8 (void);
extern void RegexInterpreter_Operator_m3BD9AD7DCDA4E84DE4821DA786E1B1483E677B58 (void);
extern void RegexInterpreter_Operand_m09F048ED7BBB36BEFC5023D420323E37A97C9560 (void);
extern void RegexInterpreter_Leftchars_m6FD5AA399927D1767633B24AE234D596CBDD4002 (void);
extern void RegexInterpreter_Rightchars_m0D684BDADC810F61B803D30D121506833AD07CD7 (void);
extern void RegexInterpreter_Bump_mD5E6B4A80D6C620EDD37A5C04156ADF7C27EE9E1 (void);
extern void RegexInterpreter_Forwardchars_mE97F35195F994160F951E3C035F9E0C35EB739C6 (void);
extern void RegexInterpreter_Forwardcharnext_m78266C74DAAFD2D09050F148FFDA0A50656AC4C1 (void);
extern void RegexInterpreter_Stringmatch_m89AE288DB294AC0146C7772193CAD3B702849E54 (void);
extern void RegexInterpreter_Refmatch_m457B37C3E3B0671E7E89B7F6C16396D36EB32F68 (void);
extern void RegexInterpreter_Backwardnext_m0C573AFAD3FAEBED37991618A232ACB169F524FF (void);
extern void RegexInterpreter_CharAt_m9EFCB7CE5FB311B6C061AA996705CD3D402F8E8C (void);
extern void RegexInterpreter_FindFirstChar_m87D986F0502AEEFC4F13EBD0EAD891C495C4845F (void);
extern void RegexInterpreter_Go_m1CF26F8D95231F3B2235EA98AFB286C3F1629BED (void);
extern void RegexMatchTimeoutException__ctor_m24286DF7231B227E3A8B3C31AE33447361971005 (void);
extern void RegexMatchTimeoutException__ctor_mA8C497C6700E1233B4953A8AA7B8F57C8682C410 (void);
extern void RegexMatchTimeoutException__ctor_m7DE41C173C1A57CEA5D015FF63DDFCB70CFF4FBF (void);
extern void RegexMatchTimeoutException_System_Runtime_Serialization_ISerializable_GetObjectData_m6F530DBA34FE5CBECBCD322C8FBD4AE28054688C (void);
extern void RegexMatchTimeoutException_get_Input_mA4DCA4F3C9692ACDC45FBEB683EE4165C84E4073 (void);
extern void RegexMatchTimeoutException_get_Pattern_m8F12503E2B66F34B8E8EFE46971C169EA1BFBAB2 (void);
extern void RegexMatchTimeoutException_get_MatchTimeout_m12D9D88EFFA06EAC0FF06C81ECF29BAE0F906087 (void);
extern void RegexNode__ctor_mAE4AC160F21E5DF26B99B74753BCFB9EF0951B2B (void);
extern void RegexNode__ctor_mFA693535F5B9610F075D30F19626A8AFFA879D7B (void);
extern void RegexNode__ctor_m00D0AE9AB07549681FB13D96CC0AA20FED8CE6EA (void);
extern void RegexNode__ctor_m3352B98BE0DAD220AB653F929BD6D95689B5E07F (void);
extern void RegexNode__ctor_m5ECC62B9C3918CD043542E5BEAAD39A06158E27A (void);
extern void RegexNode_UseOptionR_mEBD872419C386D7C7802D2A6D2187E7B6D1D1641 (void);
extern void RegexNode_ReverseLeft_m251846220299FD022068361A09B9A7A4CF67A586 (void);
extern void RegexNode_MakeRep_mFDCFC9F5DA5DCBFC66D6F00A8E08A602F00C5F48 (void);
extern void RegexNode_Reduce_m9D14953F47CD1D924C3EEB9D0287D7F257AD2D98 (void);
extern void RegexNode_StripEnation_m2BACBC29E58CEC452B91534BCFA2A21639712B61 (void);
extern void RegexNode_ReduceGroup_m62F5B20C564AC8A8E8C1C9E00684A8BB9A988E3B (void);
extern void RegexNode_ReduceRep_m85B3760500D732A04B60CD3F247CE8ECA1E6F8C1 (void);
extern void RegexNode_ReduceSet_mD307DD149936830074467A0F24C2E20032F708E9 (void);
extern void RegexNode_ReduceAlternation_m193F3C5A073401BA987E5AB7D35F59B1FC902341 (void);
extern void RegexNode_ReduceConcatenation_m95EE4F324360AC71D52EDEC1C53F6F81D73C67C3 (void);
extern void RegexNode_MakeQuantifier_mA6A10E1AEF5AA80F82BD1BE6D28E62259208B5BC (void);
extern void RegexNode_AddChild_m1A2B3A7445927E39341C233C0E533D77FC9517A8 (void);
extern void RegexNode_Child_m6F5C25EE5349D1BE0D65C89475FB0C42A15BFF27 (void);
extern void RegexNode_ChildCount_m833AF69D2711D8EEC5E50FD666A61F428A12396D (void);
extern void RegexNode_Type_mDE9685FD99646016D0D6038C4509C6392B10F019 (void);
extern void RegexParser_Parse_mB845F2DC38DFD8C50E5B8D0D0F87330597409793 (void);
extern void RegexParser__ctor_m99DC2B2C4B64E7298BBF49AD862BFD12007E38E4 (void);
extern void RegexParser_SetPattern_m852D668CE45FBCAA3A6ACC415B94BDD0A7190265 (void);
extern void RegexParser_Reset_m426A8C90F4D8065486802AE7AC9FB07FC599EB01 (void);
extern void RegexParser_ScanRegex_m3CD95B8EAE4F2E8ECF3461F199B04A021C77231D (void);
extern void RegexParser_ScanCharClass_m01448F1178653B15083659F29014BB87A6E2295D (void);
extern void RegexParser_ScanGroupOpen_m5ABFBB28982A5097B2D560091D2AC12EA8B028F7 (void);
extern void RegexParser_ScanBlank_m4C1B18EC7A5DF860363CA17E69B20A00023B5E82 (void);
extern void RegexParser_ScanBackslash_mAAE11106CB78789803468679894E1D7AE6B0C92E (void);
extern void RegexParser_ScanBasicBackslash_mB0F1728AD89C8F196601B3110F0BA807FBC8FCEC (void);
extern void RegexParser_ScanCapname_mC9524DCBE266D677078207375663506AC32BE3F7 (void);
extern void RegexParser_ScanOctal_m95EA0AE4B61D5418914DDD3815C6277471B4EDFD (void);
extern void RegexParser_ScanDecimal_m1BFFE19E0698D0EC97D81C655F3D04B608897161 (void);
extern void RegexParser_ScanHex_mDD96A112E30DABDB5BC0C558373DA5607F8ACF95 (void);
extern void RegexParser_HexDigit_m40FED0646820843A4C35AF9349E27B0E0A84A5CE (void);
extern void RegexParser_ScanControl_m26274BE5776CF5AC03E8EAC7B5972C0A8AF2A95B (void);
extern void RegexParser_IsOnlyTopOption_mAA13B95864B8B314B8B6689B47FA4BFE3B433280 (void);
extern void RegexParser_ScanOptions_m56F01464F53558D3AF3BF1CD874A2656930AF31C (void);
extern void RegexParser_ScanCharEscape_mA872DDDD24E165F5B5C5F62DCFAE3B02CCF97C98 (void);
extern void RegexParser_ParseProperty_m3B765A1B30A38A723CAFE63AF9D6F3D08C7C6F38 (void);
extern void RegexParser_TypeFromCode_m11B176B0B9A6EF391370E021B111101AE56F4CC6 (void);
extern void RegexParser_OptionFromCode_m7E2E8164FA38C45692BC68054E574F00B65F383A (void);
extern void RegexParser_CountCaptures_m2ECDC9ED534F245A12224038BB86DE5D66DB56A3 (void);
extern void RegexParser_NoteCaptureSlot_m21BD1F90FF2C62FFA445303D574D271757D62222 (void);
extern void RegexParser_NoteCaptureName_m5CDAF94FD43B665DE067B6DC02BD72A315D48FFD (void);
extern void RegexParser_AssignNameSlots_mC5738B99DE328E7D1FA7279A717640EC2044857E (void);
extern void RegexParser_CaptureSlotFromName_mE18BA49BCA1CE4B972DC804E52C78E48905FEFAB (void);
extern void RegexParser_IsCaptureSlot_m1EE21960AA0BB1C072BD9D765AFF8A6DFC3192C3 (void);
extern void RegexParser_IsCaptureName_m5DC0D7BF3AD01C929CB982048722BCD43797CEAE (void);
extern void RegexParser_UseOptionN_mD441B71AC8368C644B8D9A0CCB3DB8BD681639B3 (void);
extern void RegexParser_UseOptionI_mB3FB0740ED596E6ABC827E903DCE8AA4258BA701 (void);
extern void RegexParser_UseOptionM_mAE3FC2655A6E80EA35D5CEBEE1D82B5B07529118 (void);
extern void RegexParser_UseOptionS_m070AD4458172FB14FF21E4502DCA6ECEE8938994 (void);
extern void RegexParser_UseOptionX_m0BFB48EDC8A2F38A29A8F6CEC85080D9E04010FC (void);
extern void RegexParser_UseOptionE_mE0B7314EAB95CAC9B9762CD3AB9FBC71BFD410D9 (void);
extern void RegexParser_IsSpecial_mF59FE804CDD354250CB77C4113F39C627349D051 (void);
extern void RegexParser_IsStopperX_m173A82937CA9600F7CB12B9A101F39CCFDA630C9 (void);
extern void RegexParser_IsQuantifier_mBD18FA810A0404B632392725175846E6B30A8782 (void);
extern void RegexParser_IsTrueQuantifier_m22CCE851B85E0760270178DF1184746040E68AEF (void);
extern void RegexParser_IsSpace_m322B45C2A04F07D8C83B547370D266F39A8DE14E (void);
extern void RegexParser_AddConcatenate_mDDD0350FE254D75D6707668332D0F3088A5572AF (void);
extern void RegexParser_PushGroup_m2D76CB852430A3B3F7454DDC3322B2A5E4CE398F (void);
extern void RegexParser_PopGroup_mEC525DC83E5BF2F8DA03129AE3DF06834EB76552 (void);
extern void RegexParser_EmptyStack_m756AC5BB66D0F2077EC7DD655A3D57681200F5CA (void);
extern void RegexParser_StartGroup_mBF861FCFB0FB9ED26744CAE2297537DAB6DF37C1 (void);
extern void RegexParser_AddAlternate_m77BCF63DD4E1AB89B70F72821CAE4B67936CD847 (void);
extern void RegexParser_AddConcatenate_m77A3D9F6B1484C7B656BDF3CC5659248F9DEA1EA (void);
extern void RegexParser_AddConcatenate_m3B10D68AE09CC6A2F06AC65E49B918D11D5375FF (void);
extern void RegexParser_Unit_m6C984A797C4A92AB0A9985E5A8207DB9CA9AE87F (void);
extern void RegexParser_AddUnitOne_m991DCA85D3432B8F7F74D18125A69FFE25108F47 (void);
extern void RegexParser_AddUnitNotone_m4F79DE175B95FDB88323D7FE9DC3132E24F8F0C0 (void);
extern void RegexParser_AddUnitSet_mA262F57A7FAB27996D8115F66BC84A5C58317D7F (void);
extern void RegexParser_AddUnitNode_mE8DC7CD039C933AE881097E041BA620AA325FDE8 (void);
extern void RegexParser_AddUnitType_mEBDFA2325ABDA3FDA74184DBFB706824F6540553 (void);
extern void RegexParser_AddGroup_m8989D6A9CD4B700522D722DEF5FEFC267C4DBFB7 (void);
extern void RegexParser_PushOptions_m6E501EE3C5D6FE990ED74935A9AC9A6E834FD968 (void);
extern void RegexParser_PopOptions_mAE4848419A71162B8E563781AC817A8D6ACEB91B (void);
extern void RegexParser_EmptyOptionsStack_mC16A4D18503FF296911710732A95062A8279163B (void);
extern void RegexParser_PopKeepOptions_m911F83DFA290ED90B06D3D3166553E6205C256C1 (void);
extern void RegexParser_MakeException_m385FDF5B69D02A0CE93393E92AFAD928A9CCE3FC (void);
extern void RegexParser_Textpos_mBF50F3EB06FDA6C68AF4BF0D0C8D4FC09FDCFC88 (void);
extern void RegexParser_Textto_mA45B325CA6CCBC0012F1CBB0282AE114B34AEBE2 (void);
extern void RegexParser_RightCharMoveRight_m4386CDEFA7DC102FCA0E6E00E522E1712AE3AFE9 (void);
extern void RegexParser_MoveRight_m5F6B031B292FC0F81DF5E7039EDA6C9D0706A0A9 (void);
extern void RegexParser_MoveRight_m921C1D96B625B4C8AF1D658E514D57CC44E40B61 (void);
extern void RegexParser_MoveLeft_m57BB7A0146D0E8834D715667048B75A517761AD5 (void);
extern void RegexParser_CharAt_mF46179762DDF46FB78E32CCA3058950244CFCB70 (void);
extern void RegexParser_RightChar_m23D21ECC3D6999A466C0A91BDF589BFE4801CF91 (void);
extern void RegexParser_RightChar_m6BEE1FBF048A2DFC53235447BB2F6AC8C56A4147 (void);
extern void RegexParser_CharsRight_m6F83CD819B532331F5778143B6A95B31C8734A79 (void);
extern void RegexParser__cctor_mC883BE9D9C8D47723D5CAD7CE25B5F347929AF54 (void);
extern void RegexPrefix__ctor_m05CD44F32CB0B5F8B2C080EAB94886F9743E86A4 (void);
extern void RegexPrefix_get_CaseInsensitive_m4DAE2D9E91916BFFAB928F7DCAA20A5ABD4560E6 (void);
extern void RegexPrefix_get_Empty_m54EA279F2BE7F39358AB5DF1A5362A8CD92B4326 (void);
extern void RegexPrefix_get_Prefix_m844D02262C0720B0DC488AD1541713AD37BC518E (void);
extern void RegexPrefix__cctor_m55930FB75AB023BADBEAB107BE38A1A559DDB7F2 (void);
extern void RegexRunner__ctor_mBC777F351AD6D1CDD5F20C408B936F7D0F36ED69 (void);
extern void RegexRunner_Scan_m903691F4DFB68C0267F240D61A6F166BB592CE53 (void);
extern void RegexRunner_StartTimeoutWatch_mA17870A15492787CA1384132267054AB09BE8BE9 (void);
extern void RegexRunner_CheckTimeout_m20BF0CD8BC16738ED6E7F20AAF5B4EE695E7E120 (void);
extern void RegexRunner_DoCheckTimeout_mC26B9239FA1CD3DC98F11A82800F61852793CEA5 (void);
extern void RegexRunner_InitMatch_mCCF94A1E7CCE89275E47BB218C4DD58DF5D5593D (void);
extern void RegexRunner_TidyMatch_m0EEEF0CFBB55AFF485B4D9F9FD21834B9B3A01FA (void);
extern void RegexRunner_EnsureStorage_mB3DB043396771740A05B2AABC293DAAB1250B024 (void);
extern void RegexRunner_IsBoundary_mCF4AE83F6FE3CC46C71C4558DBCDEC255BD422B8 (void);
extern void RegexRunner_IsECMABoundary_m56C692126DF69F0933BBFCBF122F764C6093DA26 (void);
extern void RegexRunner_DoubleTrack_m15E35365FAF5F438C7BD0BB0C37594F765949F1F (void);
extern void RegexRunner_DoubleStack_m89C12DF438D7BE4ADA2A4D1DFA36F274F292E1F4 (void);
extern void RegexRunner_DoubleCrawl_m7B67CFD3071825C9F01BC0ACE0E27B1C4EC6DED5 (void);
extern void RegexRunner_Crawl_mBE60CC8F17E6D85E1A87F0A86BC684F32ACE7CA5 (void);
extern void RegexRunner_Popcrawl_m2C994BE22A9A4DE0D897691A1CEF87189DB43E67 (void);
extern void RegexRunner_Crawlpos_m4EC84964D3AD3DB49724BFC28CE89FC34478D532 (void);
extern void RegexRunner_Capture_m3EB42B55A19BD7776156F04CBE9367C6EBA59F9C (void);
extern void RegexRunner_TransferCapture_m642CC595E9FA641B913CCBE02E233DBB319A6E8A (void);
extern void RegexRunner_Uncapture_m70B0FFB308F48B4673F73311330BFF640F47B7D9 (void);
extern void RegexRunner_IsMatched_m7E64237D020BFFA74813A8B6455D5F35671A876C (void);
extern void RegexRunner_MatchIndex_m150A8B71C07022C485630C71B2A9FA6FD195F88F (void);
extern void RegexRunner_MatchLength_m19C9B24F4F6CAC56A391A0AC47A4358C61615BF7 (void);
extern void RegexTree__ctor_m89DA1335AAB55B0707774E978E6078CE42DB0DA6 (void);
extern void RegexWriter__ctor_m37DF964F59213D287C9D812A3D0945744BF341EE (void);
extern void RegexWriter_Write_m69A5583635E511D67176A977E96C17AC982C4F01 (void);
extern void RegexWriter_Dispose_mD34F8BFC41D28F22FBC65E3F1A211DFE85625E04 (void);
extern void RegexWriter_RegexCodeFromRegexTree_m3352CAF05A1B1753D32D768B83BB778B3E2D4568 (void);
extern void RegexWriter_PatchJump_mC1090F37107CAF7F4B52B020E070BDE9284118A6 (void);
extern void RegexWriter_Emit_m5884414A6EA7431DA9C5708E650B20BEDF982CAC (void);
extern void RegexWriter_Emit_m41C2B9DF58542C452CA4FC77A317884578757D75 (void);
extern void RegexWriter_Emit_mDED2545E64CE95D941A1232DBA24EB6C81A7DB0C (void);
extern void RegexWriter_StringCode_m5D4A227BBAA0674B820D70BEA1A2F7D0B29635FE (void);
extern void RegexWriter_MapCapnum_m5EA7BA45A1C4D51DC20C281246D3997A3001A0B6 (void);
extern void RegexWriter_EmitFragment_m0B35F7C3BDC22B5DE82735F2652C7948AE8494A5 (void);
extern void GeneratedCodeAttribute__ctor_m37F2FCCEDE98E3DB23D3A973EFB3E67CFCBCBFFE (void);
extern void Stopwatch_GetTimestamp_mA3BDF219C573A34751D6A792E86C825B74D2CEB7 (void);
extern void Stopwatch__ctor_mAFE6B2F45CF1C3469EF6D5307972BC098B473D0A (void);
extern void Stopwatch_get_Elapsed_mF16CCA2F9B7E2EAEFCAF8D81F44370DBAC1B2178 (void);
extern void Stopwatch_get_ElapsedMilliseconds_m8D8E60ADEB47D008C06D8E57D28D41EAE07AECBF (void);
extern void Stopwatch_get_ElapsedTicks_m3F4040FBF8C7CCDC69E0E04824019DEBB25AA410 (void);
extern void Stopwatch_Start_m35906A38C7D336CF9DD35854C7E170D762A55915 (void);
extern void Stopwatch__cctor_mF9BBC572803E232BF2D323301E90A6AFDB496FB9 (void);
extern void ExcludeFromCodeCoverageAttribute__ctor_m2CB8196E20519167E842D03BE4CFBCC84BEFC9E2 (void);
extern void Oid__ctor_m4AF81B9D51BED48AE505251E0874EC654BA07B72 (void);
extern void Oid__ctor_m533C0126C7C8D382D3E702287A9AD0CED2D0377D (void);
extern void Oid__ctor_m3973A5FE5482D45FABC8642E59B655274B0B5D59 (void);
extern void Oid_get_Value_m59D678A83ED4DE7D87FB7450C8CF4084DB506FD3 (void);
extern void Oid_set_Value_m122282994E6EE138CE4284B052EF8BE2086CA72F (void);
extern void OidCollection__ctor_m9B8C252AE048962DAD7419597007ABA2793F9D98 (void);
extern void OidCollection_Add_m1D4822DF4EA0B912A1DED6AA102D41F7FAEDC8F5 (void);
extern void OidCollection_get_Item_m0AEC71C746E735035A05C50EE2721C1E8FE08F7A (void);
extern void OidCollection_get_Count_m9560D70809087591935904924A15FBEC1FEFEB11 (void);
extern void OidCollection_GetEnumerator_mBB791F48E305B3355A1BCE624E0BB367331F82AC (void);
extern void OidCollection_System_Collections_IEnumerable_GetEnumerator_m4624AA2C7F0693698228803D9B59EFE6AAD6AFE4 (void);
extern void OidCollection_System_Collections_ICollection_CopyTo_m4EF1FB9A18E822BA196BF3F0204B7C06178B9CFC (void);
extern void OidCollection_get_SyncRoot_mD310358F3BF4454604AD96C734BA6EC180CE7A85 (void);
extern void OidEnumerator__ctor_m2168FEE96614FB7460C84762ED3728A5413F42E6 (void);
extern void OidEnumerator_get_Current_mCED077699FA00ED57FA82F8B460C28B86F1B5C9F (void);
extern void OidEnumerator_System_Collections_IEnumerator_get_Current_m0B627B9F510EF4F283F40EBE17164B9231BB7735 (void);
extern void OidEnumerator_MoveNext_mD42201944C163E81DABDE08B1535C4AB9A5778DD (void);
extern void OidEnumerator_Reset_m24F1F25812A7BFE9B02B312BA9E670C930622F27 (void);
extern void AsnEncodedData__ctor_m90028EC56A5E36272EAA4A9279AE23358D46F547 (void);
extern void AsnEncodedData_set_Oid_m877771219F651EA3FF834952300180274531C9C7 (void);
extern void AsnEncodedData_get_RawData_m629D858D7D20E72C1F46124E5032FE0D3B32AADF (void);
extern void AsnEncodedData_set_RawData_m05F9F17765AD384ECE3A333DCBF6A0572099346B (void);
extern void AsnEncodedData_CopyFrom_mA5DD4A419C5FC29D82DBE9B69C9F8CBC2872A1F2 (void);
extern void AsnEncodedData_ToString_m669772943B78D9242BC5F176A4BD857326F95021 (void);
extern void AsnEncodedData_Default_m883444CBD5238793D1B94A6DE273FF2F9C47EA70 (void);
extern void AsnEncodedData_BasicConstraintsExtension_m74BFC192666755E9D8A088880789AB6EA84F1FCC (void);
extern void AsnEncodedData_EnhancedKeyUsageExtension_m3C95D5D2D8F81BEA5E33B264767C8A5E5575D7CA (void);
extern void AsnEncodedData_KeyUsageExtension_m9A9C72CF2A831A1849A55336FC8D6DEE097E28D3 (void);
extern void AsnEncodedData_SubjectKeyIdentifierExtension_m3CEDDCED26AD5621407DAA401F385269348BB789 (void);
extern void AsnEncodedData_SubjectAltName_mDE2F39E1A43C1E13D89319F221517EE54B1A6A14 (void);
extern void AsnEncodedData_NetscapeCertType_m55E43EBDA7882304E8FE5F3DC134A2719F519373 (void);
extern void PublicKey_get_EncodedKeyValue_m153EF781728D471C55002A40BBDA435F1B3A1213 (void);
extern void PublicKey_get_EncodedParameters_mE338A4A69A15907E38A644D624A07DDD55C521E0 (void);
extern void PublicKey_get_Oid_mB5796F569C136759CB740F747C6A3B6F8976401A (void);
extern void PublicKey__cctor_m1B2E44B5BBF321952E0FD1F19C048E43A6506407 (void);
extern void X509BasicConstraintsExtension__ctor_m672BE9B41107A8C30454BF4CAC120D70512BA89A (void);
extern void X509BasicConstraintsExtension__ctor_m76C88E8D4C7CCEC7B2C3D56CC58A56352F7729E4 (void);
extern void X509BasicConstraintsExtension__ctor_m02334D8CAA280B339CA41E2019CF1F02C8B2E7DE (void);
extern void X509BasicConstraintsExtension_get_CertificateAuthority_mB4ECEF43A245E97DA80C6323BE339BE2DF99FF5C (void);
extern void X509BasicConstraintsExtension_get_HasPathLengthConstraint_m8F7C371EFF99BD110663DB106BEE889955255BC7 (void);
extern void X509BasicConstraintsExtension_get_PathLengthConstraint_m4D3F016E42C243887D07DD955518A818DC14BBC5 (void);
extern void X509BasicConstraintsExtension_CopyFrom_mCC603D99CEC828BDF86FDEB3C4650CF4D3753B8E (void);
extern void X509BasicConstraintsExtension_Decode_m7C50097B6238FEF4DAD6A9A09A449DB0CAFFEBEB (void);
extern void X509BasicConstraintsExtension_Encode_mD7FA31A70B025F67062C3844E4FEB09B14A4DE2B (void);
extern void X509BasicConstraintsExtension_ToString_mBFC61BA07CD2BB8EACCC19A5AE72EA81C11D02E4 (void);
extern void X509EnhancedKeyUsageExtension__ctor_mBDA55EB3C6594D4F6A06102B4EAF633F86C3E092 (void);
extern void X509EnhancedKeyUsageExtension_CopyFrom_m9577FF97B8E3074B12B7056BF7E30AA0F84309BF (void);
extern void X509EnhancedKeyUsageExtension_Decode_m1110DD9BD5D717540009DB5D90805458CF07423C (void);
extern void X509EnhancedKeyUsageExtension_ToString_m9576793CE590716A3BB5AAC1632F946DF75605DE (void);
extern void X509Extension__ctor_mFC88C10C2C6718E6AE74CE37E10D4EBB8FD67BAC (void);
extern void X509Extension_get_Critical_mEF827F09D95784210C2F8D5FD7BE27BB54803E35 (void);
extern void X509Extension_set_Critical_m19FBE6E0C8B9069E5A2C232DAAE06046AF18EFEF (void);
extern void X509Extension_CopyFrom_m674ABC4ECF701B708C28B546B42B7799CA24D4A9 (void);
extern void X509Extension_FormatUnkownData_mD0463F5096A6937E95E3165048299CBA9A1A6F7D (void);
extern void X509KeyUsageExtension__ctor_mE0EE68497EDEDD78213EEDC931D68DD35DF6340C (void);
extern void X509KeyUsageExtension__ctor_m1DE9CD768712A07E5803496501CCFDA81CFB2689 (void);
extern void X509KeyUsageExtension__ctor_m2FAA526721435935D0B10E0DB916AF56CFD84751 (void);
extern void X509KeyUsageExtension_get_KeyUsages_m1BD6C5BC6E3E380982C135B2E81731435345144F (void);
extern void X509KeyUsageExtension_CopyFrom_mCBCF5CA17676B2030F6C82ED80A008EF029AF733 (void);
extern void X509KeyUsageExtension_GetValidFlags_m82B1C7DA4F4E0F16090782189E1B4984D133BB45 (void);
extern void X509KeyUsageExtension_Decode_mB64239ABDEA862E43E26CF324D3E41ECCF3088BC (void);
extern void X509KeyUsageExtension_Encode_m38D20B2FB68EDF3594EDC0D9BA88800ABF305542 (void);
extern void X509KeyUsageExtension_ToString_m270131139984275D7571120A2984F0F5E77E79DC (void);
extern void X509SubjectKeyIdentifierExtension__ctor_mFC2A19B1301ABBA5F6C5CE407ABC4F4CA1EB49B1 (void);
extern void X509SubjectKeyIdentifierExtension__ctor_m421293DCB67F3A918098962166B8166FD9657D08 (void);
extern void X509SubjectKeyIdentifierExtension__ctor_m610C9C804421B7CB519F3A5C7D66827CC1106EE1 (void);
extern void X509SubjectKeyIdentifierExtension__ctor_mECF6398B9841FB4533228FE77F51F93316EADE94 (void);
extern void X509SubjectKeyIdentifierExtension__ctor_m3DD2C4E1505A6E625499FBD833BFC076D28D83EB (void);
extern void X509SubjectKeyIdentifierExtension__ctor_m1858C1B050FAF65946B790737652E874781B4B56 (void);
extern void X509SubjectKeyIdentifierExtension_get_SubjectKeyIdentifier_mFF2A155072C67F430E7F4B178733A80388943CBA (void);
extern void X509SubjectKeyIdentifierExtension_CopyFrom_m959BB16C6973B1B565D02ED9993AF9069019D77A (void);
extern void X509SubjectKeyIdentifierExtension_FromHexChar_m6A90ECC8622DD1D2DEA0FB18356E97B15E8371A9 (void);
extern void X509SubjectKeyIdentifierExtension_FromHexChars_mFBC6B2009778633420F9F4FF38D7E641FBF63EFA (void);
extern void X509SubjectKeyIdentifierExtension_FromHex_m0C965FECB6AC9330589DBDC182E7EA34EFE4195D (void);
extern void X509SubjectKeyIdentifierExtension_Decode_m9E9B87F478F55E202411CBCE3AA314CBF73C7F30 (void);
extern void X509SubjectKeyIdentifierExtension_Encode_m8746D45CE4AC46543D3332BC6001FA1BC7A72CB3 (void);
extern void X509SubjectKeyIdentifierExtension_ToString_m9AE51334149DE6200E554F9C30E2DC5570D36707 (void);
extern void EditorBrowsableAttribute__ctor_mE6105AD9666A4DF03DB2590C687EAC6B12D908CE (void);
extern void EditorBrowsableAttribute_Equals_m2DFA6ADDE69D95657A8257840C01FCAF74A6B02F (void);
extern void EditorBrowsableAttribute_GetHashCode_m1AE50FA45B338D71189AA3C249DB56406DEC798C (void);
extern void DisplayNameAttribute__ctor_mE16A1FD5C8425C7F00EC5728A1227AF6FBEFB968 (void);
extern void DisplayNameAttribute__ctor_m396F07353E80428B4AEB85DC66F5B9380A077AE3 (void);
extern void DisplayNameAttribute_get_DisplayName_mA3E91855E1EAC0085BF2C1EB501B7801B9563D5D (void);
extern void DisplayNameAttribute_get_DisplayNameValue_mD9F58F5FFF2B7C682D8470101352BEA88AE0E5AC (void);
extern void DisplayNameAttribute_set_DisplayNameValue_m2DA6A8AA72F3969BA5639F707AB90A2D363BC49C (void);
extern void DisplayNameAttribute_Equals_m06E1BC5B48A9D42FECF4BEC20338F8DD6A525E88 (void);
extern void DisplayNameAttribute_GetHashCode_m882BB47C783C5D55149DB892F41108D4953B8B16 (void);
extern void DisplayNameAttribute__cctor_mD9A9030A1931AD6CDF0428CFEC3E34B91652A8F4 (void);
extern void InvalidEnumArgumentException__ctor_m30DFADC9DAE3067EBFB7AF17F9EE5DBAF5B00D10 (void);
extern void InvalidEnumArgumentException__ctor_mB48FBD0C68C97EF69D80B56EB927CAF08E3181E1 (void);
extern void InvalidEnumArgumentException__ctor_mB6DA9F3F2403BFA3AAD577AA4A27034D29A60EB4 (void);
extern void InvalidEnumArgumentException__ctor_m05AB9EBB8AEDE6DF1BFE1E8E7F2BA4EB303DAA13 (void);
extern void ArrayConverter__ctor_mAA6B57C16F167FF104B8E23402DE87ACA5E13A2B (void);
extern void BaseNumberConverter__ctor_m697E7E5DB315CE6CDA318F256A634BAEF3C7C76F (void);
extern void BooleanConverter__ctor_m50990A1CB60D3557199985EF164FF1E8AFCADC87 (void);
extern void DecimalConverter__ctor_mC1E815EF17DE8DBD91FB472C83A4F38514704B46 (void);
extern void DesignTimeVisibleAttribute__ctor_m89415646271B6901AF745CDD2BF3A2244DFCD946 (void);
extern void DesignTimeVisibleAttribute_get_Visible_m0EB229B7592D784080EEFA2C7100DF58ADFF064F (void);
extern void DesignTimeVisibleAttribute_Equals_m59ADCA70D1F8363BAACDC37274852A8E6C9A954A (void);
extern void DesignTimeVisibleAttribute_GetHashCode_m4E2FF875B5BD47A6C7EDCB5F9CB66D6860F5132B (void);
extern void DesignTimeVisibleAttribute__cctor_m60EE97AF27C65BA7BD5BC1FC200B7B261BD3F084 (void);
extern void DoubleConverter__ctor_m3CA5201BA68AB510BB2112A6755FCFEAB6DE53BA (void);
extern void Int16Converter__ctor_m3803A7772B21F49200FC142C193A40DC286C9DFF (void);
extern void Int32Converter__ctor_m88220DA12F6ED6FE4D1232B73C20DECE9EFC63FF (void);
extern void Int64Converter__ctor_m3C124DB065F27F6BEAB7ABAF4B3D13E6A9F3E94C (void);
extern void SingleConverter__ctor_m463B63EB8D945D2D43E5B8099D8F0EC24AEBDA7A (void);
extern void StringConverter__ctor_m074D8686ED6ABC0E64C20D1EFD4406F215E7187A (void);
extern void TimeSpanConverter__ctor_m7319DCEEDA2578B340AD4FEEE010AE5CEFAC9BD5 (void);
extern void TypeConverterAttribute__ctor_mEEE3CA7C17555BA93BB76201BA2BCF879E617DA9 (void);
extern void TypeConverterAttribute__ctor_m0E1A55E1BD368F0AABCAF766BBCE1D275777A52A (void);
extern void TypeConverterAttribute_get_ConverterTypeName_mBBE5335F11FC13345D2828866172BB5DDF9F3E45 (void);
extern void TypeConverterAttribute_Equals_mA3BF2C8CFA0AE8B5D0F52E066B1141AA53B4E015 (void);
extern void TypeConverterAttribute_GetHashCode_m23ACA2A297447E17150BF490188919676FBD8A01 (void);
extern void TypeConverterAttribute__cctor_m7A206154E18C66B9CEA6934D8AFB1AE9DA4EA666 (void);
extern void CollectionConverter__ctor_m20ED0C661750EBE427D356F24AED92A407988147 (void);
extern void EnumConverter__ctor_m3E3C7D81C8092A5591BAA556288B2A3E22DCA99B (void);
extern void TypeConverter__ctor_mA5B1882A94D0491297B903563E8B03D75B2F67A2 (void);
extern void IPAddress_get_IsIPv4_m912994B89DDE21F85E4FE40381CDFF1688BE3F38 (void);
extern void IPAddress_get_IsIPv6_m957F80A11C7E40C2CCAE554F12AA547E387B6884 (void);
extern void IPAddress_get_PrivateAddress_mE1CD072F1E7BDF981FD71CBD984F5FD2A2EC6399 (void);
extern void IPAddress_set_PrivateAddress_m73BD8A117538CBDB80F8FF47D31609FA4855D74E (void);
extern void IPAddress_get_PrivateScopeId_m3C01EDB8971607BAC0398557E295B5A818191532 (void);
extern void IPAddress_set_PrivateScopeId_m2BA43F20C3B975BDAC6E330C09E09BFD34594A42 (void);
extern void IPAddress__ctor_mC240D2060BA0F5A96D286CDE3772FE4354D7030B (void);
extern void IPAddress__ctor_m8D2578858CF6F8E56591F45C8A14150DEFFA44F5 (void);
extern void IPAddress__ctor_mFD3C42E1EECC3099D6E347238FB2A0EBE822AF19 (void);
extern void IPAddress_get_AddressFamily_m1CE4BCCE499BD70B22F9E37B3F266F9306A98C21 (void);
extern void IPAddress_ToString_m91FE727877BEAA0115F497387E2E9CB0B45C3895 (void);
extern void IPAddress_Equals_mC4514B3B7F9B95B79A1121706C115AECE61636C7 (void);
extern void IPAddress_Equals_m5EA56A0CBC4F66012C11628D8CC75E03E5A8C462 (void);
extern void IPAddress_GetHashCode_m243850627E81961E58E3A7A0A39A1F365C47A4BA (void);
extern void IPAddress_ThrowAddressNullException_m2D5037C15BE289427E4A761A643C4F26C2F3F034 (void);
extern void IPAddress__cctor_m83F9B947B6C0D0F8DEA3E0C62FCCC2573495D6CA (void);
extern void ReadOnlyIPAddress__ctor_m0E47AC9070535FCCC4796C165AC8DAB6FA2B5AC5 (void);
extern void IPAddressParser_IPv4AddressToString_m3D56686708BDA87E76737D94441BF1AE486922D2 (void);
extern void IPAddressParser_IPv4AddressToString_m4113E0673C882A7CB1313639E2836FB24B9BC6A0 (void);
extern void IPAddressParser_IPv4AddressToStringHelper_m4226589AD61857FE45C082110271FFF2D3DF8C91 (void);
extern void IPAddressParser_IPv6AddressToString_m7C25DEA2FB9E3F863777653CB9C314BBEDF911A6 (void);
extern void IPAddressParser_IPv6AddressToStringHelper_m405EE1B288D87500878DD96ADAEE31B106CCF930 (void);
extern void IPAddressParser_FormatIPv4AddressNumber_m716041E973744C431B090589D624C53696FD801F (void);
extern void IPAddressParser_AppendSections_m141C016465F9E7E1538E03340FEC16F89B500284 (void);
extern void IPAddressParser_AppendHex_m877259AD615A68FDC55EDC188758E5AA95C124B2 (void);
extern void IPAddressParser_ExtractIPv4Address_mB79D4899358C69C1FC7C44C81A26BBD976FC2CB6 (void);
extern void IPAddressParser_Reverse_mDEBC20FC5D00F7C505E1ADE428CB6967B72B425E (void);
extern void U3CPrivateImplementationDetailsU3E_ComputeStringHash_m171C269D828658C44041FA68B6DE8CA290ED517F (void);
extern void BypassElementCollection__ctor_m344EC8CD2EACA36E1213C5914037BB133E87FE54 (void);
extern void ConnectionManagementElementCollection__ctor_m4698342A715032A762FA04B2745BFCA46263BCB3 (void);
extern void ConnectionManagementSection__ctor_m2FA1D29F887BD6498FBEF692BBC0AA727E38E802 (void);
extern void ConnectionManagementSection_get_Properties_mFCE0F9418E9E8CD2E1301815CC4D548261F58E95 (void);
extern void DefaultProxySection__ctor_m962B68CEFCDE7DFB7646EF16297CA4BE0097284D (void);
extern void DefaultProxySection_get_Properties_m5186F1A2734EB96F065C7BC14A215D17F8325418 (void);
extern void DefaultProxySection_Reset_m0B9A1155840E7C55FC24AEDB23FB17A2815EEB77 (void);
extern void ProxyElement__ctor_mC1BDF426F97F4F84CB5A34B423F59EC8748EBD70 (void);
extern void ProxyElement_get_Properties_mBFE6E283FE7E9329B586ACAADC8F2E047C7ABB2A (void);
extern void HttpWebRequestElement__ctor_mB9DDAC7FF431D4AF4CAFCBC96E1A3572A1F83717 (void);
extern void HttpWebRequestElement_get_Properties_mAA0F11EA73B5397C50809EAC679F2C564F9EC7CA (void);
extern void Ipv6Element__ctor_m869AC261E0994A8B3D83CEE25C6CB99E6A5EA4C1 (void);
extern void Ipv6Element_get_Properties_m382D5B5B098EFF19C2A4AC1889CD1C8EAB918322 (void);
extern void NetSectionGroup__ctor_mCD2F0DC2B9F0C6C346BC91BD8750C7111A9528AC (void);
extern void SettingsSection__ctor_mBD94E7E098221DC09775A3AE1713930F6820D242 (void);
extern void SettingsSection_get_Properties_mA774C9C884F4AD5DEC2BBE31DF7E35CAC71EBF8F (void);
extern void PerformanceCountersElement__ctor_m9537195CBC7DC607F7253D06E7B6B1090456E68F (void);
extern void PerformanceCountersElement_get_Properties_m7C16FBC3E915E8384FB4409209628737B1537C9B (void);
extern void ServicePointManagerElement__ctor_m0F1CF21589CA283DA734A472930A9665698B2D36 (void);
extern void ServicePointManagerElement_get_Properties_m70469523AAD1A2C2E9D4F90DDA1BA619FCE68B58 (void);
extern void SocketElement__ctor_m4FB4C851F1BB20CED74C206EFB1BA9DDFBE024D2 (void);
extern void SocketElement_get_Properties_m8AF94DEEDA0EB4092F59FBDC361592E1B9EBDEC3 (void);
extern void WebProxyScriptElement__ctor_mB613D71DE569317E40726CD8DB9A6ABA0A302821 (void);
extern void WebProxyScriptElement_get_Properties_mF5188225C0C1D64BC136465BB4B0E962FFE9240B (void);
extern void WebRequestModulesSection__ctor_mF25CD80A5111A4F87B7FBF7663A3442470B97C0E (void);
extern void WebRequestModulesSection_get_Properties_mBDBDED569E677717FB617CD36B94AE34BF4208F6 (void);
extern void WebRequestModuleElementCollection__ctor_m62C156D78336B2E37EA6F10D477FDEAE4161F99E (void);
extern void DiagnosticsConfigurationHandler__ctor_mDFB0000E6218840F66D21BB6C9BF3F647CCBC5A3 (void);
extern void DiagnosticsConfigurationHandler_Create_mF21B1CC68B87DE078690B2B493FCDC5F728CBAAB (void);
extern void ThrowStub_ThrowNotSupportedException_m14168B43936EF78B7B8AC08BD815254DAA1D9FA2 (void);
static Il2CppMethodPointer s_methodPointers[772] = 
{
	OidLookup_ToOid_m084AE2948BA1D784EAC148B97B6D5EF94F0D58B0,
	OidLookup_ShouldUseCache_mAF605BA00A22EA400B99D24286837F6904C84ED0,
	OidLookup_NativeFriendlyNameToOid_m383F983E8333B3DAB97C9278B3B0EDF09E61758D,
	OidLookup__cctor_mBF99F75584EA62215139C76A1BBF57BCEAD2ADBC,
	U3CU3Ec__cctor_m63CA89B172417DB40229C01C86A24DAAD5EC9963,
	U3CU3Ec__ctor_m381C6259C7B5579501CBFB1E4D1DF201D5928470,
	U3CU3Ec_U3C_cctorU3Eb__10_0_m6CCA3946EAC08544EB57D764DBF2D23E12DE30A1,
	U3CU3Ec_U3C_cctorU3Eb__10_1_m7F86444F2B083758A76759BD54D31A3538101A4E,
	IPv4AddressHelper_ParseHostNumber_m3ECD734470D95F75F0A7475BB69A53D48A924179,
	IPv4AddressHelper_IsValid_mB71D85892F391896358B6C19C6E4899ED31EFCF8,
	IPv4AddressHelper_ParseCanonical_m38827B1F002AC415916FF17D5C7DA3279486A91C,
	IPv4AddressHelper_IsValidCanonical_m157BF3179C5A511A8915C572111A7014D7E5DB84,
	IPv4AddressHelper_ParseNonCanonical_m8A508ED61556F4182AAEBE3E7D43C738A57C984D,
	IPv4AddressHelper_ParseCanonicalName_mF74A625E0C85FC8C5CAE3A47785606BF93777FF1,
	IPv4AddressHelper_Parse_m0F43F114BC6D0C8D85D29D3CA2CE24CA19714026,
	IPv6AddressHelper_FindCompressionRange_m5BA0C004FA7BE7CB3C520AB47C4D6B6FA6C13C56,
	IPv6AddressHelper_ShouldHaveIpv4Embedded_m08BEFD0452A0145779E70931B00F2D6EA15A1E70,
	IPv6AddressHelper_Parse_m6F68528ADBA8B6ECED3060135FC1DF6CCD2B0543,
	IPv6AddressHelper_ParseCanonicalName_m5C5161CE7D6F7D7C7D81A808F51107C85A6CA21B,
	IPv6AddressHelper_IsLoopback_mAE6B2C6CFAD787337C6ABB41F2D2942A4C191C84,
	IPv6AddressHelper_InternalIsValid_mDF9ADFBBDDD76F8B77EC140686B6280F4BD53478,
	IPv6AddressHelper_IsValid_mDEE6D566114425592CA0A067775A2E0C771CA5DA,
	NotImplemented_ByDesignWithMessage_m9A8A83C6B994C16CA1CFA80F5AD748ABA3510535,
	IriHelper_CheckIriUnicodeRange_m584D1B233AB9DB3642640CDBD787B0E51D2AC625,
	IriHelper_CheckIriUnicodeRange_m0989646614B5D6CD18622388A069BEA8A755F013,
	IriHelper_CheckIsReserved_m0F5942330B329B65D738A398854399FD43A5589C,
	IriHelper_EscapeUnescapeIri_m311CD9679869740C6A303485944EF6DAE96FB169,
	Uri_get_IsImplicitFile_m8D1A522158E4EB8AD8C077376D023ED1262998F6,
	Uri_get_IsUncOrDosPath_m33F2D8808AB44816518C1C464152D4B7872374F0,
	Uri_get_IsDosPath_m16357B61F5D111C45EF7D40276B96429E414F0D2,
	Uri_get_IsUncPath_m2D9E7E2DB4EA57EF268B9BA167A1389E18AE9835,
	Uri_get_HostType_mA30DC51045EE5FE94B3CB84B0D361A9C28EE572E,
	Uri_get_Syntax_mCBAC18B1DC71D269BF6C98417D923674DE258DC2,
	Uri_get_IsNotAbsoluteUri_m374185EB37A209B6102F955427913168C5543028,
	Uri_IriParsingStatic_mF071497F10CC1CC81177D6F4BCE9D2007D8FDA16,
	Uri_get_AllowIdn_m612B747CE2F03FCA867D81F14710758B561209E7,
	Uri_AllowIdnStatic_m0B61B2A586F2BF870D185A1BD65151F52EFBC457,
	Uri_IsIntranet_m3AC6DBC7C35F309DFAD0951ABC7D95A8000548EE,
	Uri_get_UserDrivenParsing_mF2426529046B3DF3292A7AA2F4885F8D6591BBBD,
	Uri_SetUserDrivenParsing_mDE80D352D6343F50F152EB65EF142E84F7F79D69,
	Uri_get_SecuredPathIndex_m575B7B28B17F8F7EE3FA563ACF6390B0926732AD,
	Uri_NotAny_mB88A38A602D4A784CADBD43FDF4CDA0235371E81,
	Uri_InFact_mEEE8ADE0780C0712A3DEB8935BD6FC7B23F38A7B,
	Uri_StaticNotAny_mDC2D7341AE3679E5750481E7B793F4E3636ACD6C,
	Uri_StaticInFact_m8F3964455197F6C2CB588843D71DDAD3B2F2AB77,
	Uri_EnsureUriInfo_m6FE387306AF82CDCFC56E2E9C2171C406D1FA982,
	Uri_EnsureParseRemaining_mDF7D4313A56047EB22457E12330C138156D54783,
	Uri_EnsureHostString_m7F5F3ED8488F83170A857CD51F0CFEB8322AA055,
	Uri__ctor_m6CA436E6AD2768A121FA851CBEEFA3623E849D3A,
	Uri__ctor_m24549041BC5661EAC10BA8CB35B60AD6512AF69B,
	Uri__ctor_m5AA91BCDE21E8AD62B9A48948CF3661B2F00E38F,
	Uri_GetCombinedString_m7D6A3DEB5DB42F79271CEC67DA2AEAD0E05BCEEF,
	Uri_GetException_m80C3194C72CA8F86F95A6DFC8D5C29BE31D749A8,
	Uri__ctor_mE1AB9FB0563073B8DFB381A69A54E9E0459D583F,
	Uri_System_Runtime_Serialization_ISerializable_GetObjectData_m39D9B260E68B8715E9B693A125A2116CE14D760D,
	Uri_GetObjectData_m0D16C2BC060F0B88503DCC0EEACB151D43FA16CA,
	Uri_get_AbsoluteUri_m080934F4F2E2160EBEABDF00F8B6D59888EA63AE,
	Uri_get_IsUnc_mB190B452D6A7B09FC8BDD967A5C38F32E87E0FCA,
	Uri_StaticIsFile_m8BD582D32BB053DCB5BADA29C1DD807AACCCE7E1,
	Uri_get_InitializeLock_m29D252F7183FE4C205E618018E8C42B56BC2B091,
	Uri_InitializeUriConfig_mCD4E00EDCA27459C7B005DDFFAD9D54396BF7DCB,
	Uri_get_Port_m7A1413AC9D9A2FC5DC8A7F89DF4A01ACA0241A02,
	Uri_get_Scheme_m29106D5109538220B22FC49DE7B44040E51B0F6F,
	Uri_get_OriginalStringSwitched_mBE7152D5DA7A1D922B728B9EA8C64815B0469FB7,
	Uri_get_OriginalString_m3031F9054CA10F2C55C0E2415CC19810D360A5D6,
	Uri_get_DnsSafeHost_mE5001E7AE7F1879A2059024EA06C9031F6320A2B,
	Uri_get_IsAbsoluteUri_m2F9B759B85D295639D7959A616E1FBC203D756DF,
	Uri_get_UserEscaped_m5A376B8E2DDB815CAA2E34AF8890FBA943DC2085,
	Uri_IsGenDelim_mED487423251FBC7A3AA85A70023D531DED88B0D3,
	Uri_IsHexDigit_m89270CD137F8D5AAC9953CB367BF194D0D0EEDBC,
	Uri_FromHex_m30578BD6ADBA0FE875257A8296ED979DC7849020,
	Uri_GetHashCode_m41959A208E51019C57C20014B2505993FB94B70C,
	Uri_ToString_mDD98375BF8CEBEF6E3708BBCA6C4AB0B54165DA2,
	Uri_op_Inequality_m64BE981CDF88B751677B80D883B201BF3CFD83F7,
	Uri_Equals_m1BD19791DD8A42FEFF5844C29623626C2849C452,
	Uri_CheckForColonInFirstPathSegment_m3A849DB35C397D05D788FF50CBA2B107CF729399,
	Uri_ParseScheme_m8E14D8CA20935DF4A14DC0C7DFE2330C734F1AD6,
	Uri_ParseMinimal_m24D824EC77216C64F4B8B9175915CD6C3C6DB7B5,
	Uri_PrivateParseMinimal_m4D4C3CB26AC0DE8B0E1F7DC4143F2004D9D25B17,
	Uri_PrivateParseMinimalIri_mD8C7CA7EE14D994E915B48FB6A2CDE62253D4F1E,
	Uri_CreateUriInfo_m3B4F804AB6CB010246F2A52F123D6E44DECDE998,
	Uri_CreateHostString_mBBAF0ACD4E2EE673F8F7F4E416D87517D94245CF,
	Uri_CreateHostStringHelper_m196C6DAB9F26F08459220AC87C0A74785A61E9C3,
	Uri_GetHostViaCustomSyntax_m6632DAD1F66901E82C60A47D626A9140A586C802,
	Uri_GetParts_m432C93EAC8684C7410585D36DA00B04F6DC34357,
	Uri_GetEscapedParts_m0C33275AFEA210E5CAD22649E8B61B49FB43971E,
	Uri_GetUnescapedParts_m57F83B75B05C5D8EAB6B8ACCB6C8E4F0922CB7ED,
	Uri_ReCreateParts_mB622FFDF74676902D184FD96C4E6B740B65140AF,
	Uri_GetUriPartsFromUserString_m3D6EEB76609CCC54A0ABE65911CC24603A184B29,
	Uri_ParseRemaining_m9134D26360C9EEDFAFAFEE1ECA1A5CEE66BE5A3A,
	Uri_ParseSchemeCheckImplicitFile_m3577C77315714DF277763F7DE3BAAD8D94D2CA93,
	Uri_CheckKnownSchemes_m598B70C53F57AB20E2406763BAA5636A3B57195A,
	Uri_CheckSchemeSyntax_m37B81B236F58066982C17FF76058B3ECB0740647,
	Uri_CheckAuthorityHelper_m7946931160B7EC50E7167A11E570A4AF8AEB5B2E,
	Uri_CheckAuthorityHelperHandleDnsIri_mB737EC35D3A477668E6E289D09DFB43EAD4D4D62,
	Uri_CheckAuthorityHelperHandleAnyHostIri_mE38845CEFA41AB52AB949CAE2A43D77FB397C08D,
	Uri_FindEndOfComponent_m59CB1EFDA410B42014EA7DCDBA0C9FC9F5996E02,
	Uri_FindEndOfComponent_m198F7AC80BEA08E8DAA6B4577D8A872B41C519B1,
	Uri_CheckCanonical_m54AFA85AA00A9909C2EC01F026873F12532AD7F7,
	Uri_GetCanonicalPath_m66FA22F6E85D79E360A7EE86C0E03F1A009F6F99,
	Uri_UnescapeOnly_mBED4CBBC30D19BA6BA801BCF9B33CD681771102B,
	Uri_Compress_m22981FC433505DEE0F230312B93B062096607CCD,
	Uri_CalculateCaseInsensitiveHashCode_m635E27F73B39946637685A19CD09741D14A359EE,
	Uri_CombineUri_m92FB418ECA7D55262958241C8A274715C58C6424,
	Uri_IsLWS_mE00B6D5A24D07A5EA80278F981F1516343C0846D,
	Uri_IsAsciiLetter_m54D66D3D390C364A49529D304526152E2339A140,
	Uri_IsAsciiLetterOrDigit_m9033B2053D679076DEADF04748CE5E983B25A9E0,
	Uri_IsBidiControlCharacter_m37BFE363354928FB34D016950008D1FB29DFBB0F,
	Uri_StripBidiControlCharacter_mB8C52362A2A248EB33737D1A4792B3FB57975912,
	Uri_CreateThis_m522F36D576D9A8803410439127DB32837331BA3A,
	Uri_InitializeUri_m044EA7C6188B132874B4522D5CA41EEFDB9A1718,
	Uri_CheckForConfigLoad_mC7297EBC00884B1793FDB16F79BF0FCFD53E9FA7,
	Uri_CheckForUnicode_mC763EF0DDC61EE359A6256B95209736EC0353AA1,
	Uri_CheckForEscapedUnreserved_mCEF702F733C72BE825E9FEBBEA751540A6A78D11,
	Uri_TryCreate_m183077985BD7B1BE8DBDBCC4604F775C44723EC9,
	Uri_GetComponents_m30E3EAD4E9A39939456E764FEF94AFD4D6B7532A,
	Uri_IsWellFormedOriginalString_mD71C404996C187FB7CB38474B7E4DC4DD760A9DF,
	Uri_IsWellFormedUriString_m35D88312036B391850B73BD0F09779D9D47ECCE8,
	Uri_InternalIsWellFormedOriginalString_m273089E57528670A95D845618A5C3CCD028A3098,
	Uri_UnescapeDataString_m095AF55EE0F237A44FC8A53D2EE6FA66A567634D,
	Uri_EscapeUnescapeIri_m14C0CD1A4C14E2458685E5A10049204B1A15C16C,
	Uri__ctor_m6EA3AF49C103A3D56EBBB24BCAEE8DE7E8676AF4,
	Uri_CreateHelper_mB015D0E87C2F9401D7C9B929C194AA5E432DA224,
	Uri_ResolveHelper_mCF316B7B35401CFE251F64CCE66AF24F687E7E04,
	Uri_GetRelativeSerializationString_mDEB0AD1094EC2842C2F0830C1E43FF2FFDEB6F5B,
	Uri_GetComponentsHelper_m83D8E4A027E180AFB34BA5290A8FF381EBCBCB75,
	Uri_CreateThisFromUri_m986BB8F1CF8D75EEFA12D9D0963BA927FA394BE5,
	Uri__cctor_mF9A6F75B43C4652D9B89180A998B3D86B61F5594,
	UriInfo__ctor_m88A6E3E7164B78B18C70D83CA5AD734517D308B3,
	MoreInfo__ctor_m4AC1286A4D4DAD0AB0EE99A2C952D54BFA5F44A7,
	UriFormatException__ctor_m4D218C92C093CD0CDDDC012911A1E10CB327BB70,
	UriFormatException__ctor_m718A5DEB020FAAD1F654CD05E63C3988AA7712D3,
	UriFormatException__ctor_m15F8C1205D9A648F72DD5D94DB92FD1FA1397FE0,
	UriFormatException_System_Runtime_Serialization_ISerializable_GetObjectData_mF5C6CA057915A5F7F4E9D8FBE5D6EB2595A4FD80,
	UriHelper_EscapeString_mA9075A39AFEE45E310AA6E2977B46D74F2FE38D3,
	UriHelper_EnsureDestinationSize_m0A91CAD854571386F0490A975BA25CEEA93614C0,
	UriHelper_UnescapeString_m37D24870CC3AF00984435A567B7AB28E7225D7B3,
	UriHelper_UnescapeString_mAB62B336BC58C7697B4CA7355405F3C1FDBBB7E0,
	UriHelper_MatchUTF8Sequence_mD118AEEE32C6BCBA4ED2E41E2B008EB83412D54B,
	UriHelper_EscapeAsciiChar_mFA21BF7A2306446A8E552111DA06160A41CD07D1,
	UriHelper_EscapedAscii_m93D49C2D42E1E5E292199FACF5B4D6CC770D3DA1,
	UriHelper_IsNotSafeForUnescape_m79864E996F8F46D4A1C00B965E2EB4EE7AFE63ED,
	UriHelper_IsReservedUnreservedOrHash_m7B622D953837E4F0FEF44B114718B75B846512A1,
	UriHelper_IsUnreserved_mED7CF4BB5F43C337685396D5F99D7EFABAD5E5C9,
	UriHelper_Is3986Unreserved_mCDBDBB83B68181182A6AD07747084B3204C7BDCA,
	UriHelper__cctor_mB25AC23398D53149F4C33FB739BC6010324D0D81,
	UriParser_get_SchemeName_m3BD969B4E8EEA3769D070FA42CE340CDC766F47D,
	UriParser_get_DefaultPort_m38684C86B40F59960533D8F812B311ACBBEB7AA5,
	UriParser_OnNewUri_m752BC0174A2E3F7AA99F1088E1D539E5B8F7B5B2,
	UriParser_InitializeAndValidate_m5B38A10B57DC49B1CE13F361ABD78D7269EDA886,
	UriParser_Resolve_m7CC03524A2848FBD24BAB57CC62A19C751937182,
	UriParser_GetComponents_m230F3341901B4B90DC4650093272BC15A1161FAF,
	UriParser_IsWellFormedOriginalString_m8C48DF0E4CF7684AE739F1CCA139CB09D9758839,
	UriParser_get_ShouldUseLegacyV2Quirks_m8EC9AB4BB2AD4FAAC4CD7C921C7A61AA4FC914D2,
	UriParser__cctor_m0F0B1F5441AF97DDBE797061C6CABA2CD4CDBE69,
	UriParser_get_Flags_mBE183D7CBEEEBEC670CCF1DF21D56B354796D6C3,
	UriParser_NotAny_mCE6CF6736414BE547215F6E44C3E1283DEA05F56,
	UriParser_InFact_m3C07C3050DE1E69F6B0BC816EC34AF610373E532,
	UriParser_IsAllSet_mEE811F1E7BBCC34D7A32BC30A18AD5D34E690813,
	UriParser_IsFullMatch_mFC1C9EEDD31F040997931B26EF54CE7988495550,
	UriParser__ctor_m4FA54685DFA00FC6AEA73E94CD59C5FF2872E55A,
	UriParser_FindOrFetchAsUnknownV1Syntax_m780CA32D27A4F1D8B7798FC4D3CC28A6FC4ACCE0,
	UriParser_get_IsSimple_m8ED850D585BA3BA0FDC239496065E6D2D973C7E5,
	UriParser_InternalOnNewUri_m93B84E04949FAE8FE3B6919DA9F933B682D4B2F8,
	UriParser_InternalValidate_m69157B3E80381F3FC104788160F2213B3FE8F39D,
	UriParser_InternalResolve_mD1963080EBCF493DA78656AA5AEA458BF5138F5B,
	UriParser_InternalGetComponents_m52F450C5283A1820D5AFD07505E5FE82D4F476B0,
	UriParser_InternalIsWellFormedOriginalString_m8721FCB51EB53C8B322E0A8909B181D6CF0D108F,
	BuiltInUriParser__ctor_m6E9775075E7360DD524B7E3E456A3FFA58F89E79,
	DomainNameHelper_ParseCanonicalName_mCDD58AA526B15176E4468ABA169068B279F3817F,
	DomainNameHelper_IsValid_mD977C4425926C44931F21A478C861D1041AF5B67,
	DomainNameHelper_IsValidByIri_m94490F7D0A09B0C35C89E2CD478B935EECEC5F96,
	DomainNameHelper_IdnEquivalent_m797D2222F0F6786FF059095B73ACF47A3374E8DC,
	DomainNameHelper_IdnEquivalent_mEFD473FCBC1D7F515577673838569560777F5D2B,
	DomainNameHelper_IsIdnAce_m95A0515B49C9679EE331F830D05C2C12853919BC,
	DomainNameHelper_IsIdnAce_mC02E0A64A83B0C99CF26AD5D49AA541E33143947,
	DomainNameHelper_UnicodeEquivalent_m390DFCD943A3716158026F5E1AF6558E56461F12,
	DomainNameHelper_UnicodeEquivalent_m9C7E79F0172D7AC0DEF7FC5236F1B9766C735517,
	DomainNameHelper_IsASCIILetterOrDigit_m5C30C4CDEF0ECE647B2C5E714A595B08A8009B3A,
	DomainNameHelper_IsValidDomainLabelCharacter_m9BBE29F6DEACCA6864574A1B6C8BCBE6732BF5CF,
	UncNameHelper_ParseCanonicalName_m938F53305ABCF1840011969B589F0D53A1028C79,
	UncNameHelper_IsValid_mCF30A9D7038A73997B8B198D7EB606B171E00526,
	IOAsyncCallback__ctor_m58A7C732444005CB72C5B352096C2DB252A6F113,
	IOAsyncCallback_Invoke_m70CC21061AD0965DBC87CB382AD395A35E1788B2,
	UriTypeConverter__ctor_m02CB01E1305031BE5A45FB54601E520748B6EDB6,
	UriTypeConverter_CanConvert_m970377ED30FF1B6875CCDA64FDC87EFE7D381DE3,
	Capture__ctor_mDC1197B1E29487F3D086091CF3D5B1029D593402,
	Capture_get_Index_m35E8D105E5E2142415F85116BFD4BA369AF7A17A,
	Capture_set_Index_mD5E578A0833A03602FBDAA2ABF77C9CC849CDABE,
	Capture_get_Length_mA3E97C9DF116CD4B4C20EBFB70A1312F33DA7A92,
	Capture_set_Length_mF9069396819D5D0282F38E70F347CDC625BE016E,
	Capture_get_Text_mDD394681E5FC39FFDCCD9552C0BF09DF8A7054ED,
	Capture_set_Text_m99AE1BB9AF9D7744D0E6F747DCF5A378591C353C,
	Capture_get_Value_m1AB4193C2FC4B0D08AA34FECF10D03876D848BDC,
	Capture_ToString_mC843EDDC990DBE2696BA08D08B1081CA12228F2C,
	Group__ctor_mFDE74CF068520CBE797B563B419B8817E60CB58C,
	Group_get_Success_m4E0238EE4B1E7F927E2AF13E2E5901BCA92BE62F,
	Group__cctor_mCEC94034CDEE90B4E5B77AE639965DA426C49161,
	Group__ctor_mEB6936CDFD191A4C92766CE3F3301161D7505C5E,
	GroupCollection_get_IsReadOnly_mF1DE2BBB4E573C6EDE85B787281F90951AAB8C4A,
	GroupCollection_get_Count_mE8775E53B4F5DA6D520BEF5ECEED2E0C08A26B39,
	GroupCollection_get_Item_m40EC174D4AC8FDD68F8819C35B779C79A44322F3,
	GroupCollection_GetEnumerator_m47E269C5A8C902948812AF4F52D0C96759550EE9,
	GroupCollection_System_Collections_Generic_IEnumerableU3CSystem_Text_RegularExpressions_GroupU3E_GetEnumerator_m593F2B44A71E46606D2E8C0816E626675EE2087D,
	GroupCollection_GetGroup_m8EDFDB8D85D8BEC6EB98B68CA69EE0022FC16B13,
	GroupCollection_GetGroupImpl_m27CE16949BE0A9CFC61CC6CBF3FB179F63654D92,
	GroupCollection_get_SyncRoot_m7505B29FFFE40E5319BAD3F3B3332F8A6299570A,
	GroupCollection_CopyTo_m122BEB82372AC34C575FCB8C71188A2A49CCC482,
	GroupCollection_CopyTo_mCA848040E42611E6A969C9E4F632FD81FC27B52A,
	GroupCollection_System_Collections_Generic_IListU3CSystem_Text_RegularExpressions_GroupU3E_IndexOf_m63AB69A8D37D49B1AFFE8CDEED6442D25A25F94A,
	GroupCollection_System_Collections_Generic_IListU3CSystem_Text_RegularExpressions_GroupU3E_Insert_m515A74F3095C972A28657B89FE6B65A3EC5324FA,
	GroupCollection_System_Collections_Generic_IListU3CSystem_Text_RegularExpressions_GroupU3E_RemoveAt_m981151BB2C68E7F2DC0BF4DD61D4AD4AB5B5AE3A,
	GroupCollection_System_Collections_Generic_IListU3CSystem_Text_RegularExpressions_GroupU3E_get_Item_m7FD6BE83AFD3DCC2F9ABB2AB4B938F552DF1D45D,
	GroupCollection_System_Collections_Generic_IListU3CSystem_Text_RegularExpressions_GroupU3E_set_Item_mB5DBB39B4E6F360B8105064E6659EFC49E8B1C85,
	GroupCollection_System_Collections_Generic_ICollectionU3CSystem_Text_RegularExpressions_GroupU3E_Add_m96D9B7FCEC33784ED88BAC3C291146E64A921D8D,
	GroupCollection_System_Collections_Generic_ICollectionU3CSystem_Text_RegularExpressions_GroupU3E_Clear_m67DAFAB787617D8A9B720AAF612436587E2C3172,
	GroupCollection_System_Collections_Generic_ICollectionU3CSystem_Text_RegularExpressions_GroupU3E_Contains_m260CC86A4FD94F5682B73C9C8BE03551C14B3568,
	GroupCollection_System_Collections_Generic_ICollectionU3CSystem_Text_RegularExpressions_GroupU3E_Remove_m008F559DB848823129DDCB7B832C731F0D4AFF83,
	GroupCollection_System_Collections_IList_Add_m08D9178D05AE66397CBCF2184448AE712741E4D1,
	GroupCollection_System_Collections_IList_Clear_mCFF90C2303D7106D722E8B574ED3261C80F250CE,
	GroupCollection_System_Collections_IList_Contains_m9E34B033D0BB903B2491336D7F70BE5A59D46208,
	GroupCollection_System_Collections_IList_IndexOf_mA974FC7CFC23310A1CAAD8A750823D5E0ED762AC,
	GroupCollection_System_Collections_IList_Insert_m3CDAAB43F292FD0ABD2D9595F02233FB55577623,
	GroupCollection_System_Collections_IList_get_IsFixedSize_m5AA2CBFB17E2617597BDFBB56CCA1E435A763014,
	GroupCollection_System_Collections_IList_Remove_m32982B763D442200E71B8A5E6C85C9501A555F23,
	GroupCollection_System_Collections_IList_RemoveAt_m32BF4A16212F4849AD46F680E5D0D14268CB7C58,
	GroupCollection_System_Collections_IList_get_Item_m969EA1758BE453F49B700FABF29940C5C9800573,
	GroupCollection_System_Collections_IList_set_Item_mDDFE5D350953DB61758FE64D556670EA5A402C48,
	GroupCollection__ctor_mB178F93E8DB52AA6412FF28E5FE50943E2BABFFB,
	Enumerator__ctor_m17B823D35315CED0509F6B4C5F04C148B9388DDD,
	Enumerator_MoveNext_m1DDDE3FC6B83FF15ED8AA895DE1357C37B7241C5,
	Enumerator_get_Current_m6201905A1315214B04F5001307F52887F9161F43,
	Enumerator_System_Collections_IEnumerator_get_Current_m16F94921184B09EDC3D45E9CFE3F3FAA1E5D862D,
	Enumerator_System_Collections_IEnumerator_Reset_m5B7067CCF9E8139F34641618492A2DAACA5419B0,
	Enumerator_System_IDisposable_Dispose_m1C77D8E3DFF1A034E9329E11854F11012F20AA69,
	Match__ctor_m2A6BD37E98680890114AC62CDD23E1821E80A53A,
	Match_get_Empty_m50E10899C5A892532ED484CDA11DE27CC6A7B546,
	Match_Reset_m53A78F16760B7CD5CC8A104F6CD95CA255D888AA,
	Match_AddMatch_m5F28F3255C64D67D56C1542CE32A9D324F96780E,
	Match_BalanceMatch_m85EA44453E637DEBE24ABC7562BF91EE1222DBFD,
	Match_RemoveMatch_m154DB10030027B3738903FFB50DE511D71656B04,
	Match_IsMatched_m9D6E0C0AB1F3E1E8E49E77E3A0A0ECCD7C2C4FBA,
	Match_MatchIndex_m97F06DECDDA8BD3E4D8DF6B9224B8E24C0764F35,
	Match_MatchLength_m5C93EA60B018A79D9D05C8E69D25835848471B01,
	Match_Tidy_mF0A46109EAF0E91CBCA732985265E1DC20CA3F51,
	Match__cctor_mB241D90D9F6B1311AC606248B3CCA821F8CC9855,
	Match__ctor_mBABEF8F11A3BC6DB713519819D135CC87AC1B4CD,
	MatchSparse__ctor_m7CEA8A7F538C706527778E8E75003DD74AC2FD41,
	ExclusiveReference_Get_mCC2B9161946C9532001438C65A38E8D8FD56FB3C,
	ExclusiveReference_Release_m7A911FBF52436AA832188BF89CC558155BE09018,
	ExclusiveReference__ctor_m41EB96FC1FA261CB792752B89F15E6513FEA1722,
	Regex_GetCachedCode_m2039E73548B61FE55B6C8D85C00076DBA50332E6,
	Regex_GetCachedCodeEntryInternal_m9FE175C47BF909F7F223013004C0EA0DAD660C06,
	Regex_FillCacheDictionary_mC47F317967E556D1F03A344B78B215356D604755,
	Regex_TryGetCacheValue_mA2B41F91C84CB02DA59E18DB80B0A49CBC600A8B,
	Regex_TryGetCacheValueSmall_mA62E6520886883BC787412F588CF79B7A02B9A28,
	Regex_LookupCachedAndPromote_m2FE5584043609F1616158AF8621FC3ED48BCD900,
	Regex_IsMatch_m7E96E666FBE7259D7638A3A6A21BE824D2406F49,
	Regex_IsMatch_mC358D80573E765ECC19F9F5882664E3B1FA0966E,
	Regex_Match_m58565ECF23ACCD2CA77D6F10A6A182B03CF0FF84,
	Regex_Match_mDF110993CAA26CD41AE9F5DB7F0C307B2680CED3,
	Regex__cctor_m689E73750FECC607BB4E1BF503660C16BE0F40E8,
	Regex_ValidateMatchTimeout_m530D1B5EBC69BEAA11CCB7053A96C4AA9869C591,
	Regex_InitDefaultMatchTimeout_m8EEBEF239D034DCF29E50E681B318B517202854E,
	Regex__ctor_m082970AA73B8236360F0CA651FA24A8D1EBF89CD,
	Regex__ctor_mE3996C71B04A4A6845745D01C93B1D27423D0621,
	Regex_System_Runtime_Serialization_ISerializable_GetObjectData_mD788EE38607BF5D618FDA920048707DF4CA4B99C,
	Regex__ctor_mA8EBF2F80CC00D741BFD2FFD7DC9DB26FBCEE2A4,
	Regex_get_RightToLeft_m52C4DEE1DC94FC9531785B74111022F3BCC1DF4B,
	Regex_ToString_m4982DE4C344F7D529666314646F071580A450D5B,
	Regex_GroupNameFromNumber_mC99A37419C4C0C9C3869BCDF055EDC72782D53FF,
	Regex_InitializeReferences_m55385AD102411A38BF4426C08F6FEC6E55E4D4D6,
	Regex_Run_m4DCD2202C599273C00D6052A77BDD2F39E2F5BF8,
	Regex_UseOptionR_mFAB8780E9F998BE60C4B1127D57DD627A3B5629C,
	Regex_UseOptionInvariant_mD51BCBA0BF4C1B5CEBE9423B4AAA93CD83569B60,
	CachedCodeEntryKey__ctor_mE505E40C97820B3FD140F6212F0B4ECF8745585F,
	CachedCodeEntryKey_Equals_m59F40708B97179EA5C9019EA6A958D7AB8BC8CA2,
	CachedCodeEntryKey_Equals_m55F9ED97CB9AF357034A75C2FDB8498FC55D3AF0,
	CachedCodeEntryKey_op_Equality_mE9E3CA5933F8AE24BCE5AD52D6C4EE014C085E40,
	CachedCodeEntryKey_GetHashCode_m253B53C100F840B987A487020F02184019D776DE,
	CachedCodeEntry__ctor_m14145ADFF67BA7C888673D806C24E28C96B83B2A,
	RegexBoyerMoore__ctor_mFDCB1AB420E718548B67DE42C233402955BBB189,
	RegexBoyerMoore_MatchPattern_m0E84EAABBFCDA85484466A41A7B90187D3489E8C,
	RegexBoyerMoore_IsMatch_mC09692237823CC6C74D4F5D8900D82CEC840FB47,
	RegexBoyerMoore_Scan_m86EB5A209D33DA3F4C7335F8FE133238666FA57B,
	RegexCharClass__ctor_m165680E6EA10035E0630220C967BD772F5D6F52E,
	RegexCharClass__ctor_mFAD2AF3D7B0D4D3DC8BC850B8899C5C4D55F8B49,
	RegexCharClass_get_CanMerge_mC093FE4BE7C1933BA9F146CF2A33CD729A965172,
	RegexCharClass_set_Negate_m8EA71CB8F671E99EF0B388CB520F44B8B6653A6B,
	RegexCharClass_AddChar_mED269B31D5C606032B3394A9B9305ED172D16746,
	RegexCharClass_AddCharClass_mDE8C70A6A99B2223DE4EA36A4753A13B5EEDE7BB,
	RegexCharClass_AddSet_mFC202CC46631017C7D64ED17EAA9C786C5C7CE83,
	RegexCharClass_AddSubtraction_m4EF34AFB93C001C6A4521E1F1C5F4B54833E8A12,
	RegexCharClass_AddRange_mB1188A62712FE4ABB7F5DB28645351E375B5C6A7,
	RegexCharClass_AddCategoryFromName_m032AED661665AE30D7F3F712914E2937F6E4FF97,
	RegexCharClass_AddCategory_m3A3C78511F279FD0CF67A3EE36F59A548A37269A,
	RegexCharClass_AddLowercase_mB87FC245CC837E582370E5410A6B40B23BEAC0BE,
	RegexCharClass_AddLowercaseRange_m75E07C55A082FE5B2926997F2564B611DF61A343,
	RegexCharClass_AddWord_mAED995BB6613893E0950C5895515204F1E95D152,
	RegexCharClass_AddSpace_mD1BF3AD215D69EC6CF0E49A359F7FC709FD6FBB6,
	RegexCharClass_AddDigit_m04C1001F7E8DA85CB2D262ED403BA9BD215A7A8E,
	RegexCharClass_SingletonChar_m4B0E4CE38D80141C013BE74B01C58FB81B0DE630,
	RegexCharClass_IsMergeable_m986CE5853CF227EC275D0F16E91472A3FA903531,
	RegexCharClass_IsEmpty_m10A0C981C0200FFA5211CF9EEC8EE1FFECE27545,
	RegexCharClass_IsSingleton_mA3970BE305000CAE9576642E3C6751249767A2B4,
	RegexCharClass_IsSingletonInverse_m04C0F39C52377F64FA6B7D93AF2B89D203E9A108,
	RegexCharClass_IsSubtraction_mE9E413F4B753B830F3091897DB20C4E6E88EA5F9,
	RegexCharClass_IsNegated_m7F52D93AA1B63D6C1AACB04DE2CA8ECFB901BA47,
	RegexCharClass_IsECMAWordChar_m95AFA872BB22C901C60DFCC9C8735BA78EE1EC84,
	RegexCharClass_IsWordChar_m2912E5148F0F89512DDF7D629188ADA82961080C,
	RegexCharClass_CharInClass_m635FAC50DE8EF6690ECA98FADD367716A5A54BEC,
	RegexCharClass_CharInClassRecursive_mEC6F84D9CFEAD5A3F0091FC05296AA53FF188232,
	RegexCharClass_CharInClassInternal_m013394FB15A790E1BF02D518C3F7485359A4E3E3,
	RegexCharClass_CharInCategory_m18B64CFDDA01F24D468ED85E35E72D7B71203957,
	RegexCharClass_CharInCategoryGroup_mE20D51D1FD31B839D900136C42B8A7F014032C82,
	RegexCharClass_NegateCategory_m6BE8AAA97753BF3F6A9577E4EB5D8A0590ADF638,
	RegexCharClass_Parse_mE586DA457874C3808D48C2C514AED5B147221D27,
	RegexCharClass_ParseRecursive_m04198C9230D295E6B3261040CA9DD37DE03EE79F,
	RegexCharClass_RangeCount_m36A38A50CD2E3E61B5F55D95562BD92923F981F1,
	RegexCharClass_ToStringClass_m6B0B92A0A525001CD78A6C2A766295268E0FED57,
	RegexCharClass_GetRangeAt_m67B8198B09F344044D8F742C5C9869034C4A3128,
	RegexCharClass_Canonicalize_m77EEBAD046B3EAD5110DD472434BDE16851C7A9E,
	RegexCharClass_SetFromProperty_mD4411181E13EEB6F8D96972AF4564698999E9DF1,
	RegexCharClass__cctor_m15D71DF95CE4C3DEF5BDF06B03E6521A07E58296,
	LowerCaseMapping__ctor_m06D0A21D5D72E39535F82076EF046F98C62D7D5A,
	SingleRangeComparer__ctor_m9B430EFE70974D964D73913D81E7C6C9236BBCCE,
	SingleRangeComparer_Compare_m917FDE86E5F31A755D07077EA095F8B7E27E130B,
	SingleRangeComparer__cctor_m649382AF0493818C7B5D453010FE22A3523F1A90,
	SingleRange__ctor_m14FD490AFB119E412298554D31FADDCB9465511E,
	RegexCode__ctor_mA50EE1CFA1A050BFC45E135DAB7E2704049512E0,
	RegexCode_OpcodeBacktracks_m44FC8B177CFCB191B72C109984A707744FD7B2BC,
	RegexFCD__ctor_mE1C0A21180069D25EEF6F3702938ABB087F35C0C,
	RegexFCD_FirstChars_m3BAEF0481AF78F4FC40C35CEDA9100017A6D794B,
	RegexFCD_Prefix_mE4F5ABEA5443B6940F83E59C60D0A9A201CC4BD6,
	RegexFCD_Anchors_m9211098DA08795B8459B918CA3D9A1A4733365D7,
	RegexFCD_AnchorFromType_m825872EAB7A0322BBD677DF34281A3ACEE8808D4,
	RegexFCD_PushInt_m0B38AFDAF0D14FF1E41AC90838B40C4C866D8875,
	RegexFCD_IntIsEmpty_m05E3C6FF40B69E98D90CF1C12DF3F32AEC800B5E,
	RegexFCD_PopInt_m1DF4F5E6B4BA9E15AFF2B3C44D1A233EA46EFCD0,
	RegexFCD_PushFC_m1396C7FD5ACE3D28076FAFF667D9A0D169F8B675,
	RegexFCD_FCIsEmpty_m749E6423466EAD51E38445AC91E76DE31258574F,
	RegexFCD_PopFC_mB83F2100E8EBACF17904BD5CACB57525D2966628,
	RegexFCD_TopFC_mAE41171B96938FD879E0C0AE7C0D06645AAA84CD,
	RegexFCD_Dispose_m0B04A2F3B819FA7BFAF0AECCBF6D22E6D9789C07,
	RegexFCD_RegexFCFromRegexTree_mD5A53219905969B914CE27404553A4360DA78500,
	RegexFCD_SkipChild_m5ACE619BAC10DD5DFE755655DB6151E7DB0885FD,
	RegexFCD_CalculateFC_m16F7EB523C02B530A741A7F6364F2E46865DCC91,
	RegexFC__ctor_m18E889B95C8C6014582FD2C6991F789190519F0A,
	RegexFC__ctor_m073D88DF033828EB626DBEA191FE78296CADB57D,
	RegexFC__ctor_m69E0058CD2F4A36F14589250B3F5CBAD7025D6F5,
	RegexFC_AddFC_m61D0F8CDE635ED3A48AEA069A7700B100D2AE851,
	RegexFC_get_CaseInsensitive_m4FCD7C7545925463ADA4055E9FE62D5A452E2102,
	RegexFC_set_CaseInsensitive_mF9A3745D1575160C2F2B1F7F9EB5541BD0C9FF4B,
	RegexFC_GetFirstChars_m6BAC398ABDCA5AF58CBE678143C9792611A2A3E8,
	RegexInterpreter__ctor_mB81896A8292574F7532CF707F74A1AC1DECEA9FF,
	RegexInterpreter_InitTrackCount_mFFFBE9294C6FCB854D4502362473C40A5ABBBFC4,
	RegexInterpreter_Advance_m31E2455F84B61536DE535BE51D4D0B42DC33857B,
	RegexInterpreter_Goto_m9BF559085E75D4AA7457FAD60E879AB96BB4AEC9,
	RegexInterpreter_Textto_m66DE4B949DBAA02672CFDE27162BCED542038850,
	RegexInterpreter_Trackto_mDFDFF0401BC8F2B2D26AEC65B54B963F84A089AE,
	RegexInterpreter_Textstart_mDF185C6B3E245A2307A8B0B9F5FE5DF8C8C9936E,
	RegexInterpreter_Textpos_m6E20619B1C7F8B77D7A57A8A6939C24011808DEC,
	RegexInterpreter_Trackpos_m6BDE792A7333FADB2533591B88211FD77E8F0401,
	RegexInterpreter_TrackPush_mC82959F407482A4713C419296455A18F5D861E93,
	RegexInterpreter_TrackPush_mA225A4A0302F57CE82842189DCB8895A9AAEDB3D,
	RegexInterpreter_TrackPush_m94D69F6CC8E122478F4EADE04FB7FC9482CEC878,
	RegexInterpreter_TrackPush_m606F28BD60B479F0FD3073F8B5627836DB89230E,
	RegexInterpreter_TrackPush2_m5131F1DCBCA4D4D2231B44A85B08DE319BE31C9F,
	RegexInterpreter_TrackPush2_m18FF10B704A6586A18D9AB21D28737DC32DA2797,
	RegexInterpreter_Backtrack_m8D227FAE1EFD91174D43FF070D9200291941E593,
	RegexInterpreter_SetOperator_m693B374C6827D36CE755EFFDA53E245FB04652EC,
	RegexInterpreter_TrackPop_m2FE356634C8337D8B657D30790588E4A4D7DECC1,
	RegexInterpreter_TrackPop_mD3125258D278581E337C822F3C9847233E06224B,
	RegexInterpreter_TrackPeek_m8D2CDC071ADBBC028FBAA364E2C18BD19FD6D861,
	RegexInterpreter_TrackPeek_m45C4F44554EC086C01B03C054BF6A94105D39699,
	RegexInterpreter_StackPush_m9BC14D7DD0FAF99AF230B436F32557E056F8FB60,
	RegexInterpreter_StackPush_m7741DC1BAEE69DFB68E1D60A9FC39C718829DF0F,
	RegexInterpreter_StackPop_m3284B40674B96F317D05BD2616AAA80181E0AEFF,
	RegexInterpreter_StackPop_mAEBB71FEDEADC065C5FA19E7C2C0FF0E82CEE681,
	RegexInterpreter_StackPeek_m16057890EE30C3383227F21632C02C6BEF981991,
	RegexInterpreter_StackPeek_mAFBD38869C8A829D6ACF534643B8A6C9863A10D8,
	RegexInterpreter_Operator_m3BD9AD7DCDA4E84DE4821DA786E1B1483E677B58,
	RegexInterpreter_Operand_m09F048ED7BBB36BEFC5023D420323E37A97C9560,
	RegexInterpreter_Leftchars_m6FD5AA399927D1767633B24AE234D596CBDD4002,
	RegexInterpreter_Rightchars_m0D684BDADC810F61B803D30D121506833AD07CD7,
	RegexInterpreter_Bump_mD5E6B4A80D6C620EDD37A5C04156ADF7C27EE9E1,
	RegexInterpreter_Forwardchars_mE97F35195F994160F951E3C035F9E0C35EB739C6,
	RegexInterpreter_Forwardcharnext_m78266C74DAAFD2D09050F148FFDA0A50656AC4C1,
	RegexInterpreter_Stringmatch_m89AE288DB294AC0146C7772193CAD3B702849E54,
	RegexInterpreter_Refmatch_m457B37C3E3B0671E7E89B7F6C16396D36EB32F68,
	RegexInterpreter_Backwardnext_m0C573AFAD3FAEBED37991618A232ACB169F524FF,
	RegexInterpreter_CharAt_m9EFCB7CE5FB311B6C061AA996705CD3D402F8E8C,
	RegexInterpreter_FindFirstChar_m87D986F0502AEEFC4F13EBD0EAD891C495C4845F,
	RegexInterpreter_Go_m1CF26F8D95231F3B2235EA98AFB286C3F1629BED,
	RegexMatchTimeoutException__ctor_m24286DF7231B227E3A8B3C31AE33447361971005,
	RegexMatchTimeoutException__ctor_mA8C497C6700E1233B4953A8AA7B8F57C8682C410,
	RegexMatchTimeoutException__ctor_m7DE41C173C1A57CEA5D015FF63DDFCB70CFF4FBF,
	RegexMatchTimeoutException_System_Runtime_Serialization_ISerializable_GetObjectData_m6F530DBA34FE5CBECBCD322C8FBD4AE28054688C,
	RegexMatchTimeoutException_get_Input_mA4DCA4F3C9692ACDC45FBEB683EE4165C84E4073,
	RegexMatchTimeoutException_get_Pattern_m8F12503E2B66F34B8E8EFE46971C169EA1BFBAB2,
	RegexMatchTimeoutException_get_MatchTimeout_m12D9D88EFFA06EAC0FF06C81ECF29BAE0F906087,
	RegexNode__ctor_mAE4AC160F21E5DF26B99B74753BCFB9EF0951B2B,
	RegexNode__ctor_mFA693535F5B9610F075D30F19626A8AFFA879D7B,
	RegexNode__ctor_m00D0AE9AB07549681FB13D96CC0AA20FED8CE6EA,
	RegexNode__ctor_m3352B98BE0DAD220AB653F929BD6D95689B5E07F,
	RegexNode__ctor_m5ECC62B9C3918CD043542E5BEAAD39A06158E27A,
	RegexNode_UseOptionR_mEBD872419C386D7C7802D2A6D2187E7B6D1D1641,
	RegexNode_ReverseLeft_m251846220299FD022068361A09B9A7A4CF67A586,
	RegexNode_MakeRep_mFDCFC9F5DA5DCBFC66D6F00A8E08A602F00C5F48,
	RegexNode_Reduce_m9D14953F47CD1D924C3EEB9D0287D7F257AD2D98,
	RegexNode_StripEnation_m2BACBC29E58CEC452B91534BCFA2A21639712B61,
	RegexNode_ReduceGroup_m62F5B20C564AC8A8E8C1C9E00684A8BB9A988E3B,
	RegexNode_ReduceRep_m85B3760500D732A04B60CD3F247CE8ECA1E6F8C1,
	RegexNode_ReduceSet_mD307DD149936830074467A0F24C2E20032F708E9,
	RegexNode_ReduceAlternation_m193F3C5A073401BA987E5AB7D35F59B1FC902341,
	RegexNode_ReduceConcatenation_m95EE4F324360AC71D52EDEC1C53F6F81D73C67C3,
	RegexNode_MakeQuantifier_mA6A10E1AEF5AA80F82BD1BE6D28E62259208B5BC,
	RegexNode_AddChild_m1A2B3A7445927E39341C233C0E533D77FC9517A8,
	RegexNode_Child_m6F5C25EE5349D1BE0D65C89475FB0C42A15BFF27,
	RegexNode_ChildCount_m833AF69D2711D8EEC5E50FD666A61F428A12396D,
	RegexNode_Type_mDE9685FD99646016D0D6038C4509C6392B10F019,
	RegexParser_Parse_mB845F2DC38DFD8C50E5B8D0D0F87330597409793,
	RegexParser__ctor_m99DC2B2C4B64E7298BBF49AD862BFD12007E38E4,
	RegexParser_SetPattern_m852D668CE45FBCAA3A6ACC415B94BDD0A7190265,
	RegexParser_Reset_m426A8C90F4D8065486802AE7AC9FB07FC599EB01,
	RegexParser_ScanRegex_m3CD95B8EAE4F2E8ECF3461F199B04A021C77231D,
	RegexParser_ScanCharClass_m01448F1178653B15083659F29014BB87A6E2295D,
	RegexParser_ScanGroupOpen_m5ABFBB28982A5097B2D560091D2AC12EA8B028F7,
	RegexParser_ScanBlank_m4C1B18EC7A5DF860363CA17E69B20A00023B5E82,
	RegexParser_ScanBackslash_mAAE11106CB78789803468679894E1D7AE6B0C92E,
	RegexParser_ScanBasicBackslash_mB0F1728AD89C8F196601B3110F0BA807FBC8FCEC,
	RegexParser_ScanCapname_mC9524DCBE266D677078207375663506AC32BE3F7,
	RegexParser_ScanOctal_m95EA0AE4B61D5418914DDD3815C6277471B4EDFD,
	RegexParser_ScanDecimal_m1BFFE19E0698D0EC97D81C655F3D04B608897161,
	RegexParser_ScanHex_mDD96A112E30DABDB5BC0C558373DA5607F8ACF95,
	RegexParser_HexDigit_m40FED0646820843A4C35AF9349E27B0E0A84A5CE,
	RegexParser_ScanControl_m26274BE5776CF5AC03E8EAC7B5972C0A8AF2A95B,
	RegexParser_IsOnlyTopOption_mAA13B95864B8B314B8B6689B47FA4BFE3B433280,
	RegexParser_ScanOptions_m56F01464F53558D3AF3BF1CD874A2656930AF31C,
	RegexParser_ScanCharEscape_mA872DDDD24E165F5B5C5F62DCFAE3B02CCF97C98,
	RegexParser_ParseProperty_m3B765A1B30A38A723CAFE63AF9D6F3D08C7C6F38,
	RegexParser_TypeFromCode_m11B176B0B9A6EF391370E021B111101AE56F4CC6,
	RegexParser_OptionFromCode_m7E2E8164FA38C45692BC68054E574F00B65F383A,
	RegexParser_CountCaptures_m2ECDC9ED534F245A12224038BB86DE5D66DB56A3,
	RegexParser_NoteCaptureSlot_m21BD1F90FF2C62FFA445303D574D271757D62222,
	RegexParser_NoteCaptureName_m5CDAF94FD43B665DE067B6DC02BD72A315D48FFD,
	RegexParser_AssignNameSlots_mC5738B99DE328E7D1FA7279A717640EC2044857E,
	RegexParser_CaptureSlotFromName_mE18BA49BCA1CE4B972DC804E52C78E48905FEFAB,
	RegexParser_IsCaptureSlot_m1EE21960AA0BB1C072BD9D765AFF8A6DFC3192C3,
	RegexParser_IsCaptureName_m5DC0D7BF3AD01C929CB982048722BCD43797CEAE,
	RegexParser_UseOptionN_mD441B71AC8368C644B8D9A0CCB3DB8BD681639B3,
	RegexParser_UseOptionI_mB3FB0740ED596E6ABC827E903DCE8AA4258BA701,
	RegexParser_UseOptionM_mAE3FC2655A6E80EA35D5CEBEE1D82B5B07529118,
	RegexParser_UseOptionS_m070AD4458172FB14FF21E4502DCA6ECEE8938994,
	RegexParser_UseOptionX_m0BFB48EDC8A2F38A29A8F6CEC85080D9E04010FC,
	RegexParser_UseOptionE_mE0B7314EAB95CAC9B9762CD3AB9FBC71BFD410D9,
	RegexParser_IsSpecial_mF59FE804CDD354250CB77C4113F39C627349D051,
	RegexParser_IsStopperX_m173A82937CA9600F7CB12B9A101F39CCFDA630C9,
	RegexParser_IsQuantifier_mBD18FA810A0404B632392725175846E6B30A8782,
	RegexParser_IsTrueQuantifier_m22CCE851B85E0760270178DF1184746040E68AEF,
	RegexParser_IsSpace_m322B45C2A04F07D8C83B547370D266F39A8DE14E,
	RegexParser_AddConcatenate_mDDD0350FE254D75D6707668332D0F3088A5572AF,
	RegexParser_PushGroup_m2D76CB852430A3B3F7454DDC3322B2A5E4CE398F,
	RegexParser_PopGroup_mEC525DC83E5BF2F8DA03129AE3DF06834EB76552,
	RegexParser_EmptyStack_m756AC5BB66D0F2077EC7DD655A3D57681200F5CA,
	RegexParser_StartGroup_mBF861FCFB0FB9ED26744CAE2297537DAB6DF37C1,
	RegexParser_AddAlternate_m77BCF63DD4E1AB89B70F72821CAE4B67936CD847,
	RegexParser_AddConcatenate_m77A3D9F6B1484C7B656BDF3CC5659248F9DEA1EA,
	RegexParser_AddConcatenate_m3B10D68AE09CC6A2F06AC65E49B918D11D5375FF,
	RegexParser_Unit_m6C984A797C4A92AB0A9985E5A8207DB9CA9AE87F,
	RegexParser_AddUnitOne_m991DCA85D3432B8F7F74D18125A69FFE25108F47,
	RegexParser_AddUnitNotone_m4F79DE175B95FDB88323D7FE9DC3132E24F8F0C0,
	RegexParser_AddUnitSet_mA262F57A7FAB27996D8115F66BC84A5C58317D7F,
	RegexParser_AddUnitNode_mE8DC7CD039C933AE881097E041BA620AA325FDE8,
	RegexParser_AddUnitType_mEBDFA2325ABDA3FDA74184DBFB706824F6540553,
	RegexParser_AddGroup_m8989D6A9CD4B700522D722DEF5FEFC267C4DBFB7,
	RegexParser_PushOptions_m6E501EE3C5D6FE990ED74935A9AC9A6E834FD968,
	RegexParser_PopOptions_mAE4848419A71162B8E563781AC817A8D6ACEB91B,
	RegexParser_EmptyOptionsStack_mC16A4D18503FF296911710732A95062A8279163B,
	RegexParser_PopKeepOptions_m911F83DFA290ED90B06D3D3166553E6205C256C1,
	RegexParser_MakeException_m385FDF5B69D02A0CE93393E92AFAD928A9CCE3FC,
	RegexParser_Textpos_mBF50F3EB06FDA6C68AF4BF0D0C8D4FC09FDCFC88,
	RegexParser_Textto_mA45B325CA6CCBC0012F1CBB0282AE114B34AEBE2,
	RegexParser_RightCharMoveRight_m4386CDEFA7DC102FCA0E6E00E522E1712AE3AFE9,
	RegexParser_MoveRight_m5F6B031B292FC0F81DF5E7039EDA6C9D0706A0A9,
	RegexParser_MoveRight_m921C1D96B625B4C8AF1D658E514D57CC44E40B61,
	RegexParser_MoveLeft_m57BB7A0146D0E8834D715667048B75A517761AD5,
	RegexParser_CharAt_mF46179762DDF46FB78E32CCA3058950244CFCB70,
	RegexParser_RightChar_m23D21ECC3D6999A466C0A91BDF589BFE4801CF91,
	RegexParser_RightChar_m6BEE1FBF048A2DFC53235447BB2F6AC8C56A4147,
	RegexParser_CharsRight_m6F83CD819B532331F5778143B6A95B31C8734A79,
	RegexParser__cctor_mC883BE9D9C8D47723D5CAD7CE25B5F347929AF54,
	RegexPrefix__ctor_m05CD44F32CB0B5F8B2C080EAB94886F9743E86A4,
	RegexPrefix_get_CaseInsensitive_m4DAE2D9E91916BFFAB928F7DCAA20A5ABD4560E6,
	RegexPrefix_get_Empty_m54EA279F2BE7F39358AB5DF1A5362A8CD92B4326,
	RegexPrefix_get_Prefix_m844D02262C0720B0DC488AD1541713AD37BC518E,
	RegexPrefix__cctor_m55930FB75AB023BADBEAB107BE38A1A559DDB7F2,
	RegexRunner__ctor_mBC777F351AD6D1CDD5F20C408B936F7D0F36ED69,
	RegexRunner_Scan_m903691F4DFB68C0267F240D61A6F166BB592CE53,
	RegexRunner_StartTimeoutWatch_mA17870A15492787CA1384132267054AB09BE8BE9,
	RegexRunner_CheckTimeout_m20BF0CD8BC16738ED6E7F20AAF5B4EE695E7E120,
	RegexRunner_DoCheckTimeout_mC26B9239FA1CD3DC98F11A82800F61852793CEA5,
	NULL,
	NULL,
	NULL,
	RegexRunner_InitMatch_mCCF94A1E7CCE89275E47BB218C4DD58DF5D5593D,
	RegexRunner_TidyMatch_m0EEEF0CFBB55AFF485B4D9F9FD21834B9B3A01FA,
	RegexRunner_EnsureStorage_mB3DB043396771740A05B2AABC293DAAB1250B024,
	RegexRunner_IsBoundary_mCF4AE83F6FE3CC46C71C4558DBCDEC255BD422B8,
	RegexRunner_IsECMABoundary_m56C692126DF69F0933BBFCBF122F764C6093DA26,
	RegexRunner_DoubleTrack_m15E35365FAF5F438C7BD0BB0C37594F765949F1F,
	RegexRunner_DoubleStack_m89C12DF438D7BE4ADA2A4D1DFA36F274F292E1F4,
	RegexRunner_DoubleCrawl_m7B67CFD3071825C9F01BC0ACE0E27B1C4EC6DED5,
	RegexRunner_Crawl_mBE60CC8F17E6D85E1A87F0A86BC684F32ACE7CA5,
	RegexRunner_Popcrawl_m2C994BE22A9A4DE0D897691A1CEF87189DB43E67,
	RegexRunner_Crawlpos_m4EC84964D3AD3DB49724BFC28CE89FC34478D532,
	RegexRunner_Capture_m3EB42B55A19BD7776156F04CBE9367C6EBA59F9C,
	RegexRunner_TransferCapture_m642CC595E9FA641B913CCBE02E233DBB319A6E8A,
	RegexRunner_Uncapture_m70B0FFB308F48B4673F73311330BFF640F47B7D9,
	RegexRunner_IsMatched_m7E64237D020BFFA74813A8B6455D5F35671A876C,
	RegexRunner_MatchIndex_m150A8B71C07022C485630C71B2A9FA6FD195F88F,
	RegexRunner_MatchLength_m19C9B24F4F6CAC56A391A0AC47A4358C61615BF7,
	NULL,
	RegexTree__ctor_m89DA1335AAB55B0707774E978E6078CE42DB0DA6,
	RegexWriter__ctor_m37DF964F59213D287C9D812A3D0945744BF341EE,
	RegexWriter_Write_m69A5583635E511D67176A977E96C17AC982C4F01,
	RegexWriter_Dispose_mD34F8BFC41D28F22FBC65E3F1A211DFE85625E04,
	RegexWriter_RegexCodeFromRegexTree_m3352CAF05A1B1753D32D768B83BB778B3E2D4568,
	RegexWriter_PatchJump_mC1090F37107CAF7F4B52B020E070BDE9284118A6,
	RegexWriter_Emit_m5884414A6EA7431DA9C5708E650B20BEDF982CAC,
	RegexWriter_Emit_m41C2B9DF58542C452CA4FC77A317884578757D75,
	RegexWriter_Emit_mDED2545E64CE95D941A1232DBA24EB6C81A7DB0C,
	RegexWriter_StringCode_m5D4A227BBAA0674B820D70BEA1A2F7D0B29635FE,
	RegexWriter_MapCapnum_m5EA7BA45A1C4D51DC20C281246D3997A3001A0B6,
	RegexWriter_EmitFragment_m0B35F7C3BDC22B5DE82735F2652C7948AE8494A5,
	GeneratedCodeAttribute__ctor_m37F2FCCEDE98E3DB23D3A973EFB3E67CFCBCBFFE,
	Stopwatch_GetTimestamp_mA3BDF219C573A34751D6A792E86C825B74D2CEB7,
	Stopwatch__ctor_mAFE6B2F45CF1C3469EF6D5307972BC098B473D0A,
	Stopwatch_get_Elapsed_mF16CCA2F9B7E2EAEFCAF8D81F44370DBAC1B2178,
	Stopwatch_get_ElapsedMilliseconds_m8D8E60ADEB47D008C06D8E57D28D41EAE07AECBF,
	Stopwatch_get_ElapsedTicks_m3F4040FBF8C7CCDC69E0E04824019DEBB25AA410,
	Stopwatch_Start_m35906A38C7D336CF9DD35854C7E170D762A55915,
	Stopwatch__cctor_mF9BBC572803E232BF2D323301E90A6AFDB496FB9,
	ExcludeFromCodeCoverageAttribute__ctor_m2CB8196E20519167E842D03BE4CFBCC84BEFC9E2,
	Oid__ctor_m4AF81B9D51BED48AE505251E0874EC654BA07B72,
	Oid__ctor_m533C0126C7C8D382D3E702287A9AD0CED2D0377D,
	Oid__ctor_m3973A5FE5482D45FABC8642E59B655274B0B5D59,
	Oid_get_Value_m59D678A83ED4DE7D87FB7450C8CF4084DB506FD3,
	Oid_set_Value_m122282994E6EE138CE4284B052EF8BE2086CA72F,
	OidCollection__ctor_m9B8C252AE048962DAD7419597007ABA2793F9D98,
	OidCollection_Add_m1D4822DF4EA0B912A1DED6AA102D41F7FAEDC8F5,
	OidCollection_get_Item_m0AEC71C746E735035A05C50EE2721C1E8FE08F7A,
	OidCollection_get_Count_m9560D70809087591935904924A15FBEC1FEFEB11,
	OidCollection_GetEnumerator_mBB791F48E305B3355A1BCE624E0BB367331F82AC,
	OidCollection_System_Collections_IEnumerable_GetEnumerator_m4624AA2C7F0693698228803D9B59EFE6AAD6AFE4,
	OidCollection_System_Collections_ICollection_CopyTo_m4EF1FB9A18E822BA196BF3F0204B7C06178B9CFC,
	OidCollection_get_SyncRoot_mD310358F3BF4454604AD96C734BA6EC180CE7A85,
	OidEnumerator__ctor_m2168FEE96614FB7460C84762ED3728A5413F42E6,
	OidEnumerator_get_Current_mCED077699FA00ED57FA82F8B460C28B86F1B5C9F,
	OidEnumerator_System_Collections_IEnumerator_get_Current_m0B627B9F510EF4F283F40EBE17164B9231BB7735,
	OidEnumerator_MoveNext_mD42201944C163E81DABDE08B1535C4AB9A5778DD,
	OidEnumerator_Reset_m24F1F25812A7BFE9B02B312BA9E670C930622F27,
	AsnEncodedData__ctor_m90028EC56A5E36272EAA4A9279AE23358D46F547,
	AsnEncodedData_set_Oid_m877771219F651EA3FF834952300180274531C9C7,
	AsnEncodedData_get_RawData_m629D858D7D20E72C1F46124E5032FE0D3B32AADF,
	AsnEncodedData_set_RawData_m05F9F17765AD384ECE3A333DCBF6A0572099346B,
	AsnEncodedData_CopyFrom_mA5DD4A419C5FC29D82DBE9B69C9F8CBC2872A1F2,
	AsnEncodedData_ToString_m669772943B78D9242BC5F176A4BD857326F95021,
	AsnEncodedData_Default_m883444CBD5238793D1B94A6DE273FF2F9C47EA70,
	AsnEncodedData_BasicConstraintsExtension_m74BFC192666755E9D8A088880789AB6EA84F1FCC,
	AsnEncodedData_EnhancedKeyUsageExtension_m3C95D5D2D8F81BEA5E33B264767C8A5E5575D7CA,
	AsnEncodedData_KeyUsageExtension_m9A9C72CF2A831A1849A55336FC8D6DEE097E28D3,
	AsnEncodedData_SubjectKeyIdentifierExtension_m3CEDDCED26AD5621407DAA401F385269348BB789,
	AsnEncodedData_SubjectAltName_mDE2F39E1A43C1E13D89319F221517EE54B1A6A14,
	AsnEncodedData_NetscapeCertType_m55E43EBDA7882304E8FE5F3DC134A2719F519373,
	PublicKey_get_EncodedKeyValue_m153EF781728D471C55002A40BBDA435F1B3A1213,
	PublicKey_get_EncodedParameters_mE338A4A69A15907E38A644D624A07DDD55C521E0,
	PublicKey_get_Oid_mB5796F569C136759CB740F747C6A3B6F8976401A,
	PublicKey__cctor_m1B2E44B5BBF321952E0FD1F19C048E43A6506407,
	X509BasicConstraintsExtension__ctor_m672BE9B41107A8C30454BF4CAC120D70512BA89A,
	X509BasicConstraintsExtension__ctor_m76C88E8D4C7CCEC7B2C3D56CC58A56352F7729E4,
	X509BasicConstraintsExtension__ctor_m02334D8CAA280B339CA41E2019CF1F02C8B2E7DE,
	X509BasicConstraintsExtension_get_CertificateAuthority_mB4ECEF43A245E97DA80C6323BE339BE2DF99FF5C,
	X509BasicConstraintsExtension_get_HasPathLengthConstraint_m8F7C371EFF99BD110663DB106BEE889955255BC7,
	X509BasicConstraintsExtension_get_PathLengthConstraint_m4D3F016E42C243887D07DD955518A818DC14BBC5,
	X509BasicConstraintsExtension_CopyFrom_mCC603D99CEC828BDF86FDEB3C4650CF4D3753B8E,
	X509BasicConstraintsExtension_Decode_m7C50097B6238FEF4DAD6A9A09A449DB0CAFFEBEB,
	X509BasicConstraintsExtension_Encode_mD7FA31A70B025F67062C3844E4FEB09B14A4DE2B,
	X509BasicConstraintsExtension_ToString_mBFC61BA07CD2BB8EACCC19A5AE72EA81C11D02E4,
	X509EnhancedKeyUsageExtension__ctor_mBDA55EB3C6594D4F6A06102B4EAF633F86C3E092,
	X509EnhancedKeyUsageExtension_CopyFrom_m9577FF97B8E3074B12B7056BF7E30AA0F84309BF,
	X509EnhancedKeyUsageExtension_Decode_m1110DD9BD5D717540009DB5D90805458CF07423C,
	X509EnhancedKeyUsageExtension_ToString_m9576793CE590716A3BB5AAC1632F946DF75605DE,
	X509Extension__ctor_mFC88C10C2C6718E6AE74CE37E10D4EBB8FD67BAC,
	X509Extension_get_Critical_mEF827F09D95784210C2F8D5FD7BE27BB54803E35,
	X509Extension_set_Critical_m19FBE6E0C8B9069E5A2C232DAAE06046AF18EFEF,
	X509Extension_CopyFrom_m674ABC4ECF701B708C28B546B42B7799CA24D4A9,
	X509Extension_FormatUnkownData_mD0463F5096A6937E95E3165048299CBA9A1A6F7D,
	X509KeyUsageExtension__ctor_mE0EE68497EDEDD78213EEDC931D68DD35DF6340C,
	X509KeyUsageExtension__ctor_m1DE9CD768712A07E5803496501CCFDA81CFB2689,
	X509KeyUsageExtension__ctor_m2FAA526721435935D0B10E0DB916AF56CFD84751,
	X509KeyUsageExtension_get_KeyUsages_m1BD6C5BC6E3E380982C135B2E81731435345144F,
	X509KeyUsageExtension_CopyFrom_mCBCF5CA17676B2030F6C82ED80A008EF029AF733,
	X509KeyUsageExtension_GetValidFlags_m82B1C7DA4F4E0F16090782189E1B4984D133BB45,
	X509KeyUsageExtension_Decode_mB64239ABDEA862E43E26CF324D3E41ECCF3088BC,
	X509KeyUsageExtension_Encode_m38D20B2FB68EDF3594EDC0D9BA88800ABF305542,
	X509KeyUsageExtension_ToString_m270131139984275D7571120A2984F0F5E77E79DC,
	X509SubjectKeyIdentifierExtension__ctor_mFC2A19B1301ABBA5F6C5CE407ABC4F4CA1EB49B1,
	X509SubjectKeyIdentifierExtension__ctor_m421293DCB67F3A918098962166B8166FD9657D08,
	X509SubjectKeyIdentifierExtension__ctor_m610C9C804421B7CB519F3A5C7D66827CC1106EE1,
	X509SubjectKeyIdentifierExtension__ctor_mECF6398B9841FB4533228FE77F51F93316EADE94,
	X509SubjectKeyIdentifierExtension__ctor_m3DD2C4E1505A6E625499FBD833BFC076D28D83EB,
	X509SubjectKeyIdentifierExtension__ctor_m1858C1B050FAF65946B790737652E874781B4B56,
	X509SubjectKeyIdentifierExtension_get_SubjectKeyIdentifier_mFF2A155072C67F430E7F4B178733A80388943CBA,
	X509SubjectKeyIdentifierExtension_CopyFrom_m959BB16C6973B1B565D02ED9993AF9069019D77A,
	X509SubjectKeyIdentifierExtension_FromHexChar_m6A90ECC8622DD1D2DEA0FB18356E97B15E8371A9,
	X509SubjectKeyIdentifierExtension_FromHexChars_mFBC6B2009778633420F9F4FF38D7E641FBF63EFA,
	X509SubjectKeyIdentifierExtension_FromHex_m0C965FECB6AC9330589DBDC182E7EA34EFE4195D,
	X509SubjectKeyIdentifierExtension_Decode_m9E9B87F478F55E202411CBCE3AA314CBF73C7F30,
	X509SubjectKeyIdentifierExtension_Encode_m8746D45CE4AC46543D3332BC6001FA1BC7A72CB3,
	X509SubjectKeyIdentifierExtension_ToString_m9AE51334149DE6200E554F9C30E2DC5570D36707,
	EditorBrowsableAttribute__ctor_mE6105AD9666A4DF03DB2590C687EAC6B12D908CE,
	EditorBrowsableAttribute_Equals_m2DFA6ADDE69D95657A8257840C01FCAF74A6B02F,
	EditorBrowsableAttribute_GetHashCode_m1AE50FA45B338D71189AA3C249DB56406DEC798C,
	DisplayNameAttribute__ctor_mE16A1FD5C8425C7F00EC5728A1227AF6FBEFB968,
	DisplayNameAttribute__ctor_m396F07353E80428B4AEB85DC66F5B9380A077AE3,
	DisplayNameAttribute_get_DisplayName_mA3E91855E1EAC0085BF2C1EB501B7801B9563D5D,
	DisplayNameAttribute_get_DisplayNameValue_mD9F58F5FFF2B7C682D8470101352BEA88AE0E5AC,
	DisplayNameAttribute_set_DisplayNameValue_m2DA6A8AA72F3969BA5639F707AB90A2D363BC49C,
	DisplayNameAttribute_Equals_m06E1BC5B48A9D42FECF4BEC20338F8DD6A525E88,
	DisplayNameAttribute_GetHashCode_m882BB47C783C5D55149DB892F41108D4953B8B16,
	DisplayNameAttribute__cctor_mD9A9030A1931AD6CDF0428CFEC3E34B91652A8F4,
	InvalidEnumArgumentException__ctor_m30DFADC9DAE3067EBFB7AF17F9EE5DBAF5B00D10,
	InvalidEnumArgumentException__ctor_mB48FBD0C68C97EF69D80B56EB927CAF08E3181E1,
	InvalidEnumArgumentException__ctor_mB6DA9F3F2403BFA3AAD577AA4A27034D29A60EB4,
	InvalidEnumArgumentException__ctor_m05AB9EBB8AEDE6DF1BFE1E8E7F2BA4EB303DAA13,
	ArrayConverter__ctor_mAA6B57C16F167FF104B8E23402DE87ACA5E13A2B,
	BaseNumberConverter__ctor_m697E7E5DB315CE6CDA318F256A634BAEF3C7C76F,
	BooleanConverter__ctor_m50990A1CB60D3557199985EF164FF1E8AFCADC87,
	DecimalConverter__ctor_mC1E815EF17DE8DBD91FB472C83A4F38514704B46,
	DesignTimeVisibleAttribute__ctor_m89415646271B6901AF745CDD2BF3A2244DFCD946,
	DesignTimeVisibleAttribute_get_Visible_m0EB229B7592D784080EEFA2C7100DF58ADFF064F,
	DesignTimeVisibleAttribute_Equals_m59ADCA70D1F8363BAACDC37274852A8E6C9A954A,
	DesignTimeVisibleAttribute_GetHashCode_m4E2FF875B5BD47A6C7EDCB5F9CB66D6860F5132B,
	DesignTimeVisibleAttribute__cctor_m60EE97AF27C65BA7BD5BC1FC200B7B261BD3F084,
	DoubleConverter__ctor_m3CA5201BA68AB510BB2112A6755FCFEAB6DE53BA,
	Int16Converter__ctor_m3803A7772B21F49200FC142C193A40DC286C9DFF,
	Int32Converter__ctor_m88220DA12F6ED6FE4D1232B73C20DECE9EFC63FF,
	Int64Converter__ctor_m3C124DB065F27F6BEAB7ABAF4B3D13E6A9F3E94C,
	SingleConverter__ctor_m463B63EB8D945D2D43E5B8099D8F0EC24AEBDA7A,
	StringConverter__ctor_m074D8686ED6ABC0E64C20D1EFD4406F215E7187A,
	TimeSpanConverter__ctor_m7319DCEEDA2578B340AD4FEEE010AE5CEFAC9BD5,
	TypeConverterAttribute__ctor_mEEE3CA7C17555BA93BB76201BA2BCF879E617DA9,
	TypeConverterAttribute__ctor_m0E1A55E1BD368F0AABCAF766BBCE1D275777A52A,
	TypeConverterAttribute_get_ConverterTypeName_mBBE5335F11FC13345D2828866172BB5DDF9F3E45,
	TypeConverterAttribute_Equals_mA3BF2C8CFA0AE8B5D0F52E066B1141AA53B4E015,
	TypeConverterAttribute_GetHashCode_m23ACA2A297447E17150BF490188919676FBD8A01,
	TypeConverterAttribute__cctor_m7A206154E18C66B9CEA6934D8AFB1AE9DA4EA666,
	CollectionConverter__ctor_m20ED0C661750EBE427D356F24AED92A407988147,
	EnumConverter__ctor_m3E3C7D81C8092A5591BAA556288B2A3E22DCA99B,
	TypeConverter__ctor_mA5B1882A94D0491297B903563E8B03D75B2F67A2,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	IPAddress_get_IsIPv4_m912994B89DDE21F85E4FE40381CDFF1688BE3F38,
	IPAddress_get_IsIPv6_m957F80A11C7E40C2CCAE554F12AA547E387B6884,
	IPAddress_get_PrivateAddress_mE1CD072F1E7BDF981FD71CBD984F5FD2A2EC6399,
	IPAddress_set_PrivateAddress_m73BD8A117538CBDB80F8FF47D31609FA4855D74E,
	IPAddress_get_PrivateScopeId_m3C01EDB8971607BAC0398557E295B5A818191532,
	IPAddress_set_PrivateScopeId_m2BA43F20C3B975BDAC6E330C09E09BFD34594A42,
	IPAddress__ctor_mC240D2060BA0F5A96D286CDE3772FE4354D7030B,
	IPAddress__ctor_m8D2578858CF6F8E56591F45C8A14150DEFFA44F5,
	IPAddress__ctor_mFD3C42E1EECC3099D6E347238FB2A0EBE822AF19,
	IPAddress_get_AddressFamily_m1CE4BCCE499BD70B22F9E37B3F266F9306A98C21,
	IPAddress_ToString_m91FE727877BEAA0115F497387E2E9CB0B45C3895,
	IPAddress_Equals_mC4514B3B7F9B95B79A1121706C115AECE61636C7,
	IPAddress_Equals_m5EA56A0CBC4F66012C11628D8CC75E03E5A8C462,
	IPAddress_GetHashCode_m243850627E81961E58E3A7A0A39A1F365C47A4BA,
	IPAddress_ThrowAddressNullException_m2D5037C15BE289427E4A761A643C4F26C2F3F034,
	IPAddress__cctor_m83F9B947B6C0D0F8DEA3E0C62FCCC2573495D6CA,
	ReadOnlyIPAddress__ctor_m0E47AC9070535FCCC4796C165AC8DAB6FA2B5AC5,
	IPAddressParser_IPv4AddressToString_m3D56686708BDA87E76737D94441BF1AE486922D2,
	IPAddressParser_IPv4AddressToString_m4113E0673C882A7CB1313639E2836FB24B9BC6A0,
	IPAddressParser_IPv4AddressToStringHelper_m4226589AD61857FE45C082110271FFF2D3DF8C91,
	IPAddressParser_IPv6AddressToString_m7C25DEA2FB9E3F863777653CB9C314BBEDF911A6,
	IPAddressParser_IPv6AddressToStringHelper_m405EE1B288D87500878DD96ADAEE31B106CCF930,
	IPAddressParser_FormatIPv4AddressNumber_m716041E973744C431B090589D624C53696FD801F,
	IPAddressParser_AppendSections_m141C016465F9E7E1538E03340FEC16F89B500284,
	IPAddressParser_AppendHex_m877259AD615A68FDC55EDC188758E5AA95C124B2,
	IPAddressParser_ExtractIPv4Address_mB79D4899358C69C1FC7C44C81A26BBD976FC2CB6,
	IPAddressParser_Reverse_mDEBC20FC5D00F7C505E1ADE428CB6967B72B425E,
	U3CPrivateImplementationDetailsU3E_ComputeStringHash_m171C269D828658C44041FA68B6DE8CA290ED517F,
	BypassElementCollection__ctor_m344EC8CD2EACA36E1213C5914037BB133E87FE54,
	ConnectionManagementElementCollection__ctor_m4698342A715032A762FA04B2745BFCA46263BCB3,
	ConnectionManagementSection__ctor_m2FA1D29F887BD6498FBEF692BBC0AA727E38E802,
	ConnectionManagementSection_get_Properties_mFCE0F9418E9E8CD2E1301815CC4D548261F58E95,
	DefaultProxySection__ctor_m962B68CEFCDE7DFB7646EF16297CA4BE0097284D,
	DefaultProxySection_get_Properties_m5186F1A2734EB96F065C7BC14A215D17F8325418,
	DefaultProxySection_Reset_m0B9A1155840E7C55FC24AEDB23FB17A2815EEB77,
	ProxyElement__ctor_mC1BDF426F97F4F84CB5A34B423F59EC8748EBD70,
	ProxyElement_get_Properties_mBFE6E283FE7E9329B586ACAADC8F2E047C7ABB2A,
	HttpWebRequestElement__ctor_mB9DDAC7FF431D4AF4CAFCBC96E1A3572A1F83717,
	HttpWebRequestElement_get_Properties_mAA0F11EA73B5397C50809EAC679F2C564F9EC7CA,
	Ipv6Element__ctor_m869AC261E0994A8B3D83CEE25C6CB99E6A5EA4C1,
	Ipv6Element_get_Properties_m382D5B5B098EFF19C2A4AC1889CD1C8EAB918322,
	NetSectionGroup__ctor_mCD2F0DC2B9F0C6C346BC91BD8750C7111A9528AC,
	SettingsSection__ctor_mBD94E7E098221DC09775A3AE1713930F6820D242,
	SettingsSection_get_Properties_mA774C9C884F4AD5DEC2BBE31DF7E35CAC71EBF8F,
	PerformanceCountersElement__ctor_m9537195CBC7DC607F7253D06E7B6B1090456E68F,
	PerformanceCountersElement_get_Properties_m7C16FBC3E915E8384FB4409209628737B1537C9B,
	ServicePointManagerElement__ctor_m0F1CF21589CA283DA734A472930A9665698B2D36,
	ServicePointManagerElement_get_Properties_m70469523AAD1A2C2E9D4F90DDA1BA619FCE68B58,
	SocketElement__ctor_m4FB4C851F1BB20CED74C206EFB1BA9DDFBE024D2,
	SocketElement_get_Properties_m8AF94DEEDA0EB4092F59FBDC361592E1B9EBDEC3,
	WebProxyScriptElement__ctor_mB613D71DE569317E40726CD8DB9A6ABA0A302821,
	WebProxyScriptElement_get_Properties_mF5188225C0C1D64BC136465BB4B0E962FFE9240B,
	WebRequestModulesSection__ctor_mF25CD80A5111A4F87B7FBF7663A3442470B97C0E,
	WebRequestModulesSection_get_Properties_mBDBDED569E677717FB617CD36B94AE34BF4208F6,
	WebRequestModuleElementCollection__ctor_m62C156D78336B2E37EA6F10D477FDEAE4161F99E,
	DiagnosticsConfigurationHandler__ctor_mDFB0000E6218840F66D21BB6C9BF3F647CCBC5A3,
	DiagnosticsConfigurationHandler_Create_mF21B1CC68B87DE078690B2B493FCDC5F728CBAAB,
	ThrowStub_ThrowNotSupportedException_m14168B43936EF78B7B8AC08BD815254DAA1D9FA2,
};
extern void CachedCodeEntryKey__ctor_mE505E40C97820B3FD140F6212F0B4ECF8745585F_AdjustorThunk (void);
extern void CachedCodeEntryKey_Equals_m59F40708B97179EA5C9019EA6A958D7AB8BC8CA2_AdjustorThunk (void);
extern void CachedCodeEntryKey_Equals_m55F9ED97CB9AF357034A75C2FDB8498FC55D3AF0_AdjustorThunk (void);
extern void CachedCodeEntryKey_GetHashCode_m253B53C100F840B987A487020F02184019D776DE_AdjustorThunk (void);
extern void LowerCaseMapping__ctor_m06D0A21D5D72E39535F82076EF046F98C62D7D5A_AdjustorThunk (void);
extern void SingleRange__ctor_m14FD490AFB119E412298554D31FADDCB9465511E_AdjustorThunk (void);
extern void RegexPrefix__ctor_m05CD44F32CB0B5F8B2C080EAB94886F9743E86A4_AdjustorThunk (void);
extern void RegexPrefix_get_CaseInsensitive_m4DAE2D9E91916BFFAB928F7DCAA20A5ABD4560E6_AdjustorThunk (void);
extern void RegexPrefix_get_Prefix_m844D02262C0720B0DC488AD1541713AD37BC518E_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[9] = 
{
	{ 0x06000114, CachedCodeEntryKey__ctor_mE505E40C97820B3FD140F6212F0B4ECF8745585F_AdjustorThunk },
	{ 0x06000115, CachedCodeEntryKey_Equals_m59F40708B97179EA5C9019EA6A958D7AB8BC8CA2_AdjustorThunk },
	{ 0x06000116, CachedCodeEntryKey_Equals_m55F9ED97CB9AF357034A75C2FDB8498FC55D3AF0_AdjustorThunk },
	{ 0x06000118, CachedCodeEntryKey_GetHashCode_m253B53C100F840B987A487020F02184019D776DE_AdjustorThunk },
	{ 0x06000145, LowerCaseMapping__ctor_m06D0A21D5D72E39535F82076EF046F98C62D7D5A_AdjustorThunk },
	{ 0x06000149, SingleRange__ctor_m14FD490AFB119E412298554D31FADDCB9465511E_AdjustorThunk },
	{ 0x060001ED, RegexPrefix__ctor_m05CD44F32CB0B5F8B2C080EAB94886F9743E86A4_AdjustorThunk },
	{ 0x060001EE, RegexPrefix_get_CaseInsensitive_m4DAE2D9E91916BFFAB928F7DCAA20A5ABD4560E6_AdjustorThunk },
	{ 0x060001F0, RegexPrefix_get_Prefix_m844D02262C0720B0DC488AD1541713AD37BC518E_AdjustorThunk },
};
static const int32_t s_InvokerIndices[772] = 
{
	8486,
	9605,
	8486,
	10294,
	10294,
	7102,
	4750,
	4750,
	8355,
	7359,
	7778,
	7469,
	7999,
	8037,
	7803,
	9546,
	9597,
	8074,
	8036,
	9597,
	7790,
	8272,
	9810,
	8907,
	7819,
	8908,
	8022,
	6869,
	6869,
	6869,
	6869,
	7089,
	6985,
	6869,
	9608,
	6869,
	1656,
	3852,
	6869,
	7102,
	7087,
	3949,
	3949,
	8911,
	8911,
	6985,
	7102,
	5484,
	5600,
	2864,
	2870,
	7912,
	9805,
	2878,
	2878,
	2878,
	6985,
	6869,
	9608,
	10235,
	10294,
	6946,
	6985,
	6869,
	6985,
	6985,
	6869,
	6869,
	9616,
	9616,
	9714,
	6946,
	6985,
	8856,
	3852,
	9608,
	8384,
	6985,
	6946,
	2904,
	5694,
	7102,
	7662,
	7102,
	2140,
	4778,
	2140,
	1137,
	4778,
	7102,
	7672,
	8275,
	8366,
	99,
	3,
	12,
	902,
	852,
	486,
	1140,
	7438,
	8049,
	9711,
	8491,
	9616,
	9616,
	9616,
	9616,
	8452,
	1324,
	1290,
	3852,
	3852,
	3852,
	8295,
	2140,
	6869,
	8854,
	6869,
	9810,
	821,
	1396,
	8035,
	7652,
	4778,
	2140,
	5600,
	10294,
	7102,
	7102,
	7102,
	5600,
	2878,
	2878,
	7272,
	7326,
	7251,
	7250,
	7280,
	8714,
	9205,
	9616,
	9616,
	9616,
	9616,
	10294,
	6985,
	6946,
	6985,
	2839,
	1145,
	1143,
	3852,
	10194,
	10294,
	6946,
	3816,
	3816,
	3816,
	1601,
	5562,
	9810,
	6869,
	6985,
	2839,
	1145,
	1143,
	3852,
	1338,
	8037,
	7472,
	7472,
	7637,
	7637,
	8854,
	8791,
	8034,
	7637,
	8906,
	8906,
	8037,
	7794,
	2866,
	5600,
	7102,
	3852,
	1338,
	6946,
	5562,
	6946,
	5562,
	6985,
	5600,
	6985,
	6985,
	961,
	6869,
	10294,
	7102,
	6869,
	6946,
	4778,
	6985,
	6985,
	4778,
	4778,
	6985,
	2864,
	2864,
	4494,
	2614,
	5562,
	4778,
	2614,
	5600,
	7102,
	3852,
	3852,
	4494,
	7102,
	3852,
	4494,
	2614,
	6869,
	5600,
	5562,
	4778,
	2614,
	7102,
	5600,
	6869,
	6985,
	6985,
	7102,
	7102,
	199,
	10235,
	416,
	1293,
	5562,
	5562,
	3816,
	4465,
	4465,
	5562,
	10294,
	7102,
	116,
	6985,
	5600,
	7102,
	2162,
	2162,
	7102,
	8925,
	8925,
	9827,
	3852,
	1647,
	4785,
	2153,
	10294,
	10008,
	10281,
	5600,
	2864,
	2878,
	945,
	6869,
	6985,
	4778,
	7102,
	168,
	6869,
	6869,
	1303,
	3852,
	4035,
	8926,
	6946,
	72,
	905,
	1647,
	471,
	657,
	7102,
	870,
	6869,
	5484,
	5692,
	5600,
	5600,
	5600,
	2956,
	905,
	5600,
	5600,
	1389,
	2306,
	2306,
	1242,
	9936,
	9608,
	9608,
	9608,
	9608,
	9608,
	9608,
	9616,
	9616,
	8909,
	8317,
	7497,
	7497,
	7818,
	9810,
	9810,
	9106,
	6946,
	6985,
	5949,
	7102,
	8483,
	10294,
	986,
	7102,
	2072,
	10294,
	2956,
	15,
	9605,
	5378,
	9424,
	9855,
	9711,
	9707,
	5562,
	6869,
	6946,
	5600,
	6869,
	6985,
	6985,
	7102,
	4785,
	7102,
	1302,
	5484,
	985,
	1322,
	1642,
	6869,
	5484,
	4785,
	2870,
	7102,
	5562,
	5562,
	5562,
	5562,
	6946,
	6946,
	6946,
	7102,
	5562,
	2583,
	1293,
	5562,
	2583,
	7102,
	5562,
	7102,
	5562,
	6946,
	4465,
	5562,
	2583,
	7102,
	5562,
	6946,
	4465,
	6946,
	4465,
	6946,
	6946,
	6946,
	6946,
	7087,
	3852,
	1601,
	7102,
	4966,
	6869,
	7102,
	1366,
	7102,
	2878,
	2878,
	6985,
	6985,
	7071,
	2583,
	1296,
	1295,
	1293,
	886,
	6869,
	6985,
	1293,
	6985,
	4778,
	6985,
	6985,
	6985,
	6985,
	6985,
	1130,
	5600,
	4778,
	6946,
	6946,
	9106,
	5600,
	5600,
	5562,
	6985,
	2134,
	6985,
	7102,
	4769,
	4769,
	6985,
	7087,
	6946,
	4966,
	9714,
	7087,
	3816,
	7102,
	7087,
	6985,
	4570,
	9714,
	7102,
	2583,
	2864,
	7102,
	4494,
	3816,
	3852,
	6869,
	6869,
	6869,
	6869,
	6869,
	6869,
	9616,
	9616,
	9616,
	6869,
	9616,
	1291,
	7102,
	7102,
	6869,
	5600,
	7102,
	7102,
	1246,
	6985,
	5692,
	5692,
	5600,
	5600,
	5562,
	7102,
	7102,
	7102,
	6869,
	7102,
	4785,
	6946,
	5562,
	7087,
	7102,
	5562,
	7102,
	4966,
	7087,
	4966,
	6946,
	10294,
	2849,
	6869,
	10256,
	6985,
	10294,
	7102,
	56,
	7102,
	7102,
	7102,
	-1,
	-1,
	-1,
	7102,
	4769,
	7102,
	1031,
	1031,
	7102,
	7102,
	7102,
	5562,
	6946,
	6946,
	1293,
	886,
	7102,
	3816,
	4465,
	4465,
	-1,
	118,
	2223,
	9810,
	7102,
	4785,
	2583,
	5562,
	2583,
	1293,
	4494,
	4465,
	1302,
	2870,
	10220,
	7102,
	7071,
	6947,
	6947,
	7102,
	10294,
	7102,
	5600,
	2870,
	5600,
	6985,
	5600,
	7102,
	4494,
	4778,
	6946,
	6985,
	6985,
	2864,
	6985,
	5600,
	6985,
	6985,
	6869,
	7102,
	7102,
	5600,
	6985,
	5600,
	5600,
	4769,
	4769,
	4769,
	4769,
	4769,
	4769,
	4769,
	4769,
	6985,
	6985,
	6985,
	10294,
	7102,
	2849,
	863,
	6869,
	6869,
	6946,
	5600,
	4494,
	6985,
	4769,
	2849,
	5600,
	4494,
	4769,
	7102,
	6869,
	5484,
	5600,
	4785,
	7102,
	2849,
	2534,
	6946,
	5600,
	4465,
	4494,
	6985,
	4769,
	7102,
	2849,
	2849,
	2849,
	2849,
	1334,
	6985,
	5600,
	9616,
	8910,
	9810,
	4494,
	6985,
	4769,
	5562,
	3852,
	6946,
	7102,
	5600,
	6985,
	6985,
	5600,
	3852,
	6946,
	10294,
	7102,
	5600,
	1341,
	2878,
	7102,
	7102,
	7102,
	7102,
	5484,
	6869,
	3852,
	6946,
	10294,
	7102,
	7102,
	7102,
	7102,
	7102,
	7102,
	7102,
	7102,
	5600,
	6985,
	3852,
	6946,
	10294,
	7102,
	5600,
	7102,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	6869,
	6869,
	7088,
	5693,
	7088,
	5693,
	5563,
	2865,
	2213,
	6946,
	6985,
	1642,
	3852,
	6946,
	10235,
	10294,
	5563,
	9820,
	9359,
	9031,
	9114,
	9114,
	8629,
	8152,
	9357,
	9950,
	9940,
	9950,
	7102,
	7102,
	7102,
	6985,
	7102,
	6985,
	5600,
	7102,
	6985,
	7102,
	6985,
	7102,
	6985,
	7102,
	7102,
	6985,
	7102,
	6985,
	7102,
	6985,
	7102,
	6985,
	7102,
	6985,
	7102,
	6985,
	7102,
	7102,
	1150,
	10294,
};
static const Il2CppTokenRangePair s_rgctxIndices[5] = 
{
	{ 0x02000067, { 2, 20 } },
	{ 0x02000069, { 22, 26 } },
	{ 0x0200006A, { 48, 5 } },
	{ 0x0200006B, { 53, 3 } },
	{ 0x06000296, { 0, 2 } },
};
extern const uint32_t g_rgctx_TU26_t633A31097C4ACDEA7F1CF739800D99D7461E2B39;
extern const uint32_t g_rgctx_T_t047CCAB016DADA45BF2B971B3E7C13F5D9EEAD31;
extern const uint32_t g_rgctx_Span_1_t44C40F6E179DE0B4F52D7DA2E1884C0EE6DE3FB3;
extern const uint32_t g_rgctx_ValueListBuilder_1_t7F5B0862C4E91CFE3E7AACC01A9A4D41595E20A2;
extern const uint32_t g_rgctx_TU5BU5D_tD81986F934513BED533ED6D53F0492758758629F;
extern const uint32_t g_rgctx_Span_1_get_Item_m80D3921AC76937AF0380B57568A09BBDA770B554;
extern const uint32_t g_rgctx_Span_1_t44C40F6E179DE0B4F52D7DA2E1884C0EE6DE3FB3;
extern const uint32_t g_rgctx_TU26_t67A6DFC9FB86C15F08315BE43C31B6A06047EAF6;
extern const uint32_t g_rgctx_Span_1_get_Length_m839FCF6D0CEBC13A23B4913B4B3BE1D2EF497540;
extern const uint32_t g_rgctx_ValueListBuilder_1_Grow_m64DA028E30FA837F9D8AA340D3A9344AA8CAEB90;
extern const uint32_t g_rgctx_ValueListBuilder_1_t7F5B0862C4E91CFE3E7AACC01A9A4D41595E20A2;
extern const uint32_t g_rgctx_T_t1A62CE1E223DA0C2F58D33DE37B955BF8862482B;
extern const uint32_t g_rgctx_Span_1_Slice_mD195CE80DA136235424FC8460CCC1F444AA8F4C1;
extern const uint32_t g_rgctx_Span_1_op_Implicit_m3BF14B5756BF4908C2A9A5E34B150191F65411C7;
extern const uint32_t g_rgctx_ReadOnlySpan_1_tE79F9BEAD399D2FE4DF1B222B7C84A1229A0FBF7;
extern const uint32_t g_rgctx_ArrayPool_1_get_Shared_mFF75CCDA4CFD179AEFF8DCACCB63C2F7A077C4BE;
extern const uint32_t g_rgctx_ArrayPool_1_t58F3AC60669102F215C83BF8491B2CFFADF20417;
extern const uint32_t g_rgctx_ArrayPool_1_t58F3AC60669102F215C83BF8491B2CFFADF20417;
extern const uint32_t g_rgctx_ArrayPool_1_Return_mFE24FCB54D3C02CA684BD71EA9152AED02E0B440;
extern const uint32_t g_rgctx_ArrayPool_1_Rent_m5EF306341F1ED22323168043C5B68D869254CC35;
extern const uint32_t g_rgctx_Span_1_op_Implicit_m951005873D5028B6640FA52DF3A3E88636FD12BD;
extern const uint32_t g_rgctx_Span_1_TryCopyTo_m42CCEFF44F507F716B350717B93A19A5C5529A78;
extern const uint32_t g_rgctx_LinkedList_1_t7397445C149BDCAF9D2BCC1925A63AA489329223;
extern const uint32_t g_rgctx_LinkedListNode_1_t9E9525296AF0CCB2ED20655A790C2E4CD8622A56;
extern const uint32_t g_rgctx_T_tB8CC012B03969318B6B5424CAE7A44EDE0AA6665;
extern const uint32_t g_rgctx_LinkedList_1_AddLast_m92E9EBD6A9A39368C44BA4FA908B1C7A20FBB251;
extern const uint32_t g_rgctx_LinkedList_1_ValidateNewNode_m5C8011CEF3761C503DA6B158D6D88EED65B22AAE;
extern const uint32_t g_rgctx_LinkedList_1_InternalInsertNodeToEmptyList_m260D83D9B9C9EA04987CB40AE53195CB13DF8DB9;
extern const uint32_t g_rgctx_LinkedList_1_InternalInsertNodeBefore_m05B6CF176F09A50E6D93416C84B856EC3E4BAF89;
extern const uint32_t g_rgctx_LinkedListNode_1__ctor_m20FB5915DCE814C5B5ABFF83B3DB1D13FD2002B2;
extern const uint32_t g_rgctx_LinkedListNode_1_get_Next_mB1A01A4FC5490DBA9D6A301ED56851E198C9B4BE;
extern const uint32_t g_rgctx_LinkedListNode_1_Invalidate_m92AA7C26274AB3F1B2C90AAD36ED50BA53479F31;
extern const uint32_t g_rgctx_LinkedList_1_Find_m05423F53A6D38259BBFB8C5A4DC487E2C2182B00;
extern const uint32_t g_rgctx_TU5BU5D_tB7945C667DD94B711E283C8F803CF9F9F3DF9C5E;
extern const uint32_t g_rgctx_LinkedList_1_get_Count_m9716A6902AA14DDA5DD9C943647E5A06A1DBB6E9;
extern const uint32_t g_rgctx_EqualityComparer_1_get_Default_m831FBA2817035A8EB2087E6CF99799DA6F98DAE3;
extern const uint32_t g_rgctx_EqualityComparer_1_t392B3893C4126DDA49A6D8C4161C74BC1ECB9674;
extern const uint32_t g_rgctx_EqualityComparer_1_t392B3893C4126DDA49A6D8C4161C74BC1ECB9674;
extern const uint32_t g_rgctx_EqualityComparer_1_Equals_m6B7ECAC5C3E3877985F42ACB5093D9C5A28CCEBF;
extern const uint32_t g_rgctx_Enumerator_t82440D780591BF278D7D247E299FBB6BA82EFC94;
extern const uint32_t g_rgctx_Enumerator__ctor_mE01A798BE61864D38C425E7B6BE7935FB7850D7D;
extern const uint32_t g_rgctx_LinkedList_1_GetEnumerator_m8083B3D2C3FAE458314291AD1BA07D81465BE251;
extern const uint32_t g_rgctx_IEnumerator_1_t320534D3BAF982233715DB633A60650A40579E29;
extern const uint32_t g_rgctx_LinkedList_1_InternalRemoveNode_mDA777B72523F891BA292ADFA7EEB3ED1C7889689;
extern const uint32_t g_rgctx_LinkedList_1_ValidateNode_m131AE8D054B03E0FBCE81CEED06D9D1EB793E9C5;
extern const uint32_t g_rgctx_TU5BU5D_tB7945C667DD94B711E283C8F803CF9F9F3DF9C5E;
extern const uint32_t g_rgctx_LinkedList_1_CopyTo_m7F21A67D5C258A0B54301C4A6A402621CC82D581;
extern const uint32_t g_rgctx_TU5BU5D_tB7945C667DD94B711E283C8F803CF9F9F3DF9C5E;
extern const uint32_t g_rgctx_LinkedList_1_t2BE688DBB804B0F90493D85355AF0536C5697D2D;
extern const uint32_t g_rgctx_Enumerator_tCADEF731D1220B7CE58F6606ED534A3F13AC177A;
extern const uint32_t g_rgctx_LinkedListNode_1_tB749E738FAD47D5A2B5D4F38A5755505747EC445;
extern const uint32_t g_rgctx_T_t40ED5F405E4C66916984D6B18DAD11FB5460B70E;
extern const uint32_t g_rgctx_LinkedList_1_get_Count_m00D92A248A5513B9C324C4F8A7EEAAE4A1AB9A85;
extern const uint32_t g_rgctx_T_tD35F114112C8EBEAD274D7F6267468B20051DAC1;
extern const uint32_t g_rgctx_LinkedListNode_1_tFCD18B72115D3A8805DDD98C9A8B73001D31CED9;
extern const uint32_t g_rgctx_LinkedList_1_t18ED4FB74A527D35562A422950F86F0D2AA3209C;
static const Il2CppRGCTXDefinition s_rgctxValues[56] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t633A31097C4ACDEA7F1CF739800D99D7461E2B39 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t047CCAB016DADA45BF2B971B3E7C13F5D9EEAD31 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Span_1_t44C40F6E179DE0B4F52D7DA2E1884C0EE6DE3FB3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ValueListBuilder_1_t7F5B0862C4E91CFE3E7AACC01A9A4D41595E20A2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tD81986F934513BED533ED6D53F0492758758629F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Span_1_get_Item_m80D3921AC76937AF0380B57568A09BBDA770B554 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Span_1_t44C40F6E179DE0B4F52D7DA2E1884C0EE6DE3FB3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU26_t67A6DFC9FB86C15F08315BE43C31B6A06047EAF6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Span_1_get_Length_m839FCF6D0CEBC13A23B4913B4B3BE1D2EF497540 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ValueListBuilder_1_Grow_m64DA028E30FA837F9D8AA340D3A9344AA8CAEB90 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ValueListBuilder_1_t7F5B0862C4E91CFE3E7AACC01A9A4D41595E20A2 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t1A62CE1E223DA0C2F58D33DE37B955BF8862482B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Span_1_Slice_mD195CE80DA136235424FC8460CCC1F444AA8F4C1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Span_1_op_Implicit_m3BF14B5756BF4908C2A9A5E34B150191F65411C7 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ReadOnlySpan_1_tE79F9BEAD399D2FE4DF1B222B7C84A1229A0FBF7 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArrayPool_1_get_Shared_mFF75CCDA4CFD179AEFF8DCACCB63C2F7A077C4BE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArrayPool_1_t58F3AC60669102F215C83BF8491B2CFFADF20417 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_ArrayPool_1_t58F3AC60669102F215C83BF8491B2CFFADF20417 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArrayPool_1_Return_mFE24FCB54D3C02CA684BD71EA9152AED02E0B440 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_ArrayPool_1_Rent_m5EF306341F1ED22323168043C5B68D869254CC35 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Span_1_op_Implicit_m951005873D5028B6640FA52DF3A3E88636FD12BD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Span_1_TryCopyTo_m42CCEFF44F507F716B350717B93A19A5C5529A78 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedList_1_t7397445C149BDCAF9D2BCC1925A63AA489329223 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedListNode_1_t9E9525296AF0CCB2ED20655A790C2E4CD8622A56 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tB8CC012B03969318B6B5424CAE7A44EDE0AA6665 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_AddLast_m92E9EBD6A9A39368C44BA4FA908B1C7A20FBB251 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_ValidateNewNode_m5C8011CEF3761C503DA6B158D6D88EED65B22AAE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_InternalInsertNodeToEmptyList_m260D83D9B9C9EA04987CB40AE53195CB13DF8DB9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_InternalInsertNodeBefore_m05B6CF176F09A50E6D93416C84B856EC3E4BAF89 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNode_1__ctor_m20FB5915DCE814C5B5ABFF83B3DB1D13FD2002B2 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNode_1_get_Next_mB1A01A4FC5490DBA9D6A301ED56851E198C9B4BE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNode_1_Invalidate_m92AA7C26274AB3F1B2C90AAD36ED50BA53479F31 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_Find_m05423F53A6D38259BBFB8C5A4DC487E2C2182B00 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tB7945C667DD94B711E283C8F803CF9F9F3DF9C5E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_get_Count_m9716A6902AA14DDA5DD9C943647E5A06A1DBB6E9 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EqualityComparer_1_get_Default_m831FBA2817035A8EB2087E6CF99799DA6F98DAE3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_t392B3893C4126DDA49A6D8C4161C74BC1ECB9674 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_EqualityComparer_1_t392B3893C4126DDA49A6D8C4161C74BC1ECB9674 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_EqualityComparer_1_Equals_m6B7ECAC5C3E3877985F42ACB5093D9C5A28CCEBF },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_t82440D780591BF278D7D247E299FBB6BA82EFC94 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Enumerator__ctor_mE01A798BE61864D38C425E7B6BE7935FB7850D7D },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_GetEnumerator_m8083B3D2C3FAE458314291AD1BA07D81465BE251 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_IEnumerator_1_t320534D3BAF982233715DB633A60650A40579E29 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_InternalRemoveNode_mDA777B72523F891BA292ADFA7EEB3ED1C7889689 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_ValidateNode_m131AE8D054B03E0FBCE81CEED06D9D1EB793E9C5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tB7945C667DD94B711E283C8F803CF9F9F3DF9C5E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_CopyTo_m7F21A67D5C258A0B54301C4A6A402621CC82D581 },
	{ (Il2CppRGCTXDataType)1, (const void *)&g_rgctx_TU5BU5D_tB7945C667DD94B711E283C8F803CF9F9F3DF9C5E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedList_1_t2BE688DBB804B0F90493D85355AF0536C5697D2D },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Enumerator_tCADEF731D1220B7CE58F6606ED534A3F13AC177A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedListNode_1_tB749E738FAD47D5A2B5D4F38A5755505747EC445 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t40ED5F405E4C66916984D6B18DAD11FB5460B70E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_get_Count_m00D92A248A5513B9C324C4F8A7EEAAE4A1AB9A85 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD35F114112C8EBEAD274D7F6267468B20051DAC1 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedListNode_1_tFCD18B72115D3A8805DDD98C9A8B73001D31CED9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedList_1_t18ED4FB74A527D35562A422950F86F0D2AA3209C },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_System_CodeGenModule;
const Il2CppCodeGenModule g_System_CodeGenModule = 
{
	"System.dll",
	772,
	s_methodPointers,
	9,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	5,
	s_rgctxIndices,
	56,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

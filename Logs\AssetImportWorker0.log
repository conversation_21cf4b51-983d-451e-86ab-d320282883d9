Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.30f1 (62b05ba0686a) revision 6467675'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
D:/My Project/Driving Simulator Game Z TEC
-logFile
Logs/AssetImportWorker0.log
-srvPort
53742
-job-worker-count
11
-background-job-worker-count
8
-asset-garbage-collectors
1
Successfully changed project path to: D:/My Project/Driving Simulator Game Z TEC
D:/My Project/Driving Simulator Game Z TEC
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [19760]  Target information:

Player connection [19760]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1934080354 [EditorId] 1934080354 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [19760]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1934080354 [EditorId] 1934080354 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [19760] Host joined multi-casting on [***********:54997]...
Player connection [19760] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 11
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.90 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.30f1 (62b05ba0686a)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Driving Simulator Game Z TEC/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56824
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.008792 seconds.
- Loaded All Assemblies, in  0.713 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 378 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.012 seconds
Domain Reload Profiling: 1721ms
	BeginReloadAssembly (223ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (91ms)
	LoadAllAssembliesAndSetupDomain (306ms)
		LoadAssemblies (220ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (300ms)
			TypeCache.Refresh (297ms)
				TypeCache.ScanAssembly (272ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1013ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (936ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (554ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (193ms)
			ProcessInitializeOnLoadMethodAttributes (97ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.293 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.28 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.020 seconds
Domain Reload Profiling: 2308ms
	BeginReloadAssembly (320ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (69ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (65ms)
	LoadAllAssembliesAndSetupDomain (815ms)
		LoadAssemblies (584ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (414ms)
			TypeCache.Refresh (313ms)
				TypeCache.ScanAssembly (284ms)
			BuildScriptInfoCaches (84ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1021ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (822ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (145ms)
			ProcessInitializeOnLoadAttributes (597ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 1.87 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 55 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4451 unused Assets / (1.5 MB). Loaded Objects now: 4999.
Memory consumption went from 112.9 MB to 111.3 MB.
Total: 11.314800 ms (FindLiveObjects: 1.020700 ms CreateObjectMapping: 0.525700 ms MarkObjects: 7.321700 ms  DeleteObjects: 2.443000 ms)

========================================================================
Received Import Request.
  Time since last request: 3154.177982 seconds.
  path: Assets/UNITYA~1 1.UNI
  artifactKey: Guid(f277b7691003e7543b8c65c4a09815f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UNITYA~1 1.UNI using Guid(f277b7691003e7543b8c65c4a09815f6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '979ba720f24c1af42bea43026ab6d5bb') in 0.0333011 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 2.430223 seconds.
  path: Assets/UNITYA~1.UNI
  artifactKey: Guid(113274c5c93a84147a410e0cda7830b9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UNITYA~1.UNI using Guid(113274c5c93a84147a410e0cda7830b9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '137dacd506edc45bce42b8d524e3f623') in 0.0014729 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.49 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4451 unused Assets / (1.5 MB). Loaded Objects now: 5011.
Memory consumption went from 102.9 MB to 101.4 MB.
Total: 10.461600 ms (FindLiveObjects: 0.690700 ms CreateObjectMapping: 0.444400 ms MarkObjects: 7.511800 ms  DeleteObjects: 1.813500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 287.456665 seconds.
  path: Assets/Helicopter
  artifactKey: Guid(7ce4e128a09054e499253084ef8af42d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Helicopter using Guid(7ce4e128a09054e499253084ef8af42d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0f62405853430e9fac7a537a8961a84c') in 0.0022226 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 2.25 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4442 unused Assets / (1.7 MB). Loaded Objects now: 5002.
Memory consumption went from 102.9 MB to 101.2 MB.
Total: 12.716200 ms (FindLiveObjects: 0.891000 ms CreateObjectMapping: 0.536200 ms MarkObjects: 8.380600 ms  DeleteObjects: 2.906500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.241 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.36 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.078 seconds
Domain Reload Profiling: 2318ms
	BeginReloadAssembly (365ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (86ms)
	RebuildCommonClasses (70ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (729ms)
		LoadAssemblies (604ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (330ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (294ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1078ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (861ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (158ms)
			ProcessInitializeOnLoadAttributes (620ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.92 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4449 unused Assets / (1.6 MB). Loaded Objects now: 5002.
Memory consumption went from 104.6 MB to 103.0 MB.
Total: 14.381000 ms (FindLiveObjects: 1.118400 ms CreateObjectMapping: 0.478400 ms MarkObjects: 9.549900 ms  DeleteObjects: 3.232600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.143 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.46 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.105 seconds
Domain Reload Profiling: 2248ms
	BeginReloadAssembly (332ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (81ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (679ms)
		LoadAssemblies (565ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (297ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (267ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1106ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (873ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (22ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (153ms)
			ProcessInitializeOnLoadAttributes (625ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 1.44 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4449 unused Assets / (1.5 MB). Loaded Objects now: 5002.
Memory consumption went from 104.6 MB to 103.1 MB.
Total: 11.125200 ms (FindLiveObjects: 0.922000 ms CreateObjectMapping: 0.517400 ms MarkObjects: 6.791500 ms  DeleteObjects: 2.892200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.246 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.35 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.019 seconds
Domain Reload Profiling: 2264ms
	BeginReloadAssembly (363ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (81ms)
	RebuildCommonClasses (72ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (737ms)
		LoadAssemblies (594ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (351ms)
			TypeCache.Refresh (147ms)
				TypeCache.ScanAssembly (130ms)
			BuildScriptInfoCaches (186ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1020ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (806ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (183ms)
			ProcessInitializeOnLoadAttributes (547ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.37 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 25 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4477 unused Assets / (2.3 MB). Loaded Objects now: 5019.
Memory consumption went from 107.9 MB to 105.6 MB.
Total: 9.503500 ms (FindLiveObjects: 0.689700 ms CreateObjectMapping: 0.471500 ms MarkObjects: 5.818700 ms  DeleteObjects: 2.521700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 3437.418854 seconds.
  path: Assets/AdvancedHelicopterController/Scripts
  artifactKey: Guid(14dabc33f510440488eaededb9a56ee0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Scripts using Guid(14dabc33f510440488eaededb9a56ee0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cde20de122ce5b2fbecac029375a6ccc') in 0.0267411 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.317917 seconds.
  path: Assets/AdvancedHelicopterController/Scenes/SampleScene.unity
  artifactKey: Guid(ed99b7de30970ef4bac5ddee481e216d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Scenes/SampleScene.unity using Guid(ed99b7de30970ef4bac5ddee481e216d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '08516c64bb7086f58fcc3ce969d6c1d3') in 0.0009387 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.247 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.39 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.109 seconds
Domain Reload Profiling: 2355ms
	BeginReloadAssembly (347ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (84ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (61ms)
	initialDomainReloadingComplete (74ms)
	LoadAllAssembliesAndSetupDomain (698ms)
		LoadAssemblies (594ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (297ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (266ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1110ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (894ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (161ms)
			ProcessInitializeOnLoadAttributes (638ms)
			ProcessInitializeOnLoadMethodAttributes (58ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.39 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 25 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4477 unused Assets / (2.4 MB). Loaded Objects now: 5019.
Memory consumption went from 107.9 MB to 105.5 MB.
Total: 10.943900 ms (FindLiveObjects: 0.728900 ms CreateObjectMapping: 0.492100 ms MarkObjects: 7.055300 ms  DeleteObjects: 2.666200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.321 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.18 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.095 seconds
Domain Reload Profiling: 2414ms
	BeginReloadAssembly (368ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (77ms)
	RebuildCommonClasses (136ms)
	RebuildNativeTypeToScriptingClass (35ms)
	initialDomainReloadingComplete (60ms)
	LoadAllAssembliesAndSetupDomain (720ms)
		LoadAssemblies (605ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (314ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (285ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1096ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (879ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (24ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (161ms)
			ProcessInitializeOnLoadAttributes (623ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (12ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.37 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 25 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4477 unused Assets / (2.4 MB). Loaded Objects now: 5019.
Memory consumption went from 107.9 MB to 105.5 MB.
Total: 10.543200 ms (FindLiveObjects: 0.813800 ms CreateObjectMapping: 0.479300 ms MarkObjects: 6.488400 ms  DeleteObjects: 2.760600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.284 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.19 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.064 seconds
Domain Reload Profiling: 2347ms
	BeginReloadAssembly (348ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (73ms)
	RebuildCommonClasses (133ms)
	RebuildNativeTypeToScriptingClass (36ms)
	initialDomainReloadingComplete (66ms)
	LoadAllAssembliesAndSetupDomain (698ms)
		LoadAssemblies (584ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (318ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (286ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1065ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (837ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (151ms)
			ProcessInitializeOnLoadAttributes (596ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (12ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.31 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 25 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4477 unused Assets / (2.2 MB). Loaded Objects now: 5019.
Memory consumption went from 107.9 MB to 105.7 MB.
Total: 9.539800 ms (FindLiveObjects: 0.696600 ms CreateObjectMapping: 0.480900 ms MarkObjects: 5.730300 ms  DeleteObjects: 2.630300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.262 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.38 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.126 seconds
Domain Reload Profiling: 2385ms
	BeginReloadAssembly (323ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (81ms)
	RebuildCommonClasses (61ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (122ms)
	LoadAllAssembliesAndSetupDomain (729ms)
		LoadAssemblies (578ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (319ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (287ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1127ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (887ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (160ms)
			ProcessInitializeOnLoadAttributes (625ms)
			ProcessInitializeOnLoadMethodAttributes (64ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.43 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 25 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4477 unused Assets / (2.4 MB). Loaded Objects now: 5019.
Memory consumption went from 107.9 MB to 105.6 MB.
Total: 13.049800 ms (FindLiveObjects: 0.754300 ms CreateObjectMapping: 0.546500 ms MarkObjects: 8.501000 ms  DeleteObjects: 3.246800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.329 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.19 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.072 seconds
Domain Reload Profiling: 2398ms
	BeginReloadAssembly (352ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (79ms)
	RebuildCommonClasses (70ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (118ms)
	LoadAllAssembliesAndSetupDomain (759ms)
		LoadAssemblies (627ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (327ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (296ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1073ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (868ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (171ms)
			ProcessInitializeOnLoadAttributes (605ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (13ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.43 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 25 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4477 unused Assets / (2.3 MB). Loaded Objects now: 5019.
Memory consumption went from 107.1 MB to 104.7 MB.
Total: 10.107300 ms (FindLiveObjects: 0.752200 ms CreateObjectMapping: 0.482100 ms MarkObjects: 6.008500 ms  DeleteObjects: 2.863300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 519.606991 seconds.
  path: Assets/Scenes/TPSFREEHAND.unity
  artifactKey: Guid(12a4d75da2db4d3439c09e07c73a85b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/TPSFREEHAND.unity using Guid(12a4d75da2db4d3439c09e07c73a85b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6d7c2021ccb1b1066762a956431e1687') in 0.0177958 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 44.254683 seconds.
  path: Assets/AdvancedHelicopterController/Prefabs/Collectables/Particles/TextureMaterial/WFX_M_GlowCircle Add.mat
  artifactKey: Guid(e675b378574b34465aa64f9a725bab33) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Prefabs/Collectables/Particles/TextureMaterial/WFX_M_GlowCircle Add.mat using Guid(e675b378574b34465aa64f9a725bab33) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '019cdbd42109a0712320702c1bec596a') in 0.2559941 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 12.169279 seconds.
  path: Assets/AdvancedHelicopterController/Models/Helicopter/Custom_Helicopter_Model.fbx
  artifactKey: Guid(b7f2b9b96b2a1452e8bbaa16b70b65a0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Models/Helicopter/Custom_Helicopter_Model.fbx using Guid(b7f2b9b96b2a1452e8bbaa16b70b65a0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3afb431c31fe6581e842311141f6748e') in 0.2375097 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.307 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.30 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.053 seconds
Domain Reload Profiling: 2358ms
	BeginReloadAssembly (359ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (87ms)
	RebuildCommonClasses (72ms)
	RebuildNativeTypeToScriptingClass (34ms)
	initialDomainReloadingComplete (57ms)
	LoadAllAssembliesAndSetupDomain (782ms)
		LoadAssemblies (629ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (351ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (314ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1054ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (845ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (170ms)
			ProcessInitializeOnLoadAttributes (583ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (13ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.39 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 25 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4476 unused Assets / (2.3 MB). Loaded Objects now: 5063.
Memory consumption went from 111.6 MB to 109.3 MB.
Total: 10.182400 ms (FindLiveObjects: 0.849100 ms CreateObjectMapping: 0.519000 ms MarkObjects: 6.211400 ms  DeleteObjects: 2.601100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.169 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 2.17 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.103 seconds
Domain Reload Profiling: 2271ms
	BeginReloadAssembly (341ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (70ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (680ms)
		LoadAssemblies (573ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (307ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (277ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1104ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (874ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (166ms)
			ProcessInitializeOnLoadAttributes (619ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 2.02 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4476 unused Assets / (2.3 MB). Loaded Objects now: 5063.
Memory consumption went from 111.3 MB to 109.0 MB.
Total: 14.031800 ms (FindLiveObjects: 0.890200 ms CreateObjectMapping: 0.521200 ms MarkObjects: 9.207500 ms  DeleteObjects: 3.411200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.971 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.35 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.037 seconds
Domain Reload Profiling: 3008ms
	BeginReloadAssembly (634ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (36ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (202ms)
	RebuildCommonClasses (61ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (63ms)
	LoadAllAssembliesAndSetupDomain (1191ms)
		LoadAssemblies (1022ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (408ms)
			TypeCache.Refresh (158ms)
				TypeCache.ScanAssembly (135ms)
			BuildScriptInfoCaches (227ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1038ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (834ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (190ms)
			ProcessInitializeOnLoadAttributes (565ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.38 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4576 unused Assets / (2.3 MB). Loaded Objects now: 5163.
Memory consumption went from 112.3 MB to 110.0 MB.
Total: 11.144600 ms (FindLiveObjects: 0.783700 ms CreateObjectMapping: 0.551000 ms MarkObjects: 6.838400 ms  DeleteObjects: 2.969700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 5404.927508 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Demo/UI_Input_PhysicsBasedTrain.unity
  artifactKey: Guid(6837c76db2116214eb1ee91a41cfc0d9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Demo/UI_Input_PhysicsBasedTrain.unity using Guid(6837c76db2116214eb1ee91a41cfc0d9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6ded81e35b378b5299b828496659b31e') in 0.0449908 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.307 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.31 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.040 seconds
Domain Reload Profiling: 2347ms
	BeginReloadAssembly (349ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (92ms)
	RebuildCommonClasses (61ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (827ms)
		LoadAssemblies (664ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (346ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (312ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1041ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (812ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (156ms)
			ProcessInitializeOnLoadAttributes (573ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.54 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4576 unused Assets / (2.1 MB). Loaded Objects now: 5163.
Memory consumption went from 112.4 MB to 110.3 MB.
Total: 11.977100 ms (FindLiveObjects: 1.081400 ms CreateObjectMapping: 0.845600 ms MarkObjects: 7.085400 ms  DeleteObjects: 2.962400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.292 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.18 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.040 seconds
Domain Reload Profiling: 2331ms
	BeginReloadAssembly (382ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (97ms)
	RebuildCommonClasses (71ms)
	RebuildNativeTypeToScriptingClass (27ms)
	initialDomainReloadingComplete (50ms)
	LoadAllAssembliesAndSetupDomain (761ms)
		LoadAssemblies (654ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (313ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (280ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1041ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (834ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (150ms)
			ProcessInitializeOnLoadAttributes (593ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (13ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.55 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4576 unused Assets / (2.7 MB). Loaded Objects now: 5163.
Memory consumption went from 112.4 MB to 109.7 MB.
Total: 13.508000 ms (FindLiveObjects: 0.799700 ms CreateObjectMapping: 0.614200 ms MarkObjects: 8.236000 ms  DeleteObjects: 3.856800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.168 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.23 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.100 seconds
Domain Reload Profiling: 2268ms
	BeginReloadAssembly (334ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (80ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (703ms)
		LoadAssemblies (588ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (292ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (261ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1101ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (892ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (163ms)
			ProcessInitializeOnLoadAttributes (637ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (12ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.51 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4576 unused Assets / (2.2 MB). Loaded Objects now: 5163.
Memory consumption went from 112.4 MB to 110.2 MB.
Total: 10.007600 ms (FindLiveObjects: 0.712500 ms CreateObjectMapping: 0.525000 ms MarkObjects: 6.212800 ms  DeleteObjects: 2.556100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 740.472538 seconds.
  path: Assets/Scenes/TPSFREEHAND.unity
  artifactKey: Guid(12a4d75da2db4d3439c09e07c73a85b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scenes/TPSFREEHAND.unity using Guid(12a4d75da2db4d3439c09e07c73a85b2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e40b879442d53bbca6f924885855dbce') in 0.0114167 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 793.910532 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Sample Trains/Freight Train Sample Profile (Physics Based).asset
  artifactKey: Guid(d3d7636d7dafb554fa7fc583929b1666) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Sample Trains/Freight Train Sample Profile (Physics Based).asset using Guid(d3d7636d7dafb554fa7fc583929b1666) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '001cc939d81e882f883df6864ce61ac7') in 0.2576082 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2774

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Sample Trains/Steam Train Sample Profile (Physics Based).asset
  artifactKey: Guid(c3a8a97b9dea5744994629b0bbd655f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Sample Trains/Steam Train Sample Profile (Physics Based).asset using Guid(c3a8a97b9dea5744994629b0bbd655f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bb9a1ae90b0fe1e7a66ffffeb9887a5a') in 0.2965234 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6860

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Sample Trains/Subway Sample Profile (Spline Based).asset
  artifactKey: Guid(dab720db2d507c6489ee9f650aad6bb1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Sample Trains/Subway Sample Profile (Spline Based).asset using Guid(dab720db2d507c6489ee9f650aad6bb1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '68f536d18d7535d1ee44abf87fec0a31') in 0.2379967 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3649

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Sample Trains/Steam Train Sample Profile (Spline Based).asset
  artifactKey: Guid(a1d3c2a420dbdba489b7ac3ffa35e393) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Sample Trains/Steam Train Sample Profile (Spline Based).asset using Guid(a1d3c2a420dbdba489b7ac3ffa35e393) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f475236fe7022cf1e5e8448c841a4ea9') in 0.2251136 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6331

========================================================================
Received Import Request.
  Time since last request: 104.079397 seconds.
  path: Assets/AdvancedHelicopterController/Prefabs/Helicopter/AdvancedHelicopterSystem.prefab
  artifactKey: Guid(7352086092189489cb23959e59f776cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Prefabs/Helicopter/AdvancedHelicopterSystem.prefab using Guid(7352086092189489cb23959e59f776cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '99635357a4947b94b4d6e755471f5c2d') in 0.8332423 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 829

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/MSK 2.2/Models/Player/Animations1/Back.FBX
  artifactKey: Guid(dc256bf8754f2e74e924994f674cb39d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MSK 2.2/Models/Player/Animations1/Back.FBX using Guid(dc256bf8754f2e74e924994f674cb39d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fafced02e117877b35af926af3d0b677') in 0.1560852 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 77

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/AdvancedHelicopterController/Prefabs/Bullets/Bullet.prefab
  artifactKey: Guid(db080674001e04599bd7cbbae2cbafb6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Prefabs/Bullets/Bullet.prefab using Guid(db080674001e04599bd7cbbae2cbafb6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '63892056d18d1bc64cf4a33ebb6a0342') in 0.0257636 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Bars/Bars_03.prefab
  artifactKey: Guid(0452b0ec3d0da9740b7d771f95cec992) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Bars/Bars_03.prefab using Guid(0452b0ec3d0da9740b7d771f95cec992) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '594f757a218e0d47d70e75f5debbe6be') in 0.4013353 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/SFX/BellSFX.prefab
  artifactKey: Guid(d3752432903c8684f991d185ad107863) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/SFX/BellSFX.prefab using Guid(d3752432903c8684f991d185ad107863) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'eadc18101804e3e82c6e38c530cbb81a') in 0.0216298 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/UnityTechnologies/EffectExamples/Blood/Prefabs/BloodSprayEffect.prefab
  artifactKey: Guid(abb9f519b142e304d9c45b56afa768bc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/Blood/Prefabs/BloodSprayEffect.prefab using Guid(abb9f519b142e304d9c45b56afa768bc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '73dadee4af19ad761c85f93d853cb062') in 0.2987687 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Bars/BarsSet_02.prefab
  artifactKey: Guid(36962df9ecf9c1a409e84cd54b01a856) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Bars/BarsSet_02.prefab using Guid(36962df9ecf9c1a409e84cd54b01a856) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cc0d82ed7165270c279209bd15876a3a') in 0.0944612 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 25

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/UnityTechnologies/EffectExamples/WeaponEffects/Prefabs/BulletImpactFleshBigEffect.prefab
  artifactKey: Guid(e5a3d6da48298a0458c1990f58c27069) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/WeaponEffects/Prefabs/BulletImpactFleshBigEffect.prefab using Guid(e5a3d6da48298a0458c1990f58c27069) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2bf5828e551de0e85e28dcba84f5d4aa') in 0.4072355 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 34

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/MSK 2.2/Standard Assets/Projectors/Blob Light Projector.prefab
  artifactKey: Guid(b0a1f6772f39e47fdae4d55c17d8ac35) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MSK 2.2/Standard Assets/Projectors/Blob Light Projector.prefab using Guid(b0a1f6772f39e47fdae4d55c17d8ac35) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8a5216560ac4d6b49f607655078eb2b2') in 0.0038249 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Benches/Bench_02_Blue.prefab
  artifactKey: Guid(884f589279fe3ba45b9817b4609f45bc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Benches/Bench_02_Blue.prefab using Guid(884f589279fe3ba45b9817b4609f45bc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '032cf307d874eb0e542a66feafe06b54') in 0.0977047 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/Player/AirPlane.prefab
  artifactKey: Guid(e02069134aab38a4c8f42286574e3b3e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Player/AirPlane.prefab using Guid(e02069134aab38a4c8f42286574e3b3e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '21943f90b231ae033691d1f52c01f84f') in 1.2904662 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 756

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Bars_02.fbx
  artifactKey: Guid(01c05f01116e7df4fb03b7f401b7e0a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Bars_02.fbx using Guid(01c05f01116e7df4fb03b7f401b7e0a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a2ce46774eba11ea91d374fac1d054bd') in 0.207447 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000057 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Benches/Bench_03_LightBrown.prefab
  artifactKey: Guid(a49251b12696a3a4eb2927291985c581) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Benches/Bench_03_LightBrown.prefab using Guid(a49251b12696a3a4eb2927291985c581) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0e030df11f62295f5e34fa6777e2e9ec') in 0.109975 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 25

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/MSK 2.2/Models/Tree/Alder.prefab
  artifactKey: Guid(158c0677c0823e3429d069ac40a33a64) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MSK 2.2/Models/Tree/Alder.prefab using Guid(158c0677c0823e3429d069ac40a33a64) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '77c4192283fd66ed01e6db03cc610290') in 0.1588707 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Bars/BarsSet_01.prefab
  artifactKey: Guid(0822a7c463db40e49b729961feaf5eb8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Bars/BarsSet_01.prefab using Guid(0822a7c463db40e49b729961feaf5eb8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dc0e65805c5dc256d06b52a545346648') in 0.1096405 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000070 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Bars_01.fbx
  artifactKey: Guid(b08da9e9a14d043408038060dc116139) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Bars_01.fbx using Guid(b08da9e9a14d043408038060dc116139) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fefc06d41b9fdf1c50888d79f41e7e19') in 0.1129692 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Benches/Bench_01_LightBrown.prefab
  artifactKey: Guid(b4d379917206b1540bdded3e84676573) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Benches/Bench_01_LightBrown.prefab using Guid(b4d379917206b1540bdded3e84676573) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1895296aa55688e91b2963aa3faf98cc') in 0.1005173 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Bars_03.fbx
  artifactKey: Guid(9e38bc78a9427b140a67b95b9c6a1434) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Bars_03.fbx using Guid(9e38bc78a9427b140a67b95b9c6a1434) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7ce5c99b5138753bc35c95ae67894401') in 0.0960067 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 69.033356 seconds.
  path: Assets/UnityTechnologies/EffectExamples/WeaponEffects/Prefabs/BulletImpactWaterContainerEffect.prefab
  artifactKey: Guid(a60a75fdadab5de428529a5b8ef0426a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/WeaponEffects/Prefabs/BulletImpactWaterContainerEffect.prefab using Guid(a60a75fdadab5de428529a5b8ef0426a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0789a6f2ab16887a683aaae3e12e198b') in 0.1167848 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Bumpers_60cm_low.fbx
  artifactKey: Guid(8ef35c3d551d0a048911bd66333de32a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Bumpers_60cm_low.fbx using Guid(8ef35c3d551d0a048911bd66333de32a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1ada20a8f2513cc9bfb86f1d3b58f188') in 0.126523 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Misc/Button_01_Red.prefab
  artifactKey: Guid(9cbcb5291e5627947b08a9bb3b62e581) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Misc/Button_01_Red.prefab using Guid(9cbcb5291e5627947b08a9bb3b62e581) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2bb935bebfc8ba41294e39424e2357ef') in 0.1133753 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Models/CableCar_01_WoodenBench_Short.fbx
  artifactKey: Guid(9754e6c53e63d114eb38357bbe74e743) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Models/CableCar_01_WoodenBench_Short.fbx using Guid(9754e6c53e63d114eb38357bbe74e743) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '35b1f1b4946d654abba742770665c7b8') in 0.8539532 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/SFX/ConnectionSFX.prefab
  artifactKey: Guid(d7d6059667a633a4fb308b776175fd5a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/SFX/ConnectionSFX.prefab using Guid(d7d6059667a633a4fb308b776175fd5a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f7004069791d80dd5ea27161c9e3c642') in 0.0142473 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/_Prefabs/Parts/Wheels/CableCar_01_WheelsTruck_visual.prefab
  artifactKey: Guid(6feb3a3a40463594fb2b5b40e408e22d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/_Prefabs/Parts/Wheels/CableCar_01_WheelsTruck_visual.prefab using Guid(6feb3a3a40463594fb2b5b40e408e22d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7c81adf729c401c513a9c14fea890309') in 0.6125044 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 39

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/ClosedLoop_prefab.prefab
  artifactKey: Guid(f362de180b85b2e43bbc0a90d92ff40a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/ClosedLoop_prefab.prefab using Guid(f362de180b85b2e43bbc0a90d92ff40a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c3d3a4ee9858f3b8e642f349dd8f6f96') in 0.6039308 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 63

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Cable Car Rails/Models/CableCar_RailSegment_GuideCollider.fbx
  artifactKey: Guid(42c57014798c62c4d98aef9e00f61e76) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Cable Car Rails/Models/CableCar_RailSegment_GuideCollider.fbx using Guid(42c57014798c62c4d98aef9e00f61e76) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6b8a5a8cc8c746b8d5fad43889102fbd') in 0.0677765 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Cable Car Rails/Models/CableCar_RailSegment_LOD1.fbx
  artifactKey: Guid(fcdfaf5dde0b7c246b5d723dd948fe23) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Cable Car Rails/Models/CableCar_RailSegment_LOD1.fbx using Guid(fcdfaf5dde0b7c246b5d723dd948fe23) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3e279c29ab2a8be2e4c8b66c955ab089') in 0.5236891 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/_Prefabs/Parts/Wheels/CableCar_01_WheelsTruck_physics.prefab
  artifactKey: Guid(9a56638dbbd78254fa670c6f2b3807d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/_Prefabs/Parts/Wheels/CableCar_01_WheelsTruck_physics.prefab using Guid(9a56638dbbd78254fa670c6f2b3807d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a66a23b3e1437f528e62d86f03b1d861') in 0.0041529 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 33

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/_Prefabs/CableCar_01_Red.prefab
  artifactKey: Guid(4a62ee4c38f4ece4b9e101a39a2b186d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/_Prefabs/CableCar_01_Red.prefab using Guid(4a62ee4c38f4ece4b9e101a39a2b186d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '531ded93f968351fab4a1e6a4d5e9e55') in 4.2265219 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 794

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/Models/CargoWagon02.fbx
  artifactKey: Guid(a0e5e2e7c3384be42951f4625716d1d5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/Models/CargoWagon02.fbx using Guid(a0e5e2e7c3384be42951f4625716d1d5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f46a7084581c31f4dccd32f2dffd86a7') in 0.1914449 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/UnityTechnologies/EffectExamples/WeaponEffects/Prefabs/CartridgeEjectEffect.prefab
  artifactKey: Guid(7ca96822084510147adb2aea03d45e3b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/WeaponEffects/Prefabs/CartridgeEjectEffect.prefab using Guid(7ca96822084510147adb2aea03d45e3b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '185c7bec116873c2c922514aa45b9119') in 0.2257662 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Misc/Button_01_Blue.prefab
  artifactKey: Guid(a9c8efde0d6661140a7554889366bc58) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Misc/Button_01_Blue.prefab using Guid(a9c8efde0d6661140a7554889366bc58) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd0c230a8b8433d40574fd6a396635dea') in 0.1386895 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Models/CableCar_01_Door.fbx
  artifactKey: Guid(6ef3f2d550313084c91b423b58a77bdc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Models/CableCar_01_Door.fbx using Guid(6ef3f2d550313084c91b423b58a77bdc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'eb215b2955e06da5b0562bf12e3bdf27') in 2.5570019 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Control Zones/CustomEventZone.prefab
  artifactKey: Guid(8b2f7e5bae8a213459ec8053fbe23d0d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Control Zones/CustomEventZone.prefab using Guid(8b2f7e5bae8a213459ec8053fbe23d0d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a4aaf243d12d87258f77d6930c4e716a') in 0.0039131 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Misc/Container_Red_12_19.prefab
  artifactKey: Guid(446a197fa0c2cb740aebe5d2c905aff0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Misc/Container_Red_12_19.prefab using Guid(446a197fa0c2cb740aebe5d2c905aff0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ea6c3fdaf5f53b59caff96908c0dfacf') in 0.1722516 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/SFX/DoorCloseSFX.prefab
  artifactKey: Guid(32112702707c57c48aad9faaf60411a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/SFX/DoorCloseSFX.prefab using Guid(32112702707c57c48aad9faaf60411a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3e65fd4d8dfbdf0c862939818f3cc50b') in 0.0061074 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Doors/DoubleDoor.prefab
  artifactKey: Guid(28145abaf928f354e95e42f87fb598d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Doors/DoubleDoor.prefab using Guid(28145abaf928f354e95e42f87fb598d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e60863bc9a64e7456762df5dba884a2e') in 0.4907689 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 49

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/UnityTechnologies/EffectExamples/FireExplosionEffects/Prefabs/FlamesParticleEffect.prefab
  artifactKey: Guid(3228a22491941734bb83ad691d17fcb9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/FireExplosionEffects/Prefabs/FlamesParticleEffect.prefab using Guid(3228a22491941734bb83ad691d17fcb9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '420de0220fa99798df855d5ea1039a20') in 0.2711539 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 21

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/UnityTechnologies/EffectExamples/FireExplosionEffects/Prefabs/FlameThrowerEffect.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/FireExplosionEffects/Prefabs/FlameThrowerEffect.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b09346677fa14b287ba51e78647b8d58') in 0.3193295 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 56

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/AdvancedHelicopterController/Prefabs/Helicopter/HelicopterComponents/Particle/Helicopter_Explosion_Particle.prefab
  artifactKey: Guid(cf1075a1b4e2741aeb2a825679d4c720) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Prefabs/Helicopter/HelicopterComponents/Particle/Helicopter_Explosion_Particle.prefab using Guid(cf1075a1b4e2741aeb2a825679d4c720) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd3374b65f726b4ab8f646524b40c2882') in 0.033858 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 46

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/Game Player Model/Game Character.fbx
  artifactKey: Guid(a4c93fa2c7efea34c9aa948e0bb8c27a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Game Player Model/Game Character.fbx using Guid(a4c93fa2c7efea34c9aa948e0bb8c27a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '000bbfadb76c41fbb6b73da638fef554') in 0.2174453 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 240

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_CW_6_prefab.prefab
  artifactKey: Guid(493498a7afc6a174ca50181e14d56f6b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_CW_6_prefab.prefab using Guid(493498a7afc6a174ca50181e14d56f6b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'daf066548d1cefa1b6ecf8cc0a105ae2') in 0.4629996 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 64

========================================================================
Received Import Request.
  Time since last request: 0.000058 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Monitor_01_Medium.fbx
  artifactKey: Guid(1d99992929367024b9d47742d0ec1509) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Monitor_01_Medium.fbx using Guid(1d99992929367024b9d47742d0ec1509) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e16fa09b19f631aac109b022694ac98c') in 0.0967777 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_ACW_1_prefab.prefab
  artifactKey: Guid(d2819cd1e8dea234c9ddb97bf6019a32) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_ACW_1_prefab.prefab using Guid(d2819cd1e8dea234c9ddb97bf6019a32) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7a058292cf413d591eb439d59524a7f3') in 0.4429188 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 64

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Parts/SFX/SteamLocomotiveWhistleSFX.prefab
  artifactKey: Guid(aa80abb15add1634588a4a43fa27762d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Parts/SFX/SteamLocomotiveWhistleSFX.prefab using Guid(aa80abb15add1634588a4a43fa27762d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e41e37ab67e1e1840a8bfb5bccc4f66b') in 0.0210262 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Locomotive_01_Orange_Black.prefab
  artifactKey: Guid(7757500491a5dcd498e948e2ab2f6d51) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Locomotive_01_Orange_Black.prefab using Guid(7757500491a5dcd498e948e2ab2f6d51) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a9d3d5c5f7df35a43668dc6998faf7ab') in 3.8666933 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1216

========================================================================
Received Import Request.
  Time since last request: 0.000071 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Misc/Switch_01_Blue.prefab
  artifactKey: Guid(d37010c2df9f80647842acbab036e536) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Misc/Switch_01_Blue.prefab using Guid(d37010c2df9f80647842acbab036e536) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4cad988ad261ce2bb00304b91a7c8b24') in 0.2280058 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/Models/TenderWagon_01_WheelsTruck.fbx
  artifactKey: Guid(1cfb642562272024a923694c0f1b6e98) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/Models/TenderWagon_01_WheelsTruck.fbx using Guid(1cfb642562272024a923694c0f1b6e98) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b165ef579ea763a903c8f02205c7bf95') in 0.8757069 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000053 seconds.
  path: Assets/Invector-3rdPersonController_LITE/3D Models/Others/NewAssets/support.prefab
  artifactKey: Guid(5bb3244b3f1f9a7419e850ed4755f3a3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Invector-3rdPersonController_LITE/3D Models/Others/NewAssets/support.prefab using Guid(5bb3244b3f1f9a7419e850ed4755f3a3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '24557d465cfb2b11eb827c28d7951b11') in 0.2119817 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Import Request.
  Time since last request: 0.000073 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/SubwayCart_01_A.prefab
  artifactKey: Guid(f6df241377f6aaf4281239e4bb371d8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/SubwayCart_01_A.prefab using Guid(f6df241377f6aaf4281239e4bb371d8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bdde98bcf77c42e852065f0671e6c044') in 2.5817461 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1441

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Subway_01_Door_Double_Left.fbx
  artifactKey: Guid(b4b7411e419b81a48b398d0f766cc7df) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Subway_01_Door_Double_Left.fbx using Guid(b4b7411e419b81a48b398d0f766cc7df) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'efdf3cfaa224535c27cfa97b6f6e670e') in 0.2146775 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000066 seconds.
  path: Assets/UnityTechnologies/EffectExamples/Shared/Prefabs/Stone.prefab
  artifactKey: Guid(6b4eed762f985a04bbc740b9da12e38d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/Shared/Prefabs/Stone.prefab using Guid(6b4eed762f985a04bbc740b9da12e38d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c829e206bfb43dcb71bb9604f3f88463') in 0.3008153 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000199 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Locomotive_01/Locomotive_01_LOD_2.fbx
  artifactKey: Guid(d7d5317a401a7664a904b419629ef1e3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Locomotive_01/Locomotive_01_LOD_2.fbx using Guid(d7d5317a401a7664a904b419629ef1e3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a9e58014606aaee49a03fb14814e2637') in 1.1388566 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000064 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/JanneyCoupler_low.fbx
  artifactKey: Guid(4b8534c8328df814887ba6bed6e09db3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/JanneyCoupler_low.fbx using Guid(4b8534c8328df814887ba6bed6e09db3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'eac55eb3e38afc3f7998f283553727f9') in 0.1477562 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/SubwayCart_01_C.prefab
  artifactKey: Guid(df15c58b67ba656419fef2b40afae551) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/SubwayCart_01_C.prefab using Guid(df15c58b67ba656419fef2b40afae551) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b9a6a3dbcb2877639ef44207282d6235') in 2.3505559 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1441

========================================================================
Received Import Request.
  Time since last request: 45.493438 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/Models/PassengerWagon_WheelsTruck.fbx
  artifactKey: Guid(4c22251ff0f178a46a669f1576db2826) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/Models/PassengerWagon_WheelsTruck.fbx using Guid(4c22251ff0f178a46a669f1576db2826) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c8ba3b407c90d8d2ffb589b35bf44113') in 0.6585207 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Misc/TrainStation_PlaceHolder.prefab
  artifactKey: Guid(05e76fa12b2b2e04f959601b1cc35ca4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Misc/TrainStation_PlaceHolder.prefab using Guid(05e76fa12b2b2e04f959601b1cc35ca4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '29e11ae34f36fc5cde2b52c3a10b906d') in 0.0294048 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 28

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/Couplers/TrainJoint_Back.prefab
  artifactKey: Guid(ab148b410ecaf6c4887469723247a03f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/Couplers/TrainJoint_Back.prefab using Guid(ab148b410ecaf6c4887469723247a03f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1323208f2634ad3408592241f0aa47d0') in 0.1631673 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Parts/Wheels/TenderWagon_01_WheelsTruck_visual.prefab
  artifactKey: Guid(1ca15ba3f6fdb634987e3d5ed9b06ac8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Parts/Wheels/TenderWagon_01_WheelsTruck_visual.prefab using Guid(1ca15ba3f6fdb634987e3d5ed9b06ac8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9335c53232eb6685ebfcb35f8d4d9135') in 0.6944625 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 39

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Deprecated/TrainJoint_Back.fbx
  artifactKey: Guid(4d0aa22d10b5e6c498ec9925d3bfc6f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Deprecated/TrainJoint_Back.fbx using Guid(4d0aa22d10b5e6c498ec9925d3bfc6f2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '98d49c7fb5dcf89b4af79026a2330ccc') in 0.175141 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000062 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/Couplers/TrainJoint_Back_nonPhysics.prefab
  artifactKey: Guid(429f0d45f74600048951c5be785e4c9a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/Couplers/TrainJoint_Back_nonPhysics.prefab using Guid(429f0d45f74600048951c5be785e4c9a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '44f420f4cccee0b48aad7f50c83e7082') in 0.1003937 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 17

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Sample Trains/Steam Train Sample.prefab
  artifactKey: Guid(f4eabc27aa79d384abd19e6fc903fa37) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Sample Trains/Steam Train Sample.prefab using Guid(f4eabc27aa79d384abd19e6fc903fa37) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '25c091e6630dd52b6d892bc75d752279') in 16.5770972 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13760

========================================================================
Received Import Request.
  Time since last request: 361.852298 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Deprecated/Locomotive.fbx
  artifactKey: Guid(0ad22278b9f2b52478964f560cac6aed) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Deprecated/Locomotive.fbx using Guid(0ad22278b9f2b52478964f560cac6aed) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '621297f36e43fec6f1fd74ed19ac3da7') in 0.3388814 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Locomotive_01/Locomotive_01_Door.fbx
  artifactKey: Guid(5c41a1a50d8245549844b48e3747a66a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Locomotive_01/Locomotive_01_Door.fbx using Guid(5c41a1a50d8245549844b48e3747a66a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '63045c5984b68a0315a2241027a75cf3') in 1.0531665 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/Doors/Locomotive_01_Door.prefab
  artifactKey: Guid(52e64ff5c6219894e80d6ff2334e0468) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/Doors/Locomotive_01_Door.prefab using Guid(52e64ff5c6219894e80d6ff2334e0468) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '583392752f54ddc2f590e34d37501f7f') in 1.1160784 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/Colliders/Locomotive_01_Colliders.prefab
  artifactKey: Guid(9aae5fe3f0934ca42bac0cc2223b663e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/Colliders/Locomotive_01_Colliders.prefab using Guid(9aae5fe3f0934ca42bac0cc2223b663e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6102254f91c755aff8c04b0e2f3e1219') in 0.0072275 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 377

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Locomotive_01_Blue_Black.prefab
  artifactKey: Guid(2697e21310b3c504793869825543fbfc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Locomotive_01_Blue_Black.prefab using Guid(2697e21310b3c504793869825543fbfc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '339c652d3ce04cf694b8941e89082b70') in 3.8064618 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1216

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.361 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 2.31 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.063 seconds
Domain Reload Profiling: 2423ms
	BeginReloadAssembly (364ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (85ms)
	RebuildCommonClasses (88ms)
	RebuildNativeTypeToScriptingClass (29ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (828ms)
		LoadAssemblies (686ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (343ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (307ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1063ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (828ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (158ms)
			ProcessInitializeOnLoadAttributes (587ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.47 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4586 unused Assets / (2.7 MB). Loaded Objects now: 5173.
Memory consumption went from 121.0 MB to 118.3 MB.
Total: 9.974400 ms (FindLiveObjects: 0.717000 ms CreateObjectMapping: 0.511600 ms MarkObjects: 5.873200 ms  DeleteObjects: 2.871200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.182 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.44 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.040 seconds
Domain Reload Profiling: 2222ms
	BeginReloadAssembly (359ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (82ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (689ms)
		LoadAssemblies (576ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (315ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (277ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1041ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (815ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (161ms)
			ProcessInitializeOnLoadAttributes (575ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.36 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4576 unused Assets / (2.6 MB). Loaded Objects now: 5173.
Memory consumption went from 121.0 MB to 118.4 MB.
Total: 9.926900 ms (FindLiveObjects: 0.731600 ms CreateObjectMapping: 0.507100 ms MarkObjects: 5.786700 ms  DeleteObjects: 2.899800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.206 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.32 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.120 seconds
Domain Reload Profiling: 2325ms
	BeginReloadAssembly (334ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (65ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (48ms)
	LoadAllAssembliesAndSetupDomain (734ms)
		LoadAssemblies (610ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (318ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (286ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1121ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (888ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (160ms)
			ProcessInitializeOnLoadAttributes (638ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (11ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.28 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4576 unused Assets / (2.6 MB). Loaded Objects now: 5173.
Memory consumption went from 121.0 MB to 118.4 MB.
Total: 10.786400 ms (FindLiveObjects: 0.754000 ms CreateObjectMapping: 0.510800 ms MarkObjects: 6.369400 ms  DeleteObjects: 3.151200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.189 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.20 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.040 seconds
Domain Reload Profiling: 2230ms
	BeginReloadAssembly (333ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (71ms)
	RebuildCommonClasses (63ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (723ms)
		LoadAssemblies (588ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (321ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (281ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1041ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (821ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (146ms)
			ProcessInitializeOnLoadAttributes (590ms)
			ProcessInitializeOnLoadMethodAttributes (50ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.32 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4576 unused Assets / (2.6 MB). Loaded Objects now: 5173.
Memory consumption went from 121.0 MB to 118.4 MB.
Total: 12.965800 ms (FindLiveObjects: 0.729700 ms CreateObjectMapping: 0.517200 ms MarkObjects: 8.339600 ms  DeleteObjects: 3.377500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.164 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 2.16 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.040 seconds
Domain Reload Profiling: 2205ms
	BeginReloadAssembly (315ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (67ms)
	RebuildCommonClasses (71ms)
	RebuildNativeTypeToScriptingClass (34ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (695ms)
		LoadAssemblies (555ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (317ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (285ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1041ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (806ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (21ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (159ms)
			ProcessInitializeOnLoadAttributes (565ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.41 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4576 unused Assets / (2.7 MB). Loaded Objects now: 5173.
Memory consumption went from 121.0 MB to 118.3 MB.
Total: 10.933800 ms (FindLiveObjects: 0.793900 ms CreateObjectMapping: 0.504300 ms MarkObjects: 6.546300 ms  DeleteObjects: 3.088300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.127 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.16 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.039 seconds
Domain Reload Profiling: 2164ms
	BeginReloadAssembly (301ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (62ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (688ms)
		LoadAssemblies (539ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (318ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (280ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1040ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (830ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (149ms)
			ProcessInitializeOnLoadAttributes (595ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.69 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4576 unused Assets / (2.7 MB). Loaded Objects now: 5173.
Memory consumption went from 121.0 MB to 118.3 MB.
Total: 13.077200 ms (FindLiveObjects: 0.829700 ms CreateObjectMapping: 0.696600 ms MarkObjects: 7.071100 ms  DeleteObjects: 4.478100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.171 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.21 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.008 seconds
Domain Reload Profiling: 2177ms
	BeginReloadAssembly (318ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (71ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (720ms)
		LoadAssemblies (580ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (318ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (283ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1009ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (805ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (161ms)
			ProcessInitializeOnLoadAttributes (555ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.60 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4576 unused Assets / (2.6 MB). Loaded Objects now: 5173.
Memory consumption went from 121.1 MB to 118.5 MB.
Total: 11.320100 ms (FindLiveObjects: 0.739300 ms CreateObjectMapping: 0.536600 ms MarkObjects: 6.967500 ms  DeleteObjects: 3.075400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.216 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.68 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.987 seconds
Domain Reload Profiling: 2202ms
	BeginReloadAssembly (339ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (73ms)
	RebuildCommonClasses (73ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (53ms)
	LoadAllAssembliesAndSetupDomain (725ms)
		LoadAssemblies (584ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (329ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (282ms)
			ResolveRequiredComponents (23ms)
	FinalizeReload (988ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (773ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (153ms)
			ProcessInitializeOnLoadAttributes (539ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.71 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4576 unused Assets / (2.6 MB). Loaded Objects now: 5173.
Memory consumption went from 121.1 MB to 118.4 MB.
Total: 12.586400 ms (FindLiveObjects: 0.824500 ms CreateObjectMapping: 0.692500 ms MarkObjects: 7.239000 ms  DeleteObjects: 3.827900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.181 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.22 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.026 seconds
Domain Reload Profiling: 2206ms
	BeginReloadAssembly (306ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (68ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (55ms)
	LoadAllAssembliesAndSetupDomain (733ms)
		LoadAssemblies (568ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (332ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (291ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1027ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (826ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (157ms)
			ProcessInitializeOnLoadAttributes (589ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.66 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4576 unused Assets / (2.8 MB). Loaded Objects now: 5173.
Memory consumption went from 121.1 MB to 118.3 MB.
Total: 11.345400 ms (FindLiveObjects: 0.855300 ms CreateObjectMapping: 0.773200 ms MarkObjects: 6.313900 ms  DeleteObjects: 3.401700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.225 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.21 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.029 seconds
Domain Reload Profiling: 2254ms
	BeginReloadAssembly (332ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (74ms)
	RebuildCommonClasses (62ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (760ms)
		LoadAssemblies (608ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (340ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (309ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1030ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (820ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (158ms)
			ProcessInitializeOnLoadAttributes (580ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (10ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.68 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4576 unused Assets / (2.5 MB). Loaded Objects now: 5173.
Memory consumption went from 121.1 MB to 118.6 MB.
Total: 10.455600 ms (FindLiveObjects: 0.728900 ms CreateObjectMapping: 0.505300 ms MarkObjects: 6.305300 ms  DeleteObjects: 2.915000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.151 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.31 ms, found 3 plugins.
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Assertion failed on expression: 'pred(*previous, *i)'
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.099 seconds
Domain Reload Profiling: 2249ms
	BeginReloadAssembly (339ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (66ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (47ms)
	LoadAllAssembliesAndSetupDomain (676ms)
		LoadAssemblies (566ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (317ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (285ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1100ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (872ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (30ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (175ms)
			ProcessInitializeOnLoadAttributes (596ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (12ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Refreshing native plugins compatible for Editor in 1.27 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4576 unused Assets / (2.9 MB). Loaded Objects now: 5173.
Memory consumption went from 121.0 MB to 118.2 MB.
Total: 10.783500 ms (FindLiveObjects: 0.729400 ms CreateObjectMapping: 0.499800 ms MarkObjects: 6.119900 ms  DeleteObjects: 3.432400 ms)

Prepare: number of updated asset objects reloaded= 0

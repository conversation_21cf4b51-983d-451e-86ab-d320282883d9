﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void EmbeddedAttribute__ctor_m9EC595866FA01247FB88943D9880C87559FC1EFF (void);
extern void NullableAttribute__ctor_m35D97A5CDE83B9F8998E650C6C1C650E8FF44AB5 (void);
extern void NullableAttribute__ctor_m92C5041F3EBA384F0F2B2748AAD8BDAD812E6D5F (void);
extern void NullableContextAttribute__ctor_m494F6EEDFC6FA5345FA4CFB264B0659FEDC1BDAD (void);
extern void NativeTextGenerationSettings_get_hasLink_m2D703AAD28BFE44DA7FB6B40A679F26D21718910 (void);
extern void NativeTextGenerationSettings_CreateTextSpan_m63AE7655BC8C77D4614541BA5426F0E1FECA728A (void);
extern void NativeTextGenerationSettings_get_Default_mC9C0F0330DD4B0E8B20D78F4A126C10C5A485297 (void);
extern void NativeTextGenerationSettings_ToString_m84C204918B5C4F40D1EADDB371DCE76FF098E84D (void);
extern void U3CU3Ec__cctor_m46FBAA2482CE6467605F08CD73073673047070B1 (void);
extern void U3CU3Ec__ctor_m6792C1379E4ED043802F1A4106223CF4E247A551 (void);
extern void U3CU3Ec_U3Cget_hasLinkU3Eb__17_0_m98C2509F2425C63BB98B51C9189240F7BCCA809D (void);
extern void TextSpan_ToString_mB43B06596C5F8782F98817A120945168F1A893C9 (void);
extern void RichTextTagParser_tagMatch_mB42907CB050EE08A22B58A843C2866A2409EA3DF (void);
extern void RichTextTagParser_SpanToEnum_mE1DDD45167FB2EBA5B030DF8823DF2BEB4DBA186 (void);
extern void RichTextTagParser_FindTags_m3FF6ECE75133FD1990A6AB52AB57E2A2F4E3AA68 (void);
extern void RichTextTagParser_PickResultingTags_mA3B1F54CD2E2BCBBEE710AD4737DA42B3C07AE7B (void);
extern void RichTextTagParser_GenerateSegments_mA068B63EE134BA6A7EF4B072690902E89662BDE3 (void);
extern void RichTextTagParser_ApplyStateToSegment_m08B617CB0FFDB3DB445F53FBB40536B7A85AE90B (void);
extern void RichTextTagParser_AddLink_mE38CFF0C55D49F8528258B58C4B302AD3392A5E3 (void);
extern void RichTextTagParser_CreateTextSpan_mA05BB9D0792E6B991CCF66298659CECF1C0084B6 (void);
extern void RichTextTagParser_CreateTextGenerationSettingsArray_m56B01D0B6FE522B13F4FC21093A1274D322861A9 (void);
extern void RichTextTagParser__cctor_m0EB9B76F35AAF19494036DD28568E68A7B27D875 (void);
extern void TagTypeInfo_get_EqualityContract_mB3CF3FD825662931FE86695414D02674731AFCA7 (void);
extern void TagTypeInfo__ctor_m74267566395F3250A672157AB0E98A2551B1806D (void);
extern void TagTypeInfo_ToString_m1342960CB528ABB59728C886E6771016529483E9 (void);
extern void TagTypeInfo_PrintMembers_m6E2CC8929B55363B69CF0CF8241846EB5DBA79C7 (void);
extern void TagTypeInfo_GetHashCode_mCBF213A31B84649CA841A6D829B66A8D96D7B9E6 (void);
extern void TagTypeInfo_Equals_m817CA3DA02CDB35F7D551F2E3758E6E65C756550 (void);
extern void TagTypeInfo_Equals_mC80117C96252E06A7D7141753A3A0D068A65DE1C (void);
extern void TagValue_get_EqualityContract_m00E5101C2526BC713F54ABF39B166BA78184715B (void);
extern void TagValue__ctor_mE71E165C404C672A9A6E7E5289C98480D8141005 (void);
extern void TagValue__ctor_m4A36B903BF6F2F9F28B8F1AE5423BA9323187885 (void);
extern void TagValue_get_StringValue_m3BCE1D8318343EA9E53C39F41BD39072BAC25942 (void);
extern void TagValue_get_NumericalValue_m415D37C150AB01B6B0258F0F924EA4B368BB7BEA (void);
extern void TagValue_get_ColorValue_m15B3229E66795A64B41FD75CFD1F2410547565E6 (void);
extern void TagValue_ToString_mDCC527BA92B1236A29FAA11B8FE948F0B4299C5E (void);
extern void TagValue_PrintMembers_m24131FF07C0B85E32DE5F764E5D9E0A9CD7C113D (void);
extern void TagValue_GetHashCode_m2915DE1C63BC335C2265A963E95E815E5E9A995B (void);
extern void TagValue_Equals_mD893FA591B10D0282CC0AC81A985F08DD4A47E96 (void);
extern void TagValue_Equals_m515F3E172DD41E2448C492D3F738C9D1841450A2 (void);
extern void ParseError_get_EqualityContract_mB5140327B441DF6B62DCE939319010DA1112D3F6 (void);
extern void ParseError__ctor_m6373646BDA23AF427AB57DD1B690CEB3F2E3AE58 (void);
extern void ParseError_ToString_m70DD13F900670887C5384CCF968B75C19BDCE86A (void);
extern void ParseError_PrintMembers_mEE74D0B217AF4F81CC96EE909D9DF42481CA90F0 (void);
extern void ParseError_GetHashCode_mDE107EC7556BE750EE3C12ABE1CD1E1D4BB6BD30 (void);
extern void ParseError_Equals_m1B64D22340D2A741B8BF64CFF6687B0AD8BF35FB (void);
extern void ParseError_Equals_m8E5224941B564114DBC9D9BBA24439ED311B1A64 (void);
extern void Character__ctor_m5DCCE862D40487C733C29A233DB8513A9A6A02F6 (void);
extern void Character__ctor_mEEAC42D4227E0053C8008C12B222CC208D781795 (void);
extern void Character__ctor_m21FBFAF1F6324565246096EFFB81C3F9E15D43CC (void);
extern void ColorUtilities_CompareColors_m0F0F140129DEE889FB8AE3B2921C495E94B5E875 (void);
extern void ColorUtilities_MultiplyColors_m81D3E41A86E195794977F31C5A944DB40FE734B4 (void);
extern void FastAction__ctor_m837FFCD82DA457A7BFCC2EA03FBD3E358DA1F3EE (void);
extern void FontFeatureTable_get_glyphPairAdjustmentRecords_mABE78F7C2EA171927CC33170617D72E6C976323E (void);
extern void FontFeatureTable_get_MarkToBaseAdjustmentRecords_m73A1A8FCDB3E9629C15FC2568473B235B9A52D9B (void);
extern void FontFeatureTable_get_MarkToMarkAdjustmentRecords_m3DB78C6BBC2E41936F04E46107002DC5F2136B8F (void);
extern void FontFeatureTable__ctor_m5F00F284C63F1867F679A3250ABFC1393C27025C (void);
extern void FontFeatureTable_SortGlyphPairAdjustmentRecords_m2F5E2ED405FCAEE946CE5CF81163DDCC1B02A905 (void);
extern void FontFeatureTable_SortMarkToBaseAdjustmentRecords_m2AF48E3FC40E5C970FCD9A4ACA4354FD3CD09004 (void);
extern void FontFeatureTable_SortMarkToMarkAdjustmentRecords_mF4A796852F11F07614DF6434DB8E3122E94E7E3B (void);
extern void U3CU3Ec__cctor_m024C5AAB034EF6BBF574EA4EB3AE66D03E259CEA (void);
extern void U3CU3Ec__ctor_m51815D1379A3BDB616D65C006DA7AB32406723F4 (void);
extern void U3CU3Ec_U3CSortGlyphPairAdjustmentRecordsU3Eb__24_0_m35574DBBB0E7B7C971A1BACA4934A37792FCD0B2 (void);
extern void U3CU3Ec_U3CSortGlyphPairAdjustmentRecordsU3Eb__24_1_m9FF21AEEA154F6D846868FA78051C4E23C2ABA47 (void);
extern void U3CU3Ec_U3CSortMarkToBaseAdjustmentRecordsU3Eb__25_0_mA88B3352AD07E2B6F17D92A70DB39D99F45585AB (void);
extern void U3CU3Ec_U3CSortMarkToBaseAdjustmentRecordsU3Eb__25_1_m37069B1EDD9DBDD69291CFDF48C95F71D8379C0B (void);
extern void U3CU3Ec_U3CSortMarkToMarkAdjustmentRecordsU3Eb__26_0_mC1E58F904A13796D7E050CAC2E9066D0202AC853 (void);
extern void U3CU3Ec_U3CSortMarkToMarkAdjustmentRecordsU3Eb__26_1_mFD0F303D96190115E0218C32844A836A4437E61E (void);
extern void Extents_ToString_m8A1F748127EE9CCCD6FFAF4CE1F38E37C07831AC (void);
extern void LinkInfo_SetLinkId_mB4145264190D5C857705261CB27F87C6E10C3F3F (void);
extern void LinkInfo_GetLinkText_mE5F0F2128BB22FFE009BBBDE4EF0E921F95AA6D8 (void);
extern void LinkInfo_GetLinkId_mE502F621D5C09B05C2C509E35404BED4420B4FDB (void);
extern void MaterialManager_GetFallbackMaterial_m10F67CE1AE1E0B9D8BA8AFC06110FADA2404B5A1 (void);
extern void MaterialManager_GetFallbackMaterial_mB65C8D7625B0D0A0D623FC6AC545469AF6B7724D (void);
extern void MaterialManager_CopyMaterialPresetProperties_m2DB1A033E378F3DF347DEA0DC51F1E51776169F4 (void);
extern void MaterialManager__cctor_mB6337D5678E1F45035C1F8EE33D389AB4CBA6527 (void);
extern void MaterialReference__ctor_m044AAA2C1079EB25A5534A6E0FA2314F033DB15A (void);
extern void MaterialReference_AddMaterialReference_m13CC47A7CA6C8781EA68A355B36FCD5AFF467A40 (void);
extern void MaterialReference_AddMaterialReference_mEE57C2284E5BE17BDD80C69165FAECB3B4CC1BCD (void);
extern void MaterialReferenceManager_get_instance_m30BD1A367C3807D0B42CF7156CE699B15E51ED07 (void);
extern void MaterialReferenceManager_AddFontAsset_mF950087FC7EAA324CC15D3E19D62138A96F6FDF7 (void);
extern void MaterialReferenceManager_AddFontAssetInternal_m6F5A4E5ED988BA6F482F015F051ACD19D7B0A005 (void);
extern void MaterialReferenceManager_AddSpriteAsset_m7FBAAC41386CFA769FDDFF1630D041C4D008A322 (void);
extern void MaterialReferenceManager_AddSpriteAssetInternal_m788619DC6BAD5B77E9419ACBDECBCCFE1A6AC97C (void);
extern void MaterialReferenceManager_AddFontMaterial_m76DC9390D0599D1AC898AE981C8756EA0E536AA6 (void);
extern void MaterialReferenceManager_AddFontMaterialInternal_m732F46EF768A41B9519917F4FA1E746E056C745C (void);
extern void MaterialReferenceManager_AddColorGradientPreset_m1496DD123DB526F2B3F49DD3BCAED78A6B1B83EB (void);
extern void MaterialReferenceManager_AddColorGradientPreset_Internal_mF27270501EB3725B4CBE4C241B4A2FCD8D871BF1 (void);
extern void MaterialReferenceManager_TryGetFontAsset_m499C7538909343667E9B837489662862CFF9FB56 (void);
extern void MaterialReferenceManager_TryGetFontAssetInternal_m2FECC618624B12D200EB311F59CBEECA7CDBB69D (void);
extern void MaterialReferenceManager_TryGetSpriteAsset_m1909F75399C0A23DDE7DEDCA60476E1F1ED567EF (void);
extern void MaterialReferenceManager_TryGetSpriteAssetInternal_mC434A7C6DB005EDBBA52154E2AB0E36ED7083C84 (void);
extern void MaterialReferenceManager_TryGetColorGradientPreset_m7442603626A04928C6C042BDAC3D3957B8C0AA99 (void);
extern void MaterialReferenceManager_TryGetColorGradientPresetInternal_mD8018B3225786E71F804D629F3107AB75EE5212B (void);
extern void MaterialReferenceManager_TryGetMaterial_mDC2610737935CD2DE6B1F6302F63C2F3BF7E09CF (void);
extern void MaterialReferenceManager_TryGetMaterialInternal_mEBFC9CE0A6063B25FEA9070F22FD8AD27107ECE7 (void);
extern void MaterialReferenceManager__ctor_mC102EC445A27BE8E3968ADB80EF8FEF3BCFB7778 (void);
extern void MeshInfo__ctor_m3FD8AAF58EBCF22706EDB08B503B4FB2C108E86A (void);
extern void MeshInfo_ResizeMeshInfo_m22D30D08188ACAEBA3CE46383E5D2FFC3E8C519E (void);
extern void MeshInfo_Clear_m06992FEB7AC9B2AE1728BEDFC8D8A39DE1AAD475 (void);
extern void MeshInfo_ClearUnusedVertices_m7B6003EF4CA72C0ABBA4D25DEA8B0BF3934B2830 (void);
extern void MeshInfo_SortGeometry_m92046C53AA6AE75EE3627CE73846296AB3E99DD1 (void);
extern void MeshInfo_SwapVertexData_mD145A4C2B45422DFCB2D9697956E16395E717C7F (void);
extern void MeshInfo__cctor_m667601EA787620405D7DB5F0B37E4D489DE067D9 (void);
extern void FontAsset_get_fontAssetCreationEditorSettings_m024033F91B976A8EAA5CBE67D3DB2A756B91CF01 (void);
extern void FontAsset_set_fontAssetCreationEditorSettings_mF7EE6A46807D78A7E99872171D2AD774DE20C7EB (void);
extern void FontAsset_get_sourceFontFile_m6B0E805BD1B7712F0B5135D157E96F3F40314830 (void);
extern void FontAsset_set_sourceFontFile_m2E6D2AED5E5D2585A09E9BF830387DEB10A2F4E8 (void);
extern void FontAsset_get_atlasPopulationMode_m5364C5A9E84969D8E4FF8436BD18A3F10BF90366 (void);
extern void FontAsset_set_atlasPopulationMode_m1A9DD5C702ED0924B9C208F4CE5ADEACF9188268 (void);
extern void FontAsset_get_faceInfo_mF020EC579E3C18A6279D55D86AF1C585031B49A9 (void);
extern void FontAsset_set_faceInfo_mCCA87B67C4CA2C0A1F6D85FB1FAA09667996EDCD (void);
extern void FontAsset_get_familyNameHashCode_mF2DB211A5712A291B2D28FCDB7F7C29057770330 (void);
extern void FontAsset_set_familyNameHashCode_mE1495199BCE7B771CC920E2DBB86A8AF1518CB55 (void);
extern void FontAsset_get_styleNameHashCode_m3CD3D77F64DAEB31D8F69E4D7CC1AD0AC784ABF5 (void);
extern void FontAsset_set_styleNameHashCode_mE1BE5B75DE1E9EA0F76569609E6C4FFDC57558BA (void);
extern void FontAsset_get_glyphTable_m212E940F74AEE62ACBB3374486296CA518D934B5 (void);
extern void FontAsset_set_glyphTable_m2753BC6CEE011983C2B4B181867C3EB00CDE87D4 (void);
extern void FontAsset_get_glyphLookupTable_mD04A90D8262F1963EDC472272B67BBFAF73DEEA5 (void);
extern void FontAsset_get_characterTable_mC77FAE1355269834F7C8A2D46AFB4BFE7B7AD72D (void);
extern void FontAsset_set_characterTable_m44703292F669F2F6D4920EB9427077E24FB1512C (void);
extern void FontAsset_get_characterLookupTable_m7E76D6C706C5CEB04A9541C68AE6D9E5C75F0FFC (void);
extern void FontAsset_get_atlasTexture_mC49216F40093C7AC4FA5DA68F9F5C9FCC83B8F27 (void);
extern void FontAsset_get_atlasTextures_mADD7A506F0444A1EE4F1D52536B0C5DA9BE35075 (void);
extern void FontAsset_set_atlasTextures_m5315BA4903B77742EFA1E54CEA2AF12726B10A99 (void);
extern void FontAsset_get_atlasTextureCount_mC20300C53E52D7A8351DE296BAD565268568119F (void);
extern void FontAsset_get_isMultiAtlasTexturesEnabled_mF222228A76102BB0EA593A60439D22F912761F1E (void);
extern void FontAsset_set_isMultiAtlasTexturesEnabled_mA470AF6D312989E752C68DC0FD5700235877566B (void);
extern void FontAsset_get_getFontFeatures_m6AC33ECD0C754EA278473023D0DF03F086AD962C (void);
extern void FontAsset_set_getFontFeatures_mC076D171BEA658A422307182B6C2A4555F6196AD (void);
extern void FontAsset_get_clearDynamicDataOnBuild_mC1F714E56F087B29E0A3B43D820EDCEB78E9EE75 (void);
extern void FontAsset_set_clearDynamicDataOnBuild_mA1C6F298742DF78EC0F81157F0E04246F8B82F7E (void);
extern void FontAsset_get_atlasWidth_mE711550FDD4B5F988B77AB5D332A80A34B5CF364 (void);
extern void FontAsset_set_atlasWidth_mFFB9D37EB1C648384ED1426B42E26A4104D329B1 (void);
extern void FontAsset_get_atlasHeight_m306FBF7D35C39123A4770E147FAF95B1B8DE8086 (void);
extern void FontAsset_set_atlasHeight_m7116DFD32F38971CE39D7F4BF84CB5217DCAA2B5 (void);
extern void FontAsset_get_atlasPadding_m251A35FB5F499EE66CC2E2150CBEDB2C8C5D5581 (void);
extern void FontAsset_set_atlasPadding_mB34AA836A3D02722ABED71B3583D767560CA956D (void);
extern void FontAsset_get_atlasRenderMode_m036D4BA220E5D4B0C407CA6BC1B09D8914B5058A (void);
extern void FontAsset_set_atlasRenderMode_m993764193CE75D57DC4CC755336596681A7866D2 (void);
extern void FontAsset_get_usedGlyphRects_mE039AEF3AE45A15A86B2C0F774E6ED58AFA2F341 (void);
extern void FontAsset_set_usedGlyphRects_mBF80C1063C0A274AD95F55C43DA734E126F6643F (void);
extern void FontAsset_get_freeGlyphRects_mCDAEF0519586C5248BBEDEAA85086CC117903E88 (void);
extern void FontAsset_set_freeGlyphRects_mED3C0E01ABFC63CE700C701476BA2B66D112AA9B (void);
extern void FontAsset_get_fontFeatureTable_m7C4EB9A655B237CE02FAF7B8B16C2F2863FE5070 (void);
extern void FontAsset_set_fontFeatureTable_m3FD11B99122416777808E1CE5414D7BA40920C3B (void);
extern void FontAsset_get_fallbackFontAssetTable_m43303BFBE8A8C55D8CE8A67C47EFAFF5A712CB69 (void);
extern void FontAsset_set_fallbackFontAssetTable_mE22C3D2323111FD6A5DF9847FB812BD41E18832E (void);
extern void FontAsset_get_fontWeightTable_m8DADE0BCE53D53EEECE27025F7E94FDD4BF00099 (void);
extern void FontAsset_set_fontWeightTable_m39D8B63BD3FCC773AAB5634C6D9314C713161814 (void);
extern void FontAsset_get_regularStyleWeight_m6C4B4D4CAD36800E6E686A05A5DB8D4475F2707F (void);
extern void FontAsset_set_regularStyleWeight_m2D1E5440A5E1794A003FF087A87393DA9A114385 (void);
extern void FontAsset_get_regularStyleSpacing_mB7EEEA236312F5AC31FD3B787808279206F521B1 (void);
extern void FontAsset_set_regularStyleSpacing_m7CCE54FB9163D65A6B40269B2BDD30199023E797 (void);
extern void FontAsset_get_boldStyleWeight_m804ACC85DD80DC72DB4BCC83C3FB866411F8EFCA (void);
extern void FontAsset_set_boldStyleWeight_m204B04CF9E98AD8669025BFDC0EF3CE9AB5CBBA2 (void);
extern void FontAsset_get_boldStyleSpacing_mB8CF4F4880B110E41D566648FF1D995010CF1FF0 (void);
extern void FontAsset_set_boldStyleSpacing_m62DAA35837E8563DD76E3D162B6DB59BE3804914 (void);
extern void FontAsset_get_italicStyleSlant_m69E70060C6E7940B4ACE61F2B7CB8965F86DA96B (void);
extern void FontAsset_set_italicStyleSlant_m223875ED81B0397CA36E94A6F346AEE68510C0D2 (void);
extern void FontAsset_get_tabMultiple_m9C0422A00BFCF82091F14F4E303E2717247350AE (void);
extern void FontAsset_set_tabMultiple_mC927B74D27FBB94245E09FE39D9F6749AF07017B (void);
extern void FontAsset_CreateFontAsset_mDBAB2C51721B94702B8706450DAF74FD2E757712 (void);
extern void FontAsset_CreateFontAssetInternal_m6AA9E69176D6E61BDD7D59309A0509379C10F11A (void);
extern void FontAsset_CreateFontAsset_m37F0A299C881426F96D0D444AE5E95AE7E68E082 (void);
extern void FontAsset_CreateFontAssetOSFallbackList_m0ADBCC935CAA11B764D84E21C39CC8321BC4A048 (void);
extern void FontAsset_CreateFontAssetWithOSFallbackList_mA0DB907C4F9C070C7C28CAFE074EE76CBB03AC84 (void);
extern void FontAsset_CreateFontAssetFromFamilyName_m0B15D684FEAA30BBDB993A8ED4A24A975B4B6D36 (void);
extern void FontAsset_CreateFontAsset_mBC142F8527671635D9472BCC22C65A2E94368253 (void);
extern void FontAsset_CreateFontAsset_m8C8C64410ED418883256F0277AA6ACB4514FFE0A (void);
extern void FontAsset_CreateFontAsset_m627EE2CFBDCEADB906A5CD26FB474D3C1A37A166 (void);
extern void FontAsset_CreateFontAsset_m27CFD9D5831E873A379D2D063871A6A1BA9E00C9 (void);
extern void FontAsset_CreateFontAsset_mEFD731F35B521BB833F532C0EE7637F97647E650 (void);
extern void FontAsset_CreateFontAsset_mCD759D9A87211073FF1FFF46876CD4F8A32AF3F2 (void);
extern void FontAsset_CreateFontAsset_m402BB043D44E6CDA7DDD1100EFE85F0963CB4673 (void);
extern void FontAsset_CreateFontAssetInstance_m9114A363FC6DDEB899CC75C73A41CF903A59333E (void);
extern void FontAsset_GetFontAssetByID_m743704FE77E53C291724F4CC53DF449219C448D6 (void);
extern void FontAsset_RegisterCallbackInstance_mF1E71A17E78B9C7F6853DB4D0112ECCC06ED59D4 (void);
extern void FontAsset_OnDestroy_m3587016A089072C5C03168AA4C6AA1956FE12785 (void);
extern void FontAsset_ReadFontAssetDefinition_m6D84DBCB130D530B2F78A7E24232D8A6A81AEC48 (void);
extern void FontAsset_InitializeDictionaryLookupTables_m29A4AEF49CF11A0E49C229EF13B2262AE66757FF (void);
extern void FontAsset_InitializeGlyphLookupDictionary_m82782B7B5C602AD5097A016BF668868C0892CCF6 (void);
extern void FontAsset_InitializeCharacterLookupDictionary_m8886A4CA911334BD319AB78F1CBBD68E13624BB6 (void);
extern void FontAsset_ClearFallbackCharacterTable_mA684313E1CF74889B3DC97BF39195270F19E3582 (void);
extern void FontAsset_InitializeLigatureSubstitutionLookupDictionary_m85D0338B542EEF7E5DE8224AA699730752F93FD3 (void);
extern void FontAsset_InitializeGlyphPairAdjustmentRecordsLookupDictionary_mA1901466C14645EC72B595467630F276B09F980A (void);
extern void FontAsset_InitializeMarkToBaseAdjustmentRecordsLookupDictionary_m7BC52CF67C055F71B1E9A79B761F79A86E5D8716 (void);
extern void FontAsset_InitializeMarkToMarkAdjustmentRecordsLookupDictionary_mC911E1C69D58A7490587C63473D65BA1DB775D56 (void);
extern void FontAsset_AddSynthesizedCharactersAndFaceMetrics_m203BD62D0A537A6EA7CD7DBA1FF9A94301492933 (void);
extern void FontAsset_AddSynthesizedCharacter_m6ABFCE6454A09D5CF7914F318DDC79198C47F9EA (void);
extern void FontAsset_AddCharacterToLookupCache_mB90E06CE313CC0BB6F81415BF8FB4E043108EED8 (void);
extern void FontAsset_AddCharacterToLookupCache_m616C9F912D8CF75B1AA391BE362788FB8DEC0F59 (void);
extern void FontAsset_GetCharacterInLookupCache_mA0F217E4902B0569BE722EABCC5EBDC1842ED108 (void);
extern void FontAsset_RemoveCharacterInLookupCache_mC07C7791E07A35D8D93914F410903CBAC84BB9F0 (void);
extern void FontAsset_ContainsCharacterInLookupCache_mDCD1EC1477C61DF6E612307A3965EF6A2056EE61 (void);
extern void FontAsset_CreateCompositeKey_m842F3D888783D1F7F55A695FFC704A118840B5D8 (void);
extern void FontAsset_LoadFontFace_m64C78A2FE5DA2E7029E43B467A1B242827B45B4F (void);
extern void FontAsset_SortCharacterTable_m9A551DF3B19E246E8C4BE86463E0ED1DEB27D321 (void);
extern void FontAsset_SortGlyphTable_mC853714CB002D923A19C3A925BB24D6BF42A08CD (void);
extern void FontAsset_SortFontFeatureTable_m072B32D6D8C562F60D3D6CBCC7DCB3282EDD587F (void);
extern void FontAsset_SortAllTables_mACA7063865A460F5949E5B8A8D978D588124A094 (void);
extern void FontAsset_HasCharacter_m6BAF48714E1BF5D8EE7ACF33F774C8C6EEE452F3 (void);
extern void FontAsset_HasCharacter_mE87EEF6CDA1F4E1D6928CC9A3C01A91922D4FB21 (void);
extern void FontAsset_HasCharacter_m3E405FA081E68243DDB6558FA03530686E894EFE (void);
extern void FontAsset_HasCharacterWithStyle_Internal_m78B49AB1F1C6A0C3A177EC9E0E92B2DEDB1E0DA5 (void);
extern void FontAsset_HasCharacter_Internal_mC5BF3BF620A498A319DC33FF02F02742D0A9CFEA (void);
extern void FontAsset_HasCharacters_mD670CCEB48448CE5C1430B938F99D4FC659FB2F8 (void);
extern void FontAsset_HasCharacters_m97A50BC627C163418CAE0B42A50893057B025E90 (void);
extern void FontAsset_HasCharacters_m048839FDD1876CDCA3C5A744592545B46C75E15B (void);
extern void FontAsset_GetCharacters_m5CCEC5A0F89B0BC29B1EFEE9E33923F6879409F4 (void);
extern void FontAsset_GetCharactersArray_m4CE08FECFCF3F9913B5B29DF0F2272FA4B3F945F (void);
extern void FontAsset_GetGlyphIndex_mF20097CDB68A8CE866E61D4C237FBB95257A9745 (void);
extern void FontAsset_GetGlyphIndex_m7101FAA6F39074FDB45DE3DE6BEBDC276D03E04C (void);
extern void FontAsset_GetGlyphVariantIndex_m9D2C993281FC370D6DE57D783B2BFE94BD71B1BC (void);
extern void FontAsset_UpdateFontAssetData_mAAC0ED05410942C08E8EFD4678F9565FD8C373D4 (void);
extern void FontAsset_ClearFontAssetData_m225ADFCBB0CFD481E18637F3D3FDFFEAFC6FE9A1 (void);
extern void FontAsset_ClearCharacterAndGlyphTablesInternal_mFD829694B1BEC9B07C488FA99E8C238701FFE427 (void);
extern void FontAsset_ClearCharacterAndGlyphTables_m4CE0F4F8421393E0D168A9D3AECF0C22FDE06D54 (void);
extern void FontAsset_ClearFontFeaturesTables_mF7DFB9072C78EB088D529A168D1E3E4596FA79C9 (void);
extern void FontAsset_ClearAtlasTextures_m5B320A65E1CD35F2C17E27F09158F8E9BDA9EA2B (void);
extern void FontAsset_DestroyAtlasTextures_mBE2810F8C55E286B5B7ABE24A6F9132F51CBE027 (void);
extern void FontAsset_RegisterFontAssetForFontFeatureUpdate_m5D26FB74AB0C3AA8DC363DA517C1017641F85739 (void);
extern void FontAsset_RegisterFontAssetForKerningUpdate_m0523A0E6C5C52374CD3187CB9A2D601EB0E36BAB (void);
extern void FontAsset_UpdateFontFeaturesForFontAssetsInQueue_m021524DE2B5F8ABE9DDA58447F896159EA15E0F3 (void);
extern void FontAsset_RegisterAtlasTextureForApply_mAB5DC9E4B77C8E0FCADF0AD98E8D63C5229FC383 (void);
extern void FontAsset_UpdateAtlasTexturesInQueue_m359EFCCB6F692F5F8546D4205F366B079075792E (void);
extern void FontAsset_UpdateFontAssetsInUpdateQueue_m67B9FE54C99FDC8FD3FE3471768C416083E36768 (void);
extern void FontAsset_TryAddCharacters_m7F1D0CB7E4D9B8D3CE44D4D01F9CDCEFD4D1B46B (void);
extern void FontAsset_TryAddCharacters_m9618B4F12C004B8267E0D17ED81B94BE48D85119 (void);
extern void FontAsset_TryAddCharacters_m5E282618D9ED92AD0112BC7B6B2C3B1066DDFA63 (void);
extern void FontAsset_TryAddCharacters_mDA1C3A68799C84A80C27CDB84482684F6822137F (void);
extern void FontAsset_TryAddGlyphVariantIndexInternal_m59E4DBF931E9D1D5BD78C94179248E8B17ABE993 (void);
extern void FontAsset_TryGetGlyphVariantIndexInternal_m5BE497F578235C39647D9BBD613F1D3A7F027245 (void);
extern void FontAsset_TryAddGlyphInternal_mA41540AE85F2F11562E1DB5B763B37D29D9D497B (void);
extern void FontAsset_TryAddCharacterInternal_m41BFAA446B6BC9880AD3E03D6AC199C46BAD3F69 (void);
extern void FontAsset_TryAddCharacterInternal_m407B178A59705227BA6CC1AF1EE17E5F901AD943 (void);
extern void FontAsset_TryAddGlyphToAtlas_m79A28E53E32F937D3676947E7703F3F54FC4A12A (void);
extern void FontAsset_TryAddGlyphToTexture_m6CD601B197CDABD52881560CE6D295EBD6E696AB (void);
extern void FontAsset_TryAddGlyphsToNewAtlasTexture_m8F98FBF7A0EC1B37C4DB43536DA42D3864F6F3AB (void);
extern void FontAsset_SetupNewAtlasTexture_m38F81BE1582A15DDDB950E7AAC650CD9B7D14168 (void);
extern void FontAsset_CreateCharacterAndAddToCache_m0325EB298F9AAC25F38C0EDDAEAA89DE96E306B5 (void);
extern void FontAsset_UpdateFontFeaturesForNewlyAddedGlyphs_mD8A62062839B8D975FD9D27F2E31650742591E69 (void);
extern void FontAsset_UpdateGlyphAdjustmentRecordsForNewGlyphs_mD1BEDEA9F9D0F016024F7C231EB10A6C02D29F44 (void);
extern void FontAsset_UpdateGPOSFontFeaturesForNewlyAddedGlyphs_m0B5C369451E41ACBE2856832AEDAD5D0329B8E26 (void);
extern void FontAsset_ImportFontFeatures_m5EBD1B255DE7F72C2028374FA01E5D8CFDAEB4A7 (void);
extern void FontAsset_UpdateGSUBFontFeaturesForNewGlyphIndex_m9EFD3DFEF97AF01AF16DA1B63202261BCABD7BA5 (void);
extern void FontAsset_UpdateLigatureSubstitutionRecords_mDEA6498E8E56015D0F27ABC6D546FB7820C48246 (void);
extern void FontAsset_AddLigatureSubstitutionRecords_mC03697ED7E1F030F0D27186CB45AA41CE3C4A1B3 (void);
extern void FontAsset_UpdateGlyphAdjustmentRecords_mD1C9297EA75EA767A823709CC39B6E57905E22A3 (void);
extern void FontAsset_AddPairAdjustmentRecords_m04CD8E1CDF9FB571C124DC5AC526FD36D294B6DE (void);
extern void FontAsset_UpdateDiacriticalMarkAdjustmentRecords_m4D989B1B4C6E6FE7F3B9853D2E5D49316506E3B0 (void);
extern void FontAsset_AddMarkToBaseAdjustmentRecords_mE7F211ED7856EBBE38EB53F75589EFEFA2600D79 (void);
extern void FontAsset_AddMarkToMarkAdjustmentRecords_mF5B3428009326F0934D7C751E4EEAAC38F4ABA16 (void);
extern void FontAsset_get_nativeFontAsset_mDC06A90902EB4D0F12898100D27CE21392032257 (void);
extern void FontAsset_UpdateFallbacks_m2969A07345DAEF15127FDD2FE5AF253291A04D7A (void);
extern void FontAsset_UpdateWeightFallbacks_m5C92F81D09E59F733932E911E4163B881B160A37 (void);
extern void FontAsset_UpdateFaceInfo_m237405CB07D4014F4F6C807F082E7F7507887F06 (void);
extern void FontAsset_GetFallbacks_m2B1FDD4D2D947F1EB01A905B70AE133516466325 (void);
extern void FontAsset_HasRecursion_m8DD6A2628A19E39736330C0760285DEC9F9FB1C3 (void);
extern void FontAsset_HasRecursionInternal_m077ADF5BE6C08557D7ED213F85AEF145CF96BAC7 (void);
extern void FontAsset_GetWeightFallbacks_m4B7596CCD2450BFAC7B5559369610051DFBC5CD7 (void);
extern void FontAsset_UpdateFallbacks_mF9559D4A2B74EAD8C2736C7A032A08E537224F02 (void);
extern void FontAsset_UpdateWeightFallbacks_mBC79323557BBF6C821A965664D63D548A0DCBC42 (void);
extern void FontAsset_Create_m00CB9D0A80F9B9AF6D4FF4AA22D91D44BD3392FE (void);
extern void FontAsset_UpdateFaceInfo_m029F0B2932389C2245EAD2E137ED53B15900C0AB (void);
extern void FontAsset_Destroy_m3D53E650ED29327AF72DD31C97906C11A4CCB0AA (void);
extern void FontAsset_Finalize_m89E40E8DFD22ABB4DDD28A0D882D705C5DDDDDBF (void);
extern void FontAsset__ctor_mD55676BD025F9D05DBC9A5B32480E092169B9D45 (void);
extern void FontAsset__cctor_m0F11465E3C2F3D158DB9BF403CE86298432BD00E (void);
extern void FontAsset_UpdateFallbacks_Injected_m57440C9C279DE16968E7DB30392FF6A8B53B1306 (void);
extern void FontAsset_UpdateWeightFallbacks_Injected_m999DF41E926E829DF3C4D0AAA34B1A63C1A7366B (void);
extern void FontAsset_Create_Injected_m02BBCA74D470A637ECF6CC5430A9F4B3FD70253D (void);
extern void FontAsset_UpdateFaceInfo_Injected_m0EA7955663DE89C85FF2ABAC0B5C52870FC13831 (void);
extern void U3CU3Ec__cctor_mEB4ED309CE67EB2C5633590466BD1C4EE90E0D5C (void);
extern void U3CU3Ec__ctor_m3CDF0903C024856278B5A7DF46C7EFCBA6F6B651 (void);
extern void U3CU3Ec_U3CSortCharacterTableU3Eb__191_0_m53398D111DBAF3B96D00D9A3932CD90E5F665324 (void);
extern void U3CU3Ec_U3CSortGlyphTableU3Eb__192_0_mC2605EC70E99D1F12C197E5D1DCF577C65BAB6B2 (void);
extern void FontAssetFactory_CreateDefaultEditorFontAsset_mDA1CBA9F2B49F628BD1ACFFB46E2F7BDD198D720 (void);
extern void FontAssetFactory_SetupFontAssetSettings_m8BEB0CA4D2E2623D76DEDBFC0A9CFA8261CC0F59 (void);
extern void FontAssetFactory_SetHideFlags_m25B6A1A10811A91A1B0737A73C303625B435999F (void);
extern void FontAssetFactory__cctor_m85AF34C9ED54D11E03B2BF32C90DFCC2FC3E00AD (void);
extern void FontAssetUtilities_GetCharacterFromFontAsset_m854EBABBF60E9B80275BE56FA803B258D9B61D99 (void);
extern void FontAssetUtilities_GetCharacterFromFontAsset_Internal_mF5271CACE66EF55DB6D72E37102051F14202D90A (void);
extern void FontAssetUtilities_GetCharacterFromFontAssetsInternal_mE53763E9CB71B0606391F6A0CC5524AADE1908BC (void);
extern void FontAssetUtilities_GetCharacterFromFontAssetsInternal_m3291A1DDB038692BCA5EDE6548043943E0EE1BD2 (void);
extern void FontAssetUtilities_GetTextElementFromTextAssets_m8AAAF9F3A364454283DB8B6C8FE06C5D4278705D (void);
extern void FontAssetUtilities_GetSpriteCharacterFromSpriteAsset_mB9147480CA9223089D54748F37F227BC180D78E1 (void);
extern void FontAssetUtilities_GetSpriteCharacterFromSpriteAsset_Internal_m5A04BE23599904FF0B23061BC9F7EDBF6AC7DB88 (void);
extern void SpriteAsset_get_faceInfo_m54EC5227F682ED6A24F5633283258E6641CDA4DC (void);
extern void SpriteAsset_set_faceInfo_m060A5DBBD5941A53BFE9D45E2B637D88ED8223EA (void);
extern void SpriteAsset_get_spriteSheet_mC53205114A12A79F7495FA5F5EFC9948F151256B (void);
extern void SpriteAsset_set_spriteSheet_m1DE591615ABCBB4B10118BF4C0E1B57F559C6469 (void);
extern void SpriteAsset_get_width_mC8BB271B0C76D6B0609C2B9220FFA00F07BD5C31 (void);
extern void SpriteAsset_set_width_mE25BEA3FADDD8254C7A4FD664669B5D535162993 (void);
extern void SpriteAsset_get_height_mF1EBD2DCEAE3BD57B4E80E21EB09BBF9F7539A49 (void);
extern void SpriteAsset_set_height_m3C64D7A7037FEB68C2E329ABEE0318EDDBEF9FB8 (void);
extern void SpriteAsset_get_spriteCharacterTable_m8D0D65C430AD8BC8C2BC8151DC4672CC0F690E0A (void);
extern void SpriteAsset_set_spriteCharacterTable_m38553B81E01B502CCD568A654E9EF3B0D0BCA92D (void);
extern void SpriteAsset_get_spriteCharacterLookupTable_mDB0BA7D703F58A05BB765AE44D6244D98C17DE03 (void);
extern void SpriteAsset_set_spriteCharacterLookupTable_m38F3E7A0A52B82595C87E6A630B156E4D22F2E25 (void);
extern void SpriteAsset_get_spriteGlyphTable_m491B3F4A5350C38D8B5166A60B7C43ED3608C0BA (void);
extern void SpriteAsset_set_spriteGlyphTable_m7402B7CF195E4B9F15DDA3273F49CA460A68DAD3 (void);
extern void SpriteAsset_Awake_m3A012935612A7EB924A77B85EDCF6C09257F60BE (void);
extern void SpriteAsset_UpdateLookupTables_mCC7A470A65A72908C9CDBDDFD17A056188A5C7CE (void);
extern void SpriteAsset_GetSpriteIndexFromHashcode_mE73615D1D9A8BB45C3426197EC54B1A002642DE0 (void);
extern void SpriteAsset_GetSpriteIndexFromUnicode_m321E02B6000E5F6673F5724155C3EF1DE3F5A66B (void);
extern void SpriteAsset_GetSpriteIndexFromName_mBCB684ED6E3DF5663A7FDA02CA69C99D9B17281B (void);
extern void SpriteAsset_SearchForSpriteByUnicode_mC50EFD6F2B78609891C357212CF65A8F18EB8A66 (void);
extern void SpriteAsset_SearchForSpriteByUnicodeInternal_m34EB60FC8F44FCDEEE1FC2B61261BAE76DA6DB25 (void);
extern void SpriteAsset_SearchForSpriteByUnicodeInternal_m691C0974B2011F07E6FD54AEA76ED5E1334278D9 (void);
extern void SpriteAsset_SearchForSpriteByHashCode_m8B9CAB0028CC297570785693F2973918B31C9028 (void);
extern void SpriteAsset_SearchForSpriteByHashCodeInternal_mE1AAA3464383B1964F2C98428A413B306730BFED (void);
extern void SpriteAsset_SearchForSpriteByHashCodeInternal_m3A73CAD6EEBB517196C227B01CAE6AB4AFE69293 (void);
extern void SpriteAsset_SortGlyphTable_mA700CE5246D5798FA65779BE53179FFF4FFED6E5 (void);
extern void SpriteAsset_SortCharacterTable_m5447649977AF2C9F62A14415B44CDDD897A53AE1 (void);
extern void SpriteAsset_SortGlyphAndCharacterTables_m0E2B691E7C1F284E12A88B47B705307E83C7D927 (void);
extern void SpriteAsset__ctor_mE03F69799389DE8D90E69CD70054955033C4ED3C (void);
extern void U3CU3Ec__cctor_m84A11BA2AE10FB375A960D167ABF5F559DACAE3A (void);
extern void U3CU3Ec__ctor_mEFC122BF1D0D0CA8F0EAE9CE353C37A8CFABB5F3 (void);
extern void U3CU3Ec_U3CSortGlyphTableU3Eb__44_0_mDA0ABBA7C3A604F677CA4C213CBBA053C79A0873 (void);
extern void U3CU3Ec_U3CSortCharacterTableU3Eb__45_0_m48BF102E54D9CC4B1311BFFCACB235207416E60A (void);
extern void SpriteCharacter_get_name_mD5A9CC908308BB48D459973C8844FE1FD7C925B1 (void);
extern void SpriteCharacter__ctor_m0B3812DF9A667CA2C3AA321DF3403197EEBC83BA (void);
extern void SpriteGlyph__ctor_mFFE2D9AA5A28EA0C20C10694569A1E23A664BB4D (void);
extern void TextAsset_get_version_m2316C9F212FE4A13335032354A496CAEC86CB25B (void);
extern void TextAsset_set_version_m24718A4A6A86F4CE41D9ED1E0F796CCF367516CD (void);
extern void TextAsset_get_instanceID_m843A6CAA7FE9322CD19546671D3F0E90A0E27AFB (void);
extern void TextAsset_get_hashCode_m4D519E837097D8869A8D38EBD11611FADE411092 (void);
extern void TextAsset_set_hashCode_mC6ED3271A5EFC05562FD1083BE1C872CB69CFF74 (void);
extern void TextAsset_get_material_m4B9C02D34426436FDB01F1963A9FDC11D75604EF (void);
extern void TextAsset_set_material_mD9988165763E0E72C7FB7537760899EC1841C829 (void);
extern void TextAsset_get_materialHashCode_m9BAA469D5760D87EEC6E09B9ED3C5902B75017E6 (void);
extern void TextAsset_set_materialHashCode_m15EE7CCAF81DBAA326049F00788BCC918FDB2631 (void);
extern void TextAsset__ctor_m2C43EAC2D75854E6724A235794DEAF8A1AF9E786 (void);
extern void TextColorGradient__ctor_mAF03C3B68C29D94ED1F1517E65477CFC2B078FAC (void);
extern void TextColorGradient__ctor_m0712D0C09E7BCBC00909CEB71B43F2276ADB8B55 (void);
extern void TextColorGradient__ctor_m0A6B9264A8FBABEEC5BA84353D9B3DD7999DD2FF (void);
extern void TextColorGradient__cctor_mC5AE96BE4461EF9AE84CE4E3765B712D6A64693E (void);
extern void TextSettings_get_version_mB5B7C3F04FFC8DCDFCFF63CFFF7612B768E267CB (void);
extern void TextSettings_set_version_m7BEE644EEDD77892C295170F9B2B68BC45C7C6CF (void);
extern void TextSettings_get_defaultFontAsset_mC6280464BFEE081DB23243BB94E49C72A0885A1F (void);
extern void TextSettings_set_defaultFontAsset_mC0137D9AEB9EE7A9E1B3D7C499414EDD217AF3BA (void);
extern void TextSettings_get_defaultFontAssetPath_mF7B1713753CFAE048C745C3572332CE18CD51D3B (void);
extern void TextSettings_set_defaultFontAssetPath_m35111382F9C2E3C76FA6B77BABEE8149BDDCC7ED (void);
extern void TextSettings_get_fallbackFontAssets_m332526E834C994425141A58C968FD40320573F98 (void);
extern void TextSettings_set_fallbackFontAssets_mEBEC769B2FF339BDB44D72CB48F0010577889772 (void);
extern void TextSettings_get_fallbackOSFontAssets_mA595476A990F2F1CBD78743707F9CF8F6CC4BBA9 (void);
extern void TextSettings_GetStaticFallbackOSFontAsset_m4449E418309F6CACD851E0459EF2DEEE18172C17 (void);
extern void TextSettings_SetStaticFallbackOSFontAsset_m9C1063549F7710BDE66D57AA1E875C1E15D83C7C (void);
extern void TextSettings_GetFallbackFontAssets_m4FFB3FC5647A87AB65BDF7C8F4119B0ECFD4B56C (void);
extern void TextSettings_get_matchMaterialPreset_m4675979547AE4C83E680260EAE5ACBC4FAC53B87 (void);
extern void TextSettings_set_matchMaterialPreset_mD5A3DDE2D93FE2872C869A2AE0B0C984CF5BC12C (void);
extern void TextSettings_get_missingCharacterUnicode_mA707E5E6633633BBB3BAFB96B97A5A995100F3F3 (void);
extern void TextSettings_set_missingCharacterUnicode_mE3E3E2F902BE89B9FE78F03D3B8C508583D1CDA3 (void);
extern void TextSettings_get_clearDynamicDataOnBuild_m0C130F53F106D77250F38239FB73DCB97299FB79 (void);
extern void TextSettings_set_clearDynamicDataOnBuild_m86B73E68953D42C6AE4210BEA5D678768155A1AD (void);
extern void TextSettings_get_enableEmojiSupport_mC3DEBE44D903676B740164D050DF8266DF6D44B1 (void);
extern void TextSettings_set_enableEmojiSupport_m79FB14818237BD3F40335E1F39560690F834A31B (void);
extern void TextSettings_get_emojiFallbackTextAssets_m438C7B82C8926311E01DD9DA46C5BA306D1BC9B2 (void);
extern void TextSettings_set_emojiFallbackTextAssets_m55237A9CD208CAC211165C721FC045BE3364ACD6 (void);
extern void TextSettings_get_defaultSpriteAsset_m8FA900F9747B7ADBCD2A2F612E7D977DB58D6445 (void);
extern void TextSettings_set_defaultSpriteAsset_m6083C46D0B3AFB6816363CF2564D2DB2D1ECFEB9 (void);
extern void TextSettings_get_defaultSpriteAssetPath_mF57875222B7FACC9B69369305EEEB53FFE7986E0 (void);
extern void TextSettings_set_defaultSpriteAssetPath_m9C49C00A96230DAF828E629C106B229A4552F753 (void);
extern void TextSettings_get_fallbackSpriteAssets_mC6D74088EBDFDFBACD581FE4AB4EAA447ED242AD (void);
extern void TextSettings_set_fallbackSpriteAssets_mB28744780B58AE04BA7F1275508FF7DEE4E485E5 (void);
extern void TextSettings_set_s_GlobalSpriteAsset_m77A3A39C3263DF665FCE30F0ADB1B04B15E73E33 (void);
extern void TextSettings_get_s_GlobalSpriteAsset_m3C98B253850B44AA610FE92AE729D035DE17AAE3 (void);
extern void TextSettings_get_missingSpriteCharacterUnicode_m496D85B0D88C91F2A5CC9929C233EAA84831523E (void);
extern void TextSettings_set_missingSpriteCharacterUnicode_mFE1AFD04C4AA793559E0A157C9E5472E3C3F6701 (void);
extern void TextSettings_get_defaultStyleSheet_mDA420960556C00405FA66CBD2DA36807F8F4B4F2 (void);
extern void TextSettings_set_defaultStyleSheet_m2867A0B047E623E4485F10EE717FF0B46BAAECEE (void);
extern void TextSettings_get_styleSheetsResourcePath_mE65A055D1C99CA2048BE6F246258EF262E43C4E6 (void);
extern void TextSettings_set_styleSheetsResourcePath_m19A5C893047D78B43AC5EB43FB9EA79792464C05 (void);
extern void TextSettings_get_defaultColorGradientPresetsPath_m1571454580E1F0DC859D3F201BB1F6355A5DBB8E (void);
extern void TextSettings_set_defaultColorGradientPresetsPath_m951D5A7AE71C87BFA5B9B8CE8EA2F78A474FE01D (void);
extern void TextSettings_get_lineBreakingRules_m96E2C32D4F08309D904B0BCD83CEBE8CD6716A04 (void);
extern void TextSettings_set_lineBreakingRules_mB1174FE0F923443130A3D4686C119EDB0430992D (void);
extern void TextSettings_get_displayWarnings_m3CA9FCB44B30CC06F54CD3716D68285FF844DF83 (void);
extern void TextSettings_set_displayWarnings_m08373EE3900E911B4021CBF8AF915DA69856CC20 (void);
extern void TextSettings_OnEnable_mBFC6BA8BA147B68E9FB956B2D496A2E8C2972A13 (void);
extern void TextSettings_InitializeFontReferenceLookup_m34B5FB3D61296A7620196398D267812EEFCE0B04 (void);
extern void TextSettings_GetCachedFontAssetInternal_m9308E0DDEF8F6D8ACA17DAADB43745C860788202 (void);
extern void TextSettings_GetCachedFontAsset_m439117E0F4363B6D7790046FE0C3829DB6EF4AAD (void);
extern void TextSettings_GetFontShader_m8C53D3D3DF5AC3F3928057E4B49B6BCB171AA5F7 (void);
extern void TextSettings_GetOSFontAssetList_mDE7F06506867FEA39ECE07E5030CD14CD680BA54 (void);
extern void TextSettings__ctor_m860D28B10258792A195E1C6391479E16D04CA8BF (void);
extern void FontReferenceMap__ctor_mA94127060F9F3FDC3093D9356832503BFBCE44F5 (void);
extern void TextStyle_get_hashCode_mA1F4D3630B6AE71C2A31F94B7054C28BDD96084F (void);
extern void TextStyle_get_styleOpeningTagArray_m123040451C694F92BC9700969B4682EC4BACF8BE (void);
extern void TextStyle_get_styleClosingTagArray_m0B50B87D1CCDC30647772E268433096209D7BC42 (void);
extern void TextStyle__ctor_mF1C354C192665DC3942DBDC0B7EECDBD653FF684 (void);
extern void TextStyle_RefreshStyle_m2D0771408F06C24EF303749ED8E656C800575BDB (void);
extern void TextStyleSheet_get_styles_m7916C62D70AA430314D85EA5B5A778FFAE1544DB (void);
extern void TextStyleSheet_Reset_m4C7EA0DF62767E14E3407398D533F1499647038B (void);
extern void TextStyleSheet_GetStyle_m648B766D750E1B37DD126918BF7EB22DDFD21D29 (void);
extern void TextStyleSheet_GetStyle_mF87D8108EEF60C3FC32A2B01D5C1C23D3F22A4D1 (void);
extern void TextStyleSheet_RefreshStyles_m92525DE6F7951D03D906E18EC89349AA2794AEC5 (void);
extern void TextStyleSheet_LoadStyleDictionaryInternal_m482E4CDF0CD2ED291F85869E141EEB5EC21F6D81 (void);
extern void TextStyleSheet__ctor_m1A0B93B55B12CE15FFF1053443179BD010022B75 (void);
extern void TextElement_get_elementType_m7BF97842479112227C1C3C83E0E94A176CD7D31A (void);
extern void TextElement_get_unicode_m40C69806537940F7BA1D3969713DA10CCBE57BC7 (void);
extern void TextElement_set_unicode_m99608D824B25E3529236C06BCC0983B5FC094F98 (void);
extern void TextElement_get_textAsset_m52383A3758AABF5BEA013155765BD1141479685A (void);
extern void TextElement_set_textAsset_m3F65429660C011F6F25B65D6BA7C4B2CF05659FA (void);
extern void TextElement_get_glyph_m101DBCCA0CDE2461B504174272A2FFCD53EA59E2 (void);
extern void TextElement_set_glyph_m6E8E2F1366089FA638680F1CF53F6F5027D022A5 (void);
extern void TextElement_get_glyphIndex_m43F82F2F998D640DEDBE6860EBE7B171DDF4FE56 (void);
extern void TextElement_set_glyphIndex_mFD72B93816998BE291DA6379EAF5E4153BC64F6C (void);
extern void TextElement_get_scale_mD16946900449FEE9E2F86B2C4C71E26F4491A0E6 (void);
extern void TextElement_set_scale_m83FC0850B2B0F31BDFC779954923F50FD06DC03F (void);
extern void TextElement__ctor_m8CB701D9A4C0444E834F178D97E9FC63E6D7E0B9 (void);
extern void TextElementInfo_ToString_mA643B4D503B51EB7632B7E352D699E63DDD29985 (void);
extern void TextEventManager_ON_FONT_PROPERTY_CHANGED_mC5C27C15B5C5715EE7B079A45D09914AB12B479C (void);
extern void TextEventManager__cctor_m616826AB5C10C1D16331F079CAF25B9440697C4F (void);
extern void TextGenerator_get_IsExecutingJob_m2570EC49336A66E65C8429B8516F8E79578A955C (void);
extern void TextGenerator_set_IsExecutingJob_m876815F0F8AC65A3B9521C9691BFC452357F0D5C (void);
extern void TextGenerator_GenerateText_mCF9D4EB0CC6D0BEA0136B23FCEE39EA9A48D6B5F (void);
extern void TextGenerator_get_isTextTruncated_m9F4D94C358488A77D334F3877DB94E04C52B8939 (void);
extern void TextGenerator_GenerateTextMesh_mAB70FC29A49A6C4F8211EA977E37C66BE67D1831 (void);
extern void TextGenerator_ValidateHtmlTag_m87FDAB842DB322DD2F533814602E24428190B366 (void);
extern void TextGenerator_ClearMarkupTagAttributes_m6047C48E973FC0E5A524AEB3F78D20E958E747C0 (void);
extern void TextGenerator_SaveWordWrappingState_mC07B2C5977EECE10216F8C6AC9CC4204F7EF1936 (void);
extern void TextGenerator_RestoreWordWrappingState_mA63B3DD2C02E61CD8670A32A53163AF6BF765F61 (void);
extern void TextGenerator_SaveGlyphVertexInfo_m0CD6E1D45488FFC6675294AC64F40AC23C986A09 (void);
extern void TextGenerator_SaveSpriteVertexInfo_m4B47901F01927E7CC4E486A1C4354AFBF4D138A5 (void);
extern void TextGenerator_DrawUnderlineMesh_m307EA8034106ACD13F89CC7E78C5DE08CCCCEFAE (void);
extern void TextGenerator_DrawTextHighlight_m4046F4CC59C6DD8FE5B0BD97DB8BFE015B829389 (void);
extern void TextGenerator_ClearMesh_m68BA46B0365FC730BA5D2E6BDF2528BD370B2D83 (void);
extern void TextGenerator_LayoutPhase_mE6488553B1AEC28B6E6F2A216C30588CA508990E (void);
extern void TextGenerator_ParsingPhase_m65E0C35911D968AB823AC4700D9E3E1D4E8C1FB2 (void);
extern void TextGenerator_InsertNewLine_m00109EA00343212A7FD05D49E7DBF81DBFE4B5E4 (void);
extern void TextGenerator_GetPreferredValues_m00CB10940AD7561839EBA19B67E32B92B19F1CB6 (void);
extern void TextGenerator_GetPreferredValuesInternal_m125B070164DFEA503C67525D1F418DAF41300ABD (void);
extern void TextGenerator_CalculatePreferredValues_mCD2ED8A220C3BA31EAB8AF6CED02E3277A723EAA (void);
extern void TextGenerator_Prepare_mD0A24977334138340CA73FB9787627373C6AA255 (void);
extern void TextGenerator_PrepareFontAsset_m6C6A2A767DB5BF4BC32E6591D625D33E6C03A330 (void);
extern void TextGenerator_SetArraySizes_m780796D50B2A5406E06F493503DA82BF5DA08A0C (void);
extern void TextGenerator_GetTextElement_mBF596DEE5D061411E04C283461A7B84D415DEC95 (void);
extern void TextGenerator_PopulateTextBackingArray_m1CC14B29C1BA4A763D3AF938B4E0920E45D75AB2 (void);
extern void TextGenerator_PopulateTextProcessingArray_mEC6B2EE86D363FF3F7CEE50C77A6124A0A27DA16 (void);
extern void TextGenerator_PopulateFontAsset_m46B39A380C3A810B02E43C8E89623FEEC54DEF00 (void);
extern void TextGenerator_ComputeMarginSize_m485F8B01196058B15F597DE99D6F6A47FA539D3F (void);
extern void TextGenerator_GetSpecialCharacters_m77E456375F75AA87D3D33E8B15241E946E1B0968 (void);
extern void TextGenerator_GetEllipsisSpecialCharacter_m9CB8856D34D4B6B0C5BFB200ABFE2FFA4B3AEA60 (void);
extern void TextGenerator_GetUnderlineSpecialCharacter_mBDF79614A582C3D08886E593DD03D38809F0CFA9 (void);
extern void TextGenerator_DoMissingGlyphCallback_m643F3C7C677B4F98BFE251055ECE1E588BEFFB04 (void);
extern void TextGenerator__ctor_m52E4D01DC28BDF753BF52F6501E7FD2FB2B30D90 (void);
extern void MissingCharacterEventCallback__ctor_m22C62F2B7DAAEC494F16008EEA0F192BE77E4AC4 (void);
extern void MissingCharacterEventCallback_Invoke_m5BF78AFFA87C08BC81EC893548949E960E0797D4 (void);
extern void SpecialCharacter__ctor_m6697A8BF272F0144733EE12368C038F45E99F969 (void);
extern void TextGenerationSettings_get_renderedText_mFD039407ECD06E5380E60936DEA1D23250E0C276 (void);
extern void TextGenerationSettings_set_renderedText_m25E0BDD52F62728B9AA802E5605176A778A1CC5D (void);
extern void TextGenerationSettings_get_text_mEC99436FE537C0C5F6B94997B785E445F6DD1924 (void);
extern void TextGenerationSettings_set_text_m6A2B95D427C9CA7FB2EDB4A9302ECECB2BF657D5 (void);
extern void TextGenerationSettings__ctor_mA20608A16443434DAE9FEF0BF8BD076270FA660E (void);
extern void TextGenerationSettings_Equals_mA5EDDF0453F2A7314AF5E1FE29F4138CD97E52D5 (void);
extern void TextGenerationSettings_Equals_mDCEEB056B70FC65ED6065E3BFE8D69D823DFEFD0 (void);
extern void TextGenerationSettings_GetHashCode_m1F750434FCE1853C36A579827B064619E85453E0 (void);
extern void TextGenerationSettings_op_Inequality_m01EC5AAB9B6A14F1E7CF3C05A738F509479B25C4 (void);
extern void TextGenerationSettings_ToString_mF8AB635EB369C7F66AC3B396934158CF3B6BA6CE (void);
extern void RenderedText__ctor_m5E28340FCE0357D83ED3B1177C6CEC98ED6472CE (void);
extern void RenderedText__ctor_m96430C7045F316918FD79C218263B55A240D083A (void);
extern void RenderedText__ctor_m770AEA85C46BE852A0A8DC504DE2D57A4458406B (void);
extern void RenderedText__ctor_mA4DB8477929189BAD3F68C72F4384282592890E8 (void);
extern void RenderedText_get_CharacterCount_mFEE0D0D5C8A7FD6A60CE7ACB3957FD83C76CB06E (void);
extern void RenderedText_GetEnumerator_mC1D9E5B2EA09158C7802ACFC60C2C8BCA2FC9E2C (void);
extern void RenderedText_CreateString_m90A3B193313D89CE3E3B32767AB3B5A8CA96ADB3 (void);
extern void RenderedText_Equals_m7024D63D324DF45CDEFEA65600AF84AC12AD8C47 (void);
extern void RenderedText_Equals_m83C00E78CD558AEB7441DA4A3CD747A0536C7AFB (void);
extern void RenderedText_Equals_m71BF21AB72D97E26E954F5B967A7F26B645F9105 (void);
extern void RenderedText_GetHashCode_mB77F278664A8CBD695F3C5B523FF765528A286E3 (void);
extern void Enumerator_get_Current_m5C193C555FEB096F6BA1F5663D817497567DDD0B (void);
extern void Enumerator__ctor_mE1DAD4EACF9B07D76BFCA18112F41A0B72EC8CE8 (void);
extern void Enumerator_MoveNext_mC26293738E22339239A5C2417D6FEE1E8B74946A (void);
extern void TextBackingContainer_get_Capacity_m8A8EE5A2670CBA7DEF97AA76DCAE4B90DE74A3E7 (void);
extern void TextBackingContainer_get_Count_mB42F11C4CB2C7D67D039811AE8BBDBBC13F0C416 (void);
extern void TextBackingContainer_set_Count_m78573180C571ED79DF38DC0A837E536A6869FE97 (void);
extern void TextBackingContainer_get_Item_m00B920D306C1039BB4E8E3DACFF86130A4ADA612 (void);
extern void TextBackingContainer_set_Item_m7C358387720AA2C2978B1CA8EA84A59E4490737D (void);
extern void TextBackingContainer__ctor_m46B1D0F45A11DA593305FA9D33D191A0E60CDBE7 (void);
extern void TextBackingContainer_Resize_m9ABA1F50B99CFE494EE450304F6BF71541217698 (void);
extern void CharacterSubstitution__ctor_mBB5C3EA59D985711FE3DF1F266D648201E18CE29 (void);
extern void Offset_get_left_m83657AF289FA1DB8B5D4007B8310573B76AA6D82 (void);
extern void Offset_get_right_m45AEBB7DE1D42A9A7234FB0DCE4E92420060D3FB (void);
extern void Offset_get_top_mD62FECE7914DF9723A872AAD91BDB07295C6E0F4 (void);
extern void Offset_get_bottom_m3BC4AB202A1B7D7D5A65EF746CDA1A73B5D8866C (void);
extern void Offset_get_zero_mF5B6D7C3F437FA438844A0B3EF405D805F1D1958 (void);
extern void Offset__ctor_mBF4CFCEFCC225000639F695A3021128FDD2E29A7 (void);
extern void Offset_op_Equality_m122A34D50DB0E70BDEEC631D0082E9CFB8D19C8E (void);
extern void Offset_op_Multiply_mE5215371DD76A27676FF67C992C065BC456A8131 (void);
extern void Offset_GetHashCode_m951A99E76473F284CD7DAC8577CDAA13F421E038 (void);
extern void Offset_Equals_m7FD59DC3FC3848900FDE2D3EC70B28869AC0B19D (void);
extern void Offset__cctor_mB8571222B76084876413C594C17AC5A343B40732 (void);
extern void HighlightState__ctor_mDBB71C58F46D7BDC518026AC796D24F2D9B36D3F (void);
extern void HighlightState_op_Equality_m6E4A396D3C2C5932BCCE96E7B3AE42E37E447923 (void);
extern void HighlightState_op_Inequality_m2DFBCB59E593F72191BFBBD7424A8C6151E68272 (void);
extern void HighlightState_GetHashCode_m52F707DADCC7F767D809342CBCDC94AF834150BA (void);
extern void HighlightState_Equals_mE0643D119E2942355F5FFED759BC329326E0C849 (void);
extern void TextGeneratorUtilities_Approximately_m696ABB909732F536F1FF83EA8CE34CF53266794D (void);
extern void TextGeneratorUtilities_HexCharsToColor_m4D7AB2E490DA31C514A42A880AB35CE9CBAF77B4 (void);
extern void TextGeneratorUtilities_HexToInt_m41648DAEE872433A0AFA82018A9539ECC5C0FFC6 (void);
extern void TextGeneratorUtilities_ConvertToFloat_m93B85749154AAFB614CD246876786B75C98AE11C (void);
extern void TextGeneratorUtilities_ConvertToFloat_m37E1CF11E22ED6528B166907211426EEE6D0A4F8 (void);
extern void TextGeneratorUtilities_InsertOpeningTextStyle_mF71E0B0C1B5E938C5AAC7F8FB3CD5278DEEC2408 (void);
extern void TextGeneratorUtilities_InsertClosingTextStyle_m08B150E030816A5084205B49DA40DED97E0C7036 (void);
extern void TextGeneratorUtilities_ReplaceOpeningStyleTag_m67FC3FFDE1912D2E7C2DC2BED4C5BA250B1DB705 (void);
extern void TextGeneratorUtilities_ReplaceOpeningStyleTag_m6F65A6D9D8DEA4915B30DEBFF43850B9D7063252 (void);
extern void TextGeneratorUtilities_ReplaceClosingStyleTag_m9DD77D4EACF2389DF2631F515A23C11DC5E58A3B (void);
extern void TextGeneratorUtilities_InsertOpeningStyleTag_m94153F78A4B8F7A1811D2C1E9567996E39616F60 (void);
extern void TextGeneratorUtilities_InsertClosingStyleTag_mD6A4B3357D6478C5770AEE460F61917584B905DB (void);
extern void TextGeneratorUtilities_InsertTextStyleInTextProcessingArray_m7CC6FF13CD9B2F3BD04C60C2A2B527960C6D1D09 (void);
extern void TextGeneratorUtilities_GetStyle_m236E7C5EBFA951D1D4B2C3C7803BBEA5C2FD1812 (void);
extern void TextGeneratorUtilities_GetStyleHashCode_mA4CDB93771348C1236A8E9BE1EB4A9D5C057D516 (void);
extern void TextGeneratorUtilities_GetStyleHashCode_m7B5002A635CF32D023E543FDE814E0E958A89EF1 (void);
extern void TextGeneratorUtilities_GetUTF16_m5B397339CD29B370A27D3BA3B8BEFC12E8C56434 (void);
extern void TextGeneratorUtilities_GetUTF16_m4E03C41F3B5323D6234DEC0A312F13CEAACCA8E6 (void);
extern void TextGeneratorUtilities_GetUTF32_m334BA95AED813976AC79E68EB677BADB5CB15CE3 (void);
extern void TextGeneratorUtilities_GetUTF32_mAF367B8C1D5B586B49AED2B69E5E7ECEF3378D0D (void);
extern void TextGeneratorUtilities_FillCharacterVertexBuffers_mE0CCB8DA0D27F37DCFC4E47E89697D8823A8FCE8 (void);
extern void TextGeneratorUtilities_FillSpriteVertexBuffers_mD1AECFE4D4356A6925BF056E15CF84118313412B (void);
extern void TextGeneratorUtilities_AdjustLineOffset_m811C187EA3E41781116F0C7A679B05BB27874123 (void);
extern void TextGeneratorUtilities_ResizeLineExtents_m2EA9BE32A38D5E075DEF8EDA9EC01766E45C0F85 (void);
extern void TextGeneratorUtilities_LegacyStyleToNewStyle_m8E9C09A7BE7B27A4EC73ADD747BFD9A4DE7E656C (void);
extern void TextGeneratorUtilities_LegacyAlignmentToNewAlignment_mA16F5CB58D3C8F352CAACB8DACA5AFED59D3ED63 (void);
extern void TextGeneratorUtilities_GetHorizontalAlignment_m3DA30E12E49D11E451FFBE24B875A16B514CD9A9 (void);
extern void TextGeneratorUtilities_GetVerticalAlignment_mBBD1285FD68BA3FBADB17BCC63C5638A971CDD13 (void);
extern void TextGeneratorUtilities_ConvertToUTF32_m6295E74C04568A52624812F2E615A7F25F235C70 (void);
extern void TextGeneratorUtilities_GetMarkupTagHashCode_mFFDE1B0B5CD9774F83C988C5D436D1AD01AAD843 (void);
extern void TextGeneratorUtilities_GetMarkupTagHashCode_m951A939A8B3B0BE3229CB1A94E79FF123C8EF6DE (void);
extern void TextGeneratorUtilities_ToUpperASCIIFast_m359D6A8BE78E2C74BA677D8453799487962EDE99 (void);
extern void TextGeneratorUtilities_ToUpperASCIIFast_mEEED07AD0989B1DF84D559CDE3A397A9F2EA913C (void);
extern void TextGeneratorUtilities_ToUpperFast_mE1809281C56E4137C6794B2E94D38BBFA68DBAAE (void);
extern void TextGeneratorUtilities_GetAttributeParameters_m261C1E8FB533D3570153B2BAF0D671C5DF4B58DB (void);
extern void TextGeneratorUtilities_IsBitmapRendering_m93C5008776EEDD84825ED2133CDA0FC66DD56EEA (void);
extern void TextGeneratorUtilities_IsBaseGlyph_mEE0E7D6C3FB32204C2299FBA2B9F7C51E06F80FE (void);
extern void TextGeneratorUtilities_MinAlpha_mB52BE8C9C82C15B23D29BF606465B16DD4B1F7E5 (void);
extern void TextGeneratorUtilities_GammaToLinear_m37B603C94918DB93477EFF98E8A77FD4D8B0C8FB (void);
extern void TextGeneratorUtilities_GammaToLinear_m5D4B51EF525F9238F6644BD47106DACCB78797D7 (void);
extern void TextGeneratorUtilities_IsValidUTF16_m944B75A058B351075C02F1DA61B688FAF1186DE8 (void);
extern void TextGeneratorUtilities_IsValidUTF32_mD6B22F5E6EAD47537B906859CB093622EECF716D (void);
extern void TextGeneratorUtilities_IsEmoji_m84855B4FDA2F5CE4FE0A7231AD6EEF30DB941CFA (void);
extern void TextGeneratorUtilities_IsEmojiPresentationForm_mE187DF246A5CD1794D085E96FFC0BA7AE1930BF7 (void);
extern void TextGeneratorUtilities_IsHangul_m5A23BA8E0EBE57243E2E96A248B3F6570A87A966 (void);
extern void TextGeneratorUtilities_IsCJK_m2F2718B1203271CC2C501C5054590299FBCA5B7D (void);
extern void TextGeneratorUtilities__cctor_m01201F8A0A4161B232AB9D179AB74877D4E17D85 (void);
extern void TextLib__ctor_mF8D936A838D79316C758F83145B710655DFC03B1 (void);
extern void TextLib_GetInstance_mD30A84164E4E7C3364F03AD35130CD717E2F375B (void);
extern void TextLib_GenerateText_m13A550DB25F00892F7D78D4B030FCAA570BC4FAD (void);
extern void TextLib_GenerateTextInternal_m7D3C05F5B323902C68CD2F01123F73B32E45A84B (void);
extern void TextLib_MeasureText_m6580802198F2FADA81B762D54C168EB7AEE2B22A (void);
extern void TextLib_FindIntersectingLink_m4DE95020DDDEEECFE33A8740C92A969D579DC543 (void);
extern void TextLib_GetInstance_Injected_m168216684370B5808000BE0A8FAE90E15C8486D9 (void);
extern void TextLib_GenerateTextInternal_Injected_m7AEB4D48072F79EB02A71B0DC764C331204BBEC4 (void);
extern void TextLib_MeasureText_Injected_m1D2675BE31277F6CC3141C53B02FD412C3C6D447 (void);
extern void TextLib_FindIntersectingLink_Injected_mE6E1252F6C0ED48E688152B7521FEEF75A884B10 (void);
extern void BindingsMarshaller_ConvertToNative_mE836D629252E1DDD28A8EA9C4C131B4852552943 (void);
extern void TextGenerationInfo_Create_m5111A4C2F3696D5ADBD1F70E636AA44BA8E01A0F (void);
extern void TextGenerationInfo_Destroy_mE2BCA3944ED096FF13C9DE612FCAA6A786E9DF86 (void);
extern void TextSelectionService_Substring_mC9DB8C3D47FE1629EEC9D0D89A4EF6A08CFCC5D3 (void);
extern void TextSelectionService_SelectCurrentWord_mC83009B68895B57B21D29EF536EECF7347BA9CBB (void);
extern void TextSelectionService_PreviousCodePointIndex_m45CB1B5E202D7B53367F3D08AA7827ED7A98038C (void);
extern void TextSelectionService_NextCodePointIndex_m527BA266FB15DA3B08A6617828B1C3CEE39A19C5 (void);
extern void TextSelectionService_GetCursorLogicalIndexFromPosition_mBA2DD838B1D97C6D457C3C2BA65BE432CEEEDB17 (void);
extern void TextSelectionService_GetCursorPositionFromLogicalIndex_m65E335DD05F6743F25AB797EFB23407AE30964FD (void);
extern void TextSelectionService_LineUpCharacterPosition_m429F67543D0621D328360979D816ABE95E6BB1BC (void);
extern void TextSelectionService_LineDownCharacterPosition_m4F52F3E56C4B27A5D1C524415921044106CF25A2 (void);
extern void TextSelectionService_GetHighlightRectangles_m566945AD89280E50889D1453C15F9AA2A0D9858E (void);
extern void TextSelectionService_GetCharacterHeightFromIndex_mF2A5D41465225808B081A5510EFD20C97391F4E8 (void);
extern void TextSelectionService_GetStartOfNextWord_m2BA6E18E802ADAC83667AE413A773C9E9354CCD9 (void);
extern void TextSelectionService_GetEndOfPreviousWord_m92F062A11E21FD682B4801E4B26161D2C0FC1B06 (void);
extern void TextSelectionService_GetFirstCharacterIndexOnLine_m5229D2CC50462416B53D129B524701861A7D444C (void);
extern void TextSelectionService_GetLastCharacterIndexOnLine_m7A908AEB27123913863F9F9AD55DF5F296FB472A (void);
extern void TextSelectionService_GetLineHeight_mEEADDDBB8F94B913DB7FA0D9ABB5B339E5B1615E (void);
extern void TextSelectionService_GetLineNumber_mA715A3FF339C2136A805EA44F6956561FC1F3340 (void);
extern void TextSelectionService_SelectToPreviousParagraph_m1BBE29062FB29FB4D6764D88D2F0BC0F2E3EBAA6 (void);
extern void TextSelectionService_SelectToStartOfParagraph_m129E7A0FDA646AFD8B25C3288D611A0E19E96AD6 (void);
extern void TextSelectionService_SelectToEndOfParagraph_m2AC2A628E1D3BB615243FD916D1CA47B9FBD1986 (void);
extern void TextSelectionService_SelectToNextParagraph_mE88FD507ABB1735C6FCDD7096A8DBA767C87FE82 (void);
extern void TextSelectionService_SelectCurrentParagraph_m73D982DBEAB009DE2559E8FCD1F147EEF0892938 (void);
extern void TextSelectionService_Substring_Injected_m6B91E70BBCF772F89152D77B572F5239AACE2E14 (void);
extern void TextSelectionService_GetCursorLogicalIndexFromPosition_Injected_m538B3D5B3D8DA25755AFE7FA224FD3D4364E7458 (void);
extern void TextSelectionService_GetCursorPositionFromLogicalIndex_Injected_mFF821F2B56F7036EFB569C3BE66B8E272925768E (void);
extern void TextSelectionService_GetHighlightRectangles_Injected_m31DFC05DB12DE6AC39C7C221BA80C1A3B7E37736 (void);
extern void TextHandle__ctor_m0E8BD79BF9F66EED2A87EA09B246B712DDEED9C9 (void);
extern void TextHandle_Finalize_mB3704C5A55586426C9B22FADA0C5207FB9E5C984 (void);
extern void TextHandle_InitThreadArrays_m2E49138B4E0D4F54538207E77DE0685957F23F6D (void);
extern void TextHandle_get_settingsArray_m4CFA0E8F9B3E29B41E0E18949AF5D567F85F82E7 (void);
extern void TextHandle_get_generators_mCBD7F4350C23561F33C524F4C79D47DF66B047A1 (void);
extern void TextHandle_get_textInfosCommon_m1E82D8C5A9EEA975E483336173869F9C9CA59F1B (void);
extern void TextHandle_get_textInfoCommon_mBE430AB83498AB18C506CE685FE584A7DDE1DA8D (void);
extern void TextHandle_get_generator_m85DB7A6FAFB8B38CAAB9495A0C2E234DD2F60BD0 (void);
extern void TextHandle_get_settings_m924DCD63BED9F000A224620C4A1FD1E8DEF51D10 (void);
extern void TextHandle_get_preferredSize_m2D3DA9F4778C3F35E1FE4BE637D173D6F6BF5804 (void);
extern void TextHandle_set_preferredSize_m9192F51B748162E27A87A09A0FA40FFEA364F34B (void);
extern void TextHandle_get_TextInfoNode_m51F4F2E0AE0333E1996DDAB34E3883D41A2D4053 (void);
extern void TextHandle_set_TextInfoNode_m310BBBDEF5E0B6A9C15B25192FE5698120D35CBC (void);
extern void TextHandle_get_IsCachedPermanent_mC63CD6C07312D65B00A06165DC9C564EFE7D6553 (void);
extern void TextHandle_set_IsCachedPermanent_mC1BD778CAF118C89DB68E1D42D065B314491E2E9 (void);
extern void TextHandle_get_IsCachedTemporary_m2EB6D0791424D7F76530A84A777A62F9A17AAD95 (void);
extern void TextHandle_set_IsCachedTemporary_m9203496A1717C888AC5DF6BD4DBC5738593B460D (void);
extern void TextHandle_get_useAdvancedText_mE7649150D29DE7D2768B084FB7140617F813BB7B (void);
extern void TextHandle_get_characterCount_m58420F9E2A2AF85169DCFCE4EFA7291E966615F2 (void);
extern void TextHandle_AddTextInfoToPermanentCache_m6A1549020930BF0356032E2A12C8C98B00558F54 (void);
extern void TextHandle_AddTextInfoToTemporaryCache_m99B5D63C51AEC0B3494DF6750C1B0F6648C0355C (void);
extern void TextHandle_RemoveTextInfoFromTemporaryCache_m078F98710A4FD1722F0932C65EEE801155088605 (void);
extern void TextHandle_RemoveTextInfoFromPermanentCache_mB6AE8BE8E9180FD0539472F63B53648C19CEF484 (void);
extern void TextHandle_UpdateCurrentFrame_mDC4EC98BDB3E7DA3588FB85229D532069E5D8F19 (void);
extern void TextHandle_get_textInfo_mA9F2BFB37F7ADA773731AFBC3B53FDD858D87FEE (void);
extern void TextHandle_SetDirty_m485BF8AC302B5A6DC3F63BC8DE3A0D823C2B7F2D (void);
extern void TextHandle_IsDirty_mB36F6490BB2EC7F57091D47A9582AF1BE6D5B086 (void);
extern void TextHandle_get_IsPlaceholder_m009132CDAC2C0E7C785A4CA97F4DF2B7B2A1B1D0 (void);
extern void TextHandle_IsElided_mF2AB6B8A1E01EE5FD2301E9BF77BEE5BC99C4ED5 (void);
extern void TextHandle_UpdatePreferredValues_m16C579932E755BC3FD8D82085F75EC011A44AD59 (void);
extern void TextHandle_Update_mD9BFF1818EC7158DB744B719BDD9C8235AE2368C (void);
extern void TextHandle_UpdateWithHash_mC4F89BB34B092BE7F5E2219531A4541454BB97BA (void);
extern void TextHandle_PrepareFontAsset_m09DBCA8383E73C03046A2B72F443F5CAE77F61B4 (void);
extern void TextHandle_UpdatePreferredSize_m10613CD9F9F36B26EF2045DC196CC8BA9F4919A3 (void);
extern void TextHandle_ConvertPixelUnitsToTextCoreRelativeUnits_m472293EA75E31A85F0DEF2DEE1CAE688CB1A5BBD (void);
extern void TextHandle_GetLineHeightDefault_mA4195042076C97B09260B329C0A8D052A8105B2E (void);
extern void TextHandle_GetCursorPositionFromStringIndexUsingCharacterHeight_m082A44C87BA6376E99A5BD37090429F4A3CF0E0E (void);
extern void TextHandle_GetCursorPositionFromStringIndexUsingLineHeight_m44EE4238CC96507DCEB45A28D61E969C877011FE (void);
extern void TextHandle_GetHighlightRectangles_mEAC7CB5CB926D25D78BD7F8A3697603AB14BCA98 (void);
extern void TextHandle_GetCursorIndexFromPosition_m68B7D720ED1589CC46538FACB50E7F7E56AA701E (void);
extern void TextHandle_LineDownCharacterPosition_mDD7F4379B59B9CAF7431CCC3E4056CF3511ECF9F (void);
extern void TextHandle_LineUpCharacterPosition_m69F1091BCD4A92E343B60966F4C49A9E36106C5B (void);
extern void TextHandle_FindIntersectingLink_m9D72AF4B459885AEFB03A0FF212241F8532B9132 (void);
extern void TextHandle_GetCorrespondingStringIndex_mDA160FA6E48243DAD913D24CA9095C1CC1450AE3 (void);
extern void TextHandle_GetLineInfoFromCharacterIndex_m1B157F587FBD4CEEF9135A24E79256BE849B807F (void);
extern void TextHandle_GetLineNumber_mED5D753BFDB5DDB5145EEDC829EA8D4EF1D305B1 (void);
extern void TextHandle_GetLineHeight_mAC48AA68AFCC8EDE5C52EF69941ADAD3B144539E (void);
extern void TextHandle_GetLineHeightFromCharacterIndex_mA935CA07C41CEA0C7447033337D05CB2652A1D62 (void);
extern void TextHandle_GetCharacterHeightFromIndex_mA512F4B21032917955542D5E71D611A55E6F1F0D (void);
extern void TextHandle_Substring_m3B3C2EC9E167E4C74AB319DE140780560976EDEC (void);
extern void TextHandle_PreviousCodePointIndex_m7BD6CE7A75924E4AE08D5FA885B036F5D983EEB4 (void);
extern void TextHandle_NextCodePointIndex_m59631E23176BDCBB5ED792AB0E521F90BDA0D018 (void);
extern void TextHandle_GetStartOfNextWord_mC0F6A773ACA7BFE20E6785ABA0D183BE1BD84B2E (void);
extern void TextHandle_GetEndOfPreviousWord_mBD60CE374828B5794B8C16A67E38E032D71DE964 (void);
extern void TextHandle_GetFirstCharacterIndexOnLine_m631B6581B25FBE6A666BB0B1488673E09786A7D2 (void);
extern void TextHandle_GetLastCharacterIndexOnLine_mFDF4AD3FF30611192C0118CBC836BA1755F3000A (void);
extern void TextHandle_IndexOf_mD0CDAB3319422D67356DBC547E91A08882D001B2 (void);
extern void TextHandle_LastIndexOf_m7A2F2860D56B0C90A73958E02006CAA1D6BEACC4 (void);
extern void TextHandle_SelectCurrentWord_mC2F2F8B7A1ED894DC478781774A4C1F728631C3F (void);
extern void TextHandle_SelectCurrentParagraph_m7BC0C69779C5B11C26E5D02D57C56A611033652D (void);
extern void TextHandle_SelectToPreviousParagraph_m6E718F46C68ABC46845CD39E4B606B85947F3B2B (void);
extern void TextHandle_SelectToNextParagraph_m16601CF807B7A64898D67CA6EA5BECC296570BB2 (void);
extern void TextHandle_SelectToStartOfParagraph_m4DE404748B8A558DFDCA27A3256E94245CD3DA97 (void);
extern void TextHandle_SelectToEndOfParagraph_m9DEE8C168D515A0B26A291B2A3356CA50D8BD36E (void);
extern void TextHandle_IsAdvancedTextEnabledForElement_m12DA5360680D35F3B945CD4E1F976127F3EE4D0D (void);
extern void TextHandle__cctor_m3EFFF534A8E9459492960B615C91F18081422439 (void);
extern void U3CU3Ec__cctor_mCAD10D256AA33CABDB9D1557537FC3E9E5791457 (void);
extern void U3CU3Ec__ctor_mE8360FCB61EE3220EA9C9CB6801D610BE09142D4 (void);
extern void U3CU3Ec_U3CInitThreadArraysU3Eb__4_0_mCF68501B75B6CB0B993D71DF65368A5283DB16FD (void);
extern void U3CU3Ec_U3CInitThreadArraysU3Eb__4_1_mA224B8DA6F9DA3A6739F510020E1A2934ABEF0A3 (void);
extern void U3CU3Ec_U3CInitThreadArraysU3Eb__4_2_mC2C94977DDA63B3C58384A1BB6C06337C78C05D2 (void);
extern void U3CU3Ec_U3Cget_settingsArrayU3Eb__7_0_m1D5600B2942B290610B6C31285C7E308AAB4BB03 (void);
extern void U3CU3Ec_U3Cget_generatorsU3Eb__10_0_m740BEC468DCDEB6C6A4598DB15212EC281D9D37B (void);
extern void U3CU3Ec_U3Cget_textInfosCommonU3Eb__13_0_m99B7E3B18B4609B05EFAD07DBF15DC3EA591491E (void);
extern void TextHandlePermanentCache_AddTextInfoToCache_m1C616E90C43C4E66CC51A854906B5DE5EB39B4BE (void);
extern void TextHandlePermanentCache_RemoveTextInfoFromCache_m2F1117399668FE56B9748DFC5CC08E2C8A6D3FE6 (void);
extern void TextHandlePermanentCache__ctor_m1E871678633B506E9BC7B0C5DE21E8B7F1B50F8F (void);
extern void TextHandleTemporaryCache_ClearTemporaryCache_mEFD7828E924D9EDB408EB21943D5CE037335C59E (void);
extern void TextHandleTemporaryCache_AddTextInfoToCache_m5FC534D96C0BFDD0AF066A5505F2D5FF653EE00D (void);
extern void TextHandleTemporaryCache_RemoveTextInfoFromCache_mFB501203B3D05153DBE9AB2CB6A9C3E33CA6050F (void);
extern void TextHandleTemporaryCache_RefreshCaching_mAD296DA1C2B6E1E9BE3D3B14ECC251451F3B2A7B (void);
extern void TextHandleTemporaryCache_RecycleTextInfoFromCache_mEDF5CB6B0DFF3EE879FFE1BE2A28EEBC29F3DAEB (void);
extern void TextHandleTemporaryCache_UpdateCurrentFrame_mE33A6DAC9CBA7D232E88B08B49897FF83EE6B966 (void);
extern void TextHandleTemporaryCache__ctor_m4D067665AB7AF075868ECEA6E108142A2CCC3A21 (void);
extern void TextInfo_get_vertexDataLayout_m51EB99E4BAB733094A5AEE4539F76C39E3DD0AFE (void);
extern void TextInfo_set_vertexDataLayout_m77EA7C758DF4F919E27628EB3AA0CE12F2BCEBA6 (void);
extern void TextInfo_RemoveFromCache_m5727A31E536090A5EA1D56165A48625E69F6DFC0 (void);
extern void TextInfo__ctor_m0D2430C2058B0D6B9C7F21C2E62A4A8D6CA5ED8F (void);
extern void TextInfo_Clear_m60412774208F9D920707448E71E89C99233D9128 (void);
extern void TextInfo_ClearMeshInfo_mCA598F01C7F302CFCD0F508E2DBF072E66CA74F3 (void);
extern void TextInfo_ClearLineInfo_m986C886D34A324C8C4D30F9D8EF24AC242A10AD7 (void);
extern void TextInfo_ClearPageInfo_m57DE207346C5245799E50F8A57B56B65665B7430 (void);
extern void TextInfo_GetCursorPositionFromStringIndexUsingCharacterHeight_m53316961E455FBC1E28FB426A7BB0FDD702C882E (void);
extern void TextInfo_GetCursorPositionFromStringIndexUsingLineHeight_m92FA693363C8A09B287E537ED70617DD52EFF7B9 (void);
extern void TextInfo_GetCursorIndexFromPosition_mEFA727A71DA09BBDF62135A5F05532DFD967F55A (void);
extern void TextInfo_LineDownCharacterPosition_m8683D3F9CDDAC905CD2785EE8951F579CBCBAEFB (void);
extern void TextInfo_LineUpCharacterPosition_m166FEAC52CE6DA4980BA78CE7C3C77247CE9F27C (void);
extern void TextInfo_FindNearestLine_mE69F45519747DEE0F3BCC90FFD7B619D0352DB26 (void);
extern void TextInfo_FindNearestCharacterOnLine_m1150A5C41A48FCC3746736DD2AE7DD9C8249BAE0 (void);
extern void TextInfo_FindIntersectingLink_mBE527632D2D2B31BDADE678B59FB1BF596E933C0 (void);
extern void TextInfo_GetCorrespondingStringIndex_mB7380B38E2873CAC40E66805B8AF3942C59054B3 (void);
extern void TextInfo_GetLineInfoFromCharacterIndex_m4ED8ACCF098AD57E7B5EC26FC26BFECAA0B064AB (void);
extern void TextInfo_PointIntersectRectangle_m3E0050FEAB5B3DF87B666EEAF9056B1D427F87BC (void);
extern void TextInfo_DistanceToLine_mB4F796AA01255D2949718D2FD6CCB86CCD8676D8 (void);
extern void TextInfo_GetLineNumber_m8D725D36FE6B36FDC076911B66E9B0896C591656 (void);
extern void TextInfo_GetLineHeight_mEFB708CFEF226B16BD35D8E422D1CC6FE5EA8A80 (void);
extern void TextInfo_GetLineHeightFromCharacterIndex_m6B7E544BE97C3E0130ACE2C8D8F90C67C386E0B9 (void);
extern void TextInfo_GetCharacterHeightFromIndex_mC6DB161E05C109C71A04F03F0C5DECA896C92BB9 (void);
extern void TextInfo_Substring_mA367A49634FD8CB29C564253F98C9615A19CC572 (void);
extern void TextInfo_IndexOf_m71674062F329F8A71F76BE506FD2AAEA10124358 (void);
extern void TextInfo_LastIndexOf_m5F4AB42B005B2F9CFE08C28F6E0933F3C22DD728 (void);
extern void TextInfo__cctor_m0B33CCC02D4B32FB18864B6B7657695E9AEB3F0D (void);
extern void FontStyleStack_Clear_m989659363648B27540168E46F23E1EF9877C06E0 (void);
extern void FontStyleStack_Add_m26E701C9F052EEEBB213B9B8BC6CB8F1F8F6AFCB (void);
extern void FontStyleStack_Remove_mC2B4F44A6596E92D6992DBCA298648F8A7416CAB (void);
extern void TextResourceManager_AddFontAsset_mAAD97609F00F02146B283982E5DE667EB7A692EA (void);
extern void TextResourceManager__cctor_m54E40FCFB355A0C90B56366B057F307F75832868 (void);
extern void FontAssetRef__ctor_m5553FC4D7E51DE97D70CE09E1C99B002A61FFDB5 (void);
extern void TextShaderUtilities_get_ShaderRef_MobileSDF_m9014F695545914ADF74FFD8A461B1DD2590A163B (void);
extern void TextShaderUtilities_get_ShaderRef_MobileSDF_IMGUI_mFB26197BEF0BDC723970439AD27E94EB2EF5D46C (void);
extern void TextShaderUtilities_get_ShaderRef_MobileBitmap_m54CAA31B3B374C0517BF356C80B34F169C89F649 (void);
extern void TextShaderUtilities_get_ShaderRef_Sprite_mEB8685333A53464F71519FA69438381EABEFAA02 (void);
extern void TextShaderUtilities__cctor_m5E3AAC923C6500CFCB7F775610B085AD43CDE4EE (void);
extern void TextShaderUtilities_GetShaderPropertyIDs_mBA841B8CACE147593703FF6E7E2804FF2017DE52 (void);
extern void TextUtilities_ToUpperFast_m7E98D5D17030A387012E262648346523DCC3AA0B (void);
extern void TextUtilities_GetHashCodeCaseInSensitive_m3241BDD3A6F82A061C21BF5D347B690D732F5B59 (void);
extern void TextUtilities_GetTextFontWeightIndex_m758569A41B59CA4E6A6EE3E73299B41C25FC4E67 (void);
extern void UnicodeLineBreakingRules_get_leadingCharactersLookup_m1DAC015D7E37112EAE0437E6472AEA0719DFF3DC (void);
extern void UnicodeLineBreakingRules_get_followingCharactersLookup_m5510A21873DC5DA66F4A2DFA4C26A5EFAD494D8B (void);
extern void UnicodeLineBreakingRules_get_useModernHangulLineBreakingRules_mD86D283CE7BA23A0174B9227A7BD915D3D9FD464 (void);
extern void UnicodeLineBreakingRules_LoadLineBreakingRules_m4686111E39B00E27AA6AD88762793EEFCCC14A75 (void);
extern void UnicodeLineBreakingRules_GetCharacters_m93663235F54D32D801E67994714B79759D2F4137 (void);
extern void UnicodeLineBreakingRules__ctor_mD94779B4996B56EA84C847EC4DD287AB1A8ADE85 (void);
static Il2CppMethodPointer s_methodPointers[744] = 
{
	EmbeddedAttribute__ctor_m9EC595866FA01247FB88943D9880C87559FC1EFF,
	NullableAttribute__ctor_m35D97A5CDE83B9F8998E650C6C1C650E8FF44AB5,
	NullableAttribute__ctor_m92C5041F3EBA384F0F2B2748AAD8BDAD812E6D5F,
	NullableContextAttribute__ctor_m494F6EEDFC6FA5345FA4CFB264B0659FEDC1BDAD,
	NativeTextGenerationSettings_get_hasLink_m2D703AAD28BFE44DA7FB6B40A679F26D21718910,
	NativeTextGenerationSettings_CreateTextSpan_m63AE7655BC8C77D4614541BA5426F0E1FECA728A,
	NativeTextGenerationSettings_get_Default_mC9C0F0330DD4B0E8B20D78F4A126C10C5A485297,
	NativeTextGenerationSettings_ToString_m84C204918B5C4F40D1EADDB371DCE76FF098E84D,
	U3CU3Ec__cctor_m46FBAA2482CE6467605F08CD73073673047070B1,
	U3CU3Ec__ctor_m6792C1379E4ED043802F1A4106223CF4E247A551,
	U3CU3Ec_U3Cget_hasLinkU3Eb__17_0_m98C2509F2425C63BB98B51C9189240F7BCCA809D,
	TextSpan_ToString_mB43B06596C5F8782F98817A120945168F1A893C9,
	RichTextTagParser_tagMatch_mB42907CB050EE08A22B58A843C2866A2409EA3DF,
	RichTextTagParser_SpanToEnum_mE1DDD45167FB2EBA5B030DF8823DF2BEB4DBA186,
	RichTextTagParser_FindTags_m3FF6ECE75133FD1990A6AB52AB57E2A2F4E3AA68,
	RichTextTagParser_PickResultingTags_mA3B1F54CD2E2BCBBEE710AD4737DA42B3C07AE7B,
	RichTextTagParser_GenerateSegments_mA068B63EE134BA6A7EF4B072690902E89662BDE3,
	RichTextTagParser_ApplyStateToSegment_m08B617CB0FFDB3DB445F53FBB40536B7A85AE90B,
	RichTextTagParser_AddLink_mE38CFF0C55D49F8528258B58C4B302AD3392A5E3,
	RichTextTagParser_CreateTextSpan_mA05BB9D0792E6B991CCF66298659CECF1C0084B6,
	RichTextTagParser_CreateTextGenerationSettingsArray_m56B01D0B6FE522B13F4FC21093A1274D322861A9,
	RichTextTagParser__cctor_m0EB9B76F35AAF19494036DD28568E68A7B27D875,
	TagTypeInfo_get_EqualityContract_mB3CF3FD825662931FE86695414D02674731AFCA7,
	TagTypeInfo__ctor_m74267566395F3250A672157AB0E98A2551B1806D,
	TagTypeInfo_ToString_m1342960CB528ABB59728C886E6771016529483E9,
	TagTypeInfo_PrintMembers_m6E2CC8929B55363B69CF0CF8241846EB5DBA79C7,
	TagTypeInfo_GetHashCode_mCBF213A31B84649CA841A6D829B66A8D96D7B9E6,
	TagTypeInfo_Equals_m817CA3DA02CDB35F7D551F2E3758E6E65C756550,
	TagTypeInfo_Equals_mC80117C96252E06A7D7141753A3A0D068A65DE1C,
	TagValue_get_EqualityContract_m00E5101C2526BC713F54ABF39B166BA78184715B,
	TagValue__ctor_mE71E165C404C672A9A6E7E5289C98480D8141005,
	TagValue__ctor_m4A36B903BF6F2F9F28B8F1AE5423BA9323187885,
	TagValue_get_StringValue_m3BCE1D8318343EA9E53C39F41BD39072BAC25942,
	TagValue_get_NumericalValue_m415D37C150AB01B6B0258F0F924EA4B368BB7BEA,
	TagValue_get_ColorValue_m15B3229E66795A64B41FD75CFD1F2410547565E6,
	TagValue_ToString_mDCC527BA92B1236A29FAA11B8FE948F0B4299C5E,
	TagValue_PrintMembers_m24131FF07C0B85E32DE5F764E5D9E0A9CD7C113D,
	TagValue_GetHashCode_m2915DE1C63BC335C2265A963E95E815E5E9A995B,
	TagValue_Equals_mD893FA591B10D0282CC0AC81A985F08DD4A47E96,
	TagValue_Equals_m515F3E172DD41E2448C492D3F738C9D1841450A2,
	ParseError_get_EqualityContract_mB5140327B441DF6B62DCE939319010DA1112D3F6,
	ParseError__ctor_m6373646BDA23AF427AB57DD1B690CEB3F2E3AE58,
	ParseError_ToString_m70DD13F900670887C5384CCF968B75C19BDCE86A,
	ParseError_PrintMembers_mEE74D0B217AF4F81CC96EE909D9DF42481CA90F0,
	ParseError_GetHashCode_mDE107EC7556BE750EE3C12ABE1CD1E1D4BB6BD30,
	ParseError_Equals_m1B64D22340D2A741B8BF64CFF6687B0AD8BF35FB,
	ParseError_Equals_m8E5224941B564114DBC9D9BBA24439ED311B1A64,
	Character__ctor_m5DCCE862D40487C733C29A233DB8513A9A6A02F6,
	Character__ctor_mEEAC42D4227E0053C8008C12B222CC208D781795,
	Character__ctor_m21FBFAF1F6324565246096EFFB81C3F9E15D43CC,
	ColorUtilities_CompareColors_m0F0F140129DEE889FB8AE3B2921C495E94B5E875,
	ColorUtilities_MultiplyColors_m81D3E41A86E195794977F31C5A944DB40FE734B4,
	FastAction__ctor_m837FFCD82DA457A7BFCC2EA03FBD3E358DA1F3EE,
	NULL,
	NULL,
	NULL,
	NULL,
	FontFeatureTable_get_glyphPairAdjustmentRecords_mABE78F7C2EA171927CC33170617D72E6C976323E,
	FontFeatureTable_get_MarkToBaseAdjustmentRecords_m73A1A8FCDB3E9629C15FC2568473B235B9A52D9B,
	FontFeatureTable_get_MarkToMarkAdjustmentRecords_m3DB78C6BBC2E41936F04E46107002DC5F2136B8F,
	FontFeatureTable__ctor_m5F00F284C63F1867F679A3250ABFC1393C27025C,
	FontFeatureTable_SortGlyphPairAdjustmentRecords_m2F5E2ED405FCAEE946CE5CF81163DDCC1B02A905,
	FontFeatureTable_SortMarkToBaseAdjustmentRecords_m2AF48E3FC40E5C970FCD9A4ACA4354FD3CD09004,
	FontFeatureTable_SortMarkToMarkAdjustmentRecords_mF4A796852F11F07614DF6434DB8E3122E94E7E3B,
	U3CU3Ec__cctor_m024C5AAB034EF6BBF574EA4EB3AE66D03E259CEA,
	U3CU3Ec__ctor_m51815D1379A3BDB616D65C006DA7AB32406723F4,
	U3CU3Ec_U3CSortGlyphPairAdjustmentRecordsU3Eb__24_0_m35574DBBB0E7B7C971A1BACA4934A37792FCD0B2,
	U3CU3Ec_U3CSortGlyphPairAdjustmentRecordsU3Eb__24_1_m9FF21AEEA154F6D846868FA78051C4E23C2ABA47,
	U3CU3Ec_U3CSortMarkToBaseAdjustmentRecordsU3Eb__25_0_mA88B3352AD07E2B6F17D92A70DB39D99F45585AB,
	U3CU3Ec_U3CSortMarkToBaseAdjustmentRecordsU3Eb__25_1_m37069B1EDD9DBDD69291CFDF48C95F71D8379C0B,
	U3CU3Ec_U3CSortMarkToMarkAdjustmentRecordsU3Eb__26_0_mC1E58F904A13796D7E050CAC2E9066D0202AC853,
	U3CU3Ec_U3CSortMarkToMarkAdjustmentRecordsU3Eb__26_1_mFD0F303D96190115E0218C32844A836A4437E61E,
	Extents_ToString_m8A1F748127EE9CCCD6FFAF4CE1F38E37C07831AC,
	LinkInfo_SetLinkId_mB4145264190D5C857705261CB27F87C6E10C3F3F,
	LinkInfo_GetLinkText_mE5F0F2128BB22FFE009BBBDE4EF0E921F95AA6D8,
	LinkInfo_GetLinkId_mE502F621D5C09B05C2C509E35404BED4420B4FDB,
	MaterialManager_GetFallbackMaterial_m10F67CE1AE1E0B9D8BA8AFC06110FADA2404B5A1,
	MaterialManager_GetFallbackMaterial_mB65C8D7625B0D0A0D623FC6AC545469AF6B7724D,
	MaterialManager_CopyMaterialPresetProperties_m2DB1A033E378F3DF347DEA0DC51F1E51776169F4,
	MaterialManager__cctor_mB6337D5678E1F45035C1F8EE33D389AB4CBA6527,
	MaterialReference__ctor_m044AAA2C1079EB25A5534A6E0FA2314F033DB15A,
	MaterialReference_AddMaterialReference_m13CC47A7CA6C8781EA68A355B36FCD5AFF467A40,
	MaterialReference_AddMaterialReference_mEE57C2284E5BE17BDD80C69165FAECB3B4CC1BCD,
	MaterialReferenceManager_get_instance_m30BD1A367C3807D0B42CF7156CE699B15E51ED07,
	MaterialReferenceManager_AddFontAsset_mF950087FC7EAA324CC15D3E19D62138A96F6FDF7,
	MaterialReferenceManager_AddFontAssetInternal_m6F5A4E5ED988BA6F482F015F051ACD19D7B0A005,
	MaterialReferenceManager_AddSpriteAsset_m7FBAAC41386CFA769FDDFF1630D041C4D008A322,
	MaterialReferenceManager_AddSpriteAssetInternal_m788619DC6BAD5B77E9419ACBDECBCCFE1A6AC97C,
	MaterialReferenceManager_AddFontMaterial_m76DC9390D0599D1AC898AE981C8756EA0E536AA6,
	MaterialReferenceManager_AddFontMaterialInternal_m732F46EF768A41B9519917F4FA1E746E056C745C,
	MaterialReferenceManager_AddColorGradientPreset_m1496DD123DB526F2B3F49DD3BCAED78A6B1B83EB,
	MaterialReferenceManager_AddColorGradientPreset_Internal_mF27270501EB3725B4CBE4C241B4A2FCD8D871BF1,
	MaterialReferenceManager_TryGetFontAsset_m499C7538909343667E9B837489662862CFF9FB56,
	MaterialReferenceManager_TryGetFontAssetInternal_m2FECC618624B12D200EB311F59CBEECA7CDBB69D,
	MaterialReferenceManager_TryGetSpriteAsset_m1909F75399C0A23DDE7DEDCA60476E1F1ED567EF,
	MaterialReferenceManager_TryGetSpriteAssetInternal_mC434A7C6DB005EDBBA52154E2AB0E36ED7083C84,
	MaterialReferenceManager_TryGetColorGradientPreset_m7442603626A04928C6C042BDAC3D3957B8C0AA99,
	MaterialReferenceManager_TryGetColorGradientPresetInternal_mD8018B3225786E71F804D629F3107AB75EE5212B,
	MaterialReferenceManager_TryGetMaterial_mDC2610737935CD2DE6B1F6302F63C2F3BF7E09CF,
	MaterialReferenceManager_TryGetMaterialInternal_mEBFC9CE0A6063B25FEA9070F22FD8AD27107ECE7,
	MaterialReferenceManager__ctor_mC102EC445A27BE8E3968ADB80EF8FEF3BCFB7778,
	MeshInfo__ctor_m3FD8AAF58EBCF22706EDB08B503B4FB2C108E86A,
	MeshInfo_ResizeMeshInfo_m22D30D08188ACAEBA3CE46383E5D2FFC3E8C519E,
	MeshInfo_Clear_m06992FEB7AC9B2AE1728BEDFC8D8A39DE1AAD475,
	MeshInfo_ClearUnusedVertices_m7B6003EF4CA72C0ABBA4D25DEA8B0BF3934B2830,
	MeshInfo_SortGeometry_m92046C53AA6AE75EE3627CE73846296AB3E99DD1,
	MeshInfo_SwapVertexData_mD145A4C2B45422DFCB2D9697956E16395E717C7F,
	MeshInfo__cctor_m667601EA787620405D7DB5F0B37E4D489DE067D9,
	NULL,
	NULL,
	FontAsset_get_fontAssetCreationEditorSettings_m024033F91B976A8EAA5CBE67D3DB2A756B91CF01,
	FontAsset_set_fontAssetCreationEditorSettings_mF7EE6A46807D78A7E99872171D2AD774DE20C7EB,
	FontAsset_get_sourceFontFile_m6B0E805BD1B7712F0B5135D157E96F3F40314830,
	FontAsset_set_sourceFontFile_m2E6D2AED5E5D2585A09E9BF830387DEB10A2F4E8,
	FontAsset_get_atlasPopulationMode_m5364C5A9E84969D8E4FF8436BD18A3F10BF90366,
	FontAsset_set_atlasPopulationMode_m1A9DD5C702ED0924B9C208F4CE5ADEACF9188268,
	FontAsset_get_faceInfo_mF020EC579E3C18A6279D55D86AF1C585031B49A9,
	FontAsset_set_faceInfo_mCCA87B67C4CA2C0A1F6D85FB1FAA09667996EDCD,
	FontAsset_get_familyNameHashCode_mF2DB211A5712A291B2D28FCDB7F7C29057770330,
	FontAsset_set_familyNameHashCode_mE1495199BCE7B771CC920E2DBB86A8AF1518CB55,
	FontAsset_get_styleNameHashCode_m3CD3D77F64DAEB31D8F69E4D7CC1AD0AC784ABF5,
	FontAsset_set_styleNameHashCode_mE1BE5B75DE1E9EA0F76569609E6C4FFDC57558BA,
	FontAsset_get_glyphTable_m212E940F74AEE62ACBB3374486296CA518D934B5,
	FontAsset_set_glyphTable_m2753BC6CEE011983C2B4B181867C3EB00CDE87D4,
	FontAsset_get_glyphLookupTable_mD04A90D8262F1963EDC472272B67BBFAF73DEEA5,
	FontAsset_get_characterTable_mC77FAE1355269834F7C8A2D46AFB4BFE7B7AD72D,
	FontAsset_set_characterTable_m44703292F669F2F6D4920EB9427077E24FB1512C,
	FontAsset_get_characterLookupTable_m7E76D6C706C5CEB04A9541C68AE6D9E5C75F0FFC,
	FontAsset_get_atlasTexture_mC49216F40093C7AC4FA5DA68F9F5C9FCC83B8F27,
	FontAsset_get_atlasTextures_mADD7A506F0444A1EE4F1D52536B0C5DA9BE35075,
	FontAsset_set_atlasTextures_m5315BA4903B77742EFA1E54CEA2AF12726B10A99,
	FontAsset_get_atlasTextureCount_mC20300C53E52D7A8351DE296BAD565268568119F,
	FontAsset_get_isMultiAtlasTexturesEnabled_mF222228A76102BB0EA593A60439D22F912761F1E,
	FontAsset_set_isMultiAtlasTexturesEnabled_mA470AF6D312989E752C68DC0FD5700235877566B,
	FontAsset_get_getFontFeatures_m6AC33ECD0C754EA278473023D0DF03F086AD962C,
	FontAsset_set_getFontFeatures_mC076D171BEA658A422307182B6C2A4555F6196AD,
	FontAsset_get_clearDynamicDataOnBuild_mC1F714E56F087B29E0A3B43D820EDCEB78E9EE75,
	FontAsset_set_clearDynamicDataOnBuild_mA1C6F298742DF78EC0F81157F0E04246F8B82F7E,
	FontAsset_get_atlasWidth_mE711550FDD4B5F988B77AB5D332A80A34B5CF364,
	FontAsset_set_atlasWidth_mFFB9D37EB1C648384ED1426B42E26A4104D329B1,
	FontAsset_get_atlasHeight_m306FBF7D35C39123A4770E147FAF95B1B8DE8086,
	FontAsset_set_atlasHeight_m7116DFD32F38971CE39D7F4BF84CB5217DCAA2B5,
	FontAsset_get_atlasPadding_m251A35FB5F499EE66CC2E2150CBEDB2C8C5D5581,
	FontAsset_set_atlasPadding_mB34AA836A3D02722ABED71B3583D767560CA956D,
	FontAsset_get_atlasRenderMode_m036D4BA220E5D4B0C407CA6BC1B09D8914B5058A,
	FontAsset_set_atlasRenderMode_m993764193CE75D57DC4CC755336596681A7866D2,
	FontAsset_get_usedGlyphRects_mE039AEF3AE45A15A86B2C0F774E6ED58AFA2F341,
	FontAsset_set_usedGlyphRects_mBF80C1063C0A274AD95F55C43DA734E126F6643F,
	FontAsset_get_freeGlyphRects_mCDAEF0519586C5248BBEDEAA85086CC117903E88,
	FontAsset_set_freeGlyphRects_mED3C0E01ABFC63CE700C701476BA2B66D112AA9B,
	FontAsset_get_fontFeatureTable_m7C4EB9A655B237CE02FAF7B8B16C2F2863FE5070,
	FontAsset_set_fontFeatureTable_m3FD11B99122416777808E1CE5414D7BA40920C3B,
	FontAsset_get_fallbackFontAssetTable_m43303BFBE8A8C55D8CE8A67C47EFAFF5A712CB69,
	FontAsset_set_fallbackFontAssetTable_mE22C3D2323111FD6A5DF9847FB812BD41E18832E,
	FontAsset_get_fontWeightTable_m8DADE0BCE53D53EEECE27025F7E94FDD4BF00099,
	FontAsset_set_fontWeightTable_m39D8B63BD3FCC773AAB5634C6D9314C713161814,
	FontAsset_get_regularStyleWeight_m6C4B4D4CAD36800E6E686A05A5DB8D4475F2707F,
	FontAsset_set_regularStyleWeight_m2D1E5440A5E1794A003FF087A87393DA9A114385,
	FontAsset_get_regularStyleSpacing_mB7EEEA236312F5AC31FD3B787808279206F521B1,
	FontAsset_set_regularStyleSpacing_m7CCE54FB9163D65A6B40269B2BDD30199023E797,
	FontAsset_get_boldStyleWeight_m804ACC85DD80DC72DB4BCC83C3FB866411F8EFCA,
	FontAsset_set_boldStyleWeight_m204B04CF9E98AD8669025BFDC0EF3CE9AB5CBBA2,
	FontAsset_get_boldStyleSpacing_mB8CF4F4880B110E41D566648FF1D995010CF1FF0,
	FontAsset_set_boldStyleSpacing_m62DAA35837E8563DD76E3D162B6DB59BE3804914,
	FontAsset_get_italicStyleSlant_m69E70060C6E7940B4ACE61F2B7CB8965F86DA96B,
	FontAsset_set_italicStyleSlant_m223875ED81B0397CA36E94A6F346AEE68510C0D2,
	FontAsset_get_tabMultiple_m9C0422A00BFCF82091F14F4E303E2717247350AE,
	FontAsset_set_tabMultiple_mC927B74D27FBB94245E09FE39D9F6749AF07017B,
	FontAsset_CreateFontAsset_mDBAB2C51721B94702B8706450DAF74FD2E757712,
	FontAsset_CreateFontAssetInternal_m6AA9E69176D6E61BDD7D59309A0509379C10F11A,
	FontAsset_CreateFontAsset_m37F0A299C881426F96D0D444AE5E95AE7E68E082,
	FontAsset_CreateFontAssetOSFallbackList_m0ADBCC935CAA11B764D84E21C39CC8321BC4A048,
	FontAsset_CreateFontAssetWithOSFallbackList_mA0DB907C4F9C070C7C28CAFE074EE76CBB03AC84,
	FontAsset_CreateFontAssetFromFamilyName_m0B15D684FEAA30BBDB993A8ED4A24A975B4B6D36,
	FontAsset_CreateFontAsset_mBC142F8527671635D9472BCC22C65A2E94368253,
	FontAsset_CreateFontAsset_m8C8C64410ED418883256F0277AA6ACB4514FFE0A,
	FontAsset_CreateFontAsset_m627EE2CFBDCEADB906A5CD26FB474D3C1A37A166,
	FontAsset_CreateFontAsset_m27CFD9D5831E873A379D2D063871A6A1BA9E00C9,
	FontAsset_CreateFontAsset_mEFD731F35B521BB833F532C0EE7637F97647E650,
	FontAsset_CreateFontAsset_mCD759D9A87211073FF1FFF46876CD4F8A32AF3F2,
	FontAsset_CreateFontAsset_m402BB043D44E6CDA7DDD1100EFE85F0963CB4673,
	FontAsset_CreateFontAssetInstance_m9114A363FC6DDEB899CC75C73A41CF903A59333E,
	FontAsset_GetFontAssetByID_m743704FE77E53C291724F4CC53DF449219C448D6,
	FontAsset_RegisterCallbackInstance_mF1E71A17E78B9C7F6853DB4D0112ECCC06ED59D4,
	FontAsset_OnDestroy_m3587016A089072C5C03168AA4C6AA1956FE12785,
	FontAsset_ReadFontAssetDefinition_m6D84DBCB130D530B2F78A7E24232D8A6A81AEC48,
	FontAsset_InitializeDictionaryLookupTables_m29A4AEF49CF11A0E49C229EF13B2262AE66757FF,
	NULL,
	NULL,
	FontAsset_InitializeGlyphLookupDictionary_m82782B7B5C602AD5097A016BF668868C0892CCF6,
	FontAsset_InitializeCharacterLookupDictionary_m8886A4CA911334BD319AB78F1CBBD68E13624BB6,
	FontAsset_ClearFallbackCharacterTable_mA684313E1CF74889B3DC97BF39195270F19E3582,
	FontAsset_InitializeLigatureSubstitutionLookupDictionary_m85D0338B542EEF7E5DE8224AA699730752F93FD3,
	FontAsset_InitializeGlyphPairAdjustmentRecordsLookupDictionary_mA1901466C14645EC72B595467630F276B09F980A,
	FontAsset_InitializeMarkToBaseAdjustmentRecordsLookupDictionary_m7BC52CF67C055F71B1E9A79B761F79A86E5D8716,
	FontAsset_InitializeMarkToMarkAdjustmentRecordsLookupDictionary_mC911E1C69D58A7490587C63473D65BA1DB775D56,
	FontAsset_AddSynthesizedCharactersAndFaceMetrics_m203BD62D0A537A6EA7CD7DBA1FF9A94301492933,
	FontAsset_AddSynthesizedCharacter_m6ABFCE6454A09D5CF7914F318DDC79198C47F9EA,
	FontAsset_AddCharacterToLookupCache_mB90E06CE313CC0BB6F81415BF8FB4E043108EED8,
	FontAsset_AddCharacterToLookupCache_m616C9F912D8CF75B1AA391BE362788FB8DEC0F59,
	FontAsset_GetCharacterInLookupCache_mA0F217E4902B0569BE722EABCC5EBDC1842ED108,
	FontAsset_RemoveCharacterInLookupCache_mC07C7791E07A35D8D93914F410903CBAC84BB9F0,
	FontAsset_ContainsCharacterInLookupCache_mDCD1EC1477C61DF6E612307A3965EF6A2056EE61,
	FontAsset_CreateCompositeKey_m842F3D888783D1F7F55A695FFC704A118840B5D8,
	FontAsset_LoadFontFace_m64C78A2FE5DA2E7029E43B467A1B242827B45B4F,
	FontAsset_SortCharacterTable_m9A551DF3B19E246E8C4BE86463E0ED1DEB27D321,
	FontAsset_SortGlyphTable_mC853714CB002D923A19C3A925BB24D6BF42A08CD,
	FontAsset_SortFontFeatureTable_m072B32D6D8C562F60D3D6CBCC7DCB3282EDD587F,
	FontAsset_SortAllTables_mACA7063865A460F5949E5B8A8D978D588124A094,
	FontAsset_HasCharacter_m6BAF48714E1BF5D8EE7ACF33F774C8C6EEE452F3,
	FontAsset_HasCharacter_mE87EEF6CDA1F4E1D6928CC9A3C01A91922D4FB21,
	FontAsset_HasCharacter_m3E405FA081E68243DDB6558FA03530686E894EFE,
	FontAsset_HasCharacterWithStyle_Internal_m78B49AB1F1C6A0C3A177EC9E0E92B2DEDB1E0DA5,
	FontAsset_HasCharacter_Internal_mC5BF3BF620A498A319DC33FF02F02742D0A9CFEA,
	FontAsset_HasCharacters_mD670CCEB48448CE5C1430B938F99D4FC659FB2F8,
	FontAsset_HasCharacters_m97A50BC627C163418CAE0B42A50893057B025E90,
	FontAsset_HasCharacters_m048839FDD1876CDCA3C5A744592545B46C75E15B,
	FontAsset_GetCharacters_m5CCEC5A0F89B0BC29B1EFEE9E33923F6879409F4,
	FontAsset_GetCharactersArray_m4CE08FECFCF3F9913B5B29DF0F2272FA4B3F945F,
	FontAsset_GetGlyphIndex_mF20097CDB68A8CE866E61D4C237FBB95257A9745,
	FontAsset_GetGlyphIndex_m7101FAA6F39074FDB45DE3DE6BEBDC276D03E04C,
	FontAsset_GetGlyphVariantIndex_m9D2C993281FC370D6DE57D783B2BFE94BD71B1BC,
	FontAsset_UpdateFontAssetData_mAAC0ED05410942C08E8EFD4678F9565FD8C373D4,
	FontAsset_ClearFontAssetData_m225ADFCBB0CFD481E18637F3D3FDFFEAFC6FE9A1,
	FontAsset_ClearCharacterAndGlyphTablesInternal_mFD829694B1BEC9B07C488FA99E8C238701FFE427,
	FontAsset_ClearCharacterAndGlyphTables_m4CE0F4F8421393E0D168A9D3AECF0C22FDE06D54,
	FontAsset_ClearFontFeaturesTables_mF7DFB9072C78EB088D529A168D1E3E4596FA79C9,
	FontAsset_ClearAtlasTextures_m5B320A65E1CD35F2C17E27F09158F8E9BDA9EA2B,
	FontAsset_DestroyAtlasTextures_mBE2810F8C55E286B5B7ABE24A6F9132F51CBE027,
	FontAsset_RegisterFontAssetForFontFeatureUpdate_m5D26FB74AB0C3AA8DC363DA517C1017641F85739,
	FontAsset_RegisterFontAssetForKerningUpdate_m0523A0E6C5C52374CD3187CB9A2D601EB0E36BAB,
	FontAsset_UpdateFontFeaturesForFontAssetsInQueue_m021524DE2B5F8ABE9DDA58447F896159EA15E0F3,
	FontAsset_RegisterAtlasTextureForApply_mAB5DC9E4B77C8E0FCADF0AD98E8D63C5229FC383,
	FontAsset_UpdateAtlasTexturesInQueue_m359EFCCB6F692F5F8546D4205F366B079075792E,
	FontAsset_UpdateFontAssetsInUpdateQueue_m67B9FE54C99FDC8FD3FE3471768C416083E36768,
	FontAsset_TryAddCharacters_m7F1D0CB7E4D9B8D3CE44D4D01F9CDCEFD4D1B46B,
	FontAsset_TryAddCharacters_m9618B4F12C004B8267E0D17ED81B94BE48D85119,
	FontAsset_TryAddCharacters_m5E282618D9ED92AD0112BC7B6B2C3B1066DDFA63,
	FontAsset_TryAddCharacters_mDA1C3A68799C84A80C27CDB84482684F6822137F,
	FontAsset_TryAddGlyphVariantIndexInternal_m59E4DBF931E9D1D5BD78C94179248E8B17ABE993,
	FontAsset_TryGetGlyphVariantIndexInternal_m5BE497F578235C39647D9BBD613F1D3A7F027245,
	FontAsset_TryAddGlyphInternal_mA41540AE85F2F11562E1DB5B763B37D29D9D497B,
	FontAsset_TryAddCharacterInternal_m41BFAA446B6BC9880AD3E03D6AC199C46BAD3F69,
	FontAsset_TryAddCharacterInternal_m407B178A59705227BA6CC1AF1EE17E5F901AD943,
	FontAsset_TryAddGlyphToAtlas_m79A28E53E32F937D3676947E7703F3F54FC4A12A,
	FontAsset_TryAddGlyphToTexture_m6CD601B197CDABD52881560CE6D295EBD6E696AB,
	FontAsset_TryAddGlyphsToNewAtlasTexture_m8F98FBF7A0EC1B37C4DB43536DA42D3864F6F3AB,
	FontAsset_SetupNewAtlasTexture_m38F81BE1582A15DDDB950E7AAC650CD9B7D14168,
	FontAsset_CreateCharacterAndAddToCache_m0325EB298F9AAC25F38C0EDDAEAA89DE96E306B5,
	FontAsset_UpdateFontFeaturesForNewlyAddedGlyphs_mD8A62062839B8D975FD9D27F2E31650742591E69,
	FontAsset_UpdateGlyphAdjustmentRecordsForNewGlyphs_mD1BEDEA9F9D0F016024F7C231EB10A6C02D29F44,
	FontAsset_UpdateGPOSFontFeaturesForNewlyAddedGlyphs_m0B5C369451E41ACBE2856832AEDAD5D0329B8E26,
	FontAsset_ImportFontFeatures_m5EBD1B255DE7F72C2028374FA01E5D8CFDAEB4A7,
	FontAsset_UpdateGSUBFontFeaturesForNewGlyphIndex_m9EFD3DFEF97AF01AF16DA1B63202261BCABD7BA5,
	FontAsset_UpdateLigatureSubstitutionRecords_mDEA6498E8E56015D0F27ABC6D546FB7820C48246,
	FontAsset_AddLigatureSubstitutionRecords_mC03697ED7E1F030F0D27186CB45AA41CE3C4A1B3,
	FontAsset_UpdateGlyphAdjustmentRecords_mD1C9297EA75EA767A823709CC39B6E57905E22A3,
	FontAsset_AddPairAdjustmentRecords_m04CD8E1CDF9FB571C124DC5AC526FD36D294B6DE,
	FontAsset_UpdateDiacriticalMarkAdjustmentRecords_m4D989B1B4C6E6FE7F3B9853D2E5D49316506E3B0,
	FontAsset_AddMarkToBaseAdjustmentRecords_mE7F211ED7856EBBE38EB53F75589EFEFA2600D79,
	FontAsset_AddMarkToMarkAdjustmentRecords_mF5B3428009326F0934D7C751E4EEAAC38F4ABA16,
	FontAsset_get_nativeFontAsset_mDC06A90902EB4D0F12898100D27CE21392032257,
	FontAsset_UpdateFallbacks_m2969A07345DAEF15127FDD2FE5AF253291A04D7A,
	FontAsset_UpdateWeightFallbacks_m5C92F81D09E59F733932E911E4163B881B160A37,
	FontAsset_UpdateFaceInfo_m237405CB07D4014F4F6C807F082E7F7507887F06,
	FontAsset_GetFallbacks_m2B1FDD4D2D947F1EB01A905B70AE133516466325,
	FontAsset_HasRecursion_m8DD6A2628A19E39736330C0760285DEC9F9FB1C3,
	FontAsset_HasRecursionInternal_m077ADF5BE6C08557D7ED213F85AEF145CF96BAC7,
	FontAsset_GetWeightFallbacks_m4B7596CCD2450BFAC7B5559369610051DFBC5CD7,
	FontAsset_UpdateFallbacks_mF9559D4A2B74EAD8C2736C7A032A08E537224F02,
	FontAsset_UpdateWeightFallbacks_mBC79323557BBF6C821A965664D63D548A0DCBC42,
	FontAsset_Create_m00CB9D0A80F9B9AF6D4FF4AA22D91D44BD3392FE,
	FontAsset_UpdateFaceInfo_m029F0B2932389C2245EAD2E137ED53B15900C0AB,
	FontAsset_Destroy_m3D53E650ED29327AF72DD31C97906C11A4CCB0AA,
	FontAsset_Finalize_m89E40E8DFD22ABB4DDD28A0D882D705C5DDDDDBF,
	FontAsset__ctor_mD55676BD025F9D05DBC9A5B32480E092169B9D45,
	FontAsset__cctor_m0F11465E3C2F3D158DB9BF403CE86298432BD00E,
	FontAsset_UpdateFallbacks_Injected_m57440C9C279DE16968E7DB30392FF6A8B53B1306,
	FontAsset_UpdateWeightFallbacks_Injected_m999DF41E926E829DF3C4D0AAA34B1A63C1A7366B,
	FontAsset_Create_Injected_m02BBCA74D470A637ECF6CC5430A9F4B3FD70253D,
	FontAsset_UpdateFaceInfo_Injected_m0EA7955663DE89C85FF2ABAC0B5C52870FC13831,
	U3CU3Ec__cctor_mEB4ED309CE67EB2C5633590466BD1C4EE90E0D5C,
	U3CU3Ec__ctor_m3CDF0903C024856278B5A7DF46C7EFCBA6F6B651,
	U3CU3Ec_U3CSortCharacterTableU3Eb__191_0_m53398D111DBAF3B96D00D9A3932CD90E5F665324,
	U3CU3Ec_U3CSortGlyphTableU3Eb__192_0_mC2605EC70E99D1F12C197E5D1DCF577C65BAB6B2,
	FontAssetFactory_CreateDefaultEditorFontAsset_mDA1CBA9F2B49F628BD1ACFFB46E2F7BDD198D720,
	FontAssetFactory_SetupFontAssetSettings_m8BEB0CA4D2E2623D76DEDBFC0A9CFA8261CC0F59,
	FontAssetFactory_SetHideFlags_m25B6A1A10811A91A1B0737A73C303625B435999F,
	FontAssetFactory__cctor_m85AF34C9ED54D11E03B2BF32C90DFCC2FC3E00AD,
	FontAssetUtilities_GetCharacterFromFontAsset_m854EBABBF60E9B80275BE56FA803B258D9B61D99,
	FontAssetUtilities_GetCharacterFromFontAsset_Internal_mF5271CACE66EF55DB6D72E37102051F14202D90A,
	FontAssetUtilities_GetCharacterFromFontAssetsInternal_mE53763E9CB71B0606391F6A0CC5524AADE1908BC,
	FontAssetUtilities_GetCharacterFromFontAssetsInternal_m3291A1DDB038692BCA5EDE6548043943E0EE1BD2,
	FontAssetUtilities_GetTextElementFromTextAssets_m8AAAF9F3A364454283DB8B6C8FE06C5D4278705D,
	FontAssetUtilities_GetSpriteCharacterFromSpriteAsset_mB9147480CA9223089D54748F37F227BC180D78E1,
	FontAssetUtilities_GetSpriteCharacterFromSpriteAsset_Internal_m5A04BE23599904FF0B23061BC9F7EDBF6AC7DB88,
	SpriteAsset_get_faceInfo_m54EC5227F682ED6A24F5633283258E6641CDA4DC,
	SpriteAsset_set_faceInfo_m060A5DBBD5941A53BFE9D45E2B637D88ED8223EA,
	SpriteAsset_get_spriteSheet_mC53205114A12A79F7495FA5F5EFC9948F151256B,
	SpriteAsset_set_spriteSheet_m1DE591615ABCBB4B10118BF4C0E1B57F559C6469,
	SpriteAsset_get_width_mC8BB271B0C76D6B0609C2B9220FFA00F07BD5C31,
	SpriteAsset_set_width_mE25BEA3FADDD8254C7A4FD664669B5D535162993,
	SpriteAsset_get_height_mF1EBD2DCEAE3BD57B4E80E21EB09BBF9F7539A49,
	SpriteAsset_set_height_m3C64D7A7037FEB68C2E329ABEE0318EDDBEF9FB8,
	SpriteAsset_get_spriteCharacterTable_m8D0D65C430AD8BC8C2BC8151DC4672CC0F690E0A,
	SpriteAsset_set_spriteCharacterTable_m38553B81E01B502CCD568A654E9EF3B0D0BCA92D,
	SpriteAsset_get_spriteCharacterLookupTable_mDB0BA7D703F58A05BB765AE44D6244D98C17DE03,
	SpriteAsset_set_spriteCharacterLookupTable_m38F3E7A0A52B82595C87E6A630B156E4D22F2E25,
	SpriteAsset_get_spriteGlyphTable_m491B3F4A5350C38D8B5166A60B7C43ED3608C0BA,
	SpriteAsset_set_spriteGlyphTable_m7402B7CF195E4B9F15DDA3273F49CA460A68DAD3,
	SpriteAsset_Awake_m3A012935612A7EB924A77B85EDCF6C09257F60BE,
	SpriteAsset_UpdateLookupTables_mCC7A470A65A72908C9CDBDDFD17A056188A5C7CE,
	SpriteAsset_GetSpriteIndexFromHashcode_mE73615D1D9A8BB45C3426197EC54B1A002642DE0,
	SpriteAsset_GetSpriteIndexFromUnicode_m321E02B6000E5F6673F5724155C3EF1DE3F5A66B,
	SpriteAsset_GetSpriteIndexFromName_mBCB684ED6E3DF5663A7FDA02CA69C99D9B17281B,
	SpriteAsset_SearchForSpriteByUnicode_mC50EFD6F2B78609891C357212CF65A8F18EB8A66,
	SpriteAsset_SearchForSpriteByUnicodeInternal_m34EB60FC8F44FCDEEE1FC2B61261BAE76DA6DB25,
	SpriteAsset_SearchForSpriteByUnicodeInternal_m691C0974B2011F07E6FD54AEA76ED5E1334278D9,
	SpriteAsset_SearchForSpriteByHashCode_m8B9CAB0028CC297570785693F2973918B31C9028,
	SpriteAsset_SearchForSpriteByHashCodeInternal_mE1AAA3464383B1964F2C98428A413B306730BFED,
	SpriteAsset_SearchForSpriteByHashCodeInternal_m3A73CAD6EEBB517196C227B01CAE6AB4AFE69293,
	SpriteAsset_SortGlyphTable_mA700CE5246D5798FA65779BE53179FFF4FFED6E5,
	SpriteAsset_SortCharacterTable_m5447649977AF2C9F62A14415B44CDDD897A53AE1,
	SpriteAsset_SortGlyphAndCharacterTables_m0E2B691E7C1F284E12A88B47B705307E83C7D927,
	SpriteAsset__ctor_mE03F69799389DE8D90E69CD70054955033C4ED3C,
	U3CU3Ec__cctor_m84A11BA2AE10FB375A960D167ABF5F559DACAE3A,
	U3CU3Ec__ctor_mEFC122BF1D0D0CA8F0EAE9CE353C37A8CFABB5F3,
	U3CU3Ec_U3CSortGlyphTableU3Eb__44_0_mDA0ABBA7C3A604F677CA4C213CBBA053C79A0873,
	U3CU3Ec_U3CSortCharacterTableU3Eb__45_0_m48BF102E54D9CC4B1311BFFCACB235207416E60A,
	SpriteCharacter_get_name_mD5A9CC908308BB48D459973C8844FE1FD7C925B1,
	SpriteCharacter__ctor_m0B3812DF9A667CA2C3AA321DF3403197EEBC83BA,
	SpriteGlyph__ctor_mFFE2D9AA5A28EA0C20C10694569A1E23A664BB4D,
	TextAsset_get_version_m2316C9F212FE4A13335032354A496CAEC86CB25B,
	TextAsset_set_version_m24718A4A6A86F4CE41D9ED1E0F796CCF367516CD,
	TextAsset_get_instanceID_m843A6CAA7FE9322CD19546671D3F0E90A0E27AFB,
	TextAsset_get_hashCode_m4D519E837097D8869A8D38EBD11611FADE411092,
	TextAsset_set_hashCode_mC6ED3271A5EFC05562FD1083BE1C872CB69CFF74,
	TextAsset_get_material_m4B9C02D34426436FDB01F1963A9FDC11D75604EF,
	TextAsset_set_material_mD9988165763E0E72C7FB7537760899EC1841C829,
	TextAsset_get_materialHashCode_m9BAA469D5760D87EEC6E09B9ED3C5902B75017E6,
	TextAsset_set_materialHashCode_m15EE7CCAF81DBAA326049F00788BCC918FDB2631,
	TextAsset__ctor_m2C43EAC2D75854E6724A235794DEAF8A1AF9E786,
	TextColorGradient__ctor_mAF03C3B68C29D94ED1F1517E65477CFC2B078FAC,
	TextColorGradient__ctor_m0712D0C09E7BCBC00909CEB71B43F2276ADB8B55,
	TextColorGradient__ctor_m0A6B9264A8FBABEEC5BA84353D9B3DD7999DD2FF,
	TextColorGradient__cctor_mC5AE96BE4461EF9AE84CE4E3765B712D6A64693E,
	TextSettings_get_version_mB5B7C3F04FFC8DCDFCFF63CFFF7612B768E267CB,
	TextSettings_set_version_m7BEE644EEDD77892C295170F9B2B68BC45C7C6CF,
	TextSettings_get_defaultFontAsset_mC6280464BFEE081DB23243BB94E49C72A0885A1F,
	TextSettings_set_defaultFontAsset_mC0137D9AEB9EE7A9E1B3D7C499414EDD217AF3BA,
	TextSettings_get_defaultFontAssetPath_mF7B1713753CFAE048C745C3572332CE18CD51D3B,
	TextSettings_set_defaultFontAssetPath_m35111382F9C2E3C76FA6B77BABEE8149BDDCC7ED,
	TextSettings_get_fallbackFontAssets_m332526E834C994425141A58C968FD40320573F98,
	TextSettings_set_fallbackFontAssets_mEBEC769B2FF339BDB44D72CB48F0010577889772,
	TextSettings_get_fallbackOSFontAssets_mA595476A990F2F1CBD78743707F9CF8F6CC4BBA9,
	TextSettings_GetStaticFallbackOSFontAsset_m4449E418309F6CACD851E0459EF2DEEE18172C17,
	TextSettings_SetStaticFallbackOSFontAsset_m9C1063549F7710BDE66D57AA1E875C1E15D83C7C,
	TextSettings_GetFallbackFontAssets_m4FFB3FC5647A87AB65BDF7C8F4119B0ECFD4B56C,
	TextSettings_get_matchMaterialPreset_m4675979547AE4C83E680260EAE5ACBC4FAC53B87,
	TextSettings_set_matchMaterialPreset_mD5A3DDE2D93FE2872C869A2AE0B0C984CF5BC12C,
	TextSettings_get_missingCharacterUnicode_mA707E5E6633633BBB3BAFB96B97A5A995100F3F3,
	TextSettings_set_missingCharacterUnicode_mE3E3E2F902BE89B9FE78F03D3B8C508583D1CDA3,
	TextSettings_get_clearDynamicDataOnBuild_m0C130F53F106D77250F38239FB73DCB97299FB79,
	TextSettings_set_clearDynamicDataOnBuild_m86B73E68953D42C6AE4210BEA5D678768155A1AD,
	TextSettings_get_enableEmojiSupport_mC3DEBE44D903676B740164D050DF8266DF6D44B1,
	TextSettings_set_enableEmojiSupport_m79FB14818237BD3F40335E1F39560690F834A31B,
	TextSettings_get_emojiFallbackTextAssets_m438C7B82C8926311E01DD9DA46C5BA306D1BC9B2,
	TextSettings_set_emojiFallbackTextAssets_m55237A9CD208CAC211165C721FC045BE3364ACD6,
	TextSettings_get_defaultSpriteAsset_m8FA900F9747B7ADBCD2A2F612E7D977DB58D6445,
	TextSettings_set_defaultSpriteAsset_m6083C46D0B3AFB6816363CF2564D2DB2D1ECFEB9,
	TextSettings_get_defaultSpriteAssetPath_mF57875222B7FACC9B69369305EEEB53FFE7986E0,
	TextSettings_set_defaultSpriteAssetPath_m9C49C00A96230DAF828E629C106B229A4552F753,
	TextSettings_get_fallbackSpriteAssets_mC6D74088EBDFDFBACD581FE4AB4EAA447ED242AD,
	TextSettings_set_fallbackSpriteAssets_mB28744780B58AE04BA7F1275508FF7DEE4E485E5,
	TextSettings_set_s_GlobalSpriteAsset_m77A3A39C3263DF665FCE30F0ADB1B04B15E73E33,
	TextSettings_get_s_GlobalSpriteAsset_m3C98B253850B44AA610FE92AE729D035DE17AAE3,
	TextSettings_get_missingSpriteCharacterUnicode_m496D85B0D88C91F2A5CC9929C233EAA84831523E,
	TextSettings_set_missingSpriteCharacterUnicode_mFE1AFD04C4AA793559E0A157C9E5472E3C3F6701,
	TextSettings_get_defaultStyleSheet_mDA420960556C00405FA66CBD2DA36807F8F4B4F2,
	TextSettings_set_defaultStyleSheet_m2867A0B047E623E4485F10EE717FF0B46BAAECEE,
	TextSettings_get_styleSheetsResourcePath_mE65A055D1C99CA2048BE6F246258EF262E43C4E6,
	TextSettings_set_styleSheetsResourcePath_m19A5C893047D78B43AC5EB43FB9EA79792464C05,
	TextSettings_get_defaultColorGradientPresetsPath_m1571454580E1F0DC859D3F201BB1F6355A5DBB8E,
	TextSettings_set_defaultColorGradientPresetsPath_m951D5A7AE71C87BFA5B9B8CE8EA2F78A474FE01D,
	TextSettings_get_lineBreakingRules_m96E2C32D4F08309D904B0BCD83CEBE8CD6716A04,
	TextSettings_set_lineBreakingRules_mB1174FE0F923443130A3D4686C119EDB0430992D,
	TextSettings_get_displayWarnings_m3CA9FCB44B30CC06F54CD3716D68285FF844DF83,
	TextSettings_set_displayWarnings_m08373EE3900E911B4021CBF8AF915DA69856CC20,
	TextSettings_OnEnable_mBFC6BA8BA147B68E9FB956B2D496A2E8C2972A13,
	TextSettings_InitializeFontReferenceLookup_m34B5FB3D61296A7620196398D267812EEFCE0B04,
	TextSettings_GetCachedFontAssetInternal_m9308E0DDEF8F6D8ACA17DAADB43745C860788202,
	TextSettings_GetCachedFontAsset_m439117E0F4363B6D7790046FE0C3829DB6EF4AAD,
	TextSettings_GetFontShader_m8C53D3D3DF5AC3F3928057E4B49B6BCB171AA5F7,
	TextSettings_GetOSFontAssetList_mDE7F06506867FEA39ECE07E5030CD14CD680BA54,
	TextSettings__ctor_m860D28B10258792A195E1C6391479E16D04CA8BF,
	FontReferenceMap__ctor_mA94127060F9F3FDC3093D9356832503BFBCE44F5,
	TextStyle_get_hashCode_mA1F4D3630B6AE71C2A31F94B7054C28BDD96084F,
	TextStyle_get_styleOpeningTagArray_m123040451C694F92BC9700969B4682EC4BACF8BE,
	TextStyle_get_styleClosingTagArray_m0B50B87D1CCDC30647772E268433096209D7BC42,
	TextStyle__ctor_mF1C354C192665DC3942DBDC0B7EECDBD653FF684,
	TextStyle_RefreshStyle_m2D0771408F06C24EF303749ED8E656C800575BDB,
	TextStyleSheet_get_styles_m7916C62D70AA430314D85EA5B5A778FFAE1544DB,
	TextStyleSheet_Reset_m4C7EA0DF62767E14E3407398D533F1499647038B,
	TextStyleSheet_GetStyle_m648B766D750E1B37DD126918BF7EB22DDFD21D29,
	TextStyleSheet_GetStyle_mF87D8108EEF60C3FC32A2B01D5C1C23D3F22A4D1,
	TextStyleSheet_RefreshStyles_m92525DE6F7951D03D906E18EC89349AA2794AEC5,
	TextStyleSheet_LoadStyleDictionaryInternal_m482E4CDF0CD2ED291F85869E141EEB5EC21F6D81,
	TextStyleSheet__ctor_m1A0B93B55B12CE15FFF1053443179BD010022B75,
	TextElement_get_elementType_m7BF97842479112227C1C3C83E0E94A176CD7D31A,
	TextElement_get_unicode_m40C69806537940F7BA1D3969713DA10CCBE57BC7,
	TextElement_set_unicode_m99608D824B25E3529236C06BCC0983B5FC094F98,
	TextElement_get_textAsset_m52383A3758AABF5BEA013155765BD1141479685A,
	TextElement_set_textAsset_m3F65429660C011F6F25B65D6BA7C4B2CF05659FA,
	TextElement_get_glyph_m101DBCCA0CDE2461B504174272A2FFCD53EA59E2,
	TextElement_set_glyph_m6E8E2F1366089FA638680F1CF53F6F5027D022A5,
	TextElement_get_glyphIndex_m43F82F2F998D640DEDBE6860EBE7B171DDF4FE56,
	TextElement_set_glyphIndex_mFD72B93816998BE291DA6379EAF5E4153BC64F6C,
	TextElement_get_scale_mD16946900449FEE9E2F86B2C4C71E26F4491A0E6,
	TextElement_set_scale_m83FC0850B2B0F31BDFC779954923F50FD06DC03F,
	TextElement__ctor_m8CB701D9A4C0444E834F178D97E9FC63E6D7E0B9,
	TextElementInfo_ToString_mA643B4D503B51EB7632B7E352D699E63DDD29985,
	TextEventManager_ON_FONT_PROPERTY_CHANGED_mC5C27C15B5C5715EE7B079A45D09914AB12B479C,
	TextEventManager__cctor_m616826AB5C10C1D16331F079CAF25B9440697C4F,
	TextGenerator_get_IsExecutingJob_m2570EC49336A66E65C8429B8516F8E79578A955C,
	TextGenerator_set_IsExecutingJob_m876815F0F8AC65A3B9521C9691BFC452357F0D5C,
	TextGenerator_GenerateText_mCF9D4EB0CC6D0BEA0136B23FCEE39EA9A48D6B5F,
	TextGenerator_get_isTextTruncated_m9F4D94C358488A77D334F3877DB94E04C52B8939,
	TextGenerator_GenerateTextMesh_mAB70FC29A49A6C4F8211EA977E37C66BE67D1831,
	TextGenerator_ValidateHtmlTag_m87FDAB842DB322DD2F533814602E24428190B366,
	TextGenerator_ClearMarkupTagAttributes_m6047C48E973FC0E5A524AEB3F78D20E958E747C0,
	TextGenerator_SaveWordWrappingState_mC07B2C5977EECE10216F8C6AC9CC4204F7EF1936,
	TextGenerator_RestoreWordWrappingState_mA63B3DD2C02E61CD8670A32A53163AF6BF765F61,
	TextGenerator_SaveGlyphVertexInfo_m0CD6E1D45488FFC6675294AC64F40AC23C986A09,
	TextGenerator_SaveSpriteVertexInfo_m4B47901F01927E7CC4E486A1C4354AFBF4D138A5,
	TextGenerator_DrawUnderlineMesh_m307EA8034106ACD13F89CC7E78C5DE08CCCCEFAE,
	TextGenerator_DrawTextHighlight_m4046F4CC59C6DD8FE5B0BD97DB8BFE015B829389,
	TextGenerator_ClearMesh_m68BA46B0365FC730BA5D2E6BDF2528BD370B2D83,
	TextGenerator_LayoutPhase_mE6488553B1AEC28B6E6F2A216C30588CA508990E,
	TextGenerator_ParsingPhase_m65E0C35911D968AB823AC4700D9E3E1D4E8C1FB2,
	TextGenerator_InsertNewLine_m00109EA00343212A7FD05D49E7DBF81DBFE4B5E4,
	TextGenerator_GetPreferredValues_m00CB10940AD7561839EBA19B67E32B92B19F1CB6,
	TextGenerator_GetPreferredValuesInternal_m125B070164DFEA503C67525D1F418DAF41300ABD,
	TextGenerator_CalculatePreferredValues_mCD2ED8A220C3BA31EAB8AF6CED02E3277A723EAA,
	TextGenerator_Prepare_mD0A24977334138340CA73FB9787627373C6AA255,
	TextGenerator_PrepareFontAsset_m6C6A2A767DB5BF4BC32E6591D625D33E6C03A330,
	TextGenerator_SetArraySizes_m780796D50B2A5406E06F493503DA82BF5DA08A0C,
	TextGenerator_GetTextElement_mBF596DEE5D061411E04C283461A7B84D415DEC95,
	TextGenerator_PopulateTextBackingArray_m1CC14B29C1BA4A763D3AF938B4E0920E45D75AB2,
	TextGenerator_PopulateTextProcessingArray_mEC6B2EE86D363FF3F7CEE50C77A6124A0A27DA16,
	TextGenerator_PopulateFontAsset_m46B39A380C3A810B02E43C8E89623FEEC54DEF00,
	TextGenerator_ComputeMarginSize_m485F8B01196058B15F597DE99D6F6A47FA539D3F,
	TextGenerator_GetSpecialCharacters_m77E456375F75AA87D3D33E8B15241E946E1B0968,
	TextGenerator_GetEllipsisSpecialCharacter_m9CB8856D34D4B6B0C5BFB200ABFE2FFA4B3AEA60,
	TextGenerator_GetUnderlineSpecialCharacter_mBDF79614A582C3D08886E593DD03D38809F0CFA9,
	TextGenerator_DoMissingGlyphCallback_m643F3C7C677B4F98BFE251055ECE1E588BEFFB04,
	TextGenerator__ctor_m52E4D01DC28BDF753BF52F6501E7FD2FB2B30D90,
	MissingCharacterEventCallback__ctor_m22C62F2B7DAAEC494F16008EEA0F192BE77E4AC4,
	MissingCharacterEventCallback_Invoke_m5BF78AFFA87C08BC81EC893548949E960E0797D4,
	SpecialCharacter__ctor_m6697A8BF272F0144733EE12368C038F45E99F969,
	TextGenerationSettings_get_renderedText_mFD039407ECD06E5380E60936DEA1D23250E0C276,
	TextGenerationSettings_set_renderedText_m25E0BDD52F62728B9AA802E5605176A778A1CC5D,
	TextGenerationSettings_get_text_mEC99436FE537C0C5F6B94997B785E445F6DD1924,
	TextGenerationSettings_set_text_m6A2B95D427C9CA7FB2EDB4A9302ECECB2BF657D5,
	TextGenerationSettings__ctor_mA20608A16443434DAE9FEF0BF8BD076270FA660E,
	TextGenerationSettings_Equals_mA5EDDF0453F2A7314AF5E1FE29F4138CD97E52D5,
	TextGenerationSettings_Equals_mDCEEB056B70FC65ED6065E3BFE8D69D823DFEFD0,
	TextGenerationSettings_GetHashCode_m1F750434FCE1853C36A579827B064619E85453E0,
	TextGenerationSettings_op_Inequality_m01EC5AAB9B6A14F1E7CF3C05A738F509479B25C4,
	TextGenerationSettings_ToString_mF8AB635EB369C7F66AC3B396934158CF3B6BA6CE,
	RenderedText__ctor_m5E28340FCE0357D83ED3B1177C6CEC98ED6472CE,
	RenderedText__ctor_m96430C7045F316918FD79C218263B55A240D083A,
	RenderedText__ctor_m770AEA85C46BE852A0A8DC504DE2D57A4458406B,
	RenderedText__ctor_mA4DB8477929189BAD3F68C72F4384282592890E8,
	RenderedText_get_CharacterCount_mFEE0D0D5C8A7FD6A60CE7ACB3957FD83C76CB06E,
	RenderedText_GetEnumerator_mC1D9E5B2EA09158C7802ACFC60C2C8BCA2FC9E2C,
	RenderedText_CreateString_m90A3B193313D89CE3E3B32767AB3B5A8CA96ADB3,
	RenderedText_Equals_m7024D63D324DF45CDEFEA65600AF84AC12AD8C47,
	RenderedText_Equals_m83C00E78CD558AEB7441DA4A3CD747A0536C7AFB,
	RenderedText_Equals_m71BF21AB72D97E26E954F5B967A7F26B645F9105,
	RenderedText_GetHashCode_mB77F278664A8CBD695F3C5B523FF765528A286E3,
	Enumerator_get_Current_m5C193C555FEB096F6BA1F5663D817497567DDD0B,
	Enumerator__ctor_mE1DAD4EACF9B07D76BFCA18112F41A0B72EC8CE8,
	Enumerator_MoveNext_mC26293738E22339239A5C2417D6FEE1E8B74946A,
	TextBackingContainer_get_Capacity_m8A8EE5A2670CBA7DEF97AA76DCAE4B90DE74A3E7,
	TextBackingContainer_get_Count_mB42F11C4CB2C7D67D039811AE8BBDBBC13F0C416,
	TextBackingContainer_set_Count_m78573180C571ED79DF38DC0A837E536A6869FE97,
	TextBackingContainer_get_Item_m00B920D306C1039BB4E8E3DACFF86130A4ADA612,
	TextBackingContainer_set_Item_m7C358387720AA2C2978B1CA8EA84A59E4490737D,
	TextBackingContainer__ctor_m46B1D0F45A11DA593305FA9D33D191A0E60CDBE7,
	TextBackingContainer_Resize_m9ABA1F50B99CFE494EE450304F6BF71541217698,
	CharacterSubstitution__ctor_mBB5C3EA59D985711FE3DF1F266D648201E18CE29,
	Offset_get_left_m83657AF289FA1DB8B5D4007B8310573B76AA6D82,
	Offset_get_right_m45AEBB7DE1D42A9A7234FB0DCE4E92420060D3FB,
	Offset_get_top_mD62FECE7914DF9723A872AAD91BDB07295C6E0F4,
	Offset_get_bottom_m3BC4AB202A1B7D7D5A65EF746CDA1A73B5D8866C,
	Offset_get_zero_mF5B6D7C3F437FA438844A0B3EF405D805F1D1958,
	Offset__ctor_mBF4CFCEFCC225000639F695A3021128FDD2E29A7,
	Offset_op_Equality_m122A34D50DB0E70BDEEC631D0082E9CFB8D19C8E,
	Offset_op_Multiply_mE5215371DD76A27676FF67C992C065BC456A8131,
	Offset_GetHashCode_m951A99E76473F284CD7DAC8577CDAA13F421E038,
	Offset_Equals_m7FD59DC3FC3848900FDE2D3EC70B28869AC0B19D,
	Offset__cctor_mB8571222B76084876413C594C17AC5A343B40732,
	HighlightState__ctor_mDBB71C58F46D7BDC518026AC796D24F2D9B36D3F,
	HighlightState_op_Equality_m6E4A396D3C2C5932BCCE96E7B3AE42E37E447923,
	HighlightState_op_Inequality_m2DFBCB59E593F72191BFBBD7424A8C6151E68272,
	HighlightState_GetHashCode_m52F707DADCC7F767D809342CBCDC94AF834150BA,
	HighlightState_Equals_mE0643D119E2942355F5FFED759BC329326E0C849,
	TextGeneratorUtilities_Approximately_m696ABB909732F536F1FF83EA8CE34CF53266794D,
	TextGeneratorUtilities_HexCharsToColor_m4D7AB2E490DA31C514A42A880AB35CE9CBAF77B4,
	TextGeneratorUtilities_HexToInt_m41648DAEE872433A0AFA82018A9539ECC5C0FFC6,
	TextGeneratorUtilities_ConvertToFloat_m93B85749154AAFB614CD246876786B75C98AE11C,
	TextGeneratorUtilities_ConvertToFloat_m37E1CF11E22ED6528B166907211426EEE6D0A4F8,
	NULL,
	NULL,
	TextGeneratorUtilities_InsertOpeningTextStyle_mF71E0B0C1B5E938C5AAC7F8FB3CD5278DEEC2408,
	TextGeneratorUtilities_InsertClosingTextStyle_m08B150E030816A5084205B49DA40DED97E0C7036,
	TextGeneratorUtilities_ReplaceOpeningStyleTag_m67FC3FFDE1912D2E7C2DC2BED4C5BA250B1DB705,
	TextGeneratorUtilities_ReplaceOpeningStyleTag_m6F65A6D9D8DEA4915B30DEBFF43850B9D7063252,
	TextGeneratorUtilities_ReplaceClosingStyleTag_m9DD77D4EACF2389DF2631F515A23C11DC5E58A3B,
	TextGeneratorUtilities_InsertOpeningStyleTag_m94153F78A4B8F7A1811D2C1E9567996E39616F60,
	TextGeneratorUtilities_InsertClosingStyleTag_mD6A4B3357D6478C5770AEE460F61917584B905DB,
	TextGeneratorUtilities_InsertTextStyleInTextProcessingArray_m7CC6FF13CD9B2F3BD04C60C2A2B527960C6D1D09,
	TextGeneratorUtilities_GetStyle_m236E7C5EBFA951D1D4B2C3C7803BBEA5C2FD1812,
	TextGeneratorUtilities_GetStyleHashCode_mA4CDB93771348C1236A8E9BE1EB4A9D5C057D516,
	TextGeneratorUtilities_GetStyleHashCode_m7B5002A635CF32D023E543FDE814E0E958A89EF1,
	TextGeneratorUtilities_GetUTF16_m5B397339CD29B370A27D3BA3B8BEFC12E8C56434,
	TextGeneratorUtilities_GetUTF16_m4E03C41F3B5323D6234DEC0A312F13CEAACCA8E6,
	TextGeneratorUtilities_GetUTF32_m334BA95AED813976AC79E68EB677BADB5CB15CE3,
	TextGeneratorUtilities_GetUTF32_mAF367B8C1D5B586B49AED2B69E5E7ECEF3378D0D,
	TextGeneratorUtilities_FillCharacterVertexBuffers_mE0CCB8DA0D27F37DCFC4E47E89697D8823A8FCE8,
	TextGeneratorUtilities_FillSpriteVertexBuffers_mD1AECFE4D4356A6925BF056E15CF84118313412B,
	TextGeneratorUtilities_AdjustLineOffset_m811C187EA3E41781116F0C7A679B05BB27874123,
	TextGeneratorUtilities_ResizeLineExtents_m2EA9BE32A38D5E075DEF8EDA9EC01766E45C0F85,
	TextGeneratorUtilities_LegacyStyleToNewStyle_m8E9C09A7BE7B27A4EC73ADD747BFD9A4DE7E656C,
	TextGeneratorUtilities_LegacyAlignmentToNewAlignment_mA16F5CB58D3C8F352CAACB8DACA5AFED59D3ED63,
	TextGeneratorUtilities_GetHorizontalAlignment_m3DA30E12E49D11E451FFBE24B875A16B514CD9A9,
	TextGeneratorUtilities_GetVerticalAlignment_mBBD1285FD68BA3FBADB17BCC63C5638A971CDD13,
	TextGeneratorUtilities_ConvertToUTF32_m6295E74C04568A52624812F2E615A7F25F235C70,
	TextGeneratorUtilities_GetMarkupTagHashCode_mFFDE1B0B5CD9774F83C988C5D436D1AD01AAD843,
	TextGeneratorUtilities_GetMarkupTagHashCode_m951A939A8B3B0BE3229CB1A94E79FF123C8EF6DE,
	TextGeneratorUtilities_ToUpperASCIIFast_m359D6A8BE78E2C74BA677D8453799487962EDE99,
	TextGeneratorUtilities_ToUpperASCIIFast_mEEED07AD0989B1DF84D559CDE3A397A9F2EA913C,
	TextGeneratorUtilities_ToUpperFast_mE1809281C56E4137C6794B2E94D38BBFA68DBAAE,
	TextGeneratorUtilities_GetAttributeParameters_m261C1E8FB533D3570153B2BAF0D671C5DF4B58DB,
	TextGeneratorUtilities_IsBitmapRendering_m93C5008776EEDD84825ED2133CDA0FC66DD56EEA,
	TextGeneratorUtilities_IsBaseGlyph_mEE0E7D6C3FB32204C2299FBA2B9F7C51E06F80FE,
	TextGeneratorUtilities_MinAlpha_mB52BE8C9C82C15B23D29BF606465B16DD4B1F7E5,
	TextGeneratorUtilities_GammaToLinear_m37B603C94918DB93477EFF98E8A77FD4D8B0C8FB,
	TextGeneratorUtilities_GammaToLinear_m5D4B51EF525F9238F6644BD47106DACCB78797D7,
	TextGeneratorUtilities_IsValidUTF16_m944B75A058B351075C02F1DA61B688FAF1186DE8,
	TextGeneratorUtilities_IsValidUTF32_mD6B22F5E6EAD47537B906859CB093622EECF716D,
	TextGeneratorUtilities_IsEmoji_m84855B4FDA2F5CE4FE0A7231AD6EEF30DB941CFA,
	TextGeneratorUtilities_IsEmojiPresentationForm_mE187DF246A5CD1794D085E96FFC0BA7AE1930BF7,
	TextGeneratorUtilities_IsHangul_m5A23BA8E0EBE57243E2E96A248B3F6570A87A966,
	TextGeneratorUtilities_IsCJK_m2F2718B1203271CC2C501C5054590299FBCA5B7D,
	TextGeneratorUtilities__cctor_m01201F8A0A4161B232AB9D179AB74877D4E17D85,
	TextLib__ctor_mF8D936A838D79316C758F83145B710655DFC03B1,
	TextLib_GetInstance_mD30A84164E4E7C3364F03AD35130CD717E2F375B,
	TextLib_GenerateText_m13A550DB25F00892F7D78D4B030FCAA570BC4FAD,
	TextLib_GenerateTextInternal_m7D3C05F5B323902C68CD2F01123F73B32E45A84B,
	TextLib_MeasureText_m6580802198F2FADA81B762D54C168EB7AEE2B22A,
	TextLib_FindIntersectingLink_m4DE95020DDDEEECFE33A8740C92A969D579DC543,
	TextLib_GetInstance_Injected_m168216684370B5808000BE0A8FAE90E15C8486D9,
	TextLib_GenerateTextInternal_Injected_m7AEB4D48072F79EB02A71B0DC764C331204BBEC4,
	TextLib_MeasureText_Injected_m1D2675BE31277F6CC3141C53B02FD412C3C6D447,
	TextLib_FindIntersectingLink_Injected_mE6E1252F6C0ED48E688152B7521FEEF75A884B10,
	BindingsMarshaller_ConvertToNative_mE836D629252E1DDD28A8EA9C4C131B4852552943,
	TextGenerationInfo_Create_m5111A4C2F3696D5ADBD1F70E636AA44BA8E01A0F,
	TextGenerationInfo_Destroy_mE2BCA3944ED096FF13C9DE612FCAA6A786E9DF86,
	TextSelectionService_Substring_mC9DB8C3D47FE1629EEC9D0D89A4EF6A08CFCC5D3,
	TextSelectionService_SelectCurrentWord_mC83009B68895B57B21D29EF536EECF7347BA9CBB,
	TextSelectionService_PreviousCodePointIndex_m45CB1B5E202D7B53367F3D08AA7827ED7A98038C,
	TextSelectionService_NextCodePointIndex_m527BA266FB15DA3B08A6617828B1C3CEE39A19C5,
	TextSelectionService_GetCursorLogicalIndexFromPosition_mBA2DD838B1D97C6D457C3C2BA65BE432CEEEDB17,
	TextSelectionService_GetCursorPositionFromLogicalIndex_m65E335DD05F6743F25AB797EFB23407AE30964FD,
	TextSelectionService_LineUpCharacterPosition_m429F67543D0621D328360979D816ABE95E6BB1BC,
	TextSelectionService_LineDownCharacterPosition_m4F52F3E56C4B27A5D1C524415921044106CF25A2,
	TextSelectionService_GetHighlightRectangles_m566945AD89280E50889D1453C15F9AA2A0D9858E,
	TextSelectionService_GetCharacterHeightFromIndex_mF2A5D41465225808B081A5510EFD20C97391F4E8,
	TextSelectionService_GetStartOfNextWord_m2BA6E18E802ADAC83667AE413A773C9E9354CCD9,
	TextSelectionService_GetEndOfPreviousWord_m92F062A11E21FD682B4801E4B26161D2C0FC1B06,
	TextSelectionService_GetFirstCharacterIndexOnLine_m5229D2CC50462416B53D129B524701861A7D444C,
	TextSelectionService_GetLastCharacterIndexOnLine_m7A908AEB27123913863F9F9AD55DF5F296FB472A,
	TextSelectionService_GetLineHeight_mEEADDDBB8F94B913DB7FA0D9ABB5B339E5B1615E,
	TextSelectionService_GetLineNumber_mA715A3FF339C2136A805EA44F6956561FC1F3340,
	TextSelectionService_SelectToPreviousParagraph_m1BBE29062FB29FB4D6764D88D2F0BC0F2E3EBAA6,
	TextSelectionService_SelectToStartOfParagraph_m129E7A0FDA646AFD8B25C3288D611A0E19E96AD6,
	TextSelectionService_SelectToEndOfParagraph_m2AC2A628E1D3BB615243FD916D1CA47B9FBD1986,
	TextSelectionService_SelectToNextParagraph_mE88FD507ABB1735C6FCDD7096A8DBA767C87FE82,
	TextSelectionService_SelectCurrentParagraph_m73D982DBEAB009DE2559E8FCD1F147EEF0892938,
	TextSelectionService_Substring_Injected_m6B91E70BBCF772F89152D77B572F5239AACE2E14,
	TextSelectionService_GetCursorLogicalIndexFromPosition_Injected_m538B3D5B3D8DA25755AFE7FA224FD3D4364E7458,
	TextSelectionService_GetCursorPositionFromLogicalIndex_Injected_mFF821F2B56F7036EFB569C3BE66B8E272925768E,
	TextSelectionService_GetHighlightRectangles_Injected_m31DFC05DB12DE6AC39C7C221BA80C1A3B7E37736,
	TextHandle__ctor_m0E8BD79BF9F66EED2A87EA09B246B712DDEED9C9,
	TextHandle_Finalize_mB3704C5A55586426C9B22FADA0C5207FB9E5C984,
	TextHandle_InitThreadArrays_m2E49138B4E0D4F54538207E77DE0685957F23F6D,
	TextHandle_get_settingsArray_m4CFA0E8F9B3E29B41E0E18949AF5D567F85F82E7,
	TextHandle_get_generators_mCBD7F4350C23561F33C524F4C79D47DF66B047A1,
	TextHandle_get_textInfosCommon_m1E82D8C5A9EEA975E483336173869F9C9CA59F1B,
	NULL,
	TextHandle_get_textInfoCommon_mBE430AB83498AB18C506CE685FE584A7DDE1DA8D,
	TextHandle_get_generator_m85DB7A6FAFB8B38CAAB9495A0C2E234DD2F60BD0,
	TextHandle_get_settings_m924DCD63BED9F000A224620C4A1FD1E8DEF51D10,
	TextHandle_get_preferredSize_m2D3DA9F4778C3F35E1FE4BE637D173D6F6BF5804,
	TextHandle_set_preferredSize_m9192F51B748162E27A87A09A0FA40FFEA364F34B,
	TextHandle_get_TextInfoNode_m51F4F2E0AE0333E1996DDAB34E3883D41A2D4053,
	TextHandle_set_TextInfoNode_m310BBBDEF5E0B6A9C15B25192FE5698120D35CBC,
	TextHandle_get_IsCachedPermanent_mC63CD6C07312D65B00A06165DC9C564EFE7D6553,
	TextHandle_set_IsCachedPermanent_mC1BD778CAF118C89DB68E1D42D065B314491E2E9,
	TextHandle_get_IsCachedTemporary_m2EB6D0791424D7F76530A84A777A62F9A17AAD95,
	TextHandle_set_IsCachedTemporary_m9203496A1717C888AC5DF6BD4DBC5738593B460D,
	TextHandle_get_useAdvancedText_mE7649150D29DE7D2768B084FB7140617F813BB7B,
	TextHandle_get_characterCount_m58420F9E2A2AF85169DCFCE4EFA7291E966615F2,
	TextHandle_AddTextInfoToPermanentCache_m6A1549020930BF0356032E2A12C8C98B00558F54,
	TextHandle_AddTextInfoToTemporaryCache_m99B5D63C51AEC0B3494DF6750C1B0F6648C0355C,
	TextHandle_RemoveTextInfoFromTemporaryCache_m078F98710A4FD1722F0932C65EEE801155088605,
	TextHandle_RemoveTextInfoFromPermanentCache_mB6AE8BE8E9180FD0539472F63B53648C19CEF484,
	TextHandle_UpdateCurrentFrame_mDC4EC98BDB3E7DA3588FB85229D532069E5D8F19,
	TextHandle_get_textInfo_mA9F2BFB37F7ADA773731AFBC3B53FDD858D87FEE,
	TextHandle_SetDirty_m485BF8AC302B5A6DC3F63BC8DE3A0D823C2B7F2D,
	TextHandle_IsDirty_mB36F6490BB2EC7F57091D47A9582AF1BE6D5B086,
	TextHandle_get_IsPlaceholder_m009132CDAC2C0E7C785A4CA97F4DF2B7B2A1B1D0,
	TextHandle_IsElided_mF2AB6B8A1E01EE5FD2301E9BF77BEE5BC99C4ED5,
	TextHandle_UpdatePreferredValues_m16C579932E755BC3FD8D82085F75EC011A44AD59,
	TextHandle_Update_mD9BFF1818EC7158DB744B719BDD9C8235AE2368C,
	TextHandle_UpdateWithHash_mC4F89BB34B092BE7F5E2219531A4541454BB97BA,
	TextHandle_PrepareFontAsset_m09DBCA8383E73C03046A2B72F443F5CAE77F61B4,
	TextHandle_UpdatePreferredSize_m10613CD9F9F36B26EF2045DC196CC8BA9F4919A3,
	TextHandle_ConvertPixelUnitsToTextCoreRelativeUnits_m472293EA75E31A85F0DEF2DEE1CAE688CB1A5BBD,
	TextHandle_GetLineHeightDefault_mA4195042076C97B09260B329C0A8D052A8105B2E,
	TextHandle_GetCursorPositionFromStringIndexUsingCharacterHeight_m082A44C87BA6376E99A5BD37090429F4A3CF0E0E,
	TextHandle_GetCursorPositionFromStringIndexUsingLineHeight_m44EE4238CC96507DCEB45A28D61E969C877011FE,
	TextHandle_GetHighlightRectangles_mEAC7CB5CB926D25D78BD7F8A3697603AB14BCA98,
	TextHandle_GetCursorIndexFromPosition_m68B7D720ED1589CC46538FACB50E7F7E56AA701E,
	TextHandle_LineDownCharacterPosition_mDD7F4379B59B9CAF7431CCC3E4056CF3511ECF9F,
	TextHandle_LineUpCharacterPosition_m69F1091BCD4A92E343B60966F4C49A9E36106C5B,
	TextHandle_FindIntersectingLink_m9D72AF4B459885AEFB03A0FF212241F8532B9132,
	TextHandle_GetCorrespondingStringIndex_mDA160FA6E48243DAD913D24CA9095C1CC1450AE3,
	TextHandle_GetLineInfoFromCharacterIndex_m1B157F587FBD4CEEF9135A24E79256BE849B807F,
	TextHandle_GetLineNumber_mED5D753BFDB5DDB5145EEDC829EA8D4EF1D305B1,
	TextHandle_GetLineHeight_mAC48AA68AFCC8EDE5C52EF69941ADAD3B144539E,
	TextHandle_GetLineHeightFromCharacterIndex_mA935CA07C41CEA0C7447033337D05CB2652A1D62,
	TextHandle_GetCharacterHeightFromIndex_mA512F4B21032917955542D5E71D611A55E6F1F0D,
	TextHandle_Substring_m3B3C2EC9E167E4C74AB319DE140780560976EDEC,
	TextHandle_PreviousCodePointIndex_m7BD6CE7A75924E4AE08D5FA885B036F5D983EEB4,
	TextHandle_NextCodePointIndex_m59631E23176BDCBB5ED792AB0E521F90BDA0D018,
	TextHandle_GetStartOfNextWord_mC0F6A773ACA7BFE20E6785ABA0D183BE1BD84B2E,
	TextHandle_GetEndOfPreviousWord_mBD60CE374828B5794B8C16A67E38E032D71DE964,
	TextHandle_GetFirstCharacterIndexOnLine_m631B6581B25FBE6A666BB0B1488673E09786A7D2,
	TextHandle_GetLastCharacterIndexOnLine_mFDF4AD3FF30611192C0118CBC836BA1755F3000A,
	TextHandle_IndexOf_mD0CDAB3319422D67356DBC547E91A08882D001B2,
	TextHandle_LastIndexOf_m7A2F2860D56B0C90A73958E02006CAA1D6BEACC4,
	TextHandle_SelectCurrentWord_mC2F2F8B7A1ED894DC478781774A4C1F728631C3F,
	TextHandle_SelectCurrentParagraph_m7BC0C69779C5B11C26E5D02D57C56A611033652D,
	TextHandle_SelectToPreviousParagraph_m6E718F46C68ABC46845CD39E4B606B85947F3B2B,
	TextHandle_SelectToNextParagraph_m16601CF807B7A64898D67CA6EA5BECC296570BB2,
	TextHandle_SelectToStartOfParagraph_m4DE404748B8A558DFDCA27A3256E94245CD3DA97,
	TextHandle_SelectToEndOfParagraph_m9DEE8C168D515A0B26A291B2A3356CA50D8BD36E,
	TextHandle_IsAdvancedTextEnabledForElement_m12DA5360680D35F3B945CD4E1F976127F3EE4D0D,
	TextHandle__cctor_m3EFFF534A8E9459492960B615C91F18081422439,
	U3CU3Ec__cctor_mCAD10D256AA33CABDB9D1557537FC3E9E5791457,
	U3CU3Ec__ctor_mE8360FCB61EE3220EA9C9CB6801D610BE09142D4,
	U3CU3Ec_U3CInitThreadArraysU3Eb__4_0_mCF68501B75B6CB0B993D71DF65368A5283DB16FD,
	U3CU3Ec_U3CInitThreadArraysU3Eb__4_1_mA224B8DA6F9DA3A6739F510020E1A2934ABEF0A3,
	U3CU3Ec_U3CInitThreadArraysU3Eb__4_2_mC2C94977DDA63B3C58384A1BB6C06337C78C05D2,
	U3CU3Ec_U3Cget_settingsArrayU3Eb__7_0_m1D5600B2942B290610B6C31285C7E308AAB4BB03,
	U3CU3Ec_U3Cget_generatorsU3Eb__10_0_m740BEC468DCDEB6C6A4598DB15212EC281D9D37B,
	U3CU3Ec_U3Cget_textInfosCommonU3Eb__13_0_m99B7E3B18B4609B05EFAD07DBF15DC3EA591491E,
	TextHandlePermanentCache_AddTextInfoToCache_m1C616E90C43C4E66CC51A854906B5DE5EB39B4BE,
	TextHandlePermanentCache_RemoveTextInfoFromCache_m2F1117399668FE56B9748DFC5CC08E2C8A6D3FE6,
	TextHandlePermanentCache__ctor_m1E871678633B506E9BC7B0C5DE21E8B7F1B50F8F,
	TextHandleTemporaryCache_ClearTemporaryCache_mEFD7828E924D9EDB408EB21943D5CE037335C59E,
	TextHandleTemporaryCache_AddTextInfoToCache_m5FC534D96C0BFDD0AF066A5505F2D5FF653EE00D,
	TextHandleTemporaryCache_RemoveTextInfoFromCache_mFB501203B3D05153DBE9AB2CB6A9C3E33CA6050F,
	TextHandleTemporaryCache_RefreshCaching_mAD296DA1C2B6E1E9BE3D3B14ECC251451F3B2A7B,
	TextHandleTemporaryCache_RecycleTextInfoFromCache_mEDF5CB6B0DFF3EE879FFE1BE2A28EEBC29F3DAEB,
	TextHandleTemporaryCache_UpdateCurrentFrame_mE33A6DAC9CBA7D232E88B08B49897FF83EE6B966,
	TextHandleTemporaryCache__ctor_m4D067665AB7AF075868ECEA6E108142A2CCC3A21,
	TextInfo_get_vertexDataLayout_m51EB99E4BAB733094A5AEE4539F76C39E3DD0AFE,
	TextInfo_set_vertexDataLayout_m77EA7C758DF4F919E27628EB3AA0CE12F2BCEBA6,
	TextInfo_RemoveFromCache_m5727A31E536090A5EA1D56165A48625E69F6DFC0,
	TextInfo__ctor_m0D2430C2058B0D6B9C7F21C2E62A4A8D6CA5ED8F,
	TextInfo_Clear_m60412774208F9D920707448E71E89C99233D9128,
	TextInfo_ClearMeshInfo_mCA598F01C7F302CFCD0F508E2DBF072E66CA74F3,
	TextInfo_ClearLineInfo_m986C886D34A324C8C4D30F9D8EF24AC242A10AD7,
	TextInfo_ClearPageInfo_m57DE207346C5245799E50F8A57B56B65665B7430,
	NULL,
	NULL,
	TextInfo_GetCursorPositionFromStringIndexUsingCharacterHeight_m53316961E455FBC1E28FB426A7BB0FDD702C882E,
	TextInfo_GetCursorPositionFromStringIndexUsingLineHeight_m92FA693363C8A09B287E537ED70617DD52EFF7B9,
	TextInfo_GetCursorIndexFromPosition_mEFA727A71DA09BBDF62135A5F05532DFD967F55A,
	TextInfo_LineDownCharacterPosition_m8683D3F9CDDAC905CD2785EE8951F579CBCBAEFB,
	TextInfo_LineUpCharacterPosition_m166FEAC52CE6DA4980BA78CE7C3C77247CE9F27C,
	TextInfo_FindNearestLine_mE69F45519747DEE0F3BCC90FFD7B619D0352DB26,
	TextInfo_FindNearestCharacterOnLine_m1150A5C41A48FCC3746736DD2AE7DD9C8249BAE0,
	TextInfo_FindIntersectingLink_mBE527632D2D2B31BDADE678B59FB1BF596E933C0,
	TextInfo_GetCorrespondingStringIndex_mB7380B38E2873CAC40E66805B8AF3942C59054B3,
	TextInfo_GetLineInfoFromCharacterIndex_m4ED8ACCF098AD57E7B5EC26FC26BFECAA0B064AB,
	TextInfo_PointIntersectRectangle_m3E0050FEAB5B3DF87B666EEAF9056B1D427F87BC,
	TextInfo_DistanceToLine_mB4F796AA01255D2949718D2FD6CCB86CCD8676D8,
	TextInfo_GetLineNumber_m8D725D36FE6B36FDC076911B66E9B0896C591656,
	TextInfo_GetLineHeight_mEFB708CFEF226B16BD35D8E422D1CC6FE5EA8A80,
	TextInfo_GetLineHeightFromCharacterIndex_m6B7E544BE97C3E0130ACE2C8D8F90C67C386E0B9,
	TextInfo_GetCharacterHeightFromIndex_mC6DB161E05C109C71A04F03F0C5DECA896C92BB9,
	TextInfo_Substring_mA367A49634FD8CB29C564253F98C9615A19CC572,
	TextInfo_IndexOf_m71674062F329F8A71F76BE506FD2AAEA10124358,
	TextInfo_LastIndexOf_m5F4AB42B005B2F9CFE08C28F6E0933F3C22DD728,
	TextInfo__cctor_m0B33CCC02D4B32FB18864B6B7657695E9AEB3F0D,
	FontStyleStack_Clear_m989659363648B27540168E46F23E1EF9877C06E0,
	FontStyleStack_Add_m26E701C9F052EEEBB213B9B8BC6CB8F1F8F6AFCB,
	FontStyleStack_Remove_mC2B4F44A6596E92D6992DBCA298648F8A7416CAB,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	TextResourceManager_AddFontAsset_mAAD97609F00F02146B283982E5DE667EB7A692EA,
	TextResourceManager__cctor_m54E40FCFB355A0C90B56366B057F307F75832868,
	FontAssetRef__ctor_m5553FC4D7E51DE97D70CE09E1C99B002A61FFDB5,
	TextShaderUtilities_get_ShaderRef_MobileSDF_m9014F695545914ADF74FFD8A461B1DD2590A163B,
	TextShaderUtilities_get_ShaderRef_MobileSDF_IMGUI_mFB26197BEF0BDC723970439AD27E94EB2EF5D46C,
	TextShaderUtilities_get_ShaderRef_MobileBitmap_m54CAA31B3B374C0517BF356C80B34F169C89F649,
	TextShaderUtilities_get_ShaderRef_Sprite_mEB8685333A53464F71519FA69438381EABEFAA02,
	TextShaderUtilities__cctor_m5E3AAC923C6500CFCB7F775610B085AD43CDE4EE,
	TextShaderUtilities_GetShaderPropertyIDs_mBA841B8CACE147593703FF6E7E2804FF2017DE52,
	TextUtilities_ToUpperFast_m7E98D5D17030A387012E262648346523DCC3AA0B,
	TextUtilities_GetHashCodeCaseInSensitive_m3241BDD3A6F82A061C21BF5D347B690D732F5B59,
	TextUtilities_GetTextFontWeightIndex_m758569A41B59CA4E6A6EE3E73299B41C25FC4E67,
	UnicodeLineBreakingRules_get_leadingCharactersLookup_m1DAC015D7E37112EAE0437E6472AEA0719DFF3DC,
	UnicodeLineBreakingRules_get_followingCharactersLookup_m5510A21873DC5DA66F4A2DFA4C26A5EFAD494D8B,
	UnicodeLineBreakingRules_get_useModernHangulLineBreakingRules_mD86D283CE7BA23A0174B9227A7BD915D3D9FD464,
	UnicodeLineBreakingRules_LoadLineBreakingRules_m4686111E39B00E27AA6AD88762793EEFCCC14A75,
	UnicodeLineBreakingRules_GetCharacters_m93663235F54D32D801E67994714B79759D2F4137,
	UnicodeLineBreakingRules__ctor_mD94779B4996B56EA84C847EC4DD287AB1A8ADE85,
};
extern void NativeTextGenerationSettings_get_hasLink_m2D703AAD28BFE44DA7FB6B40A679F26D21718910_AdjustorThunk (void);
extern void NativeTextGenerationSettings_CreateTextSpan_m63AE7655BC8C77D4614541BA5426F0E1FECA728A_AdjustorThunk (void);
extern void NativeTextGenerationSettings_ToString_m84C204918B5C4F40D1EADDB371DCE76FF098E84D_AdjustorThunk (void);
extern void TextSpan_ToString_mB43B06596C5F8782F98817A120945168F1A893C9_AdjustorThunk (void);
extern void Extents_ToString_m8A1F748127EE9CCCD6FFAF4CE1F38E37C07831AC_AdjustorThunk (void);
extern void LinkInfo_SetLinkId_mB4145264190D5C857705261CB27F87C6E10C3F3F_AdjustorThunk (void);
extern void LinkInfo_GetLinkText_mE5F0F2128BB22FFE009BBBDE4EF0E921F95AA6D8_AdjustorThunk (void);
extern void LinkInfo_GetLinkId_mE502F621D5C09B05C2C509E35404BED4420B4FDB_AdjustorThunk (void);
extern void MaterialReference__ctor_m044AAA2C1079EB25A5534A6E0FA2314F033DB15A_AdjustorThunk (void);
extern void MeshInfo__ctor_m3FD8AAF58EBCF22706EDB08B503B4FB2C108E86A_AdjustorThunk (void);
extern void MeshInfo_ResizeMeshInfo_m22D30D08188ACAEBA3CE46383E5D2FFC3E8C519E_AdjustorThunk (void);
extern void MeshInfo_Clear_m06992FEB7AC9B2AE1728BEDFC8D8A39DE1AAD475_AdjustorThunk (void);
extern void MeshInfo_ClearUnusedVertices_m7B6003EF4CA72C0ABBA4D25DEA8B0BF3934B2830_AdjustorThunk (void);
extern void MeshInfo_SortGeometry_m92046C53AA6AE75EE3627CE73846296AB3E99DD1_AdjustorThunk (void);
extern void MeshInfo_SwapVertexData_mD145A4C2B45422DFCB2D9697956E16395E717C7F_AdjustorThunk (void);
extern void FontReferenceMap__ctor_mA94127060F9F3FDC3093D9356832503BFBCE44F5_AdjustorThunk (void);
extern void TextElementInfo_ToString_mA643B4D503B51EB7632B7E352D699E63DDD29985_AdjustorThunk (void);
extern void SpecialCharacter__ctor_m6697A8BF272F0144733EE12368C038F45E99F969_AdjustorThunk (void);
extern void RenderedText__ctor_m5E28340FCE0357D83ED3B1177C6CEC98ED6472CE_AdjustorThunk (void);
extern void RenderedText__ctor_m96430C7045F316918FD79C218263B55A240D083A_AdjustorThunk (void);
extern void RenderedText__ctor_m770AEA85C46BE852A0A8DC504DE2D57A4458406B_AdjustorThunk (void);
extern void RenderedText__ctor_mA4DB8477929189BAD3F68C72F4384282592890E8_AdjustorThunk (void);
extern void RenderedText_get_CharacterCount_mFEE0D0D5C8A7FD6A60CE7ACB3957FD83C76CB06E_AdjustorThunk (void);
extern void RenderedText_GetEnumerator_mC1D9E5B2EA09158C7802ACFC60C2C8BCA2FC9E2C_AdjustorThunk (void);
extern void RenderedText_CreateString_m90A3B193313D89CE3E3B32767AB3B5A8CA96ADB3_AdjustorThunk (void);
extern void RenderedText_Equals_m7024D63D324DF45CDEFEA65600AF84AC12AD8C47_AdjustorThunk (void);
extern void RenderedText_Equals_m83C00E78CD558AEB7441DA4A3CD747A0536C7AFB_AdjustorThunk (void);
extern void RenderedText_Equals_m71BF21AB72D97E26E954F5B967A7F26B645F9105_AdjustorThunk (void);
extern void RenderedText_GetHashCode_mB77F278664A8CBD695F3C5B523FF765528A286E3_AdjustorThunk (void);
extern void Enumerator_get_Current_m5C193C555FEB096F6BA1F5663D817497567DDD0B_AdjustorThunk (void);
extern void Enumerator__ctor_mE1DAD4EACF9B07D76BFCA18112F41A0B72EC8CE8_AdjustorThunk (void);
extern void Enumerator_MoveNext_mC26293738E22339239A5C2417D6FEE1E8B74946A_AdjustorThunk (void);
extern void TextBackingContainer_get_Capacity_m8A8EE5A2670CBA7DEF97AA76DCAE4B90DE74A3E7_AdjustorThunk (void);
extern void TextBackingContainer_get_Count_mB42F11C4CB2C7D67D039811AE8BBDBBC13F0C416_AdjustorThunk (void);
extern void TextBackingContainer_set_Count_m78573180C571ED79DF38DC0A837E536A6869FE97_AdjustorThunk (void);
extern void TextBackingContainer_get_Item_m00B920D306C1039BB4E8E3DACFF86130A4ADA612_AdjustorThunk (void);
extern void TextBackingContainer_set_Item_m7C358387720AA2C2978B1CA8EA84A59E4490737D_AdjustorThunk (void);
extern void TextBackingContainer__ctor_m46B1D0F45A11DA593305FA9D33D191A0E60CDBE7_AdjustorThunk (void);
extern void TextBackingContainer_Resize_m9ABA1F50B99CFE494EE450304F6BF71541217698_AdjustorThunk (void);
extern void CharacterSubstitution__ctor_mBB5C3EA59D985711FE3DF1F266D648201E18CE29_AdjustorThunk (void);
extern void Offset_get_left_m83657AF289FA1DB8B5D4007B8310573B76AA6D82_AdjustorThunk (void);
extern void Offset_get_right_m45AEBB7DE1D42A9A7234FB0DCE4E92420060D3FB_AdjustorThunk (void);
extern void Offset_get_top_mD62FECE7914DF9723A872AAD91BDB07295C6E0F4_AdjustorThunk (void);
extern void Offset_get_bottom_m3BC4AB202A1B7D7D5A65EF746CDA1A73B5D8866C_AdjustorThunk (void);
extern void Offset__ctor_mBF4CFCEFCC225000639F695A3021128FDD2E29A7_AdjustorThunk (void);
extern void Offset_GetHashCode_m951A99E76473F284CD7DAC8577CDAA13F421E038_AdjustorThunk (void);
extern void Offset_Equals_m7FD59DC3FC3848900FDE2D3EC70B28869AC0B19D_AdjustorThunk (void);
extern void HighlightState__ctor_mDBB71C58F46D7BDC518026AC796D24F2D9B36D3F_AdjustorThunk (void);
extern void HighlightState_GetHashCode_m52F707DADCC7F767D809342CBCDC94AF834150BA_AdjustorThunk (void);
extern void HighlightState_Equals_mE0643D119E2942355F5FFED759BC329326E0C849_AdjustorThunk (void);
extern void FontStyleStack_Clear_m989659363648B27540168E46F23E1EF9877C06E0_AdjustorThunk (void);
extern void FontStyleStack_Add_m26E701C9F052EEEBB213B9B8BC6CB8F1F8F6AFCB_AdjustorThunk (void);
extern void FontStyleStack_Remove_mC2B4F44A6596E92D6992DBCA298648F8A7416CAB_AdjustorThunk (void);
extern void FontAssetRef__ctor_m5553FC4D7E51DE97D70CE09E1C99B002A61FFDB5_AdjustorThunk (void);
static Il2CppTokenAdjustorThunkPair s_adjustorThunks[54] = 
{
	{ 0x06000005, NativeTextGenerationSettings_get_hasLink_m2D703AAD28BFE44DA7FB6B40A679F26D21718910_AdjustorThunk },
	{ 0x06000006, NativeTextGenerationSettings_CreateTextSpan_m63AE7655BC8C77D4614541BA5426F0E1FECA728A_AdjustorThunk },
	{ 0x06000008, NativeTextGenerationSettings_ToString_m84C204918B5C4F40D1EADDB371DCE76FF098E84D_AdjustorThunk },
	{ 0x0600000C, TextSpan_ToString_mB43B06596C5F8782F98817A120945168F1A893C9_AdjustorThunk },
	{ 0x06000049, Extents_ToString_m8A1F748127EE9CCCD6FFAF4CE1F38E37C07831AC_AdjustorThunk },
	{ 0x0600004A, LinkInfo_SetLinkId_mB4145264190D5C857705261CB27F87C6E10C3F3F_AdjustorThunk },
	{ 0x0600004B, LinkInfo_GetLinkText_mE5F0F2128BB22FFE009BBBDE4EF0E921F95AA6D8_AdjustorThunk },
	{ 0x0600004C, LinkInfo_GetLinkId_mE502F621D5C09B05C2C509E35404BED4420B4FDB_AdjustorThunk },
	{ 0x06000051, MaterialReference__ctor_m044AAA2C1079EB25A5534A6E0FA2314F033DB15A_AdjustorThunk },
	{ 0x06000066, MeshInfo__ctor_m3FD8AAF58EBCF22706EDB08B503B4FB2C108E86A_AdjustorThunk },
	{ 0x06000067, MeshInfo_ResizeMeshInfo_m22D30D08188ACAEBA3CE46383E5D2FFC3E8C519E_AdjustorThunk },
	{ 0x06000068, MeshInfo_Clear_m06992FEB7AC9B2AE1728BEDFC8D8A39DE1AAD475_AdjustorThunk },
	{ 0x06000069, MeshInfo_ClearUnusedVertices_m7B6003EF4CA72C0ABBA4D25DEA8B0BF3934B2830_AdjustorThunk },
	{ 0x0600006A, MeshInfo_SortGeometry_m92046C53AA6AE75EE3627CE73846296AB3E99DD1_AdjustorThunk },
	{ 0x0600006B, MeshInfo_SwapVertexData_mD145A4C2B45422DFCB2D9697956E16395E717C7F_AdjustorThunk },
	{ 0x0600018C, FontReferenceMap__ctor_mA94127060F9F3FDC3093D9356832503BFBCE44F5_AdjustorThunk },
	{ 0x060001A5, TextElementInfo_ToString_mA643B4D503B51EB7632B7E352D699E63DDD29985_AdjustorThunk },
	{ 0x060001CB, SpecialCharacter__ctor_m6697A8BF272F0144733EE12368C038F45E99F969_AdjustorThunk },
	{ 0x060001D6, RenderedText__ctor_m5E28340FCE0357D83ED3B1177C6CEC98ED6472CE_AdjustorThunk },
	{ 0x060001D7, RenderedText__ctor_m96430C7045F316918FD79C218263B55A240D083A_AdjustorThunk },
	{ 0x060001D8, RenderedText__ctor_m770AEA85C46BE852A0A8DC504DE2D57A4458406B_AdjustorThunk },
	{ 0x060001D9, RenderedText__ctor_mA4DB8477929189BAD3F68C72F4384282592890E8_AdjustorThunk },
	{ 0x060001DA, RenderedText_get_CharacterCount_mFEE0D0D5C8A7FD6A60CE7ACB3957FD83C76CB06E_AdjustorThunk },
	{ 0x060001DB, RenderedText_GetEnumerator_mC1D9E5B2EA09158C7802ACFC60C2C8BCA2FC9E2C_AdjustorThunk },
	{ 0x060001DC, RenderedText_CreateString_m90A3B193313D89CE3E3B32767AB3B5A8CA96ADB3_AdjustorThunk },
	{ 0x060001DD, RenderedText_Equals_m7024D63D324DF45CDEFEA65600AF84AC12AD8C47_AdjustorThunk },
	{ 0x060001DE, RenderedText_Equals_m83C00E78CD558AEB7441DA4A3CD747A0536C7AFB_AdjustorThunk },
	{ 0x060001DF, RenderedText_Equals_m71BF21AB72D97E26E954F5B967A7F26B645F9105_AdjustorThunk },
	{ 0x060001E0, RenderedText_GetHashCode_mB77F278664A8CBD695F3C5B523FF765528A286E3_AdjustorThunk },
	{ 0x060001E1, Enumerator_get_Current_m5C193C555FEB096F6BA1F5663D817497567DDD0B_AdjustorThunk },
	{ 0x060001E2, Enumerator__ctor_mE1DAD4EACF9B07D76BFCA18112F41A0B72EC8CE8_AdjustorThunk },
	{ 0x060001E3, Enumerator_MoveNext_mC26293738E22339239A5C2417D6FEE1E8B74946A_AdjustorThunk },
	{ 0x060001E4, TextBackingContainer_get_Capacity_m8A8EE5A2670CBA7DEF97AA76DCAE4B90DE74A3E7_AdjustorThunk },
	{ 0x060001E5, TextBackingContainer_get_Count_mB42F11C4CB2C7D67D039811AE8BBDBBC13F0C416_AdjustorThunk },
	{ 0x060001E6, TextBackingContainer_set_Count_m78573180C571ED79DF38DC0A837E536A6869FE97_AdjustorThunk },
	{ 0x060001E7, TextBackingContainer_get_Item_m00B920D306C1039BB4E8E3DACFF86130A4ADA612_AdjustorThunk },
	{ 0x060001E8, TextBackingContainer_set_Item_m7C358387720AA2C2978B1CA8EA84A59E4490737D_AdjustorThunk },
	{ 0x060001E9, TextBackingContainer__ctor_m46B1D0F45A11DA593305FA9D33D191A0E60CDBE7_AdjustorThunk },
	{ 0x060001EA, TextBackingContainer_Resize_m9ABA1F50B99CFE494EE450304F6BF71541217698_AdjustorThunk },
	{ 0x060001EB, CharacterSubstitution__ctor_mBB5C3EA59D985711FE3DF1F266D648201E18CE29_AdjustorThunk },
	{ 0x060001EC, Offset_get_left_m83657AF289FA1DB8B5D4007B8310573B76AA6D82_AdjustorThunk },
	{ 0x060001ED, Offset_get_right_m45AEBB7DE1D42A9A7234FB0DCE4E92420060D3FB_AdjustorThunk },
	{ 0x060001EE, Offset_get_top_mD62FECE7914DF9723A872AAD91BDB07295C6E0F4_AdjustorThunk },
	{ 0x060001EF, Offset_get_bottom_m3BC4AB202A1B7D7D5A65EF746CDA1A73B5D8866C_AdjustorThunk },
	{ 0x060001F1, Offset__ctor_mBF4CFCEFCC225000639F695A3021128FDD2E29A7_AdjustorThunk },
	{ 0x060001F4, Offset_GetHashCode_m951A99E76473F284CD7DAC8577CDAA13F421E038_AdjustorThunk },
	{ 0x060001F5, Offset_Equals_m7FD59DC3FC3848900FDE2D3EC70B28869AC0B19D_AdjustorThunk },
	{ 0x060001F7, HighlightState__ctor_mDBB71C58F46D7BDC518026AC796D24F2D9B36D3F_AdjustorThunk },
	{ 0x060001FA, HighlightState_GetHashCode_m52F707DADCC7F767D809342CBCDC94AF834150BA_AdjustorThunk },
	{ 0x060001FB, HighlightState_Equals_mE0643D119E2942355F5FFED759BC329326E0C849_AdjustorThunk },
	{ 0x060002C6, FontStyleStack_Clear_m989659363648B27540168E46F23E1EF9877C06E0_AdjustorThunk },
	{ 0x060002C7, FontStyleStack_Add_m26E701C9F052EEEBB213B9B8BC6CB8F1F8F6AFCB_AdjustorThunk },
	{ 0x060002C8, FontStyleStack_Remove_mC2B4F44A6596E92D6992DBCA298648F8A7416CAB_AdjustorThunk },
	{ 0x060002D9, FontAssetRef__ctor_m5553FC4D7E51DE97D70CE09E1C99B002A61FFDB5_AdjustorThunk },
};
static const int32_t s_InvokerIndices[744] = 
{
	7102,
	5484,
	5600,
	5484,
	6869,
	7069,
	10233,
	6985,
	10294,
	7102,
	3931,
	6985,
	8768,
	7777,
	9109,
	8042,
	9109,
	8693,
	8378,
	8065,
	8619,
	10294,
	6985,
	892,
	6985,
	3852,
	6946,
	3852,
	3852,
	6985,
	5488,
	5600,
	6985,
	7033,
	6872,
	6985,
	3852,
	6946,
	3852,
	3852,
	6985,
	2864,
	6985,
	3852,
	6946,
	3852,
	3852,
	7102,
	1392,
	2965,
	8808,
	8934,
	7102,
	-1,
	-1,
	-1,
	-1,
	6985,
	6985,
	6985,
	7102,
	7102,
	7102,
	7102,
	10294,
	7102,
	4972,
	4972,
	4974,
	4974,
	4975,
	4975,
	6985,
	1338,
	4785,
	6985,
	9109,
	8491,
	9340,
	10294,
	394,
	7911,
	7911,
	10235,
	10002,
	5600,
	9306,
	2614,
	9306,
	2614,
	9306,
	2614,
	8835,
	1597,
	8835,
	1597,
	8835,
	1597,
	8835,
	1597,
	7102,
	1291,
	2534,
	5484,
	7102,
	5562,
	2583,
	10294,
	-1,
	-1,
	6906,
	5525,
	6985,
	5600,
	6946,
	5562,
	6905,
	5523,
	6946,
	5562,
	6946,
	5562,
	6985,
	5600,
	6985,
	6985,
	5600,
	6985,
	6985,
	6985,
	5600,
	6946,
	6869,
	5484,
	6869,
	5484,
	6869,
	5484,
	6946,
	5562,
	6946,
	5562,
	6946,
	5562,
	6946,
	5562,
	6985,
	5600,
	6985,
	5600,
	6985,
	5600,
	6985,
	5600,
	6985,
	5600,
	7033,
	5639,
	7033,
	5639,
	7033,
	5639,
	7033,
	5639,
	6869,
	5484,
	6869,
	5484,
	8491,
	8491,
	7658,
	8494,
	8494,
	8494,
	7332,
	7273,
	9810,
	7271,
	7302,
	7305,
	7259,
	7331,
	9805,
	5600,
	7102,
	7102,
	7102,
	-1,
	-1,
	7102,
	7102,
	7102,
	7102,
	7102,
	7102,
	7102,
	7102,
	1390,
	2964,
	990,
	476,
	1391,
	1085,
	1187,
	6946,
	7102,
	7102,
	7102,
	7102,
	3816,
	1079,
	1082,
	229,
	229,
	1641,
	464,
	3852,
	9810,
	9810,
	4977,
	2198,
	2199,
	7102,
	5484,
	7102,
	7102,
	7102,
	5484,
	7102,
	10002,
	10002,
	10294,
	10002,
	10294,
	10294,
	1642,
	1049,
	1642,
	1049,
	1090,
	1089,
	1733,
	1733,
	228,
	1081,
	1081,
	6869,
	7102,
	833,
	7102,
	7102,
	7102,
	7102,
	5693,
	7102,
	5600,
	7102,
	5600,
	7102,
	5600,
	5600,
	6948,
	7102,
	7102,
	7102,
	6985,
	3852,
	3852,
	6845,
	9316,
	8665,
	7298,
	9313,
	10000,
	7102,
	7102,
	10294,
	9311,
	8637,
	7297,
	9311,
	10294,
	7102,
	4976,
	4976,
	9109,
	9340,
	10002,
	10294,
	7338,
	7338,
	7277,
	7338,
	7307,
	8505,
	8505,
	6905,
	5523,
	6985,
	5600,
	7033,
	5639,
	7033,
	5639,
	6985,
	5600,
	6985,
	5600,
	6985,
	5600,
	7102,
	7102,
	4465,
	4571,
	4494,
	8050,
	7663,
	7663,
	7646,
	7648,
	7648,
	7102,
	7102,
	7102,
	7102,
	10294,
	7102,
	4976,
	4976,
	6985,
	7102,
	7102,
	6985,
	5600,
	6946,
	6946,
	5562,
	6985,
	5600,
	6946,
	5562,
	7102,
	7102,
	5488,
	875,
	10294,
	6985,
	5600,
	6985,
	5600,
	6985,
	5600,
	6985,
	5600,
	6985,
	6985,
	5600,
	4778,
	6869,
	5484,
	6946,
	5562,
	6869,
	5484,
	6869,
	5484,
	6985,
	5600,
	6985,
	5600,
	6985,
	5600,
	6985,
	5600,
	10002,
	10235,
	7088,
	5693,
	6985,
	5600,
	6985,
	5600,
	6985,
	5600,
	6985,
	5600,
	6869,
	5484,
	7102,
	7102,
	4785,
	2155,
	6985,
	6985,
	7102,
	2870,
	6946,
	6985,
	6985,
	1361,
	7102,
	6985,
	7102,
	4778,
	4785,
	7102,
	7102,
	7102,
	6869,
	7088,
	5693,
	6985,
	5600,
	6985,
	5600,
	7088,
	5693,
	7033,
	5639,
	7102,
	6985,
	9295,
	10294,
	10194,
	9992,
	2870,
	6869,
	2870,
	155,
	7102,
	856,
	1893,
	429,
	1249,
	18,
	435,
	9295,
	1362,
	952,
	5,
	2205,
	2205,
	369,
	2870,
	3852,
	1118,
	98,
	5468,
	5600,
	1649,
	2932,
	3852,
	3852,
	3852,
	989,
	7102,
	2866,
	989,
	2864,
	7017,
	5627,
	6985,
	5600,
	7102,
	3852,
	3852,
	6946,
	8856,
	6985,
	5600,
	2870,
	942,
	1388,
	6946,
	7202,
	6985,
	3881,
	3852,
	3852,
	6946,
	7087,
	5468,
	6869,
	6946,
	6946,
	5562,
	4973,
	2689,
	5562,
	5562,
	2689,
	7033,
	7033,
	7033,
	7033,
	10236,
	983,
	8862,
	9148,
	6946,
	3852,
	10294,
	2314,
	8825,
	8825,
	6946,
	3852,
	8875,
	8323,
	9953,
	8532,
	8061,
	-1,
	-1,
	7453,
	7453,
	7288,
	7288,
	7715,
	7453,
	7715,
	7432,
	9106,
	8361,
	8361,
	9208,
	9211,
	9208,
	9211,
	8114,
	8114,
	8119,
	9306,
	9707,
	9707,
	9707,
	9707,
	9214,
	9027,
	9016,
	9940,
	9954,
	9940,
	7895,
	9605,
	9617,
	8931,
	9625,
	9600,
	8895,
	8895,
	9617,
	9617,
	9617,
	9617,
	10294,
	5600,
	9744,
	2129,
	2129,
	2204,
	9035,
	9736,
	8127,
	8127,
	8993,
	9744,
	10221,
	10000,
	8475,
	8131,
	9010,
	9010,
	9013,
	9221,
	9010,
	9010,
	8475,
	9176,
	9010,
	9010,
	9010,
	9010,
	9176,
	9010,
	9311,
	9311,
	9311,
	9311,
	8637,
	8134,
	9009,
	8641,
	8134,
	7102,
	7102,
	10294,
	10235,
	10235,
	10235,
	-1,
	10235,
	10235,
	10235,
	7092,
	5697,
	6985,
	5600,
	6869,
	5484,
	6869,
	5484,
	6869,
	6946,
	7102,
	5562,
	7102,
	7102,
	10294,
	6985,
	7102,
	3816,
	6869,
	6869,
	5600,
	6985,
	4778,
	6869,
	7102,
	9183,
	9885,
	2203,
	1189,
	2140,
	2013,
	4465,
	4465,
	2018,
	4465,
	4723,
	4465,
	4864,
	4864,
	4864,
	2140,
	4465,
	4465,
	4465,
	4465,
	4465,
	4465,
	2002,
	2002,
	1285,
	2247,
	5468,
	5468,
	5468,
	5468,
	6869,
	10294,
	10294,
	7102,
	6985,
	6985,
	6985,
	6985,
	6985,
	6985,
	5600,
	5600,
	7102,
	7102,
	2864,
	5600,
	5600,
	5600,
	7102,
	7102,
	6946,
	5562,
	7102,
	5562,
	7102,
	5484,
	7102,
	7102,
	-1,
	-1,
	843,
	370,
	1121,
	4465,
	4465,
	4576,
	1120,
	1122,
	4465,
	4723,
	7503,
	8537,
	4465,
	4864,
	4864,
	4864,
	2140,
	2002,
	2002,
	10294,
	7102,
	3816,
	3816,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	-1,
	10002,
	10294,
	887,
	10235,
	10235,
	10235,
	10235,
	10294,
	10294,
	9940,
	9711,
	9707,
	6985,
	6985,
	6869,
	7102,
	9810,
	7102,
};
static const Il2CppTokenRangePair s_rgctxIndices[13] = 
{
	{ 0x0200001A, { 0, 5 } },
	{ 0x0200001B, { 5, 13 } },
	{ 0x0200001C, { 18, 5 } },
	{ 0x02000069, { 53, 9 } },
	{ 0x0600006D, { 23, 4 } },
	{ 0x0600006E, { 27, 3 } },
	{ 0x060000BC, { 30, 5 } },
	{ 0x060000BD, { 35, 5 } },
	{ 0x06000201, { 40, 2 } },
	{ 0x06000202, { 42, 2 } },
	{ 0x06000259, { 44, 5 } },
	{ 0x060002B0, { 49, 2 } },
	{ 0x060002B1, { 51, 2 } },
};
extern const uint32_t g_rgctx_LinkedList_1_t9CEF548CEC479FB27EAEEB98F0FFDA705562A403;
extern const uint32_t g_rgctx_LinkedList_1__ctor_m021247779D7C36A587EE76E7E84D40BDA31D5E0E;
extern const uint32_t g_rgctx_FastAction_1_t1AEE0F0AABEE7404C4C408917291668FB716425A;
extern const uint32_t g_rgctx_Dictionary_2_tAEB17B50990020D7888CB7BC8640B14B14CCC302;
extern const uint32_t g_rgctx_Dictionary_2__ctor_mA33E63AFB65BC9E467B8BF7A677C64B4D8E0773B;
extern const uint32_t g_rgctx_FastAction_2_t56AD8F7C1E0FCEFECC175425B3463D506B2FA568;
extern const uint32_t g_rgctx_LinkedList_1_t338546D10879B9307679AC24AF0F54F1C8C6EE34;
extern const uint32_t g_rgctx_LinkedList_1_get_First_m7E96B595CD83F59B2FF559A674EFF09A9FB68090;
extern const uint32_t g_rgctx_LinkedListNode_1_t1864D9A2CBFC1623AFE0F79E74976A7FCADA3A1A;
extern const uint32_t g_rgctx_LinkedListNode_1_get_Value_m5C2DB4AC8057756DD297D734501D2899DC756E1B;
extern const uint32_t g_rgctx_Action_2_tF5943A3D7C5CE934B355A05B9009D66482F831E0;
extern const uint32_t g_rgctx_A_tBD2E164CC30412A91CBFC391AA372C6493D30445;
extern const uint32_t g_rgctx_B_t6C2D3267AFFE177F95438677193B36775F8DF1C6;
extern const uint32_t g_rgctx_Action_2_Invoke_m335EE8932D069809AC784E42D93AE07375FA5AAC;
extern const uint32_t g_rgctx_LinkedListNode_1_get_Next_mE0701B9C68739AE8AE6E4A352F32F50019C4FCB1;
extern const uint32_t g_rgctx_LinkedList_1__ctor_m72A68E0BBEE55D44CBB7EE1D9079616499D15DF3;
extern const uint32_t g_rgctx_Dictionary_2_t8CC3267E19AC648E769CF2C202B77DFBF4E2767B;
extern const uint32_t g_rgctx_Dictionary_2__ctor_m3A05DA54DC434F29C0C12E1A520C4434B9744AE9;
extern const uint32_t g_rgctx_LinkedList_1_tBF12ED575D13E6570DEBFB1EA512BE0D4EDFE1CE;
extern const uint32_t g_rgctx_LinkedList_1__ctor_mAABCB69DA28E906DE762115AF4FBB5DA7865D66F;
extern const uint32_t g_rgctx_FastAction_3_tA68636C4F81D8339F713A4430D68BCDDCC40EDF6;
extern const uint32_t g_rgctx_Dictionary_2_tF5886DEEA57665A6FF3E8518E295A3655F128908;
extern const uint32_t g_rgctx_Dictionary_2__ctor_mD2210DEFBDD439204C1964C7C3AB595D43EA3CAA;
extern const uint32_t g_rgctx_List_1_tC3FC6F5ACB717C31DE04692889685AC715C08835;
extern const uint32_t g_rgctx_List_1_get_Count_mD6ED0B6C44639D342590B07D46688E6A57DC80E0;
extern const uint32_t g_rgctx_List_1_get_Capacity_m2EBBC6DC3C62A398D30A00FA77F76DC244AC4212;
extern const uint32_t g_rgctx_List_1_set_Capacity_m2DBDB540E4F0CBE823B6E2B02C1383D775C5A26A;
extern const uint32_t g_rgctx_Dictionary_2_t4024C6E2920D15BFFA1731A694C0D39772518CD1;
extern const uint32_t g_rgctx_Dictionary_2_get_Count_mAD933D0F5B0B75851CEA6E38FE71AD40875791D0;
extern const uint32_t g_rgctx_Dictionary_2_EnsureCapacity_mAA319C3E09BABEAD270433D768D1575E79540C57;
extern const uint32_t g_rgctx_Dictionary_2U26_tA354886E91F718B1881972AC1323F7326C2E5D05;
extern const uint32_t g_rgctx_Dictionary_2_t8C717D2322C29FE2EFC956EEA020C15C6C66BA3A;
extern const uint32_t g_rgctx_Dictionary_2__ctor_mC30A33FDDC789430BFBA7954D8FA9B77CE791ACD;
extern const uint32_t g_rgctx_Dictionary_2_Clear_m64FE9C8E4B34C6708B1F98C927D987F16ED32D61;
extern const uint32_t g_rgctx_Dictionary_2_EnsureCapacity_mD7FD7F5819CD080C77723506F9B6EA8941CA5E4B;
extern const uint32_t g_rgctx_List_1U26_t5EC084F41DFB513098B09343247C9BFBFB2DF5B8;
extern const uint32_t g_rgctx_List_1_tD1B39C79A48E1B23B2A91EEC55EE77FBC8B772F8;
extern const uint32_t g_rgctx_List_1__ctor_m72871313CC8761542CFAC7962EAC17C768573698;
extern const uint32_t g_rgctx_List_1_Clear_m195FD2F6FC6FF0251513D0FE883F001301E39114;
extern const uint32_t g_rgctx_List_1_set_Capacity_mF05267A55541065939E04F1EBBD97487EA6B5591;
extern const uint32_t g_rgctx_TU5BU5DU26_t34638B2E1F7E84A13C103ECA102E8228E5495A8F;
extern const uint32_t g_rgctx_Array_Resize_TisT_tB4C007A2682984D0FC6913C0705D947ACCDD2686_mB7195CE4A4808328845CB836896D64A709E7B865;
extern const uint32_t g_rgctx_TU5BU5DU26_t70819A0A282144E9916C49712A6DC840F9A74D7F;
extern const uint32_t g_rgctx_Array_Resize_TisT_t21B35BF972C1E5C4F52B0E073B10F9EE3CC93BD7_m08A1BE0AED907371A0B1811BF2028B1DF7CEB065;
extern const uint32_t g_rgctx_TU5BU5DU26_t736AE07A845A9529B8BA8ECF79E080516EA90EC8;
extern const uint32_t g_rgctx_TU5BU5D_t5C61AA97352897ECF97743699ED58C692DE68F36;
extern const uint32_t g_rgctx_Func_1_tF8567619428DC32A69A9ECB3C2BBF145BFA5963E;
extern const uint32_t g_rgctx_Func_1_Invoke_mC0CB898B5C94676AA210B7C3515BB8ECBB9E0370;
extern const uint32_t g_rgctx_T_tD27F54E1457D0C330FB86AB38DF1AF5CAD1FAB54;
extern const uint32_t g_rgctx_TU5BU5DU26_t70904DE589CEA82A52949E06395973C9DCED555A;
extern const uint32_t g_rgctx_Array_Resize_TisT_t17405AE98647F674620A8588A2A38EC17385AEF3_m819D8D61F3D2D33140D1CBF31E6D9A4540919072;
extern const uint32_t g_rgctx_TU5BU5DU26_tB01B5E1E9375748D28EAB2E7C309F3263CEF4E49;
extern const uint32_t g_rgctx_Array_Resize_TisT_tA5CF78E77248D29C8555253A5B104C010B149A3D_m66CF7004011C393E0AFA739A42295579B02C917F;
extern const uint32_t g_rgctx_TU5BU5D_tF671EEC26D8310D901066C4094937EF74C4BDB43;
extern const uint32_t g_rgctx_TextProcessingStack_1_tBC9146D50270B331600B7384456009B35EBAE993;
extern const uint32_t g_rgctx_T_t221CFDD3BE5C61800E826B7B924C88413E6CFAD5;
extern const uint32_t g_rgctx_TU5BU5D_tF671EEC26D8310D901066C4094937EF74C4BDB43;
extern const uint32_t g_rgctx_TextProcessingStack_1U5BU5D_tF6C207267F84EF7B2E35B63848ABD883EF3029F6;
extern const uint32_t g_rgctx_TextProcessingStack_1_SetDefault_mC81167C7CE9FB6C65188B380395CF6B157C5D9FE;
extern const uint32_t g_rgctx_TextProcessingStack_1_tBC9146D50270B331600B7384456009B35EBAE993;
extern const uint32_t g_rgctx_Array_Resize_TisT_t221CFDD3BE5C61800E826B7B924C88413E6CFAD5_m6B12D3B6815FB2F9D9DF2EA23C4DFCA6BF547697;
extern const uint32_t g_rgctx_TU5BU5DU26_t4898B7229712712817C3A8E604F06F6847D1B0E9;
static const Il2CppRGCTXDefinition s_rgctxValues[62] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedList_1_t9CEF548CEC479FB27EAEEB98F0FFDA705562A403 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1__ctor_m021247779D7C36A587EE76E7E84D40BDA31D5E0E },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FastAction_1_t1AEE0F0AABEE7404C4C408917291668FB716425A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_tAEB17B50990020D7888CB7BC8640B14B14CCC302 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2__ctor_mA33E63AFB65BC9E467B8BF7A677C64B4D8E0773B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FastAction_2_t56AD8F7C1E0FCEFECC175425B3463D506B2FA568 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedList_1_t338546D10879B9307679AC24AF0F54F1C8C6EE34 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1_get_First_m7E96B595CD83F59B2FF559A674EFF09A9FB68090 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedListNode_1_t1864D9A2CBFC1623AFE0F79E74976A7FCADA3A1A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNode_1_get_Value_m5C2DB4AC8057756DD297D734501D2899DC756E1B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Action_2_tF5943A3D7C5CE934B355A05B9009D66482F831E0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_A_tBD2E164CC30412A91CBFC391AA372C6493D30445 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_B_t6C2D3267AFFE177F95438677193B36775F8DF1C6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Action_2_Invoke_m335EE8932D069809AC784E42D93AE07375FA5AAC },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedListNode_1_get_Next_mE0701B9C68739AE8AE6E4A352F32F50019C4FCB1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1__ctor_m72A68E0BBEE55D44CBB7EE1D9079616499D15DF3 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_t8CC3267E19AC648E769CF2C202B77DFBF4E2767B },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2__ctor_m3A05DA54DC434F29C0C12E1A520C4434B9744AE9 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_LinkedList_1_tBF12ED575D13E6570DEBFB1EA512BE0D4EDFE1CE },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_LinkedList_1__ctor_mAABCB69DA28E906DE762115AF4FBB5DA7865D66F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_FastAction_3_tA68636C4F81D8339F713A4430D68BCDDCC40EDF6 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_tF5886DEEA57665A6FF3E8518E295A3655F128908 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2__ctor_mD2210DEFBDD439204C1964C7C3AB595D43EA3CAA },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tC3FC6F5ACB717C31DE04692889685AC715C08835 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_mD6ED0B6C44639D342590B07D46688E6A57DC80E0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Capacity_m2EBBC6DC3C62A398D30A00FA77F76DC244AC4212 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_set_Capacity_m2DBDB540E4F0CBE823B6E2B02C1383D775C5A26A },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_t4024C6E2920D15BFFA1731A694C0D39772518CD1 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_get_Count_mAD933D0F5B0B75851CEA6E38FE71AD40875791D0 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_EnsureCapacity_mAA319C3E09BABEAD270433D768D1575E79540C57 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2U26_tA354886E91F718B1881972AC1323F7326C2E5D05 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Dictionary_2_t8C717D2322C29FE2EFC956EEA020C15C6C66BA3A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2__ctor_mC30A33FDDC789430BFBA7954D8FA9B77CE791ACD },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_Clear_m64FE9C8E4B34C6708B1F98C927D987F16ED32D61 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Dictionary_2_EnsureCapacity_mD7FD7F5819CD080C77723506F9B6EA8941CA5E4B },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1U26_t5EC084F41DFB513098B09343247C9BFBFB2DF5B8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_tD1B39C79A48E1B23B2A91EEC55EE77FBC8B772F8 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1__ctor_m72871313CC8761542CFAC7962EAC17C768573698 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_Clear_m195FD2F6FC6FF0251513D0FE883F001301E39114 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_set_Capacity_mF05267A55541065939E04F1EBBD97487EA6B5591 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_t34638B2E1F7E84A13C103ECA102E8228E5495A8F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_tB4C007A2682984D0FC6913C0705D947ACCDD2686_mB7195CE4A4808328845CB836896D64A709E7B865 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_t70819A0A282144E9916C49712A6DC840F9A74D7F },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_t21B35BF972C1E5C4F52B0E073B10F9EE3CC93BD7_m08A1BE0AED907371A0B1811BF2028B1DF7CEB065 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_t736AE07A845A9529B8BA8ECF79E080516EA90EC8 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t5C61AA97352897ECF97743699ED58C692DE68F36 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_Func_1_tF8567619428DC32A69A9ECB3C2BBF145BFA5963E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Func_1_Invoke_mC0CB898B5C94676AA210B7C3515BB8ECBB9E0370 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tD27F54E1457D0C330FB86AB38DF1AF5CAD1FAB54 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_t70904DE589CEA82A52949E06395973C9DCED555A },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_t17405AE98647F674620A8588A2A38EC17385AEF3_m819D8D61F3D2D33140D1CBF31E6D9A4540919072 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_tB01B5E1E9375748D28EAB2E7C309F3263CEF4E49 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_tA5CF78E77248D29C8555253A5B104C010B149A3D_m66CF7004011C393E0AFA739A42295579B02C917F },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tF671EEC26D8310D901066C4094937EF74C4BDB43 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TextProcessingStack_1_tBC9146D50270B331600B7384456009B35EBAE993 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_t221CFDD3BE5C61800E826B7B924C88413E6CFAD5 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_tF671EEC26D8310D901066C4094937EF74C4BDB43 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TextProcessingStack_1U5BU5D_tF6C207267F84EF7B2E35B63848ABD883EF3029F6 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_TextProcessingStack_1_SetDefault_mC81167C7CE9FB6C65188B380395CF6B157C5D9FE },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TextProcessingStack_1_tBC9146D50270B331600B7384456009B35EBAE993 },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_Array_Resize_TisT_t221CFDD3BE5C61800E826B7B924C88413E6CFAD5_m6B12D3B6815FB2F9D9DF2EA23C4DFCA6BF547697 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5DU26_t4898B7229712712817C3A8E604F06F6847D1B0E9 },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_UnityEngine_TextCoreTextEngineModule_CodeGenModule;
const Il2CppCodeGenModule g_UnityEngine_TextCoreTextEngineModule_CodeGenModule = 
{
	"UnityEngine.TextCoreTextEngineModule.dll",
	744,
	s_methodPointers,
	54,
	s_adjustorThunks,
	s_InvokerIndices,
	0,
	NULL,
	13,
	s_rgctxIndices,
	62,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

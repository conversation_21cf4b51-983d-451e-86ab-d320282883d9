{"DataTable": {"attribute_marked_count_always_link_assembly": 1, "attribute_swept_count_always_link_assembly": 0, "attribute_total_count_always_link_assembly": 1, "attribute_marked_count_preserve": 31, "attribute_total_count_preserve": 25, "attribute_total_count_preserve_body": 0, "attribute_marked_count_required_member": 232, "attribute_swept_count_required_member": 26, "attribute_total_count_required_member": 258, "attribute_marked_count_require_derived": 0, "attribute_swept_count_require_derived": 0, "attribute_total_count_require_derived": 0, "attribute_marked_count_require_implementors": 0, "attribute_swept_count_require_implementors": 0, "attribute_total_count_require_implementors": 0, "attribute_total_count_required_interface": 0, "attribute_marked_count_require_attribute_usages": 633, "attribute_total_count_require_attribute_usages": 2, "attribute_total_count_dynamic_dependency": 0, "attribute_marked_count_dynamic_dependency": 11, "attribute_swept_count_dynamic_dependency": -11, "assembly_counts_total_in": 93, "assembly_counts_link": 38, "assembly_counts_copy": 4, "assembly_counts_delete": 51, "assembly_counts_total_out": 42, "unresolved_stubbing_total_count": 0, "unresolved_stubbing_missing_interface_method_count": 0, "unresolved_stubbing_missing_abstract_class_method_count": 0, "unresolved_stubbing_missing_type_count": 0, "unresolved_stubbing_missing_method_count": 0, "unrecognized_reflection_access_total_count": 289, "unrecognized_reflection_access_core_count": 158, "unrecognized_reflection_access_unity_count": 131, "unrecognized_reflection_access_user_count": 0, "recognized_reflection_access_total_count": 43, "recognized_reflection_access_core_count": 29, "recognized_reflection_access_unity_count": 14, "recognized_reflection_access_user_count": 0, "link_xml_total_count": 10, "link_xml_embedded_count": 1, "link_xml_embedded_unity_count": 0, "link_xml_embedded_user_count": 0, "link_xml_file_count": 9, "link_xml_assembly_preserve_all_total_count": 0, "link_xml_assembly_preserve_all_unity_count": 0, "link_xml_assembly_preserve_all_core_count": 0, "link_xml_assembly_preserve_all_user_count": 0, "engine_module_total_in": 68, "engine_module_deleted": 38, "engine_module_total_out": 30, "option_rule_set": "Minimal", "option_enable_report": false, "option_enable_snapshot": false, "option_enable_engine_module_stripping": true, "option_unity_root_strategy": "AllNonEngineAndNonClassLibraries", "option_enable_ildump": false}}
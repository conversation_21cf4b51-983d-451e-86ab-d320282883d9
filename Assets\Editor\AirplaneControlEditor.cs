using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(AirplaneControl))]
public class AirplaneControlEditor : Editor
{
    private GUIStyle headerStyle;
    private GUIStyle subHeaderStyle;

    private void OnEnable()
    {
        // Initialize styles
        headerStyle = new GUIStyle();
        headerStyle.fontSize = 16;
        headerStyle.fontStyle = FontStyle.Bold;
        headerStyle.alignment = TextAnchor.MiddleCenter;
        headerStyle.normal.textColor = Color.white;
        headerStyle.margin = new RectOffset(0, 0, 10, 5);

        subHeaderStyle = new GUIStyle();
        subHeaderStyle.fontSize = 14;
        subHeaderStyle.fontStyle = FontStyle.Bold;
        subHeaderStyle.alignment = TextAnchor.MiddleCenter;
        subHeaderStyle.normal.textColor = new Color(0.8f, 0.8f, 1f);
        subHeaderStyle.margin = new RectOffset(0, 0, 5, 10);
    }

    public override void OnInspectorGUI()
    {
        // Draw the developer header
        EditorGUILayout.Space(10);
        EditorGUILayout.LabelField("Developer: Ali Taj", headerStyle);
        EditorGUILayout.LabelField("Airplane Control", subHeaderStyle);
        EditorGUILayout.Space(10);
        
        // Draw a separator line
        EditorGUILayout.LabelField("", GUI.skin.horizontalSlider);
        EditorGUILayout.Space(5);

        // Draw the default inspector
        serializedObject.Update();
        DrawDefaultInspector();
        serializedObject.ApplyModifiedProperties();
    }
}
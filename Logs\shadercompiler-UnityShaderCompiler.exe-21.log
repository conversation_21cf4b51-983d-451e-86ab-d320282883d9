Base path: 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data', plugins path 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines'
Cmd: initializeCompiler

Cmd: compileSnippet
  insize=18353 file=Assets/AdvancedHelicopterController/Models/Shaders/WFX_S Alpha-Diffuse TintColor.shader name=WFX/Transparent Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_DXT5nm UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHTMAP_DLDR_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 LIGHTPROBE_SH DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_RGBM_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=d3d11 reqs=33 mask=6 start=30 ok=1 outsize=1254


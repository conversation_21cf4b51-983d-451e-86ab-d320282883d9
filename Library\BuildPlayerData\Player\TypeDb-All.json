{"root": [{"name": "Assembly-CSharp", "path": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll", "types": [{"className": "vThirdPersonCamera", "fieldInfos": [{"name": "target", "type": "UnityEngine.Transform", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "smoothCameraRotation", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "culling<PERSON>ayer", "type": "UnityEngine.LayerMask", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "lockCamera", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "rightOffset", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "defaultDistance", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "height", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "smoothFollow", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "xMouseSensitivity", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "yMouseSensitivity", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "yMinLimit", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "yMaxLimit", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "indexList", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "indexLookPoint", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "offSetPlayerPivot", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "currentStateName", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "currentTarget", "type": "UnityEngine.Transform", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "movementSpeed", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "vPickupItem", "fieldInfos": [{"name": "_audioClip", "type": "UnityEngine.AudioClip", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "_particle", "type": "UnityEngine.GameObject", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "JoystickPlayerExample", "fieldInfos": [{"name": "speed", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "variableJoystick", "type": "VariableJoystick", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "rb", "type": "UnityEngine.Rigidbody", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "JoystickSetterExample", "fieldInfos": [{"name": "variableJoystick", "type": "VariableJoystick", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "valueText", "type": "UnityEngine.UI.Text", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "background", "type": "UnityEngine.UI.Image", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "axisSprites", "type": "UnityEngine.Sprite[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Joystick", "fieldInfos": [{"name": "handleRange", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "deadZone", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "axisOptions", "type": "AxisOptions", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "snapX", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "snapY", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "background", "type": "UnityEngine.RectTransform", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "handle", "type": "UnityEngine.RectTransform", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "AxisOptions", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "DynamicJoystick", "fieldInfos": [{"name": "moveT<PERSON><PERSON>old", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "FixedJoystick", "fieldInfos": []}, {"className": "Floating<PERSON><PERSON>stick", "fieldInfos": []}, {"className": "VariableJoystick", "fieldInfos": [{"name": "moveT<PERSON><PERSON>old", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "joystickType", "type": "JoystickType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "JoystickType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Dronecamera", "fieldInfos": [{"name": "joystick", "type": "Joystick", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "Dronecameraa", "type": "UnityEngine.Camera", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "upHeightButton", "type": "UnityEngine.UI.Button", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "downHeightButton", "type": "UnityEngine.UI.Button", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "horizontalSpeed", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "verticalSpeed", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "heightSpeed", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "smoothTime", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "limitHorizontalMovement", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "minX", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "maxX", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "limitVerticalMovement", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "minZ", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "maxZ", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "limitHeightMovement", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "minY", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "maxY", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "moveRelativeToRotation", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "enableMouseDragRotation", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "mouseSensitivity", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "touchSensitivity", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "rotationSmoothTime", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "limitVerticalRotation", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "minVerticalAngle", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "maxVerticalAngle", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "limitHorizontalRotation", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "minHorizontalAngle", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "maxHorizontalAngle", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "MobileUIController", "fieldInfos": [{"name": "jumpButton", "type": "UnityEngine.UI.Button", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "sprintButton", "type": "UnityEngine.UI.Button", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "autoHideOnPC", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "pressedAlpha", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UIButtonConnector", "fieldInfos": [{"name": "autoConnectOnStart", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "searchByName", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "jumpButtonName", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "sprintButtonName", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "manualJumpButton", "type": "UnityEngine.UI.Button", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "manualSprintButton", "type": "UnityEngine.UI.Button", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "targetInput", "type": "Invector.vCharacterController.vThirdPersonInput", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Invector.vAnimateUV", "fieldInfos": [{"name": "speed", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "_renderer", "type": "UnityEngine.Renderer", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "textureParameters", "type": "System.String[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Invector.vExtensions", "fieldInfos": []}, {"className": "Invector.ClipPlanePoints", "fieldInfos": [{"name": "UpperLeft", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "UpperRight", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "LowerLeft", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "LowerRight", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Invector.Utils.vComment", "fieldInfos": []}, {"className": "Invector.vCharacterController.vThirdPersonAnimator", "fieldInfos": []}, {"className": "Invector.vCharacterController.vThirdPersonMotor", "fieldInfos": [{"name": "useRootMotion", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "rotateByWorld", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "useContinuousSprint", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "locomotionType", "type": "Invector.vCharacterController.vThirdPersonMotor+LocomotionType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "freeSpeed", "type": "Invector.vCharacterController.vThirdPersonMotor+vMovementSpeed", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "strafeSpeed", "type": "Invector.vCharacterController.vThirdPersonMotor+vMovementSpeed", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "jumpWithRigidbodyForce", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "jumpAndRotate", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "jumpTimer", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "jumpHeight", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "airSpeed", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "airSmooth", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "extraGravity", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "limitFallVelocity", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "groundLayer", "type": "UnityEngine.LayerMask", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "groundMinDistance", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "groundMaxDistance", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "slopeLimit", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Invector.vCharacterController.vThirdPersonMotor+LocomotionType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Invector.vCharacterController.vThirdPersonMotor+vMovementSpeed", "fieldInfos": [{"name": "movementSmooth", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "animationSmooth", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "rotationSpeed", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "rotateWithCamera", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "walkSpeed", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "runningSpeed", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "sprintSpeed", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Invector.vCharacterController.vAnimatorParameters", "fieldInfos": []}, {"className": "Invector.vCharacterController.vThirdPersonController", "fieldInfos": []}, {"className": "Invector.vCharacterController.vThirdPersonInput", "fieldInfos": [{"name": "horizontalInput", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "verticallInput", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "jumpInput", "type": "UnityEngine.KeyCode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "strafeInput", "type": "UnityEngine.KeyCode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "sprintInput", "type": "UnityEngine.KeyCode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "rotateCameraXInput", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "rotateCameraYInput", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "enablePCCameraControl", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "enableMobileCameraControl", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "touchSensitivity", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "cc", "type": "Invector.vCharacterController.vThirdPersonController", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "tpCamera", "type": "vThirdPersonCamera", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "camera<PERSON>ain", "type": "UnityEngine.Camera", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "joystick", "type": "Joystick", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "jumpButton", "type": "UnityEngine.UI.Button", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "sprintButton", "type": "UnityEngine.UI.Button", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}]}, {"name": "Unity.InputSystem", "path": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.InputSystem.dll", "types": [{"className": "UISupport", "fieldInfos": []}, {"className": "Unity.XR.OpenVR.OpenVRHMD", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.XR.XRHMD", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.TrackedDevice", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputDevice", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputControl", "fieldInfos": []}, {"className": "Unity.XR.OpenVR.OpenVRControllerWMR", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.XR.XRController", "fieldInfos": []}, {"className": "Unity.XR.OpenVR.ViveWand", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.XR.XRControllerWithRumble", "fieldInfos": []}, {"className": "Unity.XR.OpenVR.ViveLighthouse", "fieldInfos": []}, {"className": "Unity.XR.OpenVR.ViveTracker", "fieldInfos": []}, {"className": "Unity.XR.OpenVR.HandedViveTracker", "fieldInfos": []}, {"className": "Unity.XR.OpenVR.OpenVROculusTouchController", "fieldInfos": []}, {"className": "Unity.XR.Oculus.Input.OculusHMD", "fieldInfos": []}, {"className": "Unity.XR.Oculus.Input.OculusTouchController", "fieldInfos": []}, {"className": "Unity.XR.Oculus.Input.OculusTrackingReference", "fieldInfos": []}, {"className": "Unity.XR.Oculus.Input.OculusRemote", "fieldInfos": []}, {"className": "Unity.XR.Oculus.Input.OculusHMDExtended", "fieldInfos": []}, {"className": "Unity.XR.Oculus.Input.GearVRTrackedController", "fieldInfos": []}, {"className": "Unity.XR.GoogleVr.DaydreamHMD", "fieldInfos": []}, {"className": "Unity.XR.GoogleVr.DaydreamController", "fieldInfos": []}, {"className": "UnityEngine.XR.WindowsMR.Input.WMRHMD", "fieldInfos": []}, {"className": "UnityEngine.XR.WindowsMR.Input.HololensHand", "fieldInfos": []}, {"className": "UnityEngine.XR.WindowsMR.Input.WMRSpatialController", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.IInputActionCollection", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.IInputActionCollection2", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.IInputInteraction", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputInteraction", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputAction", "fieldInfos": [{"name": "m_Name", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Type", "type": "UnityEngine.InputSystem.InputActionType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ExpectedControlType", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Id", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Processors", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Interactions", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SingletonActionBindings", "type": "UnityEngine.InputSystem.InputBinding[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Flags", "type": "UnityEngine.InputSystem.InputAction+ActionFlags", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputAction+ActionFlags", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputAction+CallbackContext", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputActionAsset", "fieldInfos": [{"name": "m_ActionMaps", "type": "UnityEngine.InputSystem.InputActionMap[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ControlSchemes", "type": "UnityEngine.InputSystem.InputControlScheme[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_IsProjectWide", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputActionAsset+WriteFileJson", "fieldInfos": [{"name": "name", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "maps", "type": "UnityEngine.InputSystem.InputActionMap+WriteMapJson[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "controlSchemes", "type": "UnityEngine.InputSystem.InputControlScheme+SchemeJson[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputActionAsset+WriteFileJsonNoName", "fieldInfos": [{"name": "maps", "type": "UnityEngine.InputSystem.InputActionMap+WriteMapJson[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "controlSchemes", "type": "UnityEngine.InputSystem.InputControlScheme+SchemeJson[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputActionAsset+ReadFileJson", "fieldInfos": [{"name": "name", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "maps", "type": "UnityEngine.InputSystem.InputActionMap+ReadMapJson[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "controlSchemes", "type": "UnityEngine.InputSystem.InputControlScheme+SchemeJson[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputActionChange", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputActionMap", "fieldInfos": [{"name": "m_Name", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Id", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Asset", "type": "UnityEngine.InputSystem.InputActionAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Actions", "type": "UnityEngine.InputSystem.InputAction[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Bindings", "type": "UnityEngine.InputSystem.InputBinding[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputActionMap+Flags", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputActionMap+DeviceArray", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputActionMap+BindingOverrideListJson", "fieldInfos": [{"name": "bindings", "type": "System.Collections.Generic.List`1[UnityEngine.InputSystem.InputActionMap+BindingOverrideJson]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputActionMap+BindingOverrideJson", "fieldInfos": [{"name": "action", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "id", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "path", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "interactions", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "processors", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputActionMap+BindingJson", "fieldInfos": [{"name": "name", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "id", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "path", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "interactions", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "processors", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "groups", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "action", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "isComposite", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "isPartOfComposite", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputActionMap+ReadActionJson", "fieldInfos": [{"name": "name", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "type", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "id", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "expectedControlType", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "expectedControlLayout", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "processors", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "interactions", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "passThrough", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "initialStateCheck", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "bindings", "type": "UnityEngine.InputSystem.InputActionMap+BindingJson[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputActionMap+WriteActionJson", "fieldInfos": [{"name": "name", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "type", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "id", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "expectedControlType", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "processors", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "interactions", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "initialStateCheck", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputActionMap+ReadMapJson", "fieldInfos": [{"name": "name", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "id", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "actions", "type": "UnityEngine.InputSystem.InputActionMap+ReadActionJson[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "bindings", "type": "UnityEngine.InputSystem.InputActionMap+BindingJson[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputActionMap+WriteMapJson", "fieldInfos": [{"name": "name", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "id", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "actions", "type": "UnityEngine.InputSystem.InputActionMap+WriteActionJson[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "bindings", "type": "UnityEngine.InputSystem.InputActionMap+BindingJson[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputActionMap+WriteFileJson", "fieldInfos": [{"name": "maps", "type": "UnityEngine.InputSystem.InputActionMap+WriteMapJson[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputActionMap+ReadFileJson", "fieldInfos": [{"name": "actions", "type": "UnityEngine.InputSystem.InputActionMap+ReadActionJson[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "maps", "type": "UnityEngine.InputSystem.InputActionMap+ReadMapJson[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputActionRebindingExtensions", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputActionPhase", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputActionProperty", "fieldInfos": [{"name": "m_UseReference", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Action", "type": "UnityEngine.InputSystem.InputAction", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Reference", "type": "UnityEngine.InputSystem.InputActionReference", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputActionReference", "fieldInfos": [{"name": "m_Asset", "type": "UnityEngine.InputSystem.InputActionAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ActionId", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputActionSetupExtensions", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputActionState", "fieldInfos": [{"name": "maps", "type": "UnityEngine.InputSystem.InputActionMap[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "totalProcessorCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputActionType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputBinding", "fieldInfos": [{"name": "m_Name", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Id", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Path", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Interactions", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Processors", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Groups", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Action", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Flags", "type": "UnityEngine.InputSystem.InputBinding+Flags", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputBinding+DisplayStringOptions", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputBinding+MatchOptions", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputBinding+Flags", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputBindingComposite", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputBindingCompositeContext", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputBindingResolver", "fieldInfos": [{"name": "totalProcessorCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "totalCompositeCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "totalInteractionCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "maps", "type": "UnityEngine.InputSystem.InputActionMap[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputControlScheme", "fieldInfos": [{"name": "m_Name", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_BindingGroup", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DeviceRequirements", "type": "UnityEngine.InputSystem.InputControlScheme+DeviceRequirement[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputControlScheme+MatchResult", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputControlScheme+DeviceRequirement", "fieldInfos": [{"name": "m_ControlPath", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Flags", "type": "UnityEngine.InputSystem.InputControlScheme+DeviceRequirement+Flags", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputControlScheme+DeviceRequirement+Flags", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputControlScheme+SchemeJson", "fieldInfos": [{"name": "name", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "bindingGroup", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "devices", "type": "UnityEngine.InputSystem.InputControlScheme+SchemeJson+DeviceJson[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputControlScheme+SchemeJson+DeviceJson", "fieldInfos": [{"name": "devicePath", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "isOptional", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "isOR", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputInteractionContext", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputSystem", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.CommonUsages", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputControlExtensions", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputControlLayoutChange", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputControlPath", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputProcessor", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Gamepad", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputDeviceChange", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Joystick", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Key", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Keyboard", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Mouse", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Pointer", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.PenButton", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Pen", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.FastKeyboard", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.FastMouse", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.FastTouchscreen", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Touchscreen", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputRemoting", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.RemoteInputPlayerConnection", "fieldInfos": [{"name": "m_ConnectedIds", "type": "System.Int32[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.RemoteInputPlayerConnection+Subscriber", "fieldInfos": [{"name": "owner", "type": "UnityEngine.InputSystem.RemoteInputPlayerConnection", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Sensor", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Accelerometer", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Gyroscope", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.GravitySensor", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.AttitudeSensor", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LinearAccelerationSensor", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.MagneticFieldSensor", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LightSensor", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.PressureSensor", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.ProximitySensor", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.HumiditySensor", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.AmbientTemperatureSensor", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.StepCounter", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.HingeAngle", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.TouchPhase", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputExtensions", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputFeatureNames", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputManager", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputSettings", "fieldInfos": [{"name": "m_SupportedDevices", "type": "System.String[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_UpdateMode", "type": "UnityEngine.InputSystem.InputSettings+UpdateMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ScrollDeltaBehavior", "type": "UnityEngine.InputSystem.InputSettings+ScrollDeltaBehavior", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MaxEventBytesPerUpdate", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MaxQueuedEventsPerUpdate", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CompensateForScreenOrientation", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "UnityEngine.InputSystem.InputSettings+BackgroundBehavior", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_EditorInputBehaviorInPlayMode", "type": "UnityEngine.InputSystem.InputSettings+EditorInputBehaviorInPlayMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_InputActionPropertyDrawerMode", "type": "UnityEngine.InputSystem.InputSettings+InputActionPropertyDrawerMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DefaultDeadzoneMin", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DefaultDeadzoneMax", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DefaultButtonPressPoint", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ButtonReleaseThreshold", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DefaultTapTime", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DefaultSlowTapTime", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DefaultHoldTime", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_TapRadius", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MultiTapDelayTime", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DisableRedundantEventsMerging", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ShortcutKeysConsumeInputs", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputSettings+UpdateMode", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputSettings+ScrollDeltaBehavior", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputSettings+BackgroundBehavior", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputSettings+EditorInputBehaviorInPlayMode", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputSettings+InputActionPropertyDrawerMode", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.DefaultInputActions", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputValue", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.PlayerInput", "fieldInfos": [{"name": "m_Actions", "type": "UnityEngine.InputSystem.InputActionAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_NotificationBehavior", "type": "UnityEngine.InputSystem.PlayerNotifications", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_UIInputModule", "type": "UnityEngine.InputSystem.UI.InputSystemUIInputModule", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DeviceLostEvent", "type": "UnityEngine.InputSystem.PlayerInput+DeviceLostEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DeviceRegainedEvent", "type": "UnityEngine.InputSystem.PlayerInput+DeviceRegainedEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ControlsChangedEvent", "type": "UnityEngine.InputSystem.PlayerInput+ControlsChangedEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ActionEvents", "type": "UnityEngine.InputSystem.PlayerInput+ActionEvent[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_NeverAutoSwitchControlSchemes", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DefaultControlScheme", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DefaultActionMap", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SplitScreenIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Camera", "type": "UnityEngine.Camera", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.PlayerInput+ActionEvent", "fieldInfos": [{"name": "m_ActionId", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ActionName", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.PlayerInput+DeviceLostEvent", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.PlayerInput+DeviceRegainedEvent", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.PlayerInput+ControlsChangedEvent", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.PlayerInputManager", "fieldInfos": [{"name": "m_NotificationBehavior", "type": "UnityEngine.InputSystem.PlayerNotifications", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MaxPlayerCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AllowJoining", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "UnityEngine.InputSystem.PlayerJoinBehavior", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PlayerJoinedEvent", "type": "UnityEngine.InputSystem.PlayerInputManager+PlayerJoinedEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PlayerLeftEvent", "type": "UnityEngine.InputSystem.PlayerInputManager+PlayerLeftEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_JoinAction", "type": "UnityEngine.InputSystem.InputActionProperty", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PlayerPrefab", "type": "UnityEngine.GameObject", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SplitScreen", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MaintainAspectRatioInSplitScreen", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FixedNumberOfSplitScreens", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SplitScreenRect", "type": "UnityEngine.Rect", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.PlayerInputManager+PlayerJoinedEvent", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.PlayerInputManager+PlayerLeftEvent", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.PlayerJoinBehavior", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.PlayerNotifications", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.DynamicBitfield", "fieldInfos": [{"name": "length", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.XR.PoseState", "fieldInfos": [{"name": "isTracked", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "trackingState", "type": "UnityEngine.XR.InputTrackingState", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "position", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "rotation", "type": "UnityEngine.Quaternion", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "velocity", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "angularVelocity", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.XR.PoseControl", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputControl`1[UnityEngine.InputSystem.XR.PoseState]", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.XR.TrackedPoseDriver", "fieldInfos": [{"name": "m_TrackingType", "type": "UnityEngine.InputSystem.XR.TrackedPoseDriver+TrackingType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_UpdateType", "type": "UnityEngine.InputSystem.XR.TrackedPoseDriver+UpdateType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_IgnoreTrackingState", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PositionInput", "type": "UnityEngine.InputSystem.InputActionProperty", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_RotationInput", "type": "UnityEngine.InputSystem.InputActionProperty", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_TrackingStateInput", "type": "UnityEngine.InputSystem.InputActionProperty", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PositionAction", "type": "UnityEngine.InputSystem.InputAction", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_RotationAction", "type": "UnityEngine.InputSystem.InputAction", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.XR.TrackedPoseDriver+TrackingType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.XR.TrackedPoseDriver+TrackingStates", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.XR.TrackedPoseDriver+UpdateType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.XR.XRLayoutBuilder", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.XR.XRUtilities", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.XR.FeatureType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.XR.UsageHint", "fieldInfos": [{"name": "content", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.XR.XRFeatureDescriptor", "fieldInfos": [{"name": "name", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "usageHints", "type": "System.Collections.Generic.List`1[UnityEngine.InputSystem.XR.UsageHint]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "featureType", "type": "UnityEngine.InputSystem.XR.FeatureType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "customSize", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.XR.XRDeviceDescriptor", "fieldInfos": [{"name": "deviceName", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "manufacturer", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "serialNumber", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "characteristics", "type": "UnityEngine.XR.InputDeviceCharacteristics", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "deviceId", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "inputFeatures", "type": "System.Collections.Generic.List`1[UnityEngine.InputSystem.XR.XRFeatureDescriptor]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.XR.Bone", "fieldInfos": [{"name": "m_ParentBoneIndex", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Position", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Rotation", "type": "UnityEngine.Quaternion", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.XR.Eyes", "fieldInfos": [{"name": "m_LeftEyePosition", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_LeftEyeRotation", "type": "UnityEngine.Quaternion", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_RightEyePosition", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_RightEyeRotation", "type": "UnityEngine.Quaternion", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FixationPoint", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_LeftEyeOpenAmount", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_RightEyeOpenAmount", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.XR.BoneControl", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputControl`1[UnityEngine.InputSystem.XR.Bone]", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.XR.EyesControl", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputControl`1[UnityEngine.InputSystem.XR.Eyes]", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.XR.XRSupport", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.XR.Haptics.BufferedRumble", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.XR.Haptics.HapticState", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.XR.Haptics.GetCurrentHapticStateCommand", "fieldInfos": [{"name": "samplesQueued", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "samplesAvailable", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.XR.Haptics.HapticCapabilities", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.XR.Haptics.GetHapticCapabilitiesCommand", "fieldInfos": [{"name": "numChannels", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "supportsImpulse", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "supportsBuffer", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "frequencyHz", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "maxBufferSize", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "optimalBufferSize", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.XR.Haptics.SendBufferedHapticCommand", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.XR.Haptics.SendHapticImpulseCommand", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.XInput.IXboxOneRumble", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.XInput.XInputController", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.XInput.XInputSupport", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Users.InputUser", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Users.InputUserAccountHandle", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Users.InputUserChange", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Users.InputUserPairingOptions", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Users.InputUserSettings", "fieldInfos": [{"name": "m_CustomBindings", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.UI.BaseInputOverride", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.UI.ExtendedAxisEventData", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.UI.ExtendedPointerEventData", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.UI.UIPointerType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.UI.UIPointerBehavior", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.UI.InputSystemUIInputModule", "fieldInfos": [{"name": "m_MoveRepeatDelay", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MoveRepeatRate", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_XRTrackingOrigin", "type": "UnityEngine.Transform", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ActionsAsset", "type": "UnityEngine.InputSystem.InputActionAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PointAction", "type": "UnityEngine.InputSystem.InputActionReference", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MoveAction", "type": "UnityEngine.InputSystem.InputActionReference", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SubmitAction", "type": "UnityEngine.InputSystem.InputActionReference", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CancelAction", "type": "UnityEngine.InputSystem.InputActionReference", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_LeftClickAction", "type": "UnityEngine.InputSystem.InputActionReference", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MiddleClickAction", "type": "UnityEngine.InputSystem.InputActionReference", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_RightClickAction", "type": "UnityEngine.InputSystem.InputActionReference", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ScrollWheelAction", "type": "UnityEngine.InputSystem.InputActionReference", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_TrackedDevicePositionAction", "type": "UnityEngine.InputSystem.InputActionReference", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_TrackedDeviceOrientationAction", "type": "UnityEngine.InputSystem.InputActionReference", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DeselectOnBackgroundClick", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Pointer<PERSON>eh<PERSON>or", "type": "UnityEngine.InputSystem.UI.UIPointerBehavior", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CursorLockBehavior", "type": "UnityEngine.InputSystem.UI.InputSystemUIInputModule+CursorLockBehavior", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ScrollDeltaPerTick", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.UI.InputSystemUIInputModule+InputActionReferenceState", "fieldInfos": [{"name": "refCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "enabledByInputModule", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.UI.InputSystemUIInputModule+CursorLockBehavior", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.UI.MultiplayerEventSystem", "fieldInfos": [{"name": "m_PlayerRoot", "type": "UnityEngine.GameObject", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.UI.NavigationModel", "fieldInfos": [{"name": "move", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "consecutiveMoveCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "lastMoveDirection", "type": "UnityEngine.EventSystems.MoveDirection", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "lastMoveTime", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.UI.PointerModel", "fieldInfos": [{"name": "changed<PERSON><PERSON><PERSON><PERSON>e", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.UI.TrackedDeviceRaycaster", "fieldInfos": [{"name": "m_IgnoreReversedGraphics", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CheckFor2DOcclusion", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CheckFor3DOcclusion", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MaxDistance", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_BlockingMask", "type": "UnityEngine.LayerMask", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.UI.TrackedDeviceRaycaster+RaycastHitData", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.UI.VirtualMouseInput", "fieldInfos": [{"name": "m_CursorMode", "type": "UnityEngine.InputSystem.UI.VirtualMouseInput+CursorMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CursorGraphic", "type": "UnityEngine.UI.Graphic", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CursorTransform", "type": "UnityEngine.RectTransform", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CursorSpeed", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ScrollSpeed", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_StickAction", "type": "UnityEngine.InputSystem.InputActionProperty", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_LeftButtonAction", "type": "UnityEngine.InputSystem.InputActionProperty", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MiddleButtonAction", "type": "UnityEngine.InputSystem.InputActionProperty", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_RightButtonAction", "type": "UnityEngine.InputSystem.InputActionProperty", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ForwardButtonAction", "type": "UnityEngine.InputSystem.InputActionProperty", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_BackButtonAction", "type": "UnityEngine.InputSystem.InputActionProperty", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ScrollWheelAction", "type": "UnityEngine.InputSystem.InputActionProperty", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.UI.VirtualMouseInput+CursorMode", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.OnScreen.OnScreenButton", "fieldInfos": [{"name": "m_ControlPath", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.OnScreen.OnScreenControl", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.OnScreen.OnScreenControl+OnScreenDeviceInfo", "fieldInfos": [{"name": "firstControl", "type": "UnityEngine.InputSystem.OnScreen.OnScreenControl", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.OnScreen.UGUIOnScreenControlUtils", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.OnScreen.OnScreenStick", "fieldInfos": [{"name": "m_MovementRange", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DynamicOriginRange", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ControlPath", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_<PERSON><PERSON><PERSON>", "type": "UnityEngine.InputSystem.OnScreen.OnScreenStick+Behaviour", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_UseIsolatedInputActions", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PointerDownAction", "type": "UnityEngine.InputSystem.InputAction", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PointerMoveAction", "type": "UnityEngine.InputSystem.InputAction", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.OnScreen.OnScreenStick+Behaviour", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.OnScreen.OnScreenSupport", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.HID.HID", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.HID.HIDParser", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.HID.HIDSupport", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.EnhancedTouch.EnhancedTouchSupport", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.EnhancedTouch.Finger", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.EnhancedTouch.Touch", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.EnhancedTouch.TouchHistory", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.EnhancedTouch.TouchSimulation", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.DualShock.DualShockGamepad", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.DualShock.DualShockSupport", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.DualShock.IDualShockHaptics", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Android.AndroidGamepad", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Android.AndroidGamepadWithDpadAxes", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Android.AndroidGamepadWithDpadButtons", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Android.AndroidJoystick", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Android.DualShock4GamepadAndroid", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Android.XboxOneGamepadAndroid", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Android.AndroidAccelerometer", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Android.AndroidMagneticFieldSensor", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Android.AndroidGyroscope", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Android.AndroidLightSensor", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Android.AndroidPressureSensor", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Android.AndroidProximity", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Android.AndroidGravitySensor", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Android.AndroidLinearAccelerationSensor", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Android.AndroidRotationVector", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Android.AndroidRelativeHumidity", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Android.AndroidAmbientTemperature", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Android.AndroidGameRotationVector", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Android.AndroidStepCounter", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Android.AndroidHingeAngle", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Android.AndroidSupport", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Android.LowLevel.AndroidAxis", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Android.LowLevel.AndroidGameControllerState", "fieldInfos": [{"name": "buttons", "type": "UnityEngine.InputSystem.Android.LowLevel.AndroidGameControllerState+<buttons>e__FixedBuffer", "flags": {"value__": 1}, "fixedBufferLength": 7, "fixedBufferTypename": "UInt32"}, {"name": "axis", "type": "UnityEngine.InputSystem.Android.LowLevel.AndroidGameControllerState+<axis>e__FixedBuffer", "flags": {"value__": 1}, "fixedBufferLength": 48, "fixedBufferTypename": "Single"}]}, {"className": "UnityEngine.InputSystem.Android.LowLevel.AndroidInputSource", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Android.LowLevel.AndroidDeviceCapabilities", "fieldInfos": [{"name": "deviceDescriptor", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "productId", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "vendorId", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "isVirtual", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "motionAxes", "type": "UnityEngine.InputSystem.Android.LowLevel.AndroidAxis[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "inputSources", "type": "UnityEngine.InputSystem.Android.LowLevel.AndroidInputSource", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Android.LowLevel.AndroidKeyCode", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Android.LowLevel.AndroidSensorType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Android.LowLevel.AndroidSensorCapabilities", "fieldInfos": [{"name": "sensorType", "type": "UnityEngine.InputSystem.Android.LowLevel.AndroidSensorType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Android.LowLevel.AndroidSensorState", "fieldInfos": [{"name": "data", "type": "UnityEngine.InputSystem.Android.LowLevel.AndroidSensorState+<data>e__FixedBuffer", "flags": {"value__": 1}, "fixedBufferLength": 16, "fixedBufferTypename": "Single"}]}, {"className": "UnityEngine.InputSystem.Android.LowLevel.AndroidCompensateDirectionProcessor", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Processors.CompensateDirectionProcessor", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputProcessor`1[UnityEngine.Vector3]", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Android.LowLevel.AndroidCompensateRotationProcessor", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Processors.CompensateRotationProcessor", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputProcessor`1[UnityEngine.Quaternion]", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Haptics.DualMotorRumble", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Haptics.IDualMotorRumble", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Haptics.IHaptics", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.DisableDeviceCommand", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.EnableDeviceCommand", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.EnableIMECompositionCommand", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.IInputDeviceCommandInfo", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.InitiateUserAccountPairingCommand", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.InputDeviceCommandDelegate", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.InputDeviceExecuteCommandDelegate", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.InputDeviceCommand", "fieldInfos": [{"name": "sizeInBytes", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.QueryCanRunInBackground", "fieldInfos": [{"name": "canRunInBackground", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.QueryDimensionsCommand", "fieldInfos": [{"name": "outDimensions", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.QueryEnabledStateCommand", "fieldInfos": [{"name": "isEnabled", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.QueryKeyboardLayoutCommand", "fieldInfos": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "UnityEngine.InputSystem.LowLevel.QueryKeyboardLayoutCommand+<nameBuffer>e__FixedBuffer", "flags": {"value__": 1}, "fixedBufferLength": 256, "fixedBufferTypename": "Byte"}]}, {"className": "UnityEngine.InputSystem.LowLevel.QueryKeyNameCommand", "fieldInfos": [{"name": "scanOrKeyCode", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "UnityEngine.InputSystem.LowLevel.QueryKeyNameCommand+<nameBuffer>e__FixedBuffer", "flags": {"value__": 1}, "fixedBufferLength": 256, "fixedBufferTypename": "Byte"}]}, {"className": "UnityEngine.InputSystem.LowLevel.QueryPairedUserAccountCommand", "fieldInfos": [{"name": "handle", "type": "System.UInt64", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.QuerySamplingFrequencyCommand", "fieldInfos": [{"name": "frequency", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.QueryUserIdCommand", "fieldInfos": [{"name": "idBuffer", "type": "UnityEngine.InputSystem.LowLevel.QueryUserIdCommand+<idBuffer>e__FixedBuffer", "flags": {"value__": 1}, "fixedBufferLength": 512, "fixedBufferTypename": "Byte"}]}, {"className": "UnityEngine.InputSystem.LowLevel.RequestResetCommand", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.RequestSyncCommand", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.SetIMECursorPositionCommand", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.SetSamplingFrequencyCommand", "fieldInfos": [{"name": "frequency", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.WarpMousePositionCommand", "fieldInfos": [{"name": "warpPositionInPlayerDisplaySpace", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.GamepadState", "fieldInfos": [{"name": "buttons", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "leftStick", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "rightStick", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "leftTrigger", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "rightTrigger", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.GamepadButton", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.DualMotorRumbleCommand", "fieldInfos": [{"name": "lowFrequencyMotorSpeed", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "highFrequencyMotorSpeed", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.ICustomDeviceReset", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.IEventMerger", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.IEventPreProcessor", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.IInputUpdateCallbackReceiver", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.ITextInputReceiver", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.JoystickState", "fieldInfos": [{"name": "buttons", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "stick", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.KeyboardState", "fieldInfos": [{"name": "keys", "type": "UnityEngine.InputSystem.LowLevel.KeyboardState+<keys>e__FixedBuffer", "flags": {"value__": 1}, "fixedBufferLength": 14, "fixedBufferTypename": "Byte"}]}, {"className": "UnityEngine.InputSystem.LowLevel.MouseState", "fieldInfos": [{"name": "position", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "delta", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "scroll", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "buttons", "type": "System.UInt16", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "displayIndex", "type": "System.UInt16", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "clickCount", "type": "System.UInt16", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.MouseButton", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.PenState", "fieldInfos": [{"name": "position", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "delta", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "tilt", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "pressure", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "twist", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "buttons", "type": "System.UInt16", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.PointerState", "fieldInfos": [{"name": "position", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "delta", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "pressure", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "radius", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "buttons", "type": "System.UInt16", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "displayIndex", "type": "System.UInt16", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.AccelerometerState", "fieldInfos": [{"name": "acceleration", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.GyroscopeState", "fieldInfos": [{"name": "angularVelocity", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.GravityState", "fieldInfos": [{"name": "gravity", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.AttitudeState", "fieldInfos": [{"name": "attitude", "type": "UnityEngine.Quaternion", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.LinearAccelerationState", "fieldInfos": [{"name": "acceleration", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.TouchFlags", "fieldInfos": [{"name": "value__", "type": "System.Byte", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.TouchState", "fieldInfos": [{"name": "touchId", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "position", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "delta", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "pressure", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "radius", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "phaseId", "type": "System.Byte", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "tapCount", "type": "System.Byte", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "displayIndex", "type": "System.Byte", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "flags", "type": "System.Byte", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "startTime", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "startPosition", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.TouchscreenState", "fieldInfos": [{"name": "primaryTouchData", "type": "UnityEngine.InputSystem.LowLevel.TouchscreenState+<primaryTouchData>e__FixedBuffer", "flags": {"value__": 1}, "fixedBufferLength": 56, "fixedBufferTypename": "Byte"}, {"name": "touchData", "type": "UnityEngine.InputSystem.LowLevel.TouchscreenState+<touchData>e__FixedBuffer", "flags": {"value__": 1}, "fixedBufferLength": 560, "fixedBufferTypename": "Byte"}]}, {"className": "UnityEngine.InputSystem.LowLevel.ActionEvent", "fieldInfos": [{"name": "m_ValueData", "type": "UnityEngine.InputSystem.LowLevel.ActionEvent+<m_ValueData>e__FixedBuffer", "flags": {"value__": 1}, "fixedBufferLength": 1, "fixedBufferTypename": "Byte"}]}, {"className": "UnityEngine.InputSystem.LowLevel.DeltaStateEvent", "fieldInfos": [{"name": "stateOffset", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.DeviceConfigurationEvent", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.DeviceRemoveEvent", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.DeviceResetEvent", "fieldInfos": [{"name": "hardReset", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.IInputEventTypeInfo", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.IMECompositionEvent", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.IMECompositionString", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.InputEvent", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.InputEventBuffer", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.InputEventListener", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.InputEventPtr", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.InputEventStream", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.InputEventTrace", "fieldInfos": [{"name": "m_DeviceId", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_EventBufferSize", "type": "System.Int64", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MaxEventBufferSize", "type": "System.Int64", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_GrowIncrementSize", "type": "System.Int64", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_EventCount", "type": "System.Int64", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_EventSizeInBytes", "type": "System.Int64", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_EventBufferStorage", "type": "System.UInt64", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_EventBufferHeadStorage", "type": "System.UInt64", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_EventBufferTailStorage", "type": "System.UInt64", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HasWrapped", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_RecordFrameMarkers", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DeviceInfos", "type": "UnityEngine.InputSystem.LowLevel.InputEventTrace+DeviceInfo[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.InputEventTrace+Enumerator", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.InputEventTrace+FileFlags", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.InputEventTrace+ReplayController", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.InputEventTrace+DeviceInfo", "fieldInfos": [{"name": "m_DeviceId", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Layout", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_StateSizeInBytes", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FullLayout<PERSON>son", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.StateEvent", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.TextEvent", "fieldInfos": [{"name": "character", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.InputUpdateDelegate", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.IInputRuntime", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.InputRuntime", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.InputRuntimeExtensions", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.InputMetrics", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.InputUpdateType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.InputUpdate", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.NativeInputRuntime", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.IInputStateCallbackReceiver", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.IInputStateChangeMonitor", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.IInputStateTypeInfo", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.InputState", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.InputStateBlock", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.LowLevel.InputStateBuffers", "fieldInfos": [{"name": "sizePer<PERSON>uffer", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "totalSize", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.LowLevel.InputStateHistory", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Processors.AxisDeadzoneProcessor", "fieldInfos": [{"name": "min", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "max", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputProcessor`1[System.Single]", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Processors.ClampProcessor", "fieldInfos": [{"name": "min", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "max", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Processors.InvertProcessor", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Processors.InvertVector2Processor", "fieldInfos": [{"name": "invertX", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "invertY", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputProcessor`1[UnityEngine.Vector2]", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Processors.InvertVector3Processor", "fieldInfos": [{"name": "invertX", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "invertY", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "invertZ", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Processors.NormalizeProcessor", "fieldInfos": [{"name": "min", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "max", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "zero", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Processors.NormalizeVector2Processor", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Processors.NormalizeVector3Processor", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Processors.ScaleProcessor", "fieldInfos": [{"name": "factor", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Processors.ScaleVector2Processor", "fieldInfos": [{"name": "x", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "y", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Processors.ScaleVector3Processor", "fieldInfos": [{"name": "x", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "y", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "z", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Processors.StickDeadzoneProcessor", "fieldInfos": [{"name": "min", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "max", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Layouts.InputControlAttribute", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Layouts.InputDeviceFindControlLayoutDelegate", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Layouts.InputControlLayout", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Layouts.InputControlLayoutAttribute", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Layouts.InputDeviceBuilder", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Layouts.InputDeviceDescription", "fieldInfos": [{"name": "m_InterfaceName", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DeviceClass", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Manufacturer", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Product", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Serial", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Version", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Capabilities", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Layouts.InputDeviceDescription+DeviceDescriptionJson", "fieldInfos": [{"name": "interface", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "type", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "product", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "serial", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "version", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "manufacturer", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "capabilities", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Layouts.InputDeviceMatcher", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Controls.AnyKeyControl", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Controls.ButtonControl", "fieldInfos": [{"name": "pressPoint", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Controls.AxisControl", "fieldInfos": [{"name": "clamp", "type": "UnityEngine.InputSystem.Controls.AxisControl+Clamp", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "clampMin", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "clampMax", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "clampConstant", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "invert", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "normalize", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "normalizeMin", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "normalizeMax", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "normalizeZero", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "scale", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "scaleFactor", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputControl`1[System.Single]", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Controls.DeltaControl", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Controls.Vector2Control", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputControl`1[UnityEngine.Vector2]", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Controls.DiscreteButtonControl", "fieldInfos": [{"name": "minValue", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "maxValue", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "wrapAtValue", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "nullValue", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "writeMode", "type": "UnityEngine.InputSystem.Controls.DiscreteButtonControl+WriteMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Controls.DoubleControl", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputControl`1[System.Double]", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Controls.DpadControl", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Controls.IntegerControl", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputControl`1[System.Int32]", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Controls.KeyControl", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Controls.QuaternionControl", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputControl`1[UnityEngine.Quaternion]", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Controls.StickControl", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Controls.TouchControl", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputControl`1[UnityEngine.InputSystem.LowLevel.TouchState]", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Controls.TouchPhaseControl", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputControl`1[UnityEngine.InputSystem.TouchPhase]", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Controls.TouchPressControl", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Controls.Vector3Control", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.InputControl`1[UnityEngine.Vector3]", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Interactions.HoldInteraction", "fieldInfos": [{"name": "duration", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "pressPoint", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Interactions.MultiTapInteraction", "fieldInfos": [{"name": "tapTime", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "tap<PERSON>elay", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "tapCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "pressPoint", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Interactions.PressInteraction", "fieldInfos": [{"name": "pressPoint", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "behavior", "type": "UnityEngine.InputSystem.Interactions.PressBehavior", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Interactions.PressBehavior", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Interactions.SlowTapInteraction", "fieldInfos": [{"name": "duration", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "pressPoint", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Interactions.TapInteraction", "fieldInfos": [{"name": "duration", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "pressPoint", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Utilities.InputActionTrace", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.ArrayHelpers", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.Vector2MagnitudeComparer", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.Vector3MagnitudeComparer", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.CSharpCodeHelpers", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.DelegateHelpers", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.DisplayStringFormatAttribute", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.ExceptionHelpers", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.FourCC", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.InputArrayExtensions", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.InternedString", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.JsonParser", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.MemoryHelpers", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.MiscHelpers", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.NameAndParameters", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.NamedValue", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.NumberHelpers", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.ForDeviceEventObservable", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.Observable", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.PredictiveParser", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.PrimitiveValue", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.ReadOnlyArrayExtensions", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.ISavedState", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.SpriteUtilities", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.StringHelpers", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.Substring", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.TypeHelpers", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Utilities.TypeTable", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Composites.AxisComposite", "fieldInfos": [{"name": "negative", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "positive", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "minValue", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "maxValue", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "whichSideWins", "type": "UnityEngine.InputSystem.Composites.AxisComposite+WhichSideWins", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputBindingComposite`1[System.Single]", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Composites.ButtonWithOneModifier", "fieldInfos": [{"name": "modifier", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "button", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "overrideModifiersNeedToBePressedFirst", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "modifiersOrder", "type": "UnityEngine.InputSystem.Composites.ButtonWithOneModifier+ModifiersOrder", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Composites.ButtonWithTwoModifiers", "fieldInfos": [{"name": "modifier1", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "modifier2", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "button", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "overrideModifiersNeedToBePressedFirst", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "modifiersOrder", "type": "UnityEngine.InputSystem.Composites.ButtonWithTwoModifiers+ModifiersOrder", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Composites.OneModifierComposite", "fieldInfos": [{"name": "modifier", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "binding", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "overrideModifiersNeedToBePressedFirst", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "modifiersOrder", "type": "UnityEngine.InputSystem.Composites.OneModifierComposite+ModifiersOrder", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Composites.TwoModifiersComposite", "fieldInfos": [{"name": "modifier1", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "modifier2", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "binding", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "overrideModifiersNeedToBePressedFirst", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "modifiersOrder", "type": "UnityEngine.InputSystem.Composites.TwoModifiersComposite+ModifiersOrder", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.Composites.Vector2Composite", "fieldInfos": [{"name": "up", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "down", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "left", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "right", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "normalize", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "mode", "type": "UnityEngine.InputSystem.Composites.Vector2Composite+Mode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputBindingComposite`1[UnityEngine.Vector2]", "fieldInfos": []}, {"className": "UnityEngine.InputSystem.Composites.Vector3Composite", "fieldInfos": [{"name": "up", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "down", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "left", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "right", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "forward", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "backward", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "mode", "type": "UnityEngine.InputSystem.Composites.Vector3Composite+Mode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.InputSystem.InputBindingComposite`1[UnityEngine.Vector3]", "fieldInfos": []}]}, {"name": "Unity.InputSystem.ForUI", "path": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.InputSystem.ForUI.dll", "types": [{"className": "UnityEngine.InputSystem.Plugins.InputForUI.InputSystemProvider", "fieldInfos": []}]}, {"name": "Unity.Multiplayer.Center.Common", "path": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Multiplayer.Center.Common.dll", "types": [{"className": "Unity.Multiplayer.Center.Onboarding.StyleConstants", "fieldInfos": []}, {"className": "Unity.Multiplayer.Center.Common.AnswerData", "fieldInfos": [{"name": "Answers", "type": "System.Collections.Generic.List`1[Unity.Multiplayer.Center.Common.AnsweredQuestion]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.Multiplayer.Center.Common.AnsweredQuestion", "fieldInfos": [{"name": "QuestionId", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "Answers", "type": "System.Collections.Generic.List`1[System.String]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.Multiplayer.Center.Common.OnboardingSectionAttribute", "fieldInfos": []}, {"className": "Unity.Multiplayer.Center.Common.OnboardingSectionCategory", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.Multiplayer.Center.Common.DisplayCondition", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.Multiplayer.Center.Common.InfrastructureDependency", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.Multiplayer.Center.Common.IOnboardingSection", "fieldInfos": []}, {"className": "Unity.Multiplayer.Center.Common.ISectionDependingOnUserChoices", "fieldInfos": []}, {"className": "Unity.Multiplayer.Center.Common.ISectionWithAnalytics", "fieldInfos": []}, {"className": "Unity.Multiplayer.Center.Common.Preset", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.Multiplayer.Center.Common.SelectedSolutionsData", "fieldInfos": [{"name": "SelectedHostingModel", "type": "Unity.Multiplayer.Center.Common.SelectedSolutionsData+HostingModel", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "SelectedNetcodeSolution", "type": "Unity.Multiplayer.Center.Common.SelectedSolutionsData+NetcodeSolution", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.Multiplayer.Center.Common.SelectedSolutionsData+HostingModel", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.Multiplayer.Center.Common.SelectedSolutionsData+NetcodeSolution", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.Multiplayer.Center.Common.Analytics.InteractionDataType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.Multiplayer.Center.Common.Analytics.IOnboardingSectionAnalyticsProvider", "fieldInfos": []}]}, {"name": "Unity.TextMeshPro", "path": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll", "types": [{"className": "UnityEngine.TextCore.OTL_FeatureTag", "fieldInfos": [{"name": "value__", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.FastAction", "fieldInfos": []}, {"className": "TMPro.GlyphAnchorPoint", "fieldInfos": [{"name": "m_XCoordinate", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_YCoordinate", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.MarkPositionAdjustment", "fieldInfos": [{"name": "m_XPositionAdjustment", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_YPositionAdjustment", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.MarkToBaseAdjustmentRecord", "fieldInfos": [{"name": "m_BaseGlyphID", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_BaseGlyphAnchorPoint", "type": "TMPro.GlyphAnchorPoint", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MarkGlyphID", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MarkPositionAdjustment", "type": "TMPro.MarkPositionAdjustment", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.MarkToMarkAdjustmentRecord", "fieldInfos": [{"name": "m_BaseMarkGlyphID", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_BaseMarkGlyphAnchorPoint", "type": "TMPro.GlyphAnchorPoint", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CombiningMarkGlyphID", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CombiningMarkPositionAdjustment", "type": "TMPro.MarkPositionAdjustment", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.SingleSubstitutionRecord", "fieldInfos": []}, {"className": "TMPro.MultipleSubstitutionRecord", "fieldInfos": [{"name": "m_TargetGlyphID", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SubstituteGlyphIDs", "type": "System.UInt32[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.AlternateSubstitutionRecord", "fieldInfos": []}, {"className": "TMPro.LigatureSubstitutionRecord", "fieldInfos": [{"name": "m_ComponentGlyphIDs", "type": "System.UInt32[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_LigatureGlyphID", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.ITextPreprocessor", "fieldInfos": []}, {"className": "TMPro.MaterialReferenceManager", "fieldInfos": []}, {"className": "TMPro.TMP_MaterialReference", "fieldInfos": [{"name": "material", "type": "UnityEngine.Material", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "referenceCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.MaterialReference", "fieldInfos": [{"name": "index", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "fontAsset", "type": "TMPro.TMP_FontAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "spriteAsset", "type": "TMPro.TMP_SpriteAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "material", "type": "UnityEngine.Material", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "isDefaultMaterial", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "isFallbackMaterial", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "fallbackMaterial", "type": "UnityEngine.Material", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "padding", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "referenceCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TextContainerAnchors", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TextContainer", "fieldInfos": [{"name": "m_pivot", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_anchorPosition", "type": "TMPro.TextContainerAnchors", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_rect", "type": "UnityEngine.Rect", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_margins", "type": "UnityEngine.Vector4", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TextMeshPro", "fieldInfos": [{"name": "_Sorting<PERSON>ayer", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "_SortingLayerID", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "_SortingOrder", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_hasFontAssetChanged", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_renderer", "type": "UnityEngine.Renderer", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_maskType", "type": "TMPro.MaskingTypes", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Text", "fieldInfos": [{"name": "m_text", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_isRightToLeft", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontAsset", "type": "TMPro.TMP_FontAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_sharedMaterial", "type": "UnityEngine.Material", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontSharedMaterials", "type": "UnityEngine.Material[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontMaterial", "type": "UnityEngine.Material", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontMaterials", "type": "UnityEngine.Material[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontColor32", "type": "UnityEngine.Color32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontColor", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_enableVertexGradient", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_colorMode", "type": "TMPro.ColorMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontColorGradient", "type": "TMPro.VertexGradient", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontColorGradientPreset", "type": "TMPro.TMP_ColorGradient", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_spriteAsset", "type": "TMPro.TMP_SpriteAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_tintAllSprites", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_StyleSheet", "type": "TMPro.TMP_StyleSheet", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_TextStyleHashCode", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_overrideHtmlColors", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_faceColor", "type": "UnityEngine.Color32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontSize", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontSizeBase", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontWeight", "type": "TMPro.FontWeight", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_enableAutoSizing", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontSizeMin", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontSizeMax", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontStyle", "type": "TMPro.FontStyles", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HorizontalAlignment", "type": "TMPro.HorizontalAlignmentOptions", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_VerticalAlignment", "type": "TMPro.VerticalAlignmentOptions", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_textAlignment", "type": "TMPro.TextAlignmentOptions", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_characterSpacing", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_wordSpacing", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_lineSpacing", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_lineSpacingMax", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_paragraphSpacing", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_charWidthMaxAdj", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_TextWrappingMode", "type": "TMPro.TextWrappingModes", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_wordWrappingRatios", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_overflowMode", "type": "TMPro.TextOverflowModes", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_linkedTextComponent", "type": "TMPro.TMP_Text", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "parentLinkedComponent", "type": "TMPro.TMP_Text", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_enableKerning", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ActiveFontFeatures", "type": "System.Collections.Generic.List`1[UnityEngine.TextCore.OTL_FeatureTag]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_enableExtraPadding", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "checkPaddingRequired", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_isRichText", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_EmojiFallbackSupport", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_parseCtrlCharacters", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_isOrthographic", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_isCullingEnabled", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_horizontalMapping", "type": "TMPro.TextureMappingOptions", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_verticalMapping", "type": "TMPro.TextureMappingOptions", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_uvLineOffset", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_geometrySortingOrder", "type": "TMPro.VertexSortingOrder", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_IsTextObjectScaleStatic", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_VertexBufferAutoSizeReduction", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_useMaxVisibleDescender", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_pageToDisplay", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_margin", "type": "UnityEngine.Vector4", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_isUsingLegacyAnimationComponent", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_isVolumetricText", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Text+MissingCharacterEventCallback", "fieldInfos": []}, {"className": "TMPro.TMP_Text+CharacterSubstitution", "fieldInfos": [{"name": "index", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "unicode", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Text+TextInputSources", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Text+TextProcessingElement", "fieldInfos": [{"name": "elementType", "type": "TMPro.TextProcessingElementType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "unicode", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "stringIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "length", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Text+SpecialCharacter", "fieldInfos": [{"name": "character", "type": "TMPro.TMP_Character", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "fontAsset", "type": "TMPro.TMP_FontAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "material", "type": "UnityEngine.Material", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "materialIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Text+TextBackingContainer", "fieldInfos": []}, {"className": "TMPro.TextMeshProUGUI", "fieldInfos": [{"name": "m_hasFontAssetChanged", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_baseMaterial", "type": "UnityEngine.Material", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_maskOffset", "type": "UnityEngine.Vector4", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.Compute_DistanceTransform_EventTypes", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMPro_EventManager", "fieldInfos": []}, {"className": "TMPro.Compute_DT_EventArgs", "fieldInfos": [{"name": "EventType", "type": "TMPro.Compute_DistanceTransform_EventTypes", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "ProgressPercentage", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "Colors", "type": "UnityEngine.Color[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMPro_ExtensionMethods", "fieldInfos": []}, {"className": "TMPro.TMP_Math", "fieldInfos": []}, {"className": "TMPro.TMP_VertexDataUpdateFlags", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.VertexGradient", "fieldInfos": [{"name": "topLeft", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "topRight", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "bottomLeft", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "bottomRight", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_PageInfo", "fieldInfos": [{"name": "firstCharacterIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "lastCharacterIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "ascender", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "baseLine", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "descender", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_LinkInfo", "fieldInfos": [{"name": "textComponent", "type": "TMPro.TMP_Text", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "hashCode", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "linkIdFirstCharacterIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "linkIdLength", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "linkTextfirstCharacterIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "linkTextLength", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_WordInfo", "fieldInfos": [{"name": "textComponent", "type": "TMPro.TMP_Text", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "firstCharacterIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "lastCharacterIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "characterCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_SpriteInfo", "fieldInfos": [{"name": "spriteIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "characterIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "vertexIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.Extents", "fieldInfos": [{"name": "min", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "max", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.Mesh_Extents", "fieldInfos": [{"name": "min", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "max", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.WordWrapState", "fieldInfos": [{"name": "previous_WordBreak", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "total_CharacterCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "visible_CharacterCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "visibleSpaceCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "visible_SpriteCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "visible_LinkCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "firstCharacterIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "firstVisibleCharacterIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "lastCharacterIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "lastVisibleCharIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "lineNumber", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "maxCapHeight", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "maxAscender", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "maxDescender", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "startOfLineAscender", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "maxLineAscender", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "maxLineDescender", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "pageAscender", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "horizontalAlignment", "type": "TMPro.HorizontalAlignmentOptions", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "marginLeft", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "marginRight", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "xAdvance", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "preferredWidth", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "preferredHeight", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "rendered<PERSON><PERSON><PERSON>", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "renderedHeight", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "previousLineScale", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "wordCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "fontStyle", "type": "TMPro.FontStyles", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "italicAngle", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "fontScaleMultiplier", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "currentFontSize", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "baselineOffset", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "lineOffset", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "isDrivenLineSpacing", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "lastBaseGlyphIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "cSpace", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "mSpace", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "textInfo", "type": "TMPro.TMP_TextInfo", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "vertexColor", "type": "UnityEngine.Color32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "underlineColor", "type": "UnityEngine.Color32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "strikethroughColor", "type": "UnityEngine.Color32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "spriteAnimationID", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "currentFontAsset", "type": "TMPro.TMP_FontAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "currentSpriteAsset", "type": "TMPro.TMP_SpriteAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "currentMaterial", "type": "UnityEngine.Material", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "currentMaterialIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "tagNoParsing", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "isNonBreakingSpace", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "fxRotation", "type": "UnityEngine.Quaternion", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "fxScale", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TagAttribute", "fieldInfos": [{"name": "startIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "length", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "hashCode", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.RichTextTagAttribute", "fieldInfos": [{"name": "nameHashCode", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "valueHashCode", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "valueType", "type": "TMPro.TagValueType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "valueStartIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "valueLength", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "unitType", "type": "TMPro.TagUnitType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Asset", "fieldInfos": [{"name": "m_Version", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FaceInfo", "type": "UnityEngine.TextCore.FaceInfo", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Material", "type": "UnityEngine.Material", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Character", "fieldInfos": []}, {"className": "TMPro.TMP_TextElement", "fieldInfos": [{"name": "m_ElementType", "type": "TMPro.TextElementType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Unicode", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_GlyphIndex", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Scale", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Vertex", "fieldInfos": [{"name": "position", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "uv", "type": "UnityEngine.Vector4", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "uv2", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "color", "type": "UnityEngine.Color32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Offset", "fieldInfos": []}, {"className": "TMPro.HighlightState", "fieldInfos": [{"name": "color", "type": "UnityEngine.Color32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_CharacterInfo", "fieldInfos": [{"name": "elementType", "type": "TMPro.TMP_TextElementType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "character", "type": "System.Char", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "index", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "stringLength", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "textElement", "type": "TMPro.TMP_TextElement", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "alternativeGlyph", "type": "UnityEngine.TextCore.Glyph", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "fontAsset", "type": "TMPro.TMP_FontAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "material", "type": "UnityEngine.Material", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "materialReferenceIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "isUsingAlternateTypeface", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "pointSize", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "lineNumber", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "pageNumber", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "vertexIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "topLeft", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "bottomLeft", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "topRight", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "bottomRight", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "origin", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "xAdvance", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "ascender", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "baseLine", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "descender", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "aspectRatio", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "scale", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "color", "type": "UnityEngine.Color32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "underlineColor", "type": "UnityEngine.Color32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "underlineVertexIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "strikethroughColor", "type": "UnityEngine.Color32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "strikethroughVertexIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "highlightColor", "type": "UnityEngine.Color32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "style", "type": "TMPro.FontStyles", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "isVisible", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.ColorMode", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_ColorGradient", "fieldInfos": [{"name": "colorMode", "type": "TMPro.ColorMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "topLeft", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "topRight", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "bottomLeft", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "bottomRight", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Compatibility", "fieldInfos": []}, {"className": "TMPro.ITweenValue", "fieldInfos": []}, {"className": "TMPro.ColorTween", "fieldInfos": []}, {"className": "TMPro.FloatTween", "fieldInfos": []}, {"className": "TMPro.TMP_DefaultControls", "fieldInfos": []}, {"className": "TMPro.TMP_Dropdown", "fieldInfos": [{"name": "m_Template", "type": "UnityEngine.RectTransform", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CaptionText", "type": "TMPro.TMP_Text", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CaptionImage", "type": "UnityEngine.UI.Image", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Placeholder", "type": "UnityEngine.UI.Graphic", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ItemText", "type": "TMPro.TMP_Text", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ItemImage", "type": "UnityEngine.UI.Image", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Value", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MultiSelect", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Options", "type": "TMPro.TMP_Dropdown+OptionDataList", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnValueChanged", "type": "TMPro.TMP_Dropdown+DropdownEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AlphaFadeSpeed", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Dropdown+DropdownItem", "fieldInfos": [{"name": "m_Text", "type": "TMPro.TMP_Text", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Image", "type": "UnityEngine.UI.Image", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_RectTransform", "type": "UnityEngine.RectTransform", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Toggle", "type": "UnityEngine.UI.Toggle", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Dropdown+OptionData", "fieldInfos": [{"name": "m_Text", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Image", "type": "UnityEngine.Sprite", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Color", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Dropdown+OptionDataList", "fieldInfos": [{"name": "m_Options", "type": "System.Collections.Generic.List`1[TMPro.TMP_Dropdown+OptionData]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Dropdown+DropdownEvent", "fieldInfos": []}, {"className": "TMPro.TMP_DynamicFontAssetUtilities", "fieldInfos": []}, {"className": "TMPro.AtlasPopulationMode", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_FontAsset", "fieldInfos": [{"name": "m_SourceFontFileGUID", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CreationSettings", "type": "TMPro.FontAssetCreationSettings", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SourceFontFile", "type": "UnityEngine.Font", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SourceFontFilePath", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AtlasPopulationMode", "type": "TMPro.AtlasPopulationMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "InternalDynamicOS", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_GlyphTable", "type": "System.Collections.Generic.List`1[UnityEngine.TextCore.Glyph]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CharacterTable", "type": "System.Collections.Generic.List`1[TMPro.TMP_Character]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AtlasTextures", "type": "UnityEngine.Texture2D[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AtlasTextureIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_IsMultiAtlasTexturesEnabled", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_GetFontFeatures", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ClearDynamicDataOnBuild", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AtlasWidth", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AtlasHeight", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AtlasPadding", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AtlasRenderMode", "type": "UnityEngine.TextCore.LowLevel.GlyphRenderMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_UsedGlyphRects", "type": "System.Collections.Generic.List`1[UnityEngine.TextCore.GlyphRect]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FreeGlyphRects", "type": "System.Collections.Generic.List`1[UnityEngine.TextCore.GlyphRect]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FontFeatureTable", "type": "TMPro.TMP_FontFeatureTable", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ShouldReimportFontFeatures", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FallbackFontAssetTable", "type": "System.Collections.Generic.List`1[TMPro.TMP_FontAsset]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FontWeightTable", "type": "TMPro.TMP_FontWeightPair[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "fontWeights", "type": "TMPro.TMP_FontWeightPair[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "normalStyle", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "normalSpacingOffset", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "boldStyle", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "boldSpacing", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "italicStyle", "type": "System.Byte", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "tabSize", "type": "System.Byte", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fontInfo", "type": "TMPro.FaceInfo_Legacy", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_glyphInfoList", "type": "System.Collections.Generic.List`1[TMPro.TMP_Glyph]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_KerningTable", "type": "TMPro.KerningTable", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "fallbackFontAssets", "type": "System.Collections.Generic.List`1[TMPro.TMP_FontAsset]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "atlas", "type": "UnityEngine.Texture2D", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.FaceInfo_Legacy", "fieldInfos": [{"name": "Name", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "PointSize", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "Scale", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "CharacterCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "LineHeight", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "Baseline", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "Ascender", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "CapHeight", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "Descender", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "CenterLine", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "SuperscriptOffset", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "SubscriptOffset", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "SubSize", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "Underline", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "UnderlineThickness", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "strikethrough", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "strikethroughThickness", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "Padding", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "AtlasWidth", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "AtlasHeight", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Glyph", "fieldInfos": []}, {"className": "TMPro.TMP_TextElement_Legacy", "fieldInfos": [{"name": "id", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "x", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "y", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "width", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "height", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "xOffset", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "yOffset", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "xAdvance", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "scale", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.FontAssetCreationSettings", "fieldInfos": [{"name": "sourceFontFileName", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "sourceFontFileGUID", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "faceIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "pointSizeSamplingMode", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "pointSize", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "padding", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "paddingMode", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "packingMode", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "atlasWidth", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "atlasHeight", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "characterSetSelectionMode", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "characterSequence", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "referencedFontAssetGUID", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "referencedTextAssetGUID", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "fontStyle", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "fontStyleModifier", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "renderMode", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "includeFontFeatures", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_FontWeightPair", "fieldInfos": [{"name": "regularTypeface", "type": "TMPro.TMP_FontAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "italicTypeface", "type": "TMPro.TMP_FontAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.Kern<PERSON>Pair<PERSON>ey", "fieldInfos": [{"name": "ascii_Left", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "ascii_Right", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "key", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.GlyphValueRecord_Legacy", "fieldInfos": [{"name": "xPlacement", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "yPlacement", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "xAdvance", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "yAdvance", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.KerningPair", "fieldInfos": [{"name": "m_FirstGlyph", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FirstGlyphAdjustments", "type": "TMPro.GlyphValueRecord_Legacy", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SecondGlyph", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SecondGlyphAdjustments", "type": "TMPro.GlyphValueRecord_Legacy", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "xOffset", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_IgnoreSpacingAdjustments", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.KerningTable", "fieldInfos": [{"name": "kerningPairs", "type": "System.Collections.Generic.List`1[TMPro.KerningPair]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_FontUtilities", "fieldInfos": []}, {"className": "TMPro.TMP_FontAssetUtilities", "fieldInfos": []}, {"className": "TMPro.FontFeatureLookupFlags", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_GlyphValueRecord", "fieldInfos": [{"name": "m_XPlacement", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_YPlacement", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_XAdvance", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_YAdvance", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_GlyphAdjustmentRecord", "fieldInfos": [{"name": "m_GlyphIndex", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_GlyphValueRecord", "type": "TMPro.TMP_GlyphValueRecord", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_GlyphPairAdjustmentRecord", "fieldInfos": [{"name": "m_FirstAdjustmentRecord", "type": "TMPro.TMP_GlyphAdjustmentRecord", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SecondAdjustmentRecord", "type": "TMPro.TMP_GlyphAdjustmentRecord", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FeatureLookupFlags", "type": "TMPro.FontFeatureLookupFlags", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.Glyph<PERSON>", "fieldInfos": [{"name": "firstGlyphIndex", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "secondGlyphIndex", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "key", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_FontFeatureTable", "fieldInfos": [{"name": "m_MultipleSubstitutionRecords", "type": "System.Collections.Generic.List`1[TMPro.MultipleSubstitutionRecord]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_LigatureSubstitutionRecords", "type": "System.Collections.Generic.List`1[TMPro.LigatureSubstitutionRecord]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_GlyphPairAdjustmentRecords", "type": "System.Collections.Generic.List`1[UnityEngine.TextCore.LowLevel.GlyphPairAdjustmentRecord]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MarkToBaseAdjustmentRecords", "type": "System.Collections.Generic.List`1[TMPro.MarkToBaseAdjustmentRecord]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MarkToMarkAdjustmentRecords", "type": "System.Collections.Generic.List`1[TMPro.MarkToMarkAdjustmentRecord]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_InputField", "fieldInfos": [{"name": "m_TextViewport", "type": "UnityEngine.RectTransform", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_TextComponent", "type": "TMPro.TMP_Text", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Placeholder", "type": "UnityEngine.UI.Graphic", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_VerticalScrollbar", "type": "UnityEngine.UI.Scrollbar", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_VerticalScrollbarEventHandler", "type": "TMPro.TMP_ScrollbarEventHandler", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_LayoutGroup", "type": "UnityEngine.UI.LayoutGroup", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ScrollSensitivity", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ContentType", "type": "TMPro.TMP_InputField+ContentType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_InputType", "type": "TMPro.TMP_InputField+InputType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AsteriskChar", "type": "System.Char", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_KeyboardType", "type": "UnityEngine.TouchScreenKeyboardType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_LineType", "type": "TMPro.TMP_InputField+LineType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HideMobileInput", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HideSoftKeyboard", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CharacterValidation", "type": "TMPro.TMP_InputField+CharacterValidation", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_RegexValue", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_GlobalPointSize", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CharacterLimit", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnEndEdit", "type": "TMPro.TMP_InputField+SubmitEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnSubmit", "type": "TMPro.TMP_InputField+SubmitEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnSelect", "type": "TMPro.TMP_InputField+SelectionEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnDeselect", "type": "TMPro.TMP_InputField+SelectionEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnTextSelection", "type": "TMPro.TMP_InputField+TextSelectionEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnEndTextSelection", "type": "TMPro.TMP_InputField+TextSelectionEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnValueChanged", "type": "TMPro.TMP_InputField+OnChangeEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnTouchScreenKeyboardStatusChanged", "type": "TMPro.TMP_InputField+TouchScreenKeyboardEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CaretColor", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CustomCaretColor", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SelectionColor", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Text", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CaretBlinkRate", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_<PERSON>tWidth", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ReadOnly", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_RichText", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_GlobalFontAsset", "type": "TMPro.TMP_FontAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnFocusSelectAll", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ResetOnDeActivation", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_KeepTextSelectionVisible", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_RestoreOriginalTextOnEscape", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_isRichTextEditingAllowed", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_LineLimit", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_InputValidator", "type": "TMPro.TMP_InputValidator", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ShouldActivateOnSelect", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_InputField+ContentType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_InputField+InputType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_InputField+CharacterValidation", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_InputField+LineType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_InputField+OnValidateInput", "fieldInfos": []}, {"className": "TMPro.TMP_InputField+SubmitEvent", "fieldInfos": []}, {"className": "TMPro.TMP_InputField+OnChangeEvent", "fieldInfos": []}, {"className": "TMPro.TMP_InputField+SelectionEvent", "fieldInfos": []}, {"className": "TMPro.TMP_InputField+TextSelectionEvent", "fieldInfos": []}, {"className": "TMPro.TMP_InputField+TouchScreenKeyboardEvent", "fieldInfos": []}, {"className": "TMPro.TMP_InputField+EditState", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.SetPropertyUtility", "fieldInfos": []}, {"className": "TMPro.TMP_InputValidator", "fieldInfos": []}, {"className": "TMPro.TMP_LineInfo", "fieldInfos": [{"name": "characterCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "visibleCharacterCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "spaceCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "visibleSpaceCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "wordCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "firstCharacterIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "firstVisibleCharacterIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "lastCharacterIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "lastVisibleCharacterIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "length", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "lineHeight", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "ascender", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "baseline", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "descender", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "maxAdvance", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "width", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "marginLeft", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "marginRight", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "alignment", "type": "TMPro.HorizontalAlignmentOptions", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_MaterialManager", "fieldInfos": []}, {"className": "TMPro.VertexSortingOrder", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_MeshInfo", "fieldInfos": [{"name": "mesh", "type": "UnityEngine.Mesh", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "vertexCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "vertices", "type": "UnityEngine.Vector3[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "normals", "type": "UnityEngine.Vector3[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "tangents", "type": "UnityEngine.Vector4[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "uvs0", "type": "UnityEngine.Vector4[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "uvs2", "type": "UnityEngine.Vector2[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "colors32", "type": "UnityEngine.Color32[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "triangles", "type": "System.Int32[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "material", "type": "UnityEngine.Material", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_ResourceManager", "fieldInfos": []}, {"className": "TMPro.MarkupTag", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TagValueType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TagUnitType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.CodePoint", "fieldInfos": []}, {"className": "TMPro.TMP_ScrollbarEventHandler", "fieldInfos": [{"name": "isSelected", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_SelectionCaret", "fieldInfos": []}, {"className": "TMPro.TMP_Settings", "fieldInfos": [{"name": "assetVersion", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_TextWrappingMode", "type": "TMPro.TextWrappingModes", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_enableKerning", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ActiveFontFeatures", "type": "System.Collections.Generic.List`1[UnityEngine.TextCore.OTL_FeatureTag]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_enableExtraPadding", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_enableTintAllSprites", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_enableParseEscapeCharacters", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_EnableRaycastTarget", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_GetFontFeaturesAtRuntime", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_missingGlyphCharacter", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ClearDynamicDataOnBuild", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_warningsDisabled", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_defaultFontAsset", "type": "TMPro.TMP_FontAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_defaultFontAssetPath", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_defaultFontSize", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_defaultAutoSizeMinRatio", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_defaultAutoSizeMaxRatio", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_defaultTextMeshProTextContainerSize", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_defaultTextMeshProUITextContainerSize", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_autoSizeTextContainer", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_IsTextObjectScaleStatic", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_fallbackFontAssets", "type": "System.Collections.Generic.List`1[TMPro.TMP_FontAsset]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_matchMaterialPreset", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HideSubTextObjects", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_defaultSpriteAsset", "type": "TMPro.TMP_SpriteAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_defaultSpriteAssetPath", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_enableEmojiSupport", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MissingCharacterSpriteUnicode", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_EmojiFallbackTextAssets", "type": "System.Collections.Generic.List`1[TMPro.TMP_Asset]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_defaultColorGradientPresetsPath", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_defaultStyleSheet", "type": "TMPro.TMP_StyleSheet", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_StyleSheetsResourcePath", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_leadingCharacters", "type": "UnityEngine.TextAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_following<PERSON><PERSON><PERSON><PERSON>", "type": "UnityEngine.TextAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_UseModernHangulLineBreakingRules", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Settings+LineBreakingTable", "fieldInfos": []}, {"className": "TMPro.ShaderUtilities", "fieldInfos": []}, {"className": "TMPro.TMP_Sprite", "fieldInfos": [{"name": "name", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "hashCode", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "unicode", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "pivot", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "sprite", "type": "UnityEngine.Sprite", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_SpriteAnimator", "fieldInfos": []}, {"className": "TMPro.TMP_SpriteAsset", "fieldInfos": [{"name": "spriteSheet", "type": "UnityEngine.Texture", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SpriteCharacterTable", "type": "System.Collections.Generic.List`1[TMPro.TMP_SpriteCharacter]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_GlyphTable", "type": "System.Collections.Generic.List`1[TMPro.TMP_SpriteGlyph]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "spriteInfoList", "type": "System.Collections.Generic.List`1[TMPro.TMP_Sprite]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "fallbackSpriteAssets", "type": "System.Collections.Generic.List`1[TMPro.TMP_SpriteAsset]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_SpriteCharacter", "fieldInfos": [{"name": "m_Name", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_SpriteGlyph", "fieldInfos": [{"name": "sprite", "type": "UnityEngine.Sprite", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_Style", "fieldInfos": [{"name": "m_Name", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HashCode", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OpeningDefinition", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ClosingDefinition", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OpeningTagArray", "type": "System.UInt32[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ClosingTagArray", "type": "System.UInt32[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_StyleSheet", "fieldInfos": [{"name": "m_StyleList", "type": "System.Collections.Generic.List`1[TMPro.TMP_Style]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_SubMesh", "fieldInfos": [{"name": "m_fontAsset", "type": "TMPro.TMP_FontAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_spriteAsset", "type": "TMPro.TMP_SpriteAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_material", "type": "UnityEngine.Material", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_sharedMaterial", "type": "UnityEngine.Material", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_isDefaultMaterial", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_padding", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_renderer", "type": "UnityEngine.Renderer", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_TextComponent", "type": "TMPro.TextMeshPro", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_SubMeshUI", "fieldInfos": [{"name": "m_fontAsset", "type": "TMPro.TMP_FontAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_spriteAsset", "type": "TMPro.TMP_SpriteAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_material", "type": "UnityEngine.Material", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_sharedMaterial", "type": "UnityEngine.Material", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_isDefaultMaterial", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_padding", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_TextComponent", "type": "TMPro.TextMeshProUGUI", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_materialReferenceIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.ITextElement", "fieldInfos": []}, {"className": "TMPro.TextAlignmentOptions", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.HorizontalAlignmentOptions", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.VerticalAlignmentOptions", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TextRenderFlags", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_TextElementType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.MaskingTypes", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TextOverflowModes", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TextWrappingModes", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.MaskingOffsetMode", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TextureMappingOptions", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.FontStyles", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.FontWeight", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TextElementType", "fieldInfos": [{"name": "value__", "type": "System.Byte", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_TextInfo", "fieldInfos": [{"name": "textComponent", "type": "TMPro.TMP_Text", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "characterCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "spriteCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "spaceCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "wordCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "linkCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "lineCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "pageCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "materialCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_TextParsingUtilities", "fieldInfos": []}, {"className": "TMPro.TextProcessingElementType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.CharacterElement", "fieldInfos": []}, {"className": "TMPro.MarkupAttribute", "fieldInfos": []}, {"className": "TMPro.MarkupElement", "fieldInfos": []}, {"className": "TMPro.TextProcessingElement", "fieldInfos": []}, {"className": "TMPro.TMP_FontStyleStack", "fieldInfos": [{"name": "bold", "type": "System.Byte", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "italic", "type": "System.Byte", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "underline", "type": "System.Byte", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "strikethrough", "type": "System.Byte", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "highlight", "type": "System.Byte", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "superscript", "type": "System.Byte", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "subscript", "type": "System.Byte", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "uppercase", "type": "System.Byte", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "lowercase", "type": "System.Byte", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "smallcaps", "type": "System.Byte", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.CaretPosition", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.CaretInfo", "fieldInfos": [{"name": "index", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "position", "type": "TMPro.CaretPosition", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.TMP_TextUtilities", "fieldInfos": []}, {"className": "TMPro.TMP_UpdateManager", "fieldInfos": []}, {"className": "TMPro.TMP_UpdateRegistry", "fieldInfos": []}, {"className": "TMPro.SpriteAssetUtilities.SpriteAssetImportFormats", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "TMPro.SpriteAssetUtilities.TexturePacker_JsonArray", "fieldInfos": []}]}, {"name": "Unity.Timeline", "path": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll", "types": [{"className": "UnityEngine.Timeline.ActivationMixerPlayable", "fieldInfos": []}, {"className": "UnityEngine.Timeline.ActivationPlayableAsset", "fieldInfos": []}, {"className": "UnityEngine.Timeline.ActivationTrack", "fieldInfos": [{"name": "m_PostPlaybackState", "type": "UnityEngine.Timeline.ActivationTrack+PostPlaybackState", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.ActivationTrack+PostPlaybackState", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.TrackAsset", "fieldInfos": [{"name": "m_Version", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_<PERSON>im<PERSON><PERSON>", "type": "UnityEngine.AnimationClip", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Locked", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Muted", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CustomPlayableFullTypename", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Curves", "type": "UnityEngine.AnimationClip", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Parent", "type": "UnityEngine.Playables.PlayableAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Children", "type": "System.Collections.Generic.List`1[UnityEngine.ScriptableObject]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Clips", "type": "System.Collections.Generic.List`1[UnityEngine.Timeline.TimelineClip]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_<PERSON><PERSON>", "type": "UnityEngine.Timeline.MarkerList", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.TrackAsset+Versions", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.TrackAsset+TrackAssetUpgrade", "fieldInfos": []}, {"className": "UnityEngine.Timeline.TrackAsset+TransientBuildData", "fieldInfos": [{"name": "trackList", "type": "System.Collections.Generic.List`1[UnityEngine.Timeline.TrackAsset]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "clipList", "type": "System.Collections.Generic.List`1[UnityEngine.Timeline.TimelineClip]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.AnimationOutputWeightProcessor", "fieldInfos": []}, {"className": "UnityEngine.Timeline.AnimationPlayableAsset", "fieldInfos": [{"name": "m_Clip", "type": "UnityEngine.AnimationClip", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Position", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_EulerAngles", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_UseTrackMatchFields", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_<PERSON><PERSON><PERSON><PERSON><PERSON>ields", "type": "UnityEngine.Timeline.MatchTargetFields", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_RemoveStartOffset", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ApplyFootIK", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Loop", "type": "UnityEngine.Timeline.AnimationPlayableAsset+LoopMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Version", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Rotation", "type": "UnityEngine.Quaternion", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.AnimationPlayableAsset+LoopMode", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.AnimationPlayableAsset+Versions", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.AnimationPlayableAsset+AnimationPlayableAssetUpgrade", "fieldInfos": []}, {"className": "UnityEngine.Timeline.AnimationPreviewUpdateCallback", "fieldInfos": []}, {"className": "UnityEngine.Timeline.MatchTargetFields", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.TrackOffset", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.AppliedOffsetMode", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.MatchTargetFieldConstants", "fieldInfos": []}, {"className": "UnityEngine.Timeline.AnimationTrack", "fieldInfos": [{"name": "m_InfiniteClipPreExtrapolation", "type": "UnityEngine.Timeline.TimelineClip+ClipExtrapolation", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_InfiniteClipPostExtrapolation", "type": "UnityEngine.Timeline.TimelineClip+ClipExtrapolation", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_InfiniteClipOffsetPosition", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_InfiniteClipOffsetEulerAngles", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_InfiniteClipTimeOffset", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_InfiniteClipRemoveOffset", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_InfiniteClipApplyFootIK", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "mInfiniteClipLoop", "type": "UnityEngine.Timeline.AnimationPlayableAsset+LoopMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_<PERSON><PERSON><PERSON><PERSON><PERSON>ields", "type": "UnityEngine.Timeline.MatchTargetFields", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Position", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_EulerAngles", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AvatarMask", "type": "UnityEngine.AvatarMask", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ApplyAvatarMask", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_TrackOffset", "type": "UnityEngine.Timeline.TrackOffset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_InfiniteClip", "type": "UnityEngine.AnimationClip", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OpenClipOffsetRotation", "type": "UnityEngine.Quaternion", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Rotation", "type": "UnityEngine.Quaternion", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ApplyOffsets", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.AnimationTrack+AnimationTrackUpgrade", "fieldInfos": []}, {"className": "UnityEngine.Timeline.ICurvesOwner", "fieldInfos": []}, {"className": "UnityEngine.Timeline.TimelineClip", "fieldInfos": [{"name": "m_Version", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Start", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ClipIn", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Asset", "type": "UnityEngine.Object", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Duration", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_TimeScale", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ParentTrack", "type": "UnityEngine.Timeline.TrackAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_EaseInDuration", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_EaseOutDuration", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_BlendInDuration", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_BlendOutDuration", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MixInCurve", "type": "UnityEngine.AnimationCurve", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MixOutCurve", "type": "UnityEngine.AnimationCurve", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_BlendInCurveMode", "type": "UnityEngine.Timeline.TimelineClip+BlendCurveMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_BlendOutCurveMode", "type": "UnityEngine.Timeline.TimelineClip+BlendCurveMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ExposedParameterNames", "type": "System.Collections.Generic.List`1[System.String]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AnimationCurves", "type": "UnityEngine.AnimationClip", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Recordable", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PostExtrapolationMode", "type": "UnityEngine.Timeline.TimelineClip+ClipExtrapolation", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PreExtrapolationMode", "type": "UnityEngine.Timeline.TimelineClip+ClipExtrapolation", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PostExtrapolationTime", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PreExtrapolationTime", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DisplayName", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.TimelineClip+Versions", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.TimelineClip+TimelineClipUpgrade", "fieldInfos": []}, {"className": "UnityEngine.Timeline.TimelineClip+ClipExtrapolation", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.TimelineClip+BlendCurveMode", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.TimelineAsset", "fieldInfos": [{"name": "m_Version", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Tracks", "type": "System.Collections.Generic.List`1[UnityEngine.ScriptableObject]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FixedDuration", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_EditorSettings", "type": "UnityEngine.Timeline.TimelineAsset+EditorSettings", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DurationMode", "type": "UnityEngine.Timeline.TimelineAsset+DurationMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MarkerTrack", "type": "UnityEngine.Timeline.MarkerTrack", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.TimelineAsset+Versions", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.TimelineAsset+TimelineAssetUpgrade", "fieldInfos": []}, {"className": "UnityEngine.Timeline.TimelineAsset+MediaType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.TimelineAsset+DurationMode", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.TimelineAsset+EditorSettings", "fieldInfos": [{"name": "m_Framerate", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ScenePreview", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.TimelineHelpURLAttribute", "fieldInfos": []}, {"className": "UnityEngine.Timeline.TrackColorAttribute", "fieldInfos": []}, {"className": "UnityEngine.Timeline.AudioClipProperties", "fieldInfos": [{"name": "volume", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.AudioMixerProperties", "fieldInfos": [{"name": "volume", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "stereoPan", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "spatialBlend", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.AudioPlayableAsset", "fieldInfos": [{"name": "m_Clip", "type": "UnityEngine.AudioClip", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Loop", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_bufferingTime", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ClipProperties", "type": "UnityEngine.Timeline.AudioClipProperties", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.AudioTrack", "fieldInfos": [{"name": "m_TrackProperties", "type": "UnityEngine.Timeline.AudioMixerProperties", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.ClipCaps", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.TimelineClipCapsExtensions", "fieldInfos": []}, {"className": "UnityEngine.Timeline.ControlPlayableAsset", "fieldInfos": [{"name": "sourceGameObject", "type": "UnityEngine.ExposedReference`1[UnityEngine.GameObject]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "prefabGameObject", "type": "UnityEngine.GameObject", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "updateParticle", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "particleRandomSeed", "type": "System.UInt32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "updateDirector", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "updateITimeControl", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "searchHierarchy", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "active", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "postPlayback", "type": "UnityEngine.Timeline.ActivationControlPlayable+PostPlaybackState", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "directorOnClipEnd", "type": "UnityEngine.Timeline.DirectorControlPlayable+PauseAction", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.ControlTrack", "fieldInfos": []}, {"className": "UnityEngine.Timeline.DiscreteTime", "fieldInfos": []}, {"className": "UnityEngine.Timeline.InfiniteRuntimeClip", "fieldInfos": []}, {"className": "UnityEngine.Timeline.RuntimeElement", "fieldInfos": []}, {"className": "UnityEngine.Timeline.IInterval", "fieldInfos": []}, {"className": "UnityEngine.Timeline.IntervalTreeNode", "fieldInfos": [{"name": "center", "type": "System.Int64", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "first", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "last", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "left", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "right", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.RuntimeClip", "fieldInfos": []}, {"className": "UnityEngine.Timeline.RuntimeClipBase", "fieldInfos": []}, {"className": "UnityEngine.Timeline.ScheduleRuntimeClip", "fieldInfos": []}, {"className": "UnityEngine.Timeline.IMarker", "fieldInfos": []}, {"className": "UnityEngine.Timeline.INotificationOptionProvider", "fieldInfos": []}, {"className": "UnityEngine.Timeline.Marker", "fieldInfos": [{"name": "m_Time", "type": "System.Double", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.MarkerList", "fieldInfos": [{"name": "m_Objects", "type": "System.Collections.Generic.List`1[UnityEngine.ScriptableObject]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.MarkerTrack", "fieldInfos": []}, {"className": "UnityEngine.Timeline.CustomSignalEventDrawer", "fieldInfos": []}, {"className": "UnityEngine.Timeline.SignalAsset", "fieldInfos": []}, {"className": "UnityEngine.Timeline.SignalEmitter", "fieldInfos": [{"name": "m_Retroactive", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_EmitOnce", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Asset", "type": "UnityEngine.Timeline.SignalAsset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.SignalReceiver", "fieldInfos": [{"name": "m_Events", "type": "UnityEngine.Timeline.SignalReceiver+EventKeyValue", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.SignalReceiver+EventKeyValue", "fieldInfos": [{"name": "m_Signals", "type": "System.Collections.Generic.List`1[UnityEngine.Timeline.SignalAsset]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Events", "type": "System.Collections.Generic.List`1[UnityEngine.Events.UnityEvent]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.SignalTrack", "fieldInfos": []}, {"className": "UnityEngine.Timeline.TrackAssetExtensions", "fieldInfos": []}, {"className": "UnityEngine.Timeline.GroupTrack", "fieldInfos": []}, {"className": "UnityEngine.Timeline.ILayerable", "fieldInfos": []}, {"className": "UnityEngine.Timeline.ActivationControlPlayable", "fieldInfos": [{"name": "gameObject", "type": "UnityEngine.GameObject", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "postPlayback", "type": "UnityEngine.Timeline.ActivationControlPlayable+PostPlaybackState", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.BasicPlayableBehaviour", "fieldInfos": []}, {"className": "UnityEngine.Timeline.DirectorControlPlayable", "fieldInfos": [{"name": "director", "type": "UnityEngine.Playables.PlayableDirector", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "pauseAction", "type": "UnityEngine.Timeline.DirectorControlPlayable+PauseAction", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.ITimeControl", "fieldInfos": []}, {"className": "UnityEngine.Timeline.NotificationFlags", "fieldInfos": [{"name": "value__", "type": "System.Int16", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.ParticleControlPlayable", "fieldInfos": []}, {"className": "UnityEngine.Timeline.PrefabControlPlayable", "fieldInfos": []}, {"className": "UnityEngine.Timeline.TimeControlPlayable", "fieldInfos": []}, {"className": "UnityEngine.Timeline.TimeNotificationBehaviour", "fieldInfos": []}, {"className": "UnityEngine.Timeline.PlayableTrack", "fieldInfos": []}, {"className": "UnityEngine.Timeline.TrackMediaType", "fieldInfos": []}, {"className": "UnityEngine.Timeline.TrackClipTypeAttribute", "fieldInfos": []}, {"className": "UnityEngine.Timeline.NotKeyableAttribute", "fieldInfos": []}, {"className": "UnityEngine.Timeline.TrackBindingFlags", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.TrackBindingTypeAttribute", "fieldInfos": []}, {"className": "UnityEngine.Timeline.SupportsChildTracksAttribute", "fieldInfos": []}, {"className": "UnityEngine.Timeline.IgnoreOnPlayableTrackAttribute", "fieldInfos": []}, {"className": "UnityEngine.Timeline.TimeFieldAttribute", "fieldInfos": []}, {"className": "UnityEngine.Timeline.FrameRateFieldAttribute", "fieldInfos": []}, {"className": "UnityEngine.Timeline.HideInMenuAttribute", "fieldInfos": []}, {"className": "UnityEngine.Timeline.CustomStyleAttribute", "fieldInfos": []}, {"className": "UnityEngine.Timeline.MenuCategoryAttribute", "fieldInfos": []}, {"className": "UnityEngine.Timeline.ITimelineClipAsset", "fieldInfos": []}, {"className": "UnityEngine.Timeline.ITimelineEvaluateCallback", "fieldInfos": []}, {"className": "UnityEngine.Timeline.TimelinePlayable", "fieldInfos": []}, {"className": "UnityEngine.Timeline.Extrapolation", "fieldInfos": []}, {"className": "UnityEngine.Timeline.HashUtility", "fieldInfos": []}, {"className": "UnityEngine.Timeline.IPropertyCollector", "fieldInfos": []}, {"className": "UnityEngine.Timeline.IPropertyPreview", "fieldInfos": []}, {"className": "UnityEngine.Timeline.NotificationUtilities", "fieldInfos": []}, {"className": "UnityEngine.Timeline.TimelineClipExtensions", "fieldInfos": []}, {"className": "UnityEngine.Timeline.TimelineCreateUtilities", "fieldInfos": []}, {"className": "UnityEngine.Timeline.TimelineUndo", "fieldInfos": []}, {"className": "UnityEngine.Timeline.StandardFrameRates", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.Timeline.TimeUtility", "fieldInfos": []}, {"className": "UnityEngine.Timeline.WeightUtility", "fieldInfos": []}]}, {"name": "Unity.VisualScripting.Core", "path": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll", "types": [{"className": "Unity.VisualScripting.VisualScriptingHelpURLAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.AnimationCurveCloner", "fieldInfos": []}, {"className": "Unity.VisualScripting.Cloner`1[UnityEngine.AnimationCurve]", "fieldInfos": []}, {"className": "Unity.VisualScripting.ArrayCloner", "fieldInfos": []}, {"className": "Unity.VisualScripting.Cloner`1[System.Array]", "fieldInfos": []}, {"className": "Unity.VisualScripting.DictionaryCloner", "fieldInfos": []}, {"className": "Unity.VisualScripting.Cloner`1[System.Collections.IDictionary]", "fieldInfos": []}, {"className": "Unity.VisualScripting.EnumerableCloner", "fieldInfos": []}, {"className": "Unity.VisualScripting.Cloner`1[System.Collections.IEnumerable]", "fieldInfos": []}, {"className": "Unity.VisualScripting.FakeSerializationCloner", "fieldInfos": []}, {"className": "Unity.VisualScripting.ReflectedCloner", "fieldInfos": []}, {"className": "Unity.VisualScripting.Cloner`1[System.Object]", "fieldInfos": []}, {"className": "Unity.VisualScripting.FieldsCloner", "fieldInfos": []}, {"className": "Unity.VisualScripting.GradientCloner", "fieldInfos": []}, {"className": "Unity.VisualScripting.Cloner`1[UnityEngine.Gradient]", "fieldInfos": []}, {"className": "Unity.VisualScripting.ListCloner", "fieldInfos": []}, {"className": "Unity.VisualScripting.Cloner`1[System.Collections.IList]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Cloning", "fieldInfos": []}, {"className": "Unity.VisualScripting.CloningContext", "fieldInfos": []}, {"className": "Unity.VisualScripting.ICloner", "fieldInfos": []}, {"className": "Unity.VisualScripting.ISpecifiesCloner", "fieldInfos": []}, {"className": "Unity.VisualScripting.AotDictionary", "fieldInfos": []}, {"className": "Unity.VisualScripting.AotList", "fieldInfos": []}, {"className": "Unity.VisualScripting.INotifiedCollectionItem", "fieldInfos": []}, {"className": "Unity.VisualScripting.InvalidConnectionException", "fieldInfos": []}, {"className": "Unity.VisualScripting.IDecoratorAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.ValueAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.AllowsNullAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.DisableAnnotationAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.EditorBindingUtility", "fieldInfos": []}, {"className": "Unity.VisualScripting.EditorTimeBinding", "fieldInfos": []}, {"className": "Unity.VisualScripting.ExpectedTypeAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.IInspectableAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.IncludeInSettingsAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.InspectableAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.InspectableIfAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.InspectorActionDirectionAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.InspectorAdaptiveWidthAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.InspectorDelayedAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.InspectorExpandTooltipAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.InspectorLabelAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.InspectorRangeAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.InspectorTextAreaAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.InspectorToggleLeftAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.InspectorWideAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.InspectViaImplementationsAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.NullMeansSelfAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.PredictableAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.TypeIconAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.TypeIconPriorityAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.TypeSet", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.TypeSetAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.WarnBeforeEditingAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.WarnBeforeRemovingAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.Ensure", "fieldInfos": []}, {"className": "Unity.VisualScripting.EnsureThat", "fieldInfos": []}, {"className": "Unity.VisualScripting.ExceptionMessages", "fieldInfos": []}, {"className": "Unity.VisualScripting.XComparable", "fieldInfos": []}, {"className": "Unity.VisualScripting.XString", "fieldInfos": []}, {"className": "Unity.VisualScripting.EmptyEventArgs", "fieldInfos": []}, {"className": "Unity.VisualScripting.EventBus", "fieldInfos": []}, {"className": "Unity.VisualScripting.EventHook", "fieldInfos": []}, {"className": "Unity.VisualScripting.EventHookComparer", "fieldInfos": []}, {"className": "Unity.VisualScripting.EventHooks", "fieldInfos": []}, {"className": "Unity.VisualScripting.XEventGraph", "fieldInfos": []}, {"className": "Unity.VisualScripting.IEventMachine", "fieldInfos": []}, {"className": "Unity.VisualScripting.DebugUtility", "fieldInfos": []}, {"className": "Unity.VisualScripting.InvalidConversionException", "fieldInfos": []}, {"className": "Unity.VisualScripting.InvalidImplementationException", "fieldInfos": []}, {"className": "Unity.VisualScripting.Graph", "fieldInfos": []}, {"className": "Unity.VisualScripting.GraphDebugData", "fieldInfos": []}, {"className": "Unity.VisualScripting.GraphInstances", "fieldInfos": []}, {"className": "Unity.VisualScripting.GraphPointer", "fieldInfos": []}, {"className": "Unity.VisualScripting.GraphPointerException", "fieldInfos": []}, {"className": "Unity.VisualScripting.GraphReference", "fieldInfos": []}, {"className": "Unity.VisualScripting.GraphsExceptionUtility", "fieldInfos": []}, {"className": "Unity.VisualScripting.GraphSource", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.GraphStack", "fieldInfos": []}, {"className": "Unity.VisualScripting.IGraph", "fieldInfos": []}, {"className": "Unity.VisualScripting.IGraphData", "fieldInfos": []}, {"className": "Unity.VisualScripting.IGraphDebugData", "fieldInfos": []}, {"className": "Unity.VisualScripting.IGraphElement", "fieldInfos": []}, {"className": "Unity.VisualScripting.IGraphElementData", "fieldInfos": []}, {"className": "Unity.VisualScripting.IGraphElementDebugData", "fieldInfos": []}, {"className": "Unity.VisualScripting.IGraphElementWithData", "fieldInfos": []}, {"className": "Unity.VisualScripting.IGraphElementWithDebugData", "fieldInfos": []}, {"className": "Unity.VisualScripting.IGraphItem", "fieldInfos": []}, {"className": "Unity.VisualScripting.IGraphNest", "fieldInfos": []}, {"className": "Unity.VisualScripting.IGraphNester", "fieldInfos": []}, {"className": "Unity.VisualScripting.IGraphNesterElement", "fieldInfos": []}, {"className": "Unity.VisualScripting.IGraphParent", "fieldInfos": []}, {"className": "Unity.VisualScripting.IGraphParentElement", "fieldInfos": []}, {"className": "Unity.VisualScripting.IGraphRoot", "fieldInfos": []}, {"className": "Unity.VisualScripting.MergedGraphElementCollection", "fieldInfos": []}, {"className": "Unity.VisualScripting.MergedKeyedCollection`2[System.Guid,Unity.VisualScripting.IGraphElement]", "fieldInfos": []}, {"className": "Unity.VisualScripting.GraphGroup", "fieldInfos": []}, {"className": "Unity.VisualScripting.GraphElement`1[Unity.VisualScripting.IGraph]", "fieldInfos": []}, {"className": "Unity.VisualScripting.MouseButton", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.PressState", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.AnimatorMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.GlobalMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.IGraphEventListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.XGraphEventListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.IGraphEventListenerData", "fieldInfos": []}, {"className": "Unity.VisualScripting.MessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnBecameInvisibleMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnBecameVisibleMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnCollisionEnter2DMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnCollisionEnterMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnCollisionExit2DMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnCollisionExitMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnCollisionStay2DMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnCollisionStayMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnControllerColliderHitMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnJointBreak2DMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnJointBreakMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnMouseDownMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnMouseDragMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnMouseEnterMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnMouseExitMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnMouseOverMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnMouseUpAsButtonMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnMouseUpMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnParticleCollisionMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnTransformChildrenChangedMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnTransformParentChangedMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnTriggerEnter2DMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnTriggerEnterMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnTriggerExit2DMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnTriggerExitMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnTriggerStay2DMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnTriggerStayMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnButtonClickMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnDropdownValueChangedMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnInputFieldEndEditMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnInputFieldValueChangedMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnScrollbarValueChangedMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnScrollRectValueChangedMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnSliderValueChangedMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnToggleValueChangedMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnBeginDragMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnCancelMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnDeselectMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnDragMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnDropMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnEndDragMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnMoveMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnPointerClickMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnPointerDownMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnPointerEnterMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnPointerExitMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnPointerUpMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnScrollMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnSelectMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityOnSubmitMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityMessageListener", "fieldInfos": []}, {"className": "Unity.VisualScripting.IMachine", "fieldInfos": []}, {"className": "Unity.VisualScripting.IMacro", "fieldInfos": []}, {"className": "Unity.VisualScripting.AotIncompatibleAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.IAotStubbable", "fieldInfos": []}, {"className": "Unity.VisualScripting.PlatformUtility", "fieldInfos": []}, {"className": "Unity.VisualScripting.XArrayPool", "fieldInfos": []}, {"className": "Unity.VisualScripting.XHashSetPool", "fieldInfos": []}, {"className": "Unity.VisualScripting.IPoolable", "fieldInfos": []}, {"className": "Unity.VisualScripting.XListPool", "fieldInfos": []}, {"className": "Unity.VisualScripting.ProfiledSegment", "fieldInfos": []}, {"className": "Unity.VisualScripting.ProfiledSegmentCollection", "fieldInfos": []}, {"className": "Unity.VisualScripting.ProfilingScope", "fieldInfos": []}, {"className": "Unity.VisualScripting.ProfilingUtility", "fieldInfos": []}, {"className": "Unity.VisualScripting.ActionDirection", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.AttributeUtility", "fieldInfos": []}, {"className": "Unity.VisualScripting.ConversionUtility", "fieldInfos": []}, {"className": "Unity.VisualScripting.GenericClosingException", "fieldInfos": []}, {"className": "Unity.VisualScripting.IAttributeProvider", "fieldInfos": []}, {"className": "Unity.VisualScripting.IPrewarmable", "fieldInfos": []}, {"className": "Unity.VisualScripting.LooseAssemblyName", "fieldInfos": []}, {"className": "Unity.VisualScripting.Member", "fieldInfos": []}, {"className": "Unity.VisualScripting.MemberFilter", "fieldInfos": []}, {"className": "Unity.VisualScripting.MemberInfoComparer", "fieldInfos": []}, {"className": "Unity.VisualScripting.MemberUtility", "fieldInfos": []}, {"className": "Unity.VisualScripting.ExtensionMethodCache", "fieldInfos": []}, {"className": "Unity.VisualScripting.Namespace", "fieldInfos": []}, {"className": "Unity.VisualScripting.AdditionHandler", "fieldInfos": []}, {"className": "Unity.VisualScripting.BinaryOperatorHandler", "fieldInfos": []}, {"className": "Unity.VisualScripting.OperatorHandler", "fieldInfos": []}, {"className": "Unity.VisualScripting.AmbiguousOperatorException", "fieldInfos": []}, {"className": "Unity.VisualScripting.OperatorException", "fieldInfos": []}, {"className": "Unity.VisualScripting.AndHandler", "fieldInfos": []}, {"className": "Unity.VisualScripting.BinaryOperator", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.DecrementHandler", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnaryOperatorHandler", "fieldInfos": []}, {"className": "Unity.VisualScripting.DivisionHandler", "fieldInfos": []}, {"className": "Unity.VisualScripting.EqualityHandler", "fieldInfos": []}, {"className": "Unity.VisualScripting.ExclusiveOrHandler", "fieldInfos": []}, {"className": "Unity.VisualScripting.GreaterThanHandler", "fieldInfos": []}, {"className": "Unity.VisualScripting.GreaterThanOrEqualHandler", "fieldInfos": []}, {"className": "Unity.VisualScripting.IncrementHandler", "fieldInfos": []}, {"className": "Unity.VisualScripting.InequalityHandler", "fieldInfos": []}, {"className": "Unity.VisualScripting.InvalidOperatorException", "fieldInfos": []}, {"className": "Unity.VisualScripting.LeftShiftHandler", "fieldInfos": []}, {"className": "Unity.VisualScripting.LessThanHandler", "fieldInfos": []}, {"className": "Unity.VisualScripting.LessThanOrEqualHandler", "fieldInfos": []}, {"className": "Unity.VisualScripting.LogicalNegationHandler", "fieldInfos": []}, {"className": "Unity.VisualScripting.ModuloHandler", "fieldInfos": []}, {"className": "Unity.VisualScripting.MultiplicationHandler", "fieldInfos": []}, {"className": "Unity.VisualScripting.NumericNegationHandler", "fieldInfos": []}, {"className": "Unity.VisualScripting.OperatorUtility", "fieldInfos": []}, {"className": "Unity.VisualScripting.OrHandler", "fieldInfos": []}, {"className": "Unity.VisualScripting.PlusHandler", "fieldInfos": []}, {"className": "Unity.VisualScripting.RightShiftHandler", "fieldInfos": []}, {"className": "Unity.VisualScripting.SubtractionHandler", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnaryOperator", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.InvokerBase", "fieldInfos": []}, {"className": "Unity.VisualScripting.IOptimizedAccessor", "fieldInfos": []}, {"className": "Unity.VisualScripting.IOptimizedInvoker", "fieldInfos": []}, {"className": "Unity.VisualScripting.OptimizedReflection", "fieldInfos": []}, {"className": "Unity.VisualScripting.ReflectionFieldAccessor", "fieldInfos": []}, {"className": "Unity.VisualScripting.ReflectionInvoker", "fieldInfos": []}, {"className": "Unity.VisualScripting.ReflectionPropertyAccessor", "fieldInfos": []}, {"className": "Unity.VisualScripting.StaticActionInvokerBase", "fieldInfos": []}, {"className": "Unity.VisualScripting.StaticInvokerBase", "fieldInfos": []}, {"className": "Unity.VisualScripting.StaticActionInvoker", "fieldInfos": []}, {"className": "Unity.VisualScripting.RenamedAssemblyAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.RenamedFromAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.RenamedNamespaceAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.RuntimeCodebase", "fieldInfos": []}, {"className": "Unity.VisualScripting.TypeFilter", "fieldInfos": []}, {"className": "Unity.VisualScripting.TypeName", "fieldInfos": []}, {"className": "Unity.VisualScripting.TypeNameDetail", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.TypeQualifier", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.TypesMatching", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.TypeUtility", "fieldInfos": []}, {"className": "Unity.VisualScripting.LooseAssemblyNameConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsDirectConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsBaseConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.NamespaceConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.Ray2DConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsDirectConverter`1[UnityEngine.Ray2D]", "fieldInfos": []}, {"className": "Unity.VisualScripting.RayConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsDirectConverter`1[UnityEngine.Ray]", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityObjectConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.DictionaryAsset", "fieldInfos": []}, {"className": "Unity.VisualScripting.LudiqScriptableObject", "fieldInfos": [{"name": "_data", "type": "Unity.VisualScripting.SerializationData", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.DoNotSerializeAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.ISerializationDependency", "fieldInfos": []}, {"className": "Unity.VisualScripting.ISerializationDepender", "fieldInfos": []}, {"className": "Unity.VisualScripting.SerializableType", "fieldInfos": [{"name": "Identification", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.Unknown", "fieldInfos": []}, {"className": "Unity.VisualScripting.Serialization", "fieldInfos": []}, {"className": "Unity.VisualScripting.SerializationData", "fieldInfos": [{"name": "_json", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "_objectReferences", "type": "UnityEngine.Object[]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.SerializationOperation", "fieldInfos": []}, {"className": "Unity.VisualScripting.SerializationVersionAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsObjectAttribute", "fieldInfos": [{"name": "VersionString", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "MemberSerialization", "type": "Unity.VisualScripting.FullSerializer.fsMemberSerialization", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.SerializeAsAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsPropertyAttribute", "fieldInfos": [{"name": "Name", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.SerializeAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.ISerializedPropertyProvider", "fieldInfos": []}, {"className": "Unity.VisualScripting.SerializedPropertyProviderAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.StickyNote", "fieldInfos": []}, {"className": "Unity.VisualScripting.IGizmoDrawer", "fieldInfos": []}, {"className": "Unity.VisualScripting.ISingleton", "fieldInfos": []}, {"className": "Unity.VisualScripting.IUnityObjectOwnable", "fieldInfos": []}, {"className": "Unity.VisualScripting.LudiqBehaviour", "fieldInfos": [{"name": "_data", "type": "Unity.VisualScripting.SerializationData", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.MacroScriptableObject", "fieldInfos": []}, {"className": "Unity.VisualScripting.RequiresUnityAPIAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.SingletonAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityObjectOwnershipUtility", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityThread", "fieldInfos": []}, {"className": "Unity.VisualScripting.ComponentHolderProtocol", "fieldInfos": []}, {"className": "Unity.VisualScripting.CoroutineRunner", "fieldInfos": []}, {"className": "Unity.VisualScripting.CSharpNameUtility", "fieldInfos": []}, {"className": "Unity.VisualScripting.EnumUtility", "fieldInfos": []}, {"className": "Unity.VisualScripting.ExceptionUtility", "fieldInfos": []}, {"className": "Unity.VisualScripting.HashUtility", "fieldInfos": []}, {"className": "Unity.VisualScripting.IAnalyticsIdentifiable", "fieldInfos": []}, {"className": "Unity.VisualScripting.AnalyticsIdentifier", "fieldInfos": [{"name": "Identifier", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "Namespace", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "Hashcode", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.IGettable", "fieldInfos": []}, {"className": "Unity.VisualScripting.XGettable", "fieldInfos": []}, {"className": "Unity.VisualScripting.IIdentifiable", "fieldInfos": []}, {"className": "Unity.VisualScripting.IInitializable", "fieldInfos": []}, {"className": "Unity.VisualScripting.LinqUtility", "fieldInfos": []}, {"className": "Unity.VisualScripting.Recursion", "fieldInfos": []}, {"className": "Unity.VisualScripting.Recursion`1[System.Object]", "fieldInfos": []}, {"className": "Unity.VisualScripting.ReferenceCollector", "fieldInfos": []}, {"className": "Unity.VisualScripting.ReferenceEqualityComparer", "fieldInfos": []}, {"className": "Unity.VisualScripting.RuntimeVSUsageUtility", "fieldInfos": []}, {"className": "Unity.VisualScripting.StringUtility", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnityObjectUtility", "fieldInfos": []}, {"className": "Unity.VisualScripting.XColor", "fieldInfos": []}, {"className": "Unity.VisualScripting.ApplicationVariables", "fieldInfos": []}, {"className": "Unity.VisualScripting.IGraphDataWithVariables", "fieldInfos": []}, {"className": "Unity.VisualScripting.IGraphWithVariables", "fieldInfos": []}, {"className": "Unity.VisualScripting.InspectorVariableNameAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.ObjectVariables", "fieldInfos": []}, {"className": "Unity.VisualScripting.SavedVariables", "fieldInfos": []}, {"className": "Unity.VisualScripting.SceneVariables", "fieldInfos": []}, {"className": "Unity.VisualScripting.VariableDeclaration", "fieldInfos": []}, {"className": "Unity.VisualScripting.VariableDeclarationCollection", "fieldInfos": []}, {"className": "Unity.VisualScripting.VariableDeclarations", "fieldInfos": [{"name": "Kind", "type": "Unity.VisualScripting.VariableKind", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.VariableDeclarationsCloner", "fieldInfos": []}, {"className": "Unity.VisualScripting.Cloner`1[Unity.VisualScripting.VariableDeclarations]", "fieldInfos": []}, {"className": "Unity.VisualScripting.VariableKind", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.VariableKindAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.Variables", "fieldInfos": []}, {"className": "Unity.VisualScripting.VariablesAsset", "fieldInfos": []}, {"className": "Unity.VisualScripting.VariablesSaver", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsArrayConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsDateConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsDictionaryConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsEnumConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsForwardAttribute", "fieldInfos": [{"name": "MemberName", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.FullSerializer.fsForwardConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsGuidConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsIEnumerableConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsKeyValuePairConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsNullableConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsPrimitiveConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsReflectedConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsTypeConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsWeakReferenceConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsConverterRegistrar", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.AnimationCurve_DirectConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsDirectConverter`1[UnityEngine.AnimationCurve]", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.Bounds_DirectConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsDirectConverter`1[UnityEngine.Bounds]", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.Gradient_DirectConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsDirectConverter`1[UnityEngine.Gradient]", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.GUIStyleState_DirectConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsDirectConverter`1[UnityEngine.GUIStyleState]", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.GUIStyle_DirectConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsDirectConverter`1[UnityEngine.GUIStyle]", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.InputAction_DirectConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsDirectConverter`1[UnityEngine.InputSystem.InputAction]", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.Keyframe_DirectConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsDirectConverter`1[UnityEngine.Keyframe]", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.LayerMask_DirectConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsDirectConverter`1[UnityEngine.LayerMask]", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.RectOffset_DirectConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsDirectConverter`1[UnityEngine.RectOffset]", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.Rect_DirectConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsDirectConverter`1[UnityEngine.Rect]", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsAotCompilationManager", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsGlobalConfig", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsConfig", "fieldInfos": [{"name": "DefaultMemberSerialization", "type": "Unity.VisualScripting.FullSerializer.fsMemberSerialization", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "EnablePropertySerialization", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "SerializeNonAutoProperties", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "SerializeNonPublicSetProperties", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "CustomDateTimeFormatString", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "Serialize64BitIntegerAsString", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "SerializeEnumsAsInteger", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.FullSerializer.fsContext", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsDataType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.FullSerializer.fsData", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsMissingVersionConstructorException", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsDuplicateVersionNameException", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsIgnoreAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsISerializationCallbacks", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsSerializationCallbackProcessor", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsObjectProcessor", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsSerializationCallbackReceiverProcessor", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsJsonParser", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsJsonPrinter", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsMemberSerialization", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.FullSerializer.fsResult", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsSerializer", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsMetaProperty", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsMetaType", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsReflectionUtility", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.fsTypeCache", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.Internal.fsCyclicReferenceManager", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.Internal.fsOption", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.Internal.fsPortableReflection", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.Internal.fsTypeExtensions", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.Internal.fsVersionedType", "fieldInfos": [{"name": "VersionString", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.FullSerializer.Internal.fsVersionManager", "fieldInfos": []}, {"className": "Unity.VisualScripting.FullSerializer.Internal.Converters.UnityEvent_Converter", "fieldInfos": []}, {"className": "Unity.VisualScripting.AssemblyQualifiedNameParser.ParsedAssemblyQualifiedName", "fieldInfos": []}, {"className": "Unity.VisualScripting.GraphElement`1[Unity.VisualScripting.FlowGraph]", "fieldInfos": []}, {"className": "Unity.VisualScripting.GraphElement`1[Unity.VisualScripting.StateGraph]", "fieldInfos": []}, {"className": "Unity.VisualScripting.EventMachine`2[Unity.VisualScripting.StateGraph,Unity.VisualScripting.StateGraphAsset]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Machine`2[Unity.VisualScripting.StateGraph,Unity.VisualScripting.StateGraphAsset]", "fieldInfos": []}, {"className": "Unity.VisualScripting.GraphData`1[Unity.VisualScripting.StateGraph]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Macro`1[Unity.VisualScripting.StateGraph]", "fieldInfos": []}, {"className": "Unity.VisualScripting.EventMachine`2[Unity.VisualScripting.FlowGraph,Unity.VisualScripting.ScriptGraphAsset]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Machine`2[Unity.VisualScripting.FlowGraph,Unity.VisualScripting.ScriptGraphAsset]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Macro`1[Unity.VisualScripting.FlowGraph]", "fieldInfos": []}, {"className": "Unity.VisualScripting.GraphData`1[Unity.VisualScripting.FlowGraph]", "fieldInfos": []}]}, {"name": "Unity.VisualScripting.Flow", "path": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll", "types": [{"className": "Unity.VisualScripting.ControlConnection", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnitConnection`2[Unity.VisualScripting.ControlOutput,Unity.VisualScripting.ControlInput]", "fieldInfos": []}, {"className": "Unity.VisualScripting.InvalidConnection", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnitConnection`2[Unity.VisualScripting.IUnitOutputPort,Unity.VisualScripting.IUnitInputPort]", "fieldInfos": []}, {"className": "Unity.VisualScripting.IUnitConnection", "fieldInfos": []}, {"className": "Unity.VisualScripting.IUnitConnectionDebugData", "fieldInfos": []}, {"className": "Unity.VisualScripting.IUnitRelation", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnitConnectionDebugData", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnitRelation", "fieldInfos": []}, {"className": "Unity.VisualScripting.ValueConnection", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnitConnection`2[Unity.VisualScripting.ValueOutput,Unity.VisualScripting.ValueInput]", "fieldInfos": []}, {"className": "Unity.VisualScripting.PortKeyAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.PortLabelAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.PortLabelHiddenAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.SpecialUnitAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnitFooterPortsAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnitHeaderInspectableAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnitOrderAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnitShortTitleAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnitSubtitleAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnitSurtitleAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnitTitleAttribute", "fieldInfos": []}, {"className": "Unity.VisualScripting.Flow", "fieldInfos": [{"name": "loopIdentifier", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.FlowGraph", "fieldInfos": []}, {"className": "Unity.VisualScripting.FlowGraphData", "fieldInfos": []}, {"className": "Unity.VisualScripting.CreateStruct", "fieldInfos": []}, {"className": "Unity.VisualScripting.Unit", "fieldInfos": []}, {"className": "Unity.VisualScripting.Expose", "fieldInfos": []}, {"className": "Unity.VisualScripting.GetMember", "fieldInfos": []}, {"className": "Unity.VisualScripting.MemberUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.InvokeMember", "fieldInfos": []}, {"className": "Unity.VisualScripting.SetMember", "fieldInfos": []}, {"className": "Unity.VisualScripting.CountItems", "fieldInfos": []}, {"className": "Unity.VisualScripting.AddDictionaryItem", "fieldInfos": []}, {"className": "Unity.VisualScripting.ClearDictionary", "fieldInfos": []}, {"className": "Unity.VisualScripting.CreateDictionary", "fieldInfos": []}, {"className": "Unity.VisualScripting.DictionaryContainsKey", "fieldInfos": []}, {"className": "Unity.VisualScripting.GetDictionaryItem", "fieldInfos": []}, {"className": "Unity.VisualScripting.MergeDictionaries", "fieldInfos": []}, {"className": "Unity.VisualScripting.MultiInputUnit`1[System.Collections.IDictionary]", "fieldInfos": []}, {"className": "Unity.VisualScripting.RemoveDictionaryItem", "fieldInfos": []}, {"className": "Unity.VisualScripting.SetDictionaryItem", "fieldInfos": []}, {"className": "Unity.VisualScripting.FirstItem", "fieldInfos": []}, {"className": "Unity.VisualScripting.LastItem", "fieldInfos": []}, {"className": "Unity.VisualScripting.AddListItem", "fieldInfos": []}, {"className": "Unity.VisualScripting.ClearList", "fieldInfos": []}, {"className": "Unity.VisualScripting.CreateList", "fieldInfos": []}, {"className": "Unity.VisualScripting.MultiInputUnit`1[System.Object]", "fieldInfos": []}, {"className": "Unity.VisualScripting.GetListItem", "fieldInfos": []}, {"className": "Unity.VisualScripting.InsertListItem", "fieldInfos": []}, {"className": "Unity.VisualScripting.ListContainsItem", "fieldInfos": []}, {"className": "Unity.VisualScripting.MergeLists", "fieldInfos": []}, {"className": "Unity.VisualScripting.MultiInputUnit`1[System.Collections.IEnumerable]", "fieldInfos": []}, {"className": "Unity.VisualScripting.RemoveListItem", "fieldInfos": []}, {"className": "Unity.VisualScripting.RemoveListItemAt", "fieldInfos": []}, {"className": "Unity.VisualScripting.SetListItem", "fieldInfos": []}, {"className": "Unity.VisualScripting.Break", "fieldInfos": []}, {"className": "Unity.VisualScripting.Cache", "fieldInfos": []}, {"className": "Unity.VisualScripting.For", "fieldInfos": []}, {"className": "Unity.VisualScripting.LoopUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.ForEach", "fieldInfos": []}, {"className": "Unity.VisualScripting.IBranchUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.If", "fieldInfos": []}, {"className": "Unity.VisualScripting.ISelectUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.Once", "fieldInfos": []}, {"className": "Unity.VisualScripting.SelectOnEnum", "fieldInfos": []}, {"className": "Unity.VisualScripting.SelectOnFlow", "fieldInfos": []}, {"className": "Unity.VisualScripting.SelectOnInteger", "fieldInfos": []}, {"className": "Unity.VisualScripting.SelectUnit`1[System.Int32]", "fieldInfos": []}, {"className": "Unity.VisualScripting.SelectOnString", "fieldInfos": []}, {"className": "Unity.VisualScripting.SelectUnit`1[System.String]", "fieldInfos": []}, {"className": "Unity.VisualScripting.SelectUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.Sequence", "fieldInfos": []}, {"className": "Unity.VisualScripting.SwitchOnEnum", "fieldInfos": []}, {"className": "Unity.VisualScripting.SwitchOnInteger", "fieldInfos": []}, {"className": "Unity.VisualScripting.SwitchUnit`1[System.Int32]", "fieldInfos": []}, {"className": "Unity.VisualScripting.SwitchOnString", "fieldInfos": []}, {"className": "Unity.VisualScripting.SwitchUnit`1[System.String]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Throw", "fieldInfos": []}, {"className": "Unity.VisualScripting.ToggleFlow", "fieldInfos": []}, {"className": "Unity.VisualScripting.ToggleValue", "fieldInfos": []}, {"className": "Unity.VisualScripting.TryCatch", "fieldInfos": []}, {"className": "Unity.VisualScripting.While", "fieldInfos": []}, {"className": "Unity.VisualScripting.BoltAnimationEvent", "fieldInfos": []}, {"className": "Unity.VisualScripting.MachineEventUnit`1[UnityEngine.AnimationEvent]", "fieldInfos": []}, {"className": "Unity.VisualScripting.EventUnit`1[UnityEngine.AnimationEvent]", "fieldInfos": []}, {"className": "Unity.VisualScripting.BoltNamedAnimationEvent", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnAnimatorIK", "fieldInfos": []}, {"className": "Unity.VisualScripting.GameObjectEventUnit`1[System.Int32]", "fieldInfos": []}, {"className": "Unity.VisualScripting.EventUnit`1[System.Int32]", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnAnimatorMove", "fieldInfos": []}, {"className": "Unity.VisualScripting.GameObjectEventUnit`1[Unity.VisualScripting.EmptyEventArgs]", "fieldInfos": []}, {"className": "Unity.VisualScripting.EventUnit`1[Unity.VisualScripting.EmptyEventArgs]", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnApplicationFocus", "fieldInfos": []}, {"className": "Unity.VisualScripting.GlobalEventUnit`1[Unity.VisualScripting.EmptyEventArgs]", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnApplicationLostFocus", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnApplicationPause", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnApplicationQuit", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnApplicationResume", "fieldInfos": []}, {"className": "Unity.VisualScripting.BoltUnityEvent", "fieldInfos": []}, {"className": "Unity.VisualScripting.MachineEventUnit`1[System.String]", "fieldInfos": []}, {"className": "Unity.VisualScripting.EventUnit`1[System.String]", "fieldInfos": []}, {"className": "Unity.VisualScripting.CustomEvent", "fieldInfos": []}, {"className": "Unity.VisualScripting.GameObjectEventUnit`1[Unity.VisualScripting.CustomEventArgs]", "fieldInfos": []}, {"className": "Unity.VisualScripting.EventUnit`1[Unity.VisualScripting.CustomEventArgs]", "fieldInfos": []}, {"className": "Unity.VisualScripting.CustomEventArgs", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnDrawGizmos", "fieldInfos": []}, {"className": "Unity.VisualScripting.ManualEventUnit`1[Unity.VisualScripting.EmptyEventArgs]", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnDrawGizmosSelected", "fieldInfos": []}, {"className": "Unity.VisualScripting.GenericGuiEventUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.GameObjectEventUnit`1[UnityEngine.EventSystems.BaseEventData]", "fieldInfos": []}, {"className": "Unity.VisualScripting.EventUnit`1[UnityEngine.EventSystems.BaseEventData]", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnBeginDrag", "fieldInfos": []}, {"className": "Unity.VisualScripting.PointerEventUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.GameObjectEventUnit`1[UnityEngine.EventSystems.PointerEventData]", "fieldInfos": []}, {"className": "Unity.VisualScripting.EventUnit`1[UnityEngine.EventSystems.PointerEventData]", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnButtonClick", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnCancel", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnDeselect", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnDrag", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnDrop", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnDropdownValueChanged", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnEndDrag", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnGUI", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnInputFieldEndEdit", "fieldInfos": []}, {"className": "Unity.VisualScripting.GameObjectEventUnit`1[System.String]", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnInputFieldValueChanged", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnMove", "fieldInfos": []}, {"className": "Unity.VisualScripting.GameObjectEventUnit`1[UnityEngine.EventSystems.AxisEventData]", "fieldInfos": []}, {"className": "Unity.VisualScripting.EventUnit`1[UnityEngine.EventSystems.AxisEventData]", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnPointerClick", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnPointerDown", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnPointerEnter", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnPointerExit", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnPointerUp", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnScroll", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnScrollbarValueChanged", "fieldInfos": []}, {"className": "Unity.VisualScripting.GameObjectEventUnit`1[System.Single]", "fieldInfos": []}, {"className": "Unity.VisualScripting.EventUnit`1[System.Single]", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnScrollRectValueChanged", "fieldInfos": []}, {"className": "Unity.VisualScripting.GameObjectEventUnit`1[UnityEngine.Vector2]", "fieldInfos": []}, {"className": "Unity.VisualScripting.EventUnit`1[UnityEngine.Vector2]", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnSelect", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnSliderValueChanged", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnSubmit", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnToggleValueChanged", "fieldInfos": []}, {"className": "Unity.VisualScripting.GameObjectEventUnit`1[System.Boolean]", "fieldInfos": []}, {"className": "Unity.VisualScripting.EventUnit`1[System.Boolean]", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnTransformChildrenChanged", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnTransformParentChanged", "fieldInfos": []}, {"className": "Unity.VisualScripting.IEventUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.IGameObjectEventUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.IMouseEventUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnButtonInput", "fieldInfos": []}, {"className": "Unity.VisualScripting.MachineEventUnit`1[Unity.VisualScripting.EmptyEventArgs]", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnKeyboardInput", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnMouseDown", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnMouseDrag", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnMouseEnter", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnMouseExit", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnMouseInput", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnMouseOver", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnMouseUp", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnMouseUpAsButton", "fieldInfos": []}, {"className": "Unity.VisualScripting.FixedUpdate", "fieldInfos": []}, {"className": "Unity.VisualScripting.LateUpdate", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnDestroy", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnDisable", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnEnable", "fieldInfos": []}, {"className": "Unity.VisualScripting.Start", "fieldInfos": []}, {"className": "Unity.VisualScripting.Update", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnDestinationReached", "fieldInfos": []}, {"className": "Unity.VisualScripting.CollisionEventUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.GameObjectEventUnit`1[UnityEngine.Collision]", "fieldInfos": []}, {"className": "Unity.VisualScripting.EventUnit`1[UnityEngine.Collision]", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnCollisionEnter", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnCollisionExit", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnCollisionStay", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnControllerColliderHit", "fieldInfos": []}, {"className": "Unity.VisualScripting.GameObjectEventUnit`1[UnityEngine.ControllerColliderHit]", "fieldInfos": []}, {"className": "Unity.VisualScripting.EventUnit`1[UnityEngine.ControllerColliderHit]", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnJointBreak", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnParticleCollision", "fieldInfos": []}, {"className": "Unity.VisualScripting.GameObjectEventUnit`1[UnityEngine.GameObject]", "fieldInfos": []}, {"className": "Unity.VisualScripting.EventUnit`1[UnityEngine.GameObject]", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnTriggerEnter", "fieldInfos": []}, {"className": "Unity.VisualScripting.TriggerEventUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.GameObjectEventUnit`1[UnityEngine.Collider]", "fieldInfos": []}, {"className": "Unity.VisualScripting.EventUnit`1[UnityEngine.Collider]", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnTriggerExit", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnTriggerStay", "fieldInfos": []}, {"className": "Unity.VisualScripting.CollisionEvent2DUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.GameObjectEventUnit`1[UnityEngine.Collision2D]", "fieldInfos": []}, {"className": "Unity.VisualScripting.EventUnit`1[UnityEngine.Collision2D]", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnCollisionEnter2D", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnCollisionExit2D", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnCollisionStay2D", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnJointBreak2D", "fieldInfos": []}, {"className": "Unity.VisualScripting.GameObjectEventUnit`1[UnityEngine.Joint2D]", "fieldInfos": []}, {"className": "Unity.VisualScripting.EventUnit`1[UnityEngine.Joint2D]", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnTriggerEnter2D", "fieldInfos": []}, {"className": "Unity.VisualScripting.TriggerEvent2DUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.GameObjectEventUnit`1[UnityEngine.Collider2D]", "fieldInfos": []}, {"className": "Unity.VisualScripting.EventUnit`1[UnityEngine.Collider2D]", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnTriggerExit2D", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnTriggerStay2D", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnBecameInvisible", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnBecameVisible", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnTimerElapsed", "fieldInfos": []}, {"className": "Unity.VisualScripting.TriggerCustomEvent", "fieldInfos": []}, {"className": "Unity.VisualScripting.Formula", "fieldInfos": []}, {"className": "Unity.VisualScripting.GetScriptGraph", "fieldInfos": []}, {"className": "Unity.VisualScripting.GetGraph`3[Unity.VisualScripting.FlowGraph,Unity.VisualScripting.ScriptGraphAsset,Unity.VisualScripting.ScriptMachine]", "fieldInfos": []}, {"className": "Unity.VisualScripting.GetScriptGraphs", "fieldInfos": []}, {"className": "Unity.VisualScripting.GetGraphs`3[Unity.VisualScripting.FlowGraph,Unity.VisualScripting.ScriptGraphAsset,Unity.VisualScripting.ScriptMachine]", "fieldInfos": []}, {"className": "Unity.VisualScripting.HasScriptGraph", "fieldInfos": []}, {"className": "Unity.VisualScripting.HasGraph`3[Unity.VisualScripting.FlowGraph,Unity.VisualScripting.ScriptGraphAsset,Unity.VisualScripting.ScriptMachine]", "fieldInfos": []}, {"className": "Unity.VisualScripting.ScriptGraphContainerType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.SetScriptGraph", "fieldInfos": []}, {"className": "Unity.VisualScripting.SetGraph`3[Unity.VisualScripting.FlowGraph,Unity.VisualScripting.ScriptGraphAsset,Unity.VisualScripting.ScriptMachine]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Literal", "fieldInfos": []}, {"className": "Unity.VisualScripting.And", "fieldInfos": []}, {"className": "Unity.VisualScripting.ApproximatelyEqual", "fieldInfos": []}, {"className": "Unity.VisualScripting.BinaryComparisonUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.Comparison", "fieldInfos": []}, {"className": "Unity.VisualScripting.Equal", "fieldInfos": []}, {"className": "Unity.VisualScripting.EqualityComparison", "fieldInfos": []}, {"className": "Unity.VisualScripting.ExclusiveOr", "fieldInfos": []}, {"className": "Unity.VisualScripting.Greater", "fieldInfos": []}, {"className": "Unity.VisualScripting.GreaterOrEqual", "fieldInfos": []}, {"className": "Unity.VisualScripting.Less", "fieldInfos": []}, {"className": "Unity.VisualScripting.LessOrEqual", "fieldInfos": []}, {"className": "Unity.VisualScripting.Negate", "fieldInfos": []}, {"className": "Unity.VisualScripting.NotApproximatelyEqual", "fieldInfos": []}, {"className": "Unity.VisualScripting.NotEqual", "fieldInfos": []}, {"className": "Unity.VisualScripting.NumericComparison", "fieldInfos": []}, {"className": "Unity.VisualScripting.Or", "fieldInfos": []}, {"className": "Unity.VisualScripting.DeprecatedGenericAdd", "fieldInfos": []}, {"className": "Unity.VisualScripting.Add`1[System.Object]", "fieldInfos": []}, {"className": "Unity.VisualScripting.GenericDivide", "fieldInfos": []}, {"className": "Unity.VisualScripting.Divide`1[System.Object]", "fieldInfos": []}, {"className": "Unity.VisualScripting.GenericModulo", "fieldInfos": []}, {"className": "Unity.VisualScripting.Modulo`1[System.Object]", "fieldInfos": []}, {"className": "Unity.VisualScripting.GenericMultiply", "fieldInfos": []}, {"className": "Unity.VisualScripting.Multiply`1[System.Object]", "fieldInfos": []}, {"className": "Unity.VisualScripting.GenericSubtract", "fieldInfos": []}, {"className": "Unity.VisualScripting.Subtract`1[System.Object]", "fieldInfos": []}, {"className": "Unity.VisualScripting.GenericSum", "fieldInfos": []}, {"className": "Unity.VisualScripting.Sum`1[System.Object]", "fieldInfos": []}, {"className": "Unity.VisualScripting.DeprecatedScalarAdd", "fieldInfos": []}, {"className": "Unity.VisualScripting.Add`1[System.Single]", "fieldInfos": []}, {"className": "Unity.VisualScripting.ScalarAbsolute", "fieldInfos": []}, {"className": "Unity.VisualScripting.Absolute`1[System.Single]", "fieldInfos": []}, {"className": "Unity.VisualScripting.ScalarAverage", "fieldInfos": []}, {"className": "Unity.VisualScripting.Average`1[System.Single]", "fieldInfos": []}, {"className": "Unity.VisualScripting.MultiInputUnit`1[System.Single]", "fieldInfos": []}, {"className": "Unity.VisualScripting.ScalarDivide", "fieldInfos": []}, {"className": "Unity.VisualScripting.Divide`1[System.Single]", "fieldInfos": []}, {"className": "Unity.VisualScripting.ScalarExponentiate", "fieldInfos": []}, {"className": "Unity.VisualScripting.ScalarLerp", "fieldInfos": []}, {"className": "Unity.VisualScripting.Lerp`1[System.Single]", "fieldInfos": []}, {"className": "Unity.VisualScripting.ScalarMaximum", "fieldInfos": []}, {"className": "Unity.VisualScripting.Maximum`1[System.Single]", "fieldInfos": []}, {"className": "Unity.VisualScripting.ScalarMinimum", "fieldInfos": []}, {"className": "Unity.VisualScripting.Minimum`1[System.Single]", "fieldInfos": []}, {"className": "Unity.VisualScripting.ScalarModulo", "fieldInfos": []}, {"className": "Unity.VisualScripting.Modulo`1[System.Single]", "fieldInfos": []}, {"className": "Unity.VisualScripting.ScalarMoveTowards", "fieldInfos": []}, {"className": "Unity.VisualScripting.MoveTowards`1[System.Single]", "fieldInfos": []}, {"className": "Unity.VisualScripting.ScalarMultiply", "fieldInfos": []}, {"className": "Unity.VisualScripting.Multiply`1[System.Single]", "fieldInfos": []}, {"className": "Unity.VisualScripting.ScalarNormalize", "fieldInfos": []}, {"className": "Unity.VisualScripting.Normalize`1[System.Single]", "fieldInfos": []}, {"className": "Unity.VisualScripting.ScalarPerSecond", "fieldInfos": []}, {"className": "Unity.VisualScripting.PerSecond`1[System.Single]", "fieldInfos": []}, {"className": "Unity.VisualScripting.ScalarRoot", "fieldInfos": []}, {"className": "Unity.VisualScripting.ScalarRound", "fieldInfos": []}, {"className": "Unity.VisualScripting.Round`2[System.Single,System.Int32]", "fieldInfos": []}, {"className": "Unity.VisualScripting.ScalarSubtract", "fieldInfos": []}, {"className": "Unity.VisualScripting.Subtract`1[System.Single]", "fieldInfos": []}, {"className": "Unity.VisualScripting.ScalarSum", "fieldInfos": []}, {"className": "Unity.VisualScripting.Sum`1[System.Single]", "fieldInfos": []}, {"className": "Unity.VisualScripting.DeprecatedVector2Add", "fieldInfos": []}, {"className": "Unity.VisualScripting.Add`1[UnityEngine.Vector2]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector2Absolute", "fieldInfos": []}, {"className": "Unity.VisualScripting.Absolute`1[UnityEngine.Vector2]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector2Angle", "fieldInfos": []}, {"className": "Unity.VisualScripting.Angle`1[UnityEngine.Vector2]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector2Average", "fieldInfos": []}, {"className": "Unity.VisualScripting.Average`1[UnityEngine.Vector2]", "fieldInfos": []}, {"className": "Unity.VisualScripting.MultiInputUnit`1[UnityEngine.Vector2]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector2Distance", "fieldInfos": []}, {"className": "Unity.VisualScripting.Distance`1[UnityEngine.Vector2]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector2Divide", "fieldInfos": []}, {"className": "Unity.VisualScripting.Divide`1[UnityEngine.Vector2]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector2DotProduct", "fieldInfos": []}, {"className": "Unity.VisualScripting.DotProduct`1[UnityEngine.Vector2]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector2Lerp", "fieldInfos": []}, {"className": "Unity.VisualScripting.Lerp`1[UnityEngine.Vector2]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector2Maximum", "fieldInfos": []}, {"className": "Unity.VisualScripting.Maximum`1[UnityEngine.Vector2]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector2Minimum", "fieldInfos": []}, {"className": "Unity.VisualScripting.Minimum`1[UnityEngine.Vector2]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector2Modulo", "fieldInfos": []}, {"className": "Unity.VisualScripting.Modulo`1[UnityEngine.Vector2]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector2MoveTowards", "fieldInfos": []}, {"className": "Unity.VisualScripting.MoveTowards`1[UnityEngine.Vector2]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector2Multiply", "fieldInfos": []}, {"className": "Unity.VisualScripting.Multiply`1[UnityEngine.Vector2]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector2Normalize", "fieldInfos": []}, {"className": "Unity.VisualScripting.Normalize`1[UnityEngine.Vector2]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector2PerSecond", "fieldInfos": []}, {"className": "Unity.VisualScripting.PerSecond`1[UnityEngine.Vector2]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector2Project", "fieldInfos": []}, {"className": "Unity.VisualScripting.Project`1[UnityEngine.Vector2]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector2Round", "fieldInfos": []}, {"className": "Unity.VisualScripting.Round`2[UnityEngine.Vector2,UnityEngine.Vector2]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector2Subtract", "fieldInfos": []}, {"className": "Unity.VisualScripting.Subtract`1[UnityEngine.Vector2]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector2Sum", "fieldInfos": []}, {"className": "Unity.VisualScripting.Sum`1[UnityEngine.Vector2]", "fieldInfos": []}, {"className": "Unity.VisualScripting.DeprecatedVector3Add", "fieldInfos": []}, {"className": "Unity.VisualScripting.Add`1[UnityEngine.Vector3]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector3Absolute", "fieldInfos": []}, {"className": "Unity.VisualScripting.Absolute`1[UnityEngine.Vector3]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector3Angle", "fieldInfos": []}, {"className": "Unity.VisualScripting.Angle`1[UnityEngine.Vector3]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector3Average", "fieldInfos": []}, {"className": "Unity.VisualScripting.Average`1[UnityEngine.Vector3]", "fieldInfos": []}, {"className": "Unity.VisualScripting.MultiInputUnit`1[UnityEngine.Vector3]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector3CrossProduct", "fieldInfos": []}, {"className": "Unity.VisualScripting.CrossProduct`1[UnityEngine.Vector3]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector3Distance", "fieldInfos": []}, {"className": "Unity.VisualScripting.Distance`1[UnityEngine.Vector3]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector3Divide", "fieldInfos": []}, {"className": "Unity.VisualScripting.Divide`1[UnityEngine.Vector3]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector3DotProduct", "fieldInfos": []}, {"className": "Unity.VisualScripting.DotProduct`1[UnityEngine.Vector3]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector3Lerp", "fieldInfos": []}, {"className": "Unity.VisualScripting.Lerp`1[UnityEngine.Vector3]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector3Maximum", "fieldInfos": []}, {"className": "Unity.VisualScripting.Maximum`1[UnityEngine.Vector3]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector3Minimum", "fieldInfos": []}, {"className": "Unity.VisualScripting.Minimum`1[UnityEngine.Vector3]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector3Modulo", "fieldInfos": []}, {"className": "Unity.VisualScripting.Modulo`1[UnityEngine.Vector3]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector3MoveTowards", "fieldInfos": []}, {"className": "Unity.VisualScripting.MoveTowards`1[UnityEngine.Vector3]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector3Multiply", "fieldInfos": []}, {"className": "Unity.VisualScripting.Multiply`1[UnityEngine.Vector3]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector3Normalize", "fieldInfos": []}, {"className": "Unity.VisualScripting.Normalize`1[UnityEngine.Vector3]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector3PerSecond", "fieldInfos": []}, {"className": "Unity.VisualScripting.PerSecond`1[UnityEngine.Vector3]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector3Project", "fieldInfos": []}, {"className": "Unity.VisualScripting.Project`1[UnityEngine.Vector3]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector3Round", "fieldInfos": []}, {"className": "Unity.VisualScripting.Round`2[UnityEngine.Vector3,UnityEngine.Vector3]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector3Subtract", "fieldInfos": []}, {"className": "Unity.VisualScripting.Subtract`1[UnityEngine.Vector3]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector3Sum", "fieldInfos": []}, {"className": "Unity.VisualScripting.Sum`1[UnityEngine.Vector3]", "fieldInfos": []}, {"className": "Unity.VisualScripting.DeprecatedVector4Add", "fieldInfos": []}, {"className": "Unity.VisualScripting.Add`1[UnityEngine.Vector4]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector4Absolute", "fieldInfos": []}, {"className": "Unity.VisualScripting.Absolute`1[UnityEngine.Vector4]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector4Average", "fieldInfos": []}, {"className": "Unity.VisualScripting.Average`1[UnityEngine.Vector4]", "fieldInfos": []}, {"className": "Unity.VisualScripting.MultiInputUnit`1[UnityEngine.Vector4]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector4Distance", "fieldInfos": []}, {"className": "Unity.VisualScripting.Distance`1[UnityEngine.Vector4]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector4Divide", "fieldInfos": []}, {"className": "Unity.VisualScripting.Divide`1[UnityEngine.Vector4]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector4DotProduct", "fieldInfos": []}, {"className": "Unity.VisualScripting.DotProduct`1[UnityEngine.Vector4]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector4Lerp", "fieldInfos": []}, {"className": "Unity.VisualScripting.Lerp`1[UnityEngine.Vector4]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector4Maximum", "fieldInfos": []}, {"className": "Unity.VisualScripting.Maximum`1[UnityEngine.Vector4]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector4Minimum", "fieldInfos": []}, {"className": "Unity.VisualScripting.Minimum`1[UnityEngine.Vector4]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector4Modulo", "fieldInfos": []}, {"className": "Unity.VisualScripting.Modulo`1[UnityEngine.Vector4]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector4MoveTowards", "fieldInfos": []}, {"className": "Unity.VisualScripting.MoveTowards`1[UnityEngine.Vector4]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector4Multiply", "fieldInfos": []}, {"className": "Unity.VisualScripting.Multiply`1[UnityEngine.Vector4]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector4Normalize", "fieldInfos": []}, {"className": "Unity.VisualScripting.Normalize`1[UnityEngine.Vector4]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector4PerSecond", "fieldInfos": []}, {"className": "Unity.VisualScripting.PerSecond`1[UnityEngine.Vector4]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector4Round", "fieldInfos": []}, {"className": "Unity.VisualScripting.Round`2[UnityEngine.Vector4,UnityEngine.Vector4]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector4Subtract", "fieldInfos": []}, {"className": "Unity.VisualScripting.Subtract`1[UnityEngine.Vector4]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Vector4Sum", "fieldInfos": []}, {"className": "Unity.VisualScripting.Sum`1[UnityEngine.Vector4]", "fieldInfos": []}, {"className": "Unity.VisualScripting.MissingType", "fieldInfos": []}, {"className": "Unity.VisualScripting.GraphInput", "fieldInfos": []}, {"className": "Unity.VisualScripting.GraphOutput", "fieldInfos": []}, {"className": "Unity.VisualScripting.Null", "fieldInfos": []}, {"className": "Unity.VisualScripting.NullCheck", "fieldInfos": []}, {"className": "Unity.VisualScripting.NullCoalesce", "fieldInfos": []}, {"className": "Unity.VisualScripting.This", "fieldInfos": []}, {"className": "Unity.VisualScripting.Cooldown", "fieldInfos": []}, {"className": "Unity.VisualScripting.Timer", "fieldInfos": []}, {"className": "Unity.VisualScripting.WaitForEndOfFrameUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.WaitUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.WaitForFlow", "fieldInfos": []}, {"className": "Unity.VisualScripting.WaitForNextFrameUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.WaitForSecondsUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.WaitUntilUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.WaitWhileUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.GetVariable", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnifiedVariableUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.IsVariableDefined", "fieldInfos": []}, {"className": "Unity.VisualScripting.IUnifiedVariableUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.GetApplicationVariable", "fieldInfos": []}, {"className": "Unity.VisualScripting.GetVariableUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.VariableUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.GetGraphVariable", "fieldInfos": []}, {"className": "Unity.VisualScripting.GetObjectVariable", "fieldInfos": []}, {"className": "Unity.VisualScripting.GetSavedVariable", "fieldInfos": []}, {"className": "Unity.VisualScripting.GetSceneVariable", "fieldInfos": []}, {"className": "Unity.VisualScripting.IApplicationVariableUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.IGraphVariableUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.IObjectVariableUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.IsApplicationVariableDefined", "fieldInfos": []}, {"className": "Unity.VisualScripting.IsVariableDefinedUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.ISavedVariableUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.ISceneVariableUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.IsGraphVariableDefined", "fieldInfos": []}, {"className": "Unity.VisualScripting.IsObjectVariableDefined", "fieldInfos": []}, {"className": "Unity.VisualScripting.IsSavedVariableDefined", "fieldInfos": []}, {"className": "Unity.VisualScripting.IsSceneVariableDefined", "fieldInfos": []}, {"className": "Unity.VisualScripting.IVariableUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.SetApplicationVariable", "fieldInfos": []}, {"className": "Unity.VisualScripting.SetVariableUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.SetGraphVariable", "fieldInfos": []}, {"className": "Unity.VisualScripting.SetObjectVariable", "fieldInfos": []}, {"className": "Unity.VisualScripting.SetSavedVariable", "fieldInfos": []}, {"className": "Unity.VisualScripting.SetSceneVariable", "fieldInfos": []}, {"className": "Unity.VisualScripting.SaveVariables", "fieldInfos": []}, {"className": "Unity.VisualScripting.SetVariable", "fieldInfos": []}, {"className": "Unity.VisualScripting.INesterUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.IUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.XUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.IUnitDebugData", "fieldInfos": []}, {"className": "Unity.VisualScripting.IMultiInputUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.ControlInput", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnitPort`3[Unity.VisualScripting.ControlOutput,Unity.VisualScripting.IUnitOutputPort,Unity.VisualScripting.ControlConnection]", "fieldInfos": []}, {"className": "Unity.VisualScripting.ControlInputDefinition", "fieldInfos": []}, {"className": "Unity.VisualScripting.ControlPortDefinition", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnitPortDefinition", "fieldInfos": []}, {"className": "Unity.VisualScripting.ControlOutput", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnitPort`3[Unity.VisualScripting.ControlInput,Unity.VisualScripting.IUnitInputPort,Unity.VisualScripting.ControlConnection]", "fieldInfos": []}, {"className": "Unity.VisualScripting.ControlOutputDefinition", "fieldInfos": []}, {"className": "Unity.VisualScripting.InvalidInput", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnitPort`3[Unity.VisualScripting.IUnitOutputPort,Unity.VisualScripting.IUnitOutputPort,Unity.VisualScripting.InvalidConnection]", "fieldInfos": []}, {"className": "Unity.VisualScripting.InvalidOutput", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnitPort`3[Unity.VisualScripting.IUnitInputPort,Unity.VisualScripting.IUnitInputPort,Unity.VisualScripting.InvalidConnection]", "fieldInfos": []}, {"className": "Unity.VisualScripting.IUnitControlPort", "fieldInfos": []}, {"className": "Unity.VisualScripting.IUnitControlPortDefinition", "fieldInfos": []}, {"className": "Unity.VisualScripting.IUnitInputPort", "fieldInfos": []}, {"className": "Unity.VisualScripting.IUnitInputPortDefinition", "fieldInfos": []}, {"className": "Unity.VisualScripting.IUnitInvalidPort", "fieldInfos": []}, {"className": "Unity.VisualScripting.IUnitOutputPort", "fieldInfos": []}, {"className": "Unity.VisualScripting.IUnitOutputPortDefinition", "fieldInfos": []}, {"className": "Unity.VisualScripting.IUnitPort", "fieldInfos": []}, {"className": "Unity.VisualScripting.IUnitPortDefinition", "fieldInfos": []}, {"className": "Unity.VisualScripting.IUnitValuePort", "fieldInfos": []}, {"className": "Unity.VisualScripting.IUnitValuePortDefinition", "fieldInfos": []}, {"className": "Unity.VisualScripting.MissingValuePortInputException", "fieldInfos": []}, {"className": "Unity.VisualScripting.ValueInput", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnitPort`3[Unity.VisualScripting.ValueOutput,Unity.VisualScripting.IUnitOutputPort,Unity.VisualScripting.ValueConnection]", "fieldInfos": []}, {"className": "Unity.VisualScripting.ValueInputDefinition", "fieldInfos": []}, {"className": "Unity.VisualScripting.ValuePortDefinition", "fieldInfos": []}, {"className": "Unity.VisualScripting.ValueOutput", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnitPort`3[Unity.VisualScripting.ValueInput,Unity.VisualScripting.IUnitInputPort,Unity.VisualScripting.ValueConnection]", "fieldInfos": []}, {"className": "Unity.VisualScripting.ValueOutputDefinition", "fieldInfos": []}, {"className": "Unity.VisualScripting.ScriptGraphAsset", "fieldInfos": []}, {"className": "Unity.VisualScripting.ScriptMachine", "fieldInfos": []}, {"className": "Unity.VisualScripting.SubgraphUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.NesterUnit`2[Unity.VisualScripting.FlowGraph,Unity.VisualScripting.ScriptGraphAsset]", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnitCategory", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnitCategoryConverter", "fieldInfos": []}, {"className": "Unity.VisualScripting.UnitPreservation", "fieldInfos": []}, {"className": "Unity.VisualScripting.InputSystem.InputActionChangeOption", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.InputSystem.OutputType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.InputSystem.OnInputSystemEvent", "fieldInfos": [{"name": "InputActionChangeType", "type": "Unity.VisualScripting.InputSystem.InputActionChangeOption", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.InputSystem.OnInputSystemEventButton", "fieldInfos": []}, {"className": "Unity.VisualScripting.InputSystem.OnInputSystemEventFloat", "fieldInfos": []}, {"className": "Unity.VisualScripting.InputSystem.OnInputSystemEventVector2", "fieldInfos": []}, {"className": "Unity.VisualScripting.Dependencies.NCalc.BinaryExpression", "fieldInfos": []}, {"className": "Unity.VisualScripting.Dependencies.NCalc.LogicalExpression", "fieldInfos": []}, {"className": "Unity.VisualScripting.Dependencies.NCalc.BinaryExpressionType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.Dependencies.NCalc.EvaluateFunctionHandler", "fieldInfos": []}, {"className": "Unity.VisualScripting.Dependencies.NCalc.EvaluateParameterHandler", "fieldInfos": []}, {"className": "Unity.VisualScripting.Dependencies.NCalc.EvaluationException", "fieldInfos": []}, {"className": "Unity.VisualScripting.Dependencies.NCalc.EvaluateOptions", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.Dependencies.NCalc.EvaluationVisitor", "fieldInfos": []}, {"className": "Unity.VisualScripting.Dependencies.NCalc.LogicalExpressionVisitor", "fieldInfos": []}, {"className": "Unity.VisualScripting.Dependencies.NCalc.Expression", "fieldInfos": []}, {"className": "Unity.VisualScripting.Dependencies.NCalc.FunctionArgs", "fieldInfos": []}, {"className": "Unity.VisualScripting.Dependencies.NCalc.FunctionExpression", "fieldInfos": []}, {"className": "Unity.VisualScripting.Dependencies.NCalc.IdentifierExpression", "fieldInfos": []}, {"className": "Unity.VisualScripting.Dependencies.NCalc.NCalcLexer", "fieldInfos": []}, {"className": "Unity.VisualScripting.Dependencies.NCalc.NCalcParser", "fieldInfos": []}, {"className": "Unity.VisualScripting.Dependencies.NCalc.ParameterArgs", "fieldInfos": []}, {"className": "Unity.VisualScripting.Dependencies.NCalc.SerializationVisitor", "fieldInfos": []}, {"className": "Unity.VisualScripting.Dependencies.NCalc.TernaryExpression", "fieldInfos": []}, {"className": "Unity.VisualScripting.Dependencies.NCalc.UnaryExpression", "fieldInfos": []}, {"className": "Unity.VisualScripting.Dependencies.NCalc.UnaryExpressionType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.Dependencies.NCalc.ValueExpression", "fieldInfos": []}, {"className": "Unity.VisualScripting.Dependencies.NCalc.ValueType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.SetGraph`3[Unity.VisualScripting.StateGraph,Unity.VisualScripting.StateGraphAsset,Unity.VisualScripting.StateMachine]", "fieldInfos": []}, {"className": "Unity.VisualScripting.GetGraphs`3[Unity.VisualScripting.StateGraph,Unity.VisualScripting.StateGraphAsset,Unity.VisualScripting.StateMachine]", "fieldInfos": []}, {"className": "Unity.VisualScripting.GetGraph`3[Unity.VisualScripting.StateGraph,Unity.VisualScripting.StateGraphAsset,Unity.VisualScripting.StateMachine]", "fieldInfos": []}, {"className": "Unity.VisualScripting.NesterUnit`2[Unity.VisualScripting.StateGraph,Unity.VisualScripting.StateGraphAsset]", "fieldInfos": []}, {"className": "Unity.VisualScripting.HasGraph`3[Unity.VisualScripting.StateGraph,Unity.VisualScripting.StateGraphAsset,Unity.VisualScripting.StateMachine]", "fieldInfos": []}]}, {"name": "Unity.VisualScripting.State", "path": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll", "types": [{"className": "Unity.VisualScripting.AnyState", "fieldInfos": []}, {"className": "Unity.VisualScripting.State", "fieldInfos": []}, {"className": "Unity.VisualScripting.FlowState", "fieldInfos": []}, {"className": "Unity.VisualScripting.NesterState`2[Unity.VisualScripting.FlowGraph,Unity.VisualScripting.ScriptGraphAsset]", "fieldInfos": []}, {"className": "Unity.VisualScripting.FlowStateTransition", "fieldInfos": []}, {"className": "Unity.VisualScripting.NesterStateTransition`2[Unity.VisualScripting.FlowGraph,Unity.VisualScripting.ScriptGraphAsset]", "fieldInfos": []}, {"className": "Unity.VisualScripting.StateTransition", "fieldInfos": []}, {"className": "Unity.VisualScripting.HasStateGraph", "fieldInfos": []}, {"className": "Unity.VisualScripting.INesterState", "fieldInfos": []}, {"className": "Unity.VisualScripting.INesterStateTransition", "fieldInfos": []}, {"className": "Unity.VisualScripting.IState", "fieldInfos": []}, {"className": "Unity.VisualScripting.IStateDebugData", "fieldInfos": []}, {"className": "Unity.VisualScripting.IStateTransition", "fieldInfos": []}, {"className": "Unity.VisualScripting.IStateTransitionDebugData", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnEnterState", "fieldInfos": []}, {"className": "Unity.VisualScripting.OnExitState", "fieldInfos": []}, {"className": "Unity.VisualScripting.StateEnterReason", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.StateEventHooks", "fieldInfos": []}, {"className": "Unity.VisualScripting.StateExitReason", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.StateGraph", "fieldInfos": []}, {"className": "Unity.VisualScripting.StateGraphAsset", "fieldInfos": []}, {"className": "Unity.VisualScripting.StateGraphData", "fieldInfos": []}, {"className": "Unity.VisualScripting.StateMachine", "fieldInfos": []}, {"className": "Unity.VisualScripting.StateUnit", "fieldInfos": []}, {"className": "Unity.VisualScripting.SuperState", "fieldInfos": []}, {"className": "Unity.VisualScripting.NesterState`2[Unity.VisualScripting.StateGraph,Unity.VisualScripting.StateGraphAsset]", "fieldInfos": []}, {"className": "Unity.VisualScripting.TriggerStateTransition", "fieldInfos": []}, {"className": "Unity.VisualScripting.GetStateGraph", "fieldInfos": []}, {"className": "Unity.VisualScripting.GetStateGraphs", "fieldInfos": []}, {"className": "Unity.VisualScripting.SetStateGraph", "fieldInfos": []}, {"className": "Unity.VisualScripting.StateGraphContainerType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}]}, {"name": "UnityEngine.UI", "path": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll", "types": [{"className": "UnityEngine.UI.AnimationTriggers", "fieldInfos": [{"name": "m_NormalTrigger", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HighlightedTrigger", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PressedTrigger", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SelectedTrigger", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DisabledTrigger", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Button", "fieldInfos": [{"name": "m_OnClick", "type": "UnityEngine.UI.Button+ButtonClickedEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Button+ButtonClickedEvent", "fieldInfos": []}, {"className": "UnityEngine.UI.Selectable", "fieldInfos": [{"name": "m_Navigation", "type": "UnityEngine.UI.Navigation", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Transition", "type": "UnityEngine.UI.Selectable+Transition", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Colors", "type": "UnityEngine.UI.ColorBlock", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SpriteState", "type": "UnityEngine.UI.SpriteState", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AnimationTriggers", "type": "UnityEngine.UI.AnimationTriggers", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Interactable", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_TargetGraphic", "type": "UnityEngine.UI.Graphic", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Selectable+Transition", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Selectable+SelectionState", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.EventSystems.UIBehaviour", "fieldInfos": []}, {"className": "UnityEngine.UI.CanvasUpdate", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.ICanvasElement", "fieldInfos": []}, {"className": "UnityEngine.UI.CanvasUpdateRegistry", "fieldInfos": []}, {"className": "UnityEngine.UI.ColorBlock", "fieldInfos": [{"name": "m_NormalColor", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HighlightedColor", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PressedColor", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SelectedColor", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DisabledColor", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ColorMultiplier", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FadeDuration", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.ClipperRegistry", "fieldInfos": []}, {"className": "UnityEngine.UI.Clipping", "fieldInfos": []}, {"className": "UnityEngine.UI.IClipper", "fieldInfos": []}, {"className": "UnityEngine.UI.IClippable", "fieldInfos": []}, {"className": "UnityEngine.UI.RectangularVertexClipper", "fieldInfos": []}, {"className": "UnityEngine.UI.DefaultControls", "fieldInfos": []}, {"className": "UnityEngine.UI.Dropdown", "fieldInfos": [{"name": "m_Template", "type": "UnityEngine.RectTransform", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CaptionText", "type": "UnityEngine.UI.Text", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CaptionImage", "type": "UnityEngine.UI.Image", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ItemText", "type": "UnityEngine.UI.Text", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ItemImage", "type": "UnityEngine.UI.Image", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Value", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Options", "type": "UnityEngine.UI.Dropdown+OptionDataList", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnValueChanged", "type": "UnityEngine.UI.Dropdown+DropdownEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AlphaFadeSpeed", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Dropdown+DropdownItem", "fieldInfos": [{"name": "m_Text", "type": "UnityEngine.UI.Text", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Image", "type": "UnityEngine.UI.Image", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_RectTransform", "type": "UnityEngine.RectTransform", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Toggle", "type": "UnityEngine.UI.Toggle", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Dropdown+OptionData", "fieldInfos": [{"name": "m_Text", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Image", "type": "UnityEngine.Sprite", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Dropdown+OptionDataList", "fieldInfos": [{"name": "m_Options", "type": "System.Collections.Generic.List`1[UnityEngine.UI.Dropdown+OptionData]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Dropdown+DropdownEvent", "fieldInfos": []}, {"className": "UnityEngine.UI.FontData", "fieldInfos": [{"name": "m_Font", "type": "UnityEngine.Font", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FontSize", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FontStyle", "type": "UnityEngine.FontStyle", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_BestFit", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MinSize", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MaxSize", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Alignment", "type": "UnityEngine.TextAnchor", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AlignByGeometry", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_RichText", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HorizontalOverflow", "type": "UnityEngine.HorizontalWrapMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_VerticalOverflow", "type": "UnityEngine.VerticalWrapMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_LineSpacing", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.FontUpdateTracker", "fieldInfos": []}, {"className": "UnityEngine.UI.Graphic", "fieldInfos": [{"name": "m_Material", "type": "UnityEngine.Material", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Color", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_RaycastTarget", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_RaycastPadding", "type": "UnityEngine.Vector4", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.GraphicRaycaster", "fieldInfos": [{"name": "m_IgnoreReversedGraphics", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_BlockingObjects", "type": "UnityEngine.UI.GraphicRaycaster+BlockingObjects", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_BlockingMask", "type": "UnityEngine.LayerMask", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.GraphicRaycaster+BlockingObjects", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.EventSystems.BaseRaycaster", "fieldInfos": []}, {"className": "UnityEngine.UI.GraphicRegistry", "fieldInfos": []}, {"className": "UnityEngine.UI.IGraphicEnabledDisabled", "fieldInfos": []}, {"className": "UnityEngine.UI.Image", "fieldInfos": [{"name": "m_Sprite", "type": "UnityEngine.Sprite", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Type", "type": "UnityEngine.UI.Image+Type", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PreserveAspect", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FillCenter", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FillMethod", "type": "UnityEngine.UI.Image+FillMethod", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FillAmount", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FillClockwise", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Fill<PERSON><PERSON><PERSON>", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_UseSpriteMesh", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PixelsPerUnitMultiplier", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Image+Type", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Image+FillMethod", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Image+OriginHorizontal", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Image+OriginVertical", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Image+Origin90", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Image+Origin180", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Image+Origin360", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.MaskableGraphic", "fieldInfos": [{"name": "m_Maskable", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnCullStateChanged", "type": "UnityEngine.UI.MaskableGraphic+CullStateChangedEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.MaskableGraphic+CullStateChangedEvent", "fieldInfos": []}, {"className": "UnityEngine.UI.IMask", "fieldInfos": []}, {"className": "UnityEngine.UI.IMaskable", "fieldInfos": []}, {"className": "UnityEngine.UI.InputField", "fieldInfos": [{"name": "m_TextComponent", "type": "UnityEngine.UI.Text", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Placeholder", "type": "UnityEngine.UI.Graphic", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ContentType", "type": "UnityEngine.UI.InputField+ContentType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_InputType", "type": "UnityEngine.UI.InputField+InputType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AsteriskChar", "type": "System.Char", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_KeyboardType", "type": "UnityEngine.TouchScreenKeyboardType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_LineType", "type": "UnityEngine.UI.InputField+LineType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HideMobileInput", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CharacterValidation", "type": "UnityEngine.UI.InputField+CharacterValidation", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CharacterLimit", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnSubmit", "type": "UnityEngine.UI.InputField+SubmitEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnDidEndEdit", "type": "UnityEngine.UI.InputField+EndEditEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnValueChanged", "type": "UnityEngine.UI.InputField+OnChangeEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CaretColor", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CustomCaretColor", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SelectionColor", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Text", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CaretBlinkRate", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_<PERSON>tWidth", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ReadOnly", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ShouldActivateOnSelect", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.InputField+ContentType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.InputField+InputType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.InputField+CharacterValidation", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.InputField+LineType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.InputField+OnValidateInput", "fieldInfos": []}, {"className": "UnityEngine.UI.InputField+SubmitEvent", "fieldInfos": []}, {"className": "UnityEngine.UI.InputField+EndEditEvent", "fieldInfos": []}, {"className": "UnityEngine.UI.InputField+OnChangeEvent", "fieldInfos": []}, {"className": "UnityEngine.UI.InputField+EditState", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.AspectRatioFitter", "fieldInfos": [{"name": "m_AspectMode", "type": "UnityEngine.UI.AspectRatioFitter+AspectMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_AspectRatio", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.AspectRatioFitter+AspectMode", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.CanvasScaler", "fieldInfos": [{"name": "m_UiScaleMode", "type": "UnityEngine.UI.CanvasScaler+ScaleMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ReferencePixelsPerUnit", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ScaleFactor", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ReferenceResolution", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ScreenMatchMode", "type": "UnityEngine.UI.CanvasScaler+ScreenMatchMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MatchWidthOrHeight", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PhysicalUnit", "type": "UnityEngine.UI.CanvasScaler+Unit", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FallbackScreenDPI", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DefaultSpriteDPI", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DynamicPixelsPerUnit", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PresetInfoIsWorld", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.CanvasScaler+ScaleMode", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.CanvasScaler+ScreenMatchMode", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.CanvasScaler+Unit", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.ContentSizeFitter", "fieldInfos": [{"name": "m_HorizontalFit", "type": "UnityEngine.UI.ContentSizeFitter+FitMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_VerticalFit", "type": "UnityEngine.UI.ContentSizeFitter+FitMode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.ContentSizeFitter+FitMode", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.GridLayoutGroup", "fieldInfos": [{"name": "m_<PERSON><PERSON>orner", "type": "UnityEngine.UI.GridLayoutGroup+Corner", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_StartAxis", "type": "UnityEngine.UI.GridLayoutGroup+Axis", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CellSize", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Spacing", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Constraint", "type": "UnityEngine.UI.GridLayoutGroup+Constraint", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ConstraintCount", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.GridLayoutGroup+Corner", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.GridLayoutGroup+Axis", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.GridLayoutGroup+Constraint", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.LayoutGroup", "fieldInfos": [{"name": "m_Padding", "type": "UnityEngine.RectOffset", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ChildAlignment", "type": "UnityEngine.TextAnchor", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.HorizontalLayoutGroup", "fieldInfos": []}, {"className": "UnityEngine.UI.HorizontalOrVerticalLayoutGroup", "fieldInfos": [{"name": "m_Spacing", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ChildForceExpandWidth", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ChildForceExpandHeight", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ChildControl<PERSON>idth", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ChildControlHeight", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ChildScaleWidth", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ChildScaleHeight", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ReverseArrangement", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.ILayoutElement", "fieldInfos": []}, {"className": "UnityEngine.UI.ILayoutController", "fieldInfos": []}, {"className": "UnityEngine.UI.ILayoutGroup", "fieldInfos": []}, {"className": "UnityEngine.UI.ILayoutSelfController", "fieldInfos": []}, {"className": "UnityEngine.UI.ILayoutIgnorer", "fieldInfos": []}, {"className": "UnityEngine.UI.LayoutElement", "fieldInfos": [{"name": "m_IgnoreLayout", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_<PERSON><PERSON><PERSON><PERSON>", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_<PERSON><PERSON><PERSON>ght", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PreferredWidth", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PreferredHeight", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FlexibleWidth", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_FlexibleHeight", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_LayoutPriority", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.LayoutRebuilder", "fieldInfos": []}, {"className": "UnityEngine.UI.LayoutUtility", "fieldInfos": []}, {"className": "UnityEngine.UI.VerticalLayoutGroup", "fieldInfos": []}, {"className": "UnityEngine.UI.Mask", "fieldInfos": [{"name": "m_ShowMaskGraphic", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.MaskUtilities", "fieldInfos": []}, {"className": "UnityEngine.UI.IMaterialModifier", "fieldInfos": []}, {"className": "UnityEngine.UI.Misc", "fieldInfos": []}, {"className": "UnityEngine.UI.MultipleDisplayUtilities", "fieldInfos": []}, {"className": "UnityEngine.UI.Navigation", "fieldInfos": [{"name": "m_Mode", "type": "UnityEngine.UI.Navigation+Mode", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_WrapAround", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SelectOnUp", "type": "UnityEngine.UI.Selectable", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SelectOnDown", "type": "UnityEngine.UI.Selectable", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SelectOnLeft", "type": "UnityEngine.UI.Selectable", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SelectOnRight", "type": "UnityEngine.UI.Selectable", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Navigation+Mode", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.RawImage", "fieldInfos": [{"name": "m_Texture", "type": "UnityEngine.Texture", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_UVRect", "type": "UnityEngine.Rect", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.RectMask2D", "fieldInfos": [{"name": "m_Padding", "type": "UnityEngine.Vector4", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Softness", "type": "UnityEngine.Vector2Int", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Scrollbar", "fieldInfos": [{"name": "m_HandleRect", "type": "UnityEngine.RectTransform", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Direction", "type": "UnityEngine.UI.Scrollbar+Direction", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Value", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Size", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_NumberOfSteps", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnValueChanged", "type": "UnityEngine.UI.Scrollbar+ScrollEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Scrollbar+Direction", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Scrollbar+ScrollEvent", "fieldInfos": []}, {"className": "UnityEngine.UI.Scrollbar+Axis", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.ScrollRect", "fieldInfos": [{"name": "m_Content", "type": "UnityEngine.RectTransform", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Horizontal", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Vertical", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MovementType", "type": "UnityEngine.UI.ScrollRect+MovementType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Elasticity", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Inertia", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DecelerationRate", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ScrollSensitivity", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Viewport", "type": "UnityEngine.RectTransform", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HorizontalScrollbar", "type": "UnityEngine.UI.Scrollbar", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_VerticalScrollbar", "type": "UnityEngine.UI.Scrollbar", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HorizontalScrollbarVisibility", "type": "UnityEngine.UI.ScrollRect+ScrollbarVisibility", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_VerticalScrollbarVisibility", "type": "UnityEngine.UI.ScrollRect+ScrollbarVisibility", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HorizontalScrollbarSpacing", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_VerticalScrollbarSpacing", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnValueChanged", "type": "UnityEngine.UI.ScrollRect+ScrollRectEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.ScrollRect+MovementType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.ScrollRect+ScrollbarVisibility", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.ScrollRect+ScrollRectEvent", "fieldInfos": []}, {"className": "UnityEngine.UI.SetPropertyUtility", "fieldInfos": []}, {"className": "UnityEngine.UI.Slider", "fieldInfos": [{"name": "m_FillRect", "type": "UnityEngine.RectTransform", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_HandleRect", "type": "UnityEngine.RectTransform", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Direction", "type": "UnityEngine.UI.Slider+Direction", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MinValue", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MaxValue", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_WholeNumbers", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Value", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_OnValueChanged", "type": "UnityEngine.UI.Slider+SliderEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Slider+Direction", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Slider+SliderEvent", "fieldInfos": []}, {"className": "UnityEngine.UI.Slider+Axis", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.SpriteState", "fieldInfos": [{"name": "m_HighlightedSprite", "type": "UnityEngine.Sprite", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_PressedSprite", "type": "UnityEngine.Sprite", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SelectedSprite", "type": "UnityEngine.Sprite", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DisabledSprite", "type": "UnityEngine.Sprite", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.StencilMaterial", "fieldInfos": []}, {"className": "UnityEngine.UI.Text", "fieldInfos": [{"name": "m_FontData", "type": "UnityEngine.UI.FontData", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Text", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Toggle", "fieldInfos": [{"name": "toggleTransition", "type": "UnityEngine.UI.Toggle+ToggleTransition", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "graphic", "type": "UnityEngine.UI.Graphic", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Group", "type": "UnityEngine.UI.ToggleGroup", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "onValueChanged", "type": "UnityEngine.UI.Toggle+ToggleEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_IsOn", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Toggle+ToggleTransition", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.Toggle+ToggleEvent", "fieldInfos": []}, {"className": "UnityEngine.UI.ToggleGroup", "fieldInfos": [{"name": "m_AllowSwitchOff", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.ReflectionMethodsCache", "fieldInfos": []}, {"className": "UnityEngine.UI.VertexHelper", "fieldInfos": []}, {"className": "UnityEngine.UI.BaseVertexEffect", "fieldInfos": []}, {"className": "UnityEngine.UI.BaseMeshEffect", "fieldInfos": []}, {"className": "UnityEngine.UI.IVertexModifier", "fieldInfos": []}, {"className": "UnityEngine.UI.IMeshModifier", "fieldInfos": []}, {"className": "UnityEngine.UI.Outline", "fieldInfos": []}, {"className": "UnityEngine.UI.Shadow", "fieldInfos": [{"name": "m_EffectColor", "type": "UnityEngine.Color", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_EffectDistance", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_UseGraphicAlpha", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UI.PositionAsUV1", "fieldInfos": []}, {"className": "UnityEngine.UI.CoroutineTween.ITweenValue", "fieldInfos": []}, {"className": "UnityEngine.UI.CoroutineTween.ColorTween", "fieldInfos": []}, {"className": "UnityEngine.UI.CoroutineTween.FloatTween", "fieldInfos": []}, {"className": "UnityEngine.UIElements.PanelEventHandler", "fieldInfos": []}, {"className": "UnityEngine.UIElements.PanelEventHandler+PointerEventType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.UIElements.PanelEventHandler+PointerEvent", "fieldInfos": []}, {"className": "UnityEngine.UIElements.PanelRaycaster", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.AxisEventData", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.BaseEventData", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.AbstractEventData", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.PointerEventData", "fieldInfos": [{"name": "hovered", "type": "System.Collections.Generic.List`1[UnityEngine.GameObject]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.EventSystems.EventHandle", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.EventSystems.IEventSystemHandler", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.IPointerMoveHandler", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.IPointerEnterHandler", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.IPointerExitHandler", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.IPointerDownHandler", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.IPointerUpHandler", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.IPointerClickHandler", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.IBeginDragHandler", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.IInitializePotentialDragHandler", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.IDragHandler", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.IEndDragHandler", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.IDropHandler", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.IScrollHandler", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.IUpdateSelectedHandler", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.ISelectHandler", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.IDeselectHandler", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.IMoveHandler", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.ISubmitHandler", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.ICancelHandler", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.EventSystem", "fieldInfos": [{"name": "m_FirstSelected", "type": "UnityEngine.GameObject", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_sendNavigationEvents", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_DragT<PERSON><PERSON><PERSON>", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.EventSystems.EventSystem+UIToolkitOverrideConfig", "fieldInfos": [{"name": "activeEventSystem", "type": "UnityEngine.EventSystems.EventSystem", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "sendEvents", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "createPanelGameObjectsOnStart", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.EventSystems.EventTrigger", "fieldInfos": [{"name": "m_Delegates", "type": "System.Collections.Generic.List`1[UnityEngine.EventSystems.EventTrigger+Entry]", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.EventSystems.EventTrigger+TriggerEvent", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.EventTrigger+Entry", "fieldInfos": [{"name": "eventID", "type": "UnityEngine.EventSystems.EventTriggerType", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "callback", "type": "UnityEngine.EventSystems.EventTrigger+TriggerEvent", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.EventSystems.EventTriggerType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.EventSystems.ExecuteEvents", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.BaseInput", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.BaseInputModule", "fieldInfos": [{"name": "m_SendPointerHoverToParent", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.EventSystems.PointerInputModule", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.PointerInputModule+ButtonState", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.PointerInputModule+MouseState", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.PointerInputModule+MouseButtonEventData", "fieldInfos": [{"name": "buttonState", "type": "UnityEngine.EventSystems.PointerEventData+FramePressState", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.EventSystems.StandaloneInputModule", "fieldInfos": [{"name": "m_HorizontalAxis", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_VerticalAxis", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_SubmitButton", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_CancelButton", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_InputActionsPerSecond", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_Repeat<PERSON><PERSON>y", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_ForceModuleActive", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.EventSystems.StandaloneInputModule+InputMode", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.EventSystems.TouchInputModule", "fieldInfos": [{"name": "m_ForceModuleActive", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.EventSystems.MoveDirection", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.EventSystems.RaycasterManager", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.Physics2DRaycaster", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.PhysicsRaycaster", "fieldInfos": [{"name": "m_EventMask", "type": "UnityEngine.LayerMask", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "m_MaxRayIntersections", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "UnityEngine.EventSystems.PhysicsRaycaster+RaycastHitComparer", "fieldInfos": []}, {"className": "UnityEngine.EventSystems.RaycastResult", "fieldInfos": [{"name": "module", "type": "UnityEngine.EventSystems.BaseRaycaster", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "distance", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "index", "type": "System.Single", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "depth", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "sortingGroupID", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "sortingGroupOrder", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "<PERSON><PERSON>ayer", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "sortingOrder", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "worldPosition", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "worldNormal", "type": "UnityEngine.Vector3", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "screenPosition", "type": "UnityEngine.Vector2", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "displayIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}]}, {"name": "nunit.framework", "path": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\PackageCache\\com.unity.ext.nunit\\net40\\unity-custom\\nunit.framework.dll", "types": [{"className": "NUnit.FrameworkPackageSettings", "fieldInfos": []}, {"className": "NUnit.Env", "fieldInfos": []}, {"className": "NUnit.Compatibility.AttributeHelper", "fieldInfos": []}, {"className": "NUnit.Compatibility.LongLivedMarshalByRefObject", "fieldInfos": []}, {"className": "NUnit.Compatibility.TypeExtensions", "fieldInfos": []}, {"className": "NUnit.Compatibility.AssemblyExtensions", "fieldInfos": []}, {"className": "NUnit.Compatibility.AdditionalTypeExtensions", "fieldInfos": []}, {"className": "NUnit.Compatibility.NUnitNullType", "fieldInfos": []}, {"className": "NUnit.Framework.ActionTargets", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "NUnit.Framework.Assert", "fieldInfos": []}, {"className": "NUnit.Framework.TestDelegate", "fieldInfos": []}, {"className": "NUnit.Framework.AsyncTestDelegate", "fieldInfos": []}, {"className": "NUnit.Framework.AssertionHelper", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.ConstraintFactory", "fieldInfos": []}, {"className": "NUnit.Framework.Assume", "fieldInfos": []}, {"className": "NUnit.Framework.ApartmentAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.PropertyAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.NUnitAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.AuthorAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.CategoryAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.CombinatorialAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.CombiningStrategyAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.CultureAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.IncludeExcludeAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.DataAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.DatapointAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.DatapointsAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.DatapointSourceAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.DescriptionAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.ExplicitAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.IgnoreAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.SingleThreadedAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.TestAssemblyDirectoryResolveAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.LevelOfParallelismAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.MaxTimeAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.OneTimeSetUpAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.OneTimeTearDownAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.OrderAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.PairwiseAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.ParallelizableAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.ParallelScope", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "NUnit.Framework.PlatformAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.RandomAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.RangeAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.ValuesAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.RepeatAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.RequiresMTAAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.RequiresSTAAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.RequiresThreadAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.RetryAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.SequentialAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.SetCultureAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.SetUICultureAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.SetUpAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.PreTestAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.PostTestAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.SetUpFixtureAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.TearDownAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.TestActionAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.TestAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.TestCaseAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.TestCaseSourceAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.TestFixtureAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.TestFixtureSetUpAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.TestFixtureSourceAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.TestFixtureTearDownAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.TestOfAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.TheoryAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.TimeoutAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.ValueSourceAttribute", "fieldInfos": []}, {"className": "NUnit.Framework.CollectionAssert", "fieldInfos": []}, {"className": "NUnit.Framework.Contains", "fieldInfos": []}, {"className": "NUnit.Framework.DirectoryAssert", "fieldInfos": []}, {"className": "NUnit.Framework.Does", "fieldInfos": []}, {"className": "NUnit.Framework.AssertionException", "fieldInfos": []}, {"className": "NUnit.Framework.ResultStateException", "fieldInfos": []}, {"className": "NUnit.Framework.IgnoreException", "fieldInfos": []}, {"className": "NUnit.Framework.InconclusiveException", "fieldInfos": []}, {"className": "NUnit.Framework.SuccessException", "fieldInfos": []}, {"className": "NUnit.Framework.FileAssert", "fieldInfos": []}, {"className": "NUnit.Framework.GlobalSettings", "fieldInfos": []}, {"className": "NUnit.Framework.Guard", "fieldInfos": []}, {"className": "NUnit.Framework.Has", "fieldInfos": []}, {"className": "NUnit.Framework.Is", "fieldInfos": []}, {"className": "NUnit.Framework.ITestAction", "fieldInfos": []}, {"className": "NUnit.Framework.Iz", "fieldInfos": []}, {"className": "NUnit.Framework.List", "fieldInfos": []}, {"className": "NUnit.Framework.ListMapper", "fieldInfos": []}, {"className": "NUnit.Framework.SpecialValue", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "NUnit.Framework.StringAssert", "fieldInfos": []}, {"className": "NUnit.Framework.TestCaseData", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.TestCaseParameters", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.TestParameters", "fieldInfos": []}, {"className": "NUnit.Framework.TestContext", "fieldInfos": []}, {"className": "NUnit.Framework.TestFixtureData", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.TestFixtureParameters", "fieldInfos": []}, {"className": "NUnit.Framework.TestParameters", "fieldInfos": []}, {"className": "NUnit.Framework.Throws", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.ActionsHelper", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.AssemblyHelper", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.AsyncInvocationRegion", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.GenericMethodHelper", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.InvalidDataSourceException", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.CultureDetector", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.ExceptionHelper", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.TextMessageWriter", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.MessageWriter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.InvalidTestFixtureException", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.ILogger", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.InternalTrace", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.InternalTraceLevel", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "NUnit.Framework.Internal.InternalTraceWriter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Logger", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.MethodWrapper", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.NUnitException", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.OSPlatform", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.ParameterWrapper", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.PlatformHelper", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.PropertyBag", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.PropertyNames", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Randomizer", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Reflect", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.TestResult", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.RuntimeType", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "NUnit.Framework.Internal.RuntimeFramework", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.StackFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.StringUtil", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.ITestExecutionContext", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.TestExecutionContext", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.TestExecutionStatus", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "NUnit.Framework.Internal.TestFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.TestFilter+EmptyFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.TestListener", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.TestNameGenerator", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.TestProgressReporter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.ParameterizedFixtureSuite", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.TestSuite", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Test", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.ParameterizedMethodSuite", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.SetUpFixture", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.TestAssembly", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.TestFixture", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.TestMethod", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.ThreadUtility", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.TypeHelper", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.TypeWrapper", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.TestCaseResult", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.TestSuiteResult", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Filters.ClassNameFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Filters.ValueMatchFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Filters.CompositeFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Filters.FullNameFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Filters.MethodNameFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Filters.PropertyFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Filters.TestNameFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Filters.AndFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Filters.CategoryFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Filters.IdFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Filters.NotFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Filters.OrFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Execution.EventListenerTextWriter", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Execution.CommandBuilder", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Execution.CompositeWorkItem", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Execution.WorkItem", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Execution.IWorkItemDispatcher", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Execution.SimpleWorkItem", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Execution.SimpleWorkItemDispatcher", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Execution.TextCapture", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Execution.WorkItemState", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "NUnit.Framework.Internal.Commands.OneTimeSetUpCommand", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Commands.TestCommand", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Commands.OneTimeTearDownCommand", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Commands.ApplyChangesToContextCommand", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Commands.DelegatingTestCommand", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Commands.CommandStage", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "NUnit.Framework.Internal.Commands.MaxTimeCommand", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Commands.SetUpTearDownCommand", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Commands.SetUpTearDownItem", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Commands.SkipCommand", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Commands.TestActionCommand", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Commands.TestActionItem", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Commands.TestMethodCommand", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Commands.TheoryResultCommand", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Builders.CombinatorialStrategy", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Builders.DatapointProvider", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Builders.DefaultSuiteBuilder", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Builders.DefaultTestCaseBuilder", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Builders.NamespaceTreeBuilder", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Builders.NUnitTestCaseBuilder", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Builders.NUnitTestFixtureBuilder", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Builders.PairwiseStrategy", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Builders.ParameterDataProvider", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Builders.ParameterDataSourceProvider", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Builders.ProviderCache", "fieldInfos": []}, {"className": "NUnit.Framework.Internal.Builders.SequentialStrategy", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.IApplyToContext", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.IApplyToTest", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.ICommandWrapper", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.IWrapTestMethod", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.IWrapSetUpTearDown", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.IDisposableFixture", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.IFixtureBuilder", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.IImplyFixture", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.IMethodInfo", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.IParameterDataProvider", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.IParameterDataSource", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.IParameterInfo", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.IPropertyBag", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.IReflectionInfo", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.ISimpleTestBuilder", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.ISuiteBuilder", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.ITest", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.ITestCaseBuilder", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.ITestCaseData", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.ITestData", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.ITestFilter", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.ITestFixtureData", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.ITestListener", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.ITestBuilder", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.ITestResult", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.ITypeInfo", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.IXmlNodeBuilder", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.ResultState", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.FailureSite", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "NUnit.Framework.Interfaces.RunState", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "NUnit.Framework.Interfaces.TestOutput", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.TestStatus", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "NUnit.Framework.Interfaces.TNode", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.NodeList", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.AttributeDictionary", "fieldInfos": []}, {"className": "NUnit.Framework.Interfaces.ICombiningStrategy", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.AllItemsConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.PrefixConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.Constraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.AndConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.BinaryConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.AssignableFromConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.TypeConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.AssignableToConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.AttributeConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.AttributeExistsConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.BinarySerializableConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.CollectionConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.CollectionContainsConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.CollectionItemsEqualConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.CollectionEquivalentConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.CollectionOrderedConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.CollectionSubsetConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.CollectionSupersetConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.CollectionTally", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.ComparisonAdapter", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.ComparisonConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.ConstraintBuilder", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.ConstraintExpression", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.ConstraintStatus", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "NUnit.Framework.Constraints.ConstraintResult", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.ContainsConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.DelayedConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.DictionaryContainsKeyConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.DictionaryContainsValueConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.EmptyCollectionConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.EmptyConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.EmptyDirectoryConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.EmptyStringConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.StringConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.EndsWithConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.EqualConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.EqualityAdapter", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.ExactTypeConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.ExceptionNotThrownConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.FalseConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.FileExistsConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.FileOrDirectoryExistsConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.FloatingPointNumerics", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.GreaterThanConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.GreaterThanOrEqualConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.IConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.InstanceOfTypeConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.IResolveConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.LessThanConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.LessThanOrEqualConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.ValueFormatter", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.ValueFormatterFactory", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.MsgUtils", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.NaNConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.NoItemConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.NotConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.NullConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.Numerics", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.NUnitComparer", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.NUnitEqualityComparer", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.AllOperator", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.CollectionOperator", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.PrefixOperator", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.ConstraintOperator", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.AndOperator", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.BinaryOperator", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.AttributeOperator", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.SelfResolvingOperator", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.NoneOperator", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.NotOperator", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.OrOperator", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.PropOperator", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.SomeOperator", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.ThrowsOperator", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.WithOperator", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.OrConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.PathConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.PropertyConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.PropertyExistsConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.RangeConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.RegexConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.ResolvableConstraintExpression", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.ReusableConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.SameAsConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.SamePathConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.SamePathOrUnderConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.SomeItemsConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.EqualConstraintResult", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.StartsWithConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.SubPathConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.SubstringConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.ThrowsConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.ThrowsExceptionConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.ThrowsNothingConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.Tolerance", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.ToleranceMode", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "NUnit.Framework.Constraints.TrueConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.UniqueItemsConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.XmlSerializableConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.ExactCountConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.ExactCountOperator", "fieldInfos": []}, {"className": "NUnit.Framework.Constraints.ExceptionTypeConstraint", "fieldInfos": []}, {"className": "NUnit.Framework.Api.DefaultTestAssemblyBuilder", "fieldInfos": []}, {"className": "NUnit.Framework.Api.FrameworkController", "fieldInfos": []}, {"className": "NUnit.Framework.Api.ITestAssemblyBuilder", "fieldInfos": []}, {"className": "NUnit.Framework.Api.ITestAssemblyRunner", "fieldInfos": []}, {"className": "NUnit.Framework.Api.NUnitTestAssemblyRunner", "fieldInfos": []}]}, {"name": "Unity.VisualScripting.Antlr3.Runtime", "path": "D:\\My Project\\Driving Simulator Game Z TEC\\Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll", "types": [{"className": "Unity.VisualScripting.Antlr3.Runtime.Collections.HashList", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Tree.ITreeVisitorAction", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Misc.ErrorManager", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Tree.ITreeAdaptor", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.IToken", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.ITokenStream", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.RecognitionException", "fieldInfos": [{"name": "approximateLineInfo", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Tree.BaseTreeAdaptor", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.CommonErrorNode", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Tree.CommonTree", "fieldInfos": [{"name": "startIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "stopIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "parent", "type": "Unity.VisualScripting.Antlr3.Runtime.Tree.CommonTree", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "childIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Tree.BaseTree", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Tree.ITree", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Tree.CommonTreeAdaptor", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.CommonToken", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.DFA", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.IIntStream", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.BaseRecognizer", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Token", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.NoViableAltException", "fieldInfos": [{"name": "grammarDecisionDescription", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "decisionNumber", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "stateNumber", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.Antlr3.Runtime.RecognizerSharedState", "fieldInfos": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "errorRecovery", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "lastErrorIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "failed", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "syntaxErrors", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "backtracking", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "tokenStartCharIndex", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "tokenStartLine", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "tokenStartCharPositionInLine", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "channel", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "type", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "text", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.Antlr3.Runtime.BitSet", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Tree.Tree", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Tree.RewriteEmptyStreamException", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Tree.RewriteCardinalityException", "fieldInfos": [{"name": "elementDescription", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Tree.RewriteRuleNodeStream", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Tree.RewriteRuleElementStream`1[System.Object]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Tree.ITreeNodeStream", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.ICharStream", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.MismatchedTokenException", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.MissingTokenException", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.MismatchedTreeNodeException", "fieldInfos": [{"name": "expecting", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.Antlr3.Runtime.MismatchedSetException", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.EarlyExitException", "fieldInfos": [{"name": "decisionNumber", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.Antlr3.Runtime.UnwantedTokenException", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.MismatchedNotSetException", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.FailedPredicateException", "fieldInfos": [{"name": "ruleName", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}, {"name": "predicateText", "type": "System.String", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.Antlr3.Runtime.ITokenSource", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Lexer", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.MismatchedRangeException", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.ANTLRStringStream", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.CharStreamState", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.ANTLRReaderStream", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.ANTLRInputStream", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Tree.TreeWizard", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Tree.TreePatternLexer", "fieldInfos": [{"name": "error", "type": "System.Boolean", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Tree.TreePatternParser", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.RuleReturnScope", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Parser", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.CharStreamConstants", "fieldInfos": [{"name": "value__", "type": "System.Int32", "flags": {"value__": 0}, "fixedBufferLength": 0, "fixedBufferTypename": null}]}, {"className": "Unity.VisualScripting.Antlr3.Runtime.ANTLRFileStream", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Constants", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.ClassicToken", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Tree.UnBufferedTreeNodeStream", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Collections.StackList", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Tree.CommonTreeNodeStream", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Collections.CollectionUtils", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.ParserRuleReturnScope", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.SynPredPointer", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Tree.RewriteEarlyExitException", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Misc.Stats", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Tree.TreeVisitor", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.CommonTokenStream", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.TokenRewriteStream", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Tree.RewriteRuleSubtreeStream", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Tree.TreeRuleReturnScope", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Tree.TreeParser", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Tree.RewriteRuleTokenStream", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Tree.RewriteRuleElementStream`1[Unity.VisualScripting.Antlr3.Runtime.IToken]", "fieldInfos": []}, {"className": "Unity.VisualScripting.Antlr3.Runtime.Tree.ParseTree", "fieldInfos": []}]}]}
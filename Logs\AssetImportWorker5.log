Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.30f1 (62b05ba0686a) revision 6467675'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker5
-projectPath
D:/My Project/Driving Simulator Game Z TEC
-logFile
Logs/AssetImportWorker5.log
-srvPort
53742
-job-worker-count
11
-background-job-worker-count
8
-asset-garbage-collectors
1
Successfully changed project path to: D:/My Project/Driving Simulator Game Z TEC
D:/My Project/Driving Simulator Game Z TEC
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [26988]  Target information:

Player connection [26988]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 970196966 [EditorId] 970196966 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [26988]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 970196966 [EditorId] 970196966 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [26988] Host joined multi-casting on [***********:54997]...
Player connection [26988] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 11
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2.11 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.30f1 (62b05ba0686a)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Driving Simulator Game Z TEC/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56664
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.006974 seconds.
- Loaded All Assemblies, in  0.763 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 393 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.092 seconds
Domain Reload Profiling: 1852ms
	BeginReloadAssembly (283ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (2ms)
	RebuildCommonClasses (66ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (88ms)
	LoadAllAssembliesAndSetupDomain (299ms)
		LoadAssemblies (276ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (292ms)
			TypeCache.Refresh (290ms)
				TypeCache.ScanAssembly (266ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1093ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1006ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (557ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (90ms)
			ProcessInitializeOnLoadAttributes (236ms)
			ProcessInitializeOnLoadMethodAttributes (117ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.477 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.16 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.032 seconds
Domain Reload Profiling: 2501ms
	BeginReloadAssembly (332ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (71ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (90ms)
	LoadAllAssembliesAndSetupDomain (950ms)
		LoadAssemblies (673ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (462ms)
			TypeCache.Refresh (361ms)
				TypeCache.ScanAssembly (328ms)
			BuildScriptInfoCaches (82ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1033ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (831ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (20ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (145ms)
			ProcessInitializeOnLoadAttributes (606ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 2.48 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 66 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4568 unused Assets / (2.0 MB). Loaded Objects now: 5129.
Memory consumption went from 117.5 MB to 115.5 MB.
Total: 27.657800 ms (FindLiveObjects: 2.255100 ms CreateObjectMapping: 1.430900 ms MarkObjects: 17.947000 ms  DeleteObjects: 6.017700 ms)

========================================================================
Received Import Request.
  Time since last request: 14576.703170 seconds.
  path: Assets/UnityTechnologies/EffectExamples/WeaponEffects/Prefabs/BulletImpactWoodEffect.prefab
  artifactKey: Guid(beb3acff7ab73944fb8f470f24d74ed3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/WeaponEffects/Prefabs/BulletImpactWoodEffect.prefab using Guid(beb3acff7ab73944fb8f470f24d74ed3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '58999980b38dcf641bc4adf989d5dd4f') in 1.0813397 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 24

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 3/_Prefabs/CargoWagon03_Blue.prefab
  artifactKey: Guid(602fc8a141d24154e8b4a8464aab9f42) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 3/_Prefabs/CargoWagon03_Blue.prefab using Guid(602fc8a141d24154e8b4a8464aab9f42) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3a06526581444d538f7dcf3544002d5f') in 0.7406627 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 228

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/_Prefabs/CargoWagon02_Orange.prefab
  artifactKey: Guid(4546adf1dbab5e94b87000d407c51745) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/_Prefabs/CargoWagon02_Orange.prefab using Guid(4546adf1dbab5e94b87000d407c51745) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '49029d652622907746d408224dfa09b9') in 0.4016291 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 244

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Models/CableCar_01_WoodenBench_Long.fbx
  artifactKey: Guid(00082eb95fb26c74b8dc27411516062a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Models/CableCar_01_WoodenBench_Long.fbx using Guid(00082eb95fb26c74b8dc27411516062a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd3abb12521662d7465f3559dbcde6a13') in 0.7524186 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000046 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Models/CableCar_01_Wheel.fbx
  artifactKey: Guid(5b5cc49049075c34097d4c2eee35c031) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Models/CableCar_01_Wheel.fbx using Guid(5b5cc49049075c34097d4c2eee35c031) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '99a95b06fbfdae5683f3dc82182e8dbc') in 0.6165872 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 3/_Prefabs/CargoWagon03_Green.prefab
  artifactKey: Guid(f2f9adebf2481a649885bb247305af13) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 3/_Prefabs/CargoWagon03_Green.prefab using Guid(f2f9adebf2481a649885bb247305af13) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '81ced314c0ae1e0c22135c695b888d44') in 0.380208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 228

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Container_12_19.fbx
  artifactKey: Guid(1aef85404f8432a428571327bc0fe078) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Container_12_19.fbx using Guid(1aef85404f8432a428571327bc0fe078) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6d183d7b708b16882b106eee0c90ce7e') in 0.1542715 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/_Prefabs/CargoWagon02_Orange_Stones.prefab
  artifactKey: Guid(4a827d06c765aae479d6c3db50c09816) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/_Prefabs/CargoWagon02_Orange_Stones.prefab using Guid(4a827d06c765aae479d6c3db50c09816) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '611a5ca14746ecb0f55cb57334aa6b18') in 0.6325805 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 255

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/_Prefabs/Parts/Benches/CableCar_01_Bench_Long.prefab
  artifactKey: Guid(3c2ae5fc3b49fda47a06c3c36298db6c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/_Prefabs/Parts/Benches/CableCar_01_Bench_Long.prefab using Guid(3c2ae5fc3b49fda47a06c3c36298db6c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '644e362525d4fffeb5ff84a2a6ae539c') in 0.5640928 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Misc/Button_01_Black.prefab
  artifactKey: Guid(c3029c0d956ab7f4d88fb3ebb595b18f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Misc/Button_01_Black.prefab using Guid(c3029c0d956ab7f4d88fb3ebb595b18f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5cf28ec85b367f800e0628f858b39313') in 0.1567875 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Models/CableCar_01_LOD2.fbx
  artifactKey: Guid(28f9b871318feca40a7e27f4eec8310f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Models/CableCar_01_LOD2.fbx using Guid(28f9b871318feca40a7e27f4eec8310f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b1c37f7a455c10f45de99cdf540f4696') in 2.0433669 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/_Prefabs/Parts/Doors/CableCar_01_Door.prefab
  artifactKey: Guid(40e7871de3aff2f4fbc150023b2ab2fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/_Prefabs/Parts/Doors/CableCar_01_Door.prefab using Guid(40e7871de3aff2f4fbc150023b2ab2fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '43cd9aea5c9dcfaf7f276026bc74bcc2') in 2.2839095 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 35

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/AdvancedHelicopterController/Models/AmmoPack/Custom_Model_Ammo.fbx
  artifactKey: Guid(e247174a94f534dffaa36c5090e18e18) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Models/AmmoPack/Custom_Model_Ammo.fbx using Guid(e247174a94f534dffaa36c5090e18e18) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'db998b54763a89271f998042e085c304') in 0.1370644 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 15

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Controls_Lever_01.fbx
  artifactKey: Guid(6997bb6764ec870478c6fab3418fa77a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Controls_Lever_01.fbx using Guid(6997bb6764ec870478c6fab3418fa77a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '790fb0baa3812db2db23503c8ce727ea') in 0.0421264 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Controls_Switch_01.fbx
  artifactKey: Guid(f23fa14aad2089643923b34b2b208978) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Controls_Switch_01.fbx using Guid(f23fa14aad2089643923b34b2b208978) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ad9a9f4fb5a539a38af4d10c73d8fc75') in 0.0300466 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Controls_LeverBase.fbx
  artifactKey: Guid(b31fcbfc314ae4a40844c24968645ec8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Controls_LeverBase.fbx using Guid(b31fcbfc314ae4a40844c24968645ec8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bfbf0e7937a5adfe0f499385e210b937') in 0.0294818 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000126 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/Couplers/DefaultJointAnchor.prefab
  artifactKey: Guid(23075f62419fc1b47b2ee8880fc8a164) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/Couplers/DefaultJointAnchor.prefab using Guid(23075f62419fc1b47b2ee8880fc8a164) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'be24d9e4d259d1b1cd071a1ab3c8993c') in 0.0029178 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Sinalization/DirectionIndicator_01.prefab
  artifactKey: Guid(82051744ef0446d46aab4d5f1a98c0d1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Sinalization/DirectionIndicator_01.prefab using Guid(82051744ef0446d46aab4d5f1a98c0d1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2f24d9ae51624a6a75116c947e13a455') in 0.20333 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 35

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/DirectionIndicator_01.fbx
  artifactKey: Guid(8f214bbd68ffa4049978c82af7aa0415) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/DirectionIndicator_01.fbx using Guid(8f214bbd68ffa4049978c82af7aa0415) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '66d998fd56131bea1af6fe2f8a15ed26') in 0.1452659 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/UnityTechnologies/EffectExamples/Misc Effects/Prefabs/DustMotesEffect.prefab
  artifactKey: Guid(5ca4c580b3888314581688fa46fb142d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/Misc Effects/Prefabs/DustMotesEffect.prefab using Guid(5ca4c580b3888314581688fa46fb142d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bda56ff6f6150964a4d31627f9051ee5') in 0.1494091 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/SFX/EngineSFX.prefab
  artifactKey: Guid(a1690ae9fbb37e84e86c9769ed62b20c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/SFX/EngineSFX.prefab using Guid(a1690ae9fbb37e84e86c9769ed62b20c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b8a3c8fd867f7f2174fd78f79025090b') in 0.0582542 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/UnityTechnologies/EffectExamples/Shared/Prefabs/Extinguish.prefab
  artifactKey: Guid(ec68cf3d27ad1bf48a4b4d3af84e4ded) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/Shared/Prefabs/Extinguish.prefab using Guid(ec68cf3d27ad1bf48a4b4d3af84e4ded) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f5e8c80c29cf7b6bfb6eb91aa3917c06') in 1.1725666 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 52

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Locomotive_01/Locomotive_01_LOD_1.fbx
  artifactKey: Guid(f89ecfa263cd2fb4785ee17c1619ceef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Locomotive_01/Locomotive_01_LOD_1.fbx using Guid(f89ecfa263cd2fb4785ee17c1619ceef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aad2168bf21bb21501c89abf76193135') in 1.4692828 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 23

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/AdvancedHelicopterController/Prefabs/Helicopter/HelicopterComponents/Particle/Particle_Helicopter_Smoke.prefab
  artifactKey: Guid(40cb42d3aab6b41a8aaee7c53bf5a3b1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Prefabs/Helicopter/HelicopterComponents/Particle/Particle_Helicopter_Smoke.prefab using Guid(40cb42d3aab6b41a8aaee7c53bf5a3b1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '600e977058c498a228fddf9eab66b634') in 0.0284138 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/SubwayCart_01_A.fbx
  artifactKey: Guid(503569a22e0992240a05f412cf03abc3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/SubwayCart_01_A.fbx using Guid(503569a22e0992240a05f412cf03abc3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '854cd060ad62d2e26f1ea29542ad2a35') in 0.5450428 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_CW_3_prefab.prefab
  artifactKey: Guid(324828001ac1faf448e87abac12498be) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_CW_3_prefab.prefab using Guid(324828001ac1faf448e87abac12498be) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c55ca23beccaec9bcee946104b8f74e1') in 0.4014377 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 64

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/AdvancedHelicopterController/Models/Missile/Missile.fbx
  artifactKey: Guid(716cb2237f07641dd8cf3f01f58329c0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Models/Missile/Missile.fbx using Guid(716cb2237f07641dd8cf3f01f58329c0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '61a714bde4fde6c9eb5ca28b40178556') in 0.0338582 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/TenderWagon_01_Black Variant.prefab
  artifactKey: Guid(ebd5c6a4e31fead47abdad510ce6c99e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/TenderWagon_01_Black Variant.prefab using Guid(ebd5c6a4e31fead47abdad510ce6c99e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c523435899e969c0ef25732cdf1b9fd0') in 3.7444759 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 479

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Parts/SFX/SteamLocomotiveEngineSFX.prefab
  artifactKey: Guid(db41ef250d633594bb34eaaedbe1501a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Parts/SFX/SteamLocomotiveEngineSFX.prefab using Guid(db41ef250d633594bb34eaaedbe1501a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2de3e7331fbd2fda2ddc080cbb3160d5') in 0.0546018 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_ACW_6_prefab.prefab
  artifactKey: Guid(7f3f0d21b03f0674bbf0ae75ac5595ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_ACW_6_prefab.prefab using Guid(7f3f0d21b03f0674bbf0ae75ac5595ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ba7f3b0c9585ee069c3682e5ea556e50') in 0.6641635 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 64

========================================================================
Received Import Request.
  Time since last request: 0.000105 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 1/_Prefabs/CargoWagon01_Orange.prefab
  artifactKey: Guid(8b880b54b7e45cc41992d90ccabf5cf3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 1/_Prefabs/CargoWagon01_Orange.prefab using Guid(8b880b54b7e45cc41992d90ccabf5cf3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5b898a26d758472bfbf34817e5acacae') in 0.5977332 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 279

========================================================================
Received Import Request.
  Time since last request: 0.000098 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/TenderWagon_01_Blue Variant.prefab
  artifactKey: Guid(04a51f12b8cd7614bbfbe8713ba47300) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/TenderWagon_01_Blue Variant.prefab using Guid(04a51f12b8cd7614bbfbe8713ba47300) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8d0d1d0809bc25e740fd7806a7664f20') in 3.5266739 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 479

========================================================================
Received Import Request.
  Time since last request: 0.000101 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Locomotive_01_White.prefab
  artifactKey: Guid(b60821af843b5f3458f52e43612f164c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Locomotive_01_White.prefab using Guid(b60821af843b5f3458f52e43612f164c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '44cd3628d9f65c1e5c40986e1424d6e9') in 4.5467869 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1216

========================================================================
Received Import Request.
  Time since last request: 42.925736 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Sample Trains/s_Train_Sample.prefab
  artifactKey: Guid(833360b08bd947042af5b0f6b1b7a013) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Sample Trains/s_Train_Sample.prefab using Guid(833360b08bd947042af5b0f6b1b7a013) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1ca5cff03f3eca3509c42b25d8bf8141') in 4.6645439 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4119

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Invector-3rdPersonController_LITE/Prefabs/ThirdPersonController_LITE.prefab
  artifactKey: Guid(fc9b148a2950aaf4cba8422632222c5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Invector-3rdPersonController_LITE/Prefabs/ThirdPersonController_LITE.prefab using Guid(fc9b148a2950aaf4cba8422632222c5d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2f776f7da2a57514ccaeb91948ad7ee5') in 1.3926507 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 253

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 1/_Prefabs/TrainSample_Addon1.prefab
  artifactKey: Guid(ac466ade8c1de8046ad0f8fdfaa27167) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 1/_Prefabs/TrainSample_Addon1.prefab using Guid(ac466ade8c1de8046ad0f8fdfaa27167) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cc66f380497b44f097697065dec7df83') in 4.470416 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1925

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/TrainWheelsSupport.fbx
  artifactKey: Guid(449af01333283a64898bbc40e4dfe055) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/TrainWheelsSupport.fbx using Guid(449af01333283a64898bbc40e4dfe055) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd7efcbc35d6cf0e3fa2defaefaa704f4') in 0.1518485 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Misc/TrainWheelsSupport.prefab
  artifactKey: Guid(5b18a0dcbef5b3546b1d8348e64f360a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Misc/TrainWheelsSupport.prefab using Guid(5b18a0dcbef5b3546b1d8348e64f360a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '32d0ba56d5ad38fcb8c83f7578dd42ed') in 0.1754852 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 42

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/Bumpers/TrainBumper_Big.prefab
  artifactKey: Guid(99fc39f25905ec946bdcbe9b67175952) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/Bumpers/TrainBumper_Big.prefab using Guid(99fc39f25905ec946bdcbe9b67175952) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fc1386d8cf4eb7d0eb91b9409393d74a') in 0.1030655 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0
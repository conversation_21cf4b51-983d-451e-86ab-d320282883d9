# Dronecamera Joystick Control Setup Guide

## 🎮 Features

✅ **Horizontal Camera Movement**: Joystick left/right se camera horizontally rotate hota hai  
✅ **Vertical Camera Movement**: Joystick up/down se camera vertically rotate hota hai  
✅ **Smooth Movement**: Camera movement smooth aur natural hai  
✅ **Movement Limits**: Camera rotation ko limit kar sakte hain  
✅ **Speed Control**: Horizontal aur vertical speed alag alag set kar sakte hain  

## 🛠️ Setup Instructions

### Step 1: Camera GameObject Setup
1. **Camera GameObject** select karein (jo aap control karna chahte hain)
2. **Add Component** → **Scripts** → **Dronecamera**
3. Script attach ho jayegi

### Step 2: Joystick Assignment
1. **Dronecamera component** mein **Joystick** field mein apna joystick drag karein
2. **Dronecameraa** field mein camera reference assign karein (optional)

### Step 3: Settings Configuration

#### Camera Movement Settings:
- **Horizontal Speed**: Horizontal movement ki speed (default: 2f)
- **Vertical Speed**: Vertical movement ki speed (default: 2f)  
- **Smooth Time**: Movement kitni smooth ho (default: 0.1f)

#### Movement Limits:
- **Limit Horizontal Rotation**: Horizontal rotation limit karna hai ya nahi
- **Min/Max Horizontal Angle**: Horizontal rotation ki limits
- **Limit Vertical Rotation**: Vertical rotation limit karna hai ya nahi (default: true)
- **Min/Max Vertical Angle**: Vertical rotation ki limits (default: -60° to 60°)

## 🎯 How It Works

### Joystick Input:
- **Left/Right**: Camera horizontally rotate hota hai (Y-axis)
- **Up/Down**: Camera vertically rotate hota hai (X-axis, inverted)

### Movement Logic:
```csharp
// Joystick input
float horizontalInput = joystick.Horizontal;
float verticalInput = joystick.Vertical;

// Camera rotation
currentHorizontalAngle += horizontalInput * horizontalSpeed * Time.deltaTime;
currentVerticalAngle += -verticalInput * verticalSpeed * Time.deltaTime;
```

## ⚙️ Customization Options

### Speed Adjustment:
- **Fast Movement**: horizontalSpeed = 5f, verticalSpeed = 5f
- **Slow Movement**: horizontalSpeed = 1f, verticalSpeed = 1f
- **No Smoothing**: smoothTime = 0f

### Rotation Limits:
- **Free Rotation**: limitHorizontalRotation = false, limitVerticalRotation = false
- **FPS Style**: limitVerticalRotation = true, minVerticalAngle = -90f, maxVerticalAngle = 90f
- **Limited Look**: minHorizontalAngle = -45f, maxHorizontalAngle = 45f

## 🎮 Usage Examples

### Example 1: Drone Camera
```
horizontalSpeed = 3f
verticalSpeed = 3f
smoothTime = 0.2f
limitVerticalRotation = true
minVerticalAngle = -80f
maxVerticalAngle = 80f
```

### Example 2: Security Camera
```
horizontalSpeed = 1f
verticalSpeed = 1f
smoothTime = 0.5f
limitHorizontalRotation = true
minHorizontalAngle = -90f
maxHorizontalAngle = 90f
```

### Example 3: Free Look Camera
```
horizontalSpeed = 4f
verticalSpeed = 4f
smoothTime = 0.1f
limitHorizontalRotation = false
limitVerticalRotation = false
```

## 🔧 Public Methods

Script mein ye methods available hain:

- `SetHorizontalSpeed(float speed)`: Runtime mein horizontal speed change karein
- `SetVerticalSpeed(float speed)`: Runtime mein vertical speed change karein  
- `ResetCameraRotation()`: Camera ko original position mein reset karein

## 📱 Testing

1. **Play Mode** mein jayen
2. **Joystick** move karein:
   - Left/Right → Camera horizontally rotate hoga
   - Up/Down → Camera vertically rotate hoga
3. **Settings adjust** karein agar movement fast/slow lagti hai

## 🎯 Tips

- **Smooth Time** kam rakhein fast response ke liye
- **Vertical Limits** use karein realistic camera movement ke liye
- **Speed values** 1-5 ke beech rakhein optimal experience ke liye
- **Different joysticks** ke liye different speeds try karein

Ye script completely separate hai aur kisi aur camera system ke saath interfere nahi karegi!

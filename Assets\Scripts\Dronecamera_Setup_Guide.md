# Dronecamera Joystick Control Setup Guide

## 🎮 Features

✅ **Left/Right Movement**: Joystick left/right se camera left/right move hota hai
✅ **Forward/Backward Movement**: Joystick up/down se camera forward/backward move hota hai
✅ **Height Control**: UI buttons se camera up/down height control
✅ **Smooth Movement**: Camera movement smooth aur natural hai
✅ **Movement Limits**: Camera position ko limit kar sakte hain
✅ **Speed Control**: Movement speed alag alag set kar sakte hain
✅ **Movement Types**: World space ya camera rotation ke relative movement

## 🛠️ Setup Instructions

### Step 1: Camera GameObject Setup
1. **Camera GameObject** select karein (jo aap control karna chahte hain)
2. **Add Component** → **Scripts** → **Dronecamera**
3. Script attach ho jayegi

### Step 2: Joystick Assignment
1. **Dronecamera component** mein **Joystick** field mein apna joystick drag karein
2. **Dronecameraa** field mein camera reference assign karein (optional)

### Step 3: Height Control Buttons Setup
1. **UI Canvas** create karein (agar already nahi hai)
2. **2 UI Buttons** create karein:
   - **Up Height Button**: "UP" text ke saath
   - **Down Height Button**: "DOWN" text ke saath
3. **Dronecamera component** mein buttons assign karein:
   - **Up Height Button** field mein up button drag karein
   - **Down Height Button** field mein down button drag karein

### Step 4: Settings Configuration

#### Camera Movement Settings:
- **Horizontal Speed**: Left/Right movement ki speed (default: 5f)
- **Vertical Speed**: Forward/Backward movement ki speed (default: 5f)
- **Height Speed**: Up/Down height movement ki speed (default: 3f)
- **Smooth Time**: Movement kitni smooth ho (default: 0.1f)

#### Movement Limits:
- **Limit Horizontal Movement**: X-axis movement limit karna hai ya nahi
- **Min/Max X**: X-axis movement ki limits
- **Limit Vertical Movement**: Z-axis movement limit karna hai ya nahi
- **Min/Max Z**: Z-axis movement ki limits
- **Limit Height Movement**: Y-axis height movement limit karna hai ya nahi (default: true)
- **Min/Max Y**: Y-axis height movement ki limits (default: 0.5f to 50f)

#### Movement Type:
- **Move Relative To Rotation**: Camera ke rotation ke relative move kare ya world space mein

## 🎯 How It Works

### Joystick Input:
- **Left/Right**: Camera left/right move hota hai (X-axis)
- **Up/Down**: Camera forward/backward move hota hai (Z-axis)

### UI Button Input:
- **Up Height Button**: Camera upar move hota hai (Y-axis positive)
- **Down Height Button**: Camera neeche move hota hai (Y-axis negative)

### Movement Logic:
```csharp
// Joystick input
float horizontalInput = joystick.Horizontal; // Left/Right
float verticalInput = joystick.Vertical;     // Forward/Backward

// Camera movement
Vector3 moveDirection = transform.right * horizontalInput + transform.forward * verticalInput;
targetPosition += moveDirection * horizontalSpeed * Time.deltaTime;
```

## ⚙️ Customization Options

### Speed Adjustment:
- **Fast Movement**: horizontalSpeed = 5f, verticalSpeed = 5f
- **Slow Movement**: horizontalSpeed = 1f, verticalSpeed = 1f
- **No Smoothing**: smoothTime = 0f

### Rotation Settings (NEW!):
- **Enable Rotation**: enableMouseDragRotation = true
- **Mouse Sensitivity**: mouseSensitivity = 2f (PC)
- **Touch Sensitivity**: touchSensitivity = 1.5f (Mobile)
- **Smooth Rotation**: rotationSmoothTime = 0.1f

### UI Exclusion Zones (Mobile-Friendly):
- **Use Exclusion Zones**: useUIExclusionZones = true
- **Bottom Zone**: bottomUIZone = 0.2f (Height buttons area)
- **Top Zone**: topUIZone = 0.1f (Menu buttons area)
- **Left Zone**: leftUIZone = 0.15f (Joystick area)
- **Right Zone**: rightUIZone = 0.15f (Other controls area)

```
Mobile Screen Layout:
┌─────────────────────────────────┐ ← Top UI Zone (10%)
│           MENU AREA             │
├─────────────────────────────────┤
│ L │                         │ R │ ← Left/Right UI Zones (15% each)
│ E │     ROTATION AREA       │ I │
│ F │    (Touch + Drag)       │ G │
│ T │                         │ H │
│   │                         │ T │
├─────────────────────────────────┤
│        HEIGHT BUTTONS           │ ← Bottom UI Zone (20%)
└─────────────────────────────────┘
```

### Rotation Limits:
- **Free Rotation**: limitHorizontalRotation = false, limitVerticalRotation = false
- **FPS Style**: limitVerticalRotation = true, minVerticalAngle = -90f, maxVerticalAngle = 90f
- **Limited Look**: minHorizontalAngle = -45f, maxHorizontalAngle = 45f

## 🎮 Usage Examples

### Example 1: Free Movement Camera
```
horizontalSpeed = 5f
verticalSpeed = 5f
heightSpeed = 3f
smoothTime = 0.2f
moveRelativeToRotation = true
limitHorizontalMovement = false
limitVerticalMovement = false
limitHeightMovement = true
minY = 0.5f, maxY = 100f
```

### Example 2: Limited Area Camera
```
horizontalSpeed = 3f
verticalSpeed = 3f
heightSpeed = 2f
smoothTime = 0.3f
moveRelativeToRotation = false
limitHorizontalMovement = true
minX = -20f, maxX = 20f
limitVerticalMovement = true
minZ = -20f, maxZ = 20f
limitHeightMovement = true
minY = 1f, maxY = 20f
```

### Example 3: Fast Drone Camera with Rotation
```
horizontalSpeed = 8f
verticalSpeed = 8f
heightSpeed = 5f
smoothTime = 0.1f
enableMouseDragRotation = true
mouseSensitivity = 3f
touchSensitivity = 2f
rotationSmoothTime = 0.05f
useUIExclusionZones = true
bottomUIZone = 0.25f
leftUIZone = 0.2f
limitVerticalRotation = true
minVerticalAngle = -90f, maxVerticalAngle = 90f
moveRelativeToRotation = true
limitHorizontalMovement = false
limitVerticalMovement = false
limitHeightMovement = true
minY = 0.5f, maxY = 200f
```

## 🔧 Public Methods

Script mein ye methods available hain:

- `SetHorizontalSpeed(float speed)`: Runtime mein horizontal speed change karein
- `SetVerticalSpeed(float speed)`: Runtime mein vertical speed change karein
- `SetHeightSpeed(float speed)`: Runtime mein height speed change karein
- `ResetCameraPosition()`: Camera ko original position mein reset karein
- `SetCameraPosition(Vector3 newPosition)`: Camera ko specific position par set karein
- `MoveUp()`: Camera ko manually upar move karein
- `MoveDown()`: Camera ko manually neeche move karein
- `SetMouseSensitivity(float sensitivity)`: Mouse sensitivity change karein
- `SetTouchSensitivity(float sensitivity)`: Touch sensitivity change karein
- `ResetCameraRotation()`: Camera rotation ko reset karein
- `SetCameraRotation(Vector3 eulerAngles)`: Camera rotation set karein
- `EnableMouseDragRotation(bool enable)`: Mouse drag rotation enable/disable karein
- `GetCurrentRotation()`: Current camera rotation get karein

## 📱 Testing

1. **Play Mode** mein jayen
2. **Joystick** move karein:
   - Left/Right → Camera left/right move hoga
   - Up/Down → Camera forward/backward move hoga
3. **Height Buttons** use karein:
   - Up Button press/hold → Camera upar move hoga
   - Down Button press/hold → Camera neeche move hoga
4. **Mouse Drag Rotation** (NEW!):
   - **PC**: Left click + mouse drag → Camera rotate hoga
   - **Mobile**: Touch + drag **center area** → Camera rotate hoga (UI zones excluded)
5. **Settings adjust** karein agar movement fast/slow lagti hai

## 🎯 Tips

- **Smooth Time** kam rakhein fast response ke liye
- **Vertical Limits** use karein realistic camera movement ke liye
- **Speed values** 1-5 ke beech rakhein optimal experience ke liye
- **Different joysticks** ke liye different speeds try karein

Ye script completely separate hai aur kisi aur camera system ke saath interfere nahi karegi!

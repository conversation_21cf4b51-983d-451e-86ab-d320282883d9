# Dronecamera Joystick Control Setup Guide

## 🎮 Features

✅ **Left/Right Movement**: Joystick left/right se camera left/right move hota hai
✅ **Forward/Backward Movement**: Joystick up/down se camera forward/backward move hota hai
✅ **Height Control**: UI buttons se camera up/down height control
✅ **Smooth Movement**: Camera movement smooth aur natural hai
✅ **Movement Limits**: Camera position ko limit kar sakte hain
✅ **Speed Control**: Movement speed alag alag set kar sakte hain
✅ **Movement Types**: World space ya camera rotation ke relative movement

## 🛠️ Setup Instructions

### Step 1: Camera GameObject Setup
1. **Camera GameObject** select karein (jo aap control karna chahte hain)
2. **Add Component** → **Scripts** → **Dronecamera**
3. Script attach ho jayegi

### Step 2: Joystick Assignment
1. **Dronecamera component** mein **Joystick** field mein apna joystick drag karein
2. **Dronecameraa** field mein camera reference assign karein (optional)

### Step 3: Height Control Buttons Setup
1. **UI Canvas** create karein (agar already nahi hai)
2. **2 UI Buttons** create karein:
   - **Up Height Button**: "UP" text ke saath
   - **Down Height Button**: "DOWN" text ke saath
3. **Dronecamera component** mein buttons assign karein:
   - **Up Height Button** field mein up button drag karein
   - **Down Height Button** field mein down button drag karein

### Step 4: Settings Configuration

#### Camera Movement Settings:
- **Horizontal Speed**: Left/Right movement ki speed (default: 5f)
- **Vertical Speed**: Forward/Backward movement ki speed (default: 5f)
- **Height Speed**: Up/Down height movement ki speed (default: 3f)
- **Smooth Time**: Movement kitni smooth ho (default: 0.1f)

#### Movement Limits:
- **Limit Horizontal Movement**: X-axis movement limit karna hai ya nahi
- **Min/Max X**: X-axis movement ki limits
- **Limit Vertical Movement**: Z-axis movement limit karna hai ya nahi
- **Min/Max Z**: Z-axis movement ki limits
- **Limit Height Movement**: Y-axis height movement limit karna hai ya nahi (default: true)
- **Min/Max Y**: Y-axis height movement ki limits (default: 0.5f to 50f)

#### Movement Type:
- **Move Relative To Rotation**: Camera ke rotation ke relative move kare ya world space mein

## 🎯 How It Works

### Joystick Input:
- **Left/Right**: Camera left/right move hota hai (X-axis)
- **Up/Down**: Camera forward/backward move hota hai (Z-axis)

### UI Button Input:
- **Up Height Button**: Camera upar move hota hai (Y-axis positive)
- **Down Height Button**: Camera neeche move hota hai (Y-axis negative)

### Movement Logic:
```csharp
// Joystick input
float horizontalInput = joystick.Horizontal; // Left/Right
float verticalInput = joystick.Vertical;     // Forward/Backward

// Camera movement
Vector3 moveDirection = transform.right * horizontalInput + transform.forward * verticalInput;
targetPosition += moveDirection * horizontalSpeed * Time.deltaTime;
```

## ⚙️ Customization Options

### Speed Adjustment:
- **Fast Movement**: horizontalSpeed = 5f, verticalSpeed = 5f
- **Slow Movement**: horizontalSpeed = 1f, verticalSpeed = 1f
- **No Smoothing**: smoothTime = 0f

### Rotation Settings (NEW!):
- **Enable Rotation**: enableMouseDragRotation = true
- **Mouse Sensitivity**: mouseSensitivity = 2f (PC)
- **Touch Sensitivity**: touchSensitivity = 1.5f (Mobile)
- **Smooth Rotation**: rotationSmoothTime = 0.1f

### UI Exclusion Zones (Mobile-Friendly):
- **Use Exclusion Zones**: useUIExclusionZones = true
- **Bottom Zone**: bottomUIZone = 0.2f (Height buttons area)
- **Top Zone**: topUIZone = 0.1f (Menu buttons area)
- **Left Zone**: leftUIZone = 0.15f (Joystick area)
- **Right Zone**: rightUIZone = 0.15f (Other controls area)

![Mobile UI Layout](https://i.imgur.com/mobile-ui-zones.png)

#### 📱 Mobile Screen Zones:

```
┌─────────────────────────────────────────────────────────┐
│ 🔴 TOP UI ZONE (10%) - Menu/Settings Buttons           │
│ ❌ No Rotation Here                                     │
├─────────────────────────────────────────────────────────┤
│🔴│                                                 │🔴│
│L │                                                 │R │
│E │            🟢 ROTATION ZONE                     │I │
│F │         ✅ Touch + Drag Here                    │G │
│T │        (Center 55% x 70%)                      │H │
│  │                                                 │T │
│Z │          📱 FREE CAMERA CONTROL                 │  │
│O │                                                 │Z │
│N │                                                 │O │
│E │                                                 │N │
│  │                                                 │E │
│🔴│                                                 │🔴│
├─────────────────────────────────────────────────────────┤
│ 🔴 BOTTOM UI ZONE (20%) - Height/Action Buttons        │
│ ❌ No Rotation Here                                     │
└─────────────────────────────────────────────────────────┘

🔴 = UI Exclusion Zones (No Rotation)
🟢 = Free Rotation Zone (Touch + Drag)
```

#### 🎯 Zone Breakdown:
- **🔴 Red Zones**: UI elements safe area (buttons, joystick)
- **🟢 Green Zone**: Free camera rotation area
- **Total Rotation Area**: ~55% width × 70% height
- **Perfect Balance**: UI accessibility + camera freedom

#### 📊 Zone Calculations:
```
Screen Width = 100%
- Left Zone (15%) = Joystick area
- Right Zone (15%) = Controls area
- Rotation Width = 70%

Screen Height = 100%
- Top Zone (10%) = Menu area
- Bottom Zone (20%) = Height buttons
- Rotation Height = 70%

Total Rotation Area = 70% × 70% = 49% of screen
```

#### 🎮 Real-World Example:
```
Mobile Device (1080×1920):
┌─────────────────────────────────┐
│ 192px - Menu/Settings           │ ← Top 10%
├─────────────────────────────────┤
│162│                       │162│ ← Left/Right 15%
│px │     756×1344 pixels   │px │
│   │   ROTATION AREA       │   │
│   │                       │   │
├─────────────────────────────────┤
│ 384px - Height Buttons          │ ← Bottom 20%
└─────────────────────────────────┘
```

### Rotation Limits:
- **Free Rotation**: limitHorizontalRotation = false, limitVerticalRotation = false
- **FPS Style**: limitVerticalRotation = true, minVerticalAngle = -90f, maxVerticalAngle = 90f
- **Limited Look**: minHorizontalAngle = -45f, maxHorizontalAngle = 45f

## 🎮 Usage Examples

### Example 1: Free Movement Camera
```
horizontalSpeed = 5f
verticalSpeed = 5f
heightSpeed = 3f
smoothTime = 0.2f
moveRelativeToRotation = true
limitHorizontalMovement = false
limitVerticalMovement = false
limitHeightMovement = true
minY = 0.5f, maxY = 100f
```

### Example 2: Limited Area Camera
```
horizontalSpeed = 3f
verticalSpeed = 3f
heightSpeed = 2f
smoothTime = 0.3f
moveRelativeToRotation = false
limitHorizontalMovement = true
minX = -20f, maxX = 20f
limitVerticalMovement = true
minZ = -20f, maxZ = 20f
limitHeightMovement = true
minY = 1f, maxY = 20f
```

### Example 3: Fast Drone Camera with Rotation
```
horizontalSpeed = 8f
verticalSpeed = 8f
heightSpeed = 5f
smoothTime = 0.1f
enableMouseDragRotation = true
mouseSensitivity = 3f
touchSensitivity = 2f
rotationSmoothTime = 0.05f
useUIExclusionZones = true
bottomUIZone = 0.25f
leftUIZone = 0.2f
limitVerticalRotation = true
minVerticalAngle = -90f, maxVerticalAngle = 90f
moveRelativeToRotation = true
limitHorizontalMovement = false
limitVerticalMovement = false
limitHeightMovement = true
minY = 0.5f, maxY = 200f
```

## 🔧 Public Methods

Script mein ye methods available hain:

- `SetHorizontalSpeed(float speed)`: Runtime mein horizontal speed change karein
- `SetVerticalSpeed(float speed)`: Runtime mein vertical speed change karein
- `SetHeightSpeed(float speed)`: Runtime mein height speed change karein
- `ResetCameraPosition()`: Camera ko original position mein reset karein
- `SetCameraPosition(Vector3 newPosition)`: Camera ko specific position par set karein
- `MoveUp()`: Camera ko manually upar move karein
- `MoveDown()`: Camera ko manually neeche move karein
- `SetMouseSensitivity(float sensitivity)`: Mouse sensitivity change karein
- `SetTouchSensitivity(float sensitivity)`: Touch sensitivity change karein
- `ResetCameraRotation()`: Camera rotation ko reset karein
- `SetCameraRotation(Vector3 eulerAngles)`: Camera rotation set karein
- `EnableMouseDragRotation(bool enable)`: Mouse drag rotation enable/disable karein
- `GetCurrentRotation()`: Current camera rotation get karein

## 📱 Testing

1. **Play Mode** mein jayen
2. **Joystick** move karein:
   - Left/Right → Camera left/right move hoga
   - Up/Down → Camera forward/backward move hoga
3. **Height Buttons** use karein:
   - Up Button press/hold → Camera upar move hoga
   - Down Button press/hold → Camera neeche move hoga
4. **Mouse Drag Rotation** (NEW!):
   - **PC**: Left click + mouse drag → Camera rotate hoga
   - **Mobile**: Touch + drag **center area** → Camera rotate hoga (UI zones excluded)
5. **Settings adjust** karein agar movement fast/slow lagti hai

## 🚨 Troubleshooting UI Zones

### Problem: Rotation area too small
**Solution**: Reduce UI zone values
```csharp
bottomUIZone = 0.15f;  // Reduce from 0.2f
leftUIZone = 0.1f;     // Reduce from 0.15f
rightUIZone = 0.1f;    // Reduce from 0.15f
```

### Problem: UI buttons getting triggered
**Solution**: Increase UI zone values
```csharp
bottomUIZone = 0.25f;  // Increase from 0.2f
leftUIZone = 0.2f;     // Increase from 0.15f
```

### Problem: Rotation continues without mouse button
**Solution**: Fixed! Now rotation only works when:
- **PC**: Left mouse button is held down + drag
- **Mobile**: Touch is held down + drag
- **Auto-stop**: Rotation stops when button/touch is released

### Problem: Rotation not working on mobile
**Check**:
1. `enableMouseDragRotation = true`
2. `useUIExclusionZones = true`
3. Touch in center green area
4. EventSystem present in scene

### Problem: Rotation too sensitive/slow
**Solution**: Adjust sensitivity values
```csharp
mouseSensitivity = 1.5f;    // Reduce for slower (PC)
touchSensitivity = 1.0f;    // Reduce for slower (Mobile)
rotationSmoothTime = 0.2f;  // Increase for smoother
```

## 🎯 Tips

- **Smooth Time** kam rakhein fast response ke liye
- **Vertical Limits** use karein realistic camera movement ke liye
- **Speed values** 1-5 ke beech rakhein optimal experience ke liye
- **Different joysticks** ke liye different speeds try karein
- **UI Zones** mobile ke liye properly adjust karein
- **Rotation Sensitivity** platform ke according set karein

## 📊 Visual Inspector Preview

Inspector mein aapko ye visual representation dikhega:

```
📱 Mobile Screen Layout:
┌─────────────────────────────────────────┐
│ 🔴 TOP ZONE (10%) - Menu Buttons       │
├─────────────────────────────────────────┤
│🔴│                                   │🔴│
│L │        🟢 ROTATION ZONE           │R │
│E │     ✅ Touch + Drag Here          │I │
│F │    (70% × 70%)                   │G │
│T │                                   │H │
│🔴│                                   │🔴│
├─────────────────────────────────────────┤
│ 🔴 BOTTOM ZONE (20%) - Height Buttons  │
└─────────────────────────────────────────┘

🔴 Red Zones: UI Safe Areas (No Rotation)
🟢 Green Zone: Free Camera Rotation
📊 Rotation Area: 70.0% × 70.0%
```

### 🎮 Complete Feature Set:
✅ **Joystick Movement** (Position Control)
✅ **Height Buttons** (Y-axis Control)
✅ **Mouse Drag Rotation** (PC)
✅ **Touch Drag Rotation** (Mobile with UI Zones)
✅ **Smooth Interpolation** (All movements)
✅ **Movement Limits** (All axes)
✅ **Rotation Limits** (Vertical/Horizontal)
✅ **Platform Detection** (Auto PC/Mobile)
✅ **UI Safe Zones** (Mobile-friendly)
✅ **Custom Editor** (Beautiful Inspector)

Ye script completely separate hai aur kisi aur camera system ke saath interfere nahi karegi!

---

**⭐ Drone Camera Developed by Ali Taj ⭐**

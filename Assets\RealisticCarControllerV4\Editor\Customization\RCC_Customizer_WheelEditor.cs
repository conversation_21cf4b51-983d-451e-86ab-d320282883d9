﻿//----------------------------------------------
//            Realistic Car Controller
//
// Copyright © 2014 - 2024 BoneCracker Games
// https://www.bonecrackergames.com
// <PERSON><PERSON><PERSON>
//
//----------------------------------------------

using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(RCC_Customizer_WheelManager))]
public class RCC_Customizer_WheelEditor : Editor {

    RCC_Customizer_WheelManager prop;

    public override void OnInspectorGUI() {

        prop = (RCC_Customizer_WheelManager)target;
        serializedObject.Update();

        EditorGUILayout.HelpBox("All wheels are stored in the configure wheels section", MessageType.None);

        DrawDefaultInspector();

        if (GUILayout.Button("Configure Wheels"))
            Selection.activeObject = RCC_ChangableWheels.Instance;

        serializedObject.ApplyModifiedProperties();

        if (GUILayout.Button("Back"))
            Selection.activeGameObject = prop.GetComponentInParent<RCC_Customizer>(true).gameObject;

    }

}

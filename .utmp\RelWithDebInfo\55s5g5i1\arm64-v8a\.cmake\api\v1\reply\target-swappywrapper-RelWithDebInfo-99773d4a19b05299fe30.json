{"artifacts": [{"path": "D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/build/intermediates/cxx/RelWithDebInfo/55s5g5i1/obj/arm64-v8a/libswappywrapper.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "add_definitions", "target_include_directories"], "files": ["FramePacing/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 13, "parent": 0}, {"command": 1, "file": 0, "line": 22, "parent": 0}, {"command": 2, "file": 0, "line": 17, "parent": 0}, {"command": 3, "file": 0, "line": 19, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security -fexceptions -frtti -stdlib=libc++ -Wall -Wextra -pedantic -Werror -O2 -g -DNDEBUG -fPIC"}], "defines": [{"backtrace": 3, "define": "EXTERNAL_FRAME_PACING_CODE"}, {"define": "swappywrapper_EXPORTS"}], "includes": [{"backtrace": 4, "path": "D:/My Project/Driving Simulator Game Z TEC/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp/FramePacing"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/transforms-3/268849a49ea9eb2bba6f4e0ac95bfd63/transformed/jetified-games-frame-pacing-1.10.0/prefab/modules/swappy_static/include"}], "language": "CXX", "sourceIndexes": [0], "sysroot": {"path": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "swappywrapper::@7e25bd1f32ce224db4e9", "link": {"commandFragments": [{"fragment": "-Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--fatal-warnings -Qunused-arguments -Wl,--no-undefined -Wl,--wrap=__android_log_print", "role": "flags"}, {"backtrace": 2, "fragment": "-landroid", "role": "libraries"}, {"backtrace": 2, "fragment": "\"C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\268849a49ea9eb2bba6f4e0ac95bfd63\\transformed\\jetified-games-frame-pacing-1.10.0\\prefab\\modules\\swappy_static\\libs\\android.arm64-v8a_API23_NDK23_cpp_shared_Release\\libswappy.a\"", "role": "libraries"}, {"backtrace": 2, "fragment": "-llog", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "swappywrapper", "nameOnDisk": "libswappywrapper.so", "paths": {"build": "FramePacing", "source": "FramePacing"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "FramePacing/UnitySwappyWrapper.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "FramePacing/UnitySwappyWrapper.h", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}
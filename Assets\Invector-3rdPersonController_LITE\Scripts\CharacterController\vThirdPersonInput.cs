using UnityEngine;
using UnityEngine.EventSystems;
using System.Collections.Generic;

namespace Invector.vCharacterController
{
    public class vThirdPersonInput : MonoBehaviour
    {
        #region Variables       

        [Header("Controller Input")]
        public string horizontalInput = "Horizontal";
        public string verticallInput = "Vertical";
        public KeyCode jumpInput = KeyCode.Space;
        public KeyCode strafeInput = KeyCode.Tab;
        public KeyCode sprintInput = KeyCode.LeftShift;

        [<PERSON>er("Camera Input")]
        public string rotateCameraXInput = "Mouse X";
        public string rotateCameraYInput = "Mouse Y";

        [Header("Camera Control Settings")]
        [Tooltip("Enable camera rotation with left click + mouse drag on PC")]
        public bool enablePCCameraControl = true;
        [Tooltip("Enable touch camera rotation on mobile")]
        public bool enableMobileCameraControl = true;
        [Tooltip("Camera rotation sensitivity for touch input")]
        public float touchSensitivity = 2f;

        [HideInInspector] public vThirdPersonController cc;
        [HideInInspector] public vThirdPersonCamera tpCamera;
        [HideInInspector] public Camera cameraMain;

        public Joystick joystick;

        // Camera control variables
        private bool isDraggingCamera = false;
        private Vector2 lastTouchPosition;

        #endregion

        protected virtual void Start()
        {
            InitilizeController();
            InitializeTpCamera();
        }

        protected virtual void FixedUpdate()
        {
            cc.UpdateMotor();               // updates the ThirdPersonMotor methods
            cc.ControlLocomotionType();     // handle the controller locomotion type and movespeed
            cc.ControlRotationType();       // handle the controller rotation type
        }

        protected virtual void Update()
        {
            InputHandle();                  // update the input methods
            cc.UpdateAnimator();            // updates the Animator Parameters
        }

        public virtual void OnAnimatorMove()
        {
            cc.ControlAnimatorRootMotion(); // handle root motion animations 
        }

        #region Basic Locomotion Inputs

        protected virtual void InitilizeController()
        {
            cc = GetComponent<vThirdPersonController>();

            if (cc != null)
                cc.Init();
        }

        protected virtual void InitializeTpCamera()
        {
            if (tpCamera == null)
            {
                tpCamera = FindObjectOfType<vThirdPersonCamera>();
                if (tpCamera == null)
                    return;
                if (tpCamera)
                {
                    tpCamera.SetMainTarget(this.transform);
                    tpCamera.Init();
                }
            }
        }

        protected virtual void InputHandle()
        {
            MoveInput();
            CameraInput();
            SprintInput();
            StrafeInput();
            JumpInput();
        }

        public virtual void MoveInput()
        {
            // Get input from keyboard/gamepad
            float horizontalAxis = Input.GetAxis(horizontalInput);
            float verticalAxis = Input.GetAxis(verticallInput);

            // Add joystick input if joystick is assigned
            if (joystick != null)
            {
                horizontalAxis += joystick.Horizontal;
                verticalAxis += joystick.Vertical;
            }

            // Clamp the combined input to prevent values exceeding -1 to 1 range
            cc.input.x = Mathf.Clamp(horizontalAxis, -1f, 1f);
            cc.input.z = Mathf.Clamp(verticalAxis, -1f, 1f);
        }

        protected virtual void CameraInput()
        {
            if (!cameraMain)
            {
                if (!Camera.main) Debug.Log("Missing a Camera with the tag MainCamera, please add one.");
                else
                {
                    cameraMain = Camera.main;
                    cc.rotateTarget = cameraMain.transform;
                }
            }

            if (cameraMain)
            {
                cc.UpdateMoveDirection(cameraMain.transform);
            }

            if (tpCamera == null)
                return;

            float X = 0f;
            float Y = 0f;

            // PC Camera Control - Left click + mouse drag
            if (enablePCCameraControl && Application.platform != RuntimePlatform.Android && Application.platform != RuntimePlatform.IPhonePlayer)
            {
                if (Input.GetMouseButtonDown(0))
                {
                    isDraggingCamera = true;
                }
                else if (Input.GetMouseButtonUp(0))
                {
                    isDraggingCamera = false;
                }

                if (isDraggingCamera)
                {
                    X = Input.GetAxis(rotateCameraXInput);
                    Y = Input.GetAxis(rotateCameraYInput);
                }
            }
            // Mobile Camera Control - Touch input
            else if (enableMobileCameraControl && (Application.platform == RuntimePlatform.Android || Application.platform == RuntimePlatform.IPhonePlayer))
            {
                HandleMobileCameraInput(ref X, ref Y);
            }
            // Default behavior - always allow mouse input (for editor and other platforms)
            else
            {
                X = Input.GetAxis(rotateCameraXInput);
                Y = Input.GetAxis(rotateCameraYInput);
            }

            tpCamera.RotateCamera(X, Y);
        }

        protected virtual void HandleMobileCameraInput(ref float X, ref float Y)
        {
            if (Input.touchCount > 0)
            {
                Touch touch = Input.GetTouch(0);

                // Check if touch is not on UI elements (like joystick)
                if (!IsPointerOverUIObject(touch.position))
                {
                    switch (touch.phase)
                    {
                        case TouchPhase.Began:
                            lastTouchPosition = touch.position;
                            isDraggingCamera = true;
                            break;

                        case TouchPhase.Moved:
                            if (isDraggingCamera)
                            {
                                Vector2 deltaPosition = touch.position - lastTouchPosition;
                                X = deltaPosition.x * touchSensitivity * Time.deltaTime;
                                Y = deltaPosition.y * touchSensitivity * Time.deltaTime;
                                lastTouchPosition = touch.position;
                            }
                            break;

                        case TouchPhase.Ended:
                        case TouchPhase.Canceled:
                            isDraggingCamera = false;
                            break;
                    }
                }
            }
        }

        protected virtual bool IsPointerOverUIObject(Vector2 screenPosition)
        {
            // Check if touch is over UI elements
            PointerEventData eventDataCurrentPosition = new PointerEventData(EventSystem.current)
            {
                position = screenPosition
            };

            var results = new List<RaycastResult>();
            EventSystem.current.RaycastAll(eventDataCurrentPosition, results);

            return results.Count > 0;
        }

        protected virtual void StrafeInput()
        {
            if (Input.GetKeyDown(strafeInput))
                cc.Strafe();
        }

        protected virtual void SprintInput()
        {
            if (Input.GetKeyDown(sprintInput))
                cc.Sprint(true);
            else if (Input.GetKeyUp(sprintInput))
                cc.Sprint(false);
        }

        /// <summary>
        /// Conditions to trigger the Jump animation & behavior
        /// </summary>
        /// <returns></returns>
        protected virtual bool JumpConditions()
        {
            return cc.isGrounded && cc.GroundAngle() < cc.slopeLimit && !cc.isJumping && !cc.stopMove;
        }

        /// <summary>
        /// Input to trigger the Jump 
        /// </summary>
        protected virtual void JumpInput()
        {
            if (Input.GetKeyDown(jumpInput) && JumpConditions())
                cc.Jump();
        }

        #endregion       
    }
}
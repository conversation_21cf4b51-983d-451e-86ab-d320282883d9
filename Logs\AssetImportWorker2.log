Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.30f1 (62b05ba0686a) revision 6467675'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit ProfessionalWorkstation' Language: 'en' Physical Memory: 32684 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.30f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
D:/My Project/Driving Simulator Game Z TEC
-logFile
Logs/AssetImportWorker2.log
-srvPort
53742
-job-worker-count
11
-background-job-worker-count
8
-asset-garbage-collectors
1
Successfully changed project path to: D:/My Project/Driving Simulator Game Z TEC
D:/My Project/Driving Simulator Game Z TEC
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [31404]  Target information:

Player connection [31404]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1279985757 [EditorId] 1279985757 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [31404]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1279985757 [EditorId] 1279985757 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-GMG2SOO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [31404] Host joined multi-casting on [***********:54997]...
Player connection [31404] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 11
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2.35 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.30f1 (62b05ba0686a)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/My Project/Driving Simulator Game Z TEC/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: NVIDIA Quadro K2200 (ID=0x13ba)
    Vendor:   NVIDIA
    VRAM:     4035 MB
    Driver:   10.18.13.5330
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56176
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.006787 seconds.
- Loaded All Assemblies, in  0.798 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 468 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.119 seconds
Domain Reload Profiling: 1912ms
	BeginReloadAssembly (284ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (67ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (98ms)
	LoadAllAssembliesAndSetupDomain (321ms)
		LoadAssemblies (281ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (314ms)
			TypeCache.Refresh (311ms)
				TypeCache.ScanAssembly (285ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (1119ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1038ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (616ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (94ms)
			ProcessInitializeOnLoadAttributes (208ms)
			ProcessInitializeOnLoadMethodAttributes (115ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.226 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.17 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.000 seconds
Domain Reload Profiling: 2221ms
	BeginReloadAssembly (294ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (60ms)
	RebuildCommonClasses (57ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (60ms)
	LoadAllAssembliesAndSetupDomain (789ms)
		LoadAssemblies (561ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (399ms)
			TypeCache.Refresh (305ms)
				TypeCache.ScanAssembly (278ms)
			BuildScriptInfoCaches (77ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1001ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (820ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (149ms)
			ProcessInitializeOnLoadAttributes (598ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Shader 'WFX/Transparent Diffuse': fallback shader 'Particles/Alpha Blended' not found
Shader 'WFX/Transparent Specular': fallback shader 'Particles/Alpha Blended' not found
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 2.84 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 66 Unused Serialized files (Serialized files now loaded: 0)
Unloading 4568 unused Assets / (1.2 MB). Loaded Objects now: 5129.
Memory consumption went from 117.5 MB to 116.2 MB.
Total: 12.333500 ms (FindLiveObjects: 1.063500 ms CreateObjectMapping: 0.634500 ms MarkObjects: 8.306600 ms  DeleteObjects: 2.324200 ms)

========================================================================
Received Import Request.
  Time since last request: 14502.969012 seconds.
  path: Assets/Invector-3rdPersonController_LITE/3D Models/Others/NewAssets/_stairs.prefab
  artifactKey: Guid(e5e911e47916cfc439e4036756e48bf1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Invector-3rdPersonController_LITE/3D Models/Others/NewAssets/_stairs.prefab using Guid(e5e911e47916cfc439e4036756e48bf1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '519ecb7f20434fc2041b5913e99704f2') in 0.6983325 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/Colliders/BaseCollider_wagon.prefab
  artifactKey: Guid(2e744b5d85b122349a7f21605462e35f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/Colliders/BaseCollider_wagon.prefab using Guid(2e744b5d85b122349a7f21605462e35f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bfba7b08131b97bb73345f7620bc314b') in 0.0023523 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Control Zones/BellDeactivationZone.prefab
  artifactKey: Guid(05cbbc82867423546bc31d10de6bf671) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Control Zones/BellDeactivationZone.prefab using Guid(05cbbc82867423546bc31d10de6bf671) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'faba69e7e0204768f9682bebff0ac462') in 0.0027365 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Particles/Prefabs/BrakingSparks.prefab
  artifactKey: Guid(f62087eb5737bdb4c9250b5f69333334) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Particles/Prefabs/BrakingSparks.prefab using Guid(f62087eb5737bdb4c9250b5f69333334) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '228b0ef64003973ed98c4b079572b817') in 0.1889186 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000050 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/Colliders/BaseCollider_locomotive.prefab
  artifactKey: Guid(db37b875b59681541abdf86e581464d5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/Colliders/BaseCollider_locomotive.prefab using Guid(db37b875b59681541abdf86e581464d5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fc5344704b2f16c28e7741a7015325e3') in 0.0020918 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Control Zones/BellActivationZone.prefab
  artifactKey: Guid(d0c86ec9fb2e7e7489e95b94221b0ced) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Control Zones/BellActivationZone.prefab using Guid(d0c86ec9fb2e7e7489e95b94221b0ced) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd2e12aca30977f7f05e96976c5648bf7') in 0.0016266 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000049 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Bench_02.fbx
  artifactKey: Guid(cdd9079f2185d3c4f833416d9c188e24) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Bench_02.fbx using Guid(cdd9079f2185d3c4f833416d9c188e24) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c777aaca4d514a0ca97d9370b173f076') in 0.282205 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Benches/Bench_03_Blue.prefab
  artifactKey: Guid(cec635bd8d2fe204c9eb61a2d864f505) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Benches/Bench_03_Blue.prefab using Guid(cec635bd8d2fe204c9eb61a2d864f505) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e9ca6b0fddeb2e628a5f942dd060a195') in 0.1305234 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 25

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Bench_03.fbx
  artifactKey: Guid(120097c5e0030474f8407d04d48a21b4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Bench_03.fbx using Guid(120097c5e0030474f8407d04d48a21b4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c398cbd905ce50fecffb3ab3d8cfd9a5') in 0.0929931 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Benches/Bench_01_Blue.prefab
  artifactKey: Guid(2205c8c0074e1b742aee3468e29bca33) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Benches/Bench_01_Blue.prefab using Guid(2205c8c0074e1b742aee3468e29bca33) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e017568cf96966fdd3f98f8663b49e96') in 0.1025773 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Benches/BenchSet_02.prefab
  artifactKey: Guid(ae59203c478008e4581b2c73d761bef4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Benches/BenchSet_02.prefab using Guid(ae59203c478008e4581b2c73d761bef4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b7e175135b83d34e6cb965534fb5c02d') in 0.1355014 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 39

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Benches/BenchSet_03.prefab
  artifactKey: Guid(6b44036b081f7b5418017aec34eaac0d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Benches/BenchSet_03.prefab using Guid(6b44036b081f7b5418017aec34eaac0d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fbd13981fb2ba42edc716d26ede35634') in 0.1077445 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 39

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/UnityTechnologies/EffectExamples/WeaponEffects/Prefabs/BulletImpactSandEffect.prefab
  artifactKey: Guid(a475880e7da0e9548aa05cb7933752cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/WeaponEffects/Prefabs/BulletImpactSandEffect.prefab using Guid(a475880e7da0e9548aa05cb7933752cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd994bb092d432369a752373a8d08674b') in 0.0809444 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/MSK 2.2/Models/Tree/Alder.fbx
  artifactKey: Guid(1475752df7e70a048856422301619c1b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MSK 2.2/Models/Tree/Alder.fbx using Guid(1475752df7e70a048856422301619c1b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c67643b4b0a6e6024a7c8464ecbfd988') in 0.2041588 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/AdvancedHelicopterController/Prefabs/Bullets/Particles/Bullet_Muzzle.prefab
  artifactKey: Guid(d329ee26ad05240af8c5f15495b44336) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Prefabs/Bullets/Particles/Bullet_Muzzle.prefab using Guid(d329ee26ad05240af8c5f15495b44336) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fd9679a3fa158aac312cae3e8c510b0c') in 0.024832 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/SFX/BrakesSFX.prefab
  artifactKey: Guid(48db2eade86efbf48bcafcc2c57068d4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/SFX/BrakesSFX.prefab using Guid(48db2eade86efbf48bcafcc2c57068d4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '62030203890b6d0f44e5451897f771b1') in 0.0235669 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/MSK 2.2/Models/Player/Animations2/Back.FBX
  artifactKey: Guid(4888ed6857d1e7742ba909e6a4d27db6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MSK 2.2/Models/Player/Animations2/Back.FBX using Guid(4888ed6857d1e7742ba909e6a4d27db6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4c946a14c4fd889bb996138d2e4153ce') in 0.039366 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 77

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/UnityTechnologies/EffectExamples/Blood/Prefabs/BloodStreamEffect.prefab
  artifactKey: Guid(a3768c8a9d1b90d4bb0eb0e4bdbe730c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/Blood/Prefabs/BloodStreamEffect.prefab using Guid(a3768c8a9d1b90d4bb0eb0e4bdbe730c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b47b195264153bb887140fa815a3cf60') in 0.2860644 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 27

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/UnityTechnologies/EffectExamples/Shared/Models/BackGround.FBX
  artifactKey: Guid(f4527c479041fc040b53171204c086a5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/Shared/Models/BackGround.FBX using Guid(f4527c479041fc040b53171204c086a5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cda2b0ff639ab64cf4d4d6434190e66b') in 0.0241651 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Benches/Bench_02_LightBrown.prefab
  artifactKey: Guid(1105403ab5db90440866ebdaa44e22b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Benches/Bench_02_LightBrown.prefab using Guid(1105403ab5db90440866ebdaa44e22b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '62bfe8dcc11e9649a7f7f3ac62e7693a') in 0.102936 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/UnityTechnologies/EffectExamples/FireExplosionEffects/Prefabs/BigExplosionEffect.prefab
  artifactKey: Guid(acf39d9f4addf294d93fa56fa373961b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/FireExplosionEffects/Prefabs/BigExplosionEffect.prefab using Guid(acf39d9f4addf294d93fa56fa373961b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a736bc3b377be5fe0f37e95b3e11e892') in 0.5316301 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 49

========================================================================
Received Import Request.
  Time since last request: 0.012198 seconds.
  path: Assets/UnityTechnologies/EffectExamples/WeaponEffects/Prefabs/BulletImpactFleshSmallEffect.prefab
  artifactKey: Guid(83e55d3418297934fab882559d009285) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/WeaponEffects/Prefabs/BulletImpactFleshSmallEffect.prefab using Guid(83e55d3418297934fab882559d009285) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3de2fd6178a6f8e5492cc81d3e2bdc8a') in 0.3775679 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 30

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/MSK 2.2/Standard Assets/Projectors/Blob Shadow Projector.prefab
  artifactKey: Guid(1880a732ad112a541100162a44295342) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MSK 2.2/Standard Assets/Projectors/Blob Shadow Projector.prefab using Guid(1880a732ad112a541100162a44295342) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2e7cdffac1439519daee9a6053d2bf63') in 0.1405063 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000108 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Bars/Bars_01.prefab
  artifactKey: Guid(8b41b563b0f16224193273b84434175e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Bars/Bars_01.prefab using Guid(8b41b563b0f16224193273b84434175e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6ca2decdbf095ce9dd01169edb5c21e1') in 0.1185491 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Bars/Bars_02.prefab
  artifactKey: Guid(caa7166ce3752a6479141c50ff1e9235) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Bars/Bars_02.prefab using Guid(caa7166ce3752a6479141c50ff1e9235) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '71574f55ace4e3e957d73993930545dd') in 0.1056613 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 16

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/UnityTechnologies/EffectExamples/WeaponEffects/Prefabs/BulletImpactMetalEffect.prefab
  artifactKey: Guid(02a509a7fa70ed740ba4197e20c5d32d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/WeaponEffects/Prefabs/BulletImpactMetalEffect.prefab using Guid(02a509a7fa70ed740ba4197e20c5d32d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '33632ab714733aac58551b83471df564') in 0.26626 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 22

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Benches/BenchSet_01.prefab
  artifactKey: Guid(0ea21305ed156eb4faf85d797fc08cb1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Parts/Benches/BenchSet_01.prefab using Guid(0ea21305ed156eb4faf85d797fc08cb1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6665b4827092d3772da853402cf73c61') in 0.1379556 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 56

========================================================================
Received Import Request.
  Time since last request: 0.000054 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Bench_01.fbx
  artifactKey: Guid(2e07d1d85cb633249a6d61d3f8b7de77) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Bench_01.fbx using Guid(2e07d1d85cb633249a6d61d3f8b7de77) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3d7da6bf6c9cf0d09ea6b3d389d697e1') in 0.1099295 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/Player/AIROPLANE OK/AIROPLNE.fbx
  artifactKey: Guid(d88da6200c7a1f14f9e60ef86ff07713) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Player/AIROPLANE OK/AIROPLNE.fbx using Guid(d88da6200c7a1f14f9e60ef86ff07713) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cf216c1a0c1f29ef81030e92af119338') in 0.4292135 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 180

========================================================================
Received Import Request.
  Time since last request: 68.708355 seconds.
  path: Assets/UnityTechnologies/EffectExamples/WeaponEffects/Prefabs/BulletImpactStoneEffect.prefab
  artifactKey: Guid(4edabb1c0dde62743b137378c7d22980) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/WeaponEffects/Prefabs/BulletImpactStoneEffect.prefab using Guid(4edabb1c0dde62743b137378c7d22980) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c8f2c2eb632a735f0fb03ae6b1fbfbdf') in 0.7952201 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 24

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/_Prefabs/CableCar_01_Green Variant.prefab
  artifactKey: Guid(7a9d52cd33af4b8469d9ed76a98d69b7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/_Prefabs/CableCar_01_Green Variant.prefab using Guid(7a9d52cd33af4b8469d9ed76a98d69b7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9b1dfd6e7b3c7394d88e12c41b8e3cd7') in 5.4656843 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1377

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/_Prefabs/Parts/Controls/CableCar_01_Lever.prefab
  artifactKey: Guid(c190541fbf49a2a42927e6c566b7461d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/_Prefabs/Parts/Controls/CableCar_01_Lever.prefab using Guid(c190541fbf49a2a42927e6c566b7461d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9e292a5a6fad35b3435d1a8a84d3f46d') in 0.4255616 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Models/CableCar_01_Glass.fbx
  artifactKey: Guid(5588aa2c089e4f24fa9215cbe66bcd22) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Models/CableCar_01_Glass.fbx using Guid(5588aa2c089e4f24fa9215cbe66bcd22) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f0c866ae9e33788d78f95a582bbb2045') in 0.0375795 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 3/_Prefabs/CargoWagon03_Orange.prefab
  artifactKey: Guid(1d7b514833dff5741ad517a56317830a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 3/_Prefabs/CargoWagon03_Orange.prefab using Guid(1d7b514833dff5741ad517a56317830a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bac5375b39a2aa3ef9fbff8ee530fce9') in 0.4589056 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 228

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/_Prefabs/CargoWagon02_Blue.prefab
  artifactKey: Guid(938f94fd1c32d0042b14850adcd64b45) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/_Prefabs/CargoWagon02_Blue.prefab using Guid(938f94fd1c32d0042b14850adcd64b45) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f2a5aebcbca82a8ceca5e124b8dd5079') in 0.369194 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 244

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/AdvancedHelicopterController/Prefabs/Collectables/Collectable_Ammo.prefab
  artifactKey: Guid(fc9e50e9349044fb19089d1c1ad29a49) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Prefabs/Collectables/Collectable_Ammo.prefab using Guid(fc9e50e9349044fb19089d1c1ad29a49) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ceeb6954500b3d0133e18f0c1d7956ce') in 0.2829839 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Models/CableCar_01_LeverBase.fbx
  artifactKey: Guid(39aa22576a59090479bd7692ebc61e45) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Models/CableCar_01_LeverBase.fbx using Guid(39aa22576a59090479bd7692ebc61e45) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6ff913cc53af12f422379ab7191032af') in 0.4617569 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/Invector-3rdPersonController_LITE/3D Models/Others/NewAssets/climbUp.prefab
  artifactKey: Guid(48f0c406a6058ef4096de43251b4dc26) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Invector-3rdPersonController_LITE/3D Models/Others/NewAssets/climbUp.prefab using Guid(48f0c406a6058ef4096de43251b4dc26) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '850b73c5920c4496c187b8f10d84feee') in 0.5588764 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/Models/Cargo_01_Stones.fbx
  artifactKey: Guid(353ef71456b20b84c848615d34a64c82) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/Models/Cargo_01_Stones.fbx using Guid(353ef71456b20b84c848615d34a64c82) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cf68b5a23a4017799426477a3cab78b7') in 0.1768828 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000044 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Bumpers_low.fbx
  artifactKey: Guid(701d38a9c1548644d9508eb88f6a2679) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Shared/Models/Bumpers_low.fbx using Guid(701d38a9c1548644d9508eb88f6a2679) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f676a9346e4774cd47e6f89d951cdae8') in 0.1477506 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Models/CableCar_01_LOD0.fbx
  artifactKey: Guid(11662905d652012439f60ac81ac9f0b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cable Car Expansion Pack/Models/CableCar_01_LOD0.fbx using Guid(11662905d652012439f60ac81ac9f0b8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd032ef811ae76da31487cb3814ff2da9') in 2.4476715 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/MSK 2.2/Standard Assets/Projectors/Grid Projector.prefab
  artifactKey: Guid(a29e133e3841243b7b0ec432e3573cdd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MSK 2.2/Standard Assets/Projectors/Grid Projector.prefab using Guid(a29e133e3841243b7b0ec432e3573cdd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2e222d28ffbd33cf05bec14bbed2d5f1') in 0.0307011 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/Invector-3rdPersonController_LITE/3D Models/Others/NewAssets/floor.prefab
  artifactKey: Guid(ce681f0b940baed4a92d9f6cf065313c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Invector-3rdPersonController_LITE/3D Models/Others/NewAssets/floor.prefab using Guid(ce681f0b940baed4a92d9f6cf065313c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '97d8371831543ba2ff88887b65d606f5') in 0.1902547 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 10

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/SFX/HornSFX.prefab
  artifactKey: Guid(13c0eebb3ff37234faf831ded7ffd8f4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/SFX/HornSFX.prefab using Guid(13c0eebb3ff37234faf831ded7ffd8f4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '22f579a2bca5e3f49f45501065d8fb6d') in 0.0243599 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Control Zones/HonkZone.prefab
  artifactKey: Guid(cda2f7ec9bb8a2d499908f8437994df3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Control Zones/HonkZone.prefab using Guid(cda2f7ec9bb8a2d499908f8437994df3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '46b44720ce9e61fc5838c3993a8ad3b7') in 0.0023954 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/InternalDetails/InternalDetails.prefab
  artifactKey: Guid(0f38c75ffcba2974199302765e2a935d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/InternalDetails/InternalDetails.prefab using Guid(0f38c75ffcba2974199302765e2a935d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4ea9079096e37d9916d6942fd7a533c1') in 0.5627619 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 450

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Misc/Mic_01.prefab
  artifactKey: Guid(58315d1f2622d504e83a843d08895332) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Misc/Mic_01.prefab using Guid(58315d1f2622d504e83a843d08895332) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '14237f5b85e23ad338a640a998474fc8') in 0.1204309 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000042 seconds.
  path: Assets/AdvancedHelicopterController/Models/FuelPack/Model_Fuel.fbx
  artifactKey: Guid(79579f219eb684bfe8daf12ffb40056f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Models/FuelPack/Model_Fuel.fbx using Guid(79579f219eb684bfe8daf12ffb40056f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b032f2ee15d784b8d169a2a26847ec00') in 0.067661 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_CW_2_prefab.prefab
  artifactKey: Guid(5e1263fe875159149af5df766562171f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_CW_2_prefab.prefab using Guid(5e1263fe875159149af5df766562171f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd8a89f5830dada2830f9be6720540321') in 0.4328061 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 64

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_ACW_4_prefab.prefab
  artifactKey: Guid(a360639247666d34f9e588fc0b456f44) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Railroad Builder/Baked Meshes/Demo/LongRailroad_ACW_4_prefab.prefab using Guid(a360639247666d34f9e588fc0b456f44) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e6847689b050ace5908032f116fc7da7') in 0.3509056 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 64

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/MSK 2.2/Perfabs/Perfabs/Motorbike3 Mobaile.prefab
  artifactKey: Guid(7383f534bcc53cb498d21bec5cae0c8f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MSK 2.2/Perfabs/Perfabs/Motorbike3 Mobaile.prefab using Guid(7383f534bcc53cb498d21bec5cae0c8f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 1.57 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: '6c7cff0a5c0ce4bb3c984c3b3678e559') in 0.9050792 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 287

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Parts/Wheels/SteamLocomotive_01_WheelsTruck_visual.prefab
  artifactKey: Guid(456470b6f36a2844aa94a701e57fab85) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Parts/Wheels/SteamLocomotive_01_WheelsTruck_visual.prefab using Guid(456470b6f36a2844aa94a701e57fab85) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd0039691eb20f012fe452f886750bf45') in 0.4646933 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 39

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/SubwayCart_01_LowerDetails.fbx
  artifactKey: Guid(fcf94f4fbf1a57047b4dc2a40755f71a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/SubwayCart_01_LowerDetails.fbx using Guid(fcf94f4fbf1a57047b4dc2a40755f71a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '62d555fcb2e55e66c35f09c69ae85c12') in 0.2425454 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/UnityTechnologies/EffectExamples/Shared/Models/LowMan.fbx
  artifactKey: Guid(7e15121f5f042ed46aba81406403f2d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/UnityTechnologies/EffectExamples/Shared/Models/LowMan.fbx using Guid(7e15121f5f042ed46aba81406403f2d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f22ba95a5158e4f09c0c45de8cf075b2') in 0.07748 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 69

========================================================================
Received Import Request.
  Time since last request: 0.000112 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Subway_01_Glass_WindowLarge.fbx
  artifactKey: Guid(623000cf1b8af324eac49a7d84e334eb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/Models/Subway_01_Glass_WindowLarge.fbx using Guid(623000cf1b8af324eac49a7d84e334eb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6c10a27a5bffbbb3ab0a5da88fc9cd48') in 0.5031385 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/MSK 2.2/Perfabs/Perfabs/Motorbike2.prefab
  artifactKey: Guid(dd43d7f0a8c9c774eabaf99fcf1fdd0d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MSK 2.2/Perfabs/Perfabs/Motorbike2.prefab using Guid(dd43d7f0a8c9c774eabaf99fcf1fdd0d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 2.83 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: 'faa5eb60608a7026494b6478c9ef0848') in 0.9043374 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 273

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Sinalization/StopblockEndingCap.prefab
  artifactKey: Guid(14502d41460528c488b6d54dca7d5fb8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Sinalization/StopblockEndingCap.prefab using Guid(14502d41460528c488b6d54dca7d5fb8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1a48bd15e274b61d7fe8ec26d5a3944c') in 0.1503205 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 45

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/MSK 2.2/Models/Motorbikes/Motorbike (Police)/motorbike3.FBX
  artifactKey: Guid(1a82822f25bae71428aeb42c66e4407c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MSK 2.2/Models/Motorbikes/Motorbike (Police)/motorbike3.FBX using Guid(1a82822f25bae71428aeb42c66e4407c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e881e2f8f41009a4c965a8c4635cead5') in 0.3210257 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 39

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/MSK 2.2/Models/Motorbikes/Motorbike (Racer)/motorbike1.FBX
  artifactKey: Guid(917feec2b09f73c4b8ee77d8b3d6a1a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MSK 2.2/Models/Motorbikes/Motorbike (Racer)/motorbike1.FBX using Guid(917feec2b09f73c4b8ee77d8b3d6a1a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b38f35f30b78e5a955335ce706036574') in 0.3412028 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 74

========================================================================
Received Import Request.
  Time since last request: 0.000105 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Prefabs/Misc/Switch_01_Black.prefab
  artifactKey: Guid(f77b53cf01418e547b594c4d26580f74) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Prefabs/Misc/Switch_01_Black.prefab using Guid(f77b53cf01418e547b594c4d26580f74) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5b705e537f3b62fc3bcae701a5bf14f0') in 0.1613469 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/AdvancedHelicopterController/Prefabs/Bullets/Particles/Missile_Firing_Particle.prefab
  artifactKey: Guid(24a76472927c6464891fa04890bcb0aa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/AdvancedHelicopterController/Prefabs/Bullets/Particles/Missile_Firing_Particle.prefab using Guid(24a76472927c6464891fa04890bcb0aa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fac999aeabab805b4ce367b6fa081926') in 0.023768 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 29

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/MSK 2.2/Scenes/Motorbike1.prefab
  artifactKey: Guid(ecdfb33c46a74f849b59f9b5f3db5b58) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MSK 2.2/Scenes/Motorbike1.prefab using Guid(ecdfb33c46a74f849b59f9b5f3db5b58) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 2.84 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: '7f8aae3c2f54bdc8bb67853b37048fee') in 0.2622597 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 304

========================================================================
Received Import Request.
  Time since last request: 0.000178 seconds.
  path: Assets/MSK 2.2/Perfabs/Main Camera.prefab
  artifactKey: Guid(8512d52775bf95340b741dfb9552c938) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MSK 2.2/Perfabs/Main Camera.prefab using Guid(8512d52775bf95340b741dfb9552c938) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3fab6513f5aa6ee896d8cfae43638135') in 0.0120609 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000045 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Sample Trains/SubwaySample_01.prefab
  artifactKey: Guid(82580ac1d09587e4fb102047962dd713) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Subway Expansion Pack/_Prefabs/Sample Trains/SubwaySample_01.prefab using Guid(82580ac1d09587e4fb102047962dd713) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '82299388a342476e121afa5d339f35d6') in 3.8834803 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5133

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Parts/LODS/TenderWagon_01.prefab
  artifactKey: Guid(c2cd488d290a293408a98c9b3c6e4e28) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Parts/LODS/TenderWagon_01.prefab using Guid(c2cd488d290a293408a98c9b3c6e4e28) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0d548f7dce6223bad68fbac84c0630ec') in 2.0456566 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 43

========================================================================
Received Import Request.
  Time since last request: 0.000121 seconds.
  path: Assets/MSK 2.2/Scenes/Motorbike2.prefab
  artifactKey: Guid(9457400ec70feee45959794eb9de25fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/MSK 2.2/Scenes/Motorbike2.prefab using Guid(9457400ec70feee45959794eb9de25fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 1.93 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: 'd3cfa772697e31165353c9ae6b224c8f') in 0.2546916 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 273

========================================================================
Received Import Request.
  Time since last request: 0.000119 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/Models/TenderWagon_01_LOD2.fbx
  artifactKey: Guid(7f84fc81c8988f842a54a4294307bae1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/Models/TenderWagon_01_LOD2.fbx using Guid(7f84fc81c8988f842a54a4294307bae1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3091f3e63d1e9c941a73d9e9e0afc710') in 2.7694907 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 43.643203 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Parts/Wheels/PassengerWagon_01_WheelsTruck_visual.prefab
  artifactKey: Guid(d9583da9429fc494daf92b6b02897f0f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Steam Locomotive Expansion Pack/_Prefabs/Parts/Wheels/PassengerWagon_01_WheelsTruck_visual.prefab using Guid(d9583da9429fc494daf92b6b02897f0f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cebc48b01688ccc87603277ddd1b49e5') in 0.627047 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 39

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/Couplers/TrainJoint_Front_nonPhysics.prefab
  artifactKey: Guid(02d28c7b6b140e0439c40cad05c56ac3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Wagons Creator/Parts/Couplers/TrainJoint_Front_nonPhysics.prefab using Guid(02d28c7b6b140e0439c40cad05c56ac3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ca25112195f6c2bdead677835aa38aea') in 0.1607211 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 17

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 3/_Prefabs/TrainSample_Addon3.prefab
  artifactKey: Guid(df81a37cad707f547a915699f54f41b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 3/_Prefabs/TrainSample_Addon3.prefab using Guid(df81a37cad707f547a915699f54f41b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e40dbb268753c8790e43fa38299f4a4b') in 4.3958036 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1772

========================================================================
Received Import Request.
  Time since last request: 0.000055 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/_Prefabs/s_TrainSample_Addon2.prefab
  artifactKey: Guid(70371570e2f6a374aa62aa32b329be40) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 2/_Prefabs/s_TrainSample_Addon2.prefab using Guid(70371570e2f6a374aa62aa32b329be40) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a4954592a107f4369fd6b90a09fd364a') in 5.2507735 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4734

========================================================================
Received Import Request.
  Time since last request: 0.000056 seconds.
  path: Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 3/_Prefabs/s_TrainSample_Addon3.prefab
  artifactKey: Guid(f36bcca2525ce954c87b8e09d56e646e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/WSM Game Studio/Train Controller_v3/Addons/Cargo Wagon 3/_Prefabs/s_TrainSample_Addon3.prefab using Guid(f36bcca2525ce954c87b8e09d56e646e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c81c7a869dfbec2c8f05b413f1c237ed') in 4.7145145 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2903

Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0
using UnityEngine;

[System.Serializable]
public class WheelData
{
    public WheelCollider wheelCollider;
    public Transform wheelTransform;
}

public class TrailerWheelController : MonoBehaviour
{
    public WheelData[] wheels;

    void Update()
    {
        foreach (WheelData wheel in wheels)
        {
            UpdateWheelVisual(wheel);
        }
    }

    void UpdateWheelVisual(WheelData wheel)
    {
        if (wheel.wheelCollider == null || wheel.wheelTransform == null) return;

        Vector3 position;
        Quaternion rotation;
        wheel.wheelCollider.GetWorldPose(out position, out rotation);

        wheel.wheelTransform.position = position;
        wheel.wheelTransform.rotation = rotation;
    }
}

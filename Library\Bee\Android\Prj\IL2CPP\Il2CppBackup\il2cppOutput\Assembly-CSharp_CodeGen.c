﻿#include "pch-c.h"


#include "codegen/il2cpp-codegen-metadata.h"





extern void vThirdPersonCamera_Start_m6F1DB07307F4B8C95E4C5FB201888725E3B02A1B (void);
extern void vThirdPersonCamera_Init_mD8592182BBE5A54455050CC81A0B299E051CF379 (void);
extern void vThirdPersonCamera_FixedUpdate_m99E42E06595EAF27ED0819116573C97D9848091F (void);
extern void vThirdPersonCamera_SetTarget_m6B1C91EF4F2F124087ED2C2219E2B161160CEFF1 (void);
extern void vThirdPersonCamera_SetMainTarget_mAF982B863F84C45555E430FC5D0EE1AB0E97DF2A (void);
extern void vThirdPersonCamera_ScreenPointToRay_m3CA495A90C11FBD5814E381CB36703C5B105D362 (void);
extern void vThirdPersonCamera_RotateCamera_m33FCBB9230395B74A5D0804A2AC1E6F14CCDD8C6 (void);
extern void vThirdPersonCamera_CameraMovement_mEF8E6ACF1BEB937DED51D6612972F41CCFF00807 (void);
extern void vThirdPersonCamera_CullingRayCast_m97DE809FF3AEA955E0BF37821A29DBFF56B336FD (void);
extern void vThirdPersonCamera__ctor_m3D578D8C1B7E5FB5075D1FC778507CAFE08FB8EC (void);
extern void vPickupItem_Start_mC1F1D0B642CB8A9AB3FE99348A259926B55FFD29 (void);
extern void vPickupItem_OnTriggerEnter_m6DA50A9897E0A2E17E648D3B62C068788D1B6B95 (void);
extern void vPickupItem__ctor_mD5C9868D0DE858FCD222F300A536140260BF4594 (void);
extern void JoystickPlayerExample_FixedUpdate_m9AEDBA111F95D67A006A5D3821956048224541B7 (void);
extern void JoystickPlayerExample__ctor_m702422E0AE29402330CF41FDDBEE76F0506342E2 (void);
extern void JoystickSetterExample_ModeChanged_m35AF30EE3E6C8CEBF064A7AB80F5795E9AF06D23 (void);
extern void JoystickSetterExample_AxisChanged_m5CA220FEA14E06BD8A445E31C5B66E4601C5E404 (void);
extern void JoystickSetterExample_SnapX_m25A77C3DE4C6DBBE3B4A58E2DE8CD44B1773D6A1 (void);
extern void JoystickSetterExample_SnapY_m54FE8DCB2CE8D2BF5D2CDF84C68DE263F0E25B1B (void);
extern void JoystickSetterExample_Update_m99B2432D22FE669B4DC3209696AD4A62096C7D41 (void);
extern void JoystickSetterExample__ctor_m2A3D66E05BCDF78D0F116348094717BEBA73EC91 (void);
extern void Joystick_get_Horizontal_m78CF4472B86063E54254AC8AE0A52126E4008AFA (void);
extern void Joystick_get_Vertical_mA2B0917896CF9CE47A6D342D1734E43441C3D4BE (void);
extern void Joystick_get_Direction_m52502695D42BDAB6075089BDE442ABE72EAC81EC (void);
extern void Joystick_get_HandleRange_mB38F0D3B6ADE2D1557D7A3D6759C469F17509D77 (void);
extern void Joystick_set_HandleRange_m686B579A1F02EFCD4878BEA27EA606FC23CD2505 (void);
extern void Joystick_get_DeadZone_mCE52B635A8CF24F6DD867C14E34094515DE6AEFC (void);
extern void Joystick_set_DeadZone_mD5699A14E5284026F303C8AF8D3457DFA9920F19 (void);
extern void Joystick_get_AxisOptions_mA74F5FEE31C158E5179F0B108272ED28A661E388 (void);
extern void Joystick_set_AxisOptions_m541692280806ECF76F7C2C710973AF9D8AB334C6 (void);
extern void Joystick_get_SnapX_m51CAFDCC399606BA82986908700AAA45668A0F40 (void);
extern void Joystick_set_SnapX_mB2090989F6AC933B30823751D74E799FC8D9B87A (void);
extern void Joystick_get_SnapY_m35AFC1AD1DF5EDE5FCE8BAFEBE91AD51D7451613 (void);
extern void Joystick_set_SnapY_m7419D5EB972285A9E5E446CD668BEC266D11CD86 (void);
extern void Joystick_Start_m5E46F090910AB69BE9838BFDB91A4F6E6934480C (void);
extern void Joystick_OnPointerDown_mF176903D532D9129C90BBBD00FD7714BA3A0D8E6 (void);
extern void Joystick_OnDrag_m39E69636AEDC0E471EAD1371A99F4694ECDBA1E9 (void);
extern void Joystick_HandleInput_m15A4E86369A1AF0A4A5727DEC0FD93212A99901C (void);
extern void Joystick_FormatInput_mDDF7AF40138CF227F0106811C8749180FBF45342 (void);
extern void Joystick_SnapFloat_mADE5AF21C67A2298A08CD12F9A8ED73AFA866987 (void);
extern void Joystick_OnPointerUp_mEDED4DA77D954CBAC11CF82B57AB7A4DBFCDE22C (void);
extern void Joystick_ScreenPointToAnchoredPosition_mC1EB7560E844BF682674E4E7BD640604BC12B024 (void);
extern void Joystick__ctor_m9BBE494CA4714F24227F33CB54C10B4DA78BF06B (void);
extern void DynamicJoystick_get_MoveThreshold_m16C670C1DA0A45E83F6F87C4304F459EDDEEDD5A (void);
extern void DynamicJoystick_set_MoveThreshold_m000C1AE325C0B9C33172E4202F2AFB59820517F9 (void);
extern void DynamicJoystick_Start_mFE16C6CE0B753F08E79A2AEC75782DEEF3B96F72 (void);
extern void DynamicJoystick_OnPointerDown_mBFA3026A0DA4A6B53C0E747A1E892CBC7F43E136 (void);
extern void DynamicJoystick_OnPointerUp_m10389907A9D3362F6B75FDC5F35AF37A5DD5AE7C (void);
extern void DynamicJoystick_HandleInput_m3F157F4825BE6682228C8E135581C6404D268506 (void);
extern void DynamicJoystick__ctor_m9DDA6263314BD7B97679DF14A4664358BD3E58CB (void);
extern void FixedJoystick__ctor_m8C8BB74E5EA8CA2C3DD2AE084301EC91F519AD24 (void);
extern void FloatingJoystick_Start_mB22059CD82AF77A8F94AC72E81A8BAE969399E81 (void);
extern void FloatingJoystick_OnPointerDown_mFE5B4F54D5BBCA72F9729AB64765F558FA5C7A54 (void);
extern void FloatingJoystick_OnPointerUp_m80ABA9C914E1953F91DBF74853CE84879352280D (void);
extern void FloatingJoystick__ctor_m6B72425996D46B025F9E9D22121E9D01BEC6BD3C (void);
extern void VariableJoystick_get_MoveThreshold_m8C9D3A63DB3B6CF1F0139C3504EC2E7AC4E7CF99 (void);
extern void VariableJoystick_set_MoveThreshold_m23DC4187B405EB690D297379E2113568B40C705A (void);
extern void VariableJoystick_SetMode_mB9D0B9B6E2E4F431E36AED6F5AC989305ADDB1EE (void);
extern void VariableJoystick_Start_m21743760641EA0317ACCD95949B99825446FE74D (void);
extern void VariableJoystick_OnPointerDown_m8ABE5C8EFBC8DB3A2EE135FFF3C0F67C533AF4B5 (void);
extern void VariableJoystick_OnPointerUp_m65296D82A6C2E1BDC2D622B9C922FAE8E4544526 (void);
extern void VariableJoystick_HandleInput_mD1BCF9A068737A9C057EE8CEB7E6DEB682CC03AB (void);
extern void VariableJoystick__ctor_m6C7B41973BE20A94F16DB5DCC9AA805C5D8DF852 (void);
extern void Dronecamera_Start_mB25EC1D606ABD06CC0436A070B14C8F875896072 (void);
extern void Dronecamera_Update_m0C908CA1F7C5C779BFA7A3112BAA72C0E19610B8 (void);
extern void Dronecamera_MoveCameraWithJoystick_m7234EF3B3047F1620C412C889288B540648ED917 (void);
extern void Dronecamera_HandleMouseDragRotation_mE42BCC518DE5A7F4D79144FBAC1B6967ED63A955 (void);
extern void Dronecamera_HandlePCMouseInput_m0BC5344B78370262DCCE10ECE8E1BEDD3B7ED293 (void);
extern void Dronecamera_HandleMobileTouchInput_m609650BA3225FAC464CC46D89E6CAFD2D65495DD (void);
extern void Dronecamera_ApplyRotationLimits_mF40884CF03F4638E3047A01B7049138237E07764 (void);
extern void Dronecamera_ApplyRotation_mD61C9041292528DBE7135D3A4271279D217316DB (void);
extern void Dronecamera_IsPointerOverUIObject_m654BD5466701B9CCF623FE875961A162BF3FFDE5 (void);
extern void Dronecamera_InitializeHeightButtons_mCABFB813024645DF96FA4A43F1253621587BB418 (void);
extern void Dronecamera_HandleHeightControl_mE408A3FFEB53A0CBE1932DC7417ACC98CCCB4177 (void);
extern void Dronecamera_OnUpHeightButtonDown_m74D3635E0A27B743E5AFBEC7E449379BB1CC202E (void);
extern void Dronecamera_OnUpHeightButtonUp_mA844F1F0D36D4CF66A9F0C3DC06DDA2EAE10A788 (void);
extern void Dronecamera_OnDownHeightButtonDown_mF0F28009D75C2BE7CE8A0ACC699DA138AEBC541A (void);
extern void Dronecamera_OnDownHeightButtonUp_mB9C8EC09CB821A1F67FFBB6B30FD77DB17D3686D (void);
extern void Dronecamera_SetHorizontalSpeed_m5505737BB39CD76313F6E1505F740152371629D4 (void);
extern void Dronecamera_SetVerticalSpeed_m209C4B67BDFCFAA3329F6FD36A3218DA7458B081 (void);
extern void Dronecamera_SetHeightSpeed_m54EABABE05BAB08A9BB8746F8AE9FCF0320E951B (void);
extern void Dronecamera_ResetCameraPosition_m2253B8E6F3E49B59ABF61F4AA85D9B5E3C5D6F45 (void);
extern void Dronecamera_SetCameraPosition_mEAF6EAD8C75CCD796D6DFAE98C1975A77774573A (void);
extern void Dronecamera_MoveUp_m196C1E805E8C56B6D8450478E6AA122E2FD785B5 (void);
extern void Dronecamera_MoveDown_m627BDF1B9B10F02E51118D2613B4DC8462226EB9 (void);
extern void Dronecamera_SetMouseSensitivity_mD28E4B51ECB8C5BEE4FFC4C7C44947FA595077AC (void);
extern void Dronecamera_SetTouchSensitivity_mA2E429CB0EE154C261451604530D0F9E5632D742 (void);
extern void Dronecamera_SetRotationSmoothTime_m4BC752B43266F6EA8AD5ED7638CEF0A508B7A2CE (void);
extern void Dronecamera_ResetCameraRotation_mFA55F9BC8279964A39EDA73506CCCB7C3A7F7EE3 (void);
extern void Dronecamera_SetCameraRotation_mC9E5B973B32745CCE129EF243B79D71B044C855F (void);
extern void Dronecamera_EnableMouseDragRotation_mA249239B610F0264E0F2C2AC3EB3E9FAA2BFE91F (void);
extern void Dronecamera_GetCurrentRotation_m055BDD1618442A62BFCCB42243C355B53C5E71F8 (void);
extern void Dronecamera__ctor_m2F607A561102E691DE39F3DC817C2B1AB4467A33 (void);
extern void Dronecamera_U3CInitializeHeightButtonsU3Eb__49_1_m6172975D6976B11F404CCE862057B23C54FE8AA5 (void);
extern void Dronecamera_U3CInitializeHeightButtonsU3Eb__49_2_mBE8FDA998CCC259CED0E70F27A54F213D5495AED (void);
extern void Dronecamera_U3CInitializeHeightButtonsU3Eb__49_4_mB365B4A8E27965E3EEF895FF6430294A957C6133 (void);
extern void Dronecamera_U3CInitializeHeightButtonsU3Eb__49_5_m4489A4B498D417C860B15EA36C487F6A26F4CCA0 (void);
extern void U3CU3Ec__cctor_m4C5A3084CD5F889C756CBE1EA4772248D7B50931 (void);
extern void U3CU3Ec__ctor_mC7774D2A2B163199BDC2A2510EBEDE304365C24B (void);
extern void U3CU3Ec_U3CInitializeHeightButtonsU3Eb__49_0_m213ADC50D7BE0E5A758E4952754793EE6CED581E (void);
extern void U3CU3Ec_U3CInitializeHeightButtonsU3Eb__49_3_m4ECFF7592DC1F1C0643FB2F454471C65272F231D (void);
extern void MobileUIController_Start_m074EFFE279927A0620065F7811D9CD859DBF7639 (void);
extern void MobileUIController_IsMobilePlatform_m2CD3E196BB2D77024C3D4B4A6689A8D062133DFD (void);
extern void MobileUIController_SetupButtonVisuals_mA747185A69371F962EEE6007939CBCAFC10DC193 (void);
extern void MobileUIController_SetupButtonColors_m4334AFED6EDC4E2F0846BF19E11975CDA50C1007 (void);
extern void MobileUIController_GetJumpButton_m5102F477E3F6439E90E8C8B80CEACC02E82E54D6 (void);
extern void MobileUIController_GetSprintButton_m4BF14023D6F6F097A46A52B0842AC12DA55C2239 (void);
extern void MobileUIController_ShowMobileUI_mF7DB4300600C763AF1A288D36C7E9617E4E3387B (void);
extern void MobileUIController__ctor_m974A0A16C9C910988983411B96043155B93AA610 (void);
extern void UIButtonConnector_Start_m780549563CDE77604AB3B867B40BD4D3A82DFA70 (void);
extern void UIButtonConnector_ConnectButtons_m25F227A8EDC173B76B05CC4044594531FE89EF0F (void);
extern void UIButtonConnector_GetJumpButton_mD593CAF745343EDB2E5EEAAE155194816CB137A5 (void);
extern void UIButtonConnector_GetSprintButton_m41D949630E2C40EB4319199F97625FC7DC1B3B50 (void);
extern void UIButtonConnector_CreateUIButtons_m68530BA6DA8A972C672E2CF5C7D4ABA0356F018F (void);
extern void UIButtonConnector_CreateButton_m70271C7445BE7CF02B5B47032085A3726A81C881 (void);
extern void UIButtonConnector__ctor_mE72F9349DC26E0439C172832A8BA54C8B10924E0 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mBEB95BEB954BB63E9710BBC7AD5E78C4CB0A0033 (void);
extern void UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mE70FB23ACC1EA12ABC948AA22C2E78B2D0AA39B1 (void);
extern void vAnimateUV_Update_m66F5990A03BD0815F7668A782CA115ED8B38389C (void);
extern void vAnimateUV__ctor_mCCD0FC34C3D9D677264B06C082F85CA6A578388D (void);
extern void vExtensions_ClampAngle_mC745F23413CFFE89C380A181B8822B5FD182C136 (void);
extern void vExtensions_NearClipPlanePoints_m003C36BE5383EE7CB45ECA5E95AD25AE252E6C9F (void);
extern void vComment__ctor_m47EB3EA1C77A6C8F268D7A04E165F5AEBADAF45D (void);
extern void vThirdPersonAnimator_UpdateAnimator_m7EA6C596955D09907BD7989E18A8894A5E751C0D (void);
extern void vThirdPersonAnimator_SetAnimatorMoveSpeed_mB1581005F33C90C881ED88407F648ED3CC11DE4B (void);
extern void vThirdPersonAnimator__ctor_m54C353C05D33D60772981256305CE45ACBF990F5 (void);
extern void vAnimatorParameters__cctor_mE37274F16F1A3EE7C96DA6208559DDE802883199 (void);
extern void vThirdPersonController_ControlAnimatorRootMotion_m29906C6CA6E53CC7F75F9914F1B3D6C142847C9B (void);
extern void vThirdPersonController_ControlLocomotionType_m2A92DEF4E973DE8832056BBA1D6407C7F2F53E4A (void);
extern void vThirdPersonController_ControlRotationType_mFF584E6DACAAEEA9B88C771893DD339BF1BD5505 (void);
extern void vThirdPersonController_UpdateMoveDirection_mAC8BF43A75391CE547408FEDB21A47481E6439F9 (void);
extern void vThirdPersonController_Sprint_mCE86802FF093A35683B6ECA6A01B208B5B2B23C5 (void);
extern void vThirdPersonController_Strafe_m900CDEB779AC91FE365EDC25505FD5A9C5F9105F (void);
extern void vThirdPersonController_Jump_mFEE659CBDB64CAAFDBA41005BB7249EF32C13FC0 (void);
extern void vThirdPersonController__ctor_mA24203A184A1DD987239101CC4A952B667192F05 (void);
extern void vThirdPersonInput_Start_m22131DC671E3ABE6F5DEB4324FAC9524C11E5C4F (void);
extern void vThirdPersonInput_FixedUpdate_m792F2A32BED686EFFB21BAA6D08D4EDD53E8ECC3 (void);
extern void vThirdPersonInput_Update_m7C3D6C7100B421A24D0E3369996BBB6D83AF2397 (void);
extern void vThirdPersonInput_OnAnimatorMove_m367FEA4F189AA37EEBA9CC068756635895F0FF6F (void);
extern void vThirdPersonInput_InitilizeController_mBC15F4E84B7940A2EB9287A355AE77FBFFB0EC4F (void);
extern void vThirdPersonInput_InitializeTpCamera_m1C117EA00E2DFFDE4D3EEB65CE1456E541121F40 (void);
extern void vThirdPersonInput_InitializeUIButtons_mC187796FF166E440E496745F3B67159F7F5E9072 (void);
extern void vThirdPersonInput_InputHandle_mC527CBD0644B14DE15769A1EE06E7ABF2D85C889 (void);
extern void vThirdPersonInput_MoveInput_m32BCB96DA8915B3855FF8D3E35B7243F1AA3ED24 (void);
extern void vThirdPersonInput_CameraInput_m356F66B1EDCC345D3D786A275665E5D3B4E46907 (void);
extern void vThirdPersonInput_HandleMobileCameraInput_mEFFD49503BFFF4F510E68BCF12A2EE9AA3BD9FD9 (void);
extern void vThirdPersonInput_IsPointerOverUIObject_m1FDD72FC92436C1EF153562D8C36C5746E938578 (void);
extern void vThirdPersonInput_StrafeInput_m5EB3B2446ADFDD53D5B43D37BA41F925B7FD5525 (void);
extern void vThirdPersonInput_SprintInput_mB845887F060C2DA82AF82B647053211D59E6C3A4 (void);
extern void vThirdPersonInput_JumpConditions_m352F14B518D47C11B2D6592492F9667AE3321A0F (void);
extern void vThirdPersonInput_JumpInput_mC95CE482E949A7C8C79BA84A50461E4D51E7A1DC (void);
extern void vThirdPersonInput_OnJumpButtonDown_m56B624E33CA5FE43352DBEA7E1AD6D6528549D63 (void);
extern void vThirdPersonInput_OnSprintButtonDown_m57557A16F5CE4F173DCFA5815AE7AD30691FB7B5 (void);
extern void vThirdPersonInput_OnSprintButtonUp_m7016765BC25200CCF3082619EF584D0E18EBDB32 (void);
extern void vThirdPersonInput__ctor_m62DB627C943BD934438EEE2941E9B9162636A8C5 (void);
extern void vThirdPersonInput_U3CInitializeUIButtonsU3Eb__26_0_mE104D5F0F6E28899CD7F840811FF547F99F0C711 (void);
extern void vThirdPersonInput_U3CInitializeUIButtonsU3Eb__26_1_m12FDF88C4BE73D288ABA187880DFC5531BFEE649 (void);
extern void vThirdPersonMotor_get_isStrafing_m0010DA1B5F09A4476BC0FE0208A0C303D44C0554 (void);
extern void vThirdPersonMotor_set_isStrafing_mE8B9C5E744B277A9A9E5946B5E3EA62D1D7998E1 (void);
extern void vThirdPersonMotor_get_isGrounded_m4ACD4B011DB81154C903E64F348D5C40A33F3EBF (void);
extern void vThirdPersonMotor_set_isGrounded_m5CBB17B3D9E4695F1C5D34A613FB9FFFFDE4C9CC (void);
extern void vThirdPersonMotor_get_isSprinting_m32D8773AB59AE5974EF52788B6B918D017DEDF96 (void);
extern void vThirdPersonMotor_set_isSprinting_m8A576D71F5515DA1293A296DB288B83AE48308F1 (void);
extern void vThirdPersonMotor_get_stopMove_mF2C27822EEA839C45A83EB09EFED477DF17A6E41 (void);
extern void vThirdPersonMotor_set_stopMove_mF9BF0D7E995A84A769951D057134C25C155D0AD3 (void);
extern void vThirdPersonMotor_Init_m351DBCA1A0583BCE06F7763A5B5CF3B98760C116 (void);
extern void vThirdPersonMotor_UpdateMotor_m5C6DF5E6F8A9F367AC864B35F67385C06368EA8C (void);
extern void vThirdPersonMotor_SetControllerMoveSpeed_m06E3E34132ABE71B41C6BF3D0E0299B1E7B83B50 (void);
extern void vThirdPersonMotor_MoveCharacter_mE787DD8D2F53D9842D6A1FC807B235CA918FE826 (void);
extern void vThirdPersonMotor_CheckSlopeLimit_mA1046544683BF845ED65F2F0F6EAC7D95C8FFF02 (void);
extern void vThirdPersonMotor_RotateToPosition_mCC43C82A74172446AC892E207D5D7DF98C84FA60 (void);
extern void vThirdPersonMotor_RotateToDirection_mABDE0FFC1A11BB1E6DFA6D7EA6D4FA7963B3B453 (void);
extern void vThirdPersonMotor_RotateToDirection_m80AB8DBDC7C74A62333DF69592FAACE5B60D3F90 (void);
extern void vThirdPersonMotor_ControlJumpBehaviour_m782C9CD3E180FDD7623EAE55B56FCC8B2A197BCB (void);
extern void vThirdPersonMotor_AirControl_m92FD4DA87FEE43A5FAEEEBCCBDB862332ADA3D52 (void);
extern void vThirdPersonMotor_get_jumpFwdCondition_m1D7E6D66665659CE207F0BBC76462CD697491622 (void);
extern void vThirdPersonMotor_CheckGround_mA6E7479977005732928E1B8355B5E30A1F88AE28 (void);
extern void vThirdPersonMotor_ControlMaterialPhysics_m805597598BBC43C2554E10937D0055AD1A145653 (void);
extern void vThirdPersonMotor_CheckGroundDistance_m50B96D39F995BF73FCF29CFA5FB7AC36A2784EE7 (void);
extern void vThirdPersonMotor_GroundAngle_mCA033345363F429946BA2B255E87CDA88BD41F1A (void);
extern void vThirdPersonMotor_GroundAngleFromDirection_m8CD881D8E7275D9ED2474AC660234C79B6DBA108 (void);
extern void vThirdPersonMotor__ctor_mA1BD2E6542CB923CF095AD76FD0B05270AB8B0AD (void);
extern void vMovementSpeed__ctor_mC3947D255FF4CE27C7ED19F5E211D7EA04201E54 (void);
static Il2CppMethodPointer s_methodPointers[185] = 
{
	vThirdPersonCamera_Start_m6F1DB07307F4B8C95E4C5FB201888725E3B02A1B,
	vThirdPersonCamera_Init_mD8592182BBE5A54455050CC81A0B299E051CF379,
	vThirdPersonCamera_FixedUpdate_m99E42E06595EAF27ED0819116573C97D9848091F,
	vThirdPersonCamera_SetTarget_m6B1C91EF4F2F124087ED2C2219E2B161160CEFF1,
	vThirdPersonCamera_SetMainTarget_mAF982B863F84C45555E430FC5D0EE1AB0E97DF2A,
	vThirdPersonCamera_ScreenPointToRay_m3CA495A90C11FBD5814E381CB36703C5B105D362,
	vThirdPersonCamera_RotateCamera_m33FCBB9230395B74A5D0804A2AC1E6F14CCDD8C6,
	vThirdPersonCamera_CameraMovement_mEF8E6ACF1BEB937DED51D6612972F41CCFF00807,
	vThirdPersonCamera_CullingRayCast_m97DE809FF3AEA955E0BF37821A29DBFF56B336FD,
	vThirdPersonCamera__ctor_m3D578D8C1B7E5FB5075D1FC778507CAFE08FB8EC,
	vPickupItem_Start_mC1F1D0B642CB8A9AB3FE99348A259926B55FFD29,
	vPickupItem_OnTriggerEnter_m6DA50A9897E0A2E17E648D3B62C068788D1B6B95,
	vPickupItem__ctor_mD5C9868D0DE858FCD222F300A536140260BF4594,
	JoystickPlayerExample_FixedUpdate_m9AEDBA111F95D67A006A5D3821956048224541B7,
	JoystickPlayerExample__ctor_m702422E0AE29402330CF41FDDBEE76F0506342E2,
	JoystickSetterExample_ModeChanged_m35AF30EE3E6C8CEBF064A7AB80F5795E9AF06D23,
	JoystickSetterExample_AxisChanged_m5CA220FEA14E06BD8A445E31C5B66E4601C5E404,
	JoystickSetterExample_SnapX_m25A77C3DE4C6DBBE3B4A58E2DE8CD44B1773D6A1,
	JoystickSetterExample_SnapY_m54FE8DCB2CE8D2BF5D2CDF84C68DE263F0E25B1B,
	JoystickSetterExample_Update_m99B2432D22FE669B4DC3209696AD4A62096C7D41,
	JoystickSetterExample__ctor_m2A3D66E05BCDF78D0F116348094717BEBA73EC91,
	Joystick_get_Horizontal_m78CF4472B86063E54254AC8AE0A52126E4008AFA,
	Joystick_get_Vertical_mA2B0917896CF9CE47A6D342D1734E43441C3D4BE,
	Joystick_get_Direction_m52502695D42BDAB6075089BDE442ABE72EAC81EC,
	Joystick_get_HandleRange_mB38F0D3B6ADE2D1557D7A3D6759C469F17509D77,
	Joystick_set_HandleRange_m686B579A1F02EFCD4878BEA27EA606FC23CD2505,
	Joystick_get_DeadZone_mCE52B635A8CF24F6DD867C14E34094515DE6AEFC,
	Joystick_set_DeadZone_mD5699A14E5284026F303C8AF8D3457DFA9920F19,
	Joystick_get_AxisOptions_mA74F5FEE31C158E5179F0B108272ED28A661E388,
	Joystick_set_AxisOptions_m541692280806ECF76F7C2C710973AF9D8AB334C6,
	Joystick_get_SnapX_m51CAFDCC399606BA82986908700AAA45668A0F40,
	Joystick_set_SnapX_mB2090989F6AC933B30823751D74E799FC8D9B87A,
	Joystick_get_SnapY_m35AFC1AD1DF5EDE5FCE8BAFEBE91AD51D7451613,
	Joystick_set_SnapY_m7419D5EB972285A9E5E446CD668BEC266D11CD86,
	Joystick_Start_m5E46F090910AB69BE9838BFDB91A4F6E6934480C,
	Joystick_OnPointerDown_mF176903D532D9129C90BBBD00FD7714BA3A0D8E6,
	Joystick_OnDrag_m39E69636AEDC0E471EAD1371A99F4694ECDBA1E9,
	Joystick_HandleInput_m15A4E86369A1AF0A4A5727DEC0FD93212A99901C,
	Joystick_FormatInput_mDDF7AF40138CF227F0106811C8749180FBF45342,
	Joystick_SnapFloat_mADE5AF21C67A2298A08CD12F9A8ED73AFA866987,
	Joystick_OnPointerUp_mEDED4DA77D954CBAC11CF82B57AB7A4DBFCDE22C,
	Joystick_ScreenPointToAnchoredPosition_mC1EB7560E844BF682674E4E7BD640604BC12B024,
	Joystick__ctor_m9BBE494CA4714F24227F33CB54C10B4DA78BF06B,
	DynamicJoystick_get_MoveThreshold_m16C670C1DA0A45E83F6F87C4304F459EDDEEDD5A,
	DynamicJoystick_set_MoveThreshold_m000C1AE325C0B9C33172E4202F2AFB59820517F9,
	DynamicJoystick_Start_mFE16C6CE0B753F08E79A2AEC75782DEEF3B96F72,
	DynamicJoystick_OnPointerDown_mBFA3026A0DA4A6B53C0E747A1E892CBC7F43E136,
	DynamicJoystick_OnPointerUp_m10389907A9D3362F6B75FDC5F35AF37A5DD5AE7C,
	DynamicJoystick_HandleInput_m3F157F4825BE6682228C8E135581C6404D268506,
	DynamicJoystick__ctor_m9DDA6263314BD7B97679DF14A4664358BD3E58CB,
	FixedJoystick__ctor_m8C8BB74E5EA8CA2C3DD2AE084301EC91F519AD24,
	FloatingJoystick_Start_mB22059CD82AF77A8F94AC72E81A8BAE969399E81,
	FloatingJoystick_OnPointerDown_mFE5B4F54D5BBCA72F9729AB64765F558FA5C7A54,
	FloatingJoystick_OnPointerUp_m80ABA9C914E1953F91DBF74853CE84879352280D,
	FloatingJoystick__ctor_m6B72425996D46B025F9E9D22121E9D01BEC6BD3C,
	VariableJoystick_get_MoveThreshold_m8C9D3A63DB3B6CF1F0139C3504EC2E7AC4E7CF99,
	VariableJoystick_set_MoveThreshold_m23DC4187B405EB690D297379E2113568B40C705A,
	VariableJoystick_SetMode_mB9D0B9B6E2E4F431E36AED6F5AC989305ADDB1EE,
	VariableJoystick_Start_m21743760641EA0317ACCD95949B99825446FE74D,
	VariableJoystick_OnPointerDown_m8ABE5C8EFBC8DB3A2EE135FFF3C0F67C533AF4B5,
	VariableJoystick_OnPointerUp_m65296D82A6C2E1BDC2D622B9C922FAE8E4544526,
	VariableJoystick_HandleInput_mD1BCF9A068737A9C057EE8CEB7E6DEB682CC03AB,
	VariableJoystick__ctor_m6C7B41973BE20A94F16DB5DCC9AA805C5D8DF852,
	Dronecamera_Start_mB25EC1D606ABD06CC0436A070B14C8F875896072,
	Dronecamera_Update_m0C908CA1F7C5C779BFA7A3112BAA72C0E19610B8,
	Dronecamera_MoveCameraWithJoystick_m7234EF3B3047F1620C412C889288B540648ED917,
	Dronecamera_HandleMouseDragRotation_mE42BCC518DE5A7F4D79144FBAC1B6967ED63A955,
	Dronecamera_HandlePCMouseInput_m0BC5344B78370262DCCE10ECE8E1BEDD3B7ED293,
	Dronecamera_HandleMobileTouchInput_m609650BA3225FAC464CC46D89E6CAFD2D65495DD,
	Dronecamera_ApplyRotationLimits_mF40884CF03F4638E3047A01B7049138237E07764,
	Dronecamera_ApplyRotation_mD61C9041292528DBE7135D3A4271279D217316DB,
	Dronecamera_IsPointerOverUIObject_m654BD5466701B9CCF623FE875961A162BF3FFDE5,
	Dronecamera_InitializeHeightButtons_mCABFB813024645DF96FA4A43F1253621587BB418,
	Dronecamera_HandleHeightControl_mE408A3FFEB53A0CBE1932DC7417ACC98CCCB4177,
	Dronecamera_OnUpHeightButtonDown_m74D3635E0A27B743E5AFBEC7E449379BB1CC202E,
	Dronecamera_OnUpHeightButtonUp_mA844F1F0D36D4CF66A9F0C3DC06DDA2EAE10A788,
	Dronecamera_OnDownHeightButtonDown_mF0F28009D75C2BE7CE8A0ACC699DA138AEBC541A,
	Dronecamera_OnDownHeightButtonUp_mB9C8EC09CB821A1F67FFBB6B30FD77DB17D3686D,
	Dronecamera_SetHorizontalSpeed_m5505737BB39CD76313F6E1505F740152371629D4,
	Dronecamera_SetVerticalSpeed_m209C4B67BDFCFAA3329F6FD36A3218DA7458B081,
	Dronecamera_SetHeightSpeed_m54EABABE05BAB08A9BB8746F8AE9FCF0320E951B,
	Dronecamera_ResetCameraPosition_m2253B8E6F3E49B59ABF61F4AA85D9B5E3C5D6F45,
	Dronecamera_SetCameraPosition_mEAF6EAD8C75CCD796D6DFAE98C1975A77774573A,
	Dronecamera_MoveUp_m196C1E805E8C56B6D8450478E6AA122E2FD785B5,
	Dronecamera_MoveDown_m627BDF1B9B10F02E51118D2613B4DC8462226EB9,
	Dronecamera_SetMouseSensitivity_mD28E4B51ECB8C5BEE4FFC4C7C44947FA595077AC,
	Dronecamera_SetTouchSensitivity_mA2E429CB0EE154C261451604530D0F9E5632D742,
	Dronecamera_SetRotationSmoothTime_m4BC752B43266F6EA8AD5ED7638CEF0A508B7A2CE,
	Dronecamera_ResetCameraRotation_mFA55F9BC8279964A39EDA73506CCCB7C3A7F7EE3,
	Dronecamera_SetCameraRotation_mC9E5B973B32745CCE129EF243B79D71B044C855F,
	Dronecamera_EnableMouseDragRotation_mA249239B610F0264E0F2C2AC3EB3E9FAA2BFE91F,
	Dronecamera_GetCurrentRotation_m055BDD1618442A62BFCCB42243C355B53C5E71F8,
	Dronecamera__ctor_m2F607A561102E691DE39F3DC817C2B1AB4467A33,
	Dronecamera_U3CInitializeHeightButtonsU3Eb__49_1_m6172975D6976B11F404CCE862057B23C54FE8AA5,
	Dronecamera_U3CInitializeHeightButtonsU3Eb__49_2_mBE8FDA998CCC259CED0E70F27A54F213D5495AED,
	Dronecamera_U3CInitializeHeightButtonsU3Eb__49_4_mB365B4A8E27965E3EEF895FF6430294A957C6133,
	Dronecamera_U3CInitializeHeightButtonsU3Eb__49_5_m4489A4B498D417C860B15EA36C487F6A26F4CCA0,
	U3CU3Ec__cctor_m4C5A3084CD5F889C756CBE1EA4772248D7B50931,
	U3CU3Ec__ctor_mC7774D2A2B163199BDC2A2510EBEDE304365C24B,
	U3CU3Ec_U3CInitializeHeightButtonsU3Eb__49_0_m213ADC50D7BE0E5A758E4952754793EE6CED581E,
	U3CU3Ec_U3CInitializeHeightButtonsU3Eb__49_3_m4ECFF7592DC1F1C0643FB2F454471C65272F231D,
	MobileUIController_Start_m074EFFE279927A0620065F7811D9CD859DBF7639,
	MobileUIController_IsMobilePlatform_m2CD3E196BB2D77024C3D4B4A6689A8D062133DFD,
	MobileUIController_SetupButtonVisuals_mA747185A69371F962EEE6007939CBCAFC10DC193,
	MobileUIController_SetupButtonColors_m4334AFED6EDC4E2F0846BF19E11975CDA50C1007,
	MobileUIController_GetJumpButton_m5102F477E3F6439E90E8C8B80CEACC02E82E54D6,
	MobileUIController_GetSprintButton_m4BF14023D6F6F097A46A52B0842AC12DA55C2239,
	MobileUIController_ShowMobileUI_mF7DB4300600C763AF1A288D36C7E9617E4E3387B,
	MobileUIController__ctor_m974A0A16C9C910988983411B96043155B93AA610,
	UIButtonConnector_Start_m780549563CDE77604AB3B867B40BD4D3A82DFA70,
	UIButtonConnector_ConnectButtons_m25F227A8EDC173B76B05CC4044594531FE89EF0F,
	UIButtonConnector_GetJumpButton_mD593CAF745343EDB2E5EEAAE155194816CB137A5,
	UIButtonConnector_GetSprintButton_m41D949630E2C40EB4319199F97625FC7DC1B3B50,
	UIButtonConnector_CreateUIButtons_m68530BA6DA8A972C672E2CF5C7D4ABA0356F018F,
	UIButtonConnector_CreateButton_m70271C7445BE7CF02B5B47032085A3726A81C881,
	UIButtonConnector__ctor_mE72F9349DC26E0439C172832A8BA54C8B10924E0,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1_Get_mBEB95BEB954BB63E9710BBC7AD5E78C4CB0A0033,
	UnitySourceGeneratedAssemblyMonoScriptTypes_v1__ctor_mE70FB23ACC1EA12ABC948AA22C2E78B2D0AA39B1,
	vAnimateUV_Update_m66F5990A03BD0815F7668A782CA115ED8B38389C,
	vAnimateUV__ctor_mCCD0FC34C3D9D677264B06C082F85CA6A578388D,
	NULL,
	NULL,
	vExtensions_ClampAngle_mC745F23413CFFE89C380A181B8822B5FD182C136,
	vExtensions_NearClipPlanePoints_m003C36BE5383EE7CB45ECA5E95AD25AE252E6C9F,
	vComment__ctor_m47EB3EA1C77A6C8F268D7A04E165F5AEBADAF45D,
	vThirdPersonAnimator_UpdateAnimator_m7EA6C596955D09907BD7989E18A8894A5E751C0D,
	vThirdPersonAnimator_SetAnimatorMoveSpeed_mB1581005F33C90C881ED88407F648ED3CC11DE4B,
	vThirdPersonAnimator__ctor_m54C353C05D33D60772981256305CE45ACBF990F5,
	vAnimatorParameters__cctor_mE37274F16F1A3EE7C96DA6208559DDE802883199,
	vThirdPersonController_ControlAnimatorRootMotion_m29906C6CA6E53CC7F75F9914F1B3D6C142847C9B,
	vThirdPersonController_ControlLocomotionType_m2A92DEF4E973DE8832056BBA1D6407C7F2F53E4A,
	vThirdPersonController_ControlRotationType_mFF584E6DACAAEEA9B88C771893DD339BF1BD5505,
	vThirdPersonController_UpdateMoveDirection_mAC8BF43A75391CE547408FEDB21A47481E6439F9,
	vThirdPersonController_Sprint_mCE86802FF093A35683B6ECA6A01B208B5B2B23C5,
	vThirdPersonController_Strafe_m900CDEB779AC91FE365EDC25505FD5A9C5F9105F,
	vThirdPersonController_Jump_mFEE659CBDB64CAAFDBA41005BB7249EF32C13FC0,
	vThirdPersonController__ctor_mA24203A184A1DD987239101CC4A952B667192F05,
	vThirdPersonInput_Start_m22131DC671E3ABE6F5DEB4324FAC9524C11E5C4F,
	vThirdPersonInput_FixedUpdate_m792F2A32BED686EFFB21BAA6D08D4EDD53E8ECC3,
	vThirdPersonInput_Update_m7C3D6C7100B421A24D0E3369996BBB6D83AF2397,
	vThirdPersonInput_OnAnimatorMove_m367FEA4F189AA37EEBA9CC068756635895F0FF6F,
	vThirdPersonInput_InitilizeController_mBC15F4E84B7940A2EB9287A355AE77FBFFB0EC4F,
	vThirdPersonInput_InitializeTpCamera_m1C117EA00E2DFFDE4D3EEB65CE1456E541121F40,
	vThirdPersonInput_InitializeUIButtons_mC187796FF166E440E496745F3B67159F7F5E9072,
	vThirdPersonInput_InputHandle_mC527CBD0644B14DE15769A1EE06E7ABF2D85C889,
	vThirdPersonInput_MoveInput_m32BCB96DA8915B3855FF8D3E35B7243F1AA3ED24,
	vThirdPersonInput_CameraInput_m356F66B1EDCC345D3D786A275665E5D3B4E46907,
	vThirdPersonInput_HandleMobileCameraInput_mEFFD49503BFFF4F510E68BCF12A2EE9AA3BD9FD9,
	vThirdPersonInput_IsPointerOverUIObject_m1FDD72FC92436C1EF153562D8C36C5746E938578,
	vThirdPersonInput_StrafeInput_m5EB3B2446ADFDD53D5B43D37BA41F925B7FD5525,
	vThirdPersonInput_SprintInput_mB845887F060C2DA82AF82B647053211D59E6C3A4,
	vThirdPersonInput_JumpConditions_m352F14B518D47C11B2D6592492F9667AE3321A0F,
	vThirdPersonInput_JumpInput_mC95CE482E949A7C8C79BA84A50461E4D51E7A1DC,
	vThirdPersonInput_OnJumpButtonDown_m56B624E33CA5FE43352DBEA7E1AD6D6528549D63,
	vThirdPersonInput_OnSprintButtonDown_m57557A16F5CE4F173DCFA5815AE7AD30691FB7B5,
	vThirdPersonInput_OnSprintButtonUp_m7016765BC25200CCF3082619EF584D0E18EBDB32,
	vThirdPersonInput__ctor_m62DB627C943BD934438EEE2941E9B9162636A8C5,
	vThirdPersonInput_U3CInitializeUIButtonsU3Eb__26_0_mE104D5F0F6E28899CD7F840811FF547F99F0C711,
	vThirdPersonInput_U3CInitializeUIButtonsU3Eb__26_1_m12FDF88C4BE73D288ABA187880DFC5531BFEE649,
	vThirdPersonMotor_get_isStrafing_m0010DA1B5F09A4476BC0FE0208A0C303D44C0554,
	vThirdPersonMotor_set_isStrafing_mE8B9C5E744B277A9A9E5946B5E3EA62D1D7998E1,
	vThirdPersonMotor_get_isGrounded_m4ACD4B011DB81154C903E64F348D5C40A33F3EBF,
	vThirdPersonMotor_set_isGrounded_m5CBB17B3D9E4695F1C5D34A613FB9FFFFDE4C9CC,
	vThirdPersonMotor_get_isSprinting_m32D8773AB59AE5974EF52788B6B918D017DEDF96,
	vThirdPersonMotor_set_isSprinting_m8A576D71F5515DA1293A296DB288B83AE48308F1,
	vThirdPersonMotor_get_stopMove_mF2C27822EEA839C45A83EB09EFED477DF17A6E41,
	vThirdPersonMotor_set_stopMove_mF9BF0D7E995A84A769951D057134C25C155D0AD3,
	vThirdPersonMotor_Init_m351DBCA1A0583BCE06F7763A5B5CF3B98760C116,
	vThirdPersonMotor_UpdateMotor_m5C6DF5E6F8A9F367AC864B35F67385C06368EA8C,
	vThirdPersonMotor_SetControllerMoveSpeed_m06E3E34132ABE71B41C6BF3D0E0299B1E7B83B50,
	vThirdPersonMotor_MoveCharacter_mE787DD8D2F53D9842D6A1FC807B235CA918FE826,
	vThirdPersonMotor_CheckSlopeLimit_mA1046544683BF845ED65F2F0F6EAC7D95C8FFF02,
	vThirdPersonMotor_RotateToPosition_mCC43C82A74172446AC892E207D5D7DF98C84FA60,
	vThirdPersonMotor_RotateToDirection_mABDE0FFC1A11BB1E6DFA6D7EA6D4FA7963B3B453,
	vThirdPersonMotor_RotateToDirection_m80AB8DBDC7C74A62333DF69592FAACE5B60D3F90,
	vThirdPersonMotor_ControlJumpBehaviour_m782C9CD3E180FDD7623EAE55B56FCC8B2A197BCB,
	vThirdPersonMotor_AirControl_m92FD4DA87FEE43A5FAEEEBCCBDB862332ADA3D52,
	vThirdPersonMotor_get_jumpFwdCondition_m1D7E6D66665659CE207F0BBC76462CD697491622,
	vThirdPersonMotor_CheckGround_mA6E7479977005732928E1B8355B5E30A1F88AE28,
	vThirdPersonMotor_ControlMaterialPhysics_m805597598BBC43C2554E10937D0055AD1A145653,
	vThirdPersonMotor_CheckGroundDistance_m50B96D39F995BF73FCF29CFA5FB7AC36A2784EE7,
	vThirdPersonMotor_GroundAngle_mCA033345363F429946BA2B255E87CDA88BD41F1A,
	vThirdPersonMotor_GroundAngleFromDirection_m8CD881D8E7275D9ED2474AC660234C79B6DBA108,
	vThirdPersonMotor__ctor_mA1BD2E6542CB923CF095AD76FD0B05270AB8B0AD,
	vMovementSpeed__ctor_mC3947D255FF4CE27C7ED19F5E211D7EA04201E54,
};
static const int32_t s_InvokerIndices[185] = 
{
	7102,
	7102,
	7102,
	5600,
	5600,
	4831,
	2941,
	7102,
	160,
	7102,
	7102,
	5600,
	7102,
	7102,
	7102,
	5562,
	5562,
	5484,
	5484,
	7102,
	7102,
	7033,
	7033,
	7092,
	7033,
	5639,
	7033,
	5639,
	6946,
	5562,
	6869,
	5484,
	6869,
	5484,
	7102,
	5600,
	5600,
	984,
	7102,
	2182,
	5600,
	4989,
	7102,
	7033,
	5639,
	7102,
	5600,
	5600,
	984,
	7102,
	7102,
	7102,
	5600,
	5600,
	7102,
	7033,
	5639,
	5562,
	7102,
	5600,
	5600,
	984,
	7102,
	7102,
	7102,
	7102,
	7102,
	7102,
	7102,
	7102,
	7102,
	6869,
	7102,
	7102,
	7102,
	7102,
	7102,
	7102,
	5639,
	5639,
	5639,
	7102,
	5699,
	7102,
	7102,
	5639,
	5639,
	5639,
	7102,
	5699,
	5484,
	7094,
	7102,
	5600,
	5600,
	5600,
	5600,
	10294,
	7102,
	7102,
	7102,
	7102,
	6869,
	7102,
	5600,
	6985,
	6985,
	5484,
	7102,
	7102,
	7102,
	6985,
	6985,
	7102,
	1377,
	7102,
	10307,
	7102,
	7102,
	7102,
	-1,
	-1,
	8535,
	8321,
	7102,
	7102,
	5600,
	7102,
	10294,
	7102,
	7102,
	7102,
	5600,
	5484,
	7102,
	7102,
	7102,
	7102,
	7102,
	7102,
	7102,
	7102,
	7102,
	7102,
	7102,
	7102,
	7102,
	2247,
	3954,
	7102,
	7102,
	6869,
	7102,
	7102,
	7102,
	7102,
	7102,
	5600,
	5600,
	6869,
	5484,
	6869,
	5484,
	6869,
	5484,
	6869,
	5484,
	7102,
	7102,
	5600,
	5699,
	7102,
	5699,
	5699,
	2979,
	7102,
	7102,
	6869,
	7102,
	7102,
	7102,
	7033,
	7033,
	7102,
	7102,
};
static const Il2CppTokenRangePair s_rgctxIndices[2] = 
{
	{ 0x06000079, { 0, 2 } },
	{ 0x0600007A, { 2, 6 } },
};
extern const uint32_t g_rgctx_TU5BU5D_t40147BBCEAA0A5264E3A5C5694FB12D8E6EFE786;
extern const uint32_t g_rgctx_TU5BU5D_t40147BBCEAA0A5264E3A5C5694FB12D8E6EFE786;
extern const uint32_t g_rgctx_List_1_t88D549F10EC7BB162AED6199B808B78143B4910E;
extern const uint32_t g_rgctx_List_1_get_Count_m37095E89C49AE00C84041F2024269705A32DE769;
extern const uint32_t g_rgctx_TU5BU5D_t906591AF5E99665BFD6851FC108DE81FB153B5CA;
extern const uint32_t g_rgctx_List_1_get_Item_m11F704C70B92289686A70C58F62C464E59CB37E0;
extern const uint32_t g_rgctx_T_tE379295323EC66BA39F909E4A3286F43034F7B92;
extern const uint32_t g_rgctx_TU5BU5D_t906591AF5E99665BFD6851FC108DE81FB153B5CA;
static const Il2CppRGCTXDefinition s_rgctxValues[8] = 
{
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t40147BBCEAA0A5264E3A5C5694FB12D8E6EFE786 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t40147BBCEAA0A5264E3A5C5694FB12D8E6EFE786 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_List_1_t88D549F10EC7BB162AED6199B808B78143B4910E },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Count_m37095E89C49AE00C84041F2024269705A32DE769 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t906591AF5E99665BFD6851FC108DE81FB153B5CA },
	{ (Il2CppRGCTXDataType)3, (const void *)&g_rgctx_List_1_get_Item_m11F704C70B92289686A70C58F62C464E59CB37E0 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_T_tE379295323EC66BA39F909E4A3286F43034F7B92 },
	{ (Il2CppRGCTXDataType)2, (const void *)&g_rgctx_TU5BU5D_t906591AF5E99665BFD6851FC108DE81FB153B5CA },
};
IL2CPP_EXTERN_C const Il2CppCodeGenModule g_AssemblyU2DCSharp_CodeGenModule;
const Il2CppCodeGenModule g_AssemblyU2DCSharp_CodeGenModule = 
{
	"Assembly-CSharp.dll",
	185,
	s_methodPointers,
	0,
	NULL,
	s_InvokerIndices,
	0,
	NULL,
	2,
	s_rgctxIndices,
	8,
	s_rgctxValues,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
};

%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1028851606859769006
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3708521716199821404}
  m_Layer: 0
  m_Name: RocketLauncher
  m_TagString: Enemy
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3708521716199821404
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1028851606859769006}
  m_LocalRotation: {x: -0, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 0.03, y: 5.42, z: -2.28}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7772498568873258365}
  - {fileID: 2194234994821413389}
  - {fileID: 7589017131052010343}
  m_Father: {fileID: 8538670241595308183}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1932297135758513210
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7589017131052010343}
  - component: {fileID: 4156328279074287571}
  - component: {fileID: 8711447092225323397}
  - component: {fileID: 7442107603343962734}
  - component: {fileID: 8687956929894134668}
  m_Layer: 0
  m_Name: LAUNCHER_THRUSTER
  m_TagString: Enemy
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7589017131052010343
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1932297135758513210}
  m_LocalRotation: {x: -0, y: -0.96583605, z: -0.2591539, w: 0}
  m_LocalPosition: {x: -0.03, y: -5.04, z: 1.94}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3708521716199821404}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: -30.04, y: -180, z: 0}
--- !u!33 &4156328279074287571
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1932297135758513210}
  m_Mesh: {fileID: -5001634403495201615, guid: d873849000c734c4f99c6f4a5c886235, type: 3}
--- !u!23 &8711447092225323397
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1932297135758513210}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 60350b33d9feb4861ae382e3b1ff520b, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!54 &7442107603343962734
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1932297135758513210}
  serializedVersion: 2
  m_Mass: 5
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!64 &8687956929894134668
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1932297135758513210}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: -5001634403495201615, guid: d873849000c734c4f99c6f4a5c886235, type: 3}
--- !u!1 &2277890697013247393
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2194234994821413389}
  - component: {fileID: 5777296297798795466}
  - component: {fileID: 7396359519794149011}
  m_Layer: 0
  m_Name: I_MISSILES
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &2194234994821413389
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2277890697013247393}
  m_LocalRotation: {x: -0, y: -1, z: -0, w: 0}
  m_LocalPosition: {x: -0.03, y: -3.37, z: 4.9300003}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3708521716199821404}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: -180, z: 0}
--- !u!33 &5777296297798795466
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2277890697013247393}
  m_Mesh: {fileID: -4013485220714855852, guid: d873849000c734c4f99c6f4a5c886235, type: 3}
--- !u!23 &7396359519794149011
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2277890697013247393}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 65aa74c79c63c45bbbea1e3405e6facc, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &2748888244959192996
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3273505497331432734}
  - component: {fileID: 6910278768328692896}
  - component: {fileID: 3778170581633409174}
  m_Layer: 0
  m_Name: EnemyBody
  m_TagString: Enemy
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3273505497331432734
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2748888244959192996}
  m_LocalRotation: {x: -0.5, y: -0.5, z: -0.5, w: 0.5}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 2.5, y: 2.5, z: 2.5}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 510681442136868207}
  m_Father: {fileID: 8538670241595308183}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: -90, y: -90, z: 0}
--- !u!54 &6910278768328692896
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2748888244959192996}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!65 &3778170581633409174
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2748888244959192996}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 7.979669, y: 3.0958612, z: 2.0322893}
  m_Center: {x: -0.56281394, y: -0.022809124, z: 1.3729796}
--- !u!1 &2896348568580954076
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7772498568873258365}
  - component: {fileID: 1272677417665597532}
  - component: {fileID: 8888905667489802065}
  - component: {fileID: 1227991368924961125}
  - component: {fileID: 8495355329956105368}
  m_Layer: 0
  m_Name: 3_MISSILES
  m_TagString: Enemy
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7772498568873258365
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2896348568580954076}
  m_LocalRotation: {x: -0, y: -0.96583605, z: -0.2591539, w: 0}
  m_LocalPosition: {x: -0.03, y: -5.04, z: 1.94}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3708521716199821404}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: -30.04, y: -180, z: 0}
--- !u!33 &1272677417665597532
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2896348568580954076}
  m_Mesh: {fileID: -197747360096844584, guid: d873849000c734c4f99c6f4a5c886235, type: 3}
--- !u!23 &8888905667489802065
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2896348568580954076}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 60350b33d9feb4861ae382e3b1ff520b, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!54 &1227991368924961125
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2896348568580954076}
  serializedVersion: 2
  m_Mass: 5
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!64 &8495355329956105368
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2896348568580954076}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 0
  serializedVersion: 4
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: -197747360096844584, guid: d873849000c734c4f99c6f4a5c886235, type: 3}
--- !u!1 &3590984176884662156
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3168552233024859849}
  - component: {fileID: 4690109231040853134}
  - component: {fileID: 330697045492061111}
  m_Layer: 0
  m_Name: Sprite_Enemy_Pointer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3168552233024859849
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3590984176884662156}
  m_LocalRotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0.13, z: -1.93}
  m_LocalScale: {x: 3, y: 3, z: 3}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8538670241595308183}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!212 &4690109231040853134
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3590984176884662156}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: 87c17017aeca341109664b346b211375, type: 3}
  m_Color: {r: 1, g: 0, b: 0.0117173195, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1.28, y: 1.28}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!111 &330697045492061111
Animation:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3590984176884662156}
  m_Enabled: 1
  serializedVersion: 3
  m_Animation: {fileID: 7400000, guid: ffa48aa8f941245638ad592bb71e35c5, type: 2}
  m_Animations:
  - {fileID: 7400000, guid: ffa48aa8f941245638ad592bb71e35c5, type: 2}
  m_WrapMode: 0
  m_PlayAutomatically: 1
  m_AnimatePhysics: 0
  m_CullingType: 0
--- !u!1 &6544482809255549627
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 510681442136868207}
  - component: {fileID: 6983859438398501518}
  - component: {fileID: 9203680282364719178}
  m_Layer: 0
  m_Name: obj1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &510681442136868207
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6544482809255549627}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3273505497331432734}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &6983859438398501518
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6544482809255549627}
  m_Mesh: {fileID: -4284182221599607849, guid: f1198042a4ec5445d8c5ecace887f9ec, type: 3}
--- !u!23 &9203680282364719178
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6544482809255549627}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 47bd910701f554659bbf97986d25beb8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &8211691757529059584
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8211691757529059585}
  m_Layer: 0
  m_Name: FiringPoint
  m_TagString: Enemy
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8211691757529059585
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8211691757529059584}
  m_LocalRotation: {x: 0, y: -1, z: 0, w: 0}
  m_LocalPosition: {x: -0.45, y: 9.31, z: -3.32}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8538670241595308183}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: -180, z: 0}
--- !u!1 &9022016225501045293
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8538670241595308183}
  - component: {fileID: 8211691756694134650}
  - component: {fileID: -6773241956174781782}
  - component: {fileID: 4534773716063012931}
  - component: {fileID: 7691396288205938568}
  m_Layer: 0
  m_Name: Enemy
  m_TagString: Enemy
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8538670241595308183
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9022016225501045293}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3273505497331432734}
  - {fileID: 8211691757529059585}
  - {fileID: 3708521716199821404}
  - {fileID: 3168552233024859849}
  - {fileID: 6575664069753258335}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8211691756694134650
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9022016225501045293}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ad19f517ba67b4154b2030faa33cae58, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Health: 100
  Range: 150
  Firing_Interval: 5
  EnemyMissile: {fileID: 7554301599605368831, guid: 392c4dbeb782140c9830e54f70d7bcbc, type: 3}
  Firing_Point: {fileID: 8211691757529059585}
  MissileLauncher: {fileID: 3708521716199821404}
  SpritePointer: {fileID: 3590984176884662156}
  MainCollider: {fileID: 4534773716063012931}
  particle: {fileID: 6575664069753258322}
  Accuracy: 8
--- !u!114 &-6773241956174781782
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9022016225501045293}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3cd28c75eff74465bab7954b096b9091, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TargetType: 4
--- !u!65 &4534773716063012931
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9022016225501045293}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 7.547713, y: 4.7045145, z: 19.837532}
  m_Center: {x: -0.06408429, y: 3.467856, z: -1.4037762}
--- !u!195 &7691396288205938568
NavMeshAgent:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9022016225501045293}
  m_Enabled: 1
  m_AgentTypeID: 0
  m_Radius: 9.17
  m_Speed: 3.5
  m_Acceleration: 8
  avoidancePriority: 50
  m_AngularSpeed: 120
  m_StoppingDistance: 2
  m_AutoTraverseOffMeshLink: 1
  m_AutoBraking: 0
  m_AutoRepath: 1
  m_Height: 6.04
  m_BaseOffset: -0.87
  m_WalkableMask: 4294967295
  m_ObstacleAvoidanceType: 4
--- !u!1001 &8512723524010874823
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 2
    m_TransformParent: {fileID: 8538670241595308183}
    m_Modifications:
    - target: {fileID: 3270237098715043477, guid: bde91557fb87445f2b96a1ae231758e0, type: 3}
      propertyPath: m_Name
      value: BubblesStream03
      objectReference: {fileID: 0}
    - target: {fileID: 3270237098715043478, guid: bde91557fb87445f2b96a1ae231758e0, type: 3}
      propertyPath: lengthInSec
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: 3270237098715043478, guid: bde91557fb87445f2b96a1ae231758e0, type: 3}
      propertyPath: InitialModule.maxNumParticles
      value: 150
      objectReference: {fileID: 0}
    - target: {fileID: 3270237098715043478, guid: bde91557fb87445f2b96a1ae231758e0, type: 3}
      propertyPath: EmissionModule.rateOverTime.scalar
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 3270237098715043478, guid: bde91557fb87445f2b96a1ae231758e0, type: 3}
      propertyPath: InitialModule.startLifetime.scalar
      value: 9
      objectReference: {fileID: 0}
    - target: {fileID: 3270237098715043480, guid: bde91557fb87445f2b96a1ae231758e0, type: 3}
      propertyPath: m_RootOrder
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 3270237098715043480, guid: bde91557fb87445f2b96a1ae231758e0, type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.06
      objectReference: {fileID: 0}
    - target: {fileID: 3270237098715043480, guid: bde91557fb87445f2b96a1ae231758e0, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.44000006
      objectReference: {fileID: 0}
    - target: {fileID: 3270237098715043480, guid: bde91557fb87445f2b96a1ae231758e0, type: 3}
      propertyPath: m_LocalPosition.z
      value: 6.91
      objectReference: {fileID: 0}
    - target: {fileID: 3270237098715043480, guid: bde91557fb87445f2b96a1ae231758e0, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.013632057
      objectReference: {fileID: 0}
    - target: {fileID: 3270237098715043480, guid: bde91557fb87445f2b96a1ae231758e0, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3270237098715043480, guid: bde91557fb87445f2b96a1ae231758e0, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.99990714
      objectReference: {fileID: 0}
    - target: {fileID: 3270237098715043480, guid: bde91557fb87445f2b96a1ae231758e0, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3270237098715043480, guid: bde91557fb87445f2b96a1ae231758e0, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3270237098715043480, guid: bde91557fb87445f2b96a1ae231758e0, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3270237098715043480, guid: bde91557fb87445f2b96a1ae231758e0, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_AddedGameObjects: []
  m_SourcePrefab: {fileID: 100100000, guid: bde91557fb87445f2b96a1ae231758e0, type: 3}
--- !u!1 &6575664069753258322 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 3270237098715043477, guid: bde91557fb87445f2b96a1ae231758e0, type: 3}
  m_PrefabInstance: {fileID: 8512723524010874823}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &6575664069753258335 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 3270237098715043480, guid: bde91557fb87445f2b96a1ae231758e0, type: 3}
  m_PrefabInstance: {fileID: 8512723524010874823}
  m_PrefabAsset: {fileID: 0}

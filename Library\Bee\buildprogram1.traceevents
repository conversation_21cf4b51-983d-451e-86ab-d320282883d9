{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "netcorerun.dll" } },
{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "-1" } },
{ "pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 35942, "tid": 1, "ts": 1751348789555162, "dur": 823482, "ph": "X", "name": "BuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751348789556783, "dur": 71389, "ph": "X", "name": "BuildProgramContextConstructor", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751348789654386, "dur": 670022, "ph": "X", "name": "RunBuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751348789655525, "dur": 556316, "ph": "X", "name": "PlayerBuildProgramBase.SetupPlayerBuild", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751348789656732, "dur": 77078, "ph": "X", "name": "SetupDataFiles", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751348789734449, "dur": 961, "ph": "X", "name": "SetupCopyPlugins", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751348789738264, "dur": 111905, "ph": "X", "name": "SetupUnityLinker", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751348789851266, "dur": 349800, "ph": "X", "name": "SetupIl2CppBuild", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751348789852529, "dur": 65206, "ph": "X", "name": "SetupIl2Cpp", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751348789975363, "dur": 211800, "ph": "X", "name": "SetupSpecificConfigImpl libil2cpp", "args": {"info": "release_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1751348789988670, "dur": 155138, "ph": "X", "name": "SetupSpecificConfigImpl il2cpp", "args": {"info": "release_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1751348789991252, "dur": 81957, "ph": "X", "name": "SetupSpecificConfigImpl bdwgc", "args": {"info": "release_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1751348790073250, "dur": 4625, "ph": "X", "name": "SetupSpecificConfigImpl zlib", "args": {"info": "release_Android_arm64"} },
{ "pid": 35942, "tid": 1, "ts": 1751348790201950, "dur": 9890, "ph": "X", "name": "SetupCopyDataIl2cpp", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751348790336646, "dur": 1815, "ph": "X", "name": "OutputData.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751348790338463, "dur": 40178, "ph": "X", "name": "Backend.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751348790340104, "dur": 31786, "ph": "X", "name": "JsonToString", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751348790388384, "dur": 2976, "ph": "X", "name": "", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1751348790387815, "dur": 3921, "ph": "X", "name": "Write chrome-trace events", "args": {} },

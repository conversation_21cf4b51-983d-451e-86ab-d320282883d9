{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 41616, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 41616, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 41616, "tid": 1682, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 41616, "tid": 1682, "ts": 1751353004527512, "dur": 1117, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 41616, "tid": 1682, "ts": 1751353004536154, "dur": 1583, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 41616, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 41616, "tid": 1, "ts": 1751353003701035, "dur": 10528, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 41616, "tid": 1, "ts": 1751353003711570, "dur": 71581, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 41616, "tid": 1, "ts": 1751353003783166, "dur": 63845, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 41616, "tid": 1682, "ts": 1751353004537744, "dur": 15, "ph": "X", "name": "", "args": {}}, {"pid": 41616, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003696034, "dur": 26250, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003722288, "dur": 789003, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003724053, "dur": 5670, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003729745, "dur": 5212, "ph": "X", "name": "ProcessMessages 20484", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003734977, "dur": 1062, "ph": "X", "name": "ReadAsync 20484", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003736048, "dur": 46, "ph": "X", "name": "ProcessMessages 20489", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003736097, "dur": 105, "ph": "X", "name": "ReadAsync 20489", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003736210, "dur": 2, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003736215, "dur": 59, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003736281, "dur": 3, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003736286, "dur": 56, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003736350, "dur": 3, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003736357, "dur": 60, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003736424, "dur": 2, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003736433, "dur": 47, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003736485, "dur": 2, "ph": "X", "name": "ProcessMessages 69", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003736488, "dur": 88, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003736580, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003736582, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003736643, "dur": 2, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003736648, "dur": 68, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003736723, "dur": 2, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003736728, "dur": 60, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003736794, "dur": 2, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003736799, "dur": 59, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003736864, "dur": 2, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003736869, "dur": 56, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003736930, "dur": 3, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003736935, "dur": 58, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737001, "dur": 2, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737005, "dur": 61, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737073, "dur": 2, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737077, "dur": 60, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737145, "dur": 2, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737150, "dur": 62, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737219, "dur": 2, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737223, "dur": 65, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737295, "dur": 4, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737301, "dur": 63, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737369, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737373, "dur": 66, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737446, "dur": 4, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737456, "dur": 57, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737520, "dur": 2, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737524, "dur": 59, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737590, "dur": 2, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737595, "dur": 52, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737652, "dur": 3, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737656, "dur": 68, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737730, "dur": 2, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737735, "dur": 53, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737793, "dur": 2, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737800, "dur": 60, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737868, "dur": 2, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737872, "dur": 63, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737942, "dur": 2, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003737946, "dur": 56, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738010, "dur": 2, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738014, "dur": 64, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738085, "dur": 11, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738100, "dur": 55, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738162, "dur": 2, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738167, "dur": 54, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738230, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738234, "dur": 64, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738305, "dur": 3, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738310, "dur": 65, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738382, "dur": 2, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738386, "dur": 54, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738445, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738447, "dur": 45, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738496, "dur": 2, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738499, "dur": 43, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738551, "dur": 2, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738555, "dur": 57, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738620, "dur": 2, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738624, "dur": 56, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738684, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738686, "dur": 51, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738742, "dur": 2, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738747, "dur": 57, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738808, "dur": 2, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738811, "dur": 62, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738880, "dur": 3, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738886, "dur": 56, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738949, "dur": 2, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003738953, "dur": 57, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739017, "dur": 3, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739022, "dur": 47, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739072, "dur": 1, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739075, "dur": 47, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739126, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739129, "dur": 41, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739174, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739177, "dur": 56, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739237, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739240, "dur": 49, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739294, "dur": 2, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739298, "dur": 39, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739340, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739343, "dur": 44, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739390, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739393, "dur": 49, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739446, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739448, "dur": 48, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739500, "dur": 2, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739504, "dur": 46, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739554, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739557, "dur": 58, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739623, "dur": 3, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739628, "dur": 73, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739709, "dur": 3, "ph": "X", "name": "ProcessMessages 703", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739715, "dur": 53, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739773, "dur": 2, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739777, "dur": 63, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739843, "dur": 2, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739848, "dur": 47, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739900, "dur": 2, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739903, "dur": 47, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739954, "dur": 2, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003739958, "dur": 61, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740027, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740031, "dur": 85, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740123, "dur": 3, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740129, "dur": 76, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740211, "dur": 2, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740215, "dur": 68, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740288, "dur": 2, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740293, "dur": 62, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740359, "dur": 2, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740363, "dur": 54, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740422, "dur": 2, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740426, "dur": 55, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740485, "dur": 2, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740490, "dur": 46, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740541, "dur": 2, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740545, "dur": 50, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740599, "dur": 2, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740603, "dur": 54, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740662, "dur": 2, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740666, "dur": 49, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740719, "dur": 2, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740722, "dur": 44, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740770, "dur": 2, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740773, "dur": 45, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740822, "dur": 2, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740826, "dur": 44, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740873, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740877, "dur": 43, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740924, "dur": 2, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740927, "dur": 42, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740973, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003740975, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003741026, "dur": 2, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003741029, "dur": 47, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003741080, "dur": 2, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003741083, "dur": 49, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003741136, "dur": 2, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003741139, "dur": 65, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003741209, "dur": 2, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003741212, "dur": 79, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003741297, "dur": 3, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003741303, "dur": 60, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003741368, "dur": 2, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003741372, "dur": 304, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003741684, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003741688, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003741749, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003741752, "dur": 51, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003741808, "dur": 2, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003741811, "dur": 51, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003741866, "dur": 2, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003741869, "dur": 44, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003741918, "dur": 2, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003741923, "dur": 51, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003741979, "dur": 2, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003741983, "dur": 44, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742030, "dur": 2, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742033, "dur": 48, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742085, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742088, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742144, "dur": 2, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742149, "dur": 47, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742201, "dur": 2, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742204, "dur": 46, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742254, "dur": 2, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742257, "dur": 46, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742307, "dur": 2, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742310, "dur": 42, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742356, "dur": 2, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742359, "dur": 46, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742410, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742413, "dur": 44, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742461, "dur": 1, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742464, "dur": 47, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742515, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742518, "dur": 38, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742560, "dur": 2, "ph": "X", "name": "ProcessMessages 49", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742563, "dur": 55, "ph": "X", "name": "ReadAsync 49", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742622, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742625, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742683, "dur": 2, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742686, "dur": 44, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742734, "dur": 2, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742737, "dur": 60, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742802, "dur": 2, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742805, "dur": 45, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742854, "dur": 2, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742857, "dur": 48, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742909, "dur": 1, "ph": "X", "name": "ProcessMessages 235", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742912, "dur": 39, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742955, "dur": 1, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003742958, "dur": 44, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743006, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743009, "dur": 47, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743060, "dur": 2, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743063, "dur": 46, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743115, "dur": 2, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743119, "dur": 44, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743167, "dur": 2, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743170, "dur": 46, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743222, "dur": 2, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743226, "dur": 44, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743274, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743277, "dur": 39, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743320, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743323, "dur": 44, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743372, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743374, "dur": 69, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743447, "dur": 2, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743451, "dur": 45, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743500, "dur": 2, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743504, "dur": 44, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743552, "dur": 2, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743555, "dur": 59, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743617, "dur": 5, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743624, "dur": 60, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743687, "dur": 2, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743691, "dur": 44, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743739, "dur": 2, "ph": "X", "name": "ProcessMessages 81", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743742, "dur": 50, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743796, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743799, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743853, "dur": 2, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743856, "dur": 51, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743912, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743915, "dur": 52, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743971, "dur": 2, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003743974, "dur": 44, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744021, "dur": 2, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744025, "dur": 47, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744075, "dur": 2, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744078, "dur": 43, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744125, "dur": 2, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744128, "dur": 38, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744170, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744173, "dur": 46, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744225, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744227, "dur": 47, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744279, "dur": 2, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744282, "dur": 45, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744331, "dur": 2, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744334, "dur": 47, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744385, "dur": 2, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744388, "dur": 49, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744441, "dur": 2, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744444, "dur": 50, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744498, "dur": 2, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744501, "dur": 39, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744544, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744547, "dur": 48, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744601, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744604, "dur": 52, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744661, "dur": 2, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744666, "dur": 75, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744745, "dur": 2, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744748, "dur": 50, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744801, "dur": 2, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744806, "dur": 60, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744870, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744873, "dur": 50, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744927, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744930, "dur": 50, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744984, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003744987, "dur": 50, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745042, "dur": 2, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745045, "dur": 55, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745106, "dur": 2, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745109, "dur": 64, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745178, "dur": 2, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745181, "dur": 51, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745236, "dur": 2, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745240, "dur": 42, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745285, "dur": 1, "ph": "X", "name": "ProcessMessages 163", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745288, "dur": 44, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745337, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745339, "dur": 46, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745389, "dur": 2, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745392, "dur": 52, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745448, "dur": 2, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745451, "dur": 45, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745500, "dur": 2, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745504, "dur": 46, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745554, "dur": 2, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745557, "dur": 45, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745606, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745610, "dur": 43, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745657, "dur": 2, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745660, "dur": 44, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745708, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745710, "dur": 45, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745758, "dur": 2, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745762, "dur": 46, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745812, "dur": 2, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745815, "dur": 47, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745866, "dur": 2, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745869, "dur": 47, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745920, "dur": 2, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745923, "dur": 49, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745976, "dur": 2, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003745979, "dur": 40, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746023, "dur": 2, "ph": "X", "name": "ProcessMessages 81", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746026, "dur": 44, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746074, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746078, "dur": 53, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746135, "dur": 2, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746138, "dur": 46, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746188, "dur": 2, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746192, "dur": 47, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746242, "dur": 2, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746245, "dur": 45, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746294, "dur": 2, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746297, "dur": 39, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746340, "dur": 2, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746343, "dur": 45, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746392, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746395, "dur": 46, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746445, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746448, "dur": 48, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746500, "dur": 2, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746503, "dur": 45, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746552, "dur": 2, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746556, "dur": 46, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746606, "dur": 2, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746609, "dur": 51, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746664, "dur": 2, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746667, "dur": 46, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746720, "dur": 2, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746723, "dur": 42, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746769, "dur": 2, "ph": "X", "name": "ProcessMessages 211", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746772, "dur": 46, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746822, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746825, "dur": 47, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746876, "dur": 2, "ph": "X", "name": "ProcessMessages 522", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746879, "dur": 69, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746953, "dur": 2, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003746957, "dur": 46, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747007, "dur": 2, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747010, "dur": 51, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747067, "dur": 2, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747071, "dur": 46, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747121, "dur": 2, "ph": "X", "name": "ProcessMessages 191", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747126, "dur": 44, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747173, "dur": 2, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747177, "dur": 62, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747243, "dur": 4, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747249, "dur": 51, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747305, "dur": 2, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747308, "dur": 45, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747357, "dur": 1, "ph": "X", "name": "ProcessMessages 267", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747359, "dur": 44, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747408, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747411, "dur": 43, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747458, "dur": 2, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747461, "dur": 41, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747506, "dur": 1, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747509, "dur": 36, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747549, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747551, "dur": 46, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747601, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747604, "dur": 44, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747652, "dur": 2, "ph": "X", "name": "ProcessMessages 231", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747655, "dur": 45, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747704, "dur": 1, "ph": "X", "name": "ProcessMessages 235", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747706, "dur": 47, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747757, "dur": 2, "ph": "X", "name": "ProcessMessages 233", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747760, "dur": 49, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747814, "dur": 1, "ph": "X", "name": "ProcessMessages 229", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747817, "dur": 43, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747864, "dur": 2, "ph": "X", "name": "ProcessMessages 231", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747867, "dur": 40, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747912, "dur": 1, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747914, "dur": 43, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747961, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003747964, "dur": 48, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748016, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748020, "dur": 47, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748071, "dur": 1, "ph": "X", "name": "ProcessMessages 207", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748074, "dur": 77, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748155, "dur": 2, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748158, "dur": 44, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748206, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748209, "dur": 47, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748259, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748262, "dur": 43, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748309, "dur": 2, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748312, "dur": 43, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748359, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748362, "dur": 42, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748407, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748411, "dur": 48, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748462, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748465, "dur": 45, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748514, "dur": 4, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748520, "dur": 44, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748567, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748570, "dur": 42, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748616, "dur": 2, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748620, "dur": 117, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748741, "dur": 3, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748746, "dur": 46, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748796, "dur": 2, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748799, "dur": 44, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748847, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748850, "dur": 44, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748899, "dur": 2, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748902, "dur": 53, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748959, "dur": 2, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003748962, "dur": 44, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749009, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749015, "dur": 59, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749078, "dur": 2, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749082, "dur": 52, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749138, "dur": 2, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749141, "dur": 50, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749196, "dur": 2, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749199, "dur": 51, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749254, "dur": 2, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749257, "dur": 45, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749306, "dur": 2, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749310, "dur": 56, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749370, "dur": 2, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749374, "dur": 54, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749432, "dur": 2, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749436, "dur": 50, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749492, "dur": 2, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749495, "dur": 37, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749536, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749539, "dur": 46, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749589, "dur": 1, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749591, "dur": 43, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749638, "dur": 1, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749641, "dur": 49, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749693, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749696, "dur": 43, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749743, "dur": 2, "ph": "X", "name": "ProcessMessages 210", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749747, "dur": 53, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749803, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749806, "dur": 43, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749853, "dur": 2, "ph": "X", "name": "ProcessMessages 217", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749856, "dur": 46, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749906, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749909, "dur": 42, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749955, "dur": 2, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003749958, "dur": 47, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750012, "dur": 1, "ph": "X", "name": "ProcessMessages 115", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750016, "dur": 56, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750076, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750079, "dur": 55, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750139, "dur": 2, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750143, "dur": 49, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750196, "dur": 2, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750199, "dur": 47, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750249, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750252, "dur": 53, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750309, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750314, "dur": 50, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750367, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750370, "dur": 44, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750419, "dur": 2, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750422, "dur": 54, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750481, "dur": 2, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750484, "dur": 36, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750524, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750527, "dur": 41, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750572, "dur": 1, "ph": "X", "name": "ProcessMessages 114", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750574, "dur": 45, "ph": "X", "name": "ReadAsync 114", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750623, "dur": 2, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750626, "dur": 48, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750678, "dur": 2, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750681, "dur": 43, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750728, "dur": 2, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750731, "dur": 194, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750928, "dur": 2, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003750931, "dur": 94, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751029, "dur": 4, "ph": "X", "name": "ProcessMessages 1541", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751035, "dur": 53, "ph": "X", "name": "ReadAsync 1541", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751093, "dur": 2, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751096, "dur": 47, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751148, "dur": 2, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751151, "dur": 50, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751206, "dur": 2, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751209, "dur": 45, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751261, "dur": 2, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751264, "dur": 45, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751313, "dur": 2, "ph": "X", "name": "ProcessMessages 267", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751316, "dur": 54, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751375, "dur": 2, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751379, "dur": 48, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751431, "dur": 2, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751435, "dur": 82, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751521, "dur": 2, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751525, "dur": 61, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751590, "dur": 2, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751593, "dur": 55, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751652, "dur": 2, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751656, "dur": 53, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751713, "dur": 2, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751716, "dur": 52, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751773, "dur": 2, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751777, "dur": 48, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751829, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751832, "dur": 44, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751881, "dur": 2, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751884, "dur": 47, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751935, "dur": 2, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751938, "dur": 47, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751989, "dur": 2, "ph": "X", "name": "ProcessMessages 273", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003751992, "dur": 40, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003752036, "dur": 1, "ph": "X", "name": "ProcessMessages 98", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003752038, "dur": 48, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003752090, "dur": 2, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003752094, "dur": 49, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003752147, "dur": 2, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003752150, "dur": 41, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003752195, "dur": 4, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003752200, "dur": 49, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003752253, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003752256, "dur": 45, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003752306, "dur": 2, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003752312, "dur": 55, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003752372, "dur": 2, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003752376, "dur": 42, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003752422, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003752424, "dur": 51, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003752479, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003752484, "dur": 591, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003753081, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003753088, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003753152, "dur": 640, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003753798, "dur": 111, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003753914, "dur": 15, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003753932, "dur": 60, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754000, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754007, "dur": 52, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754066, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754073, "dur": 59, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754137, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754143, "dur": 106, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754255, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754259, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754329, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754335, "dur": 54, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754395, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754401, "dur": 63, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754470, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754476, "dur": 54, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754536, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754542, "dur": 64, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754613, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754620, "dur": 62, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754688, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754694, "dur": 62, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754761, "dur": 3, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754768, "dur": 57, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754831, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754836, "dur": 66, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754909, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754917, "dur": 62, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754985, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003754991, "dur": 66, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003755062, "dur": 2, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003755066, "dur": 50, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003755124, "dur": 5, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003755132, "dur": 72, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003755210, "dur": 4, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003755217, "dur": 66, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003755289, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003755295, "dur": 66, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003755367, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003755373, "dur": 65, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003755445, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003755451, "dur": 64, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003755521, "dur": 3, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003755527, "dur": 54, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003755585, "dur": 3, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003755590, "dur": 77, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003755672, "dur": 4, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003755679, "dur": 67, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003755754, "dur": 4, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003755761, "dur": 68, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003755838, "dur": 4, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003755847, "dur": 67, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003755922, "dur": 4, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003755932, "dur": 52, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003755992, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003755998, "dur": 52, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003756056, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003756062, "dur": 58, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003756126, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003756132, "dur": 67, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003756210, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003756217, "dur": 69, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003756294, "dur": 4, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003756302, "dur": 69, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003756376, "dur": 4, "ph": "X", "name": "ProcessMessages 113", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003756415, "dur": 50, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003756470, "dur": 400, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003756876, "dur": 55, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003756935, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003756940, "dur": 8748, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003765695, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003765699, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003765753, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003765756, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003765804, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003765808, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003765865, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003765870, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003765937, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003765943, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003765992, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003765995, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003766038, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003766041, "dur": 179, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003766225, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003766230, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003766271, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003766274, "dur": 1068, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003767349, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003767354, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003767420, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003767425, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003767477, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003767480, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003767524, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003767528, "dur": 167, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003767698, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003767701, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003767743, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003767748, "dur": 629, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003768383, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003768390, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003768446, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003768451, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003768509, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003768513, "dur": 58, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003768579, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003768584, "dur": 403, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003768992, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003768995, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003769055, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003769061, "dur": 50, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003769116, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003769120, "dur": 101, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003769225, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003769228, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003769280, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003769283, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003769331, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003769334, "dur": 461, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003769800, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003769803, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003769866, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003769871, "dur": 292, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003770168, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003770171, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003770225, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003770231, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003770303, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003770307, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003770366, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003770371, "dur": 51, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003770426, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003770432, "dur": 58, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003770497, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003770502, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003770558, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003770562, "dur": 178, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003770745, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003770748, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003770809, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003770813, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003770880, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003770884, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003770948, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003770952, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771037, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771043, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771105, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771111, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771168, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771171, "dur": 52, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771231, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771236, "dur": 49, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771289, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771292, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771338, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771343, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771402, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771406, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771461, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771463, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771510, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771513, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771562, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771566, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771620, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771625, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771682, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771686, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771735, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771738, "dur": 109, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771852, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771854, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771922, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771926, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771979, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003771984, "dur": 324, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003772314, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003772319, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003772375, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003772380, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003772442, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003772445, "dur": 44, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003772493, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003772497, "dur": 146, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003772647, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003772650, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003772689, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003772692, "dur": 274, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003772972, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003772982, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003773047, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003773055, "dur": 64, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003773123, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003773127, "dur": 42, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003773173, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003773176, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003773223, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003773225, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003773268, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003773270, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003773317, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003773321, "dur": 778, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003774105, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003774109, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003774166, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003774170, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003774223, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003774227, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003774272, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003774275, "dur": 383, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003774666, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003774669, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003774734, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003774738, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003774784, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003774786, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003774827, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003774831, "dur": 248, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003775084, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003775087, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003775128, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003775132, "dur": 272, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003775408, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003775411, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003775455, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003775458, "dur": 1240, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003776707, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003776713, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003776782, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003776786, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003776835, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003776838, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003776881, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003776885, "dur": 803, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003777693, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003777696, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003777761, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003777764, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003777809, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003777812, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003777854, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003777858, "dur": 203, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003778064, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003778067, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003778106, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003778109, "dur": 674, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003778788, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003778791, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003778848, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003778852, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003778901, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003778904, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003778944, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003778947, "dur": 170, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003779120, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003779123, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003779175, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003779179, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003779232, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003779234, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003779278, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003779283, "dur": 219, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003779510, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003779513, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003779557, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003779561, "dur": 658, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003780226, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003780229, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003780291, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003780295, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003780345, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003780348, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003780402, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003780406, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003780459, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003780462, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003780504, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003780508, "dur": 201, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003780714, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003780719, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003780768, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003780771, "dur": 576, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003781352, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003781355, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003781413, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003781417, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003781465, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003781468, "dur": 49, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003781521, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003781524, "dur": 188, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003781717, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003781719, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003781764, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003781767, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003781813, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003781816, "dur": 1517, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003783340, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003783344, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003783392, "dur": 988, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353003784384, "dur": 255651, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004040045, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004040051, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004040144, "dur": 2224, "ph": "X", "name": "ProcessMessages 1224", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004042372, "dur": 7751, "ph": "X", "name": "ReadAsync 1224", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004050127, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004050130, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004050178, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004050182, "dur": 1908, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004052098, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004052102, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004052162, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004052167, "dur": 190, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004052364, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004052368, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004052432, "dur": 22, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004052457, "dur": 108772, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004161239, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004161244, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004161310, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004161315, "dur": 711, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004162032, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004162034, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004162090, "dur": 23, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004162115, "dur": 137262, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004299388, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004299394, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004299461, "dur": 29, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004299492, "dur": 9186, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004308689, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004308693, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004308748, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004308754, "dur": 667, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004309426, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004309429, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004309491, "dur": 28, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004309521, "dur": 179969, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004489500, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004489506, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004489562, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004489567, "dur": 618, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004490190, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004490193, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004490250, "dur": 35, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004490288, "dur": 1295, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004491588, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004491591, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004491640, "dur": 554, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751353004492198, "dur": 19024, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 41616, "tid": 1682, "ts": 1751353004537762, "dur": 3130, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 41616, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 41616, "tid": 8589934592, "ts": 1751353003688589, "dur": 158505, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 41616, "tid": 8589934592, "ts": 1751353003847099, "dur": 8, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 41616, "tid": 8589934592, "ts": 1751353003847108, "dur": 2672, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 41616, "tid": 1682, "ts": 1751353004540895, "dur": 10, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 41616, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 41616, "tid": 4294967296, "ts": 1751353003650454, "dur": 862923, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 41616, "tid": 4294967296, "ts": 1751353003658531, "dur": 14871, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 41616, "tid": 4294967296, "ts": 1751353004513745, "dur": 8783, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 41616, "tid": 4294967296, "ts": 1751353004518593, "dur": 242, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 41616, "tid": 4294967296, "ts": 1751353004522752, "dur": 30, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 41616, "tid": 1682, "ts": 1751353004540907, "dur": 13, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751353003716744, "dur": 50, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751353003716848, "dur": 2905, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751353003719769, "dur": 884, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751353003720743, "dur": 102, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751353003720845, "dur": 1435, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751353003724661, "dur": 3163, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751353003731035, "dur": 4978, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751353003736060, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751353003736183, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751353003736318, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751353003736549, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751353003737267, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_A558B9E65539D7D0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751353003737333, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751353003737482, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751353003737617, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751353003737785, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751353003737980, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751353003738205, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751353003738707, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751353003738930, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751353003739020, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751353003739472, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751353003739531, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751353003739593, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751353003739744, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751353003739934, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751353003740105, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751353003740181, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751353003740279, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751353003740391, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751353003740522, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751353003740642, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751353003740946, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751353003741110, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751353003741175, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751353003741715, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751353003742057, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751353003742596, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751353003743338, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751353003743586, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751353003743776, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751353003743880, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751353003744182, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751353003744574, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751353003744633, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751353003744902, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751353003745065, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751353003746046, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751353003747147, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751353003747205, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751353003747318, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751353003747375, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751353003747520, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751353003747935, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751353003748080, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751353003748380, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751353003748656, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751353003748720, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751353003748872, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751353003749168, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751353003749329, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751353003749612, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751353003749962, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751353003750105, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751353003750273, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751353003750893, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751353003751433, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751353003722336, "dur": 30035, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751353003752387, "dur": 737853, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751353004490241, "dur": 569, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751353004490810, "dur": 234, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751353004491044, "dur": 83, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751353004491407, "dur": 99, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751353004491526, "dur": 9580, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751353003721830, "dur": 30570, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003752420, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_7A1FD51B51344812.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751353003752907, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003753024, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_7A1FD51B51344812.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751353003753079, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_47A61C91F1D08784.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751353003753312, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003753377, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_47A61C91F1D08784.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751353003753502, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_99CD71E244B915CF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751353003753606, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003753728, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_95C124FA2E4BBBB0.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751353003753832, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003753977, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_B563F2CB5E9D696E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751353003754230, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003754565, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003754672, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_A3850CF6800E4193.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751353003754729, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003754816, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751353003754936, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003755017, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751353003755278, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003755397, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003755519, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751353003755693, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003755777, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751353003755974, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003756045, "dur": 118, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751353003756166, "dur": 1128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003757295, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003758037, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003758754, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003760044, "dur": 1033, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\InstancePropertyAccessor.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751353003759399, "dur": 1679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003761078, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003761750, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003762447, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003763150, "dur": 1374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003764524, "dur": 1186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003765710, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003765893, "dur": 1473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003767367, "dur": 1005, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003768372, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003768513, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751353003768910, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003768999, "dur": 1065, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751353003770066, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003770239, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003770337, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003770398, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003770766, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003770878, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003771106, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003771395, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003771581, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003771849, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003771968, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003772302, "dur": 696, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003772999, "dur": 1114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003774113, "dur": 553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003774666, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003774783, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751353003774958, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003775021, "dur": 1520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751353003776542, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003776704, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003776779, "dur": 921, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003777701, "dur": 1073, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003778824, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003779167, "dur": 1086, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003780254, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003780375, "dur": 1021, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353003781396, "dur": 268361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751353004049803, "dur": 111308, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751353004049761, "dur": 111352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751353004161142, "dur": 838, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751353004161988, "dur": 328277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003721981, "dur": 30476, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003752462, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_80B8935D38233098.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751353003753028, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003753173, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_74513773BD43745E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751353003753288, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003753384, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1830F8D8229EF0EB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751353003753508, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003753617, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D22D11490AEAA9B0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751353003753817, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003754033, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A99F700E950BD818.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751353003754304, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003754530, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003754684, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003754780, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_696347C4732CD601.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751353003754873, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003754979, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003755075, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751353003755133, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003755237, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003755333, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003755409, "dur": 377, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751353003755788, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751353003755927, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003755992, "dur": 252, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751353003757117, "dur": 945, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\treeview\\Control.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751353003756246, "dur": 1887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003758134, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003758890, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003759632, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003760430, "dur": 621, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.settings-manager\\Editor\\UserSettings.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751353003760354, "dur": 1289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003761837, "dur": 1319, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\WebApi\\CurrentUserAdminCheckResponse.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751353003761644, "dur": 1900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003763545, "dur": 173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003763718, "dur": 1952, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003765670, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003765858, "dur": 1486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003767344, "dur": 1036, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003768380, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003768568, "dur": 1665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003770233, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003770298, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003770386, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003770715, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003770889, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003771026, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003771110, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003771402, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003771578, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003771847, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003771973, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751353003772291, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003772382, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751353003773070, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003773214, "dur": 869, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003774120, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003774678, "dur": 2008, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003776687, "dur": 997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003777731, "dur": 1088, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003778819, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003779161, "dur": 1089, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003780250, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003780404, "dur": 957, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353003781362, "dur": 268454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751353004049817, "dur": 440450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003721904, "dur": 30522, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003752432, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_00CD99E311DC019E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751353003752837, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003753012, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_00CD99E311DC019E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751353003753117, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751353003753305, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003753447, "dur": 211, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751353003753661, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6678507EC9682BC1.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751353003753770, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003753996, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C5419324AD6BC088.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751353003754217, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003754451, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B014F0E06EB9C8B3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751353003754581, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003754696, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B014F0E06EB9C8B3.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751353003754837, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003754939, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751353003755206, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003755363, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751353003755509, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003755635, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003755724, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003755795, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751353003755989, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003756096, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003756726, "dur": 1325, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\SettingsProvider\\ProjectSettings\\CustomPropertyProviderSettings.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751353003756183, "dur": 2007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003758191, "dur": 795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003759021, "dur": 2066, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Inspection\\Special\\UnknownInspector.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751353003758986, "dur": 2799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003761786, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003763094, "dur": 653, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\UGUI\\EventSystem\\EventTriggerType.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751353003762463, "dur": 1285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003763748, "dur": 1931, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003765679, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003765913, "dur": 1465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003767379, "dur": 1040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003768419, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003768555, "dur": 1689, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003770250, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003770479, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003770726, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003770916, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003771039, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003771096, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003771353, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003771575, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003771840, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003771982, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003772291, "dur": 671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003772963, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003773137, "dur": 971, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003774108, "dur": 604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003774713, "dur": 2018, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003776731, "dur": 971, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003777703, "dur": 1084, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003778787, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003779159, "dur": 1115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003780274, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003780364, "dur": 988, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353003781352, "dur": 268435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751353004049790, "dur": 440495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003721964, "dur": 30474, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003752444, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_BF1A247579C7383B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751353003752988, "dur": 479, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003753467, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_BF1A247579C7383B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751353003753676, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_783D7153E7AEE314.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751353003753955, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003754193, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_67F30B299A5D5154.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751353003754379, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003754523, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751353003754622, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003754691, "dur": 475, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751353003755170, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003755287, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003755407, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003755494, "dur": 465, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751353003755961, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751353003756099, "dur": 357, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751353003756457, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003757507, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003758226, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003758903, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003759616, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003760436, "dur": 616, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_TextInfo.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751353003761053, "dur": 886, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_TextElement_Legacy.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751353003760261, "dur": 2085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003762347, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003763008, "dur": 752, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751353003763008, "dur": 2166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003765174, "dur": 513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003765688, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003765873, "dur": 1492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003767366, "dur": 1038, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003768404, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003768580, "dur": 1647, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003770287, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003770384, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003770724, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003770873, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003771040, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003771132, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003771362, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003771565, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003771838, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003771971, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003772333, "dur": 667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003773001, "dur": 1078, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003774142, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003774696, "dur": 2015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003776712, "dur": 1016, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003777728, "dur": 1060, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003778789, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003779121, "dur": 1081, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003780252, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003780380, "dur": 995, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353003781375, "dur": 268398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751353004049773, "dur": 440462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003722029, "dur": 30445, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003752481, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_16A54265357DDF1F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751353003753057, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003753289, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_051DE14E9379244A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751353003753412, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003753564, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_5AC43434136967AD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751353003753702, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003754040, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_54605A4C765A4A77.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751353003754297, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003754390, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_09E134ACBD96AA82.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751353003754566, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003754683, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_3AA3364A40DB9F8B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751353003754736, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003754824, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_3AA3364A40DB9F8B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751353003755044, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003755120, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003755206, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003755311, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751353003755380, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003755494, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003755582, "dur": 249, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751353003755833, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751353003755979, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003756054, "dur": 463, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751353003756518, "dur": 1010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003757529, "dur": 768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003758298, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003758977, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003759653, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003760453, "dur": 627, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage\\Editor\\CoverageFormats\\OpenCover\\Mono.Reflection\\ByteBuffer.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751353003760344, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003761637, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003762287, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003762937, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003763539, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003764372, "dur": 1304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003765677, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003765866, "dur": 1475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003767342, "dur": 1028, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003768370, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003768521, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751353003768739, "dur": 1355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003770104, "dur": 1314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751353003771419, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003771571, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003771637, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751353003771815, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003771890, "dur": 2051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751353003773942, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003774172, "dur": 536, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003774708, "dur": 1998, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003776706, "dur": 983, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003777689, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003777779, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751353003777964, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003778030, "dur": 599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751353003778630, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003778780, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003778859, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003779130, "dur": 1080, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003780211, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003780362, "dur": 978, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353003781341, "dur": 268424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353004049767, "dur": 258662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751353004308471, "dur": 180901, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751353004308431, "dur": 180944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751353004489405, "dur": 739, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751353003722085, "dur": 30403, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003752493, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FCC5A8FB58ECD1D2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751353003753046, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003753349, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_5E52B5CE937E0ED1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751353003753463, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003753546, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CB3FD07F3B098D2B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751353003753689, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003753844, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CB3FD07F3B098D2B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751353003753926, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_31922632D84128F5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751353003754247, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003754330, "dur": 331, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_31922632D84128F5.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751353003754670, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751353003754724, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003754799, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751353003754921, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003755016, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751353003755256, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003755365, "dur": 444, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751353003755810, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751353003755975, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003756046, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003756495, "dur": 542, "ph": "X", "name": "File", "args": {"detail": "Assets\\Invector-3rdPersonController_LITE\\Scripts\\Camera\\vThirdPersonCamera.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751353003756154, "dur": 1137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003757292, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003758051, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003758781, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003759624, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003760444, "dur": 629, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_ResourcesManager.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751353003760299, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003761597, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003762295, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003762990, "dur": 2209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003765200, "dur": 483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003765684, "dur": 186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003765870, "dur": 1524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003767395, "dur": 996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003768391, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003768564, "dur": 1665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003770235, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003770309, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003770437, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003770744, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003770887, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003771026, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003771165, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003771350, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003771573, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003771842, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003771966, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003772298, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003772377, "dur": 635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003773013, "dur": 1114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003774128, "dur": 586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003774714, "dur": 2009, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003776724, "dur": 971, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003777696, "dur": 1086, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003778782, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003779129, "dur": 1129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003780259, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003780383, "dur": 1010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353003781393, "dur": 268378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751353004049772, "dur": 440453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003722137, "dur": 30363, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003752504, "dur": 672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_493844D967613FA0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751353003753177, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003753463, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_493844D967613FA0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751353003753630, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8B525A02B9DF9C7E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751353003754215, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003754300, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9F68AD12AC89760A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751353003754477, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003754706, "dur": 297, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9F68AD12AC89760A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751353003755054, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003755130, "dur": 273, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751353003755406, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003755515, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003755624, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003755756, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003755869, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751353003756040, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003756125, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751353003756429, "dur": 588, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\DirectorStyles.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751353003756332, "dur": 1345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003757678, "dur": 794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003758473, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003759106, "dur": 694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003760423, "dur": 621, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\EditorBinding\\WarnBeforeRemovingAttribute.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751353003759801, "dur": 1243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003761045, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003761683, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003762432, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003763093, "dur": 1941, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003765035, "dur": 665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003765701, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003765915, "dur": 1460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003767375, "dur": 1002, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003768378, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003768528, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751353003769085, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003769157, "dur": 1139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751353003770298, "dur": 370, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003770721, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003770787, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751353003771189, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003771250, "dur": 783, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751353003772034, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003772280, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003772353, "dur": 634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003772987, "dur": 1135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003774122, "dur": 595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003774717, "dur": 1984, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003776702, "dur": 991, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003777694, "dur": 1085, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003778779, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003779124, "dur": 1088, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003780213, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003780399, "dur": 943, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353003781342, "dur": 268432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751353004049775, "dur": 440456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003722184, "dur": 30336, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003752526, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_989B89A6DC960046.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751353003753140, "dur": 345, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003753486, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_989B89A6DC960046.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751353003753587, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_173731A5F5D7E041.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751353003753687, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003753952, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_FD8C0D7AD7503969.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751353003754263, "dur": 491, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003754754, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_FD8C0D7AD7503969.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751353003754881, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003754984, "dur": 342, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751353003755330, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003755409, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003755514, "dur": 200, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751353003755717, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751353003755943, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003756008, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751353003756093, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003756190, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003757094, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003757798, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003758480, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003759139, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003760407, "dur": 676, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\Unity\\UnityEvent_Converter.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751353003759908, "dur": 1341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003761249, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003761998, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003762651, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003763410, "dur": 1585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003764996, "dur": 702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003765699, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003765903, "dur": 1467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003767371, "dur": 1014, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003768385, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003768553, "dur": 1697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003770250, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003770313, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003770400, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003770728, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003770873, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003771038, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003771134, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003771359, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003771567, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003771845, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003771965, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751353003772271, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003772348, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751353003772946, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003773116, "dur": 976, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003774121, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003774681, "dur": 2007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003776689, "dur": 1044, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003777734, "dur": 1066, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003778801, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003779153, "dur": 1061, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003780227, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003780359, "dur": 969, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003781340, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353003781404, "dur": 268390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751353004049794, "dur": 440443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003722249, "dur": 30285, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003752540, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_B01BEAEDCAC9F962.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751353003753088, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003753448, "dur": 189, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_B01BEAEDCAC9F962.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751353003753640, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751353003753749, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003753884, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FF5FCB0F465F7311.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751353003753999, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003754220, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E0B83A434CA2B946.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751353003754329, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003754384, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E0B83A434CA2B946.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751353003754505, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003754677, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003754814, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003754890, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751353003755109, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003755200, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003755350, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751353003755435, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003755566, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003755707, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003755790, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751353003755932, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003756048, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003756421, "dur": 572, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio\\Editor\\ProjectGeneration\\GUIDProvider.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751353003756170, "dur": 1228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003757399, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003758090, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003758771, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003759511, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003760453, "dur": 626, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\AssetUpgrade\\TrackUpgrade.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751353003760241, "dur": 1282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003761524, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003762271, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003762947, "dur": 1155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003764102, "dur": 1584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003765686, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003765891, "dur": 1496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003767388, "dur": 1022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003768411, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003768581, "dur": 1673, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003770254, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003770318, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003770379, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003770719, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003770877, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003770987, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003771259, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003771367, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003771573, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003771886, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003771980, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003772361, "dur": 615, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003772977, "dur": 1113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003774111, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003774692, "dur": 2022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003776714, "dur": 1018, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003777732, "dur": 1064, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003778796, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003779173, "dur": 1091, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003780264, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003780386, "dur": 961, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353003781347, "dur": 268460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751353004049807, "dur": 440456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003722972, "dur": 29775, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003752755, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_D3BB5BABC9B6A39A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751353003752886, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003753169, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_61E5A1A2114B6332.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751353003753424, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003753489, "dur": 262, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_61E5A1A2114B6332.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751353003753757, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_9B1D775C88DF2080.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751353003754213, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003754354, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E671E5897665917D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751353003754458, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003754568, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003754667, "dur": 292, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751353003754976, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003755065, "dur": 217, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751353003755306, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003755399, "dur": 209, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751353003755612, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003755722, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003755802, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751353003756008, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003756104, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003756194, "dur": 490, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751353003756686, "dur": 972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003757658, "dur": 746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003758405, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003759088, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003759718, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003760456, "dur": 620, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Plugins\\XR\\TrackedPoseDriver.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751353003760412, "dur": 1229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003761642, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003762279, "dur": 647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003762927, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003763756, "dur": 1916, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003765672, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003765904, "dur": 1476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003767382, "dur": 1034, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003768416, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003768562, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751353003769137, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003769231, "dur": 1348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751353003770579, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003770992, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003771119, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003771375, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003771588, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003771857, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003771976, "dur": 336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003772312, "dur": 684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003772997, "dur": 1119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003774117, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003774670, "dur": 2028, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003776699, "dur": 1007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003777706, "dur": 1109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003778815, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003779157, "dur": 1105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003780262, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003780389, "dur": 997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353003781387, "dur": 268381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751353004049769, "dur": 440481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003722379, "dur": 30186, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003752570, "dur": 567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_1C55A2A4A7EC33E2.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751353003753138, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003753226, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_F24F3CD4D4D128AB.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751353003753551, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003753652, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_00433992BAF13A73.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751353003753796, "dur": 419, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003754230, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_BDFB8953D401853F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751353003754328, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003754430, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BED6F64D074B3174.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751353003754530, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003754693, "dur": 551, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BED6F64D074B3174.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751353003755260, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751353003755577, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003755656, "dur": 385, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751353003756044, "dur": 9598, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751353003765644, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003765866, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003765955, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751353003766165, "dur": 1022, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751353003767188, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003767347, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003767426, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751353003767643, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751353003768231, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003768374, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003768508, "dur": 442, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751353003768951, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003769026, "dur": 2601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751353003771628, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003771852, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003771958, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751353003772330, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003772399, "dur": 1996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751353003774397, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003774676, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003774777, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751353003775265, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003775357, "dur": 2165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751353003777523, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003777698, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003777772, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751353003778008, "dur": 952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751353003778961, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003779123, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003779202, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751353003779446, "dur": 711, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751353003780158, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003780358, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003780429, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751353003780656, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751353003781196, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003781336, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003781415, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751353003781657, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751353003782834, "dur": 94, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353003783992, "dur": 256002, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751353004050790, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751353004049769, "dur": 1326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751353004051909, "dur": 106, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751353004052665, "dur": 246649, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751353004308416, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1751353004308397, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1751353004308603, "dur": 778, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 11, "ts": 1751353004309387, "dur": 180857, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003722430, "dur": 30148, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003752583, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5543A9502BBA10BA.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751353003752837, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003753023, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_7F1C5DFD0AD39F77.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751353003753446, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003753532, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_42AEDBADC03A3022.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751353003753647, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003753820, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_42AEDBADC03A3022.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751353003753940, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_20E0F9318BFD3B5B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751353003754205, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003754361, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B47D5ED0AB2E73A1.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751353003754542, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003754783, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B47D5ED0AB2E73A1.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751353003754889, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751353003754945, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003755018, "dur": 370, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751353003755391, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751353003755452, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003755580, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003755681, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003755742, "dur": 287, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751353003756034, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003756113, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751353003756486, "dur": 623, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\inspectors\\IInspectorChangeHandler.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751353003756312, "dur": 1354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003757667, "dur": 966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003759026, "dur": 2052, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Serialization\\MovedFromAttributeExtensions.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751353003758633, "dur": 2677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003761311, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003762438, "dur": 687, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\ParentWindow.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751353003762053, "dur": 1272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003763326, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003763930, "dur": 1737, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003765737, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003765882, "dur": 1514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003767396, "dur": 1002, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003768398, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003768559, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751353003768865, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003768931, "dur": 1439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751353003770372, "dur": 638, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003771041, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003771121, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003771378, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003771570, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003771852, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003771969, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003772298, "dur": 695, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003772994, "dur": 1095, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003774118, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003774735, "dur": 1984, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003776720, "dur": 977, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003777698, "dur": 1096, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003778794, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003779163, "dur": 1073, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003780237, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003780353, "dur": 982, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003781335, "dur": 67934, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003852822, "dur": 422, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 12, "ts": 1751353003849271, "dur": 3978, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353003853250, "dur": 196527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751353004049778, "dur": 440482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003722475, "dur": 30119, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003752600, "dur": 567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_9DC68FB86218AF77.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751353003753169, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003753258, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BA5431EB9EAB1D55.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751353003753474, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003753579, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_5E37763FF8B95A1F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751353003753696, "dur": 520, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003754269, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B56095C3DA730DAC.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751353003754370, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003754468, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_4FB16FC3A796B887.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751353003754642, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003754737, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003754891, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003754996, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003755079, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751353003755205, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003755319, "dur": 161, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751353003755486, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003755570, "dur": 545, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1751353003756117, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751353003756230, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003757305, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003758036, "dur": 1078, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector2\\Vector2Maximum.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751353003757964, "dur": 1679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003759644, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003760445, "dur": 626, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_CharacterInfo.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751353003760319, "dur": 1264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003761584, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003762226, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003762868, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003763465, "dur": 1598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003765064, "dur": 675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003765739, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003765932, "dur": 1453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003767386, "dur": 1003, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003768389, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003768567, "dur": 1667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003770235, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003770288, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003770396, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003770750, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003770880, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003771030, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003771099, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003771364, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003771579, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003771849, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003771974, "dur": 304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003772291, "dur": 672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003772964, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003773232, "dur": 862, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003774110, "dur": 561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003774672, "dur": 2060, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003776733, "dur": 985, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003777719, "dur": 1107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003778827, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003779134, "dur": 1135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003780269, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003780373, "dur": 991, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353003781364, "dur": 268415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751353004049780, "dur": 440469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003722533, "dur": 30076, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003752615, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_52F12B5AEAC5FC8F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751353003752838, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003753089, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_C2E93A20F98E8229.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751353003753223, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003753298, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_0660C742CA2CCB37.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751353003753404, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003753475, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_0660C742CA2CCB37.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751353003753595, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_7D7B641D38DA242D.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751353003753866, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003754012, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0D29C9963CEF79A6.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751353003754214, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003754384, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D7EC0EF22CAF3612.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751353003754505, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003754662, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_2CD7161C662F4679.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751353003754713, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003754871, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003754954, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003755038, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1751353003755254, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003755431, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003755542, "dur": 489, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1751353003756034, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003756146, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751353003756203, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003756275, "dur": 1047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003757323, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003758049, "dur": 1067, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Generic\\GenericSubtract.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751353003758001, "dur": 1718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003759720, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003760452, "dur": 2016, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Utilities\\InternedString.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751353003760404, "dur": 2636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003763040, "dur": 1794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003764834, "dur": 847, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003765682, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003765864, "dur": 1481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003767346, "dur": 1029, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003768376, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003768533, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751353003768869, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003768937, "dur": 1133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751353003770071, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003770252, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003770325, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003770382, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003770453, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003770737, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003770891, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003771031, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003771104, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003771346, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003771570, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003771644, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003771844, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003771959, "dur": 525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751353003772485, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003772594, "dur": 1296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751353003773892, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003774097, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003774168, "dur": 507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003774676, "dur": 2031, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003776707, "dur": 1009, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003777716, "dur": 1075, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003778791, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003779147, "dur": 1092, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003780240, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003780377, "dur": 1004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353003781381, "dur": 268404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751353004049786, "dur": 440489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003722581, "dur": 30044, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003752631, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_F4165E534DD27516.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751353003753151, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003753333, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_C287EB2677BA7F93.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751353003753441, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003753528, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_39C615B7DD834A4A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751353003753651, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003753856, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E522F23A96C2507B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751353003753970, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003754243, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_14A21DFAD1DFC142.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751353003754345, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003754458, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_438C72DDFEF65815.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751353003754597, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003754673, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_438C72DDFEF65815.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751353003754753, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9C39811BC6DD0BF6.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751353003754850, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003754904, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9C39811BC6DD0BF6.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751353003755145, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003755320, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751353003755394, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003755469, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003755589, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003755747, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003755822, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751353003755993, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003756100, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751353003756227, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003757127, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003757783, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003758459, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003759151, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003760410, "dur": 621, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\EditorBinding\\Inspector\\InspectorActionDirectionAttribute.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751353003759826, "dur": 1304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003761130, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003761801, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003762506, "dur": 596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003763103, "dur": 1851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003764954, "dur": 753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003765708, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003765884, "dur": 1472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003767357, "dur": 1010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003768422, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003768571, "dur": 1667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003770329, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003770389, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003770740, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003770890, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003771027, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003771098, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003771361, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003771568, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003771880, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003771984, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003772309, "dur": 696, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003773005, "dur": 1099, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003774104, "dur": 563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003774668, "dur": 2028, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003776697, "dur": 1016, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003777714, "dur": 1058, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003778829, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003779146, "dur": 1095, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003780242, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003780358, "dur": 973, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003781337, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353003781394, "dur": 268398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751353004049793, "dur": 440449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003722630, "dur": 30009, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003752645, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_921345D5C92C6DDE.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751353003752844, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003753040, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_921345D5C92C6DDE.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751353003753102, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46E217D19A6E01BD.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751353003753341, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003753412, "dur": 349, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46E217D19A6E01BD.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751353003753768, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B5DB3F27CCAA63FE.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751353003753952, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003754249, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D7BFC8363EAC4767.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751353003754374, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003754481, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_42EBD154127454A5.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751353003754600, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003754744, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751353003754834, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003754890, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751353003755095, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003755226, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003755340, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751353003755425, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003755528, "dur": 354, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751353003755884, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751353003756036, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003756165, "dur": 211, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751353003756377, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003757218, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003757894, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003758610, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003759286, "dur": 660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003760432, "dur": 621, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Cloning\\Cloning.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751353003759946, "dur": 1272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003761218, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003761907, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003762577, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003763293, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003763881, "dur": 1793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003765674, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003765859, "dur": 1478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003767346, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003767415, "dur": 972, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003768388, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003768523, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751353003768806, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003768923, "dur": 1240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751353003770164, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003770387, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003770466, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751353003770740, "dur": 1302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751353003772043, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003772293, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003772391, "dur": 583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003772975, "dur": 1126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003774154, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003774694, "dur": 1998, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003776693, "dur": 993, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003777740, "dur": 1065, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003778805, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003779184, "dur": 1063, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003780247, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003780354, "dur": 982, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003781337, "dur": 71919, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353003853257, "dur": 196526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751353004049784, "dur": 440438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003722708, "dur": 29943, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003752656, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_030C0AE8F7F2C9CD.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751353003752955, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003753238, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_6C2CA7D176733A22.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751353003753378, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003753455, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_6C2CA7D176733A22.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751353003753606, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_ED94C9E717A89F9F.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751353003753722, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003753827, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_ED94C9E717A89F9F.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751353003753962, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_DC79D8DB49767D4F.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751353003754228, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003754565, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9428A5C8BBBE8A51.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751353003754697, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003754825, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003754970, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003755062, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003755157, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003755260, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003755421, "dur": 503, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751353003755975, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003756103, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751353003756167, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003756236, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003757241, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003757984, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003758684, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003759340, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003760433, "dur": 629, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Evaluation\\RuntimeClip.cs"}}, {"pid": 12345, "tid": 17, "ts": 1751353003760017, "dur": 1236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003761254, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003761977, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003762616, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003763291, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003763858, "dur": 1827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003765685, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003765868, "dur": 1515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003767384, "dur": 1018, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003768403, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003768582, "dur": 1666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003770301, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003770383, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003770722, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003770871, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003770987, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003771040, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003771102, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003771354, "dur": 222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003771577, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003771856, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003771983, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003772317, "dur": 674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003772991, "dur": 1090, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003774124, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003774687, "dur": 1997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003776685, "dur": 1006, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003777692, "dur": 1089, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003778781, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003779119, "dur": 1137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003780256, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003780385, "dur": 965, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353003781351, "dur": 268410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751353004049801, "dur": 228, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1751353004049762, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1751353004050094, "dur": 2229, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1751353004052328, "dur": 437927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003722750, "dur": 29912, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003752667, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_A8A2BEB223D0393B.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751353003752882, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003753022, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_129B2BEEEC662028.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751353003753208, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003753377, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_7BFAB029D4D7DE5C.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751353003753511, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003753611, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_6E64691D4ACC974A.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751353003753727, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003753867, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_805FFE03DDD45823.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751353003754230, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003754368, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_800370C755C131C9.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751353003754745, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003754826, "dur": 359, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_800370C755C131C9.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751353003755219, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003755300, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751353003755386, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003755545, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003755729, "dur": 254, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751353003755986, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003756160, "dur": 200, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1751353003756362, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003757217, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003757914, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003758614, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003759287, "dur": 668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003760434, "dur": 633, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Utilities\\HashUtility.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751353003759956, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003761234, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003761970, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003762626, "dur": 654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003763280, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003763825, "dur": 1840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003765714, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003765942, "dur": 1405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003767347, "dur": 1035, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003768382, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003768510, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751353003768895, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003768975, "dur": 1083, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751353003770060, "dur": 1235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003771378, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003771454, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751353003771728, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003771789, "dur": 996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751353003772786, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003772968, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003773073, "dur": 1026, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003774150, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003774707, "dur": 1993, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003776701, "dur": 1025, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003777726, "dur": 1104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003778831, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003779128, "dur": 1077, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003780206, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003780366, "dur": 992, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353003781359, "dur": 268444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751353004049804, "dur": 440442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003722792, "dur": 29901, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003752698, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_592CC89A6471077C.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751353003752869, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003753044, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_2BCC56A3C6071870.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751353003753166, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003753271, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_DCB00145011AACBC.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751353003753397, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003753525, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B1475218F12198D8.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751353003753641, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003753741, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B8F1FBD1DD727BB2.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751353003753871, "dur": 396, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003754283, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_A7B3B5FA99331E00.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751353003754401, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003754509, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003754772, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_39F0A7015032F01B.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751353003754885, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003754986, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_39F0A7015032F01B.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751353003755126, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003755190, "dur": 440, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1751353003755634, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003755746, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003755818, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751353003756052, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003756194, "dur": 1458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003757653, "dur": 902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003758556, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003759290, "dur": 632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003760427, "dur": 615, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsGuidConverter.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751353003759922, "dur": 1327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003761250, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003761991, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003762631, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003763356, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003763911, "dur": 1767, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003765678, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003765861, "dur": 1471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003767429, "dur": 954, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003768384, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003768556, "dur": 1680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003770278, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003770390, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003770720, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003770883, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003771037, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003771146, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003771367, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003771572, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003771837, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003771972, "dur": 304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003772341, "dur": 663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003773004, "dur": 1106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003774110, "dur": 588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003774698, "dur": 1996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003776695, "dur": 1027, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003777723, "dur": 1069, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003778793, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003779143, "dur": 1132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003780275, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003780371, "dur": 1000, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353003781372, "dur": 268442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751353004049815, "dur": 440414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003722833, "dur": 29879, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003752719, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B52CD1DFCA664AB3.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751353003752928, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003753276, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F8AA39E8D5CE00CF.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751353003753381, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003753458, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F8AA39E8D5CE00CF.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751353003753543, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_921E3B2D436CDAE7.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751353003753662, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003753735, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3C810AF224C3A909.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751353003753834, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003753983, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_963BCDCD478EB63D.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751353003754315, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003754424, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E8D1A39870CABAC1.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751353003754592, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003754730, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003754827, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003754918, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003754988, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1751353003755151, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003755262, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751353003755545, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003755682, "dur": 216, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751353003755901, "dur": 9609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751353003765512, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003765673, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003765750, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003765863, "dur": 1505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003767368, "dur": 1038, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003768407, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003768570, "dur": 1670, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003770341, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003770395, "dur": 335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003770731, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003770868, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003770989, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003771048, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003771115, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003771348, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003771563, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003771848, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003771978, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003772301, "dur": 657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003773009, "dur": 1068, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003774136, "dur": 567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003774704, "dur": 2034, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003776739, "dur": 960, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003777700, "dur": 1086, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003778786, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003779138, "dur": 1062, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003780266, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003780369, "dur": 1008, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353003781378, "dur": 268403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751353004049782, "dur": 440445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003722873, "dur": 29854, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003752733, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D447C5E6C0383BF9.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751353003752945, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003753035, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_711E053178EA7BF1.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751353003753161, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003753245, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_4E756196A062909D.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751353003753504, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003753575, "dur": 143, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_4E756196A062909D.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751353003753723, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_97D91564DB667E63.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751353003753835, "dur": 480, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003754349, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_27EF5DC3D06E8F78.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751353003754459, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003754585, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_69199FEE963F7F27.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751353003754714, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003754802, "dur": 284, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_69199FEE963F7F27.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751353003755102, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751353003755269, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003755435, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003755543, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003755763, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003755826, "dur": 436, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751353003756264, "dur": 980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003757245, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003757942, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003758650, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003759302, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003760455, "dur": 640, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Collections\\NoAllocEnumerator.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751353003759932, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003761204, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003761922, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003762822, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003763461, "dur": 1688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003765149, "dur": 541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003765692, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003765877, "dur": 1476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003767354, "dur": 1039, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003768394, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003768550, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751353003768899, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003769034, "dur": 1793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1751353003770828, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003771110, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003771178, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751353003771523, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003771590, "dur": 1136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1751353003772727, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003773003, "dur": 1099, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003774109, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003774725, "dur": 1991, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003776717, "dur": 1000, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003777718, "dur": 1092, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003778810, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003779178, "dur": 1053, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003780232, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003780356, "dur": 982, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353003781339, "dur": 268461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751353004049800, "dur": 440447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003722916, "dur": 29821, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003752742, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_970778ACBFA20360.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751353003752920, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003753107, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8ECA4E4329F1BADB.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751353003753283, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003753355, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_6C4C0BE13C75841D.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751353003753466, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003753551, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_976F9C96B5CDAC08.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751353003753709, "dur": 522, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003754232, "dur": 202, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_976F9C96B5CDAC08.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751353003754442, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_10CDAFD975BC0AB7.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751353003754537, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003754759, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_02726B6CC8A7DF56.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751353003754872, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003754953, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003755042, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003755109, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003755194, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003755299, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751353003755377, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003755488, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003755614, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003755690, "dur": 252, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1751353003755946, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003756028, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003756147, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003757028, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003757860, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003758668, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003759307, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003760431, "dur": 634, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Playables\\TimeControlPlayable.cs"}}, {"pid": 12345, "tid": 22, "ts": 1751353003759973, "dur": 1448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003761422, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003762069, "dur": 649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003762718, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003763485, "dur": 1581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003765067, "dur": 627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003765694, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003765888, "dur": 1474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003767363, "dur": 1002, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003768443, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003768562, "dur": 1669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003770231, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003770297, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003770387, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003770717, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003770866, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003771030, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003771101, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003771351, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003771579, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003771854, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003771991, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003772303, "dur": 707, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003773011, "dur": 1095, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003774106, "dur": 568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003774674, "dur": 2005, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003776730, "dur": 994, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003777724, "dur": 1066, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003778790, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003779118, "dur": 1086, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003780204, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003780370, "dur": 993, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353003781364, "dur": 268437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751353004049802, "dur": 440431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003722283, "dur": 30267, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003752556, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_44286934693FF148.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751353003753154, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003753229, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E009CCFB4A5E9FFB.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751353003753469, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003753559, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_889FFFE959BEFFC2.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751353003753790, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003753890, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F275A7F2C1FA929A.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751353003754219, "dur": 614, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003754855, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751353003754966, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003755042, "dur": 226, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751353003755287, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003755372, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751353003755554, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003755675, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003755739, "dur": 261, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751353003756023, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003756107, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751353003756173, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003756249, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003757136, "dur": 666, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003757803, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003758434, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003759197, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003760435, "dur": 627, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsSerializer.cs"}}, {"pid": 12345, "tid": 23, "ts": 1751353003759900, "dur": 1272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003761173, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003761830, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003762519, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003763218, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003763919, "dur": 1811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003765731, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003765956, "dur": 1395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003767352, "dur": 1021, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003768374, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003768538, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751353003768930, "dur": 796, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003769741, "dur": 947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1751353003770689, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003770872, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003771042, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003771145, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003771358, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003771582, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003771856, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003771985, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003772293, "dur": 664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003773023, "dur": 1064, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003774124, "dur": 561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003774686, "dur": 1996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003776740, "dur": 964, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003777705, "dur": 1072, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003778778, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003779126, "dur": 1081, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003780208, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003780352, "dur": 981, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003781333, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003781424, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751353003781649, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353003781713, "dur": 268099, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751353004049813, "dur": 440446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003723009, "dur": 29759, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003752770, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_DC7672C0573B7C85.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751353003752910, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003753219, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_6FBB1366163B00FB.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751353003753321, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003753506, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_00B5CEDD1ED5F4B3.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751353003753606, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003753692, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C099976D06C71E3D.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751353003753794, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003753911, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_71CC72797B2E953C.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751353003754196, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003754318, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_71CC72797B2E953C.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751353003754377, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3EA5AD5E9A933796.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751353003754499, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003754620, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751353003754710, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003754796, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751353003754884, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003755053, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003755124, "dur": 338, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1751353003755466, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003755557, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003755791, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003755842, "dur": 442, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751353003756287, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003757206, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003757944, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003758711, "dur": 1021, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003759733, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003760420, "dur": 2158, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Utilities\\StringHelpers.cs"}}, {"pid": 12345, "tid": 24, "ts": 1751353003760382, "dur": 2817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003763199, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003764106, "dur": 1615, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003765722, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003765923, "dur": 1453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003767376, "dur": 1018, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003768395, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003768544, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751353003768895, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003768954, "dur": 1092, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751353003770047, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003770284, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003770382, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003770775, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003770870, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003770986, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003771041, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003771126, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003771356, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003771606, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003771868, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003771979, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003772289, "dur": 678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003772967, "dur": 1118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003774118, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003774683, "dur": 2007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003776691, "dur": 1018, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003777709, "dur": 1074, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003778784, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003779116, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003779208, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751353003779404, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003779468, "dur": 594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751353003780062, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003780210, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003780288, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003780361, "dur": 994, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353003781355, "dur": 268454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751353004049810, "dur": 440451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751353004506467, "dur": 4034, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 41616, "tid": 1682, "ts": 1751353004543570, "dur": 3783, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 41616, "tid": 1682, "ts": 1751353004547415, "dur": 4766, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 41616, "tid": 1682, "ts": 1751353004533119, "dur": 20718, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}
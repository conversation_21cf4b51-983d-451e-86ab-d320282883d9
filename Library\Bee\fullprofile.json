{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 41616, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 41616, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 41616, "tid": 1585, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 41616, "tid": 1585, "ts": 1751352307928920, "dur": 1038, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 41616, "tid": 1585, "ts": 1751352307936239, "dur": 1481, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 41616, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 41616, "tid": 1, "ts": 1751352307121319, "dur": 20610, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 41616, "tid": 1, "ts": 1751352307141935, "dur": 72374, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 41616, "tid": 1, "ts": 1751352307214322, "dur": 311758, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 41616, "tid": 1585, "ts": 1751352307937725, "dur": 16, "ph": "X", "name": "", "args": {}}, {"pid": 41616, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307117648, "dur": 26279, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307143930, "dur": 771958, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307145721, "dur": 4893, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307150622, "dur": 3222, "ph": "X", "name": "ProcessMessages 20484", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307153849, "dur": 495, "ph": "X", "name": "ReadAsync 20484", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307154349, "dur": 26, "ph": "X", "name": "ProcessMessages 20489", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307154377, "dur": 61, "ph": "X", "name": "ReadAsync 20489", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307154442, "dur": 3, "ph": "X", "name": "ProcessMessages 721", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307154447, "dur": 50, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307154500, "dur": 2, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307154503, "dur": 44, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307154551, "dur": 2, "ph": "X", "name": "ProcessMessages 194", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307154554, "dur": 53, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307154611, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307154614, "dur": 49, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307154668, "dur": 2, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307154671, "dur": 48, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307154723, "dur": 3, "ph": "X", "name": "ProcessMessages 246", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307154728, "dur": 94, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307154826, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307154828, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307154886, "dur": 2, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307154889, "dur": 50, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307154944, "dur": 2, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307154947, "dur": 42, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307154993, "dur": 2, "ph": "X", "name": "ProcessMessages 82", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307154996, "dur": 49, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155049, "dur": 2, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155052, "dur": 51, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155107, "dur": 2, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155110, "dur": 45, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155161, "dur": 2, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155165, "dur": 41, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155209, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155212, "dur": 51, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155267, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155270, "dur": 51, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155324, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155327, "dur": 52, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155384, "dur": 2, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155387, "dur": 48, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155439, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155442, "dur": 51, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155497, "dur": 2, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155500, "dur": 47, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155551, "dur": 2, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155554, "dur": 48, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155606, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155608, "dur": 77, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155689, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155693, "dur": 53, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155753, "dur": 2, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155756, "dur": 47, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155806, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155809, "dur": 48, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155861, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155864, "dur": 47, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155915, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155917, "dur": 46, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155967, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307155970, "dur": 38, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156011, "dur": 3, "ph": "X", "name": "ProcessMessages 70", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156015, "dur": 63, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156083, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156085, "dur": 48, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156136, "dur": 1, "ph": "X", "name": "ProcessMessages 273", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156139, "dur": 51, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156194, "dur": 2, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156197, "dur": 73, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156274, "dur": 2, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156277, "dur": 51, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156332, "dur": 2, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156336, "dur": 44, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156383, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156386, "dur": 39, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156429, "dur": 1, "ph": "X", "name": "ProcessMessages 135", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156432, "dur": 52, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156487, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156490, "dur": 46, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156540, "dur": 2, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156543, "dur": 50, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156597, "dur": 2, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156600, "dur": 60, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156664, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156666, "dur": 67, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156737, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156740, "dur": 39, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156782, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156786, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156836, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156839, "dur": 44, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156888, "dur": 2, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156893, "dur": 49, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156945, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156948, "dur": 44, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156996, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307156999, "dur": 47, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157049, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157052, "dur": 44, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157100, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157104, "dur": 38, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157146, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157148, "dur": 43, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157195, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157198, "dur": 47, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157249, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157252, "dur": 40, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157296, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157299, "dur": 52, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157354, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157357, "dur": 46, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157406, "dur": 1, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157410, "dur": 52, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157465, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157469, "dur": 40, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157513, "dur": 1, "ph": "X", "name": "ProcessMessages 219", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157516, "dur": 46, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157566, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157568, "dur": 49, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157621, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157624, "dur": 66, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157693, "dur": 1, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157696, "dur": 65, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157766, "dur": 2, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157770, "dur": 79, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157853, "dur": 2, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157857, "dur": 47, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157908, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157911, "dur": 47, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157962, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307157965, "dur": 47, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158015, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158018, "dur": 47, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158069, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158071, "dur": 45, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158120, "dur": 1, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158123, "dur": 104, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158231, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158234, "dur": 91, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158330, "dur": 2, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158334, "dur": 56, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158393, "dur": 2, "ph": "X", "name": "ProcessMessages 676", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158396, "dur": 45, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158446, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158449, "dur": 62, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158516, "dur": 2, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158519, "dur": 62, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158585, "dur": 2, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158591, "dur": 46, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158641, "dur": 2, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158644, "dur": 53, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158702, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158705, "dur": 41, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158749, "dur": 2, "ph": "X", "name": "ProcessMessages 137", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158753, "dur": 51, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158808, "dur": 2, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158811, "dur": 47, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158861, "dur": 2, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158865, "dur": 48, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158917, "dur": 2, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158920, "dur": 50, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158974, "dur": 2, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307158978, "dur": 44, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307159025, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307159028, "dur": 50, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307159081, "dur": 2, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307159084, "dur": 49, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307159137, "dur": 2, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307159140, "dur": 48, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307159192, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307159195, "dur": 48, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307159246, "dur": 2, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307159249, "dur": 46, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307159299, "dur": 2, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307159302, "dur": 48, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307159356, "dur": 3, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307159361, "dur": 338, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307159702, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307159705, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307159754, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307159757, "dur": 50, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307159811, "dur": 2, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307159815, "dur": 50, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307159869, "dur": 2, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307159872, "dur": 53, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307159929, "dur": 2, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307159932, "dur": 47, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307159983, "dur": 2, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307159986, "dur": 47, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160037, "dur": 2, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160040, "dur": 45, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160090, "dur": 1, "ph": "X", "name": "ProcessMessages 147", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160093, "dur": 49, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160146, "dur": 4, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160154, "dur": 49, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160207, "dur": 2, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160210, "dur": 50, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160264, "dur": 2, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160268, "dur": 46, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160318, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160321, "dur": 51, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160377, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160380, "dur": 41, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160425, "dur": 1, "ph": "X", "name": "ProcessMessages 183", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160428, "dur": 55, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160490, "dur": 2, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160495, "dur": 82, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160586, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160591, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160687, "dur": 2, "ph": "X", "name": "ProcessMessages 876", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160691, "dur": 49, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160745, "dur": 2, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160748, "dur": 49, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160801, "dur": 2, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160804, "dur": 48, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160856, "dur": 2, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160859, "dur": 40, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160902, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160906, "dur": 52, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160963, "dur": 2, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307160966, "dur": 49, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161018, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161021, "dur": 47, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161072, "dur": 2, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161075, "dur": 49, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161128, "dur": 2, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161131, "dur": 50, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161185, "dur": 2, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161188, "dur": 48, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161240, "dur": 2, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161243, "dur": 37, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161284, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161287, "dur": 48, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161339, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161342, "dur": 47, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161393, "dur": 2, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161396, "dur": 60, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161467, "dur": 2, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161471, "dur": 55, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161530, "dur": 2, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161533, "dur": 52, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161589, "dur": 2, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161593, "dur": 48, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161644, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161647, "dur": 51, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161703, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161707, "dur": 46, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161758, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161761, "dur": 50, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161815, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161818, "dur": 48, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161870, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161873, "dur": 49, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161926, "dur": 2, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161929, "dur": 47, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161980, "dur": 2, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307161984, "dur": 45, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162033, "dur": 3, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162038, "dur": 43, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162084, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162087, "dur": 50, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162141, "dur": 2, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162144, "dur": 49, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162198, "dur": 2, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162201, "dur": 48, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162252, "dur": 2, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162256, "dur": 46, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162307, "dur": 2, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162311, "dur": 52, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162367, "dur": 2, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162370, "dur": 44, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162418, "dur": 1, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162421, "dur": 51, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162476, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162479, "dur": 59, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162542, "dur": 2, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162545, "dur": 52, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162600, "dur": 2, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162604, "dur": 46, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162653, "dur": 2, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162657, "dur": 47, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162708, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162711, "dur": 45, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162759, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162762, "dur": 49, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162815, "dur": 2, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162818, "dur": 52, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162874, "dur": 1, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162877, "dur": 51, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162932, "dur": 2, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162935, "dur": 52, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162991, "dur": 2, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307162994, "dur": 49, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163047, "dur": 2, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163050, "dur": 43, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163097, "dur": 1, "ph": "X", "name": "ProcessMessages 230", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163100, "dur": 48, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163152, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163155, "dur": 46, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163205, "dur": 2, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163208, "dur": 47, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163259, "dur": 2, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163262, "dur": 51, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163318, "dur": 2, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163321, "dur": 47, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163372, "dur": 2, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163375, "dur": 46, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163424, "dur": 1, "ph": "X", "name": "ProcessMessages 166", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163427, "dur": 48, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163480, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163483, "dur": 49, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163535, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163539, "dur": 47, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163589, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163592, "dur": 46, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163642, "dur": 2, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163645, "dur": 45, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163695, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163698, "dur": 44, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163746, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163750, "dur": 47, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163800, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163803, "dur": 38, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163845, "dur": 1, "ph": "X", "name": "ProcessMessages 71", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163848, "dur": 43, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163895, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163898, "dur": 51, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163953, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307163956, "dur": 44, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164004, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164007, "dur": 44, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164054, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164057, "dur": 44, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164105, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164108, "dur": 40, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164153, "dur": 2, "ph": "X", "name": "ProcessMessages 54", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164157, "dur": 43, "ph": "X", "name": "ReadAsync 54", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164203, "dur": 1, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164206, "dur": 50, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164260, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164263, "dur": 46, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164313, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164316, "dur": 103, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164427, "dur": 4, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164433, "dur": 134, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164574, "dur": 5, "ph": "X", "name": "ProcessMessages 1320", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164582, "dur": 57, "ph": "X", "name": "ReadAsync 1320", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164644, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164647, "dur": 62, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164715, "dur": 3, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164721, "dur": 67, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164792, "dur": 2, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164796, "dur": 48, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164848, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164851, "dur": 47, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164902, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164905, "dur": 51, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164960, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307164963, "dur": 44, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307165010, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307165013, "dur": 57, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307165073, "dur": 1, "ph": "X", "name": "ProcessMessages 243", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307165076, "dur": 69, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307165152, "dur": 4, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307165159, "dur": 541, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307165710, "dur": 4, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307165719, "dur": 233, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307165956, "dur": 7, "ph": "X", "name": "ProcessMessages 3828", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307165965, "dur": 53, "ph": "X", "name": "ReadAsync 3828", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307166022, "dur": 2, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307166026, "dur": 88, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307166122, "dur": 3, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307166127, "dur": 82, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307166213, "dur": 2, "ph": "X", "name": "ProcessMessages 828", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307166217, "dur": 62, "ph": "X", "name": "ReadAsync 828", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307166286, "dur": 3, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307166291, "dur": 108, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307166403, "dur": 3, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307166408, "dur": 67, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307166482, "dur": 3, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307166491, "dur": 66, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307166561, "dur": 2, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307166566, "dur": 61, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307166634, "dur": 3, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307166639, "dur": 69, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307166713, "dur": 2, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307166716, "dur": 60, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307166783, "dur": 3, "ph": "X", "name": "ProcessMessages 223", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307166789, "dur": 74, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307166866, "dur": 2, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307166870, "dur": 60, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307166936, "dur": 3, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307166942, "dur": 69, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167014, "dur": 2, "ph": "X", "name": "ProcessMessages 641", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167018, "dur": 58, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167082, "dur": 3, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167088, "dur": 69, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167162, "dur": 2, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167165, "dur": 54, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167226, "dur": 3, "ph": "X", "name": "ProcessMessages 81", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167231, "dur": 88, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167323, "dur": 2, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167328, "dur": 61, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167397, "dur": 3, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167402, "dur": 72, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167478, "dur": 2, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167481, "dur": 58, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167546, "dur": 3, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167552, "dur": 73, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167628, "dur": 2, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167631, "dur": 37, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167673, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167675, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167726, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167729, "dur": 45, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167780, "dur": 2, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167783, "dur": 45, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167832, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167835, "dur": 43, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167881, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167884, "dur": 48, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167936, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167939, "dur": 47, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167989, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307167992, "dur": 45, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168041, "dur": 2, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168044, "dur": 43, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168091, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168095, "dur": 47, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168145, "dur": 2, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168148, "dur": 37, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168191, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168193, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168247, "dur": 2, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168250, "dur": 69, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168323, "dur": 2, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168326, "dur": 72, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168402, "dur": 2, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168406, "dur": 53, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168463, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168466, "dur": 46, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168515, "dur": 2, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168518, "dur": 46, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168568, "dur": 3, "ph": "X", "name": "ProcessMessages 273", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168572, "dur": 47, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168623, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168626, "dur": 44, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168674, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168676, "dur": 44, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168724, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168727, "dur": 46, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168777, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168780, "dur": 44, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168827, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168830, "dur": 46, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168879, "dur": 2, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168882, "dur": 50, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168936, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168939, "dur": 48, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168990, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307168993, "dur": 44, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169040, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169043, "dur": 39, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169086, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169088, "dur": 51, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169145, "dur": 2, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169149, "dur": 44, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169197, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169199, "dur": 44, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169247, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169250, "dur": 48, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169302, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169305, "dur": 47, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169356, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169359, "dur": 52, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169414, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169418, "dur": 46, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169468, "dur": 1, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169471, "dur": 70, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169544, "dur": 2, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169548, "dur": 55, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169608, "dur": 2, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169612, "dur": 65, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169680, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169683, "dur": 54, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169740, "dur": 2, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169743, "dur": 51, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169799, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169802, "dur": 47, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169853, "dur": 1, "ph": "X", "name": "ProcessMessages 33", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169856, "dur": 48, "ph": "X", "name": "ReadAsync 33", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169907, "dur": 1, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169910, "dur": 42, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169955, "dur": 1, "ph": "X", "name": "ProcessMessages 182", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307169958, "dur": 195, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307170160, "dur": 2, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307170165, "dur": 65, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307170235, "dur": 3, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307170239, "dur": 470, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307170716, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307170720, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307170787, "dur": 579, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307171372, "dur": 94, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307171472, "dur": 12, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307171486, "dur": 56, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307171547, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307171553, "dur": 49, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307171607, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307171612, "dur": 76, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307171692, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307171696, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307171753, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307171759, "dur": 48, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307171811, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307171815, "dur": 47, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307171870, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307171875, "dur": 48, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307171926, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307171930, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307171984, "dur": 3, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307171989, "dur": 57, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172051, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172055, "dur": 63, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172131, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172139, "dur": 61, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172206, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172215, "dur": 65, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172285, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172289, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172343, "dur": 3, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172350, "dur": 74, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172430, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172435, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172487, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172491, "dur": 46, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172543, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172549, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172595, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172599, "dur": 43, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172648, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172653, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172710, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172715, "dur": 63, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172782, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172788, "dur": 59, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172853, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172859, "dur": 54, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172919, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172924, "dur": 51, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172981, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307172986, "dur": 56, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173046, "dur": 3, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173052, "dur": 47, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173104, "dur": 3, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173109, "dur": 42, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173154, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173159, "dur": 43, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173206, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173211, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173264, "dur": 3, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173269, "dur": 68, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173342, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173348, "dur": 48, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173404, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173409, "dur": 49, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173463, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173467, "dur": 50, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173530, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173535, "dur": 60, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173600, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173606, "dur": 37, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173646, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173650, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173691, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173695, "dur": 51, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173752, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173758, "dur": 59, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173822, "dur": 3, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173828, "dur": 54, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173887, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173893, "dur": 56, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173954, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307173960, "dur": 47, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174013, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174018, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174079, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174083, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174149, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174153, "dur": 66, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174223, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174227, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174285, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174291, "dur": 46, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174342, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174349, "dur": 58, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174413, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174419, "dur": 63, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174487, "dur": 3, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174492, "dur": 48, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174546, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174553, "dur": 59, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174618, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174622, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174677, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174682, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174757, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174760, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174816, "dur": 2, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174859, "dur": 50, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307174914, "dur": 440, "ph": "X", "name": "ProcessMessages 19", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307175358, "dur": 58, "ph": "X", "name": "ReadAsync 19", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307175420, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307175424, "dur": 13322, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307188759, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307188765, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307188849, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307188854, "dur": 74, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307188933, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307188936, "dur": 245, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307189188, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307189191, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307189276, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307189284, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307189354, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307189361, "dur": 362, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307189727, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307189731, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307189809, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307189817, "dur": 1981, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307191806, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307191810, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307191875, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307191880, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307191938, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307191943, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307192008, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307192011, "dur": 164, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307192180, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307192183, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307192262, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307192265, "dur": 617, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307192888, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307192893, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307192952, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307192957, "dur": 62, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307193024, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307193029, "dur": 452, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307193487, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307193491, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307193553, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307193558, "dur": 62, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307193626, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307193631, "dur": 66, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307193705, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307193712, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307193770, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307193776, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307193812, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307193815, "dur": 1248, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307195069, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307195073, "dur": 538, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307195617, "dur": 8, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307195628, "dur": 65, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307195697, "dur": 6, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307195705, "dur": 162, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307195872, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307195875, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307195927, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307195932, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307195999, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196004, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196073, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196078, "dur": 66, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196149, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196153, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196210, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196214, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196275, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196280, "dur": 50, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196336, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196341, "dur": 97, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196444, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196448, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196514, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196518, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196582, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196586, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196653, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196657, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196722, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196725, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196787, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196794, "dur": 58, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196857, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196860, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196905, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196908, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196951, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196953, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196996, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307196999, "dur": 162, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307197165, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307197168, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307197214, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307197218, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307197266, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307197269, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307197311, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307197314, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307197372, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307197376, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307197436, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307197441, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307197516, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307197522, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307197584, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307197589, "dur": 55, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307197649, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307197654, "dur": 281, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307197943, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307197947, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307198005, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307198009, "dur": 52, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307198066, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307198070, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307198123, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307198127, "dur": 145, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307198276, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307198280, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307198336, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307198340, "dur": 127, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307198473, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307198477, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307198523, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307198526, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307198581, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307198584, "dur": 387, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307198976, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307198979, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307199055, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307199058, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307199120, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307199123, "dur": 43, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307199171, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307199174, "dur": 169, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307199347, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307199354, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307199403, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307199406, "dur": 1497, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307200908, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307200911, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307200961, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307200964, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307201006, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307201009, "dur": 79, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307201093, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307201096, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307201155, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307201159, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307201214, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307201218, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307201272, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307201276, "dur": 261, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307201542, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307201546, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307201599, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307201602, "dur": 343, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307201951, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307201955, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307202020, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307202023, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307202073, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307202076, "dur": 488, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307202577, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307202580, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307202643, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307202646, "dur": 49, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307202700, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307202703, "dur": 204, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307202912, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307202915, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307202988, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307202992, "dur": 728, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307203727, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307203731, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307203792, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307203796, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307203858, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307203862, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307203917, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307203921, "dur": 168, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307204094, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307204096, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307204143, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307204146, "dur": 573, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307204727, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307204733, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307204797, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307204801, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307204851, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307204854, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307204903, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307204907, "dur": 194, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307205106, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307205109, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307205155, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307205159, "dur": 1585, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307206749, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307206753, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307206805, "dur": 953, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307207766, "dur": 286569, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307494348, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307494353, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307494412, "dur": 2632, "ph": "X", "name": "ProcessMessages 1224", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307497049, "dur": 7503, "ph": "X", "name": "ReadAsync 1224", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307504558, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307504561, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307504607, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307504612, "dur": 1293, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307505909, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307505912, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307505961, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307505964, "dur": 614, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307506583, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307506586, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307506630, "dur": 23, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307506655, "dur": 121174, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307627841, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307627850, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307627928, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307627934, "dur": 771, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307628712, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307628715, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307628773, "dur": 36, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307628813, "dur": 126305, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307755128, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307755132, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307755194, "dur": 32, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307755227, "dur": 9965, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307765199, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307765203, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307765251, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307765255, "dur": 617, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307765877, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307765880, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307765938, "dur": 26, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307765965, "dur": 128974, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307894949, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307894955, "dur": 102, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307895072, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307895081, "dur": 838, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307895924, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307895927, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307895997, "dur": 49, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307896050, "dur": 1377, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307897432, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307897437, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307897486, "dur": 713, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751352307898204, "dur": 17372, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 41616, "tid": 1585, "ts": 1751352307937744, "dur": 2034, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 41616, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 41616, "tid": 8589934592, "ts": 1751352307109766, "dur": 416359, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 41616, "tid": 8589934592, "ts": 1751352307526129, "dur": 7, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 41616, "tid": 8589934592, "ts": 1751352307526138, "dur": 1948, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 41616, "tid": 1585, "ts": 1751352307939781, "dur": 11, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 41616, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 41616, "tid": 4294967296, "ts": 1751352307073695, "dur": 843957, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 41616, "tid": 4294967296, "ts": 1751352307081813, "dur": 17622, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 41616, "tid": 4294967296, "ts": 1751352307917962, "dur": 7069, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 41616, "tid": 4294967296, "ts": 1751352307922514, "dur": 171, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 41616, "tid": 4294967296, "ts": 1751352307925170, "dur": 19, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 41616, "tid": 1585, "ts": 1751352307939794, "dur": 12, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751352307133307, "dur": 2850, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751352307136175, "dur": 964, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751352307137227, "dur": 112, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751352307137340, "dur": 1226, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751352307141096, "dur": 8910, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751352307152339, "dur": 2627, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751352307155039, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751352307155104, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751352307155213, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751352307155481, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751352307156202, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_A558B9E65539D7D0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751352307156264, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751352307156330, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751352307156722, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751352307156939, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751352307157078, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751352307157316, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751352307157953, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751352307158429, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751352307158890, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_16F8EA6B67022FB5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751352307158978, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751352307159290, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751352307159674, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751352307160732, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751352307161240, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751352307161563, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751352307162100, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751352307162733, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751352307163128, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751352307165081, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751352307165233, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Shared.Editor.ref.dll_6CA31C4FA5286AB1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751352307165292, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751352307165386, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751352307165731, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751352307165818, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751352307166377, "dur": 222, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751352307166790, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751352307166982, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751352307167157, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751352307167299, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751352307167444, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751352307167596, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751352307167741, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751352307167902, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751352307168053, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751352307168199, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751352307170319, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751352307170379, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751352307170500, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751352307170602, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751352307138611, "dur": 32121, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751352307170747, "dur": 725907, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751352307896656, "dur": 659, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751352307897316, "dur": 108, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751352307897425, "dur": 64, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751352307897644, "dur": 80, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751352307897909, "dur": 118, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751352307898051, "dur": 9160, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751352307138252, "dur": 32512, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307170795, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_7A1FD51B51344812.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751352307171136, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307171370, "dur": 627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_C2E93A20F98E8229.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751352307171998, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307172066, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_C2E93A20F98E8229.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751352307172178, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E522F23A96C2507B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751352307172283, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307172358, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E522F23A96C2507B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751352307172467, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_14A21DFAD1DFC142.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751352307172777, "dur": 417, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307173246, "dur": 854, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_02726B6CC8A7DF56.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751352307174101, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307174324, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307174417, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751352307174471, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751352307174797, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307174971, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751352307175071, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307175239, "dur": 1276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307176516, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307177332, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307178197, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307178933, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307180256, "dur": 891, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Listeners\\IGraphEventListenerData.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751352307179584, "dur": 1779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307181364, "dur": 2160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307183525, "dur": 3442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307187354, "dur": 746, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework\\UnityEditor.TestRunner\\TestLaunchers\\PlatformSetup\\PlatformSpecificSetup.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751352307186968, "dur": 1405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307188440, "dur": 53, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307188527, "dur": 911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307189438, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307189895, "dur": 2651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307192547, "dur": 1034, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307193581, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307193752, "dur": 1834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307195586, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307195786, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307196022, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307196115, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307196560, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307196817, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307196970, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307197178, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307197289, "dur": 545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307197834, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307198089, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307198159, "dur": 566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307198814, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307198894, "dur": 771, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307199665, "dur": 1875, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307201541, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307201769, "dur": 843, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307202612, "dur": 590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307203259, "dur": 1120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307204425, "dur": 972, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307205397, "dur": 299433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751352307504887, "dur": 243, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751352307504836, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751352307505204, "dur": 2016, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751352307507225, "dur": 389496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307138314, "dur": 32492, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307170811, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_BF1A247579C7383B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751352307171239, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307171333, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_7F1C5DFD0AD39F77.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751352307171560, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307171637, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_6C4C0BE13C75841D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751352307171752, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307171892, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_ED94C9E717A89F9F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751352307171977, "dur": 400, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307172422, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_54605A4C765A4A77.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751352307172715, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307172956, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307173345, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751352307173938, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307174087, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307174291, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751352307174380, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751352307174444, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307174530, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751352307174717, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307174946, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751352307175073, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307176164, "dur": 623, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Signals\\SignalReceiverHeader.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751352307175203, "dur": 1661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307176865, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307177482, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307178112, "dur": 597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307178710, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307179404, "dur": 634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307180371, "dur": 755, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Utilities\\TimelineClipExtensions.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751352307180039, "dur": 1400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307182343, "dur": 753, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Editor\\Internal\\SerializedPropertyHelpers.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751352307184504, "dur": 612, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Editor\\Internal\\AdvancedDropdown\\AdvancedDropdownState.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751352307181440, "dur": 3677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307185117, "dur": 2783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307187901, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307188671, "dur": 729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307189401, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307189925, "dur": 2572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307192498, "dur": 1079, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307193577, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307193719, "dur": 1846, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307195574, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307195634, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307195741, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751352307195818, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307196035, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307196131, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307196622, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307196821, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307196987, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307197111, "dur": 186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307197298, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307197847, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307198141, "dur": 567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307198760, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307198866, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307198933, "dur": 746, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307199679, "dur": 1888, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307201567, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307201773, "dur": 835, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307202608, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307203205, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307203310, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751352307203501, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307203571, "dur": 623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751352307204195, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307204367, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307204458, "dur": 977, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307205435, "dur": 299423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751352307504859, "dur": 391805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307138304, "dur": 32490, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307170798, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_00CD99E311DC019E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751352307171077, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307171312, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_00CD99E311DC019E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751352307171452, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_74513773BD43745E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751352307171659, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307171745, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_39C615B7DD834A4A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751352307171844, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307172197, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FF5FCB0F465F7311.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751352307172316, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307172685, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_800370C755C131C9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751352307172930, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307173154, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307173332, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307173467, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307173561, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307173760, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307173919, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751352307174027, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307174152, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307174242, "dur": 272, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751352307174516, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751352307174743, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307174821, "dur": 318, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751352307175140, "dur": 1472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307176613, "dur": 658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307177272, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307177990, "dur": 605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307178595, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307180363, "dur": 744, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\InstanceInvokerBase.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751352307179294, "dur": 1813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307184033, "dur": 575, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Plugins\\WebGL\\WebGLJoystick.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751352307181108, "dur": 3559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307184668, "dur": 2472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307187141, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307187719, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307188383, "dur": 105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307188520, "dur": 886, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307189406, "dur": 483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307189889, "dur": 2602, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307192491, "dur": 1066, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307193558, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307193704, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751352307194159, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307194249, "dur": 2395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751352307196645, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307196811, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307196892, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751352307197250, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307197333, "dur": 1046, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751352307198380, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307198609, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307198808, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307198901, "dur": 746, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307199648, "dur": 1904, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307201553, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307201777, "dur": 827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307202657, "dur": 603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307203261, "dur": 1107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307204408, "dur": 979, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307205388, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307205485, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751352307205696, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307205756, "dur": 299112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751352307504872, "dur": 391818, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307139367, "dur": 31657, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307171033, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_A8A2BEB223D0393B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751352307171275, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307171390, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_A8A2BEB223D0393B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751352307171526, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_6C2CA7D176733A22.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751352307171644, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307171781, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_976F9C96B5CDAC08.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751352307171900, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307172128, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_95C124FA2E4BBBB0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751352307172238, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307172389, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_963BCDCD478EB63D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751352307172718, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307172876, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_438C72DDFEF65815.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751352307173065, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307173285, "dur": 212, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_438C72DDFEF65815.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751352307173512, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751352307173573, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307173671, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307173744, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307173855, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751352307173913, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751352307174005, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307174188, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307174350, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751352307174418, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307174550, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751352307174825, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307174900, "dur": 978, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751352307176362, "dur": 2282, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage\\Editor\\CoverageFormats\\CoverageFormat.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751352307179027, "dur": 1013, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider\\Rider\\Editor\\Util\\SerializableVersion.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751352307175879, "dur": 4268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307180266, "dur": 879, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Attributes\\TimelineHelpURLAttribute.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751352307180148, "dur": 1410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307182649, "dur": 722, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Editor\\Analytics\\InputBuildAnalytic.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751352307181559, "dur": 3726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307187871, "dur": 844, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\AssetMenu\\AssetFilesFilterPatternsMenuBuilder.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751352307185286, "dur": 3430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307188716, "dur": 686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307189403, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307189853, "dur": 2623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307192476, "dur": 1072, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307193621, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307193722, "dur": 1820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307195579, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307195733, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307195796, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307196026, "dur": 625, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307196665, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307196818, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307196968, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307197097, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307197335, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307197823, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307198213, "dur": 516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307198729, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307198874, "dur": 770, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307199644, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307199747, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751352307199930, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307199990, "dur": 1608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751352307201598, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307201797, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307201870, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751352307202115, "dur": 934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751352307203050, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307203239, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307203302, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751352307203534, "dur": 708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751352307204243, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307204416, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307204489, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751352307204714, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751352307205252, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307205477, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751352307205727, "dur": 535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751352307206965, "dur": 100, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307207994, "dur": 287001, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751352307505526, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751352307504827, "dur": 903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751352307506360, "dur": 127, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751352307507168, "dur": 248524, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751352307765636, "dur": 144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751352307765617, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751352307765806, "dur": 710, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751352307766522, "dur": 130134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307138449, "dur": 32385, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307170838, "dur": 394, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_16A54265357DDF1F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751352307171234, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307171359, "dur": 151, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_16A54265357DDF1F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751352307171513, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E009CCFB4A5E9FFB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751352307171631, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307171714, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_00B5CEDD1ED5F4B3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751352307171821, "dur": 354, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307172218, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F275A7F2C1FA929A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751352307172389, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307172589, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F275A7F2C1FA929A.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751352307172700, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3EA5AD5E9A933796.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751352307172898, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307173065, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3EA5AD5E9A933796.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751352307173217, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307173359, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307173552, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751352307173606, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307173766, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307174035, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307174131, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307174349, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307174458, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307174601, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751352307174790, "dur": 1115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751352307176314, "dur": 566, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider\\Rider\\Editor\\Util\\CommandLineParser.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751352307175908, "dur": 1940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307177849, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307178592, "dur": 1718, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqGraphs\\Changelog_1_1_0.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751352307180372, "dur": 767, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqGraphs\\Changelog_1_0_1.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751352307178492, "dur": 3059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307183869, "dur": 510, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Editor\\AssetEditor\\InputActionPropertiesView.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751352307181552, "dur": 2954, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307185154, "dur": 716, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\UI\\Tree\\TreeHeaderSettings.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751352307184507, "dur": 3008, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307187516, "dur": 1750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307189267, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307189398, "dur": 470, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307189869, "dur": 2633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307192503, "dur": 1069, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307193572, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307193714, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751352307193985, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307194098, "dur": 958, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751352307195057, "dur": 485, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307195578, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307195650, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307195785, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307196030, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307196152, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307196576, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307196822, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307196979, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307197095, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751352307197464, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307197556, "dur": 908, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751352307198465, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307198655, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307198771, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307198896, "dur": 744, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307199692, "dur": 1876, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307201568, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307201778, "dur": 828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307202653, "dur": 604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307203257, "dur": 1115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307204405, "dur": 996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307205402, "dur": 299425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751352307504869, "dur": 123512, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751352307504829, "dur": 123555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751352307628416, "dur": 928, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751352307629351, "dur": 267319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307138547, "dur": 32309, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307170868, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FCC5A8FB58ECD1D2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751352307171319, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307171588, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_051DE14E9379244A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751352307171781, "dur": 464, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307172246, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_051DE14E9379244A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751352307172364, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_B563F2CB5E9D696E.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751352307172557, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307172777, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_09E134ACBD96AA82.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751352307173031, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307173281, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_696347C4732CD601.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751352307173461, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307173694, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307173867, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751352307174056, "dur": 359, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307174416, "dur": 472, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751352307174921, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307175077, "dur": 1180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307176363, "dur": 1305, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.performance.profile-analyzer\\Editor\\ThreadFrameTime.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751352307176258, "dur": 2871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307179135, "dur": 1036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307180358, "dur": 925, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_TextInfo.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751352307180172, "dur": 1761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307182512, "dur": 623, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Actions\\Interactions\\HoldInteraction.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751352307181934, "dur": 3582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307185517, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307186173, "dur": 643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307186817, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307187818, "dur": 902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307188721, "dur": 678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307189399, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307189859, "dur": 2607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307192534, "dur": 1041, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307193575, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307193718, "dur": 1828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307195568, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307195822, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307196038, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307196102, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307196569, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307196807, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307196879, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307196981, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307197122, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307197300, "dur": 553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307197854, "dur": 304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307198158, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307198760, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307198868, "dur": 787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307199655, "dur": 1889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307201544, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307201775, "dur": 867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307202643, "dur": 573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307203216, "dur": 1218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307204435, "dur": 980, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307205415, "dur": 299493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751352307504909, "dur": 391793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307139838, "dur": 31270, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307171111, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_DC7672C0573B7C85.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751352307171344, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307171568, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_DCB00145011AACBC.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751352307171777, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307171967, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8B525A02B9DF9C7E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751352307172101, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307172261, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8B525A02B9DF9C7E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751352307172439, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_67F30B299A5D5154.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751352307172656, "dur": 426, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307173188, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_3AA3364A40DB9F8B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751352307173282, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307173497, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751352307173552, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307173815, "dur": 345, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307174161, "dur": 258, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751352307174458, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307174585, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751352307174722, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307174840, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751352307174921, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307175046, "dur": 1733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307176840, "dur": 1019, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Flow\\Properties\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751352307176780, "dur": 1922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307179510, "dur": 837, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Inspection\\Unity\\Vector3Inspector.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751352307180348, "dur": 802, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Inspection\\Unity\\Vector2Inspector.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751352307178703, "dur": 3075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307182315, "dur": 1120, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Devices\\Commands\\RequestResetCommand.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751352307185231, "dur": 610, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Devices\\Commands\\EnableDeviceCommand.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751352307181779, "dur": 4138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307185918, "dur": 575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307186494, "dur": 533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307187028, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307187857, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307188769, "dur": 622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307189392, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307189879, "dur": 2636, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307192515, "dur": 1046, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307193561, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307193685, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751352307194084, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307194242, "dur": 1270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751352307195513, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307195729, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307195811, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751352307196154, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307196227, "dur": 1312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751352307197540, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307197832, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307197921, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307198138, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307198598, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307198757, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307198876, "dur": 811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307199688, "dur": 1882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307201571, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307201767, "dur": 855, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307202623, "dur": 597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307203221, "dur": 1136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307204365, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307204424, "dur": 983, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307205408, "dur": 299475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751352307504884, "dur": 391814, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307138594, "dur": 32280, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307170881, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_493844D967613FA0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751352307171127, "dur": 794, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307171945, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D22D11490AEAA9B0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751352307172135, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307172291, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D22D11490AEAA9B0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751352307172493, "dur": 814, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D7BFC8363EAC4767.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751352307173309, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307173388, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751352307173632, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307173727, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751352307173815, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307173904, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307174174, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307174279, "dur": 675, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751352307174957, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751352307175009, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307175102, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307175604, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307176668, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307177781, "dur": 1225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307179007, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307180362, "dur": 771, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Collections\\NonNullableDictionary.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751352307179890, "dur": 1426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307181317, "dur": 3141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307185037, "dur": 553, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Views\\Branch\\BranchesSelection.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751352307186400, "dur": 799, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\UI\\UnityThreadWaiter.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751352307188014, "dur": 819, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\UI\\UnityConstants.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751352307184458, "dur": 4557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307189015, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307189454, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307189876, "dur": 2656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307192532, "dur": 1041, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307193573, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307193736, "dur": 1792, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307195539, "dur": 511, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307196058, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307196114, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307196580, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307196800, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307196974, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307197113, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307197314, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307197829, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307197978, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307198142, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307198763, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307198885, "dur": 788, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307199673, "dur": 1892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307201566, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307201780, "dur": 847, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307202628, "dur": 583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307203211, "dur": 1222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307204433, "dur": 996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307205430, "dur": 299459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751352307504890, "dur": 391836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307138707, "dur": 32194, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307170906, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_B01BEAEDCAC9F962.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751352307171207, "dur": 332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307171549, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BA5431EB9EAB1D55.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751352307171817, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307172037, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BA5431EB9EAB1D55.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751352307172101, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6678507EC9682BC1.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751352307172357, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307172607, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6678507EC9682BC1.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751352307172795, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BED6F64D074B3174.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751352307172899, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307172999, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_69199FEE963F7F27.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751352307173119, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307173185, "dur": 433, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_69199FEE963F7F27.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751352307173635, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307173740, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307174067, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307174289, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1751352307174418, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307174478, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751352307174608, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307174679, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307174771, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307174858, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307174957, "dur": 305, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751352307175265, "dur": 1450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307176725, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307177330, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307177996, "dur": 665, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307178662, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307180373, "dur": 776, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\InvalidOperatorException.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751352307179372, "dur": 1942, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307181315, "dur": 3485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307186185, "dur": 698, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\UI\\DrawActionToolbar.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751352307186956, "dur": 777, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\UI\\DrawActionButtonWithMenu.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751352307184801, "dur": 3481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307188283, "dur": 546, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307188829, "dur": 594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307189423, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307189855, "dur": 2623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307192479, "dur": 1106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307193585, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307193727, "dur": 1816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307195569, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307195703, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307195798, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307196032, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307196105, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307196573, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307196804, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307196966, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307197123, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307197339, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307197830, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307198085, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307198150, "dur": 731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307198882, "dur": 784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307199667, "dur": 1902, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307201569, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307201784, "dur": 854, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307202639, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307203238, "dur": 1139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307204378, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307204429, "dur": 1011, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307205440, "dur": 299402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751352307504844, "dur": 391805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307138753, "dur": 32162, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307170921, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_44286934693FF148.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751352307171185, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307171382, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46E217D19A6E01BD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751352307171592, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307171679, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1830F8D8229EF0EB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751352307171782, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307171865, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_173731A5F5D7E041.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751352307171968, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307172046, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_00433992BAF13A73.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751352307172314, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307172374, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_00433992BAF13A73.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751352307172462, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_BDFB8953D401853F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751352307172655, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307172991, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9428A5C8BBBE8A51.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751352307173089, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307173325, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307173596, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751352307173701, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307174027, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307174199, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307174397, "dur": 985, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751352307175800, "dur": 652, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Localization\\Localization.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751352307175384, "dur": 1570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307176955, "dur": 1009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307177966, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307179129, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307180370, "dur": 1142, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_PackageResourceImporter.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751352307180179, "dur": 1769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307183286, "dur": 558, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Actions\\Composites\\OneModifierComposite.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751352307184776, "dur": 633, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\_Deprecated\\CollabPlugin.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751352307181949, "dur": 3637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307185587, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307186456, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307187365, "dur": 2300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307189665, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307189875, "dur": 2608, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307192484, "dur": 1075, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307193560, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307193695, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751352307194116, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307194219, "dur": 1651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751352307195871, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307196117, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307196194, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751352307196585, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307196658, "dur": 2012, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751352307198672, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307198868, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307198941, "dur": 717, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307199659, "dur": 1913, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307201573, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307201786, "dur": 846, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307202633, "dur": 612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307203246, "dur": 1192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307204439, "dur": 979, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307205419, "dur": 299428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751352307504848, "dur": 391810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307138800, "dur": 32129, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307170933, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_1C55A2A4A7EC33E2.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751352307171136, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307171332, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_129B2BEEEC662028.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751352307171529, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307171622, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_C287EB2677BA7F93.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751352307171737, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307171813, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_5E37763FF8B95A1F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751352307172097, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307172218, "dur": 186, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_5E37763FF8B95A1F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751352307172417, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A99F700E950BD818.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751352307172628, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307172901, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_42EBD154127454A5.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751352307173011, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307173207, "dur": 482, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_42EBD154127454A5.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751352307173722, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751352307174028, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307174261, "dur": 605, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751352307174868, "dur": 14750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751352307189620, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307189856, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307189943, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751352307190265, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307190346, "dur": 1899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751352307192246, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307192477, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307192566, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751352307192792, "dur": 615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751352307193408, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307193557, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307193681, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751352307194112, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307194179, "dur": 2587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751352307196767, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307196976, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307197094, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751352307197417, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307197478, "dur": 2002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751352307199481, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307199653, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307199740, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751352307199970, "dur": 1425, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751352307201396, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307201544, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307201619, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751352307201822, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307201880, "dur": 590, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751352307202471, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307202613, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307202686, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307203251, "dur": 1175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307204427, "dur": 979, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307205406, "dur": 299456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751352307504863, "dur": 391820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307138895, "dur": 32046, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307170947, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5543A9502BBA10BA.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751352307171198, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307171375, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5543A9502BBA10BA.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751352307171444, "dur": 666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751352307172111, "dur": 465, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307172595, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_27EF5DC3D06E8F78.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751352307172767, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307173084, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_27EF5DC3D06E8F78.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751352307173237, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9C39811BC6DD0BF6.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751352307173350, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307173514, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307173625, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307173721, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751352307174010, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307174108, "dur": 15057, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751352307189167, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307189393, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307189510, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307189856, "dur": 2630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307192486, "dur": 1067, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307193554, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307193686, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751352307194151, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307194256, "dur": 1209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751352307195466, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307195698, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307195833, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307196040, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307196102, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307196186, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307196567, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307196810, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307196975, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307197108, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307197285, "dur": 561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307197847, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307198139, "dur": 551, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307198728, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307198819, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307198877, "dur": 768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307199646, "dur": 1911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307201558, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307201782, "dur": 858, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307202641, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307203243, "dur": 1123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307204407, "dur": 991, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307205399, "dur": 299467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751352307504867, "dur": 391812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307138922, "dur": 32033, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307170960, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_9DC68FB86218AF77.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751352307171378, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307171539, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_4E756196A062909D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751352307171649, "dur": 413, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307172063, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_4E756196A062909D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751352307172143, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B8F1FBD1DD727BB2.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751352307172323, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307172664, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E671E5897665917D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751352307172773, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307172884, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_4FB16FC3A796B887.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751352307172990, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307173312, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307173412, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307173542, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751352307173740, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307173860, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307173993, "dur": 376, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307174428, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307174522, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751352307174835, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307174958, "dur": 888, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751352307175848, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307176799, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307177420, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307178082, "dur": 636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307178719, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307179431, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307180362, "dur": 733, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Playables\\PrefabControlPlayable.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751352307180073, "dur": 1334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307181408, "dur": 1839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307183249, "dur": 502, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Views\\Merge\\Developer\\UnityMergeTree.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751352307184990, "dur": 614, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Views\\Merge\\Developer\\IsCurrent.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751352307183248, "dur": 3525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307186774, "dur": 529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307187304, "dur": 1522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307188826, "dur": 607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307189433, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307189866, "dur": 2627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307192493, "dur": 1058, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307193606, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307193721, "dur": 544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751352307194266, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307194339, "dur": 1537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751352307195878, "dur": 639, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307196562, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307196628, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307196825, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307196986, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307197096, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307197305, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307197827, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307198201, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307198762, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307198869, "dur": 794, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307199664, "dur": 1882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307201547, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307201770, "dur": 850, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307202620, "dur": 610, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307203231, "dur": 1149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307204411, "dur": 1010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307205421, "dur": 299452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751352307504873, "dur": 391819, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307139011, "dur": 31958, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307170974, "dur": 616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_52F12B5AEAC5FC8F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751352307171593, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307171760, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_42AEDBADC03A3022.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751352307171972, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307172043, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_42AEDBADC03A3022.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751352307172120, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_97D91564DB667E63.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751352307172231, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307172329, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_31922632D84128F5.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751352307172560, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307172652, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_31922632D84128F5.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751352307172783, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E8D1A39870CABAC1.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751352307172889, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307172992, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307173135, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307173266, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751352307173369, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751352307173424, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307173518, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751352307173747, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307173873, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751352307174056, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307174237, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307174386, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1751352307174541, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751352307174726, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307174813, "dur": 202, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751352307175016, "dur": 1531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307176548, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307177835, "dur": 1776, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Ports\\UnitPortDefinition.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751352307177218, "dur": 2420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307179639, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307180329, "dur": 790, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Utilities\\InternedString.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751352307183055, "dur": 854, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\State\\IInputStateTypeInfo.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751352307183910, "dur": 682, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\State\\IInputStateChangeMonitor.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751352307180329, "dur": 4745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307186289, "dur": 1316, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\PlasticNotification.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751352307187886, "dur": 691, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\PlasticApp.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751352307185075, "dur": 4089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307189165, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307189416, "dur": 461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307189877, "dur": 2662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307192540, "dur": 1048, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307193588, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307193741, "dur": 1784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307195532, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307195609, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307195689, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307195801, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307196029, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307196111, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307196566, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307196801, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307196871, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307196982, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307197119, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307197366, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307197880, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307198163, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307198756, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307198871, "dur": 782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307199653, "dur": 1909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307201563, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307201801, "dur": 823, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307202624, "dur": 582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307203207, "dur": 1157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307204412, "dur": 977, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307205390, "dur": 299446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307504837, "dur": 26854, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751352307531692, "dur": 365013, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307139068, "dur": 31916, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307170990, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_F4165E534DD27516.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751352307171166, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307171464, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_6FBB1366163B00FB.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751352307171758, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307171924, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_6E64691D4ACC974A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751352307172064, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307172223, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_71CC72797B2E953C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751352307172348, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307172677, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B47D5ED0AB2E73A1.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751352307172791, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307173080, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751352307173189, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307173321, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307173687, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307174051, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307174197, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307174426, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307174505, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751352307174779, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307174910, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307174980, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307176359, "dur": 653, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Attributes\\MenuEntryAttribute.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751352307175544, "dur": 1556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307177100, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307177796, "dur": 1346, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Events\\Input\\OnKeyboardInput.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751352307177743, "dur": 2292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307180240, "dur": 901, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Collections\\IKeyedCollection.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751352307180036, "dur": 1631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307181668, "dur": 2288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307186120, "dur": 2142, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Views\\Changesets\\ChangesetsListView.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751352307183957, "dur": 4839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307188796, "dur": 597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307189394, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307189851, "dur": 2629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307192481, "dur": 1081, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307193563, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307193701, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751352307194042, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307194122, "dur": 1141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751352307195264, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307195532, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307195597, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307195692, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307195797, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307196024, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307196122, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307196613, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307196814, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307196976, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307197113, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307197287, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307197849, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307198152, "dur": 579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307198732, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307198895, "dur": 761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307199657, "dur": 1879, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307201588, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307201764, "dur": 849, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307202614, "dur": 600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307203215, "dur": 1161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307204376, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307204428, "dur": 974, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307205403, "dur": 299471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751352307504875, "dur": 391813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307139148, "dur": 31850, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307171004, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_921345D5C92C6DDE.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751352307171149, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307171346, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_2BCC56A3C6071870.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751352307171595, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307171739, "dur": 572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B1475218F12198D8.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751352307172312, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307172408, "dur": 764, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C5419324AD6BC088.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751352307173173, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307173267, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C5419324AD6BC088.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751352307173369, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307173462, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307173563, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307173804, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307173900, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307174152, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1751352307174304, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307174434, "dur": 553, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751352307174988, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307175650, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307176836, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307177529, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307178202, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307178810, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307179502, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307180369, "dur": 823, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage\\Editor\\CoverageFormats\\OpenCover\\Model\\Summary.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751352307180209, "dur": 1744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307181954, "dur": 2994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307185691, "dur": 626, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Tool\\ToolConstants.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751352307184949, "dur": 3262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307188212, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307188946, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307189418, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307189901, "dur": 2619, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307192522, "dur": 1045, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307193568, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307193728, "dur": 1806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307195535, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307195589, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307195789, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307196025, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307196113, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307196564, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307196802, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307196972, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307197089, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751352307197453, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307197589, "dur": 960, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751352307198550, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307198764, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307198866, "dur": 776, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307199712, "dur": 1843, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307201556, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307201760, "dur": 858, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307202618, "dur": 590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307203209, "dur": 1242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307204451, "dur": 1000, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307205452, "dur": 299445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751352307504898, "dur": 391810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307139279, "dur": 31733, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307171017, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_030C0AE8F7F2C9CD.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751352307171172, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307171387, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8ECA4E4329F1BADB.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751352307171617, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307171693, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_99CD71E244B915CF.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751352307171777, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307172024, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751352307172225, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307172302, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751352307172448, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E0B83A434CA2B946.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751352307172676, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307172921, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307173071, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307173192, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307173285, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751352307173403, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307173479, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751352307173594, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751352307173705, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307173852, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751352307173921, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307174000, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307174193, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1751352307174251, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307174404, "dur": 1526, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1751352307175932, "dur": 1310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307177242, "dur": 676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307177918, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307178522, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307179206, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307180368, "dur": 753, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsDictionaryConverter.cs"}}, {"pid": 12345, "tid": 17, "ts": 1751352307179798, "dur": 1449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307182889, "dur": 766, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Plugins\\OSX\\OSXSupport.cs"}}, {"pid": 12345, "tid": 17, "ts": 1751352307181248, "dur": 2697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307185940, "dur": 515, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Views\\Diff\\DiffSelection.cs"}}, {"pid": 12345, "tid": 17, "ts": 1751352307183946, "dur": 3410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307187975, "dur": 708, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.NetTcp.dll"}}, {"pid": 12345, "tid": 17, "ts": 1751352307187357, "dur": 2417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307189775, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307189868, "dur": 2603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307192471, "dur": 1095, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307193567, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307193694, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751352307194136, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307194229, "dur": 1393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751352307195623, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307196031, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307196109, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751352307196469, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307196562, "dur": 1407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751352307197970, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307198152, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307198221, "dur": 562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307198783, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307198863, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307199150, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307199689, "dur": 1865, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307201554, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307201779, "dur": 831, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307202611, "dur": 602, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307203214, "dur": 1222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307204436, "dur": 976, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307205413, "dur": 299452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751352307504865, "dur": 391849, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307138415, "dur": 32406, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307170828, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_80B8935D38233098.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751352307171171, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307171314, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_80B8935D38233098.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751352307171496, "dur": 810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_F24F3CD4D4D128AB.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751352307172308, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307172583, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_A7B3B5FA99331E00.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751352307172935, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307173020, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751352307173137, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307173259, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_39F0A7015032F01B.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751352307173414, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307173515, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751352307173572, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307173724, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307173925, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751352307174041, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307174255, "dur": 241, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751352307174501, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751352307174694, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307174763, "dur": 295, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751352307175075, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307175785, "dur": 630, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Editor\\TMP\\TMP_ProjectTextSettings.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751352307175683, "dur": 1578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307177262, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307178292, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307178989, "dur": 638, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307179628, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307180375, "dur": 792, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.settings-manager\\Editor\\SettingsDictionary.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751352307180224, "dur": 1952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307182695, "dur": 1002, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Views\\PendingChanges\\PendingMergeLinks\\MergeLinkListViewItem.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751352307182176, "dur": 4496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307186793, "dur": 806, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework\\UnityEditor.TestRunner\\TestRunner\\Utils\\CachingTestListProvider.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751352307188322, "dur": 655, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework\\UnityEditor.TestRunner\\TestRun\\Tasks\\UnlockReloadAssembliesTask.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751352307186674, "dur": 2364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307189039, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307189399, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307189490, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307189879, "dur": 2594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307192475, "dur": 1094, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307193570, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307193732, "dur": 1803, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307195536, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307195598, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307195780, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307196027, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307196110, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307196558, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307196808, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307196978, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307197095, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751352307197530, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307197614, "dur": 1179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751352307198795, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307199159, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307199651, "dur": 1891, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307201543, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307201762, "dur": 863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307202625, "dur": 600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307203226, "dur": 1136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307204410, "dur": 985, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307205396, "dur": 299437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307504834, "dur": 23916, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307531200, "dur": 478, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 18, "ts": 1751352307528752, "dur": 2930, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751352307531683, "dur": 365033, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307139480, "dur": 31561, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307171049, "dur": 572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_592CC89A6471077C.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751352307171622, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307171766, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_921E3B2D436CDAE7.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751352307171913, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307172192, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_805FFE03DDD45823.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751352307172374, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307172587, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9F68AD12AC89760A.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751352307172684, "dur": 490, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307173226, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751352307173437, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307173593, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751352307173745, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307173959, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307174183, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307174263, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1751352307174387, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751352307174869, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307177629, "dur": 1974, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio\\Editor\\Messaging\\ExceptionEventArgs.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751352307175043, "dur": 4561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307180376, "dur": 777, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Events\\IGraphEventHandler.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751352307179605, "dur": 1733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307184451, "dur": 663, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Editor\\UITKAssetEditor\\Views\\PropertiesView.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751352307181339, "dur": 4001, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307185341, "dur": 1308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307186651, "dur": 1036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307187688, "dur": 2257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307189947, "dur": 2563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307192511, "dur": 1053, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307193565, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307193735, "dur": 1797, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307195533, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307195600, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307195696, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307195758, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307196036, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307196100, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307196577, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307196812, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307196970, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307197099, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307197295, "dur": 553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307197848, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307198087, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307198148, "dur": 563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307198772, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307198873, "dur": 788, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307199661, "dur": 1888, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307201550, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307201765, "dur": 870, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307202635, "dur": 565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307203264, "dur": 1154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307204419, "dur": 963, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307205437, "dur": 299415, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751352307504853, "dur": 391816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307139521, "dur": 31531, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307171056, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B52CD1DFCA664AB3.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751352307171242, "dur": 309, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307171575, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F8AA39E8D5CE00CF.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751352307171690, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307171773, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CB3FD07F3B098D2B.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751352307171882, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307172153, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B5DB3F27CCAA63FE.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751352307172265, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307172348, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_DC79D8DB49767D4F.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751352307172595, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307172657, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_DC79D8DB49767D4F.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751352307172816, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B014F0E06EB9C8B3.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751352307172926, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307173124, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307173277, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_2CD7161C662F4679.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751352307173503, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307173581, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1751352307173734, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307173865, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307173985, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307174206, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307174272, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1751352307174373, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307174450, "dur": 524, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751352307174977, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751352307175045, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307175394, "dur": 761, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\treeview\\TimelineDragging.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751352307175117, "dur": 1383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307176993, "dur": 878, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center\\Editor\\MultiplayerCenterWindow\\UI\\QuestionViewFactory.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751352307176501, "dur": 1516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307178018, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307178610, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307179358, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307180265, "dur": 890, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Events\\Marker.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751352307180088, "dur": 1433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307181522, "dur": 3604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307185127, "dur": 2378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307187506, "dur": 2094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307189601, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307189863, "dur": 2627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307192490, "dur": 1066, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307193556, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307193689, "dur": 615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751352307194304, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307194401, "dur": 1093, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751352307195496, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307195738, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307195807, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751352307196174, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307196490, "dur": 1142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751352307197633, "dur": 406, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307198091, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307198165, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307198769, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307198883, "dur": 785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307199668, "dur": 1910, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307201579, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307201790, "dur": 865, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307202655, "dur": 608, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307203263, "dur": 1111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307204479, "dur": 925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307205405, "dur": 299440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751352307504846, "dur": 391798, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307139604, "dur": 31460, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307171070, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D447C5E6C0383BF9.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751352307171344, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307171447, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_61E5A1A2114B6332.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751352307171682, "dur": 414, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307172109, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C099976D06C71E3D.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751352307172242, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307172338, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_20E0F9318BFD3B5B.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751352307172613, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307172694, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_20E0F9318BFD3B5B.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751352307172802, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_10CDAFD975BC0AB7.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751352307172916, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307173092, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_10CDAFD975BC0AB7.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751352307173368, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307173460, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751352307173682, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307173751, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751352307173893, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751352307173989, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307174145, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307174241, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307174483, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751352307174780, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307174982, "dur": 850, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751352307175836, "dur": 1159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307176996, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307177585, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307178211, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307178781, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307179560, "dur": 685, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307180317, "dur": 767, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Utilities\\SpriteUtilities.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751352307181269, "dur": 591, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Utilities\\PredictiveParser.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751352307180245, "dur": 2697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307184193, "dur": 500, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Views\\PendingChanges\\ChangeTreeViewItem.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751352307185010, "dur": 805, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Views\\Merge\\IIncomingChangesTab.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751352307186490, "dur": 1029, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Views\\Merge\\Gluon\\IncomingChangesTab.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751352307187520, "dur": 1320, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Views\\Merge\\Gluon\\IncomingChangesSelection.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751352307182942, "dur": 6148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307189091, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307189405, "dur": 483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307189888, "dur": 2638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307192527, "dur": 1043, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307193571, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307193707, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751352307194003, "dur": 1674, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307195694, "dur": 1243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1751352307196938, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307197289, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307197374, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307197824, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307198140, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307198758, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307198866, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307198927, "dur": 743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307199670, "dur": 1894, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307201565, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307201788, "dur": 848, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307202637, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307203234, "dur": 1188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307204423, "dur": 962, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307205442, "dur": 299418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751352307504861, "dur": 391815, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307139673, "dur": 31405, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307171084, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_970778ACBFA20360.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751352307171216, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307171359, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_47A61C91F1D08784.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751352307171555, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307171628, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_5E52B5CE937E0ED1.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751352307171736, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307171803, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_5AC43434136967AD.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751352307171908, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307172131, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3C810AF224C3A909.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751352307172313, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307172581, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B56095C3DA730DAC.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751352307172809, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307172952, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307173417, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307173567, "dur": 199, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1751352307173799, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307173895, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751352307173976, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307174233, "dur": 344, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1751352307174581, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307174666, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307174750, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307174849, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307174918, "dur": 519, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1751352307175439, "dur": 1243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307176683, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307177393, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307178267, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307179146, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307180270, "dur": 891, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsPropertyAttribute.cs"}}, {"pid": 12345, "tid": 22, "ts": 1751352307179751, "dur": 1472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307181470, "dur": 793, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Plugins\\Users\\InputUser.cs"}}, {"pid": 12345, "tid": 22, "ts": 1751352307181224, "dur": 4016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307185241, "dur": 1956, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307187198, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307188137, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307188666, "dur": 729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307189396, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307189892, "dur": 2622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307192514, "dur": 1080, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307193595, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307193728, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751352307194238, "dur": 1951, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307196198, "dur": 980, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751352307197179, "dur": 1487, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307198721, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307198793, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307198887, "dur": 762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307199650, "dur": 1909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307201560, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307201774, "dur": 877, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307202651, "dur": 578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307203229, "dur": 1130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307204415, "dur": 978, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307205393, "dur": 299483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751352307504877, "dur": 391819, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307139735, "dur": 31357, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307171098, "dur": 485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_D3BB5BABC9B6A39A.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751352307171585, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307171672, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_7BFAB029D4D7DE5C.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751352307171789, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307171885, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_7D7B641D38DA242D.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751352307172004, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307172106, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_783D7153E7AEE314.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751352307172216, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307172287, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_783D7153E7AEE314.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751352307172411, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0D29C9963CEF79A6.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751352307172627, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307172846, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0D29C9963CEF79A6.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751352307172931, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307173191, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1751352307173560, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307173935, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751352307174033, "dur": 728, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307174762, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751352307174842, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307174945, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1751352307175795, "dur": 623, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Utilities\\PreviewedBindings.cs"}}, {"pid": 12345, "tid": 23, "ts": 1751352307175086, "dur": 1390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307176477, "dur": 1020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307177498, "dur": 1152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307178652, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307180381, "dur": 1237, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsContext.cs"}}, {"pid": 12345, "tid": 23, "ts": 1751352307179777, "dur": 2132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307182527, "dur": 538, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Controls\\InputProcessor.cs"}}, {"pid": 12345, "tid": 23, "ts": 1751352307183618, "dur": 798, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Controls\\InputControlLayout.cs"}}, {"pid": 12345, "tid": 23, "ts": 1751352307185418, "dur": 618, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Controls\\DeltaControl.cs"}}, {"pid": 12345, "tid": 23, "ts": 1751352307181910, "dur": 4536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307186447, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307187037, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307187799, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307188416, "dur": 58, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307188538, "dur": 889, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307189427, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307189861, "dur": 2608, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307192475, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307192549, "dur": 1025, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307193574, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307193730, "dur": 1816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307195614, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307195687, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307195800, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307196033, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307196104, "dur": 483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307196587, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307196806, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307196985, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307197110, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307197290, "dur": 531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307197821, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307198102, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307198153, "dur": 643, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307198796, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307198888, "dur": 812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307199700, "dur": 1838, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307201624, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307201791, "dur": 839, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307202631, "dur": 587, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307203219, "dur": 1198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307204417, "dur": 974, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307205392, "dur": 299446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307504839, "dur": 260787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751352307765664, "dur": 129774, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 23, "ts": 1751352307765628, "dur": 129812, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 23, "ts": 1751352307895534, "dur": 1024, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 24, "ts": 1751352307138659, "dur": 32231, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307170895, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_989B89A6DC960046.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751352307171235, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307171339, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_711E053178EA7BF1.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751352307171535, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307171609, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_0660C742CA2CCB37.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751352307171716, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307171794, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_889FFFE959BEFFC2.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751352307172052, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307172146, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_9B1D775C88DF2080.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751352307172268, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307172343, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_FD8C0D7AD7503969.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751352307172573, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307172722, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D7EC0EF22CAF3612.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751352307172837, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307172938, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751352307173022, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307173294, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751352307173425, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307173614, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307173765, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307173876, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751352307174017, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307174188, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307174386, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307174472, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751352307174568, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307174724, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307174807, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751352307174898, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307174991, "dur": 873, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 24, "ts": 1751352307175865, "dur": 985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307176851, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307177468, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307178211, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307178882, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307179586, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307180365, "dur": 1098, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_Asset.cs"}}, {"pid": 12345, "tid": 24, "ts": 1751352307180179, "dur": 1648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307182392, "dur": 1319, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Controls\\TouchControl.cs"}}, {"pid": 12345, "tid": 24, "ts": 1751352307184293, "dur": 758, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Controls\\Processors\\ScaleVector3Processor.cs"}}, {"pid": 12345, "tid": 24, "ts": 1751352307181828, "dur": 4929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307186757, "dur": 517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307188163, "dur": 635, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.30f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 24, "ts": 1751352307187274, "dur": 1974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307189249, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307189443, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307189872, "dur": 2652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307192524, "dur": 1058, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307193583, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307193739, "dur": 1791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307195578, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307195752, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307196040, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307196121, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307196583, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307196817, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307196978, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307197101, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307197296, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307197836, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307198139, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307198802, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307198892, "dur": 765, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307199658, "dur": 1890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307201548, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307201811, "dur": 805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307202616, "dur": 607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307203224, "dur": 1146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307204465, "dur": 952, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307205417, "dur": 299433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751352307504850, "dur": 391802, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751352307911890, "dur": 3731, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 41616, "tid": 1585, "ts": 1751352307940490, "dur": 3313, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 41616, "tid": 1585, "ts": 1751352307943862, "dur": 3956, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 41616, "tid": 1585, "ts": 1751352307933821, "dur": 15708, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}
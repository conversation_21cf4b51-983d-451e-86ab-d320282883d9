{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 15948, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 15948, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 15948, "tid": 58975, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 15948, "tid": 58975, "ts": 1751441796057569, "dur": 1990, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 15948, "tid": 58975, "ts": 1751441796072253, "dur": 2927, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 15948, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 15948, "tid": 1, "ts": 1751441795163104, "dur": 9808, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 15948, "tid": 1, "ts": 1751441795172923, "dur": 72585, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 15948, "tid": 1, "ts": 1751441795245521, "dur": 93140, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 15948, "tid": 58975, "ts": 1751441796075191, "dur": 29, "ph": "X", "name": "", "args": {}}, {"pid": 15948, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795153647, "dur": 15243, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795168893, "dur": 864932, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795170810, "dur": 5201, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795176019, "dur": 3571, "ph": "X", "name": "ProcessMessages 20484", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795179596, "dur": 485, "ph": "X", "name": "ReadAsync 20484", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795180085, "dur": 28, "ph": "X", "name": "ProcessMessages 20489", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795180116, "dur": 60, "ph": "X", "name": "ReadAsync 20489", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795180182, "dur": 2, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795180187, "dur": 66, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795180259, "dur": 2, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795180263, "dur": 58, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795180327, "dur": 2, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795180331, "dur": 52, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795180389, "dur": 2, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795180393, "dur": 48, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795180445, "dur": 2, "ph": "X", "name": "ProcessMessages 198", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795180448, "dur": 43, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795180496, "dur": 2, "ph": "X", "name": "ProcessMessages 246", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795180499, "dur": 161, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795180664, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795180669, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795180729, "dur": 2, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795180732, "dur": 48, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795180784, "dur": 2, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795180788, "dur": 43, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795180835, "dur": 2, "ph": "X", "name": "ProcessMessages 169", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795180839, "dur": 62, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795180905, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795180907, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795180971, "dur": 2, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795180975, "dur": 49, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181028, "dur": 2, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181031, "dur": 51, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181086, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181089, "dur": 45, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181142, "dur": 2, "ph": "X", "name": "ProcessMessages 71", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181147, "dur": 60, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181211, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181215, "dur": 66, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181285, "dur": 2, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181288, "dur": 49, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181342, "dur": 2, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181345, "dur": 54, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181403, "dur": 2, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181408, "dur": 51, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181467, "dur": 3, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181471, "dur": 64, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181540, "dur": 2, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181545, "dur": 65, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181614, "dur": 5, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181623, "dur": 78, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181707, "dur": 6, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181716, "dur": 69, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181791, "dur": 2, "ph": "X", "name": "ProcessMessages 86", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181796, "dur": 69, "ph": "X", "name": "ReadAsync 86", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181877, "dur": 2, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181883, "dur": 66, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181959, "dur": 3, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795181965, "dur": 63, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182033, "dur": 2, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182036, "dur": 45, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182086, "dur": 1, "ph": "X", "name": "ProcessMessages 75", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182089, "dur": 58, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182151, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182154, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182210, "dur": 2, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182213, "dur": 56, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182274, "dur": 2, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182278, "dur": 52, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182335, "dur": 2, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182338, "dur": 51, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182393, "dur": 2, "ph": "X", "name": "ProcessMessages 243", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182396, "dur": 47, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182448, "dur": 2, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182452, "dur": 43, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182499, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182502, "dur": 44, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182550, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182553, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182613, "dur": 2, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182618, "dur": 66, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182688, "dur": 2, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182692, "dur": 48, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182745, "dur": 2, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182748, "dur": 47, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182804, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182807, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182871, "dur": 2, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182874, "dur": 48, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182926, "dur": 2, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182929, "dur": 45, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182979, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795182984, "dur": 54, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183042, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183045, "dur": 48, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183097, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183100, "dur": 50, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183155, "dur": 1, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183158, "dur": 52, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183214, "dur": 2, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183218, "dur": 55, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183277, "dur": 2, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183281, "dur": 48, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183333, "dur": 2, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183337, "dur": 44, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183385, "dur": 1, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183388, "dur": 53, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183449, "dur": 3, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183455, "dur": 64, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183523, "dur": 2, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183526, "dur": 52, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183583, "dur": 2, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183586, "dur": 66, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183656, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183658, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183716, "dur": 2, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183719, "dur": 47, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183769, "dur": 2, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183772, "dur": 42, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183819, "dur": 1, "ph": "X", "name": "ProcessMessages 178", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183822, "dur": 46, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183872, "dur": 1, "ph": "X", "name": "ProcessMessages 235", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183875, "dur": 59, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183939, "dur": 2, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183943, "dur": 51, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795183998, "dur": 2, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184001, "dur": 51, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184056, "dur": 2, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184060, "dur": 48, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184112, "dur": 2, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184116, "dur": 48, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184168, "dur": 2, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184171, "dur": 51, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184227, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184231, "dur": 48, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184283, "dur": 2, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184286, "dur": 51, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184342, "dur": 2, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184345, "dur": 43, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184394, "dur": 2, "ph": "X", "name": "ProcessMessages 243", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184397, "dur": 79, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184481, "dur": 2, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184484, "dur": 71, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184563, "dur": 9, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184575, "dur": 63, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184643, "dur": 2, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184646, "dur": 52, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184702, "dur": 2, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184706, "dur": 50, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184760, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184763, "dur": 68, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184838, "dur": 4, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184848, "dur": 72, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184925, "dur": 2, "ph": "X", "name": "ProcessMessages 750", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184928, "dur": 65, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795184998, "dur": 2, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185001, "dur": 54, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185064, "dur": 3, "ph": "X", "name": "ProcessMessages 113", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185070, "dur": 63, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185137, "dur": 2, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185140, "dur": 51, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185196, "dur": 2, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185199, "dur": 60, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185264, "dur": 2, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185268, "dur": 55, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185327, "dur": 2, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185330, "dur": 48, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185383, "dur": 2, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185388, "dur": 52, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185446, "dur": 2, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185451, "dur": 43, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185497, "dur": 1, "ph": "X", "name": "ProcessMessages 226", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185500, "dur": 59, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185562, "dur": 2, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185566, "dur": 47, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185616, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185619, "dur": 55, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185678, "dur": 2, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185682, "dur": 52, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185739, "dur": 2, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185743, "dur": 68, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185824, "dur": 4, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185830, "dur": 75, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185910, "dur": 2, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185914, "dur": 48, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185966, "dur": 2, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795185969, "dur": 48, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186021, "dur": 2, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186024, "dur": 56, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186087, "dur": 3, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186095, "dur": 67, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186166, "dur": 2, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186170, "dur": 44, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186218, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186221, "dur": 49, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186274, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186277, "dur": 61, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186344, "dur": 3, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186350, "dur": 73, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186427, "dur": 2, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186431, "dur": 54, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186493, "dur": 2, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186496, "dur": 49, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186550, "dur": 2, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186553, "dur": 61, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186621, "dur": 3, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186627, "dur": 53, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186684, "dur": 2, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186687, "dur": 50, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186741, "dur": 2, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186744, "dur": 47, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186795, "dur": 2, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186799, "dur": 64, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186870, "dur": 3, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186876, "dur": 67, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186947, "dur": 2, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795186950, "dur": 48, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187001, "dur": 2, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187005, "dur": 45, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187054, "dur": 2, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187057, "dur": 49, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187113, "dur": 3, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187119, "dur": 61, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187182, "dur": 2, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187186, "dur": 46, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187237, "dur": 2, "ph": "X", "name": "ProcessMessages 182", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187240, "dur": 47, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187291, "dur": 2, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187294, "dur": 43, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187341, "dur": 2, "ph": "X", "name": "ProcessMessages 227", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187345, "dur": 73, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187422, "dur": 2, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187425, "dur": 54, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187484, "dur": 2, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187487, "dur": 45, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187536, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187539, "dur": 44, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187587, "dur": 1, "ph": "X", "name": "ProcessMessages 214", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187590, "dur": 50, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187644, "dur": 2, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187647, "dur": 41, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187692, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187696, "dur": 49, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187748, "dur": 2, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187752, "dur": 44, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187801, "dur": 2, "ph": "X", "name": "ProcessMessages 183", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187804, "dur": 47, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187855, "dur": 2, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187858, "dur": 41, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187903, "dur": 2, "ph": "X", "name": "ProcessMessages 162", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795187907, "dur": 330, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188242, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188245, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188336, "dur": 2, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188340, "dur": 54, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188398, "dur": 2, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188402, "dur": 46, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188453, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188456, "dur": 46, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188507, "dur": 2, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188510, "dur": 46, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188560, "dur": 2, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188563, "dur": 39, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188606, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188608, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188656, "dur": 2, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188659, "dur": 45, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188708, "dur": 2, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188712, "dur": 45, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188761, "dur": 2, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188764, "dur": 47, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188815, "dur": 2, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188820, "dur": 54, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188879, "dur": 2, "ph": "X", "name": "ProcessMessages 214", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188882, "dur": 55, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188942, "dur": 2, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188945, "dur": 42, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188992, "dur": 2, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795188995, "dur": 60, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189063, "dur": 2, "ph": "X", "name": "ProcessMessages 130", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189068, "dur": 79, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189151, "dur": 2, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189155, "dur": 45, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189204, "dur": 8, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189213, "dur": 46, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189263, "dur": 2, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189266, "dur": 56, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189329, "dur": 4, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189335, "dur": 58, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189396, "dur": 2, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189401, "dur": 42, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189447, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189449, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189505, "dur": 2, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189508, "dur": 43, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189554, "dur": 2, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189558, "dur": 60, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189625, "dur": 3, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189631, "dur": 68, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189703, "dur": 2, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189706, "dur": 51, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189762, "dur": 1, "ph": "X", "name": "ProcessMessages 296", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189765, "dur": 50, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189818, "dur": 2, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189822, "dur": 50, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189879, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189883, "dur": 77, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189966, "dur": 2, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795189969, "dur": 53, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190026, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190029, "dur": 49, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190082, "dur": 2, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190085, "dur": 57, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190149, "dur": 3, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190155, "dur": 78, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190236, "dur": 2, "ph": "X", "name": "ProcessMessages 545", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190240, "dur": 49, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190293, "dur": 2, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190296, "dur": 44, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190344, "dur": 2, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190347, "dur": 60, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190414, "dur": 4, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190421, "dur": 69, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190494, "dur": 2, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190499, "dur": 50, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190553, "dur": 2, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190556, "dur": 44, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190604, "dur": 2, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190607, "dur": 55, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190669, "dur": 2, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190673, "dur": 73, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190750, "dur": 2, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190754, "dur": 44, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190802, "dur": 2, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190805, "dur": 48, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190857, "dur": 2, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190860, "dur": 58, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190924, "dur": 3, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795190930, "dur": 67, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191001, "dur": 2, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191005, "dur": 41, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191050, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191052, "dur": 45, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191101, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191103, "dur": 55, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191166, "dur": 3, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191171, "dur": 90, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191266, "dur": 2, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191270, "dur": 51, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191325, "dur": 2, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191328, "dur": 46, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191378, "dur": 2, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191381, "dur": 55, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191443, "dur": 3, "ph": "X", "name": "ProcessMessages 170", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191449, "dur": 60, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191513, "dur": 2, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191516, "dur": 48, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191568, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191571, "dur": 48, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191623, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191626, "dur": 58, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191691, "dur": 3, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191697, "dur": 67, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191768, "dur": 2, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191772, "dur": 49, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191825, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191828, "dur": 44, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191876, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191879, "dur": 72, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191959, "dur": 3, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795191964, "dur": 70, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192039, "dur": 2, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192042, "dur": 49, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192095, "dur": 2, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192098, "dur": 46, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192148, "dur": 2, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192152, "dur": 60, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192218, "dur": 3, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192224, "dur": 62, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192290, "dur": 2, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192294, "dur": 48, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192345, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192348, "dur": 46, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192398, "dur": 2, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192401, "dur": 67, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192474, "dur": 3, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192480, "dur": 64, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192548, "dur": 2, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192552, "dur": 49, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192605, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192608, "dur": 45, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192657, "dur": 2, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192660, "dur": 53, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192720, "dur": 3, "ph": "X", "name": "ProcessMessages 183", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192725, "dur": 57, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192787, "dur": 2, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192790, "dur": 76, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192870, "dur": 2, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192873, "dur": 46, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192924, "dur": 2, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192927, "dur": 58, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192992, "dur": 3, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795192998, "dur": 79, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193083, "dur": 2, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193086, "dur": 50, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193140, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193144, "dur": 49, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193197, "dur": 2, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193200, "dur": 65, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193272, "dur": 3, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193278, "dur": 67, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193348, "dur": 2, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193352, "dur": 57, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193413, "dur": 1, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193416, "dur": 42, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193462, "dur": 2, "ph": "X", "name": "ProcessMessages 163", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193465, "dur": 53, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193525, "dur": 2, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193530, "dur": 70, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193604, "dur": 2, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193607, "dur": 45, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193657, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193660, "dur": 46, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193710, "dur": 2, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193713, "dur": 57, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193777, "dur": 3, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193783, "dur": 74, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193861, "dur": 2, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193864, "dur": 46, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193914, "dur": 2, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193917, "dur": 45, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193966, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795193969, "dur": 64, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194040, "dur": 4, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194046, "dur": 66, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194117, "dur": 2, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194120, "dur": 50, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194174, "dur": 2, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194177, "dur": 54, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194235, "dur": 2, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194238, "dur": 56, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194301, "dur": 3, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194307, "dur": 46, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194357, "dur": 2, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194360, "dur": 49, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194413, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194415, "dur": 47, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194466, "dur": 2, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194469, "dur": 55, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194531, "dur": 3, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194536, "dur": 64, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194604, "dur": 2, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194608, "dur": 50, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194662, "dur": 2, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194665, "dur": 46, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194715, "dur": 2, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194718, "dur": 56, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194781, "dur": 3, "ph": "X", "name": "ProcessMessages 217", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194786, "dur": 62, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194852, "dur": 2, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194855, "dur": 50, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194909, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194912, "dur": 49, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194965, "dur": 2, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795194968, "dur": 56, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195031, "dur": 3, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195037, "dur": 67, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195108, "dur": 2, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195111, "dur": 44, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195160, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195163, "dur": 44, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195211, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195214, "dur": 52, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195272, "dur": 3, "ph": "X", "name": "ProcessMessages 49", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195278, "dur": 71, "ph": "X", "name": "ReadAsync 49", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195353, "dur": 2, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195356, "dur": 57, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195417, "dur": 2, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195420, "dur": 50, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195474, "dur": 2, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195477, "dur": 56, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195540, "dur": 3, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195546, "dur": 54, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195604, "dur": 2, "ph": "X", "name": "ProcessMessages 77", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195607, "dur": 52, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195663, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195666, "dur": 49, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195719, "dur": 2, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195722, "dur": 58, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195786, "dur": 4, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195792, "dur": 68, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195865, "dur": 2, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195868, "dur": 55, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195928, "dur": 2, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195931, "dur": 64, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795195999, "dur": 2, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795196002, "dur": 59, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795196068, "dur": 3, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795196073, "dur": 57, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795196135, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795196138, "dur": 48, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795196190, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795196193, "dur": 53, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795196251, "dur": 2, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795196255, "dur": 322, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795196583, "dur": 3, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795196589, "dur": 133, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795196727, "dur": 5, "ph": "X", "name": "ProcessMessages 1766", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795196733, "dur": 55, "ph": "X", "name": "ReadAsync 1766", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795196793, "dur": 2, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795196796, "dur": 48, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795196849, "dur": 2, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795196854, "dur": 60, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795196921, "dur": 3, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795196927, "dur": 65, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795196998, "dur": 2, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197002, "dur": 43, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197049, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197052, "dur": 46, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197102, "dur": 2, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197105, "dur": 58, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197170, "dur": 3, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197176, "dur": 59, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197239, "dur": 2, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197243, "dur": 44, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197292, "dur": 2, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197295, "dur": 54, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197353, "dur": 2, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197356, "dur": 55, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197418, "dur": 3, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197424, "dur": 57, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197484, "dur": 2, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197489, "dur": 51, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197544, "dur": 1, "ph": "X", "name": "ProcessMessages 213", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197547, "dur": 47, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197598, "dur": 2, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197601, "dur": 47, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197653, "dur": 2, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197656, "dur": 58, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197721, "dur": 3, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197731, "dur": 73, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197808, "dur": 2, "ph": "X", "name": "ProcessMessages 636", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197812, "dur": 48, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197864, "dur": 2, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197868, "dur": 49, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197921, "dur": 2, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197924, "dur": 56, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197987, "dur": 3, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795197993, "dur": 46, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198041, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198045, "dur": 47, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198096, "dur": 1, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198098, "dur": 49, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198152, "dur": 2, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198155, "dur": 45, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198204, "dur": 2, "ph": "X", "name": "ProcessMessages 113", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198207, "dur": 57, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198267, "dur": 2, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198271, "dur": 46, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198321, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198324, "dur": 50, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198378, "dur": 2, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198382, "dur": 51, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198438, "dur": 2, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198441, "dur": 51, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198496, "dur": 2, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198500, "dur": 37, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198542, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198545, "dur": 44, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198593, "dur": 2, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198596, "dur": 170, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198809, "dur": 2, "ph": "X", "name": "ProcessMessages 616", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198827, "dur": 78, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198913, "dur": 6, "ph": "X", "name": "ProcessMessages 1519", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198921, "dur": 52, "ph": "X", "name": "ReadAsync 1519", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198977, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795198982, "dur": 47, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199033, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199036, "dur": 49, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199090, "dur": 2, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199093, "dur": 47, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199144, "dur": 2, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199147, "dur": 38, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199187, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199189, "dur": 50, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199247, "dur": 3, "ph": "X", "name": "ProcessMessages 151", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199252, "dur": 72, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199328, "dur": 2, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199332, "dur": 46, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199382, "dur": 2, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199385, "dur": 52, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199441, "dur": 2, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199444, "dur": 59, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199511, "dur": 3, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199516, "dur": 64, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199585, "dur": 2, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199588, "dur": 54, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199646, "dur": 2, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199650, "dur": 44, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199698, "dur": 2, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199701, "dur": 66, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199774, "dur": 3, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199780, "dur": 70, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199854, "dur": 2, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199857, "dur": 77, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199940, "dur": 2, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795199945, "dur": 58, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200007, "dur": 2, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200011, "dur": 63, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200081, "dur": 4, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200087, "dur": 69, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200160, "dur": 2, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200163, "dur": 52, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200219, "dur": 2, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200222, "dur": 55, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200282, "dur": 2, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200285, "dur": 61, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200353, "dur": 4, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200359, "dur": 69, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200434, "dur": 2, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200437, "dur": 62, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200504, "dur": 2, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200507, "dur": 58, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200569, "dur": 2, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200572, "dur": 61, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200640, "dur": 4, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200647, "dur": 76, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200727, "dur": 2, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200731, "dur": 53, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200788, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200791, "dur": 58, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200857, "dur": 4, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200863, "dur": 85, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200955, "dur": 4, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795200961, "dur": 58, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201024, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201026, "dur": 49, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201079, "dur": 2, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201082, "dur": 53, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201140, "dur": 2, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201143, "dur": 80, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201230, "dur": 3, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201236, "dur": 78, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201319, "dur": 2, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201323, "dur": 54, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201381, "dur": 2, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201385, "dur": 42, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201431, "dur": 2, "ph": "X", "name": "ProcessMessages 177", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201434, "dur": 62, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201500, "dur": 2, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201504, "dur": 53, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201561, "dur": 2, "ph": "X", "name": "ProcessMessages 406", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201564, "dur": 51, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201619, "dur": 3, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201624, "dur": 53, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201681, "dur": 2, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201684, "dur": 51, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201739, "dur": 1, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201742, "dur": 46, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201792, "dur": 1, "ph": "X", "name": "ProcessMessages 167", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201795, "dur": 50, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201849, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201852, "dur": 57, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201913, "dur": 2, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201916, "dur": 53, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201973, "dur": 2, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795201978, "dur": 51, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795202033, "dur": 2, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795202036, "dur": 52, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795202092, "dur": 2, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795202095, "dur": 60, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795202162, "dur": 3, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795202169, "dur": 72, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795202246, "dur": 2, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795202250, "dur": 58, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795202315, "dur": 3, "ph": "X", "name": "ProcessMessages 157", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795202321, "dur": 67, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795202392, "dur": 2, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795202395, "dur": 69, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795202468, "dur": 2, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795202472, "dur": 60, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795202539, "dur": 4, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795202545, "dur": 73, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795202622, "dur": 2, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795202626, "dur": 45, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795202675, "dur": 2, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795202678, "dur": 46, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795202729, "dur": 2, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795202732, "dur": 55, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795202794, "dur": 2, "ph": "X", "name": "ProcessMessages 105", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795202798, "dur": 57, "ph": "X", "name": "ReadAsync 105", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795202861, "dur": 2, "ph": "X", "name": "ProcessMessages 131", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795202866, "dur": 411, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795203286, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795203290, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795203371, "dur": 676, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795204053, "dur": 113, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795204171, "dur": 12, "ph": "X", "name": "ProcessMessages 992", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795204185, "dur": 52, "ph": "X", "name": "ReadAsync 992", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795204242, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795204246, "dur": 58, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795204309, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795204313, "dur": 73, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795204395, "dur": 4, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795204401, "dur": 59, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795204467, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795204473, "dur": 74, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795204553, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795204559, "dur": 74, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795204638, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795204645, "dur": 67, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795204718, "dur": 4, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795204724, "dur": 64, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795204795, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795204801, "dur": 70, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795204877, "dur": 4, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795204883, "dur": 65, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795204957, "dur": 4, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795204965, "dur": 61, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795205033, "dur": 5, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795205041, "dur": 64, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795205112, "dur": 4, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795205122, "dur": 60, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795205191, "dur": 4, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795205200, "dur": 53, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795205260, "dur": 4, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795205268, "dur": 67, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795205341, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795205349, "dur": 68, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795205423, "dur": 5, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795205431, "dur": 57, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795205492, "dur": 2, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795205496, "dur": 49, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795205549, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795205553, "dur": 52, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795205614, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795205620, "dur": 68, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795205696, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795205703, "dur": 70, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795205780, "dur": 4, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795205790, "dur": 64, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795205861, "dur": 4, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795205871, "dur": 60, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795205938, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795205947, "dur": 63, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795206014, "dur": 5, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795206022, "dur": 62, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795206092, "dur": 4, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795206099, "dur": 66, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795206169, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795206173, "dur": 67, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795206249, "dur": 4, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795206257, "dur": 48, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795206309, "dur": 5, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795206317, "dur": 55, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795206378, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795206383, "dur": 68, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795206458, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795206465, "dur": 63, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795206534, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795206540, "dur": 64, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795206608, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795206613, "dur": 56, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795206674, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795206678, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795206739, "dur": 2, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795206744, "dur": 45, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795206794, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795206798, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795206858, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795206863, "dur": 10559, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795217430, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795217435, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795217510, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795217514, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795217572, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795217578, "dur": 59, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795217644, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795217650, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795217706, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795217709, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795217758, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795217761, "dur": 144, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795217910, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795217912, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795217966, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795217969, "dur": 1027, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795219002, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795219007, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795219061, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795219063, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795219113, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795219116, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795219161, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795219164, "dur": 158, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795219327, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795219331, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795219375, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795219378, "dur": 596, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795219982, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795219986, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795220056, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795220061, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795220127, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795220133, "dur": 304, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795220442, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795220450, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795220498, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795220503, "dur": 67, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795220576, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795220580, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795220644, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795220648, "dur": 107, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795220761, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795220765, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795220832, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795220837, "dur": 90, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795220932, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795220936, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795220993, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795220998, "dur": 85, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795221088, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795221091, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795221148, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795221152, "dur": 401, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795221558, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795221561, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795221616, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795221620, "dur": 231, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795221857, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795221865, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795221939, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795221943, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222007, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222013, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222076, "dur": 5, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222085, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222142, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222146, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222208, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222212, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222271, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222276, "dur": 46, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222329, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222334, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222404, "dur": 3, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222410, "dur": 50, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222466, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222471, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222527, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222531, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222593, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222597, "dur": 51, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222653, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222657, "dur": 123, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222788, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222792, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222855, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222862, "dur": 59, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222925, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222931, "dur": 54, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222991, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795222996, "dur": 53, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223057, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223062, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223119, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223124, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223184, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223187, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223249, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223257, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223327, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223332, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223396, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223402, "dur": 60, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223470, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223475, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223552, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223557, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223621, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223625, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223692, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223698, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223765, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223769, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223858, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223861, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223921, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223926, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223980, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795223983, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224063, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224066, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224124, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224128, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224190, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224195, "dur": 55, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224257, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224262, "dur": 57, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224325, "dur": 5, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224334, "dur": 73, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224415, "dur": 4, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224424, "dur": 51, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224479, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224483, "dur": 171, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224658, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224661, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224707, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224710, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224758, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224761, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224804, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224807, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224855, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224859, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224916, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224920, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224978, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795224982, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795225041, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795225046, "dur": 135, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795225187, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795225191, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795225250, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795225254, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795225298, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795225301, "dur": 137, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795225443, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795225447, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795225500, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795225505, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795225561, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795225565, "dur": 159, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795225728, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795225731, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795225780, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795225785, "dur": 48, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795225837, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795225840, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795225891, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795225895, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795225947, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795225950, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226001, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226004, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226045, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226047, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226090, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226093, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226137, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226140, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226183, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226186, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226248, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226251, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226299, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226302, "dur": 182, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226488, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226499, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226562, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226565, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226624, "dur": 5, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226633, "dur": 64, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226703, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226708, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226774, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226778, "dur": 51, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226833, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226837, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226888, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795226892, "dur": 198, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795227096, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795227101, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795227154, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795227159, "dur": 279, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795227447, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795227451, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795227516, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795227519, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795227572, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795227575, "dur": 200, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795227779, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795227783, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795227829, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795227832, "dur": 201, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795228041, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795228045, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795228107, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795228111, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795228159, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795228162, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795228204, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795228207, "dur": 1268, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795229480, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795229483, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795229542, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795229549, "dur": 54, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795229608, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795229612, "dur": 415, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795230033, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795230036, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795230106, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795230110, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795230168, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795230171, "dur": 202, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795230377, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795230380, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795230422, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795230425, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795230508, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795230511, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795230558, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795230561, "dur": 483, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795231049, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795231057, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795231127, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795231130, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795231186, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795231190, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795231233, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795231236, "dur": 512, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795231753, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795231756, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795231806, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795231810, "dur": 67, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795231882, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795231885, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795231935, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795231938, "dur": 154, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795232097, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795232099, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795232144, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795232148, "dur": 551, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795232704, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795232707, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795232758, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795232762, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795232822, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795232825, "dur": 91, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795232920, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795232924, "dur": 88, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795233019, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795233023, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795233082, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795233087, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795233139, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795233141, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795233186, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795233188, "dur": 177, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795233370, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795233372, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795233416, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795233419, "dur": 550, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795233974, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795233978, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795234040, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795234044, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795234098, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795234103, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795234171, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795234175, "dur": 298, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795234477, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795234481, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795234525, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795234528, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795234571, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795234574, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795234617, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795234620, "dur": 1000, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795235625, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795235628, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795235703, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795235708, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795235763, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795235766, "dur": 643, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795236414, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795236419, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795236473, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795236477, "dur": 2178, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795238664, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795238670, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795238735, "dur": 1009, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795239750, "dur": 540844, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795780604, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795780611, "dur": 198, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795780817, "dur": 10133, "ph": "X", "name": "ProcessMessages 8721", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795790961, "dur": 12142, "ph": "X", "name": "ReadAsync 8721", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795803110, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795803114, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795803179, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795803183, "dur": 682, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795803871, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795803874, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795803941, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795803945, "dur": 1305, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795805259, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795805264, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795805332, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441795805359, "dur": 202371, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441796007744, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441796007751, "dur": 99, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441796007859, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441796007869, "dur": 1311, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441796009188, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441796009193, "dur": 111, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441796009313, "dur": 67, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441796009388, "dur": 1962, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441796011358, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441796011363, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441796011452, "dur": 926, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441796012387, "dur": 21014, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 15948, "tid": 58975, "ts": 1751441796075225, "dur": 5321, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 15948, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 15948, "tid": 8589934592, "ts": 1751441795144760, "dur": 193955, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 15948, "tid": 8589934592, "ts": 1751441795338719, "dur": 8, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 15948, "tid": 8589934592, "ts": 1751441795338729, "dur": 2137, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 15948, "tid": 58975, "ts": 1751441796080552, "dur": 18, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 15948, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 15948, "tid": 4294967296, "ts": 1751441795099093, "dur": 937852, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 15948, "tid": 4294967296, "ts": 1751441795115705, "dur": 16557, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 15948, "tid": 4294967296, "ts": 1751441796037488, "dur": 12451, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 15948, "tid": 4294967296, "ts": 1751441796045088, "dur": 283, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 15948, "tid": 4294967296, "ts": 1751441796050253, "dur": 37, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 15948, "tid": 58975, "ts": 1751441796080575, "dur": 97, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751441795164241, "dur": 2966, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441795167223, "dur": 1110, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441795168418, "dur": 120, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751441795168538, "dur": 1212, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441795170561, "dur": 105, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B52CD1DFCA664AB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795172307, "dur": 2759, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795175079, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_00433992BAF13A73.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795175139, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6678507EC9682BC1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795175197, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_783D7153E7AEE314.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795175255, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C099976D06C71E3D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795177509, "dur": 3041, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795180592, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441795180650, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441795180741, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441795180798, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795181126, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441795181370, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441795181673, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441795181804, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795182008, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441795182082, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441795182238, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795182426, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795182614, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441795182731, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795183014, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441795183077, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441795183263, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795183458, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441795183560, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795183671, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795183909, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441795184120, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795184411, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795184521, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795184750, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441795184868, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441795184961, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795185108, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441795185320, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795185395, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795185551, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441795185665, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795185723, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441795185984, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441795186079, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441795186321, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441795186430, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795186575, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795186723, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441795186820, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795186891, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441795186951, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795187158, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441795187268, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795187330, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441795187581, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441795187708, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795188038, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441795188156, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441795188715, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441795188815, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441795189075, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441795189230, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441795189347, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795189540, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441795189911, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441795190085, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441795190364, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441795190493, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441795190814, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795190874, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441795191138, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441795191265, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441795191380, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795191501, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.EditorCoroutines.Editor.ref.dll_1315E0A407C70415.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795191672, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795191794, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795191931, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_8456852F39E72DEA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795192033, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441795192178, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795192432, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441795192607, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795192742, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_FC2D3B4057A989C4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795192855, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441795192949, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441795193256, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441795193393, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795193604, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441795193665, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795193757, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795193824, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795194018, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441795194242, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795194390, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441795194447, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441795194506, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795194628, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795194858, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441795195009, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441795195260, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441795195373, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795195616, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_0FE027DDD6F41767.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795195757, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795196250, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441795196392, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441795196535, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441795196606, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441795196673, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441795196734, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441795196800, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441795196857, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441795196914, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441795197062, "dur": 127, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441795197194, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441795197252, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441795197309, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441795197398, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Postprocessing.Runtime.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441795197647, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441795197762, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441795197904, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441795198182, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441795198495, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441795198665, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441795198831, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795198892, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795199093, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441795199296, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795199614, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441795199706, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Postprocessing.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441795199848, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Splines.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441795199905, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441795199995, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441795200060, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441795200253, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441795200398, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Cinemachine.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441795200458, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Splines.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441795200548, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441795200615, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441795200757, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Cinemachine.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441795200814, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Cinemachine.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441795200957, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441795201122, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441795201195, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441795201427, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-firstpass.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441795201484, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441795201600, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795201693, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795201915, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441795201974, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor-firstpass.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441795202087, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor-firstpass.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441795202328, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441795202383, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441795202497, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795202642, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441795202790, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441795202855, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441795203024, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441795169786, "dur": 33444, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441795203247, "dur": 806518, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441796009769, "dur": 1482, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441796011345, "dur": 53, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441796011580, "dur": 129, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441796011731, "dur": 9361, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751441795169350, "dur": 33910, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795203279, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_52F12B5AEAC5FC8F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441795203606, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795203769, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_129B2BEEEC662028.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441795204042, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795204119, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1830F8D8229EF0EB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441795204244, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795204311, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1830F8D8229EF0EB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441795204404, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B8F1FBD1DD727BB2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441795204508, "dur": 233, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795204763, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E0B83A434CA2B946.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441795204913, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E0B83A434CA2B946.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441795205124, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795205211, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_02726B6CC8A7DF56.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441795205309, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795205370, "dur": 321, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_02726B6CC8A7DF56.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441795205826, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751441795205974, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751441795206191, "dur": 604, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751441795206796, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751441795206913, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795207976, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795208785, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795209538, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795210298, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795211217, "dur": 794, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Core\\InputAxis.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751441795211046, "dur": 2216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795213263, "dur": 771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795214035, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795214795, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795215577, "dur": 615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795216192, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795217435, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795217904, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795218100, "dur": 1372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795219473, "dur": 978, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795220499, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795220652, "dur": 1651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795222304, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795222596, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795222756, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795222890, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795222988, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795223358, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795223581, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795223794, "dur": 488, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795224291, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795224594, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795224733, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795224867, "dur": 541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795225408, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795225667, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795225969, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795226267, "dur": 721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795226988, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795227136, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795227235, "dur": 689, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795227924, "dur": 602, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795228527, "dur": 1458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795229986, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795230526, "dur": 1022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795231548, "dur": 736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795232285, "dur": 917, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795233203, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795233514, "dur": 933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795234510, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795234641, "dur": 1519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795236161, "dur": 567032, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441795803195, "dur": 206586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795169401, "dur": 33880, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795203286, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BED6F64D074B3174.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751441795203725, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795203906, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_6C2CA7D176733A22.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751441795204024, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795204105, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_6C2CA7D176733A22.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751441795204366, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6678507EC9682BC1.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751441795204472, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795204736, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_54605A4C765A4A77.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751441795204875, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795204934, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_54605A4C765A4A77.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751441795205102, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795205198, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_39F0A7015032F01B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751441795205291, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795205368, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751441795205541, "dur": 363, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751441795206126, "dur": 453, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751441795206661, "dur": 265, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751441795206928, "dur": 1376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795208305, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795209030, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795209804, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795210643, "dur": 1333, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\inspectors\\CurvesOwner\\CurvesOwnerInspectorHelper.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751441795210568, "dur": 2438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795213007, "dur": 692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795213700, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795214981, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795215863, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795216786, "dur": 1161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795217948, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795218135, "dur": 1363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795219498, "dur": 955, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795220519, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795220650, "dur": 1651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795222308, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795222464, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795222607, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795222784, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795222888, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795222998, "dur": 304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795223315, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795223418, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751441795223812, "dur": 736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751441795224549, "dur": 1054, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795225651, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795225724, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795225918, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795226264, "dur": 703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795226968, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795227111, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795227256, "dur": 704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795227961, "dur": 567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795228528, "dur": 1424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795230000, "dur": 566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795230566, "dur": 1007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795231573, "dur": 703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795232277, "dur": 923, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795233200, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795233527, "dur": 925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795234452, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795234610, "dur": 1502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795236113, "dur": 567052, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441795803214, "dur": 264, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751441795803171, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751441795803529, "dur": 2150, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751441795805687, "dur": 204118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795169499, "dur": 33802, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795203306, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_10CDAFD975BC0AB7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751441795203609, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795203748, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_10CDAFD975BC0AB7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751441795203810, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_2BCC56A3C6071870.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751441795204020, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795204104, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_6C4C0BE13C75841D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751441795204383, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_97D91564DB667E63.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751441795204485, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795204706, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A99F700E950BD818.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751441795204782, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795204909, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_800370C755C131C9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751441795205011, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795205090, "dur": 323, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_800370C755C131C9.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751441795205479, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751441795205758, "dur": 312, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751441795206074, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795206162, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751441795206277, "dur": 367, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751441795206771, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751441795206899, "dur": 1199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795208099, "dur": 1390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795209490, "dur": 1104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795211233, "dur": 760, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Attributes\\TimelineShortcutAttribute.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751441795210595, "dur": 1842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795212438, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795213160, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795213851, "dur": 925, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795214777, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795215792, "dur": 1428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795217221, "dur": 716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795217937, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795218120, "dur": 1405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795219526, "dur": 942, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795220469, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795220596, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751441795221077, "dur": 1383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795222471, "dur": 1008, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751441795223480, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795223796, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795223981, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751441795224391, "dur": 1211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751441795225603, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795225915, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795225986, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795226259, "dur": 710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795226969, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795227109, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795227219, "dur": 707, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795227927, "dur": 592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795228575, "dur": 1389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795229965, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795230522, "dur": 1036, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795231558, "dur": 716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795232274, "dur": 911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795233186, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795233556, "dur": 933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795234490, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795234619, "dur": 1483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795236103, "dur": 748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795236851, "dur": 566385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441795803237, "dur": 206561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795169555, "dur": 33758, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795203318, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B014F0E06EB9C8B3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441795203658, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795203772, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B014F0E06EB9C8B3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441795203847, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46E217D19A6E01BD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441795204124, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795204292, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46E217D19A6E01BD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441795204418, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B5DB3F27CCAA63FE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441795204524, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795204771, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_BDFB8953D401853F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441795204857, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795204964, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_09E134ACBD96AA82.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441795205059, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795205121, "dur": 968, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_09E134ACBD96AA82.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441795206109, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441795206312, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795206379, "dur": 11568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751441795217949, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795218071, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795218145, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441795218335, "dur": 1031, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751441795219494, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795219562, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441795219758, "dur": 593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751441795220460, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795220584, "dur": 886, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441795221518, "dur": 2509, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751441795224028, "dur": 632, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795224717, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795224825, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441795225222, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795225457, "dur": 2347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751441795227805, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795227925, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795227999, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441795228214, "dur": 2154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751441795230370, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795230514, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795230594, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441795230947, "dur": 1167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751441795232115, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795232234, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795232316, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441795232538, "dur": 813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751441795233352, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795233528, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795233600, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441795233805, "dur": 544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751441795234474, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795234599, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441795235006, "dur": 954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751441795235961, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795236097, "dur": 1232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751441795238474, "dur": 204, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795780896, "dur": 50, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441795240023, "dur": 550631, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751441795803878, "dur": 205, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751441795803161, "dur": 1065, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751441795804316, "dur": 205502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795169606, "dur": 33726, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795203341, "dur": 566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_438C72DDFEF65815.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751441795203909, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795203982, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_438C72DDFEF65815.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751441795204071, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F8AA39E8D5CE00CF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751441795204194, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795204275, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F8AA39E8D5CE00CF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751441795204427, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E522F23A96C2507B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751441795204771, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795204958, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D7EC0EF22CAF3612.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751441795205055, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795205192, "dur": 329, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751441795205644, "dur": 387, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751441795206034, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751441795206184, "dur": 268, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751441795206455, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751441795206548, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795206605, "dur": 445, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751441795207051, "dur": 1299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795208359, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795209084, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795209814, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795211220, "dur": 836, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Manipulators\\AddDelete\\AddDeleteItemModeReplace.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751441795210532, "dur": 1524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795212057, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795212869, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795213626, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795214308, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795215367, "dur": 1167, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework\\UnityEditor.TestRunner\\TestLaunchers\\RuntimeTestLauncherBase.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751441795215367, "dur": 1508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795216875, "dur": 1053, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795217929, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795218117, "dur": 1373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795219491, "dur": 999, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795220491, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795220626, "dur": 659, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751441795221286, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795221368, "dur": 2692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751441795224062, "dur": 630, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795224723, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795224793, "dur": 94, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_A558B9E65539D7D0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751441795224891, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751441795225180, "dur": 1011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751441795226192, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795226324, "dur": 665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795226989, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795227133, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795227260, "dur": 692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795227952, "dur": 594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795228547, "dur": 1433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795229980, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795230534, "dur": 1010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795231545, "dur": 713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795232259, "dur": 946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795233205, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795233559, "dur": 924, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795234483, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795234634, "dur": 1519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795236154, "dur": 567035, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441795803191, "dur": 206557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795169650, "dur": 33718, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795203372, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_7A1FD51B51344812.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751441795203735, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795203901, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751441795204013, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795204088, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_C287EB2677BA7F93.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751441795204342, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_00433992BAF13A73.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751441795204456, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795204550, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_00433992BAF13A73.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751441795204662, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C5419324AD6BC088.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751441795204884, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795204957, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C5419324AD6BC088.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751441795205202, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751441795205358, "dur": 278, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751441795205639, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751441795205752, "dur": 408, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751441795206346, "dur": 563, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751441795206911, "dur": 1035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795207947, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795209003, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795209782, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795211208, "dur": 787, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Manipulators\\Sequence\\TrackZoom.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751441795210519, "dur": 1476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795211996, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795213317, "dur": 793, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Cloning\\Cloners\\ReflectedCloner.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751441795212677, "dur": 1515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795214193, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795214956, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795215669, "dur": 1005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795216675, "dur": 1217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795217893, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795218109, "dur": 1367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795219477, "dur": 1029, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795220506, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795220647, "dur": 1658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795222305, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795222585, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795222746, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795222881, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795222992, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795223054, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795223363, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795223576, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795223804, "dur": 726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795224531, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795224590, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795224760, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795224848, "dur": 548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795225396, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795225658, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795225930, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795226272, "dur": 731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795227004, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795227151, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795227255, "dur": 712, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795227967, "dur": 572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795228539, "dur": 1411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795230004, "dur": 555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795230559, "dur": 1031, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795231590, "dur": 649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795232240, "dur": 939, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795233180, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795233510, "dur": 940, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795234503, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795234626, "dur": 1485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795236111, "dur": 567075, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441795803187, "dur": 206565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795169700, "dur": 33679, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795203385, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_00CD99E311DC019E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751441795203614, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795203700, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_00CD99E311DC019E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751441795203786, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_7F1C5DFD0AD39F77.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751441795204001, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795204083, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_0660C742CA2CCB37.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751441795204201, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795204301, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_173731A5F5D7E041.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751441795204440, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795204518, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_31922632D84128F5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751441795204808, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_31922632D84128F5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751441795204982, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_42EBD154127454A5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751441795205135, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751441795205246, "dur": 481, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751441795205808, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751441795205947, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751441795206040, "dur": 452, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751441795206494, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751441795206729, "dur": 233, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751441795206964, "dur": 1714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795208678, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795209378, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795210080, "dur": 689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795211233, "dur": 789, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.splines\\Editor\\Tools\\SplineTool.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751441795210770, "dur": 1859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795212668, "dur": 693, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\AssemblyQualifiedNameParser\\ParsedAssemblyQualifiedName.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751441795212630, "dur": 1399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795214030, "dur": 832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795214863, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795215655, "dur": 616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795216272, "dur": 55, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795216519, "dur": 1375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795217897, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795218090, "dur": 1389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795219480, "dur": 1035, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795220515, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795220652, "dur": 500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751441795221208, "dur": 894, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751441795222103, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795222309, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795222752, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795222996, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795223352, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795223565, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795223799, "dur": 747, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795224546, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795224606, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795224743, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795224863, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795225440, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795225664, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795225921, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795226252, "dur": 719, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795226971, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795227114, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795227231, "dur": 706, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795227938, "dur": 594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795228532, "dur": 1422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795229955, "dur": 548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795230555, "dur": 1004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795231560, "dur": 706, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795232266, "dur": 909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795233176, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795233549, "dur": 917, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795234466, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795234638, "dur": 1486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795236124, "dur": 567129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441795803254, "dur": 206569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795169793, "dur": 33600, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795203398, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_BF1A247579C7383B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441795203926, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795203978, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_BF1A247579C7383B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441795204056, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_DCB00145011AACBC.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441795204316, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795204410, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_9B1D775C88DF2080.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441795204777, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795204902, "dur": 417, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_9B1D775C88DF2080.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441795205332, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751441795205441, "dur": 266, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751441795205736, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751441795205975, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795206096, "dur": 375, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751441795206473, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751441795206716, "dur": 173, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751441795206891, "dur": 1087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795207979, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795208699, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795209404, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795210097, "dur": 745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795211228, "dur": 762, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.splines\\Editor\\Components\\SplineAnimateEditor.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751441795210843, "dur": 1448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795212292, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795213120, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795213901, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795214584, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795215353, "dur": 608, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795215962, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795216540, "dur": 1390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795217931, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795218110, "dur": 1375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795219486, "dur": 974, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795220463, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795220596, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441795220870, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795220974, "dur": 780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751441795221755, "dur": 1086, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795222886, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795223007, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441795223312, "dur": 840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751441795224153, "dur": 551, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795224729, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795224852, "dur": 547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795225399, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795225661, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795225910, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795226168, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795226266, "dur": 708, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795226974, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795227106, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795227270, "dur": 704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795227975, "dur": 597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795228573, "dur": 1401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795229975, "dur": 577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795230552, "dur": 1024, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795231576, "dur": 688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795232264, "dur": 931, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795233196, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795233520, "dur": 942, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795234463, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795234641, "dur": 1480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795236122, "dur": 567126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441795803249, "dur": 206522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795169839, "dur": 33569, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795203414, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_80B8935D38233098.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441795203605, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795203744, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_970778ACBFA20360.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441795203945, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795204030, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_4E756196A062909D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441795204172, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_42AEDBADC03A3022.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441795204259, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795204340, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_42AEDBADC03A3022.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441795204465, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_71CC72797B2E953C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441795204721, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795204788, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_71CC72797B2E953C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441795204891, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E671E5897665917D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441795205072, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795205183, "dur": 247, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E671E5897665917D.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441795205481, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751441795205675, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751441795205815, "dur": 307, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1751441795206282, "dur": 568, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1751441795206852, "dur": 1168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795208020, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795208714, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795209445, "dur": 765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795210211, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795211226, "dur": 752, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Unity\\MacroScriptableObject.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751441795211194, "dur": 1419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795212614, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795213343, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795214074, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795215377, "dur": 1168, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\SceneView\\DrawSceneOperations.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751441795214711, "dur": 2011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795216722, "dur": 1168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795217891, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795218071, "dur": 1399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795219470, "dur": 988, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795220459, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795220602, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441795221055, "dur": 1525, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751441795222581, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795222793, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441795223250, "dur": 1347, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751441795224598, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795224736, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795224790, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_D0BA3615D322EF6C.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441795224873, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795225406, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795225693, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795225923, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795226296, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795226435, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795227000, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795227148, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795227249, "dur": 697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795227946, "dur": 591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795228537, "dur": 1440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795229977, "dur": 568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795230546, "dur": 1003, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795231549, "dur": 680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795232229, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795232324, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441795232534, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751441795233176, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795233249, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795233545, "dur": 919, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795234465, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795234631, "dur": 1464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795236101, "dur": 652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751441795236754, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795236888, "dur": 566334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441795803223, "dur": 206538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795169928, "dur": 33497, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795203430, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_16A54265357DDF1F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441795203607, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795203813, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_47A61C91F1D08784.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441795204008, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795204112, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_7BFAB029D4D7DE5C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441795204225, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795204314, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_6E64691D4ACC974A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441795204485, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795204752, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_67F30B299A5D5154.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441795204852, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795205037, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751441795205196, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_696347C4732CD601.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441795205292, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795205550, "dur": 466, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1751441795206019, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751441795206154, "dur": 169, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751441795206610, "dur": 389, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1751441795207001, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795208102, "dur": 703, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795208806, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795209517, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795210156, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795211204, "dur": 716, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing\\PostProcessing\\Editor\\PostProcessEffectBaseEditor.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751441795210919, "dur": 1361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795212281, "dur": 710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795212992, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795213683, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795214400, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795215062, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795215955, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795216550, "dur": 1370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795217920, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795218124, "dur": 1376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795219500, "dur": 976, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795220480, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795220588, "dur": 573, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441795221216, "dur": 1841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751441795223058, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795223327, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795223411, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441795223797, "dur": 670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751441795224468, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795224598, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795224667, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441795224922, "dur": 1299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795226251, "dur": 817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751441795227069, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795227220, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795227287, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441795227528, "dur": 880, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751441795228409, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795228542, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795228619, "dur": 1362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795229982, "dur": 594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795230576, "dur": 966, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795231543, "dur": 694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795232238, "dur": 961, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795233200, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795233534, "dur": 945, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795234479, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795234624, "dur": 1482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795236107, "dur": 107855, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795343964, "dur": 459279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441795803244, "dur": 206614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795169965, "dur": 33474, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795203444, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FCC5A8FB58ECD1D2.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441795203605, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795203711, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B52CD1DFCA664AB3.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441795203903, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795203972, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B52CD1DFCA664AB3.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441795204027, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E009CCFB4A5E9FFB.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441795204133, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795204203, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_921E3B2D436CDAE7.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441795204294, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795204377, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C099976D06C71E3D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441795204462, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795204541, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C099976D06C71E3D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441795204646, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_963BCDCD478EB63D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441795204917, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795204980, "dur": 843, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_963BCDCD478EB63D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441795205958, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751441795206098, "dur": 199, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751441795206432, "dur": 303, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751441795206808, "dur": 949, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795207757, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795208591, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795209343, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795210083, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795211201, "dur": 730, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.splines\\Editor\\GUI\\Editors\\EmbeddedSplineDataPropertyDrawer.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751441795212184, "dur": 571, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.splines\\Editor\\Core\\CopyPaste.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751441795210841, "dur": 1973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795212815, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795213521, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795214213, "dur": 626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795214839, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795215671, "dur": 1632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795217304, "dur": 603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795217908, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795218126, "dur": 1381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795219508, "dur": 962, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795220471, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795220587, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441795220799, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795220865, "dur": 1410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751441795222276, "dur": 1242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795223570, "dur": 529, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795224110, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441795224367, "dur": 903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751441795225271, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795225397, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795225648, "dur": 546, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795226248, "dur": 733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795226982, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795227142, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795227263, "dur": 707, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795227970, "dur": 584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795228555, "dur": 1438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795229993, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795230512, "dur": 1034, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795231547, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795232278, "dur": 930, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795233208, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795233523, "dur": 948, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795234471, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795234643, "dur": 1499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795236143, "dur": 567098, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441795803242, "dur": 206516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795170034, "dur": 33420, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795203460, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_493844D967613FA0.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441795203613, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795203728, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D447C5E6C0383BF9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441795203940, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795204000, "dur": 206, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D447C5E6C0383BF9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441795204210, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CB3FD07F3B098D2B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441795204291, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795204373, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_783D7153E7AEE314.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441795204793, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_783D7153E7AEE314.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441795204900, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B47D5ED0AB2E73A1.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441795204985, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795205054, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B47D5ED0AB2E73A1.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441795205250, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751441795205470, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751441795205619, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751441795205848, "dur": 364, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751441795206242, "dur": 743, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751441795206987, "dur": 1170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795208441, "dur": 2235, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.State\\INesterStateTransition.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751441795208158, "dur": 3053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795211212, "dur": 789, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Serialization\\DoNotSerializeAttribute.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751441795211211, "dur": 1466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795212678, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795213374, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795214758, "dur": 1779, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Views\\Merge\\DrawMergeOverview.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751441795214234, "dur": 2500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795216735, "dur": 1180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795217916, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795218112, "dur": 1383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795219495, "dur": 1024, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795220520, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795220608, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441795221198, "dur": 1447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751441795222646, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795222749, "dur": 565, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795223325, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795223612, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795223811, "dur": 741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795224552, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795224606, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795224746, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795224831, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441795225212, "dur": 908, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751441795226121, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795226249, "dur": 427, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795226686, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795226978, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795227145, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795227252, "dur": 680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795227932, "dur": 584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795228567, "dur": 1405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795229973, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795230524, "dur": 1017, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795231541, "dur": 706, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795232247, "dur": 942, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795233190, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795233508, "dur": 946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795234454, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795234604, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441795235034, "dur": 1075, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795236110, "dur": 567123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441795803234, "dur": 206534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795170084, "dur": 33385, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795203475, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_989B89A6DC960046.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751441795203621, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795203749, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_D3BB5BABC9B6A39A.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751441795204030, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795204096, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_5E52B5CE937E0ED1.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751441795204238, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795204308, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_5E52B5CE937E0ED1.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751441795204447, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FF5FCB0F465F7311.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751441795204553, "dur": 220, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795204797, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B56095C3DA730DAC.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751441795204903, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795205051, "dur": 210, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1751441795205265, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751441795205530, "dur": 325, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1751441795205908, "dur": 457, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751441795206423, "dur": 288, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751441795206775, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751441795206880, "dur": 1154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795208035, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795208758, "dur": 742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795209500, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795210226, "dur": 753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795211234, "dur": 754, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Deprecated\\CinemachinePathBase.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751441795210980, "dur": 1535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795212515, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795213271, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795213962, "dur": 1047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795215010, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795215754, "dur": 1523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795217277, "dur": 612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795217890, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795218079, "dur": 1388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795219524, "dur": 971, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795220495, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795220657, "dur": 1642, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795222352, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795222640, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795222707, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795222766, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795222882, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795222995, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795223326, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795223619, "dur": 186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795223805, "dur": 729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795224535, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795224601, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795224783, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795224844, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795225395, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795225647, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795225924, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795226299, "dur": 699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795226998, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795227140, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795227266, "dur": 669, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795227936, "dur": 588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795228525, "dur": 1434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795229959, "dur": 547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795230580, "dur": 953, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795231534, "dur": 692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795232286, "dur": 901, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795233188, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795233516, "dur": 971, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795234487, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795234612, "dur": 1486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795236104, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795236168, "dur": 567009, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441795803178, "dur": 206656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795170201, "dur": 33284, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795203491, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_B01BEAEDCAC9F962.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441795203626, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795203693, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_B01BEAEDCAC9F962.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441795203857, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_4FB16FC3A796B887.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441795204061, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795204145, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_39C615B7DD834A4A.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441795204247, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795204328, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8B525A02B9DF9C7E.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441795204453, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795204544, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_DC79D8DB49767D4F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441795204783, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795204853, "dur": 172, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_DC79D8DB49767D4F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441795205065, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441795205122, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795205194, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441795205526, "dur": 718, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751441795206278, "dur": 564, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1751441795206844, "dur": 1038, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795207883, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795208712, "dur": 783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795209496, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795210333, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795211223, "dur": 775, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Components\\CinemachineSplineDollyLookAtTargets.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751441795211071, "dur": 1831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795212903, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795213637, "dur": 835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795214472, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795215206, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795215859, "dur": 1487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795217349, "dur": 563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795217912, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795218088, "dur": 1393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795219484, "dur": 1004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795220489, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795220635, "dur": 1675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795222311, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795222598, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795222748, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795222933, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795222999, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795223302, "dur": 481, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795223810, "dur": 716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795224598, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795224738, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795224838, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795225390, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795225651, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795225919, "dur": 335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795226254, "dur": 731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795226985, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795227158, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795227247, "dur": 717, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795227965, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795228536, "dur": 1442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795229978, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795230548, "dur": 1004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795231553, "dur": 703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795232257, "dur": 913, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795233193, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795233256, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795233569, "dur": 908, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795234478, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795234646, "dur": 1488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795236135, "dur": 567080, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441795803216, "dur": 206628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795170239, "dur": 33262, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795203508, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_44286934693FF148.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441795203634, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795203757, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_DC7672C0573B7C85.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441795203992, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795204076, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_051DE14E9379244A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441795204207, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795204306, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_7D7B641D38DA242D.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441795204436, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795204528, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_20E0F9318BFD3B5B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441795204812, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795204907, "dur": 591, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_20E0F9318BFD3B5B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441795205641, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Runtime.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751441795205825, "dur": 794, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1751441795206742, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751441795206823, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795207872, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795208485, "dur": 669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795209155, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795209846, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795211207, "dur": 750, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\inspectors\\EditorClip.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751441795210537, "dur": 1420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795211958, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795212871, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795213612, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795214519, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795215247, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795215953, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795216670, "dur": 1215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795217940, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795218095, "dur": 1416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795219511, "dur": 955, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795220467, "dur": 186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795220654, "dur": 1652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795222307, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795222601, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795222778, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795222894, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795223009, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795223320, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795223574, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795223802, "dur": 743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795224545, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795224612, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795224747, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795224853, "dur": 553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795225407, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795225662, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795225926, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795226263, "dur": 716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795226980, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795227103, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795227221, "dur": 695, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795227963, "dur": 595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795228559, "dur": 1403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795229963, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795230514, "dur": 1013, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795231575, "dur": 705, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795232280, "dur": 913, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795233193, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795233538, "dur": 955, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795234493, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795234651, "dur": 1453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795236105, "dur": 105242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795343550, "dur": 394, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 15, "ts": 1751441795341349, "dur": 2599, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795343949, "dur": 459276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441795803226, "dur": 206635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795170301, "dur": 33220, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795203527, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_1C55A2A4A7EC33E2.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441795203700, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795203768, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_1C55A2A4A7EC33E2.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441795203830, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_C2E93A20F98E8229.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441795204038, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795204138, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B1475218F12198D8.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441795204256, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795204339, "dur": 109, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B1475218F12198D8.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441795204457, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F275A7F2C1FA929A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441795204750, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795204850, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9F68AD12AC89760A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441795204953, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795205048, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_69199FEE963F7F27.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441795205149, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795205239, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441795205364, "dur": 290, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441795205741, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1751441795205916, "dur": 488, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751441795206475, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751441795206559, "dur": 699, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751441795207259, "dur": 1151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795208411, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795209159, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795209904, "dur": 733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795211209, "dur": 769, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Actions\\MarkerActions.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751441795210637, "dur": 1545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795212183, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795213003, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795213712, "dur": 849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795214751, "dur": 796, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\UI\\GuiEnabled.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751441795214562, "dur": 1387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795215950, "dur": 584, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795216534, "dur": 1367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795217902, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795218086, "dur": 1418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795219504, "dur": 1007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795220514, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795220643, "dur": 1681, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795222325, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795222588, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795222764, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795222886, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795223000, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795223373, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795223568, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795223794, "dur": 746, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795224540, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795224600, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795224735, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795224836, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441795225291, "dur": 1141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751441795226434, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795226576, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795227008, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795227226, "dur": 692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795227973, "dur": 591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795228564, "dur": 1393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795229957, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795230515, "dur": 1022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795231538, "dur": 697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795232235, "dur": 959, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795233194, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795233525, "dur": 950, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795234475, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795234613, "dur": 1501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795236115, "dur": 567053, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795803169, "dur": 1126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441795804338, "dur": 205453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795170361, "dur": 33178, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795203545, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5543A9502BBA10BA.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751441795203711, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795203948, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_74513773BD43745E.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751441795204128, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795204239, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_74513773BD43745E.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751441795204398, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3C810AF224C3A909.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751441795204803, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795204962, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3C810AF224C3A909.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751441795205128, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_3AA3364A40DB9F8B.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751441795205187, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795205401, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751441795205645, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751441795205760, "dur": 573, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751441795206414, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751441795206504, "dur": 323, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751441795206829, "dur": 995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795207824, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795208686, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795209365, "dur": 768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795210133, "dur": 830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795211217, "dur": 800, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Impulse\\CinemachineFixedSignal.cs"}}, {"pid": 12345, "tid": 17, "ts": 1751441795210963, "dur": 1824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795212788, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795213514, "dur": 699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795214214, "dur": 752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795214967, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795215859, "dur": 1175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795217035, "dur": 888, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795217924, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795218106, "dur": 1395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795219502, "dur": 1001, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795220504, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795220650, "dur": 1671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795222322, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795222586, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795222747, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795222884, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795222992, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795223386, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795223580, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795223792, "dur": 736, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795224535, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795224620, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795224775, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1751441795224855, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795225393, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795225657, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795225913, "dur": 344, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795226257, "dur": 715, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795226973, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795227123, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795227244, "dur": 703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795227948, "dur": 617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795228566, "dur": 1405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795229971, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795230510, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795230601, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751441795230812, "dur": 608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751441795231420, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795231536, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795231614, "dur": 627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795232242, "dur": 935, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795233178, "dur": 326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795233504, "dur": 956, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795234461, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795234609, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751441795234910, "dur": 1197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795236108, "dur": 567063, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441795803172, "dur": 206583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795170431, "dur": 33125, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795203562, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_9DC68FB86218AF77.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751441795203705, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795203794, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_711E053178EA7BF1.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751441795204008, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795204132, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_00B5CEDD1ED5F4B3.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751441795204234, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795204311, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_ED94C9E717A89F9F.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751441795204734, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795204801, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_ED94C9E717A89F9F.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751441795204918, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3EA5AD5E9A933796.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751441795205014, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795205115, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_A3850CF6800E4193.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751441795205173, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795205317, "dur": 593, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751441795205963, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751441795206073, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1751441795206215, "dur": 339, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1751441795206609, "dur": 336, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751441795206947, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795207823, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795208604, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795209300, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795210018, "dur": 772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795211221, "dur": 750, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.splines\\Editor\\GUI\\SplineGUIUtility.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751441795210791, "dur": 1439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795212682, "dur": 677, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Pooling\\IPoolable.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751441795213544, "dur": 514, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Platforms\\AotIncompatibleAttribute.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751441795212231, "dur": 1898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795214129, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795215070, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795215878, "dur": 595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795216508, "dur": 1397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795217905, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795218122, "dur": 1365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795219488, "dur": 998, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795220487, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795220633, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751441795221052, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795221189, "dur": 1277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751441795222467, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795222588, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795222659, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795222744, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795222893, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795222992, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795223212, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795223334, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795223562, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795223801, "dur": 731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795224533, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795224598, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795224660, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795224776, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 18, "ts": 1751441795224898, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795225402, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795225649, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795225916, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795226294, "dur": 726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795227020, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795227121, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795227216, "dur": 717, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795227934, "dur": 647, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795228581, "dur": 1389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795229970, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795230544, "dur": 1034, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795231579, "dur": 690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795232269, "dur": 914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795233184, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795233563, "dur": 910, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795234473, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795234615, "dur": 1505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795236120, "dur": 567109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441795803231, "dur": 206599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795170472, "dur": 33108, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795203588, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8ECA4E4329F1BADB.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751441795203927, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795204004, "dur": 217, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8ECA4E4329F1BADB.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751441795204228, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_889FFFE959BEFFC2.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751441795204329, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795204439, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_805FFE03DDD45823.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751441795204537, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795204789, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D7BFC8363EAC4767.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751441795204884, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795205035, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9428A5C8BBBE8A51.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751441795205203, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795205372, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795205629, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751441795205754, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795205809, "dur": 726, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751441795206541, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795206653, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795206773, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1751441795206839, "dur": 1344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795208432, "dur": 1343, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Flow\\Plugin\\Changelogs\\Changelog_1_4_2.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751441795208184, "dur": 2005, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795210189, "dur": 901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795211232, "dur": 771, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Behaviours\\CinemachinePixelPerfect.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751441795211091, "dur": 1372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795212463, "dur": 768, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795213232, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795214394, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795215039, "dur": 463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795215503, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795216148, "dur": 1379, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795217528, "dur": 359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795217888, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795218073, "dur": 1392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795219514, "dur": 970, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795220484, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795220626, "dur": 680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751441795221361, "dur": 1269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1751441795222631, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795222731, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795222797, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795222939, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795222990, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795223316, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795223571, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795223795, "dur": 751, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795224547, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795224609, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795224734, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795224840, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795225398, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795225660, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795225912, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795226256, "dur": 719, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795226976, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795227120, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795227215, "dur": 713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795227929, "dur": 614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795228543, "dur": 1444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795229988, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795230520, "dur": 1034, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795231555, "dur": 715, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795232271, "dur": 926, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795233197, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795233512, "dur": 964, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795234476, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795234635, "dur": 1509, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795236144, "dur": 567030, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441795803175, "dur": 206702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795170540, "dur": 33054, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795203600, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_F4165E534DD27516.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441795203750, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795203980, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_F24F3CD4D4D128AB.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441795204168, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795204319, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_5E37763FF8B95A1F.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441795204464, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795204739, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_5E37763FF8B95A1F.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441795204841, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_A7B3B5FA99331E00.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441795204944, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795205007, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_A7B3B5FA99331E00.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441795205210, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795205284, "dur": 283, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751441795205677, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1751441795205979, "dur": 334, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751441795206407, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751441795206519, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751441795206632, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751441795206730, "dur": 437, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751441795207169, "dur": 1420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795208591, "dur": 1232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795209824, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795211213, "dur": 789, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Variables\\VariableKindAttribute.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751441795211103, "dur": 1526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795212629, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795213391, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795214188, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795214859, "dur": 864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795215723, "dur": 1841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795217565, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795217918, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795218105, "dur": 1372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795219478, "dur": 1014, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795220493, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795220656, "dur": 1660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795222317, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795222590, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795222773, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795222892, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795222994, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795223366, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795223569, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795223790, "dur": 748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795224539, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795224597, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795224835, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441795225093, "dur": 663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751441795225757, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795225945, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795226280, "dur": 683, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795227018, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795227160, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795227227, "dur": 721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795227949, "dur": 581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795228530, "dur": 1438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795229969, "dur": 549, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795230519, "dur": 1010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795231580, "dur": 668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795232249, "dur": 919, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795233214, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795233526, "dur": 930, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795234457, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795234600, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441795235051, "dur": 1065, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795236116, "dur": 567044, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441795803203, "dur": 204740, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751441795803165, "dur": 204781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751441796008113, "dur": 1509, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 21, "ts": 1751441795170602, "dur": 33001, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795203608, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_921345D5C92C6DDE.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751441795203740, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795203918, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_61E5A1A2114B6332.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751441795204030, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795204102, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_61E5A1A2114B6332.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751441795204261, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_5AC43434136967AD.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751441795204435, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795204549, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_5AC43434136967AD.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751441795204678, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0D29C9963CEF79A6.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751441795204902, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795205040, "dur": 555, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1751441795205650, "dur": 305, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751441795205957, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751441795206342, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751441795206535, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751441795206625, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor-firstpass.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751441795206833, "dur": 1234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795208068, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795208782, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795209510, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795210188, "dur": 740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795211206, "dur": 760, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Timeline\\CinemachineTrack.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751441795210928, "dur": 1430, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795212358, "dur": 986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795213345, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795214418, "dur": 812, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795215231, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795215966, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795216522, "dur": 1377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795217900, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795218075, "dur": 1399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795219475, "dur": 990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795220465, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795220598, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751441795221156, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795221224, "dur": 1227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1751441795222452, "dur": 503, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795223048, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795223297, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795223375, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795223586, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795223820, "dur": 735, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795224595, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795224773, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795224846, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795225404, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795225653, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795225950, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795226269, "dur": 696, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795227024, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795227130, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795227224, "dur": 698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795227922, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795228522, "dur": 1456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795229979, "dur": 553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795230533, "dur": 1022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795231555, "dur": 707, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795232263, "dur": 954, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795233218, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795233506, "dur": 952, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795234459, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795234632, "dur": 1499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795236132, "dur": 567051, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441795803184, "dur": 206630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795170670, "dur": 32948, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795203623, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_030C0AE8F7F2C9CD.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751441795203934, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795204037, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BA5431EB9EAB1D55.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751441795204137, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795204216, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_976F9C96B5CDAC08.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751441795204311, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795204388, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_95C124FA2E4BBBB0.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751441795204479, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795204783, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_14A21DFAD1DFC142.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751441795204887, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795204952, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_14A21DFAD1DFC142.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751441795205137, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795205190, "dur": 570, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_2CD7161C662F4679.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751441795205814, "dur": 441, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1751441795206288, "dur": 860, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1751441795207149, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795208166, "dur": 783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795208949, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795209740, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795210416, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795211203, "dur": 738, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Utilities\\Recursion.cs"}}, {"pid": 12345, "tid": 22, "ts": 1751441795211154, "dur": 1394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795212549, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795213279, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795214152, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795214810, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795215600, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795216200, "dur": 808, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795217009, "dur": 874, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795217943, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795218082, "dur": 1433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795219516, "dur": 959, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795220475, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795220638, "dur": 1670, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795222309, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795222632, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795222745, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795222885, "dur": 372, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795223316, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795223573, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795223829, "dur": 707, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795224537, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795224592, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795224842, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795225434, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795225655, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795225915, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795226247, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795226582, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795226984, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795227105, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795227223, "dur": 697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795227920, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795228006, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751441795228229, "dur": 1613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751441795229843, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795229960, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795230031, "dur": 506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795230537, "dur": 1031, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795231568, "dur": 692, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795232261, "dur": 930, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795233191, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795233532, "dur": 938, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795234470, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795234630, "dur": 1469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795236176, "dur": 567020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441795803197, "dur": 206588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795170710, "dur": 32918, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795203633, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_A8A2BEB223D0393B.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441795203766, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795203957, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_6FBB1366163B00FB.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441795204054, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795204128, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_99CD71E244B915CF.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441795204235, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795204334, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441795204439, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795204535, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_FD8C0D7AD7503969.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441795204772, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795204860, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_27EF5DC3D06E8F78.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441795204957, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795205056, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441795205167, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795205224, "dur": 1469, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441795206703, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441795206944, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795207039, "dur": 10746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1751441795217906, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795218003, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795218080, "dur": 1432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795219513, "dur": 959, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795220473, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795220590, "dur": 455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441795221046, "dur": 1755, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795222810, "dur": 1495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1751441795224306, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795224536, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795224605, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441795225021, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795225337, "dur": 1597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1751441795226935, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795227108, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795227239, "dur": 705, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795227945, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795228542, "dur": 1424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795229967, "dur": 550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795230517, "dur": 1015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795231533, "dur": 698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795232234, "dur": 947, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795233182, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795233502, "dur": 965, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795234468, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795234617, "dur": 1522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795236139, "dur": 567040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441795803181, "dur": 206593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795170759, "dur": 32884, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795203644, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_592CC89A6471077C.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441795203932, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795203997, "dur": 318, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_592CC89A6471077C.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441795204322, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D22D11490AEAA9B0.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441795204446, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795204535, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D22D11490AEAA9B0.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441795204638, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_B563F2CB5E9D696E.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441795204972, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E8D1A39870CABAC1.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441795205083, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795205225, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9C39811BC6DD0BF6.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441795205306, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795205365, "dur": 1022, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9C39811BC6DD0BF6.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441795206414, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795206483, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751441795206593, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795206685, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795206753, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1751441795206868, "dur": 1030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795207899, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795208765, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795209646, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795210382, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795211211, "dur": 776, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Core\\VirtualCameraRegistry.cs"}}, {"pid": 12345, "tid": 24, "ts": 1751441795211042, "dur": 1417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795212460, "dur": 835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795213296, "dur": 700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795213997, "dur": 1017, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795215382, "dur": 1156, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\UGUI\\EventSystem\\InputModules\\BaseInput.cs"}}, {"pid": 12345, "tid": 24, "ts": 1751441795215014, "dur": 1750, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795216765, "dur": 1167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795217933, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795218116, "dur": 1389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795219506, "dur": 994, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795220500, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795220628, "dur": 603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441795221232, "dur": 743, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795221988, "dur": 1188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751441795223177, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795223303, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795223431, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795223566, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795223800, "dur": 741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795224650, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795224767, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795224825, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441795225282, "dur": 1547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751441795226831, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795226971, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795227032, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795227113, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795227218, "dur": 712, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795227931, "dur": 602, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795228534, "dur": 1426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795229961, "dur": 581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795230542, "dur": 993, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795231535, "dur": 717, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795232253, "dur": 920, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795233174, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795233535, "dur": 960, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795234495, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795234621, "dur": 1529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795236150, "dur": 567113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441795803263, "dur": 206514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441796028929, "dur": 4288, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 15948, "tid": 58975, "ts": 1751441796082174, "dur": 3734, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 15948, "tid": 58975, "ts": 1751441796085962, "dur": 4111, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 15948, "tid": 58975, "ts": 1751441796067130, "dur": 24284, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}
{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 13400, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 13400, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 13400, "tid": 673, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 13400, "tid": 673, "ts": 1751348646778822, "dur": 9123, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 13400, "tid": 673, "ts": 1751348646800532, "dur": 2321, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 13400, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 13400, "tid": 1, "ts": 1751348646206959, "dur": 8849, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 13400, "tid": 1, "ts": 1751348646215811, "dur": 69993, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 13400, "tid": 1, "ts": 1751348646285818, "dur": 70112, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 13400, "tid": 673, "ts": 1751348646802861, "dur": 23, "ph": "X", "name": "", "args": {}}, {"pid": 13400, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646203024, "dur": 23844, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646226872, "dur": 532939, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646229052, "dur": 6252, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646235318, "dur": 5266, "ph": "X", "name": "ProcessMessages 20484", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646240592, "dur": 906, "ph": "X", "name": "ReadAsync 20484", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646241509, "dur": 58, "ph": "X", "name": "ProcessMessages 20489", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646241570, "dur": 122, "ph": "X", "name": "ReadAsync 20489", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646241703, "dur": 4, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646241710, "dur": 83, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646241799, "dur": 3, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646241804, "dur": 76, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646241887, "dur": 3, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646241893, "dur": 84, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646241987, "dur": 4, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646241994, "dur": 85, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646242086, "dur": 3, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646242093, "dur": 77, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646242177, "dur": 3, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646242183, "dur": 224, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646242415, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646242420, "dur": 120, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646242549, "dur": 4, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646242556, "dur": 94, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646242657, "dur": 3, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646242663, "dur": 65, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646242735, "dur": 3, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646242742, "dur": 83, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646242832, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646242842, "dur": 92, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646242941, "dur": 3, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646242950, "dur": 79, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646243037, "dur": 3, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646243043, "dur": 76, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646243126, "dur": 3, "ph": "X", "name": "ProcessMessages 154", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646243132, "dur": 79, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646243218, "dur": 3, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646243224, "dur": 62, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646243294, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646243299, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646243393, "dur": 3, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646243399, "dur": 78, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646243485, "dur": 3, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646243491, "dur": 81, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646243579, "dur": 3, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646243585, "dur": 73, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646243665, "dur": 3, "ph": "X", "name": "ProcessMessages 135", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646243671, "dur": 80, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646243758, "dur": 3, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646243764, "dur": 78, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646243850, "dur": 3, "ph": "X", "name": "ProcessMessages 214", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646243856, "dur": 76, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646243939, "dur": 3, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646243948, "dur": 64, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646244019, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646244030, "dur": 79, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646244117, "dur": 3, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646244123, "dur": 75, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646244205, "dur": 3, "ph": "X", "name": "ProcessMessages 223", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646244211, "dur": 76, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646244294, "dur": 3, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646244300, "dur": 77, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646244384, "dur": 3, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646244393, "dur": 77, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646244477, "dur": 3, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646244483, "dur": 76, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646244567, "dur": 3, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646244573, "dur": 75, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646244655, "dur": 3, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646244661, "dur": 117, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646244785, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646244790, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646244880, "dur": 3, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646244886, "dur": 79, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646244972, "dur": 3, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646244978, "dur": 81, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646245067, "dur": 3, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646245073, "dur": 122, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646245202, "dur": 3, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646245208, "dur": 101, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646245316, "dur": 4, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646245323, "dur": 65, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646245395, "dur": 3, "ph": "X", "name": "ProcessMessages 65", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646245402, "dur": 90, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646245498, "dur": 3, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646245505, "dur": 84, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646245596, "dur": 4, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646245603, "dur": 77, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646245687, "dur": 3, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646245693, "dur": 90, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646245790, "dur": 4, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646245797, "dur": 76, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646245880, "dur": 3, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646245886, "dur": 98, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646245992, "dur": 4, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646246000, "dur": 102, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646246110, "dur": 5, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646246117, "dur": 92, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646246220, "dur": 4, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646246226, "dur": 74, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646246308, "dur": 4, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646246314, "dur": 95, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646246416, "dur": 4, "ph": "X", "name": "ProcessMessages 796", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646246424, "dur": 97, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646246528, "dur": 3, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646246534, "dur": 82, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646246624, "dur": 4, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646246631, "dur": 90, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646246727, "dur": 4, "ph": "X", "name": "ProcessMessages 673", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646246734, "dur": 123, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646246864, "dur": 3, "ph": "X", "name": "ProcessMessages 186", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646246875, "dur": 129, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646247011, "dur": 6, "ph": "X", "name": "ProcessMessages 1496", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646247020, "dur": 100, "ph": "X", "name": "ReadAsync 1496", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646247127, "dur": 4, "ph": "X", "name": "ProcessMessages 728", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646247134, "dur": 86, "ph": "X", "name": "ReadAsync 728", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646247227, "dur": 7, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646247237, "dur": 98, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646247342, "dur": 3, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646247348, "dur": 104, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646247459, "dur": 4, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646247466, "dur": 105, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646247578, "dur": 4, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646247585, "dur": 92, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646247686, "dur": 4, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646247693, "dur": 117, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646247815, "dur": 3, "ph": "X", "name": "ProcessMessages 784", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646247820, "dur": 73, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646247900, "dur": 2, "ph": "X", "name": "ProcessMessages 82", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646247903, "dur": 69, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646247976, "dur": 2, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646247980, "dur": 81, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646248068, "dur": 3, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646248074, "dur": 88, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646248170, "dur": 4, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646248177, "dur": 108, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646248292, "dur": 3, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646248298, "dur": 86, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646248392, "dur": 4, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646248399, "dur": 85, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646248491, "dur": 4, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646248497, "dur": 88, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646248592, "dur": 4, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646248599, "dur": 69, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646248675, "dur": 3, "ph": "X", "name": "ProcessMessages 169", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646248682, "dur": 114, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646248803, "dur": 3, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646248809, "dur": 98, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646248915, "dur": 5, "ph": "X", "name": "ProcessMessages 829", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646248923, "dur": 91, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646249021, "dur": 4, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646249027, "dur": 82, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646249116, "dur": 4, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646249123, "dur": 371, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646249507, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646249512, "dur": 150, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646249671, "dur": 5, "ph": "X", "name": "ProcessMessages 1157", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646249680, "dur": 134, "ph": "X", "name": "ReadAsync 1157", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646249823, "dur": 10, "ph": "X", "name": "ProcessMessages 855", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646249837, "dur": 82, "ph": "X", "name": "ReadAsync 855", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646249927, "dur": 4, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646249933, "dur": 85, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646250026, "dur": 4, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646250033, "dur": 91, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646250131, "dur": 4, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646250138, "dur": 86, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646250232, "dur": 3, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646250238, "dur": 87, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646250331, "dur": 2, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646250335, "dur": 64, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646250407, "dur": 3, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646250413, "dur": 83, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646250501, "dur": 2, "ph": "X", "name": "ProcessMessages 689", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646250507, "dur": 67, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646250580, "dur": 3, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646250587, "dur": 85, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646250676, "dur": 2, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646250680, "dur": 65, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646250752, "dur": 3, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646250759, "dur": 93, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646250856, "dur": 2, "ph": "X", "name": "ProcessMessages 686", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646250862, "dur": 64, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646250933, "dur": 4, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646250940, "dur": 77, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646251021, "dur": 2, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646251025, "dur": 65, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646251097, "dur": 3, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646251103, "dur": 80, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646251190, "dur": 4, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646251197, "dur": 85, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646251292, "dur": 3, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646251298, "dur": 86, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646251391, "dur": 4, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646251398, "dur": 88, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646251494, "dur": 4, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646251501, "dur": 92, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646251600, "dur": 4, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646251607, "dur": 87, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646251701, "dur": 3, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646251708, "dur": 88, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646251802, "dur": 4, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646251809, "dur": 86, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646251902, "dur": 4, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646251909, "dur": 87, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646252004, "dur": 4, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646252011, "dur": 74, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646252092, "dur": 3, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646252098, "dur": 85, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646252192, "dur": 3, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646252198, "dur": 84, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646252290, "dur": 9, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646252301, "dur": 95, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646252403, "dur": 4, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646252410, "dur": 82, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646252499, "dur": 4, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646252506, "dur": 92, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646252608, "dur": 3, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646252615, "dur": 139, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646252761, "dur": 4, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646252768, "dur": 97, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646252872, "dur": 5, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646252880, "dur": 76, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646252963, "dur": 3, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646252969, "dur": 90, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646253066, "dur": 4, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646253072, "dur": 86, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646253165, "dur": 4, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646253172, "dur": 89, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646253269, "dur": 4, "ph": "X", "name": "ProcessMessages 491", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646253276, "dur": 85, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646253368, "dur": 4, "ph": "X", "name": "ProcessMessages 433", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646253374, "dur": 78, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646253459, "dur": 3, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646253465, "dur": 86, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646253558, "dur": 4, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646253565, "dur": 108, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646253678, "dur": 2, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646253682, "dur": 69, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646253758, "dur": 4, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646253765, "dur": 68, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646253838, "dur": 2, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646253842, "dur": 78, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646253928, "dur": 3, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646253934, "dur": 119, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646254066, "dur": 5, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646254074, "dur": 106, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646254187, "dur": 5, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646254195, "dur": 75, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646254290, "dur": 3, "ph": "X", "name": "ProcessMessages 169", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646254296, "dur": 99, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646254402, "dur": 4, "ph": "X", "name": "ProcessMessages 841", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646254409, "dur": 91, "ph": "X", "name": "ReadAsync 841", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646254507, "dur": 4, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646254515, "dur": 87, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646254609, "dur": 4, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646254616, "dur": 71, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646254692, "dur": 2, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646254696, "dur": 79, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646254780, "dur": 2, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646254783, "dur": 88, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646254879, "dur": 4, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646254886, "dur": 92, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646254982, "dur": 3, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646254987, "dur": 83, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646255077, "dur": 3, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646255084, "dur": 75, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646255163, "dur": 2, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646255167, "dur": 81, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646255256, "dur": 4, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646255263, "dur": 96, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646255363, "dur": 3, "ph": "X", "name": "ProcessMessages 721", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646255368, "dur": 72, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646255447, "dur": 3, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646255454, "dur": 88, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646255547, "dur": 2, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646255552, "dur": 97, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646255656, "dur": 4, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646255663, "dur": 97, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646255764, "dur": 2, "ph": "X", "name": "ProcessMessages 821", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646255769, "dur": 81, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646255860, "dur": 5, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646255868, "dur": 87, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646255958, "dur": 2, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646255963, "dur": 78, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646256048, "dur": 4, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646256054, "dur": 89, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646256147, "dur": 2, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646256151, "dur": 80, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646256239, "dur": 3, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646256245, "dur": 93, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646256343, "dur": 3, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646256348, "dur": 80, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646256436, "dur": 4, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646256442, "dur": 103, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646256550, "dur": 3, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646256554, "dur": 82, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646256646, "dur": 4, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646256653, "dur": 93, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646256751, "dur": 2, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646256755, "dur": 77, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646256839, "dur": 4, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646256846, "dur": 91, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646256941, "dur": 2, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646256946, "dur": 83, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646257035, "dur": 4, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646257042, "dur": 93, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646257139, "dur": 3, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646257144, "dur": 82, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646257233, "dur": 3, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646257240, "dur": 99, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646257344, "dur": 3, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646257349, "dur": 92, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646257448, "dur": 4, "ph": "X", "name": "ProcessMessages 686", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646257455, "dur": 98, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646257558, "dur": 3, "ph": "X", "name": "ProcessMessages 474", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646257563, "dur": 87, "ph": "X", "name": "ReadAsync 474", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646257658, "dur": 7, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646257669, "dur": 98, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646257775, "dur": 5, "ph": "X", "name": "ProcessMessages 623", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646257784, "dur": 108, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646257899, "dur": 4, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646257906, "dur": 90, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646258004, "dur": 5, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646258012, "dur": 73, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646258093, "dur": 4, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646258099, "dur": 92, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646258197, "dur": 4, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646258208, "dur": 92, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646258307, "dur": 4, "ph": "X", "name": "ProcessMessages 700", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646258314, "dur": 96, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646258417, "dur": 4, "ph": "X", "name": "ProcessMessages 697", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646258424, "dur": 90, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646258521, "dur": 4, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646258528, "dur": 84, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646258619, "dur": 4, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646258626, "dur": 88, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646258721, "dur": 4, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646258729, "dur": 92, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646258832, "dur": 4, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646258839, "dur": 91, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646258939, "dur": 4, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646258946, "dur": 91, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646259045, "dur": 4, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646259052, "dur": 52, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646259107, "dur": 2, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646259111, "dur": 54, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646259176, "dur": 2, "ph": "X", "name": "ProcessMessages 190", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646259182, "dur": 67, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646259252, "dur": 3, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646259262, "dur": 67, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646259335, "dur": 3, "ph": "X", "name": "ProcessMessages 267", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646259342, "dur": 86, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646259435, "dur": 4, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646259442, "dur": 101, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646259550, "dur": 4, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646259557, "dur": 91, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646259655, "dur": 4, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646259664, "dur": 90, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646259761, "dur": 4, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646259769, "dur": 90, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646259866, "dur": 4, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646259874, "dur": 91, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646259973, "dur": 4, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646259980, "dur": 88, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646260076, "dur": 4, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646260084, "dur": 66, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646260153, "dur": 3, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646260157, "dur": 68, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646260233, "dur": 3, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646260241, "dur": 99, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646260347, "dur": 4, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646260354, "dur": 96, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646260456, "dur": 5, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646260464, "dur": 88, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646260561, "dur": 4, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646260568, "dur": 115, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646260689, "dur": 3, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646260694, "dur": 92, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646260790, "dur": 2, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646260794, "dur": 70, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646260869, "dur": 2, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646260873, "dur": 52, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646260929, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646260933, "dur": 86, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646261026, "dur": 3, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646261033, "dur": 104, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646261141, "dur": 3, "ph": "X", "name": "ProcessMessages 753", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646261146, "dur": 74, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646261228, "dur": 3, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646261234, "dur": 91, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646261328, "dur": 2, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646261333, "dur": 77, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646261417, "dur": 4, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646261431, "dur": 72, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646261509, "dur": 2, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646261513, "dur": 409, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646261929, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646261934, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646262016, "dur": 646, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646262669, "dur": 113, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646262789, "dur": 17, "ph": "X", "name": "ProcessMessages 672", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646262809, "dur": 65, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646262881, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646262888, "dur": 77, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646262977, "dur": 7, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646262988, "dur": 102, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646263097, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646263104, "dur": 69, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646263179, "dur": 4, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646263186, "dur": 98, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646263292, "dur": 5, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646263301, "dur": 89, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646263396, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646263401, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646263473, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646263479, "dur": 61, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646263548, "dur": 4, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646263554, "dur": 71, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646263632, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646263638, "dur": 56, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646263700, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646263706, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646263772, "dur": 3, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646263778, "dur": 60, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646263843, "dur": 4, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646263850, "dur": 63, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646263919, "dur": 10, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646263932, "dur": 60, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646263997, "dur": 4, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646264003, "dur": 59, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646264070, "dur": 3, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646264076, "dur": 99, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646264181, "dur": 4, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646264188, "dur": 70, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646264263, "dur": 10, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646264275, "dur": 74, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646264359, "dur": 4, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646264365, "dur": 66, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646264441, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646264448, "dur": 69, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646264523, "dur": 5, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646264531, "dur": 68, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646264609, "dur": 4, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646264618, "dur": 61, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646264688, "dur": 4, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646264695, "dur": 77, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646264778, "dur": 10, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646264791, "dur": 65, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646264862, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646264867, "dur": 79, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646264955, "dur": 5, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646264963, "dur": 95, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646265066, "dur": 6, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646265075, "dur": 83, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646265172, "dur": 8, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646265187, "dur": 115, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646265318, "dur": 8, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646265330, "dur": 91, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646265427, "dur": 8, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646265439, "dur": 87, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646265531, "dur": 6, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646265540, "dur": 54, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646265603, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646265610, "dur": 74, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646265694, "dur": 4, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646265701, "dur": 68, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646265775, "dur": 4, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646265781, "dur": 63, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646265851, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646265858, "dur": 58, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646265922, "dur": 2, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646265968, "dur": 62, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646266037, "dur": 424, "ph": "X", "name": "ProcessMessages 51", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646266466, "dur": 63, "ph": "X", "name": "ReadAsync 51", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646266534, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646266538, "dur": 11063, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646277611, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646277617, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646277674, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646277678, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646277731, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646277734, "dur": 340, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646278080, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646278084, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646278179, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646278187, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646278258, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646278262, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646278316, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646278320, "dur": 351, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646278677, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646278681, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646278734, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646278737, "dur": 2181, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646280926, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646280932, "dur": 111, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646281048, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646281053, "dur": 144, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646281202, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646281206, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646281257, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646281262, "dur": 1124, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646282395, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646282400, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646282467, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646282470, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646282534, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646282537, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646282607, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646282614, "dur": 381, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646283003, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646283007, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646283079, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646283084, "dur": 65, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646283158, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646283164, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646283235, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646283240, "dur": 190, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646283438, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646283442, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646283520, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646283525, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646283598, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646283602, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646283677, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646283681, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646283753, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646283757, "dur": 197, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646283962, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646283966, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646284020, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646284025, "dur": 192, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646284222, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646284225, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646284283, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646284285, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646284344, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646284348, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646284418, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646284423, "dur": 94, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646284522, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646284524, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646284576, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646284579, "dur": 76, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646284661, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646284665, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646284713, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646284717, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646284764, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646284767, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646284866, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646284870, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646284934, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646284939, "dur": 61, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646285004, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646285008, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646285061, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646285065, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646285115, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646285118, "dur": 90, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646285214, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646285219, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646285271, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646285273, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646285326, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646285329, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646285380, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646285383, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646285445, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646285448, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646285499, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646285502, "dur": 152, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646285659, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646285661, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646285706, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646285709, "dur": 171, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646285885, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646285889, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646285942, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646285945, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646286001, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646286005, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646286056, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646286059, "dur": 209, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646286272, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646286277, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646286328, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646286330, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646286403, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646286407, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646286500, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646286505, "dur": 85, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646286594, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646286599, "dur": 61, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646286675, "dur": 4, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646286683, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646286743, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646286751, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646286810, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646286813, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646286880, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646286885, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646286948, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646286951, "dur": 134, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646287093, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646287098, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646287155, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646287158, "dur": 183, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646287348, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646287352, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646287434, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646287440, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646287513, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646287519, "dur": 58, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646287583, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646287587, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646287662, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646287665, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646287732, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646287737, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646287799, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646287803, "dur": 55, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646287865, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646287870, "dur": 145, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646288023, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646288028, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646288092, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646288096, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646288148, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646288151, "dur": 424, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646288580, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646288584, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646288655, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646288660, "dur": 75, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646288742, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646288748, "dur": 89, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646288848, "dur": 9, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646288864, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646288941, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646288945, "dur": 61, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646289014, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646289019, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646289103, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646289107, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646289185, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646289188, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646289239, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646289243, "dur": 44, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646289291, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646289294, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646289379, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646289382, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646289431, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646289433, "dur": 307, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646289746, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646289750, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646289821, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646289825, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646289883, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646289886, "dur": 960, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646290854, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646290858, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646290924, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646290927, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646290978, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646290982, "dur": 77, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646291064, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646291068, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646291132, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646291135, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646291185, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646291189, "dur": 45, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646291238, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646291241, "dur": 160, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646291405, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646291408, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646291456, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646291459, "dur": 469, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646291934, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646291938, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646292001, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646292005, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646292057, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646292061, "dur": 377, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646292449, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646292453, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646292520, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646292523, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646292576, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646292584, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646292635, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646292638, "dur": 201, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646292845, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646292848, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646292905, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646292908, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646292980, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646292983, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646293030, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646293033, "dur": 491, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646293531, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646293536, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646293603, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646293607, "dur": 47, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646293658, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646293662, "dur": 705, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646294377, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646294381, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646294442, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646294446, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646294496, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646294499, "dur": 375, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646294880, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646294883, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646294964, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646294967, "dur": 978, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646295953, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646295957, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646296027, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646296031, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646296084, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646296087, "dur": 239, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646296333, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646296336, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646296413, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646296417, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646296467, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646296470, "dur": 1052, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646297528, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646297535, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646297630, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646297634, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646297694, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646297697, "dur": 2823, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646300526, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646300529, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646300586, "dur": 854, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646301445, "dur": 286553, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646588011, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646588019, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646588114, "dur": 2654, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646590778, "dur": 8933, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646599717, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646599721, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646599772, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646599777, "dur": 2091, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646601873, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646601876, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646601950, "dur": 18, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646601970, "dur": 132972, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646734952, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646734957, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646735051, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646735059, "dur": 1136, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646736203, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646736208, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646736285, "dur": 24, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646736312, "dur": 2174, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646738493, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646738496, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646738582, "dur": 925, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 13400, "tid": 12884901888, "ts": 1751348646739515, "dur": 20198, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 13400, "tid": 673, "ts": 1751348646802886, "dur": 3131, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 13400, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 13400, "tid": 8589934592, "ts": 1751348646197535, "dur": 158549, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 13400, "tid": 8589934592, "ts": 1751348646356089, "dur": 11, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 13400, "tid": 8589934592, "ts": 1751348646356103, "dur": 3866, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 13400, "tid": 673, "ts": 1751348646806020, "dur": 11, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 13400, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 13400, "tid": 4294967296, "ts": 1751348646159791, "dur": 603143, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 13400, "tid": 4294967296, "ts": 1751348646168745, "dur": 13723, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 13400, "tid": 4294967296, "ts": 1751348646763470, "dur": 10820, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 13400, "tid": 4294967296, "ts": 1751348646771548, "dur": 308, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 13400, "tid": 4294967296, "ts": 1751348646774432, "dur": 21, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 13400, "tid": 673, "ts": 1751348646806034, "dur": 14, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751348646221182, "dur": 53, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751348646221298, "dur": 2883, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751348646224199, "dur": 1098, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751348646225387, "dur": 115, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751348646225503, "dur": 1394, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751348646229838, "dur": 3393, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646235604, "dur": 6135, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646241752, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_A805A84D9B0C22D9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646241816, "dur": 114, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751348646241942, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751348646242004, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646242084, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751348646242164, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646242268, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646242605, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_BDDAB6C3AC1C4D86.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646242683, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751348646242797, "dur": 102, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751348646243038, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646243108, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751348646243214, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646243324, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646243500, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_0174E068A5FA251B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646243567, "dur": 107, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751348646243685, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751348646243759, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646243867, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751348646243940, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646244051, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646244216, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_A558B9E65539D7D0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646244287, "dur": 102, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751348646244409, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751348646244515, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646244579, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751348646244681, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646244763, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646244991, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ref.dll_7B1D397D2193763B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646245061, "dur": 107, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751348646245179, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751348646245247, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646245346, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751348646245471, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646245662, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751348646245775, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646245875, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751348646245984, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646246134, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751348646246288, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751348646246369, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646246604, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751348646246699, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646246812, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646246884, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646247163, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751348646247300, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646247416, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_02929A3ECA791895.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646247501, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751348646247613, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646247726, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646247958, "dur": 125, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751348646248097, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646248167, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751348646248246, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646248430, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751348646248550, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751348646248658, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751348646248767, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646248943, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751348646249086, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751348646249175, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646249781, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646249950, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646250105, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.Editor.ref.dll_CE7BFB6B1BFC18F8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646250213, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751348646250318, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751348646250397, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646250693, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646250773, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751348646250874, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646251020, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751348646251190, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751348646251272, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646251467, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751348646251576, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646251649, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751348646251760, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646251872, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_8456852F39E72DEA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646251975, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751348646252083, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751348646252192, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646252356, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751348646252468, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646252573, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646252762, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751348646252876, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646253056, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646253229, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751348646253341, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646253447, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646253647, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751348646253717, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751348646253826, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751348646254100, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751348646254203, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646254344, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646254561, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751348646254680, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751348646254962, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751348646255064, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646255160, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646255242, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646255352, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Shared.Editor.ref.dll_6CA31C4FA5286AB1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646255417, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751348646255551, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751348646255825, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751348646255926, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646256043, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646256228, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751348646256328, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751348646256403, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751348646256550, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751348646256607, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751348646256701, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751348646256821, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751348646256917, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751348646257027, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751348646257119, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751348646257195, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751348646257321, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751348646257397, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751348646257544, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751348646257602, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751348646257732, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751348646257816, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751348646257950, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751348646258063, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751348646258182, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646258376, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751348646258586, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646258799, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751348646258901, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751348646259016, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751348646259119, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751348646259231, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646259366, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751348646259457, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751348646259529, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751348646259622, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751348646259735, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751348646259840, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751348646259943, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751348646260053, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751348646260159, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751348646260264, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751348646260368, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751348646260495, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751348646260638, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751348646260723, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751348646260835, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751348646260985, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646261052, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646261197, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751348646261295, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751348646261394, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751348646261487, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751348646261648, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751348646227001, "dur": 34705, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751348646261722, "dur": 474873, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751348646736597, "dur": 773, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751348646737371, "dur": 315, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751348646737687, "dur": 134, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751348646737855, "dur": 147, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751348646738002, "dur": 78, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751348646738284, "dur": 140, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751348646738554, "dur": 122, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751348646738702, "dur": 9298, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751348646228837, "dur": 33400, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646262238, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_711E053178EA7BF1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751348646262684, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646262856, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_173731A5F5D7E041.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751348646263076, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646263231, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_20E0F9318BFD3B5B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751348646263586, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646263645, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_20E0F9318BFD3B5B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751348646263838, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_438C72DDFEF65815.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751348646263955, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646264321, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751348646264449, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751348646264654, "dur": 283, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751348646264941, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751348646265171, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646265240, "dur": 544, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751348646265789, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646265934, "dur": 1346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646267453, "dur": 908, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center\\Editor\\MultiplayerCenterWindow\\UI\\ViewUtils.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751348646267281, "dur": 1771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646269053, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646269940, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646270730, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646271693, "dur": 876, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Attributes\\TimelineHelpURLAttribute.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751348646271651, "dur": 2486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646274139, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646275387, "dur": 2382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646277770, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646277860, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646278353, "dur": 2569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646280923, "dur": 1777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646282701, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646282967, "dur": 1523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646284491, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646284674, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646284749, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646284956, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646285143, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646285207, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646285486, "dur": 671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646286158, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646286283, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751348646286740, "dur": 1112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751348646287853, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646287988, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646288335, "dur": 645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646288980, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646289124, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646289381, "dur": 645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646290027, "dur": 1084, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646291112, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646291359, "dur": 821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646292181, "dur": 556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646292737, "dur": 1062, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646293799, "dur": 830, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646294629, "dur": 1560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646296250, "dur": 1544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751348646297795, "dur": 438822, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646227020, "dur": 34766, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646261794, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_BF1A247579C7383B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751348646262205, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646262418, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_F24F3CD4D4D128AB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751348646262544, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646262733, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_00B5CEDD1ED5F4B3.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751348646262987, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646263149, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E522F23A96C2507B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751348646263261, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646263470, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_BDFB8953D401853F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751348646263727, "dur": 374, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646264122, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9C39811BC6DD0BF6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751348646264379, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646264460, "dur": 181, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9C39811BC6DD0BF6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751348646264651, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646264777, "dur": 287, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751348646265259, "dur": 911, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751348646266172, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646267392, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646268881, "dur": 622, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Variables\\IUnifiedVariableUnit.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751348646268290, "dur": 1493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646269784, "dur": 907, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646270691, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646271888, "dur": 690, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Evaluation\\RuntimeElement.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751348646271579, "dur": 1491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646273071, "dur": 972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646274044, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646274931, "dur": 975, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646275907, "dur": 1103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646277011, "dur": 859, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646277871, "dur": 529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646278400, "dur": 2483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646280921, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646281003, "dur": 1720, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646282724, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646282975, "dur": 1523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646284500, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646284682, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646284951, "dur": 1536, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646286507, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646286677, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646286829, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646286954, "dur": 658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646287613, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646287825, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646288351, "dur": 623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646288975, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646289083, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646289393, "dur": 631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646290024, "dur": 1083, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646291108, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646291316, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646291401, "dur": 796, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646292197, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646292734, "dur": 1027, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646293823, "dur": 802, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646294625, "dur": 1566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646296205, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646296277, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751348646296532, "dur": 1249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751348646297782, "dur": 438838, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646227259, "dur": 34592, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646261858, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FCC5A8FB58ECD1D2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751348646262099, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646262246, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_2BCC56A3C6071870.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751348646262490, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646262639, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_7BFAB029D4D7DE5C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751348646262770, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646262959, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751348646263135, "dur": 458, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646263611, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B56095C3DA730DAC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751348646263734, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646263855, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_42EBD154127454A5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751348646263957, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646264048, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_42EBD154127454A5.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751348646264111, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751348646264202, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646264370, "dur": 656, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751348646265033, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646265178, "dur": 523, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751348646265707, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646265799, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751348646265915, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646266617, "dur": 532, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Recording\\TimelineRecording.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751348646266008, "dur": 1943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646267952, "dur": 1107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646269059, "dur": 788, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646269848, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646270630, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646271900, "dur": 631, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Playables\\NotificationFlags.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751348646271578, "dur": 1471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646273050, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646273908, "dur": 1334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646275243, "dur": 2141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646277385, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646277883, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646278388, "dur": 2513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646280902, "dur": 1793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646282696, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646282972, "dur": 1530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646284504, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646284714, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646284972, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646285181, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646285474, "dur": 682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646286156, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646286290, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646286666, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646286861, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646286991, "dur": 619, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646287611, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646287819, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646287967, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646288320, "dur": 636, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646288957, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646289038, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646289098, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646289384, "dur": 653, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646290038, "dur": 1095, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646291134, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646291319, "dur": 858, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646292178, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646292786, "dur": 1009, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646293796, "dur": 860, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646294657, "dur": 1605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646296263, "dur": 1507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646297770, "dur": 67091, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751348646364862, "dur": 371833, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646226944, "dur": 34793, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646261767, "dur": 407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_7A1FD51B51344812.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751348646262176, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646262335, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8ECA4E4329F1BADB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751348646262697, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646262933, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D22D11490AEAA9B0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751348646263121, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646263391, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A99F700E950BD818.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751348646263682, "dur": 390, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646264072, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A99F700E950BD818.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751348646264173, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_696347C4732CD601.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751348646264386, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646264466, "dur": 996, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_696347C4732CD601.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751348646265477, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751348646265762, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646266637, "dur": 503, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio\\Editor\\SolutionParser.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751348646265851, "dur": 1951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646267804, "dur": 1289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646269094, "dur": 1285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646270380, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646271877, "dur": 719, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_Text.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751348646271661, "dur": 1859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646273521, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646274738, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646276171, "dur": 531, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework\\UnityEngine.TestRunner\\Assertions\\LogScope\\LogScope.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751348646275921, "dur": 1164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646277086, "dur": 770, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646277857, "dur": 490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646278407, "dur": 2527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646280935, "dur": 1738, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646282721, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646282914, "dur": 605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751348646283521, "dur": 1764, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646285302, "dur": 3841, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751348646289145, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646289364, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646289499, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751348646289949, "dur": 1224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751348646291174, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646291319, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646291415, "dur": 777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646292193, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646292745, "dur": 1040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646293786, "dur": 845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646294632, "dur": 1555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646296239, "dur": 1563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751348646297802, "dur": 438825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646227099, "dur": 34708, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646261815, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_80B8935D38233098.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751348646262056, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646262232, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_7F1C5DFD0AD39F77.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751348646262543, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646262766, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_42AEDBADC03A3022.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751348646263096, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646263366, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_42AEDBADC03A3022.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751348646263455, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E0B83A434CA2B946.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751348646263791, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646263899, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646263979, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9428A5C8BBBE8A51.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751348646264141, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646264284, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9428A5C8BBBE8A51.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751348646264393, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646264685, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646264777, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751348646264967, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646265018, "dur": 332, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751348646265433, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751348646265635, "dur": 477, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751348646266115, "dur": 1705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646267821, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646268997, "dur": 1172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646270170, "dur": 1218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646271911, "dur": 689, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsIEnumerableConverter.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751348646271389, "dur": 1938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646273328, "dur": 1264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646274593, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646275735, "dur": 1069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646276805, "dur": 1061, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646277867, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646278390, "dur": 2535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646280926, "dur": 1779, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646282706, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646282953, "dur": 1559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646284513, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646284686, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646284949, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646285176, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646285472, "dur": 682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646286155, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646286284, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646286683, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646286838, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646286962, "dur": 678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646287641, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646287912, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646288344, "dur": 728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646289073, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646289359, "dur": 652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646290062, "dur": 1059, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646291121, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646291321, "dur": 855, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646292176, "dur": 553, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646292730, "dur": 1043, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646293774, "dur": 868, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646294643, "dur": 1577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646296221, "dur": 1575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751348646297796, "dur": 438827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646227167, "dur": 34662, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646261837, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_16A54265357DDF1F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751348646262056, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646262165, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B52CD1DFCA664AB3.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751348646262387, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646262615, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_5E52B5CE937E0ED1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751348646262804, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646263055, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C099976D06C71E3D.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751348646263234, "dur": 381, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646263652, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9F68AD12AC89760A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751348646263874, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646263992, "dur": 213, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9F68AD12AC89760A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751348646264225, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646264322, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751348646264413, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646264470, "dur": 1374, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751348646266277, "dur": 1377, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\Tests\\TestFixture\\ScopedDisposable.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751348646265847, "dur": 2390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646268238, "dur": 1237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646269476, "dur": 1240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646271919, "dur": 653, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\DivisionHandler.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751348646270717, "dur": 1856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646272574, "dur": 1400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646273975, "dur": 1195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646275171, "dur": 1293, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646276465, "dur": 1385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646277852, "dur": 513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646278365, "dur": 2584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646280950, "dur": 1756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646282707, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646282993, "dur": 1508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646284501, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646284671, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646284943, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646285216, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646285513, "dur": 652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646286166, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646286281, "dur": 422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751348646286745, "dur": 1095, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751348646287842, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646287956, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646288324, "dur": 646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646288971, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646289033, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646289150, "dur": 262, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646289412, "dur": 634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646290047, "dur": 1069, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646291117, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646291343, "dur": 841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646292185, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646292728, "dur": 1066, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646293794, "dur": 856, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646294650, "dur": 1562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646296213, "dur": 1559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646297773, "dur": 301976, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751348646599785, "dur": 135290, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751348646599751, "dur": 135327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751348646735125, "dur": 1328, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751348646227391, "dur": 34481, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646261879, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_493844D967613FA0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751348646262136, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646262396, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_6FBB1366163B00FB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751348646262557, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646262725, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_99CD71E244B915CF.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751348646262830, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646263085, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_95C124FA2E4BBBB0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751348646263281, "dur": 383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646263717, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_800370C755C131C9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751348646263997, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646264117, "dur": 230, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_800370C755C131C9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751348646264391, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646264581, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646264786, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646264838, "dur": 778, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751348646265622, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646265687, "dur": 993, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751348646266685, "dur": 1336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646268022, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646269447, "dur": 904, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Control\\IBranchUnit.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751348646269052, "dur": 1953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646271886, "dur": 704, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Graphs\\GraphsExceptionUtility.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751348646271006, "dur": 1864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646272872, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646274069, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646275272, "dur": 2402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646277675, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646277878, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646278376, "dur": 2564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646280940, "dur": 1768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646282709, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646282920, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751348646283199, "dur": 976, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646284187, "dur": 1664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751348646285853, "dur": 1880, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646287827, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646288039, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646288333, "dur": 707, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646289041, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646289095, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646289402, "dur": 650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646290052, "dur": 1041, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646291101, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646291183, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646291364, "dur": 843, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646292208, "dur": 513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646292722, "dur": 1074, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646293797, "dur": 852, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646294649, "dur": 1608, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646296258, "dur": 1534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751348646297793, "dur": 438818, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646227474, "dur": 34413, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646261892, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_989B89A6DC960046.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751348646262056, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646262355, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_61E5A1A2114B6332.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751348646262734, "dur": 332, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646263066, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_61E5A1A2114B6332.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751348646263224, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_31922632D84128F5.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751348646263754, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D7EC0EF22CAF3612.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751348646263894, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646263994, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751348646264099, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646264319, "dur": 1072, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751348646265464, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751348646265585, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751348646265698, "dur": 271, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751348646265970, "dur": 1290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646268330, "dur": 679, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.performance.profile-analyzer\\Editor\\FrameSummary.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751348646267261, "dur": 1880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646269142, "dur": 1298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646270441, "dur": 1287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646271856, "dur": 658, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_Asset.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751348646271730, "dur": 1544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646273275, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646274169, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646275243, "dur": 2125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646277368, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646277880, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646278384, "dur": 2510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646280895, "dur": 1789, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646282684, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646282891, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751348646283219, "dur": 1260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751348646284480, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646284700, "dur": 519, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646285232, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751348646285522, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646285614, "dur": 899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751348646286514, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646286668, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646286825, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646286953, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646287028, "dur": 587, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646287615, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646287860, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646288298, "dur": 651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646288950, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646289029, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646289092, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646289396, "dur": 622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646290019, "dur": 1076, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646291096, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646291324, "dur": 875, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646292200, "dur": 543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646292744, "dur": 1032, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646293777, "dur": 840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646294686, "dur": 1548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646296234, "dur": 1579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751348646297813, "dur": 438821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646227565, "dur": 34342, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646261916, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_B01BEAEDCAC9F962.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751348646262114, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646262343, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751348646262492, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646262698, "dur": 77, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751348646262781, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_921E3B2D436CDAE7.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751348646262973, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646263129, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_9B1D775C88DF2080.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751348646263265, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646263487, "dur": 923, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D7BFC8363EAC4767.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751348646264411, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646264490, "dur": 368, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D7BFC8363EAC4767.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751348646264872, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751348646265001, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646265070, "dur": 356, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751348646265427, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751348646265545, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751348646265687, "dur": 1006, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751348646266694, "dur": 1630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646268325, "dur": 1331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646269657, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646270558, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646271901, "dur": 637, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Collections\\NonNullableCollection.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751348646271430, "dur": 1439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646272869, "dur": 874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646273743, "dur": 900, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646274644, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646275238, "dur": 1479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646276718, "dur": 1123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646277905, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646278408, "dur": 2507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646280916, "dur": 1794, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646282711, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646282985, "dur": 1495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646284495, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646284604, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646284684, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646284958, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646285170, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646285485, "dur": 665, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646286151, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646286288, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646286667, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646286876, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646286971, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646287605, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646287822, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646288346, "dur": 632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646288978, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646289060, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646289181, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751348646289428, "dur": 1750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751348646291179, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646291328, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646291424, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751348646291641, "dur": 957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751348646292599, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646292719, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646292826, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751348646293214, "dur": 1273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751348646294488, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646294624, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646294705, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751348646295062, "dur": 1004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751348646296067, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646296197, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646296270, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751348646296656, "dur": 986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751348646297643, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646297764, "dur": 1071, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751348646300028, "dur": 189, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751348646301366, "dur": 286865, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751348646599734, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1751348646599717, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1751348646599944, "dur": 2188, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 9, "ts": 1751348646602147, "dur": 134453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646227653, "dur": 34276, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646261936, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_44286934693FF148.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751348646262135, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646262321, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46E217D19A6E01BD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751348646262580, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646262810, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46E217D19A6E01BD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751348646262874, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_7D7B641D38DA242D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751348646262983, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646263078, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_7D7B641D38DA242D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751348646263144, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B5DB3F27CCAA63FE.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751348646263295, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646263617, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_A7B3B5FA99331E00.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751348646263739, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646264188, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751348646264430, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751348646264752, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646264892, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751348646265010, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646265074, "dur": 441, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751348646265518, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751348646266250, "dur": 1095, "ph": "X", "name": "File", "args": {"detail": "Assets\\Invector-3rdPersonController_LITE\\Scripts\\Camera\\Editor\\vThirdPersonCameraEditor.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751348646265831, "dur": 2158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646267989, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646268878, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646269733, "dur": 1250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646271914, "dur": 639, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Graphs\\IGraphParent.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751348646270984, "dur": 1570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646272555, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646273463, "dur": 874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646274338, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646275205, "dur": 1008, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646276215, "dur": 168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646276383, "dur": 77, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646276460, "dur": 1388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646277848, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646278364, "dur": 2557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646280922, "dur": 1767, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646282689, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646282952, "dur": 888, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751348646283842, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646283915, "dur": 2649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751348646286566, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646286988, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646287111, "dur": 522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646287633, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646287828, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646288302, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646288374, "dur": 676, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646289101, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646289389, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646290022, "dur": 1091, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646291114, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646291381, "dur": 807, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646292189, "dur": 526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646292716, "dur": 1052, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646293769, "dur": 883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646294653, "dur": 1595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646296248, "dur": 1538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751348646297787, "dur": 438821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646227725, "dur": 34222, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646261953, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_1C55A2A4A7EC33E2.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751348646262118, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646262309, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_C2E93A20F98E8229.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751348646262515, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646262633, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_6C4C0BE13C75841D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751348646262811, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_6C4C0BE13C75841D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751348646262891, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_ED94C9E717A89F9F.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751348646263002, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646263248, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_FD8C0D7AD7503969.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751348646263384, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646263656, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_FD8C0D7AD7503969.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751348646263918, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751348646264042, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646264127, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_39F0A7015032F01B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751348646264314, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646264496, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646264743, "dur": 236, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751348646265022, "dur": 296, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1751348646265321, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751348646265499, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646265564, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646265820, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751348646265958, "dur": 1515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646267474, "dur": 1424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646268899, "dur": 1220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646270120, "dur": 1259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646271873, "dur": 657, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsContext.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751348646271380, "dur": 2001, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646273382, "dur": 1258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646274641, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646275859, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646277045, "dur": 793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646277894, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646278370, "dur": 2591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646280962, "dur": 1757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646282719, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646282955, "dur": 1550, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646284506, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646284674, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646284743, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646284964, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646285184, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646285477, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646285550, "dur": 614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646286165, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646286279, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751348646286753, "dur": 742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751348646287496, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646287614, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646287724, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646287878, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646288331, "dur": 730, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646289062, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646289354, "dur": 676, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646290032, "dur": 1078, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646291110, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646291336, "dur": 867, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646292204, "dur": 520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646292725, "dur": 1067, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646293793, "dur": 862, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646294656, "dur": 1576, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646296232, "dur": 1565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751348646297798, "dur": 438842, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646227782, "dur": 34183, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646261970, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5543A9502BBA10BA.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751348646262136, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646262293, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_47A61C91F1D08784.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751348646262402, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646262493, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BA5431EB9EAB1D55.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751348646262600, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646262684, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BA5431EB9EAB1D55.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751348646262788, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CB3FD07F3B098D2B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751348646262915, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646263120, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B8F1FBD1DD727BB2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751348646263352, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646263682, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B47D5ED0AB2E73A1.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751348646263795, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646263988, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646264125, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_02726B6CC8A7DF56.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751348646264237, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646264332, "dur": 369, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_02726B6CC8A7DF56.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751348646264749, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751348646264893, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646264952, "dur": 713, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751348646265743, "dur": 517, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751348646266523, "dur": 581, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Actions\\ActionContext.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751348646266263, "dur": 1520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646267784, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646268608, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646269452, "dur": 944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646270397, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646271898, "dur": 643, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsPropertyAttribute.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751348646271272, "dur": 1479, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646272849, "dur": 825, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Plugins\\iOS\\IOSGameController.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751348646272752, "dur": 1841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646274594, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646275442, "dur": 1457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646276901, "dur": 983, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646277885, "dur": 520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646278405, "dur": 2503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646280910, "dur": 1776, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646282688, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646282906, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751348646283192, "dur": 459, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646283661, "dur": 1108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751348646284771, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646284970, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646285097, "dur": 723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751348646285821, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646285898, "dur": 2962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751348646288862, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646289072, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646289193, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646289365, "dur": 679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646290045, "dur": 1053, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646291099, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646291334, "dur": 840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646292232, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646292727, "dur": 1054, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646293782, "dur": 856, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646294639, "dur": 1597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646296237, "dur": 1546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751348646297784, "dur": 438800, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646227841, "dur": 34141, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646261988, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_9DC68FB86218AF77.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751348646262152, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646262373, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_74513773BD43745E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751348646262629, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646262952, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_74513773BD43745E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751348646263024, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_00433992BAF13A73.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751348646263154, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646263360, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0D29C9963CEF79A6.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751348646263638, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646263828, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B014F0E06EB9C8B3.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751348646263994, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646264292, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646264446, "dur": 150, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1751348646264646, "dur": 763, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1751348646265411, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751348646265496, "dur": 448, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751348646265945, "dur": 1202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646267148, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646267857, "dur": 841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646268699, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646270372, "dur": 1449, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqGraphs\\Changelog_1_1_1.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751348646269540, "dur": 2281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646271902, "dur": 646, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.settings-manager\\Editor\\Settings.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751348646271822, "dur": 1562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646273405, "dur": 937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646274342, "dur": 882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646276199, "dur": 549, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework\\UnityEditor.TestRunner\\Api\\ICallbacksDelegator.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751348646275225, "dur": 1622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646276848, "dur": 997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646277846, "dur": 514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646278362, "dur": 2535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646280897, "dur": 1770, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646282744, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646282995, "dur": 1511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646284507, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646284701, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646284952, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646285236, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646285500, "dur": 678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646286178, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646286292, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646286673, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646286831, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646286950, "dur": 667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646287618, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646287811, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646287984, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646288303, "dur": 644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646288948, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646289076, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646289364, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646289616, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646290035, "dur": 1069, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646291105, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646291316, "dur": 885, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646292201, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646292779, "dur": 1019, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646293798, "dur": 838, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646294637, "dur": 1606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646296244, "dur": 1533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751348646297778, "dur": 438813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646227948, "dur": 34058, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646262015, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_52F12B5AEAC5FC8F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751348646262300, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646262473, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_6C2CA7D176733A22.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751348646262587, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646262676, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_6C2CA7D176733A22.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751348646262737, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B1475218F12198D8.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751348646262852, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646263036, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6678507EC9682BC1.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751348646263193, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646263371, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6678507EC9682BC1.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751348646263474, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_14A21DFAD1DFC142.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751348646263807, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646263980, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646264059, "dur": 126, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1751348646264193, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751348646264430, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646264565, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646264674, "dur": 516, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1751348646265193, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646265245, "dur": 492, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1751348646265788, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751348646265921, "dur": 1281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646267203, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646268030, "dur": 943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646268974, "dur": 874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646269849, "dur": 973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646270823, "dur": 972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646271884, "dur": 652, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.testtools.codecoverage\\Editor\\CoverageFormats\\OpenCover\\Model\\Summary.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751348646271796, "dur": 1841, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646273638, "dur": 1208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646274847, "dur": 1207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646276055, "dur": 1203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646277259, "dur": 613, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646277872, "dur": 539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646278412, "dur": 2505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646280918, "dur": 1804, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646282722, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646282979, "dur": 1508, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646284487, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646284669, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646284951, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646285172, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646285479, "dur": 668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646286157, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646286232, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646286295, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646286708, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646286840, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646287014, "dur": 604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646287619, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646287855, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646288312, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646288953, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646289026, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646289110, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646289427, "dur": 632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646290060, "dur": 1065, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646291126, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646291337, "dur": 861, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646292198, "dur": 540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646292738, "dur": 1050, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646293789, "dur": 864, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646294654, "dur": 1554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646296209, "dur": 1570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751348646297780, "dur": 438817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646228050, "dur": 33983, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646262041, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_F4165E534DD27516.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751348646262294, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646262436, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E009CCFB4A5E9FFB.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751348646262669, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646262830, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_5E37763FF8B95A1F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751348646263099, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646263285, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_DC79D8DB49767D4F.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751348646263614, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646263790, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BED6F64D074B3174.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751348646263903, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646263983, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BED6F64D074B3174.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751348646264043, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751348646264127, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646264285, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751348646264477, "dur": 227, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751348646264786, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751348646264883, "dur": 325, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751348646265237, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646265369, "dur": 708, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751348646266624, "dur": 512, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Items\\ItemsPerTrack.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751348646266081, "dur": 1615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646267697, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646268883, "dur": 1232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646270116, "dur": 1349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646271906, "dur": 649, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Cloning\\ISpecifiesCloner.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751348646271467, "dur": 1610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646273980, "dur": 546, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Devices\\Mouse.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751348646273078, "dur": 1449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646274528, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646275435, "dur": 1189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646276625, "dur": 1237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646277863, "dur": 530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646278395, "dur": 2493, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646280900, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646280988, "dur": 1744, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646282733, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646282982, "dur": 1513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646284496, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646284674, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646284944, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646285171, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646285470, "dur": 689, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646286160, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646286286, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646286680, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646286833, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646286960, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646287052, "dur": 555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646287608, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646287830, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646288327, "dur": 632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646288960, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646289032, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646289091, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646289405, "dur": 666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646290072, "dur": 1063, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646291136, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646291338, "dur": 857, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646292196, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646292748, "dur": 1023, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646293771, "dur": 864, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646294636, "dur": 1590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646296227, "dur": 1541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646297769, "dur": 62427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646364022, "dur": 789, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 15, "ts": 1751348646360199, "dur": 4618, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751348646364818, "dur": 371811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646228200, "dur": 33860, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646262069, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_921345D5C92C6DDE.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751348646262334, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646262483, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_4E756196A062909D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751348646262605, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646262813, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_5AC43434136967AD.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751348646262918, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646263209, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_71CC72797B2E953C.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751348646263371, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646263667, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_27EF5DC3D06E8F78.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751348646263843, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646264064, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_A3850CF6800E4193.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751348646264231, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646264346, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_A3850CF6800E4193.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751348646264552, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751348646264732, "dur": 309, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1751348646265047, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646265136, "dur": 419, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1751348646265705, "dur": 446, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1751348646266636, "dur": 501, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Audio\\AudioTrackInspector.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751348646266154, "dur": 1518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646267673, "dur": 881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646268555, "dur": 961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646269517, "dur": 978, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646270496, "dur": 1398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646271896, "dur": 682, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Utilities\\ReadOnlyArray.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751348646271896, "dur": 1835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646273732, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646274949, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646276169, "dur": 123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646276292, "dur": 92, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646276431, "dur": 1429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646277861, "dur": 530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646278391, "dur": 2538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646280929, "dur": 1762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646282692, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646282896, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751348646283326, "dur": 806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751348646284133, "dur": 959, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646285143, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646285242, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646285475, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646286153, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646286293, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646286670, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646286816, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646286959, "dur": 644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646287608, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646287692, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646287846, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646288309, "dur": 657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646288967, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646289055, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646289135, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646289421, "dur": 619, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646290041, "dur": 1088, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646291129, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646291328, "dur": 882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646292210, "dur": 530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646292740, "dur": 1039, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646293779, "dur": 835, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646294674, "dur": 1554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646296231, "dur": 1544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751348646297776, "dur": 438827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646228330, "dur": 33758, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646262097, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_030C0AE8F7F2C9CD.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751348646262339, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646262523, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_DCB00145011AACBC.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751348646262622, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646262943, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8B525A02B9DF9C7E.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751348646263110, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646263410, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_54605A4C765A4A77.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751348646263675, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646263939, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646264033, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_2CD7161C662F4679.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751348646264094, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646264250, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646264337, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751348646264748, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646264817, "dur": 310, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751348646265441, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751348646265595, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646265731, "dur": 933, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1751348646266666, "dur": 526, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Editor\\TMP\\HDRP\\TMP_SDF_HDRPLitShaderGUI.cs"}}, {"pid": 12345, "tid": 17, "ts": 1751348646266666, "dur": 1728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646268395, "dur": 1810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646270206, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646271896, "dur": 816, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\EditorBinding\\WarnBeforeEditingAttribute.cs"}}, {"pid": 12345, "tid": 17, "ts": 1751348646271206, "dur": 1651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646272858, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646273681, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646274545, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646275414, "dur": 1773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646277188, "dur": 698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646277891, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646278356, "dur": 2543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646280899, "dur": 1780, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646282681, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646282928, "dur": 862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751348646283793, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646283882, "dur": 2706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751348646286590, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646286830, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646286996, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646287631, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646287896, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646288322, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646288962, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646289063, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646289362, "dur": 651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646290077, "dur": 1045, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646291123, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646291322, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646291390, "dur": 793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646292184, "dur": 548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646292732, "dur": 1054, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646293787, "dur": 858, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646294646, "dur": 1577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646296224, "dur": 1565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751348646297789, "dur": 438825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646228439, "dur": 33676, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646262124, "dur": 454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_A8A2BEB223D0393B.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751348646262579, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646262743, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_39C615B7DD834A4A.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751348646262954, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_39C615B7DD834A4A.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751348646263044, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_783D7153E7AEE314.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751348646263228, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646263324, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_963BCDCD478EB63D.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751348646263633, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646263808, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_10CDAFD975BC0AB7.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751348646264029, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646264318, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751348646264657, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646264776, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646265006, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646265096, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646265188, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646265241, "dur": 619, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1751348646265862, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646266627, "dur": 502, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Editor\\TMP\\TMP_PackageUtilities.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751348646266520, "dur": 1500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646268022, "dur": 1411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646269434, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646270612, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646271909, "dur": 686, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Utilities\\IPropertyCollector.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751348646271538, "dur": 1467, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646273006, "dur": 958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646273965, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646274850, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646276044, "dur": 1375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646277420, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646277869, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646278368, "dur": 2544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646280913, "dur": 1763, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646282677, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646282908, "dur": 1491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751348646284401, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646284492, "dur": 2703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751348646287197, "dur": 1047, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646288305, "dur": 483, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646288801, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751348646289086, "dur": 808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751348646289895, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646290027, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646290100, "dur": 1019, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646291120, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646291360, "dur": 845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646292205, "dur": 515, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646292720, "dur": 1087, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646293808, "dur": 825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646294633, "dur": 1578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646296211, "dur": 1562, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751348646297774, "dur": 438806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646228587, "dur": 33556, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646262152, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_592CC89A6471077C.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751348646262512, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646262641, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_592CC89A6471077C.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751348646262718, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1830F8D8229EF0EB.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751348646262929, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646263091, "dur": 516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3C810AF224C3A909.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751348646263609, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646263774, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E8D1A39870CABAC1.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751348646263971, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646264184, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751348646264243, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646264301, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751348646264453, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751348646264708, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646264804, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751348646265059, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646265249, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646265401, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751348646265540, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751348646265706, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646265814, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751348646266110, "dur": 1263, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Utilities\\CustomTrackDrawerAttribute.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751348646265950, "dur": 1980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646267931, "dur": 1323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646269256, "dur": 1602, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646271916, "dur": 649, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnMouseDownMessageListener.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751348646270859, "dur": 1922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646272783, "dur": 1203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646273987, "dur": 1615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646275604, "dur": 1298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646276902, "dur": 973, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646277876, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646278378, "dur": 2514, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646280893, "dur": 1789, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646282683, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646282900, "dur": 801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751348646283704, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646283808, "dur": 1453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1751348646285262, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646285474, "dur": 1420, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646286961, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751348646287208, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646287311, "dur": 1452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1751348646288764, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646288967, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646289121, "dur": 288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646289410, "dur": 632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646290043, "dur": 1046, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646291101, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646291192, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646291340, "dur": 850, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646292191, "dur": 526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646292718, "dur": 1073, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646293791, "dur": 856, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646294648, "dur": 1577, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646296225, "dur": 1540, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751348646297844, "dur": 438818, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646228641, "dur": 33525, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646262173, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D447C5E6C0383BF9.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751348646262419, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646262575, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_051DE14E9379244A.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751348646262689, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646262805, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_889FFFE959BEFFC2.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751348646262923, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646263104, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_889FFFE959BEFFC2.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751348646263160, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_805FFE03DDD45823.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751348646263259, "dur": 389, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646263675, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E671E5897665917D.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751348646264059, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646264274, "dur": 686, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E671E5897665917D.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751348646264977, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751348646265180, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646265285, "dur": 12873, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751348646278160, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646278365, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646278509, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751348646278859, "dur": 1850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751348646280710, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646280906, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646281038, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751348646281405, "dur": 1106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751348646282512, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646282688, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646282957, "dur": 756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751348646283769, "dur": 2263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751348646286033, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646286160, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646286275, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751348646286706, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646286839, "dur": 2004, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751348646288844, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646289031, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646289103, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_BF8650D11F8E519A.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751348646289173, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751348646289414, "dur": 1566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751348646290981, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646291111, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646291201, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751348646291451, "dur": 619, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751348646292182, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646292264, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646292713, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646292834, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751348646293073, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751348646293657, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646293772, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646293849, "dur": 770, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646294620, "dur": 1594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646296215, "dur": 1548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751348646297816, "dur": 438851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646228703, "dur": 33475, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646262183, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_D3BB5BABC9B6A39A.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751348646262334, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646262590, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_0660C742CA2CCB37.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751348646262766, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646263066, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_97D91564DB667E63.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751348646263185, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646263292, "dur": 366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_B563F2CB5E9D696E.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751348646263725, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3EA5AD5E9A933796.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751348646263827, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646263988, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_69199FEE963F7F27.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751348646264127, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646264317, "dur": 1168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_69199FEE963F7F27.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751348646265497, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751348646265866, "dur": 11868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1751348646277846, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646277936, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646278380, "dur": 2566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646280946, "dur": 1756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646282703, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646282941, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751348646283258, "dur": 1701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1751348646284961, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646285208, "dur": 443, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646285664, "dur": 507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646286172, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646286301, "dur": 360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646286719, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646286852, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646286980, "dur": 658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646287638, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646287865, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646288305, "dur": 637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646289031, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646289094, "dur": 304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646289399, "dur": 617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646290017, "dur": 1110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646291128, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646291351, "dur": 851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646292203, "dur": 547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646292750, "dur": 1069, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646293819, "dur": 803, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646294623, "dur": 1628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646296251, "dur": 1539, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751348646297790, "dur": 438797, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646228752, "dur": 33438, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646262197, "dur": 671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_DC7672C0573B7C85.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751348646262870, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646263079, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_DC7672C0573B7C85.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751348646263170, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FF5FCB0F465F7311.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751348646263294, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646263417, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_67F30B299A5D5154.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751348646263692, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646263887, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646264015, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_5CAA8383FA1D1EAC.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751348646264146, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646264268, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751348646264483, "dur": 235, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751348646264741, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646264842, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751348646265016, "dur": 219, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1751348646265291, "dur": 742, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751348646266037, "dur": 1403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646267441, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646268284, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646269208, "dur": 916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646270125, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646271922, "dur": 683, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Events\\EventBus.cs"}}, {"pid": 12345, "tid": 22, "ts": 1751348646271163, "dur": 1484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646272647, "dur": 874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646273522, "dur": 835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646274358, "dur": 843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646275202, "dur": 979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646276182, "dur": 63, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646276246, "dur": 55, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646276301, "dur": 75, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646276424, "dur": 1455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646277879, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646278358, "dur": 2548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646280907, "dur": 1786, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646282693, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646282915, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751348646283436, "dur": 828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751348646284265, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646284494, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646284610, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646284676, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646284992, "dur": 186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646285178, "dur": 304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646285483, "dur": 679, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646286163, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646286300, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646286682, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646286841, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646287002, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646287636, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646287888, "dur": 426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646288314, "dur": 651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646288965, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646289067, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646289352, "dur": 716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646290068, "dur": 1034, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646291103, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646291331, "dur": 840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646292222, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646292723, "dur": 1040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646293815, "dur": 836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646294652, "dur": 1564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646296217, "dur": 1586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751348646297804, "dur": 438851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646228830, "dur": 33373, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646262209, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_129B2BEEEC662028.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751348646262479, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646262599, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_C287EB2677BA7F93.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751348646262800, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_976F9C96B5CDAC08.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751348646262914, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646263196, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F275A7F2C1FA929A.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751348646263380, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646263759, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_09E134ACBD96AA82.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751348646263914, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646264023, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751348646264163, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646264329, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751348646264527, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751348646264644, "dur": 266, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751348646264911, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751348646265150, "dur": 431, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 23, "ts": 1751348646265666, "dur": 332, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751348646266001, "dur": 1265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646267267, "dur": 1097, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646268366, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646269613, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646270739, "dur": 932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646271885, "dur": 630, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_ListPool.cs"}}, {"pid": 12345, "tid": 23, "ts": 1751348646271672, "dur": 1923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646273597, "dur": 1353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646274951, "dur": 1150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646276102, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646276593, "dur": 1250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646277844, "dur": 527, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646278372, "dur": 2558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646280931, "dur": 1767, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646282698, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646283003, "dur": 1475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646284490, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646284574, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646284726, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646284961, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646285189, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646285477, "dur": 668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646286196, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646286298, "dur": 365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646286663, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646286818, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646286960, "dur": 691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646287653, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646287834, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646288301, "dur": 662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646288964, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646289034, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646289085, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646289356, "dur": 672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646290029, "dur": 1102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646291132, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646291326, "dur": 893, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646292219, "dur": 516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646292736, "dur": 1047, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646293783, "dur": 840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646294624, "dur": 1598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646296222, "dur": 1595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751348646297818, "dur": 438827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646226987, "dur": 34780, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646261772, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_00CD99E311DC019E.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751348646262055, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646262171, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_970778ACBFA20360.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751348646262294, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646262544, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F8AA39E8D5CE00CF.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751348646262743, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646262919, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_6E64691D4ACC974A.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751348646263130, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646263337, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C5419324AD6BC088.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751348646263728, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646263843, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_4FB16FC3A796B887.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751348646263955, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646264074, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_3AA3364A40DB9F8B.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751348646264205, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646264281, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_3AA3364A40DB9F8B.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751348646264560, "dur": 974, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751348646265537, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751348646265702, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 24, "ts": 1751348646265882, "dur": 725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646267214, "dur": 509, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Editor\\TMP\\TMPro_ContextMenus.cs"}}, {"pid": 12345, "tid": 24, "ts": 1751348646266608, "dur": 1695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646268304, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646269396, "dur": 1015, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646270412, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646271879, "dur": 596, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\EditorBinding\\InspectableIfAttribute.cs"}}, {"pid": 12345, "tid": 24, "ts": 1751348646271272, "dur": 1400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646272673, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646273533, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646274728, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646275613, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646276713, "dur": 1141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646277855, "dur": 495, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646278414, "dur": 2505, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646280920, "dur": 1836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646282757, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646282964, "dur": 1528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646284493, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646284679, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646284946, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646285202, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646285481, "dur": 688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646286170, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646286297, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646286679, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646286822, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646286952, "dur": 648, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646287606, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646287678, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646287923, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646288316, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646289075, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646289416, "dur": 631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646290047, "dur": 1053, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646291101, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646291361, "dur": 825, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646292187, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646292747, "dur": 1022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646293770, "dur": 856, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646294627, "dur": 1591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646296219, "dur": 1580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751348646297800, "dur": 438806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751348646755509, "dur": 3841, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 13400, "tid": 673, "ts": 1751348646809177, "dur": 4000, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 13400, "tid": 673, "ts": 1751348646813242, "dur": 5089, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 13400, "tid": 673, "ts": 1751348646795797, "dur": 24155, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}
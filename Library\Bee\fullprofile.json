{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 41616, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 41616, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 41616, "tid": 2104, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 41616, "tid": 2104, "ts": 1751355058755783, "dur": 1013, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 41616, "tid": 2104, "ts": 1751355058762953, "dur": 1647, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 41616, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 41616, "tid": 1, "ts": 1751355058169428, "dur": 7824, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 41616, "tid": 1, "ts": 1751355058177256, "dur": 90728, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 41616, "tid": 1, "ts": 1751355058267996, "dur": 51290, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 41616, "tid": 2104, "ts": 1751355058764606, "dur": 15, "ph": "X", "name": "", "args": {}}, {"pid": 41616, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058165992, "dur": 23218, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058189214, "dur": 553240, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058190886, "dur": 4472, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058195365, "dur": 2884, "ph": "X", "name": "ProcessMessages 5537", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058198257, "dur": 497, "ph": "X", "name": "ReadAsync 5537", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058198760, "dur": 27, "ph": "X", "name": "ProcessMessages 19896", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058198789, "dur": 57, "ph": "X", "name": "ReadAsync 19896", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058198852, "dur": 2, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058198856, "dur": 44, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058198903, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058198906, "dur": 45, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058198955, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058198958, "dur": 44, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199006, "dur": 1, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199008, "dur": 43, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199056, "dur": 2, "ph": "X", "name": "ProcessMessages 186", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199059, "dur": 91, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199154, "dur": 1, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199157, "dur": 57, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199218, "dur": 2, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199221, "dur": 47, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199272, "dur": 2, "ph": "X", "name": "ProcessMessages 190", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199275, "dur": 46, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199325, "dur": 2, "ph": "X", "name": "ProcessMessages 182", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199328, "dur": 44, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199376, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199378, "dur": 47, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199429, "dur": 1, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199432, "dur": 44, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199479, "dur": 1, "ph": "X", "name": "ProcessMessages 183", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199482, "dur": 44, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199530, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199533, "dur": 63, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199599, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199602, "dur": 49, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199654, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199657, "dur": 44, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199704, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199707, "dur": 46, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199757, "dur": 2, "ph": "X", "name": "ProcessMessages 177", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199760, "dur": 46, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199810, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199812, "dur": 40, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199858, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199860, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199908, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199910, "dur": 42, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199957, "dur": 2, "ph": "X", "name": "ProcessMessages 177", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058199960, "dur": 43, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200007, "dur": 1, "ph": "X", "name": "ProcessMessages 177", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200009, "dur": 48, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200064, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200066, "dur": 51, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200121, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200123, "dur": 50, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200177, "dur": 2, "ph": "X", "name": "ProcessMessages 201", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200180, "dur": 46, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200230, "dur": 1, "ph": "X", "name": "ProcessMessages 216", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200233, "dur": 44, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200281, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200284, "dur": 43, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200331, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200333, "dur": 43, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200381, "dur": 2, "ph": "X", "name": "ProcessMessages 190", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200384, "dur": 43, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200431, "dur": 1, "ph": "X", "name": "ProcessMessages 179", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200435, "dur": 50, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200489, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200491, "dur": 44, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200539, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200542, "dur": 45, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200590, "dur": 2, "ph": "X", "name": "ProcessMessages 178", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200593, "dur": 45, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200642, "dur": 1, "ph": "X", "name": "ProcessMessages 166", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200645, "dur": 46, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200695, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200697, "dur": 44, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200745, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200747, "dur": 43, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200794, "dur": 2, "ph": "X", "name": "ProcessMessages 102", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200797, "dur": 45, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200846, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200849, "dur": 44, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200897, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200899, "dur": 46, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200949, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200951, "dur": 41, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058200996, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201000, "dur": 43, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201046, "dur": 1, "ph": "X", "name": "ProcessMessages 231", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201048, "dur": 47, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201099, "dur": 1, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201102, "dur": 42, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201149, "dur": 1, "ph": "X", "name": "ProcessMessages 169", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201152, "dur": 46, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201201, "dur": 1, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201204, "dur": 44, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201251, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201254, "dur": 44, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201302, "dur": 1, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201304, "dur": 45, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201353, "dur": 1, "ph": "X", "name": "ProcessMessages 173", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201356, "dur": 43, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201403, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201405, "dur": 41, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201449, "dur": 1, "ph": "X", "name": "ProcessMessages 186", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201452, "dur": 41, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201497, "dur": 1, "ph": "X", "name": "ProcessMessages 178", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201500, "dur": 47, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201550, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201553, "dur": 43, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201599, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201601, "dur": 42, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201647, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201649, "dur": 42, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201695, "dur": 1, "ph": "X", "name": "ProcessMessages 170", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201698, "dur": 44, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201746, "dur": 1, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201748, "dur": 41, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201792, "dur": 1, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201795, "dur": 45, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201844, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201846, "dur": 42, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201892, "dur": 1, "ph": "X", "name": "ProcessMessages 207", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201894, "dur": 41, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201939, "dur": 1, "ph": "X", "name": "ProcessMessages 113", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201942, "dur": 45, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201991, "dur": 1, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058201995, "dur": 44, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202045, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202048, "dur": 41, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202093, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202096, "dur": 79, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202180, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202182, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202244, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202247, "dur": 41, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202292, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202295, "dur": 68, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202367, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202369, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202422, "dur": 1, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202424, "dur": 48, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202476, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202479, "dur": 45, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202528, "dur": 2, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202531, "dur": 52, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202588, "dur": 2, "ph": "X", "name": "ProcessMessages 207", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202591, "dur": 45, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202640, "dur": 1, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202643, "dur": 47, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202694, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202696, "dur": 43, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202743, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202746, "dur": 44, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202794, "dur": 2, "ph": "X", "name": "ProcessMessages 102", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202797, "dur": 45, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202846, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202849, "dur": 45, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202897, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202900, "dur": 46, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202949, "dur": 1, "ph": "X", "name": "ProcessMessages 195", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058202952, "dur": 46, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203003, "dur": 5, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203009, "dur": 61, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203074, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203077, "dur": 45, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203126, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203128, "dur": 43, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203175, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203177, "dur": 43, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203226, "dur": 1, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203228, "dur": 42, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203274, "dur": 1, "ph": "X", "name": "ProcessMessages 161", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203277, "dur": 290, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203572, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203575, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203662, "dur": 2, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203665, "dur": 50, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203719, "dur": 2, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203722, "dur": 47, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203773, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203776, "dur": 41, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203821, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203823, "dur": 46, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203874, "dur": 1, "ph": "X", "name": "ProcessMessages 211", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203876, "dur": 47, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203927, "dur": 1, "ph": "X", "name": "ProcessMessages 194", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203930, "dur": 44, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203979, "dur": 2, "ph": "X", "name": "ProcessMessages 165", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058203982, "dur": 69, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204059, "dur": 3, "ph": "X", "name": "ProcessMessages 155", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204065, "dur": 65, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204134, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204137, "dur": 64, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204207, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204212, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204285, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204287, "dur": 45, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204336, "dur": 2, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204340, "dur": 48, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204391, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204394, "dur": 44, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204442, "dur": 1, "ph": "X", "name": "ProcessMessages 195", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204445, "dur": 46, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204495, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204498, "dur": 45, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204548, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204552, "dur": 49, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204604, "dur": 1, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204607, "dur": 49, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204661, "dur": 1, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204663, "dur": 41, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204708, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204711, "dur": 56, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204772, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204774, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204822, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204825, "dur": 43, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204872, "dur": 1, "ph": "X", "name": "ProcessMessages 214", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204875, "dur": 33, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204910, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204912, "dur": 35, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204956, "dur": 1, "ph": "X", "name": "ProcessMessages 217", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058204958, "dur": 44, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205006, "dur": 1, "ph": "X", "name": "ProcessMessages 90", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205009, "dur": 43, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205056, "dur": 1, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205058, "dur": 47, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205108, "dur": 1, "ph": "X", "name": "ProcessMessages 178", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205111, "dur": 42, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205157, "dur": 2, "ph": "X", "name": "ProcessMessages 150", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205160, "dur": 44, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205208, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205210, "dur": 42, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205256, "dur": 2, "ph": "X", "name": "ProcessMessages 142", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205259, "dur": 43, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205306, "dur": 1, "ph": "X", "name": "ProcessMessages 168", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205309, "dur": 110, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205423, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205425, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205472, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205475, "dur": 44, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205522, "dur": 2, "ph": "X", "name": "ProcessMessages 127", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205525, "dur": 42, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205571, "dur": 1, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205574, "dur": 45, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205623, "dur": 1, "ph": "X", "name": "ProcessMessages 231", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205625, "dur": 45, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205674, "dur": 1, "ph": "X", "name": "ProcessMessages 246", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205676, "dur": 41, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205721, "dur": 1, "ph": "X", "name": "ProcessMessages 105", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205724, "dur": 45, "ph": "X", "name": "ReadAsync 105", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205773, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205776, "dur": 44, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205824, "dur": 1, "ph": "X", "name": "ProcessMessages 213", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205826, "dur": 45, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205875, "dur": 1, "ph": "X", "name": "ProcessMessages 103", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205877, "dur": 40, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205921, "dur": 1, "ph": "X", "name": "ProcessMessages 219", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205924, "dur": 38, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205966, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058205968, "dur": 75, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206048, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206051, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206104, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206107, "dur": 50, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206161, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206164, "dur": 51, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206219, "dur": 2, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206222, "dur": 48, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206274, "dur": 2, "ph": "X", "name": "ProcessMessages 213", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206277, "dur": 44, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206325, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206327, "dur": 42, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206373, "dur": 1, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206376, "dur": 42, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206421, "dur": 1, "ph": "X", "name": "ProcessMessages 120", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206426, "dur": 45, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206475, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206477, "dur": 43, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206524, "dur": 1, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206526, "dur": 44, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206574, "dur": 1, "ph": "X", "name": "ProcessMessages 89", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206577, "dur": 46, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206628, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206630, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206678, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206680, "dur": 42, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206726, "dur": 1, "ph": "X", "name": "ProcessMessages 131", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206729, "dur": 46, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206778, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206781, "dur": 45, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206830, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206832, "dur": 44, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206880, "dur": 2, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206883, "dur": 44, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206931, "dur": 1, "ph": "X", "name": "ProcessMessages 113", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206934, "dur": 55, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206993, "dur": 1, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058206996, "dur": 45, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207045, "dur": 1, "ph": "X", "name": "ProcessMessages 227", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207050, "dur": 42, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207096, "dur": 1, "ph": "X", "name": "ProcessMessages 167", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207099, "dur": 44, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207146, "dur": 1, "ph": "X", "name": "ProcessMessages 169", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207149, "dur": 62, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207215, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207218, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207269, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207272, "dur": 44, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207320, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207323, "dur": 42, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207369, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207371, "dur": 45, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207421, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207423, "dur": 38, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207465, "dur": 1, "ph": "X", "name": "ProcessMessages 104", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207468, "dur": 44, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207516, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207518, "dur": 53, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207576, "dur": 2, "ph": "X", "name": "ProcessMessages 229", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207580, "dur": 100, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207684, "dur": 2, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207687, "dur": 61, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207752, "dur": 2, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207755, "dur": 40, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207800, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207802, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207861, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207864, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207925, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207927, "dur": 45, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207976, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058207979, "dur": 43, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208026, "dur": 2, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208029, "dur": 44, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208077, "dur": 1, "ph": "X", "name": "ProcessMessages 385", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208079, "dur": 45, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208128, "dur": 1, "ph": "X", "name": "ProcessMessages 273", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208131, "dur": 39, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208172, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208174, "dur": 38, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208217, "dur": 1, "ph": "X", "name": "ProcessMessages 210", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208219, "dur": 48, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208271, "dur": 2, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208274, "dur": 47, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208325, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208327, "dur": 44, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208376, "dur": 2, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208379, "dur": 67, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208449, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208451, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208501, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208503, "dur": 38, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208545, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208548, "dur": 42, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208594, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208596, "dur": 43, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208643, "dur": 1, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208645, "dur": 44, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208693, "dur": 1, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208696, "dur": 52, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208752, "dur": 1, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208754, "dur": 47, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208804, "dur": 1, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208807, "dur": 43, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208854, "dur": 2, "ph": "X", "name": "ProcessMessages 110", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208859, "dur": 44, "ph": "X", "name": "ReadAsync 110", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208907, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208910, "dur": 48, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208962, "dur": 1, "ph": "X", "name": "ProcessMessages 210", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058208966, "dur": 41, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209011, "dur": 1, "ph": "X", "name": "ProcessMessages 69", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209014, "dur": 37, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209055, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209057, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209106, "dur": 1, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209109, "dur": 45, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209158, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209161, "dur": 43, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209208, "dur": 2, "ph": "X", "name": "ProcessMessages 130", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209211, "dur": 45, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209260, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209262, "dur": 45, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209311, "dur": 1, "ph": "X", "name": "ProcessMessages 191", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209314, "dur": 45, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209362, "dur": 1, "ph": "X", "name": "ProcessMessages 159", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209365, "dur": 43, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209413, "dur": 1, "ph": "X", "name": "ProcessMessages 137", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209416, "dur": 44, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209464, "dur": 1, "ph": "X", "name": "ProcessMessages 214", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209466, "dur": 42, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209512, "dur": 2, "ph": "X", "name": "ProcessMessages 157", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209515, "dur": 42, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209561, "dur": 1, "ph": "X", "name": "ProcessMessages 149", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209564, "dur": 52, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209620, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209622, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209672, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209674, "dur": 41, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209720, "dur": 1, "ph": "X", "name": "ProcessMessages 135", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209722, "dur": 44, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209770, "dur": 1, "ph": "X", "name": "ProcessMessages 227", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209772, "dur": 42, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209818, "dur": 1, "ph": "X", "name": "ProcessMessages 139", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209820, "dur": 55, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209879, "dur": 1, "ph": "X", "name": "ProcessMessages 229", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209882, "dur": 45, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209931, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209934, "dur": 43, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209981, "dur": 1, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058209983, "dur": 42, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058210028, "dur": 1, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058210031, "dur": 48, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058210083, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058210086, "dur": 77, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058210167, "dur": 1, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058210170, "dur": 45, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058210218, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058210221, "dur": 48, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058210275, "dur": 1, "ph": "X", "name": "ProcessMessages 147", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058210277, "dur": 54, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058210336, "dur": 2, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058210339, "dur": 50, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058210393, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058210396, "dur": 60, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058210461, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058210464, "dur": 74, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058210541, "dur": 1, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058210544, "dur": 58, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058210606, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058210608, "dur": 48, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058210663, "dur": 2, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058210667, "dur": 61, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058210734, "dur": 2, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058210737, "dur": 50, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058210791, "dur": 2, "ph": "X", "name": "ProcessMessages 90", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058210795, "dur": 701, "ph": "X", "name": "ReadAsync 90", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058211500, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058211503, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058211554, "dur": 2, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058211558, "dur": 51, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058211613, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058211616, "dur": 60, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058211680, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058211683, "dur": 64, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058211751, "dur": 2, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058211754, "dur": 86, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058211849, "dur": 3, "ph": "X", "name": "ProcessMessages 190", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058211854, "dur": 96, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058211954, "dur": 2, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058211958, "dur": 61, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212024, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212027, "dur": 94, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212126, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212128, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212187, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212190, "dur": 63, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212257, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212260, "dur": 49, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212312, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212315, "dur": 41, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212360, "dur": 2, "ph": "X", "name": "ProcessMessages 147", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212364, "dur": 41, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212409, "dur": 1, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212412, "dur": 46, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212461, "dur": 3, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212466, "dur": 44, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212514, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212517, "dur": 41, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212562, "dur": 1, "ph": "X", "name": "ProcessMessages 59", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212565, "dur": 45, "ph": "X", "name": "ReadAsync 59", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212615, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212617, "dur": 37, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212659, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212662, "dur": 45, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212710, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212713, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212758, "dur": 3, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212765, "dur": 47, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212816, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212819, "dur": 92, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212915, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212918, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212967, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058212969, "dur": 45, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213019, "dur": 2, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213022, "dur": 44, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213070, "dur": 1, "ph": "X", "name": "ProcessMessages 139", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213073, "dur": 42, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213120, "dur": 1, "ph": "X", "name": "ProcessMessages 219", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213123, "dur": 43, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213170, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213173, "dur": 44, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213220, "dur": 2, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213223, "dur": 181, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213408, "dur": 1, "ph": "X", "name": "ProcessMessages 167", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213411, "dur": 54, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213469, "dur": 2, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213474, "dur": 37, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213515, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213518, "dur": 43, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213565, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213567, "dur": 42, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213613, "dur": 1, "ph": "X", "name": "ProcessMessages 217", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213615, "dur": 45, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213664, "dur": 2, "ph": "X", "name": "ProcessMessages 210", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213667, "dur": 44, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213715, "dur": 3, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213720, "dur": 65, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213793, "dur": 3, "ph": "X", "name": "ProcessMessages 233", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213799, "dur": 100, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213903, "dur": 2, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213906, "dur": 63, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213974, "dur": 1, "ph": "X", "name": "ProcessMessages 153", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058213977, "dur": 47, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214027, "dur": 1, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214030, "dur": 84, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214119, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214122, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214170, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214173, "dur": 42, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214219, "dur": 1, "ph": "X", "name": "ProcessMessages 133", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214223, "dur": 45, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214271, "dur": 1, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214274, "dur": 49, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214327, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214330, "dur": 44, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214378, "dur": 1, "ph": "X", "name": "ProcessMessages 222", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214381, "dur": 43, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214428, "dur": 2, "ph": "X", "name": "ProcessMessages 120", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214432, "dur": 43, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214479, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214482, "dur": 44, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214530, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214533, "dur": 46, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214582, "dur": 2, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214585, "dur": 45, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214634, "dur": 2, "ph": "X", "name": "ProcessMessages 87", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214638, "dur": 39, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214681, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214683, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214736, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214738, "dur": 46, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214788, "dur": 1, "ph": "X", "name": "ProcessMessages 216", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214792, "dur": 44, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214839, "dur": 2, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214843, "dur": 46, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214892, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214895, "dur": 46, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214945, "dur": 1, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214948, "dur": 46, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058214997, "dur": 1, "ph": "X", "name": "ProcessMessages 216", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215000, "dur": 43, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215048, "dur": 2, "ph": "X", "name": "ProcessMessages 179", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215052, "dur": 44, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215099, "dur": 1, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215102, "dur": 44, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215151, "dur": 1, "ph": "X", "name": "ProcessMessages 223", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215153, "dur": 39, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215196, "dur": 1, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215198, "dur": 55, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215258, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215261, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215308, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215311, "dur": 43, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215358, "dur": 1, "ph": "X", "name": "ProcessMessages 230", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215361, "dur": 44, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215409, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215413, "dur": 45, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215463, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215466, "dur": 103, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215574, "dur": 2, "ph": "X", "name": "ProcessMessages 222", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215578, "dur": 59, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215641, "dur": 2, "ph": "X", "name": "ProcessMessages 480", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215644, "dur": 48, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215696, "dur": 1, "ph": "X", "name": "ProcessMessages 147", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215701, "dur": 43, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215748, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215751, "dur": 43, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215799, "dur": 2, "ph": "X", "name": "ProcessMessages 89", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215802, "dur": 59, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215865, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215867, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215917, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215920, "dur": 43, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215966, "dur": 1, "ph": "X", "name": "ProcessMessages 150", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058215969, "dur": 46, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216019, "dur": 1, "ph": "X", "name": "ProcessMessages 273", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216022, "dur": 43, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216069, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216071, "dur": 43, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216118, "dur": 2, "ph": "X", "name": "ProcessMessages 155", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216122, "dur": 43, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216168, "dur": 1, "ph": "X", "name": "ProcessMessages 219", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216171, "dur": 45, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216219, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216222, "dur": 43, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216269, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216272, "dur": 41, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216317, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216320, "dur": 39, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216363, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216366, "dur": 42, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216412, "dur": 2, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216415, "dur": 60, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216478, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216481, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216530, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216532, "dur": 42, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216578, "dur": 1, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216581, "dur": 44, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216630, "dur": 2, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216633, "dur": 43, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216680, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216683, "dur": 46, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216739, "dur": 1, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216742, "dur": 45, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216791, "dur": 2, "ph": "X", "name": "ProcessMessages 168", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216794, "dur": 45, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216843, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216846, "dur": 44, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216893, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216896, "dur": 43, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216943, "dur": 1, "ph": "X", "name": "ProcessMessages 142", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216946, "dur": 44, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058216996, "dur": 2, "ph": "X", "name": "ProcessMessages 153", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217000, "dur": 47, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217051, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217054, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217102, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217104, "dur": 45, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217153, "dur": 1, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217157, "dur": 42, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217202, "dur": 1, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217206, "dur": 44, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217254, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217257, "dur": 44, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217304, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217307, "dur": 46, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217357, "dur": 2, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217360, "dur": 43, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217407, "dur": 1, "ph": "X", "name": "ProcessMessages 179", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217410, "dur": 46, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217460, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217462, "dur": 38, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217503, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217506, "dur": 45, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217556, "dur": 1, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217558, "dur": 40, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217602, "dur": 1, "ph": "X", "name": "ProcessMessages 78", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217605, "dur": 38, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217648, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217650, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217704, "dur": 2, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217708, "dur": 57, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217769, "dur": 1, "ph": "X", "name": "ProcessMessages 222", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217772, "dur": 45, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217821, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217825, "dur": 46, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217875, "dur": 1, "ph": "X", "name": "ProcessMessages 201", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217878, "dur": 44, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217925, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217928, "dur": 51, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217983, "dur": 1, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058217986, "dur": 46, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218036, "dur": 1, "ph": "X", "name": "ProcessMessages 186", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218039, "dur": 44, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218087, "dur": 1, "ph": "X", "name": "ProcessMessages 165", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218090, "dur": 45, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218138, "dur": 4, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218144, "dur": 40, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218188, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218191, "dur": 41, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218235, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218237, "dur": 47, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218288, "dur": 2, "ph": "X", "name": "ProcessMessages 250", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218292, "dur": 46, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218341, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218344, "dur": 42, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218390, "dur": 1, "ph": "X", "name": "ProcessMessages 191", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218393, "dur": 43, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218439, "dur": 1, "ph": "X", "name": "ProcessMessages 191", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218444, "dur": 45, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218492, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218495, "dur": 45, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218550, "dur": 1, "ph": "X", "name": "ProcessMessages 211", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218553, "dur": 39, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218597, "dur": 1, "ph": "X", "name": "ProcessMessages 109", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218600, "dur": 45, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218649, "dur": 3, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218654, "dur": 39, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218697, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218699, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218749, "dur": 1, "ph": "X", "name": "ProcessMessages 235", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218751, "dur": 40, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218796, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218799, "dur": 62, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218866, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218869, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218924, "dur": 2, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218927, "dur": 45, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218977, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058218980, "dur": 43, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219026, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219029, "dur": 48, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219081, "dur": 1, "ph": "X", "name": "ProcessMessages 153", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219084, "dur": 46, "ph": "X", "name": "ReadAsync 153", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219134, "dur": 1, "ph": "X", "name": "ProcessMessages 285", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219137, "dur": 47, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219187, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219190, "dur": 44, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219238, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219241, "dur": 46, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219291, "dur": 1, "ph": "X", "name": "ProcessMessages 103", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219295, "dur": 46, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219345, "dur": 1, "ph": "X", "name": "ProcessMessages 311", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219348, "dur": 58, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219409, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219412, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219463, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219466, "dur": 44, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219514, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219517, "dur": 43, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219564, "dur": 1, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219567, "dur": 48, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219621, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219624, "dur": 47, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219675, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219677, "dur": 45, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219726, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219729, "dur": 42, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219775, "dur": 1, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219778, "dur": 44, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219826, "dur": 1, "ph": "X", "name": "ProcessMessages 246", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219828, "dur": 45, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219877, "dur": 1, "ph": "X", "name": "ProcessMessages 182", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219880, "dur": 44, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219928, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219931, "dur": 61, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058219997, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220000, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220049, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220052, "dur": 43, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220099, "dur": 1, "ph": "X", "name": "ProcessMessages 219", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220101, "dur": 45, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220150, "dur": 1, "ph": "X", "name": "ProcessMessages 183", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220153, "dur": 47, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220204, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220207, "dur": 45, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220256, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220258, "dur": 46, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220308, "dur": 1, "ph": "X", "name": "ProcessMessages 216", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220312, "dur": 47, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220363, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220366, "dur": 44, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220414, "dur": 1, "ph": "X", "name": "ProcessMessages 165", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220417, "dur": 49, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220471, "dur": 1, "ph": "X", "name": "ProcessMessages 275", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220475, "dur": 38, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220517, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220519, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220563, "dur": 1, "ph": "X", "name": "ProcessMessages 95", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220565, "dur": 44, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220613, "dur": 1, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220617, "dur": 43, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220664, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220667, "dur": 49, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220720, "dur": 1, "ph": "X", "name": "ProcessMessages 155", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220724, "dur": 49, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220776, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220779, "dur": 46, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220829, "dur": 1, "ph": "X", "name": "ProcessMessages 229", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220832, "dur": 46, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220882, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220885, "dur": 46, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220935, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220938, "dur": 40, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220982, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058220985, "dur": 49, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221038, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221041, "dur": 46, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221091, "dur": 2, "ph": "X", "name": "ProcessMessages 207", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221094, "dur": 48, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221146, "dur": 3, "ph": "X", "name": "ProcessMessages 213", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221151, "dur": 55, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221209, "dur": 1, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221212, "dur": 46, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221262, "dur": 1, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221265, "dur": 48, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221317, "dur": 2, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221320, "dur": 48, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221372, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221375, "dur": 47, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221426, "dur": 2, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221429, "dur": 47, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221480, "dur": 2, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221483, "dur": 52, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221544, "dur": 7, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221555, "dur": 76, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221634, "dur": 3, "ph": "X", "name": "ProcessMessages 687", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221640, "dur": 50, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221694, "dur": 2, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221699, "dur": 72, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221775, "dur": 2, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221779, "dur": 52, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221835, "dur": 2, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221839, "dur": 48, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221891, "dur": 2, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221895, "dur": 46, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221945, "dur": 2, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058221948, "dur": 51, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222005, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222008, "dur": 51, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222062, "dur": 2, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222066, "dur": 49, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222120, "dur": 2, "ph": "X", "name": "ProcessMessages 233", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222124, "dur": 47, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222175, "dur": 2, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222179, "dur": 48, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222230, "dur": 4, "ph": "X", "name": "ProcessMessages 217", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222236, "dur": 52, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222292, "dur": 2, "ph": "X", "name": "ProcessMessages 294", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222295, "dur": 46, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222345, "dur": 2, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222348, "dur": 41, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222393, "dur": 1, "ph": "X", "name": "ProcessMessages 157", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222395, "dur": 47, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222447, "dur": 2, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222450, "dur": 56, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222510, "dur": 2, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222513, "dur": 54, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222571, "dur": 2, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222576, "dur": 66, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222646, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222649, "dur": 53, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222706, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222710, "dur": 50, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222764, "dur": 2, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222768, "dur": 50, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222821, "dur": 2, "ph": "X", "name": "ProcessMessages 161", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222825, "dur": 49, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222879, "dur": 2, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222882, "dur": 44, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222931, "dur": 2, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222934, "dur": 47, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222984, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058222988, "dur": 60, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223052, "dur": 2, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223056, "dur": 53, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223113, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223116, "dur": 43, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223164, "dur": 2, "ph": "X", "name": "ProcessMessages 89", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223168, "dur": 48, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223220, "dur": 1, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223222, "dur": 94, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223321, "dur": 2, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223324, "dur": 80, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223409, "dur": 2, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223413, "dur": 64, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223482, "dur": 2, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223485, "dur": 86, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223577, "dur": 2, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223581, "dur": 67, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223652, "dur": 2, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223656, "dur": 52, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223712, "dur": 2, "ph": "X", "name": "ProcessMessages 273", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223716, "dur": 43, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223763, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223766, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223825, "dur": 2, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223829, "dur": 54, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223886, "dur": 2, "ph": "X", "name": "ProcessMessages 264", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223890, "dur": 62, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223956, "dur": 2, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058223960, "dur": 51, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058224016, "dur": 4, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058224022, "dur": 52, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058224078, "dur": 2, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058224081, "dur": 48, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058224134, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058224137, "dur": 47, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058224188, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058224193, "dur": 49, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058224246, "dur": 2, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058224250, "dur": 48, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058224301, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058224305, "dur": 46, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058224355, "dur": 1, "ph": "X", "name": "ProcessMessages 223", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058224358, "dur": 57, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058227539, "dur": 3, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058227545, "dur": 397, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058227946, "dur": 620, "ph": "X", "name": "ProcessMessages 9802", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058228571, "dur": 119, "ph": "X", "name": "ReadAsync 9802", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058228694, "dur": 12, "ph": "X", "name": "ProcessMessages 1152", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058228709, "dur": 63, "ph": "X", "name": "ReadAsync 1152", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058228777, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058228781, "dur": 65, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058228851, "dur": 3, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058228856, "dur": 66, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058228929, "dur": 4, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058228936, "dur": 65, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058229010, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058229020, "dur": 60, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058229089, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058229098, "dur": 83, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058229191, "dur": 7, "ph": "X", "name": "ProcessMessages 109", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058229226, "dur": 89, "ph": "X", "name": "ReadAsync 109", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058229322, "dur": 411, "ph": "X", "name": "ProcessMessages 99", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058229738, "dur": 54, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058229798, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058229818, "dur": 17070, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058246897, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058246902, "dur": 135, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058247044, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058247049, "dur": 214, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058247268, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058247271, "dur": 1304, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058248581, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058248583, "dur": 99, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058248736, "dur": 49, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058248789, "dur": 39, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058249032, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058249135, "dur": 119, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058249257, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058249260, "dur": 2015, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058251333, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058251337, "dur": 135, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058251477, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058251481, "dur": 93, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058251655, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058251658, "dur": 638, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058252323, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058252326, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058252392, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058252504, "dur": 122, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058252630, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058252633, "dur": 234, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058252939, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058252942, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058253035, "dur": 5, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058253100, "dur": 139, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058253301, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058253304, "dur": 6679, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058259988, "dur": 17, "ph": "X", "name": "ProcessMessages 1504", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058260008, "dur": 655, "ph": "X", "name": "ReadAsync 1504", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058260668, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058260671, "dur": 106, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058260782, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058260786, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058260848, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058260852, "dur": 154, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058261010, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058261013, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058261063, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058261068, "dur": 666, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058261741, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058261744, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058261813, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058261817, "dur": 61, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058261882, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058261885, "dur": 78, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058261969, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058261972, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058262041, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058262044, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058262093, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058262097, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058262142, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058262145, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058262282, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058262285, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058262329, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058262332, "dur": 136, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058262472, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058262475, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058262518, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058262522, "dur": 470, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058262997, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058263000, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058263049, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058263051, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058263093, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058263096, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058263142, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058263144, "dur": 119, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058263272, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058263276, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058263340, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058263344, "dur": 54, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058263403, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058263407, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058263456, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058263459, "dur": 468, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058263932, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058263936, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058264000, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058264004, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058264058, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058264062, "dur": 189, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058264255, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058264257, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058264302, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058264305, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058264349, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058264352, "dur": 1421, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058265778, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058265780, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058265831, "dur": 940, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058266775, "dur": 293237, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058560022, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058560027, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058560067, "dur": 2466, "ph": "X", "name": "ProcessMessages 1224", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058562538, "dur": 6353, "ph": "X", "name": "ReadAsync 1224", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058568897, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058568900, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058568949, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058568956, "dur": 2469, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058571429, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058571432, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058571506, "dur": 21, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058571529, "dur": 1679, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058573217, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058573223, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058573306, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058573310, "dur": 125631, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058698953, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058698975, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058699042, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058699048, "dur": 604, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058699657, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058699664, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058699754, "dur": 51, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058699811, "dur": 2177, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058701994, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058701997, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058702041, "dur": 743, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 41616, "tid": 12884901888, "ts": 1751355058702792, "dur": 39265, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 41616, "tid": 2104, "ts": 1751355058764623, "dur": 2670, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 41616, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 41616, "tid": 8589934592, "ts": 1751355058161061, "dur": 158300, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 41616, "tid": 8589934592, "ts": 1751355058319365, "dur": 8, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 41616, "tid": 8589934592, "ts": 1751355058319375, "dur": 2200, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 41616, "tid": 2104, "ts": 1751355058767296, "dur": 19, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 41616, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 41616, "tid": 4294967296, "ts": 1751355058128184, "dur": 616388, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 41616, "tid": 4294967296, "ts": 1751355058135980, "dur": 12985, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 41616, "tid": 4294967296, "ts": 1751355058744879, "dur": 7025, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 41616, "tid": 4294967296, "ts": 1751355058749379, "dur": 160, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 41616, "tid": 4294967296, "ts": 1751355058752044, "dur": 19, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 41616, "tid": 2104, "ts": 1751355058767317, "dur": 17, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751355058185069, "dur": 4180, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751355058189277, "dur": 1759, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751355058191195, "dur": 193, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751355058191388, "dur": 2178, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751355058193663, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ScriptAssemblies"}}, {"pid": 12345, "tid": 0, "ts": 1751355058193737, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_7A1FD51B51344812.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058193803, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_00CD99E311DC019E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058193867, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_BF1A247579C7383B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058193931, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_80B8935D38233098.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058193992, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_16A54265357DDF1F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058194061, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FCC5A8FB58ECD1D2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058194126, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_493844D967613FA0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058194191, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_989B89A6DC960046.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058194250, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_B01BEAEDCAC9F962.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058194319, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_44286934693FF148.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058194381, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_1C55A2A4A7EC33E2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058194453, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5543A9502BBA10BA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058194547, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_9DC68FB86218AF77.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058194608, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_52F12B5AEAC5FC8F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058194666, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_F4165E534DD27516.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058194855, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_921345D5C92C6DDE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058194921, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_030C0AE8F7F2C9CD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058194988, "dur": 206, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_A8A2BEB223D0393B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058195204, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_592CC89A6471077C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058195264, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B52CD1DFCA664AB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058195322, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D447C5E6C0383BF9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058195378, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_970778ACBFA20360.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058195440, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_D3BB5BABC9B6A39A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058195498, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_DC7672C0573B7C85.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058195555, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_129B2BEEEC662028.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058195612, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_7F1C5DFD0AD39F77.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058195669, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_711E053178EA7BF1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058195727, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_2BCC56A3C6071870.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058195784, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_47A61C91F1D08784.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058195842, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_C2E93A20F98E8229.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058195899, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46E217D19A6E01BD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058195967, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8ECA4E4329F1BADB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058196139, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058196202, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_61E5A1A2114B6332.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058196260, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_74513773BD43745E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058196317, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_6FBB1366163B00FB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058196375, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_F24F3CD4D4D128AB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058196448, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E009CCFB4A5E9FFB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058196508, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_6C2CA7D176733A22.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058196567, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_4E756196A062909D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058196627, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BA5431EB9EAB1D55.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058196686, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_DCB00145011AACBC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058196757, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F8AA39E8D5CE00CF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058196822, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_051DE14E9379244A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058196886, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_0660C742CA2CCB37.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058196946, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_C287EB2677BA7F93.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058197005, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_5E52B5CE937E0ED1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058197064, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_6C4C0BE13C75841D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058197122, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_7BFAB029D4D7DE5C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058197214, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1830F8D8229EF0EB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058197274, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_99CD71E244B915CF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058197333, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_00B5CEDD1ED5F4B3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058197399, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B1475218F12198D8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058197459, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_39C615B7DD834A4A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058197518, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_42AEDBADC03A3022.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058197577, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_921E3B2D436CDAE7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058197635, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CB3FD07F3B098D2B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058197693, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_976F9C96B5CDAC08.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058197758, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_889FFFE959BEFFC2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058197816, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_5AC43434136967AD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058197874, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_5E37763FF8B95A1F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058197933, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_173731A5F5D7E041.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058197991, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_7D7B641D38DA242D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058198050, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_ED94C9E717A89F9F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058198116, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_6E64691D4ACC974A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058198175, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D22D11490AEAA9B0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058198328, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8B525A02B9DF9C7E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058198392, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058198452, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_00433992BAF13A73.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058198515, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6678507EC9682BC1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058198574, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_783D7153E7AEE314.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058198632, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C099976D06C71E3D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058198690, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_97D91564DB667E63.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058198748, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_95C124FA2E4BBBB0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058198814, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3C810AF224C3A909.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058198875, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B8F1FBD1DD727BB2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058198933, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_9B1D775C88DF2080.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058198993, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B5DB3F27CCAA63FE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058199054, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E522F23A96C2507B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058199112, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_805FFE03DDD45823.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058199171, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FF5FCB0F465F7311.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058199230, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F275A7F2C1FA929A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058199316, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_71CC72797B2E953C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058199405, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_31922632D84128F5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058199466, "dur": 472, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_20E0F9318BFD3B5B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058199949, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_FD8C0D7AD7503969.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058200045, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_DC79D8DB49767D4F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058200122, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_B563F2CB5E9D696E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058200190, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_963BCDCD478EB63D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058200257, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C5419324AD6BC088.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058200325, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0D29C9963CEF79A6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058200402, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A99F700E950BD818.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058200471, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_54605A4C765A4A77.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058200547, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_67F30B299A5D5154.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058200615, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E0B83A434CA2B946.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058200682, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_BDFB8953D401853F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058200763, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_14A21DFAD1DFC142.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058200837, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D7BFC8363EAC4767.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058200911, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B56095C3DA730DAC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058201015, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_A7B3B5FA99331E00.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058201086, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9F68AD12AC89760A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058201153, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_27EF5DC3D06E8F78.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058201230, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E671E5897665917D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058201303, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B47D5ED0AB2E73A1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058201370, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_800370C755C131C9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058201448, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3EA5AD5E9A933796.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058201516, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D7EC0EF22CAF3612.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058201583, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_09E134ACBD96AA82.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058201658, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E8D1A39870CABAC1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058201726, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BED6F64D074B3174.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058201792, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_10CDAFD975BC0AB7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058201867, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B014F0E06EB9C8B3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058201936, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_438C72DDFEF65815.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058202001, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_4FB16FC3A796B887.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058202072, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_42EBD154127454A5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058202160, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9428A5C8BBBE8A51.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058202229, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_69199FEE963F7F27.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058202298, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058202374, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_2CD7161C662F4679.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058202440, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_5CAA8383FA1D1EAC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058202506, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058202579, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_A3850CF6800E4193.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058202646, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_3AA3364A40DB9F8B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058202719, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058202786, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_7423F2FBC6CF80C4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058202861, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058202940, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058203015, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058203081, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058203148, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058203324, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058203515, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_696347C4732CD601.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058203589, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_39F0A7015032F01B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058203655, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_02726B6CC8A7DF56.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058203721, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9C39811BC6DD0BF6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058203794, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058203865, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_A805A84D9B0C22D9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058203933, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058204015, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058204082, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058204150, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058204242, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058204352, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058204695, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_BDDAB6C3AC1C4D86.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058204767, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058204856, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058204976, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058205047, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058205116, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058205190, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058205341, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_0174E068A5FA251B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058205407, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058205492, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058205560, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058205626, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058205695, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058205774, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058205921, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_A558B9E65539D7D0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058205995, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058206070, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058206173, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058206242, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058206310, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058206389, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058206573, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ref.dll_7B1D397D2193763B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058206648, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058206724, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058206799, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058206866, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058206948, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058207016, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058207186, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_FF44434556825F42.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058207256, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058207333, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058207404, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058207498, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058207566, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058207649, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058207779, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Settings.Editor.ref.dll_8A1461A5C565B4EA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058207856, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058207927, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058208005, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058208072, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058208161, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058208234, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058208366, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.ref.dll_22F664961FC0158D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058208441, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058208519, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058208622, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058208693, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058208765, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058208840, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058209012, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.ref.dll_39D3266F6029657A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058209096, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058209169, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058209248, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058209316, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058209393, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058209464, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058209602, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_02929A3ECA791895.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058209708, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058209782, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058209849, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058209924, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058209996, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058210076, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058210206, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_16F8EA6B67022FB5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058210280, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058210355, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058210432, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058210501, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058210571, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058210643, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058210770, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.ref.dll_BF8650D11F8E519A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058210845, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058210944, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058211014, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058211085, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058211156, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058211226, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058211395, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.Editor.ref.dll_514B12BC0D1D8DB7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058211464, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058211539, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058211617, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058211688, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058211771, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058211842, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058212595, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.ref.dll_06973127A197C649.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058212690, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058212785, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058212860, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058212932, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058213005, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058213066, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058213263, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.Editor.ref.dll_CE7BFB6B1BFC18F8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058213331, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058213411, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058213488, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058213557, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058213640, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058213716, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058213864, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_900B338E31D9525C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058214064, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058214144, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058214212, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058214289, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058214357, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058214432, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058214564, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.Editor.ref.dll_DC1879B0E64A8F15.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058214674, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058214748, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058214817, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058214892, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058214969, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058215081, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058215267, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.EditorCoroutines.Editor.ref.dll_1315E0A407C70415.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058215349, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058215425, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058215501, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058215570, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058215636, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058215708, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058215834, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ForUI.ref.dll_8456852F39E72DEA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058215910, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058215982, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058216050, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058216118, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058216186, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058216254, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058216409, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_FC2D3B4057A989C4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058216482, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058216553, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058216629, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058216698, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058216759, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058216870, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058217017, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Performance.Profile-Analyzer.Editor.ref.dll_8B44F821019897E5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058217099, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058217174, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058217246, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058217313, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058217391, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058217496, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058217632, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_DA88E6A3E9EB6155.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058217703, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058217776, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058217853, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058217926, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058217994, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058218071, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058218202, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.ref.dll_C6D619E6129B87D3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058218276, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058218348, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058218425, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058218495, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058218564, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058218658, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058218798, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_D0BA3615D322EF6C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058218874, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058218950, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058219019, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058219101, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058219174, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058219244, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058219379, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_17F688653313AA4C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058219444, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058219518, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058219586, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058219663, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058219742, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058219844, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058219995, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_EE1755C0672F8EE2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058220060, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058220152, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058220220, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058220292, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058220362, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058220433, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058220561, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Shared.Editor.ref.dll_6CA31C4FA5286AB1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058220635, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058220708, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058220776, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058220849, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058220945, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058221015, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058221148, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_0FE027DDD6F41767.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058221221, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058221292, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058221360, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058221430, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058221498, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058221567, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058221704, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751355058221770, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751355058221853, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751355058222170, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751355058222333, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751355058222628, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751355058222753, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751355058222891, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751355058222947, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751355058223120, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751355058223417, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751355058223480, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058223710, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751355058223903, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751355058224011, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751355058224238, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058224499, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751355058224609, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058224671, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751355058224781, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751355058224951, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751355058225096, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751355058225205, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751355058225313, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751355058225422, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751355058193641, "dur": 32870, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751355058226526, "dur": 474382, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751355058700910, "dur": 646, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751355058701557, "dur": 538, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751355058702096, "dur": 54, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751355058702151, "dur": 115, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751355058702344, "dur": 71, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751355058702662, "dur": 80, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751355058702894, "dur": 213, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751355058703153, "dur": 18839, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751355058192334, "dur": 34204, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058226557, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_7A1FD51B51344812.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751355058226846, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058227035, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46E217D19A6E01BD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751355058227157, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058227529, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751355058227641, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058227787, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0D29C9963CEF79A6.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751355058228021, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058228171, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_438C72DDFEF65815.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751355058228344, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058228453, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058228548, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058228783, "dur": 1422, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751355058230207, "dur": 2524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058232732, "dur": 1217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058233950, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058234604, "dur": 2658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058237263, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058238033, "dur": 806, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_UpdateManager.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751355058240149, "dur": 557, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_SubMesh.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751355058238033, "dur": 4027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058243190, "dur": 602, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Views\\Diff\\DiffTreeViewMenu.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751355058242060, "dur": 3881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058245941, "dur": 2457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058248398, "dur": 1301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058249700, "dur": 2837, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058252538, "dur": 944, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058253483, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058253589, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751355058253910, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058254035, "dur": 862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751355058254898, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058255205, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058255576, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751355058255935, "dur": 817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751355058256753, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058257102, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058257188, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058257568, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058257714, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058257861, "dur": 963, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058258825, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058259037, "dur": 926, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058259963, "dur": 1903, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058261866, "dur": 1061, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058262928, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058263180, "dur": 1048, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058264228, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058264502, "dur": 623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058265127, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058265198, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751355058265414, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058265477, "dur": 304284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751355058569762, "dur": 131155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058192409, "dur": 34151, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058226565, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_00CD99E311DC019E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751355058226949, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058227038, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8ECA4E4329F1BADB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751355058227160, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058227259, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_C287EB2677BA7F93.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751355058227393, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058227583, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3C810AF224C3A909.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751355058227734, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058228043, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_800370C755C131C9.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751355058228164, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058228402, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058228491, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751355058228558, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058228668, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751355058228840, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058228933, "dur": 501, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751355058229438, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058229540, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058229671, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058229806, "dur": 636, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751355058230444, "dur": 1981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058232669, "dur": 612, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center\\Editor\\Questionnaire\\PresetData.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751355058233715, "dur": 809, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center\\Editor\\MultiplayerCenterWindow\\UI\\ViewUtils.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751355058235580, "dur": 530, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center\\Editor\\MultiplayerCenterWindow\\UI\\QuestionSection.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751355058232426, "dur": 4138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058238009, "dur": 801, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Analysis\\AnalyserAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751355058236565, "dur": 2421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058241077, "dur": 537, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Plugins\\XInput\\IXboxOneRumble.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751355058238987, "dur": 2739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058242073, "dur": 570, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Views\\PendingChanges\\PendingMergeLinks\\MergeLinksListView.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751355058241727, "dur": 3637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058247664, "dur": 683, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework\\UnityEditor.TestRunner\\TestRunner\\Utils\\IEditorCompilationInterfaceProxy.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751355058245365, "dur": 3400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058248766, "dur": 925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058249692, "dur": 2685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058252378, "dur": 1101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058253480, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058253650, "dur": 1511, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058255275, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058255645, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058255778, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058256032, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058256389, "dur": 714, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058257104, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058257579, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058257708, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751355058257995, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058258161, "dur": 692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751355058258855, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058259040, "dur": 939, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058259979, "dur": 1898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058261877, "dur": 1069, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058262946, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058263184, "dur": 1015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058264200, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058264486, "dur": 652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058265139, "dur": 304599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751355058569739, "dur": 131190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058192870, "dur": 33826, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058226701, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_B01BEAEDCAC9F962.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751355058226894, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058227006, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_2BCC56A3C6071870.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751355058227149, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058227321, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_39C615B7DD834A4A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751355058227436, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058227519, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8B525A02B9DF9C7E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751355058227694, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058227796, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_54605A4C765A4A77.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751355058228032, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058228144, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_10CDAFD975BC0AB7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751355058228273, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058228360, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_A3850CF6800E4193.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751355058228423, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058228499, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751355058228626, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058228829, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751355058228950, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058229054, "dur": 531, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751355058229588, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058229674, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058229753, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751355058229848, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058229955, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058230076, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058230139, "dur": 156, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751355058230297, "dur": 2279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058232576, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058233198, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058233904, "dur": 637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058235962, "dur": 930, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Meta\\RootMetadata.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751355058234542, "dur": 2807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058237350, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058238007, "dur": 829, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Audio\\AudioTrack.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751355058239548, "dur": 610, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\AssetUpgrade\\TrackUpgrade.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751355058240225, "dur": 554, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\AssetUpgrade\\ClipUpgrade.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751355058238007, "dur": 5073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058245212, "dur": 515, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Inspector\\InspectorAssetSelection.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751355058243081, "dur": 3167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058246248, "dur": 1805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058248178, "dur": 1500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058249735, "dur": 2686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058252421, "dur": 1081, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058253502, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058253624, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751355058254094, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058254192, "dur": 854, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751355058255047, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058255278, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058255351, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058255649, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058255799, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058256023, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058256405, "dur": 715, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058257121, "dur": 471, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058257593, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058257726, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058257861, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058257924, "dur": 908, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058258833, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058259000, "dur": 935, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058259936, "dur": 1901, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058261853, "dur": 1064, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058262917, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058263168, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058263243, "dur": 969, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058264213, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058264491, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058265169, "dur": 304596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751355058569766, "dur": 131188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058192498, "dur": 34086, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058226589, "dur": 623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_80B8935D38233098.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751355058227213, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058227317, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B1475218F12198D8.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751355058227428, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058227511, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D22D11490AEAA9B0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751355058227605, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058227747, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_20E0F9318BFD3B5B.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751355058227971, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058228130, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E8D1A39870CABAC1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751355058228376, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058228615, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058228737, "dur": 476, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751355058229223, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058229336, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751355058229448, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058229577, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058229658, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751355058229750, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058229830, "dur": 282, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751355058230114, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751355058230173, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058231018, "dur": 579, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\treeview\\TimelineDragging.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751355058230248, "dur": 2180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058232429, "dur": 1726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058234156, "dur": 2979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058237136, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058237954, "dur": 874, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnCollisionEnter2DMessageListener.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751355058237548, "dur": 1598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058239147, "dur": 529, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Events\\TextEvent.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751355058239147, "dur": 3000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058242148, "dur": 2725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058244874, "dur": 2641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058247516, "dur": 1382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058248899, "dur": 871, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058249771, "dur": 2608, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058252380, "dur": 1108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058253489, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058253618, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751355058254158, "dur": 1036, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058255211, "dur": 953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751355058256165, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058256382, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058256448, "dur": 681, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058257129, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058257573, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058257719, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058257856, "dur": 970, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058258827, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058259035, "dur": 924, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058259960, "dur": 1873, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058261906, "dur": 1026, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058262932, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058263228, "dur": 990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058264218, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058264497, "dur": 647, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058265144, "dur": 304663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751355058569808, "dur": 131140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058192597, "dur": 34005, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058226610, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_16A54265357DDF1F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751355058226876, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058227046, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751355058227153, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058227224, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751355058227344, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_42AEDBADC03A3022.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751355058227530, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058227678, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_71CC72797B2E953C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751355058227988, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058228081, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D7EC0EF22CAF3612.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751355058228184, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058228370, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058228452, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751355058228569, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058228655, "dur": 589, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751355058229288, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058229425, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058229533, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058229642, "dur": 270, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751355058229914, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751355058229982, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058230136, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058231217, "dur": 2089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058233307, "dur": 1213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058234521, "dur": 3159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058238031, "dur": 802, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\fsData.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751355058237681, "dur": 1480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058239231, "dur": 617, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Editor\\UITKAssetEditor\\ReactiveProperty.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751355058239162, "dur": 3919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058244910, "dur": 572, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\GetRelativePath.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751355058243082, "dur": 3011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058246093, "dur": 2224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058248317, "dur": 1385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058249702, "dur": 2788, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058252503, "dur": 974, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058253478, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058253597, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751355058254134, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058254311, "dur": 1506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751355058255818, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058256013, "dur": 232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058256256, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751355058256617, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058256682, "dur": 2104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751355058258788, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058258996, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058259079, "dur": 875, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058259955, "dur": 1893, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058261899, "dur": 1023, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058262923, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058263174, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058263252, "dur": 968, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058264221, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058264499, "dur": 651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058265151, "dur": 304605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751355058569757, "dur": 131184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058192693, "dur": 33932, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058226633, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FCC5A8FB58ECD1D2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751355058226852, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058226972, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_7F1C5DFD0AD39F77.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751355058227078, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058227267, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_5E52B5CE937E0ED1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751355058227382, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058227756, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_DC79D8DB49767D4F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751355058227961, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058228063, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3EA5AD5E9A933796.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751355058228177, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058228315, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_69199FEE963F7F27.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751355058228428, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058228523, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751355058228574, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058228787, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058228940, "dur": 524, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751355058229467, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058229637, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058229717, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751355058229877, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751355058229958, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058230033, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058230116, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751355058230212, "dur": 2438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058232651, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058233305, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058234034, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058234618, "dur": 2793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058238044, "dur": 770, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Macros\\IMacro.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751355058237411, "dur": 1457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058238869, "dur": 2541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058241411, "dur": 3623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058245035, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058247257, "dur": 922, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework\\UnityEditor.TestRunner\\TestRun\\Tasks\\Events\\RunFinishedInvocationEvent.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751355058245646, "dur": 2763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058248410, "dur": 1298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058249709, "dur": 2741, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058252450, "dur": 1034, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058253485, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058253594, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751355058254092, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058254182, "dur": 1366, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751355058255549, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058255724, "dur": 630, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058256423, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751355058256713, "dur": 955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751355058257669, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058257865, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058257940, "dur": 890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058258830, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058259048, "dur": 894, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058259943, "dur": 1911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058261855, "dur": 1070, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058262926, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058263242, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751355058263550, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058263634, "dur": 690, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751355058264325, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058264490, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058264560, "dur": 616, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058265176, "dur": 304583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751355058569760, "dur": 131186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058192738, "dur": 33925, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058226672, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_493844D967613FA0.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751355058226977, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058227075, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_6FBB1366163B00FB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751355058227187, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058227292, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1830F8D8229EF0EB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751355058227413, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058227501, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_6E64691D4ACC974A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751355058227653, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058227729, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_6E64691D4ACC974A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751355058227857, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_A7B3B5FA99331E00.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751355058228420, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058228532, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751355058228687, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058228771, "dur": 500, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751355058229286, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058229363, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751355058229454, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058229617, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058229709, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058229774, "dur": 237, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751355058230031, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058230123, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751355058230185, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058230259, "dur": 2035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058232295, "dur": 1867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058234163, "dur": 3743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058238025, "dur": 734, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Collections\\IMergedCollection.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751355058237906, "dur": 1317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058239224, "dur": 2448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058241673, "dur": 2493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058244167, "dur": 943, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Editor\\UGUI\\UI\\PrefabLayoutRebuilder.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751355058246462, "dur": 872, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Editor\\UGUI\\UI\\ContentSizeFitterEditor.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751355058244167, "dur": 3720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058247888, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058248702, "dur": 1037, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058249740, "dur": 2718, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058252459, "dur": 1055, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058253514, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058253637, "dur": 1533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058255171, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751355058255283, "dur": 345, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058255788, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058256017, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058256430, "dur": 663, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058257143, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058257593, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058257735, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058257866, "dur": 952, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058258818, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058259060, "dur": 890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058259952, "dur": 1927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058261879, "dur": 1059, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058262939, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058263197, "dur": 1020, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058264217, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058264494, "dur": 634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058265129, "dur": 57628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058325063, "dur": 438, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 7, "ts": 1751355058322759, "dur": 2746, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058325506, "dur": 244265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751355058569773, "dur": 131151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058192810, "dur": 33871, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058226687, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_989B89A6DC960046.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751355058226842, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058227143, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_F24F3CD4D4D128AB.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751355058227247, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058227348, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_921E3B2D436CDAE7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751355058227450, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058227624, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B5DB3F27CCAA63FE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751355058227708, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058227769, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B5DB3F27CCAA63FE.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751355058227846, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D7BFC8363EAC4767.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751355058228308, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058228407, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_02726B6CC8A7DF56.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751355058228524, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058228635, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058228870, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058228946, "dur": 501, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751355058229451, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058229555, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058229659, "dur": 233, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751355058229894, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751355058229959, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058230042, "dur": 353, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751355058230396, "dur": 517, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Actions\\Menus\\TimelineContextMenu.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751355058230396, "dur": 3040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058233437, "dur": 811, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058236740, "dur": 2098, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Plugins\\ProjectSettingAttribute.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751355058234249, "dur": 4589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058239868, "dur": 517, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_ListPool.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751355058238839, "dur": 2885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058243669, "dur": 597, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Views\\Welcome\\DownloadAndInstallOperation.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751355058241725, "dur": 3824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058245550, "dur": 2498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058248056, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058248222, "dur": 1475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058249698, "dur": 2816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058252514, "dur": 972, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058253487, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058253662, "dur": 1510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058255186, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058255327, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058255650, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058255767, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058256016, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058256410, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058256721, "dur": 391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058257112, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058257610, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058257717, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058257863, "dur": 990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058258853, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058259015, "dur": 933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058259949, "dur": 1900, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058261856, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058261918, "dur": 994, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058262914, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058263172, "dur": 1042, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058264214, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058264484, "dur": 634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058265177, "dur": 304571, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751355058569750, "dur": 131149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058192444, "dur": 34127, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058226576, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_BF1A247579C7383B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751355058226824, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058226969, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_129B2BEEEC662028.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751355058227052, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058227247, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_051DE14E9379244A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751355058227359, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058227547, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6678507EC9682BC1.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751355058227667, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058227763, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_B563F2CB5E9D696E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751355058227977, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058228323, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751355058228432, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058228524, "dur": 615, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751355058229146, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058229219, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058229269, "dur": 478, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751355058229750, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751355058229847, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058229969, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058230046, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058230113, "dur": 362, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751355058230477, "dur": 2330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058232808, "dur": 1486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058236242, "dur": 531, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Plugins\\Plugin.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751355058236773, "dur": 531, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Plugins\\IPluginModule.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751355058234294, "dur": 3263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058238046, "dur": 739, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Events\\IGraphEventHandler.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751355058237558, "dur": 1388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058238947, "dur": 2786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058242434, "dur": 707, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Views\\PendingChanges\\Dialogs\\CheckinConflictsDialog.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751355058243339, "dur": 511, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Views\\PendingChanges\\ChangelistTreeViewItem.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751355058243850, "dur": 779, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Views\\PendingChanges\\Changelists\\MoveToChangelistMenuBuilder.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751355058244696, "dur": 995, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Views\\PendingChanges\\ChangeCategoryTreeViewItem.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751355058241734, "dur": 5426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058247161, "dur": 1406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058248568, "dur": 1138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058249707, "dur": 2761, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058252469, "dur": 1012, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058253481, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058253614, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751355058253940, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058254019, "dur": 1502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751355058255523, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058255783, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058255841, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058256021, "dur": 411, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058256432, "dur": 677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058257109, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058257567, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058257722, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058257890, "dur": 948, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058258838, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058259042, "dur": 915, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058259958, "dur": 1909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058261868, "dur": 1051, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058262920, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058263176, "dur": 1027, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058264204, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058264533, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058265166, "dur": 304690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751355058569856, "dur": 131102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058192932, "dur": 33780, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058226718, "dur": 826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_44286934693FF148.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751355058227545, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058227634, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E522F23A96C2507B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751355058227744, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058227940, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9F68AD12AC89760A.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751355058228051, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058228163, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B014F0E06EB9C8B3.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751355058228324, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058228399, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9C39811BC6DD0BF6.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751355058228494, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058228569, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751355058228794, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058228867, "dur": 505, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751355058229375, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751355058229433, "dur": 399, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058229832, "dur": 333, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751355058230167, "dur": 2658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058232826, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058234138, "dur": 1832, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058236395, "dur": 541, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Dependencies\\ReorderableList\\GenericListAdaptor.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751355058238016, "dur": 809, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Context\\IGraphContext.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751355058235971, "dur": 3327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058239299, "dur": 3273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058243622, "dur": 600, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Tool\\IsExeVersion.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751355058244432, "dur": 500, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Tool\\BringWindowToFront.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751355058242573, "dur": 3156, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058245730, "dur": 2536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058248266, "dur": 1414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058249681, "dur": 2689, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058252371, "dur": 1119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058253491, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058253658, "dur": 1547, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058255205, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058255272, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058255639, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058255769, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058256008, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058256414, "dur": 702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058257116, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058257570, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058257710, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058257865, "dur": 947, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058258892, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058259028, "dur": 910, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058259938, "dur": 1919, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058261858, "dur": 1052, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058262911, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058263178, "dur": 1014, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058264243, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058264536, "dur": 620, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058265156, "dur": 304595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751355058569752, "dur": 131181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058193032, "dur": 33696, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058226733, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_1C55A2A4A7EC33E2.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751355058226911, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058227014, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_47A61C91F1D08784.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751355058227141, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058227273, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_6C4C0BE13C75841D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751355058227402, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058227561, "dur": 218, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_6C4C0BE13C75841D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751355058227789, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A99F700E950BD818.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751355058228017, "dur": 741, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058228759, "dur": 527, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A99F700E950BD818.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751355058229316, "dur": 545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751355058229863, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058229979, "dur": 19445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751355058249427, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058249691, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058249807, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751355058250123, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058250196, "dur": 1860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751355058252057, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058252381, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058252488, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751355058252674, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058252731, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751355058253333, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058253474, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058253591, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751355058253973, "dur": 1247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058255229, "dur": 2155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751355058257385, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058257583, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058257696, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751355058258015, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058258076, "dur": 1653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751355058259730, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058259939, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058260011, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751355058260192, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058260293, "dur": 1423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751355058261717, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058261874, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058261946, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751355058262174, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751355058262776, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058262915, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058262981, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058263182, "dur": 1022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058264205, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058264479, "dur": 668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058265148, "dur": 304593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751355058569743, "dur": 131159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058193056, "dur": 33687, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058226749, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5543A9502BBA10BA.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751355058226929, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058227023, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_C2E93A20F98E8229.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751355058227157, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058227406, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_5E37763FF8B95A1F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751355058227516, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058227641, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_805FFE03DDD45823.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751355058227748, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058227959, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_27EF5DC3D06E8F78.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751355058228112, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058228242, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058228329, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751355058228391, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058228476, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058228596, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058228672, "dur": 451, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751355058229127, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058229210, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058229328, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751355058229447, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058229594, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058229694, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751355058229869, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058229950, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751355058230032, "dur": 233, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751355058230266, "dur": 1957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058232359, "dur": 911, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider\\Rider\\Editor\\Util\\FileSystemUtil.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751355058232224, "dur": 2099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058236239, "dur": 1512, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqGraphs\\Changelog_1_2_4.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751355058234324, "dur": 3679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058238004, "dur": 804, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Events\\SignalTrack.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751355058238004, "dur": 2724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058241914, "dur": 507, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Editor\\Analytics\\InputExitPlayModeAnalytic.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751355058243187, "dur": 543, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Devices\\TrackedDevice.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751355058244346, "dur": 641, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Devices\\Precompiled\\FastMouse.partial.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751355058240731, "dur": 4440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058245171, "dur": 2095, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058247267, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058248548, "dur": 1203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058249751, "dur": 2650, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058252402, "dur": 1102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058253504, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058253629, "dur": 1537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058255204, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058255277, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058255648, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058255777, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058256019, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058256409, "dur": 688, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058257162, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058257587, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058257738, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058257875, "dur": 964, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058258840, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058259023, "dur": 907, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058259978, "dur": 1905, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058261883, "dur": 1051, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058262934, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058263225, "dur": 981, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058264206, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058264483, "dur": 647, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058265131, "dur": 60403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058325536, "dur": 244233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751355058569769, "dur": 131152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058193110, "dur": 33649, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058226765, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_9DC68FB86218AF77.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751355058226910, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058226998, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_711E053178EA7BF1.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751355058227137, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058227261, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_711E053178EA7BF1.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751355058227354, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CB3FD07F3B098D2B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751355058227445, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058227562, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C099976D06C71E3D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751355058227673, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058227770, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_963BCDCD478EB63D.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751355058228115, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058228222, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751355058228300, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058228372, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_3AA3364A40DB9F8B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751355058228429, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058228516, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058228600, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058228817, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058228887, "dur": 504, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751355058229434, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058229620, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058229755, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058229836, "dur": 349, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751355058231064, "dur": 649, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio\\Editor\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751355058230188, "dur": 2382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058232571, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058233201, "dur": 979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058234962, "dur": 557, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Utilities\\FrameLimiterUtility.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751355058234181, "dur": 2443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058236624, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058237022, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058238034, "dur": 736, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnMouseExitMessageListener.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751355058237513, "dur": 1333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058238847, "dur": 2086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058240933, "dur": 2927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058246130, "dur": 679, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\AssetMenu\\AssetMenuOperations.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751355058243861, "dur": 3019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058246880, "dur": 1731, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058248612, "dur": 1108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058249721, "dur": 2811, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058252533, "dur": 959, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058253493, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058253639, "dur": 1543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058255274, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058255646, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058255766, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058256018, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058256391, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058256704, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058257119, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058257628, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058257709, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058257854, "dur": 844, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058258742, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058258842, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058259018, "dur": 962, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058259980, "dur": 1910, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058261890, "dur": 1045, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058262936, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058263216, "dur": 1002, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058264219, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058264510, "dur": 631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058265141, "dur": 304686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751355058569828, "dur": 131087, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058193188, "dur": 33595, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058226792, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_52F12B5AEAC5FC8F.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751355058227038, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058227252, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_0660C742CA2CCB37.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751355058227361, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058227551, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_783D7153E7AEE314.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751355058227661, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058227733, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_783D7153E7AEE314.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751355058227821, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E0B83A434CA2B946.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751355058228102, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058228192, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_42EBD154127454A5.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751355058228300, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058228386, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058228482, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058228572, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058228828, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058228913, "dur": 497, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751355058229413, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058229501, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058229600, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058229723, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058229794, "dur": 234, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751355058230032, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058230104, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751355058230178, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058230244, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751355058230365, "dur": 1964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058232330, "dur": 1332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058233663, "dur": 817, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058234481, "dur": 3220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058238042, "dur": 856, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsReflectedConverter.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751355058237702, "dur": 1494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058239197, "dur": 2704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058243532, "dur": 504, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Views\\Merge\\Developer\\DirectoryConflicts\\MoveDeleteMenu.cs"}}, {"pid": 12345, "tid": 14, "ts": 1751355058241902, "dur": 3059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058244962, "dur": 1390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058246353, "dur": 1595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058247949, "dur": 111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058248109, "dur": 66, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058248175, "dur": 1513, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058249690, "dur": 2676, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058252367, "dur": 1099, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058253522, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058253608, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751355058254067, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058254138, "dur": 901, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751355058255040, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058255210, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058255322, "dur": 332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058255654, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058255720, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058255776, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058256009, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058256402, "dur": 711, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058257114, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058257589, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058257729, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058257855, "dur": 967, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058258823, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058258998, "dur": 946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058259947, "dur": 1895, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058261856, "dur": 1074, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058262931, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058263221, "dur": 977, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058264198, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058264538, "dur": 598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058265137, "dur": 304637, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751355058569775, "dur": 131186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058193254, "dur": 33545, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058226803, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_F4165E534DD27516.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751355058226967, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058227063, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_74513773BD43745E.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751355058227182, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058227285, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_7BFAB029D4D7DE5C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751355058227401, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058227488, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_7D7B641D38DA242D.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751355058227646, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058227818, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_67F30B299A5D5154.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751355058228041, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058228138, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BED6F64D074B3174.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751355058228320, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058228622, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BED6F64D074B3174.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751355058228774, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058228847, "dur": 477, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 15, "ts": 1751355058229327, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751355058229431, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058229514, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058229629, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058229737, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058229803, "dur": 482, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751355058230287, "dur": 2250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058232538, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058233222, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058233932, "dur": 659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058234780, "dur": 834, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Interface\\Fuzzy\\FuzzyWindow.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751355058234592, "dur": 3145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058238038, "dur": 771, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Collections\\WatchedList.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751355058237737, "dur": 1397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058241187, "dur": 937, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\InputAnalytics.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751355058239134, "dur": 3092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058242227, "dur": 3690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058245920, "dur": 527, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework\\UnityEditor.TestRunner\\TestLaunchers\\Helpers\\FilePathMetaInfo.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751355058245918, "dur": 2221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058248171, "dur": 1523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058249694, "dur": 2667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058252404, "dur": 1102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058253507, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058253636, "dur": 1541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058255207, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058255274, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058255635, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058255819, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058256011, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058256392, "dur": 715, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058257108, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058257577, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058257740, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058257872, "dur": 944, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058258817, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058258994, "dur": 962, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058259956, "dur": 1879, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058261892, "dur": 1037, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058262930, "dur": 276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058263206, "dur": 1005, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058264212, "dur": 280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058264493, "dur": 664, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058265158, "dur": 304587, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751355058569746, "dur": 131158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058193698, "dur": 33181, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058226885, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B52CD1DFCA664AB3.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751355058227103, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058227269, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B52CD1DFCA664AB3.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751355058227425, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_173731A5F5D7E041.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751355058227549, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058227649, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FF5FCB0F465F7311.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751355058227758, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058227967, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E671E5897665917D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751355058228084, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058228217, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058228392, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751355058228572, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058228693, "dur": 484, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751355058229183, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058229330, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751355058229436, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058229568, "dur": 293, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751355058229862, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751355058229938, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058229990, "dur": 255, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751355058230977, "dur": 650, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Utilities\\FrameRateDisplayUtility.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751355058230247, "dur": 2645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058232893, "dur": 1340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058234234, "dur": 2227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058236462, "dur": 526, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Context\\GraphContextExtensionAttribute.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751355058238043, "dur": 788, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Assignment\\IAssigner.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751355058236462, "dur": 2510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058238973, "dur": 2263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058241237, "dur": 2784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058245230, "dur": 559, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Editor\\UGUI\\UI\\SelfControllerEditor.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751355058244022, "dur": 3007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058247029, "dur": 2079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058249109, "dur": 607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058249717, "dur": 2723, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058252440, "dur": 1069, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058253510, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058253642, "dur": 1521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058255170, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058255276, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058255638, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058255772, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058256013, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058256421, "dur": 711, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058257132, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058257580, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058257707, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058257869, "dur": 958, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058258828, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058259049, "dur": 918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058259968, "dur": 1896, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058261864, "dur": 1057, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058262921, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058263174, "dur": 1033, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058264208, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058264531, "dur": 601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058265133, "dur": 304603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058569737, "dur": 4628, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751355058574411, "dur": 126555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058193773, "dur": 33121, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058226900, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D447C5E6C0383BF9.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751355058227068, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058227236, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D447C5E6C0383BF9.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751355058227307, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_00B5CEDD1ED5F4B3.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751355058227408, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058227495, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_ED94C9E717A89F9F.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751355058227607, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058227695, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_31922632D84128F5.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751355058227954, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058228123, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_09E134ACBD96AA82.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751355058228639, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058228772, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058228857, "dur": 486, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1751355058229347, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058229416, "dur": 346, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1751355058229764, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751355058229867, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058229927, "dur": 298, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751355058230227, "dur": 2146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058232374, "dur": 1513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058233887, "dur": 646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058234534, "dur": 3019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058238029, "dur": 782, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Graphs\\IGraphDebugData.cs"}}, {"pid": 12345, "tid": 17, "ts": 1751355058237554, "dur": 1578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058241653, "dur": 556, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Plugins\\EnhancedTouch\\EnhancedTouchSupport.cs"}}, {"pid": 12345, "tid": 17, "ts": 1751355058239133, "dur": 3326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058244893, "dur": 576, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\UI\\CooldownWindowDelayer.cs"}}, {"pid": 12345, "tid": 17, "ts": 1751355058242459, "dur": 3385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058245844, "dur": 2390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058248235, "dur": 1441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058249681, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058249745, "dur": 2666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058252411, "dur": 1065, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058253477, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058253637, "dur": 486, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751355058254124, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058254206, "dur": 822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751355058255029, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058255299, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058255367, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058255640, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058255773, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058256072, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058256390, "dur": 712, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058257194, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058257575, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058257720, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751355058257984, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058258045, "dur": 612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751355058258658, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058258827, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058258898, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058259012, "dur": 948, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058259961, "dur": 1899, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058261861, "dur": 1063, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058262924, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058263188, "dur": 1007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058264196, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058264487, "dur": 672, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058265160, "dur": 304603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751355058569764, "dur": 131249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058193491, "dur": 33356, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058226853, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_A8A2BEB223D0393B.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751355058227013, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058227182, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_4E756196A062909D.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751355058227277, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058227362, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_889FFFE959BEFFC2.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751355058227456, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058227535, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_00433992BAF13A73.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751355058227644, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058227834, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_14A21DFAD1DFC142.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751355058228087, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058228183, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_4FB16FC3A796B887.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751355058228332, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058228420, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_696347C4732CD601.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751355058228527, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058228660, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058228738, "dur": 424, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751355058229166, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058229334, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058229441, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058229586, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1751355058229696, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058229772, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751355058229882, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058229984, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058230049, "dur": 357, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751355058232117, "dur": 535, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Editor\\TMP\\TMP_ColorGradientEditor.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751355058230408, "dur": 2502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058232911, "dur": 1268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058236907, "dur": 518, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Utilities\\ProgressUtility.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751355058234180, "dur": 3433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058238018, "dur": 733, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Ensure\\EnsureThat.NullableValueTypes.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751355058237614, "dur": 1390, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058239005, "dur": 4166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058244533, "dur": 748, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Developer\\UpdateReport\\UpdateReportDialog.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751355058243171, "dur": 3894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058247066, "dur": 1575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058248642, "dur": 1043, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058249686, "dur": 2682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058252369, "dur": 1150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058253520, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058253651, "dur": 1563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058255314, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058255639, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058255775, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058256009, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058256157, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058256411, "dur": 713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058257125, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058257584, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058257706, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058257791, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058258091, "dur": 729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058258821, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058258996, "dur": 944, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058259941, "dur": 1899, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058261858, "dur": 1089, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058262948, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058263193, "dur": 996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058264250, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058264521, "dur": 621, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058265143, "dur": 304586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751355058569768, "dur": 130250, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 18, "ts": 1751355058569733, "dur": 130288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 18, "ts": 1751355058700056, "dur": 769, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 19, "ts": 1751355058193345, "dur": 33473, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058226823, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_921345D5C92C6DDE.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751355058226964, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058227055, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_61E5A1A2114B6332.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751355058227182, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058227296, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_99CD71E244B915CF.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751355058227477, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058227553, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_99CD71E244B915CF.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751355058227853, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B56095C3DA730DAC.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751355058228274, "dur": 473, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058228748, "dur": 360, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B56095C3DA730DAC.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751355058229122, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058229232, "dur": 731, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1751355058229966, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058230133, "dur": 536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058230670, "dur": 2333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058233004, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058234057, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058235072, "dur": 656, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Graph\\LudiqGraphsEditorUtility.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751355058234736, "dur": 2760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058238020, "dur": 748, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Listeners\\UIInterfaces\\UnityOnCancelMessageListener.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751355058237497, "dur": 1482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058240079, "dur": 589, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Utilities\\DynamicBitfield.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751355058238980, "dur": 3393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058243570, "dur": 511, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\UI\\Progress\\DrawProgressForOperations.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751355058244081, "dur": 724, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\UI\\Progress\\DrawProgressForDialogs.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751355058242374, "dur": 3916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058246291, "dur": 1624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058248057, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058248243, "dur": 1439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058249683, "dur": 2689, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058252373, "dur": 1096, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058253469, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058253636, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751355058254066, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058254167, "dur": 1272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1751355058255441, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058255639, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058255809, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058256025, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058256415, "dur": 726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058257141, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058257590, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058257731, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058257876, "dur": 955, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058258832, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058259008, "dur": 977, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058259986, "dur": 1866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058261852, "dur": 1063, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058262916, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058263170, "dur": 1024, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058264194, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058264496, "dur": 668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058265164, "dur": 304603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751355058569768, "dur": 131231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058193548, "dur": 33315, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058226869, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_592CC89A6471077C.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751355058227031, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058227173, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_6C2CA7D176733A22.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751355058227335, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058227577, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_95C124FA2E4BBBB0.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751355058227701, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058227825, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_BDFB8953D401853F.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751355058228106, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058228210, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058228870, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058228966, "dur": 528, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 20, "ts": 1751355058229498, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058229611, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058229746, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058229813, "dur": 278, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751355058230094, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058230170, "dur": 2548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058232719, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058233341, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058234016, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058234645, "dur": 2752, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058237398, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058237991, "dur": 782, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\TimelinePlayable.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751355058241265, "dur": 563, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Runtime\\Playables\\BasicScriptPlayable.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751355058237991, "dur": 4378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058242369, "dur": 3141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058245510, "dur": 2374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058247884, "dur": 1083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058248968, "dur": 743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058249712, "dur": 2719, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058252431, "dur": 1065, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058253497, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058253655, "dur": 1526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058255272, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058255642, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058255716, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058255774, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058256006, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058256391, "dur": 708, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058257171, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058257586, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058257727, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058257873, "dur": 962, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058258835, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058259031, "dur": 956, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058259987, "dur": 1895, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058261882, "dur": 1051, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058262933, "dur": 278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058263211, "dur": 1004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058264216, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058264513, "dur": 639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058265153, "dur": 304624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751355058569783, "dur": 131128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058193424, "dur": 33407, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058226837, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_030C0AE8F7F2C9CD.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751355058226981, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058227216, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_DCB00145011AACBC.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751355058227331, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058227538, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_DCB00145011AACBC.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751355058227607, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_9B1D775C88DF2080.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751355058227716, "dur": 491, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058228302, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058228410, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_39F0A7015032F01B.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751355058228507, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058228591, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058228843, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751355058228927, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058229000, "dur": 549, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751355058229553, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058229684, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058229776, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751355058229838, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058229902, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751355058229981, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058230048, "dur": 277, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751355058230326, "dur": 2185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058232512, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058233196, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058233913, "dur": 662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058234576, "dur": 2081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058236658, "dur": 468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058237127, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058238043, "dur": 740, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Internal\\fsVersionedType.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751355058237650, "dur": 1383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058239033, "dur": 2444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058241478, "dur": 1821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058244842, "dur": 544, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\AssetsUtils\\RefreshAsset.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751355058243299, "dur": 3341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058246641, "dur": 1729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058248370, "dur": 1334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058249704, "dur": 2820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058252524, "dur": 950, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058253475, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058253634, "dur": 1541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058255308, "dur": 335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058255643, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058255719, "dur": 468, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058256196, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058256406, "dur": 699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058257106, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058257571, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058257702, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751355058257982, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058258039, "dur": 541, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1751355058258581, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058258715, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058258837, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058259025, "dur": 948, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058259974, "dur": 1895, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058261870, "dur": 1035, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058262911, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058262972, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058263190, "dur": 1029, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058264219, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058264526, "dur": 614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058265140, "dur": 304592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751355058569779, "dur": 226, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 21, "ts": 1751355058569735, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 21, "ts": 1751355058570078, "dur": 2521, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 21, "ts": 1751355058572604, "dur": 128327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058193842, "dur": 33063, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058226909, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_970778ACBFA20360.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751355058227046, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058227239, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F8AA39E8D5CE00CF.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751355058227446, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058227667, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F275A7F2C1FA929A.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751355058227959, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058228036, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B47D5ED0AB2E73A1.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751355058228133, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058228304, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9428A5C8BBBE8A51.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751355058228395, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058228483, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058228564, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751355058228619, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058228743, "dur": 451, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751355058229197, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058229280, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058229395, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751355058229469, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058229562, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058229708, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058229834, "dur": 503, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751355058230339, "dur": 2525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058232865, "dur": 2006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058234872, "dur": 2765, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058238040, "dur": 792, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\EditorBinding\\Inspector\\InspectorTextAreaAttribute.cs"}}, {"pid": 12345, "tid": 22, "ts": 1751355058237638, "dur": 1512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058239251, "dur": 526, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\InputSystem\\Editor\\UITKAssetEditor\\Views\\MatchingControlPaths.cs"}}, {"pid": 12345, "tid": 22, "ts": 1751355058239151, "dur": 2792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058241944, "dur": 2716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058244661, "dur": 3043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058247705, "dur": 1227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058248933, "dur": 827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058249761, "dur": 2630, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058252392, "dur": 1079, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058253471, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058253605, "dur": 433, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751355058254039, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058254174, "dur": 2058, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751355058256233, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058256396, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058256671, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751355058256912, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058256969, "dur": 671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751355058257641, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058257871, "dur": 963, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058258834, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058259010, "dur": 924, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058259935, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058260016, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751355058260177, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058260265, "dur": 1424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751355058261690, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058261870, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058261951, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751355058262163, "dur": 838, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751355058263002, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058263171, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058263237, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751355058263441, "dur": 637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751355058264079, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058264197, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058264257, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751355058264501, "dur": 485, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751355058264987, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058265124, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058265190, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751355058265415, "dur": 476, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751355058266562, "dur": 87, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058267464, "dur": 293737, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751355058570358, "dur": 3838, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 22, "ts": 1751355058569729, "dur": 4549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751355058574286, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751355058574383, "dur": 126552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058193886, "dur": 33032, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058226923, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_D3BB5BABC9B6A39A.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751355058227048, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058227189, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BA5431EB9EAB1D55.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751355058227290, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058227369, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_5AC43434136967AD.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751355058227471, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058227601, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B8F1FBD1DD727BB2.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751355058227688, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058227775, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C5419324AD6BC088.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751355058228039, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058228391, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058228531, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058228749, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751355058228912, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058228971, "dur": 551, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751355058229526, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058229670, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058229736, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751355058229866, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058229934, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058230039, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058230123, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1751355058230302, "dur": 2421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058232724, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058233269, "dur": 724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058233993, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058234629, "dur": 3317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058238017, "dur": 719, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Cloning\\Cloners\\ArrayCloner.cs"}}, {"pid": 12345, "tid": 23, "ts": 1751355058237947, "dur": 3421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058241369, "dur": 3031, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058244400, "dur": 3340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058247741, "dur": 1024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058248765, "dur": 931, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058249697, "dur": 2677, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058252376, "dur": 1089, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058253512, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058253647, "dur": 1532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058255208, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058255274, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058255633, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058255718, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058255770, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058256014, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058256398, "dur": 712, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058257111, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058257591, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058257725, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058257855, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058257915, "dur": 914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058258829, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058259006, "dur": 926, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058259999, "dur": 1863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058261863, "dur": 1045, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058262964, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058263217, "dur": 992, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058264210, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058264481, "dur": 635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058265162, "dur": 304682, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751355058569845, "dur": 131092, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058193963, "dur": 32965, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058226930, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_DC7672C0573B7C85.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751355058227083, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058227165, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E009CCFB4A5E9FFB.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751355058227272, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058227356, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_976F9C96B5CDAC08.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751355058227451, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058227565, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_97D91564DB667E63.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751355058227668, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058227753, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_FD8C0D7AD7503969.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751355058228218, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058228305, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058228388, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058228463, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751355058228618, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058228768, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058228843, "dur": 460, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1751355058229313, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751355058229599, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058229687, "dur": 18142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751355058247831, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058248060, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058248207, "dur": 1476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058249684, "dur": 2680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058252409, "dur": 1064, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058253473, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058253602, "dur": 392, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751355058253998, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058254092, "dur": 850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751355058254943, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058255170, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.ref.dll_22F664961FC0158D.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751355058255230, "dur": 621, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058255862, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751355058256190, "dur": 794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751355058256985, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058257105, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058257183, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058257582, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058257715, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058257864, "dur": 951, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058258875, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058259019, "dur": 951, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058259970, "dur": 1910, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058261880, "dur": 1059, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058262940, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058263205, "dur": 996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058264202, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058264489, "dur": 684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058265173, "dur": 304581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751355058569755, "dur": 131172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751355058731612, "dur": 10482, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 41616, "tid": 2104, "ts": 1751355058767976, "dur": 8903, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 41616, "tid": 2104, "ts": 1751355058776936, "dur": 4260, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 41616, "tid": 2104, "ts": 1751355058760553, "dur": 22365, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}
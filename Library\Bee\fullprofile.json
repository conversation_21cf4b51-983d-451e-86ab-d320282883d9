{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 15948, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 15948, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 15948, "tid": 58934, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 15948, "tid": 58934, "ts": 1751441674609406, "dur": 1190, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 15948, "tid": 58934, "ts": 1751441674617657, "dur": 1593, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 15948, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 15948, "tid": 1, "ts": 1751441673393617, "dur": 10768, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 15948, "tid": 1, "ts": 1751441673404389, "dur": 82370, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 15948, "tid": 1, "ts": 1751441673486773, "dur": 100632, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 15948, "tid": 58934, "ts": 1751441674619257, "dur": 17, "ph": "X", "name": "", "args": {}}, {"pid": 15948, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673388965, "dur": 22166, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673411136, "dur": 1177288, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673413283, "dur": 4595, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673417887, "dur": 991, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673418884, "dur": 3018, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673421913, "dur": 403, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673422323, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673422402, "dur": 1011, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673423418, "dur": 35434, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673458860, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673458864, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673458946, "dur": 897, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673459848, "dur": 256, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673460109, "dur": 11, "ph": "X", "name": "ProcessMessages 7551", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673460123, "dur": 79, "ph": "X", "name": "ReadAsync 7551", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673460206, "dur": 3, "ph": "X", "name": "ProcessMessages 858", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673460213, "dur": 65, "ph": "X", "name": "ReadAsync 858", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673460281, "dur": 2, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673460285, "dur": 58, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673460348, "dur": 2, "ph": "X", "name": "ProcessMessages 179", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673460352, "dur": 64, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673460420, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673460423, "dur": 65, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673460492, "dur": 2, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673460495, "dur": 63, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673460562, "dur": 2, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673460566, "dur": 67, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673460638, "dur": 2, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673460642, "dur": 70, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673460716, "dur": 2, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673460719, "dur": 78, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673460801, "dur": 2, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673460805, "dur": 64, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673460873, "dur": 2, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673460877, "dur": 64, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673460945, "dur": 2, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673460949, "dur": 64, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461016, "dur": 2, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461020, "dur": 89, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461113, "dur": 2, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461117, "dur": 63, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461185, "dur": 2, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461188, "dur": 65, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461257, "dur": 2, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461261, "dur": 56, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461322, "dur": 1, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461325, "dur": 64, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461393, "dur": 2, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461397, "dur": 64, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461466, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461470, "dur": 63, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461537, "dur": 2, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461541, "dur": 66, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461610, "dur": 2, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461614, "dur": 61, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461678, "dur": 2, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461682, "dur": 65, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461751, "dur": 2, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461754, "dur": 63, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461822, "dur": 2, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461825, "dur": 63, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461893, "dur": 2, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461896, "dur": 61, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461962, "dur": 2, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673461965, "dur": 63, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673462032, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673462036, "dur": 64, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673462105, "dur": 2, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673462109, "dur": 56, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673462169, "dur": 1, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673462172, "dur": 55, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673462232, "dur": 1, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673462236, "dur": 65, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673462304, "dur": 2, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673462308, "dur": 59, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673462371, "dur": 2, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673462375, "dur": 99, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673462482, "dur": 4, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673462490, "dur": 105, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673462599, "dur": 2, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673462604, "dur": 91, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673462704, "dur": 4, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673462712, "dur": 100, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673462817, "dur": 2, "ph": "X", "name": "ProcessMessages 844", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673462821, "dur": 66, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673462891, "dur": 2, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673462895, "dur": 73, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673462973, "dur": 2, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673462977, "dur": 60, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463043, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463046, "dur": 76, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463129, "dur": 4, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463136, "dur": 72, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463213, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463216, "dur": 69, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463293, "dur": 4, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463300, "dur": 62, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463366, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463369, "dur": 58, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463432, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463435, "dur": 51, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463489, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463493, "dur": 47, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463544, "dur": 2, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463548, "dur": 45, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463597, "dur": 1, "ph": "X", "name": "ProcessMessages 195", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463600, "dur": 46, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463650, "dur": 2, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463654, "dur": 46, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463704, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463707, "dur": 119, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463831, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463835, "dur": 75, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463914, "dur": 2, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463917, "dur": 69, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463991, "dur": 2, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673463995, "dur": 60, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673464059, "dur": 2, "ph": "X", "name": "ProcessMessages 338", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673464062, "dur": 64, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673464131, "dur": 2, "ph": "X", "name": "ProcessMessages 417", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673464134, "dur": 55, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673464193, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673464196, "dur": 66, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673464266, "dur": 2, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673464269, "dur": 71, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673464344, "dur": 2, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673464349, "dur": 63, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673464416, "dur": 2, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673464420, "dur": 74, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673464498, "dur": 2, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673464501, "dur": 60, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673464565, "dur": 2, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673464569, "dur": 64, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673464638, "dur": 2, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673464641, "dur": 62, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673464707, "dur": 3, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673464712, "dur": 65, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673464781, "dur": 2, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673464785, "dur": 72, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673464860, "dur": 2, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673464864, "dur": 63, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673464931, "dur": 2, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673464936, "dur": 62, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465001, "dur": 2, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465005, "dur": 52, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465061, "dur": 4, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465066, "dur": 42, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465112, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465115, "dur": 46, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465165, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465169, "dur": 47, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465220, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465223, "dur": 46, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465273, "dur": 2, "ph": "X", "name": "ProcessMessages 186", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465277, "dur": 44, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465325, "dur": 3, "ph": "X", "name": "ProcessMessages 179", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465330, "dur": 47, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465381, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465386, "dur": 45, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465435, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465438, "dur": 47, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465488, "dur": 2, "ph": "X", "name": "ProcessMessages 190", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465492, "dur": 45, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465540, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465543, "dur": 46, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465594, "dur": 2, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465598, "dur": 46, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465648, "dur": 2, "ph": "X", "name": "ProcessMessages 167", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465652, "dur": 47, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465703, "dur": 2, "ph": "X", "name": "ProcessMessages 214", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465708, "dur": 48, "ph": "X", "name": "ReadAsync 214", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465760, "dur": 1, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465763, "dur": 45, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465812, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465815, "dur": 44, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465867, "dur": 1, "ph": "X", "name": "ProcessMessages 173", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465870, "dur": 47, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465921, "dur": 2, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465925, "dur": 46, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465975, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673465978, "dur": 44, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466028, "dur": 1, "ph": "X", "name": "ProcessMessages 209", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466031, "dur": 46, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466081, "dur": 1, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466084, "dur": 45, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466133, "dur": 2, "ph": "X", "name": "ProcessMessages 163", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466137, "dur": 130, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466270, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466273, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466326, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466329, "dur": 40, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466372, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466377, "dur": 72, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466453, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466455, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466512, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466515, "dur": 46, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466565, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466568, "dur": 46, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466618, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466622, "dur": 45, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466671, "dur": 2, "ph": "X", "name": "ProcessMessages 183", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466674, "dur": 49, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466727, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466730, "dur": 47, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466783, "dur": 2, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466786, "dur": 45, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466835, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466838, "dur": 46, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466889, "dur": 2, "ph": "X", "name": "ProcessMessages 159", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466893, "dur": 48, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466945, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466948, "dur": 47, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673466999, "dur": 1, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467002, "dur": 49, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467054, "dur": 1, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467057, "dur": 47, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467109, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467113, "dur": 48, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467165, "dur": 1, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467168, "dur": 47, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467219, "dur": 1, "ph": "X", "name": "ProcessMessages 226", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467223, "dur": 46, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467272, "dur": 1, "ph": "X", "name": "ProcessMessages 219", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467276, "dur": 43, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467323, "dur": 2, "ph": "X", "name": "ProcessMessages 75", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467326, "dur": 194, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467524, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467528, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467580, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467583, "dur": 43, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467630, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467632, "dur": 46, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467683, "dur": 2, "ph": "X", "name": "ProcessMessages 130", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467686, "dur": 48, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467738, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467741, "dur": 39, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467784, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467787, "dur": 46, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467839, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467841, "dur": 44, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467890, "dur": 1, "ph": "X", "name": "ProcessMessages 197", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467893, "dur": 46, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467943, "dur": 1, "ph": "X", "name": "ProcessMessages 166", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467947, "dur": 44, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467995, "dur": 2, "ph": "X", "name": "ProcessMessages 150", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673467999, "dur": 48, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468050, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468053, "dur": 39, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468097, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468100, "dur": 50, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468154, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468157, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468207, "dur": 1, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468210, "dur": 46, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468259, "dur": 1, "ph": "X", "name": "ProcessMessages 198", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468262, "dur": 46, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468314, "dur": 2, "ph": "X", "name": "ProcessMessages 161", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468317, "dur": 45, "ph": "X", "name": "ReadAsync 161", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468366, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468369, "dur": 47, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468421, "dur": 1, "ph": "X", "name": "ProcessMessages 211", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468424, "dur": 47, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468478, "dur": 13, "ph": "X", "name": "ProcessMessages 198", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468493, "dur": 48, "ph": "X", "name": "ReadAsync 198", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468545, "dur": 4, "ph": "X", "name": "ProcessMessages 162", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468551, "dur": 45, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468600, "dur": 1, "ph": "X", "name": "ProcessMessages 166", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468604, "dur": 46, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468657, "dur": 1, "ph": "X", "name": "ProcessMessages 230", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468660, "dur": 39, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468703, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468706, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468752, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468755, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468806, "dur": 2, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468810, "dur": 53, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468867, "dur": 1, "ph": "X", "name": "ProcessMessages 207", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468871, "dur": 46, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468921, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468924, "dur": 45, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468973, "dur": 2, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673468977, "dur": 67, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469048, "dur": 1, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469051, "dur": 47, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469102, "dur": 2, "ph": "X", "name": "ProcessMessages 182", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469106, "dur": 46, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469156, "dur": 1, "ph": "X", "name": "ProcessMessages 150", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469160, "dur": 46, "ph": "X", "name": "ReadAsync 150", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469210, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469213, "dur": 44, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469261, "dur": 1, "ph": "X", "name": "ProcessMessages 147", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469264, "dur": 46, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469314, "dur": 2, "ph": "X", "name": "ProcessMessages 163", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469317, "dur": 87, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469408, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469411, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469459, "dur": 1, "ph": "X", "name": "ProcessMessages 230", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469462, "dur": 47, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469513, "dur": 2, "ph": "X", "name": "ProcessMessages 226", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469517, "dur": 47, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469568, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469572, "dur": 48, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469623, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469626, "dur": 47, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469678, "dur": 1, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469681, "dur": 46, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469730, "dur": 2, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469734, "dur": 47, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469785, "dur": 5, "ph": "X", "name": "ProcessMessages 168", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469793, "dur": 45, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469842, "dur": 1, "ph": "X", "name": "ProcessMessages 169", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469845, "dur": 45, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469894, "dur": 1, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469897, "dur": 39, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469939, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673469942, "dur": 54, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470001, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470004, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470054, "dur": 1, "ph": "X", "name": "ProcessMessages 254", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470057, "dur": 43, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470104, "dur": 1, "ph": "X", "name": "ProcessMessages 202", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470107, "dur": 43, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470154, "dur": 2, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470157, "dur": 44, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470206, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470209, "dur": 47, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470259, "dur": 1, "ph": "X", "name": "ProcessMessages 202", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470262, "dur": 42, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470308, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470312, "dur": 45, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470362, "dur": 1, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470364, "dur": 45, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470413, "dur": 1, "ph": "X", "name": "ProcessMessages 167", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470416, "dur": 45, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470465, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470468, "dur": 47, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470519, "dur": 1, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470522, "dur": 40, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470566, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470570, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470615, "dur": 1, "ph": "X", "name": "ProcessMessages 135", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470617, "dur": 44, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470665, "dur": 1, "ph": "X", "name": "ProcessMessages 179", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470668, "dur": 47, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470719, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470721, "dur": 44, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470769, "dur": 1, "ph": "X", "name": "ProcessMessages 215", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470772, "dur": 43, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470819, "dur": 2, "ph": "X", "name": "ProcessMessages 211", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470823, "dur": 46, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470873, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470875, "dur": 43, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470923, "dur": 1, "ph": "X", "name": "ProcessMessages 219", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470926, "dur": 44, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470974, "dur": 1, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673470981, "dur": 64, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471048, "dur": 2, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471052, "dur": 47, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471102, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471104, "dur": 41, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471150, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471154, "dur": 42, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471199, "dur": 1, "ph": "X", "name": "ProcessMessages 137", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471201, "dur": 43, "ph": "X", "name": "ReadAsync 137", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471249, "dur": 1, "ph": "X", "name": "ProcessMessages 171", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471252, "dur": 45, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471300, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471303, "dur": 44, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471351, "dur": 2, "ph": "X", "name": "ProcessMessages 200", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471354, "dur": 44, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471402, "dur": 1, "ph": "X", "name": "ProcessMessages 210", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471405, "dur": 46, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471456, "dur": 1, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471459, "dur": 43, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471506, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471508, "dur": 43, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471556, "dur": 2, "ph": "X", "name": "ProcessMessages 165", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471560, "dur": 46, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471609, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471612, "dur": 43, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471659, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471662, "dur": 42, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471708, "dur": 1, "ph": "X", "name": "ProcessMessages 177", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471710, "dur": 56, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471771, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471774, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471822, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471825, "dur": 45, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471874, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471877, "dur": 44, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471924, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471927, "dur": 48, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471980, "dur": 1, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673471983, "dur": 62, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472049, "dur": 1, "ph": "X", "name": "ProcessMessages 232", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472052, "dur": 51, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472107, "dur": 2, "ph": "X", "name": "ProcessMessages 65", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472110, "dur": 47, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472161, "dur": 1, "ph": "X", "name": "ProcessMessages 191", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472163, "dur": 45, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472212, "dur": 2, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472216, "dur": 43, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472262, "dur": 1, "ph": "X", "name": "ProcessMessages 99", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472265, "dur": 44, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472312, "dur": 1, "ph": "X", "name": "ProcessMessages 223", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472315, "dur": 38, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472357, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472360, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472410, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472412, "dur": 46, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472461, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472464, "dur": 43, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472511, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472513, "dur": 47, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472564, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472567, "dur": 45, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472616, "dur": 2, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472619, "dur": 42, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472665, "dur": 1, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472667, "dur": 58, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472729, "dur": 2, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472733, "dur": 48, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472785, "dur": 2, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472788, "dur": 46, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472838, "dur": 2, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472841, "dur": 39, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472883, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472886, "dur": 54, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472943, "dur": 2, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472947, "dur": 45, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472995, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673472998, "dur": 99, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673473102, "dur": 2, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673473105, "dur": 73, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673473182, "dur": 2, "ph": "X", "name": "ProcessMessages 937", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673473186, "dur": 58, "ph": "X", "name": "ReadAsync 937", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673473248, "dur": 2, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673473254, "dur": 62, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673473320, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673473322, "dur": 51, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673473378, "dur": 1, "ph": "X", "name": "ProcessMessages 49", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673473381, "dur": 64, "ph": "X", "name": "ReadAsync 49", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673473449, "dur": 2, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673473453, "dur": 62, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673473519, "dur": 2, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673473522, "dur": 65, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673473592, "dur": 3, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673473597, "dur": 72, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673473672, "dur": 2, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673473676, "dur": 59, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673473743, "dur": 2, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673473748, "dur": 92, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673473844, "dur": 2, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673473848, "dur": 61, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673473913, "dur": 2, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673473917, "dur": 60, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673473981, "dur": 2, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673473985, "dur": 61, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673474050, "dur": 2, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673474053, "dur": 60, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673474117, "dur": 2, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673474121, "dur": 129, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673474258, "dur": 3, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673474265, "dur": 97, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673474367, "dur": 2, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673474371, "dur": 62, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673474437, "dur": 2, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673474440, "dur": 65, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673474509, "dur": 2, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673474513, "dur": 52, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673474569, "dur": 2, "ph": "X", "name": "ProcessMessages 70", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673474572, "dur": 64, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673474640, "dur": 2, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673474644, "dur": 61, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673474709, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673474712, "dur": 60, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673474776, "dur": 2, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673474780, "dur": 58, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673474842, "dur": 2, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673474845, "dur": 60, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673474909, "dur": 2, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673474913, "dur": 55, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673474972, "dur": 1, "ph": "X", "name": "ProcessMessages 173", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673474975, "dur": 54, "ph": "X", "name": "ReadAsync 173", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673475033, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673475035, "dur": 61, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673475100, "dur": 2, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673475103, "dur": 55, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673475162, "dur": 1, "ph": "X", "name": "ProcessMessages 126", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673475165, "dur": 62, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673475230, "dur": 2, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673475234, "dur": 58, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673475297, "dur": 2, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673475301, "dur": 74, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673475378, "dur": 2, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673475381, "dur": 187, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673475573, "dur": 2, "ph": "X", "name": "ProcessMessages 65", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673475576, "dur": 94, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673475675, "dur": 3, "ph": "X", "name": "ProcessMessages 1753", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673475679, "dur": 60, "ph": "X", "name": "ReadAsync 1753", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673475744, "dur": 2, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673475747, "dur": 54, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673475805, "dur": 1, "ph": "X", "name": "ProcessMessages 70", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673475808, "dur": 72, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673475884, "dur": 2, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673475888, "dur": 60, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673475952, "dur": 2, "ph": "X", "name": "ProcessMessages 438", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673475955, "dur": 56, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673476015, "dur": 2, "ph": "X", "name": "ProcessMessages 186", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673476019, "dur": 63, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673476086, "dur": 2, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673476090, "dur": 60, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673476153, "dur": 4, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673476160, "dur": 54, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673476217, "dur": 1, "ph": "X", "name": "ProcessMessages 166", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673476220, "dur": 398, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673476623, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673476626, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673476713, "dur": 3, "ph": "X", "name": "ProcessMessages 1170", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673476718, "dur": 62, "ph": "X", "name": "ReadAsync 1170", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673476784, "dur": 2, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673476789, "dur": 60, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673476853, "dur": 1, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673476856, "dur": 51, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673476911, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673476915, "dur": 57, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673476975, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673476977, "dur": 65, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477046, "dur": 2, "ph": "X", "name": "ProcessMessages 391", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477050, "dur": 57, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477110, "dur": 3, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477115, "dur": 68, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477188, "dur": 2, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477191, "dur": 63, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477257, "dur": 2, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477260, "dur": 57, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477322, "dur": 2, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477326, "dur": 57, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477386, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477389, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477453, "dur": 2, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477458, "dur": 60, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477522, "dur": 1, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477525, "dur": 58, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477588, "dur": 2, "ph": "X", "name": "ProcessMessages 347", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477592, "dur": 60, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477655, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477658, "dur": 59, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477722, "dur": 2, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477725, "dur": 54, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477783, "dur": 1, "ph": "X", "name": "ProcessMessages 78", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477786, "dur": 61, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477851, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477854, "dur": 58, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477916, "dur": 2, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477919, "dur": 59, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477982, "dur": 2, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673477985, "dur": 60, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673478049, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673478053, "dur": 59, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673478116, "dur": 2, "ph": "X", "name": "ProcessMessages 328", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673478119, "dur": 125, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673478253, "dur": 4, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673478260, "dur": 98, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673478363, "dur": 2, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673478367, "dur": 70, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673478441, "dur": 2, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673478445, "dur": 64, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673478513, "dur": 2, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673478517, "dur": 58, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673478579, "dur": 2, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673478582, "dur": 56, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673478643, "dur": 2, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673478646, "dur": 76, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673478725, "dur": 2, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673478728, "dur": 56, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673478789, "dur": 2, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673478793, "dur": 60, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673478856, "dur": 1, "ph": "X", "name": "ProcessMessages 431", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673478859, "dur": 57, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673478921, "dur": 2, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673478925, "dur": 64, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673478992, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673478995, "dur": 57, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673479056, "dur": 2, "ph": "X", "name": "ProcessMessages 77", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673479060, "dur": 67, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673479132, "dur": 2, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673479135, "dur": 64, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673479204, "dur": 2, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673479207, "dur": 62, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673479273, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673479276, "dur": 62, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673479343, "dur": 2, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673479346, "dur": 108, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673479459, "dur": 2, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673479463, "dur": 65, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673479532, "dur": 2, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673479538, "dur": 81, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673479623, "dur": 2, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673479626, "dur": 61, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673479691, "dur": 2, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673479695, "dur": 64, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673479763, "dur": 2, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673479766, "dur": 52, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673479822, "dur": 1, "ph": "X", "name": "ProcessMessages 77", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673479825, "dur": 63, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673479892, "dur": 2, "ph": "X", "name": "ProcessMessages 497", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673479896, "dur": 60, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673479960, "dur": 2, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673479963, "dur": 60, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480026, "dur": 2, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480031, "dur": 68, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480103, "dur": 2, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480107, "dur": 64, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480174, "dur": 2, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480177, "dur": 50, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480232, "dur": 2, "ph": "X", "name": "ProcessMessages 70", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480235, "dur": 84, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480323, "dur": 2, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480326, "dur": 63, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480393, "dur": 2, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480397, "dur": 75, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480476, "dur": 2, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480480, "dur": 63, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480547, "dur": 2, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480551, "dur": 49, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480603, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480606, "dur": 62, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480672, "dur": 2, "ph": "X", "name": "ProcessMessages 529", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480675, "dur": 61, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480739, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480743, "dur": 58, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480805, "dur": 2, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480809, "dur": 63, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480876, "dur": 1, "ph": "X", "name": "ProcessMessages 557", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480879, "dur": 57, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480941, "dur": 2, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673480946, "dur": 57, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673481007, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673481009, "dur": 63, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673481076, "dur": 3, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673481081, "dur": 64, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673481148, "dur": 2, "ph": "X", "name": "ProcessMessages 664", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673481154, "dur": 3167, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673484326, "dur": 2, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673484330, "dur": 506, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673484840, "dur": 28, "ph": "X", "name": "ProcessMessages 20493", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673484870, "dur": 55, "ph": "X", "name": "ReadAsync 20493", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673484930, "dur": 2, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673484935, "dur": 48, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673484987, "dur": 2, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673484990, "dur": 45, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485040, "dur": 2, "ph": "X", "name": "ProcessMessages 276", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485045, "dur": 46, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485095, "dur": 2, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485098, "dur": 45, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485147, "dur": 2, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485151, "dur": 53, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485207, "dur": 1, "ph": "X", "name": "ProcessMessages 246", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485210, "dur": 44, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485258, "dur": 1, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485261, "dur": 46, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485311, "dur": 2, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485315, "dur": 47, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485366, "dur": 2, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485370, "dur": 50, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485424, "dur": 1, "ph": "X", "name": "ProcessMessages 513", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485427, "dur": 44, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485475, "dur": 2, "ph": "X", "name": "ProcessMessages 185", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485478, "dur": 51, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485533, "dur": 1, "ph": "X", "name": "ProcessMessages 270", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485538, "dur": 48, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485589, "dur": 2, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485592, "dur": 40, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485637, "dur": 1, "ph": "X", "name": "ProcessMessages 77", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485640, "dur": 44, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485687, "dur": 1, "ph": "X", "name": "ProcessMessages 103", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485690, "dur": 52, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485746, "dur": 2, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485749, "dur": 38, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485791, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485794, "dur": 46, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485844, "dur": 2, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485847, "dur": 45, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485896, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485899, "dur": 44, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485947, "dur": 2, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673485950, "dur": 62, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486016, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486019, "dur": 50, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486074, "dur": 2, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486078, "dur": 43, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486125, "dur": 1, "ph": "X", "name": "ProcessMessages 186", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486128, "dur": 38, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486170, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486173, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486231, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486234, "dur": 47, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486285, "dur": 2, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486288, "dur": 45, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486337, "dur": 1, "ph": "X", "name": "ProcessMessages 209", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486354, "dur": 50, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486408, "dur": 2, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486412, "dur": 47, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486462, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486465, "dur": 45, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486514, "dur": 2, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486517, "dur": 48, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486569, "dur": 1, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486572, "dur": 43, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486619, "dur": 1, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486622, "dur": 45, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486671, "dur": 1, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486674, "dur": 37, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486716, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486719, "dur": 45, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486768, "dur": 1, "ph": "X", "name": "ProcessMessages 243", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486771, "dur": 46, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486820, "dur": 3, "ph": "X", "name": "ProcessMessages 201", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486825, "dur": 49, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486878, "dur": 2, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486881, "dur": 45, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486931, "dur": 1, "ph": "X", "name": "ProcessMessages 207", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486933, "dur": 46, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486983, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673486986, "dur": 47, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487038, "dur": 2, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487041, "dur": 46, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487091, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487094, "dur": 45, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487143, "dur": 1, "ph": "X", "name": "ProcessMessages 209", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487146, "dur": 48, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487198, "dur": 1, "ph": "X", "name": "ProcessMessages 221", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487200, "dur": 45, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487249, "dur": 2, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487253, "dur": 53, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487309, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487312, "dur": 46, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487362, "dur": 1, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487365, "dur": 46, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487415, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487418, "dur": 46, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487467, "dur": 2, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487470, "dur": 47, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487521, "dur": 1, "ph": "X", "name": "ProcessMessages 243", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487524, "dur": 48, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487576, "dur": 2, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487579, "dur": 46, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487629, "dur": 1, "ph": "X", "name": "ProcessMessages 235", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487631, "dur": 40, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487676, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487679, "dur": 48, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487730, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487733, "dur": 45, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487782, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487785, "dur": 45, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487834, "dur": 1, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487837, "dur": 45, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487886, "dur": 2, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487891, "dur": 46, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487941, "dur": 2, "ph": "X", "name": "ProcessMessages 267", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487944, "dur": 45, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487993, "dur": 2, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673487997, "dur": 47, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488047, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488050, "dur": 45, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488100, "dur": 2, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488103, "dur": 46, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488152, "dur": 1, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488155, "dur": 47, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488206, "dur": 1, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488209, "dur": 54, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488268, "dur": 1, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488271, "dur": 46, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488323, "dur": 2, "ph": "X", "name": "ProcessMessages 246", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488326, "dur": 55, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488385, "dur": 2, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488388, "dur": 48, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488440, "dur": 1, "ph": "X", "name": "ProcessMessages 247", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488443, "dur": 49, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488496, "dur": 2, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488499, "dur": 45, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488548, "dur": 2, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488551, "dur": 47, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488602, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488605, "dur": 44, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488653, "dur": 1, "ph": "X", "name": "ProcessMessages 235", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488656, "dur": 55, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488715, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488717, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488766, "dur": 2, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488770, "dur": 49, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488822, "dur": 1, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488825, "dur": 45, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488874, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488877, "dur": 46, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488927, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488930, "dur": 46, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488980, "dur": 2, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673488983, "dur": 49, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489035, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489038, "dur": 46, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489088, "dur": 2, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489093, "dur": 39, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489135, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489137, "dur": 49, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489191, "dur": 2, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489194, "dur": 42, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489239, "dur": 1, "ph": "X", "name": "ProcessMessages 155", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489242, "dur": 49, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489295, "dur": 2, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489298, "dur": 46, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489348, "dur": 1, "ph": "X", "name": "ProcessMessages 251", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489351, "dur": 45, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489401, "dur": 3, "ph": "X", "name": "ProcessMessages 258", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489406, "dur": 56, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489466, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489468, "dur": 42, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489514, "dur": 2, "ph": "X", "name": "ProcessMessages 167", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489517, "dur": 46, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489567, "dur": 1, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489570, "dur": 39, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489613, "dur": 1, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489615, "dur": 49, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489667, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489670, "dur": 77, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489751, "dur": 2, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489755, "dur": 46, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489804, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489807, "dur": 45, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489856, "dur": 2, "ph": "X", "name": "ProcessMessages 235", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489859, "dur": 49, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489912, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489915, "dur": 46, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489967, "dur": 2, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673489970, "dur": 43, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673490017, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673490019, "dur": 40, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673490064, "dur": 1, "ph": "X", "name": "ProcessMessages 89", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673490066, "dur": 83, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673490153, "dur": 2, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673490156, "dur": 46, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673490207, "dur": 2, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673490210, "dur": 47, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673490261, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673490264, "dur": 60, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673490328, "dur": 2, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673490331, "dur": 45, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673490379, "dur": 8, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673490400, "dur": 49, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673490465, "dur": 2, "ph": "X", "name": "ProcessMessages 179", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673490468, "dur": 42, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673490557, "dur": 2, "ph": "X", "name": "ProcessMessages 165", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673490560, "dur": 44, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673490609, "dur": 1, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673490612, "dur": 465, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673491087, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673491095, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673491198, "dur": 1255, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673492482, "dur": 5539, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673498027, "dur": 30, "ph": "X", "name": "ProcessMessages 1748", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673498060, "dur": 450, "ph": "X", "name": "ReadAsync 1748", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673498525, "dur": 111, "ph": "X", "name": "ProcessMessages 3072", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673498667, "dur": 15591, "ph": "X", "name": "ReadAsync 3072", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673514273, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673514279, "dur": 145, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673514429, "dur": 23, "ph": "X", "name": "ProcessMessages 1268", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673514454, "dur": 58, "ph": "X", "name": "ReadAsync 1268", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673514517, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673514522, "dur": 262, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673514789, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673514792, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673514855, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673514858, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673514915, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673514918, "dur": 169, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673515092, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673515094, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673515138, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673515140, "dur": 259, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673515406, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673515409, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673515463, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673515485, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673515549, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673515554, "dur": 74, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673515634, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673515640, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673515715, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673515718, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673515777, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673515781, "dur": 55, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673515846, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673515851, "dur": 71, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673515928, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673515932, "dur": 52, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673515989, "dur": 7, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673516000, "dur": 55, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673516064, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673516071, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673516129, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673516132, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673516175, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673516178, "dur": 74, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673516256, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673516259, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673516300, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673516302, "dur": 196, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673516503, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673516506, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673516547, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673516549, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673516596, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673516601, "dur": 180, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673516791, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673516796, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673516848, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673516851, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673516894, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673516898, "dur": 155, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517061, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517065, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517114, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517118, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517168, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517172, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517214, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517217, "dur": 90, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517314, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517320, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517363, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517365, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517422, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517424, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517467, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517470, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517515, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517518, "dur": 195, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517721, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517724, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517789, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517795, "dur": 64, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517866, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517868, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517930, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517933, "dur": 39, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517976, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673517979, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673518025, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673518028, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673518071, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673518074, "dur": 178, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673518256, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673518259, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673518301, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673518303, "dur": 341, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673518650, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673518655, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673518716, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673518720, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673518770, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673518773, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673518821, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673518826, "dur": 258, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673519089, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673519092, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673519153, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673519156, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673519201, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673519204, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673519248, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673519251, "dur": 85, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673519341, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673519345, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673519407, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673519409, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673519456, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673519462, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673519516, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673519519, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673519566, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673519569, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673519629, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673519632, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673519674, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673519676, "dur": 147, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673519827, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673519830, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673519870, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673519872, "dur": 366, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673520245, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673520249, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673520316, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673520320, "dur": 45, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673520370, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673520373, "dur": 676, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673521055, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673521058, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673521112, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673521114, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673521157, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673521159, "dur": 36, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673521199, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673521201, "dur": 200, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673521406, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673521409, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673521453, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673521455, "dur": 629, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673522089, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673522092, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673522151, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673522154, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673522208, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673522211, "dur": 144, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673522363, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673522367, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673522432, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673522435, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673522482, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673522484, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673522532, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673522535, "dur": 187, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673522725, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673522727, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673522778, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673522781, "dur": 691, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673523478, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673523482, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673523544, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673523548, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673523593, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673523596, "dur": 319, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673523920, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673523923, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673523969, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673523972, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673524015, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673524018, "dur": 663, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673524687, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673524693, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673524757, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673524760, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673524812, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673524815, "dur": 820, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673525640, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673525643, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673525700, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673525703, "dur": 1303, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673527010, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673527012, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673527054, "dur": 869, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441673527927, "dur": 784330, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674312269, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674312276, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674312380, "dur": 4198, "ph": "X", "name": "ProcessMessages 8721", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674316585, "dur": 19638, "ph": "X", "name": "ReadAsync 8721", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674336231, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674336236, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674336316, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674336323, "dur": 1311, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674337643, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674337647, "dur": 135, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674337795, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674337801, "dur": 1444, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674339254, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674339259, "dur": 97, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674339364, "dur": 32, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674339400, "dur": 213076, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674552488, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674552495, "dur": 114, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674552619, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674552628, "dur": 1274, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674553916, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674553919, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674553994, "dur": 51, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674554052, "dur": 2068, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674556129, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674556133, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674556195, "dur": 539, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441674556741, "dur": 31258, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 15948, "tid": 58934, "ts": 1751441674619277, "dur": 2959, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 15948, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 15948, "tid": 8589934592, "ts": 1751441673382365, "dur": 205157, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 15948, "tid": 8589934592, "ts": 1751441673587526, "dur": 11, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 15948, "tid": 8589934592, "ts": 1751441673587540, "dur": 3674, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 15948, "tid": 58934, "ts": 1751441674622240, "dur": 57, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 15948, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 15948, "tid": 4294967296, "ts": 1751441673268804, "dur": 1322564, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 15948, "tid": 4294967296, "ts": 1751441673276802, "dur": 85630, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 15948, "tid": 4294967296, "ts": 1751441674591897, "dur": 12344, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 15948, "tid": 4294967296, "ts": 1751441674599616, "dur": 263, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 15948, "tid": 4294967296, "ts": 1751441674604507, "dur": 37, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 15948, "tid": 58934, "ts": 1751441674622299, "dur": 14, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751441673402783, "dur": 78, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441673402956, "dur": 53699, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441673456677, "dur": 1224, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441673457991, "dur": 126, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751441673458117, "dur": 1320, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441673459578, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_00CD99E311DC019E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673460547, "dur": 201, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_711E053178EA7BF1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673460825, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_C2E93A20F98E8229.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673460929, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8ECA4E4329F1BADB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673461057, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673461115, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_61E5A1A2114B6332.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673461213, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_6FBB1366163B00FB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673461271, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_F24F3CD4D4D128AB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673461363, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_6C2CA7D176733A22.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673461457, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BA5431EB9EAB1D55.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673461515, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_DCB00145011AACBC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673461575, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F8AA39E8D5CE00CF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673461667, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_0660C742CA2CCB37.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673461726, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_C287EB2677BA7F93.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673461841, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_6C4C0BE13C75841D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673461899, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_7BFAB029D4D7DE5C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673462105, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B1475218F12198D8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673462167, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_39C615B7DD834A4A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673462257, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_921E3B2D436CDAE7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673462315, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CB3FD07F3B098D2B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673462406, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_889FFFE959BEFFC2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673462463, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_5AC43434136967AD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673462552, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_173731A5F5D7E041.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673462610, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_7D7B641D38DA242D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673462667, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_ED94C9E717A89F9F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673462758, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D22D11490AEAA9B0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673462949, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_00433992BAF13A73.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673463011, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6678507EC9682BC1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673463101, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C099976D06C71E3D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673463217, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_95C124FA2E4BBBB0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673463333, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B8F1FBD1DD727BB2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673463424, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B5DB3F27CCAA63FE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673463624, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F275A7F2C1FA929A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673463744, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_31922632D84128F5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673463822, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_20E0F9318BFD3B5B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673463943, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_DC79D8DB49767D4F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673464033, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_B563F2CB5E9D696E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673464117, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_963BCDCD478EB63D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673464191, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C5419324AD6BC088.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673464260, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0D29C9963CEF79A6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673464328, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A99F700E950BD818.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673464399, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_54605A4C765A4A77.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673464466, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_67F30B299A5D5154.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673464525, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E0B83A434CA2B946.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673464613, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_BDFB8953D401853F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673464714, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D7BFC8363EAC4767.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673464774, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B56095C3DA730DAC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673464898, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9F68AD12AC89760A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673464996, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E671E5897665917D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673465052, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B47D5ED0AB2E73A1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673465144, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3EA5AD5E9A933796.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673465275, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_09E134ACBD96AA82.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673465365, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BED6F64D074B3174.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673465423, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_10CDAFD975BC0AB7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673465481, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B014F0E06EB9C8B3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673465576, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_4FB16FC3A796B887.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673465706, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9428A5C8BBBE8A51.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673465786, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_69199FEE963F7F27.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673465860, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673465930, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_2CD7161C662F4679.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673465997, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_5CAA8383FA1D1EAC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673466075, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673466145, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_A3850CF6800E4193.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673466215, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_3AA3364A40DB9F8B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673466300, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673466367, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TestRunner.ref.dll_7423F2FBC6CF80C4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673466448, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441673466530, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441673466599, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673466672, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441673466742, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673466944, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673467127, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_696347C4732CD601.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673467205, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_39F0A7015032F01B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673467273, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_02726B6CC8A7DF56.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673467341, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9C39811BC6DD0BF6.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673467407, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673467479, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_A805A84D9B0C22D9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673467549, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441673467622, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441673467698, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673467766, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441673467833, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673467917, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673468200, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_BDDAB6C3AC1C4D86.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673468279, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441673468354, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441673468463, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673468534, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441673468602, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673468676, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673468831, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UI.ref.dll_0174E068A5FA251B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673468905, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441673468978, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441673469045, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673469118, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441673469195, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673469271, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673469426, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_A558B9E65539D7D0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673469502, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441673469584, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441673469676, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673469758, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441673469831, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673469914, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673470085, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.InputSystem.ref.dll_7B1D397D2193763B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673470160, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441673470236, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441673470304, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673470375, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441673470444, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673470513, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673470676, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.ref.dll_66DE4C1C9A03AD08.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673470752, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441673470827, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441673470904, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673470995, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441673471067, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673471137, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673471278, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Common.ref.dll_FF44434556825F42.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673471349, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441673471422, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441673471491, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673471567, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441673471635, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673471727, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673471860, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Postprocessing.Runtime.ref.dll_D64FE1030D5C6CA1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673471929, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441673472007, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441673472074, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673472172, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441673472239, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673472312, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673472442, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Settings.Editor.ref.dll_8A1461A5C565B4EA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673472518, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441673472588, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441673472657, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673472734, "dur": 118, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441673472861, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673472932, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673473073, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.ref.dll_22F664961FC0158D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673473154, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441673473226, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441673473365, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441673473597, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441673473803, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673473958, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_02929A3ECA791895.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673474084, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441673474223, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673474460, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441673474626, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673474691, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673474834, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441673474971, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673475087, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673475146, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673475282, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441673475353, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441673475412, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673475557, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673475889, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441673476005, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673476288, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673476379, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673476519, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441673476592, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441673476743, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673477354, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441673477429, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673477488, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673477826, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441673477890, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673478091, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441673478158, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441673478301, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673478362, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673478484, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.Editor.ref.dll_CE7BFB6B1BFC18F8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673478689, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441673478752, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673478978, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441673479085, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673479146, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441673479355, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441673479495, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673479625, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673479764, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441673479914, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441673479974, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673480279, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441673480340, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673480400, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673480531, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441673480594, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441673480684, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441673480742, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673480808, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673481027, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673481174, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673481311, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441673481377, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441673481508, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673481797, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673481858, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441673484568, "dur": 939, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441673485570, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441673485633, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441673485743, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441673485850, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441673486078, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673486244, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673486392, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441673486669, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673486730, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673486882, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441673487237, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673487413, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441673487523, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441673487867, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441673487974, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441673488390, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Splines.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441673488706, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Cinemachine.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441673488864, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441673488920, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441673489029, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441673489271, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441673489430, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441673489707, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673489848, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor-firstpass.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441673489958, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor-firstpass.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441673490123, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673490323, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441673490421, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441673490571, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441673490802, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441673490871, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441673490979, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441673491111, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441673459481, "dur": 31770, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441673491268, "dur": 1063470, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441674554740, "dur": 1029, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441674555770, "dur": 135, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441674555906, "dur": 96, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441674556089, "dur": 51, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441674556550, "dur": 177, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441674556771, "dur": 17987, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751441673459190, "dur": 32123, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673491341, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_52F12B5AEAC5FC8F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441673491791, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673491906, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_47A61C91F1D08784.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441673492024, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673492141, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_6C4C0BE13C75841D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441673492331, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673492497, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_9B1D775C88DF2080.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441673492794, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673492918, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E671E5897665917D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441673493029, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673493121, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_5CAA8383FA1D1EAC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441673493220, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673493279, "dur": 528, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_5CAA8383FA1D1EAC.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441673493823, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673493877, "dur": 195, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751441673494221, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751441673494499, "dur": 570, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751441673495071, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751441673495187, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673496279, "dur": 1310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673497591, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673498831, "dur": 1367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673500997, "dur": 1186, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Manipulators\\Trim\\TrimItemModeMix.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751441673500199, "dur": 2397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673502597, "dur": 1501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673504099, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673505012, "dur": 1006, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673506019, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673506933, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673507788, "dur": 723, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673508511, "dur": 1700, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673510212, "dur": 1022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673511234, "dur": 204, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673511438, "dur": 1871, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673513362, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673513537, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673513648, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673513991, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673514096, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673514436, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673514567, "dur": 952, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673515519, "dur": 639, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673516158, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673516356, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673516491, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673516595, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673516738, "dur": 721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673517465, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673517543, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673517789, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673518044, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673518157, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673518466, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673518551, "dur": 829, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673519380, "dur": 448, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673519828, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673520059, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673520188, "dur": 758, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673520947, "dur": 802, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673521750, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673521856, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441673522090, "dur": 596, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751441673522793, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673522860, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673523153, "dur": 1030, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673524236, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673524342, "dur": 1099, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441673525441, "dur": 810892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441674336334, "dur": 218440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673461203, "dur": 30574, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673491782, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_A8A2BEB223D0393B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751441673492003, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673492152, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_7BFAB029D4D7DE5C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751441673492316, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673492502, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_7BFAB029D4D7DE5C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751441673492656, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A99F700E950BD818.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751441673492950, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673493163, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673493253, "dur": 626, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_2CD7161C662F4679.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751441673493966, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751441673494106, "dur": 262, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751441673494375, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673494446, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751441673494615, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673494694, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751441673495000, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673495097, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751441673495222, "dur": 1460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673496683, "dur": 887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673497571, "dur": 1524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673499096, "dur": 981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673500078, "dur": 961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673501066, "dur": 1121, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Variables\\Variables.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751441673501040, "dur": 2304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673503345, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673504237, "dur": 928, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673505165, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673506119, "dur": 802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673506922, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673507652, "dur": 815, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673508504, "dur": 1689, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673510193, "dur": 1047, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673511241, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673511417, "dur": 1923, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673513341, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673513536, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673513676, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673513988, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673514096, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673514160, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673514452, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751441673515001, "dur": 853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751441673515855, "dur": 594, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673516571, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673516645, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673516737, "dur": 742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673517480, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673517784, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673518037, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673518464, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673518544, "dur": 813, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673519357, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673519830, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673520056, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673520191, "dur": 738, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673520975, "dur": 791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673521766, "dur": 1018, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673522830, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673523149, "dur": 1067, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673524217, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673524382, "dur": 1073, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441673525456, "dur": 810806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441674336371, "dur": 435, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751441674336303, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751441674336886, "dur": 3032, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751441674339922, "dur": 214924, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673459307, "dur": 32055, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673491367, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BED6F64D074B3174.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751441673491689, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673491828, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BED6F64D074B3174.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751441673491950, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46E217D19A6E01BD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751441673492328, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673492473, "dur": 324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B8F1FBD1DD727BB2.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751441673492799, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673492980, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D7EC0EF22CAF3612.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751441673493154, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673493269, "dur": 553, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D7EC0EF22CAF3612.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751441673493840, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673493903, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751441673494020, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673494108, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673494223, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673494295, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751441673494524, "dur": 402, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751441673494929, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673495017, "dur": 168, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751441673495188, "dur": 1674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673496863, "dur": 1427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673498291, "dur": 1275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673499567, "dur": 1342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673501029, "dur": 1157, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Deprecated\\AxisState.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751441673500911, "dur": 2295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673503207, "dur": 1020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673504228, "dur": 845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673505073, "dur": 1042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673506116, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673507114, "dur": 93, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673507248, "dur": 70, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673507363, "dur": 1108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673508546, "dur": 1657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673510204, "dur": 1028, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673511232, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673511433, "dur": 1882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673513316, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673513566, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673513667, "dur": 331, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673514092, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673514498, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751441673514994, "dur": 1176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751441673516171, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673516352, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673516424, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673516549, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673516603, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673516734, "dur": 732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673517467, "dur": 329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673517797, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673518042, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673518109, "dur": 365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673518475, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673518565, "dur": 785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673519351, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673519855, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673520070, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673520211, "dur": 724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673520935, "dur": 822, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673521758, "dur": 1031, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673522790, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673523134, "dur": 1060, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673524195, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673524370, "dur": 1098, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441673525469, "dur": 810809, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441674336279, "dur": 218461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673461067, "dur": 30683, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673491755, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_921345D5C92C6DDE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441673491916, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673492016, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_74513773BD43745E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441673492123, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673492203, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_39C615B7DD834A4A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441673492322, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673492416, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C099976D06C71E3D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441673492523, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673492616, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_B563F2CB5E9D696E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441673492860, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673492942, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_800370C755C131C9.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441673493063, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673493259, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_02726B6CC8A7DF56.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441673493385, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673493510, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751441673493704, "dur": 463, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751441673494184, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673494241, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751441673494299, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751441673494672, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751441673494748, "dur": 606, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751441673495357, "dur": 1780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673497138, "dur": 1319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673498458, "dur": 1026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673499486, "dur": 1308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673501046, "dur": 1155, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Deprecated\\CinemachinePOV.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751441673500795, "dur": 2131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673502927, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673504002, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673504852, "dur": 1174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673506027, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673507030, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673507804, "dur": 697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673508502, "dur": 1700, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673510203, "dur": 1003, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673511260, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673511413, "dur": 587, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441673512001, "dur": 433, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673512443, "dur": 1670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751441673514114, "dur": 1982, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673516144, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673516238, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441673516755, "dur": 1549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751441673518306, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673518462, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673518542, "dur": 812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673519354, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673519831, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673520078, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673520203, "dur": 728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673520932, "dur": 836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673521768, "dur": 1026, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673522795, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673523102, "dur": 1116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673524218, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673524390, "dur": 1081, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441673525471, "dur": 810842, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441674336314, "dur": 218464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673461437, "dur": 30414, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673491852, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_DC7672C0573B7C85.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751441673491994, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673492065, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BA5431EB9EAB1D55.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751441673492179, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673492281, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_7D7B641D38DA242D.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751441673492416, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673492530, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_805FFE03DDD45823.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751441673492775, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673492908, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_27EF5DC3D06E8F78.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751441673493035, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673493136, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751441673493303, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673493362, "dur": 492, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751441673493873, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751441673493929, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673494095, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751441673494210, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751441673494442, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673494610, "dur": 335, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751441673495064, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673495140, "dur": 1494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673496635, "dur": 1502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673498138, "dur": 1193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673499332, "dur": 1446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673501053, "dur": 1198, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Impulse\\CinemachineImpulseManager.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751441673500780, "dur": 2078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673502859, "dur": 779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673503638, "dur": 979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673504619, "dur": 1089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673505709, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673506506, "dur": 1852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673508359, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673508503, "dur": 1704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673510208, "dur": 1015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673511224, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673511443, "dur": 1870, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673513314, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673513556, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673513644, "dur": 602, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673514253, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673514500, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751441673514986, "dur": 1153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751441673516140, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673516293, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673516359, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673516594, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673516740, "dur": 742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673517483, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673517798, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673518078, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673518478, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673518576, "dur": 771, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673519348, "dur": 485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673519834, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673520071, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673520219, "dur": 721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673520941, "dur": 820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673521761, "dur": 1040, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673522802, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673523129, "dur": 1097, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673524227, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673524384, "dur": 1067, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441673525452, "dur": 810821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441674336275, "dur": 218460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673459657, "dur": 31825, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673491490, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_7A1FD51B51344812.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751441673491690, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673491861, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_129B2BEEEC662028.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751441673492063, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673492180, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_99CD71E244B915CF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751441673492384, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673492565, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_71CC72797B2E953C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751441673492756, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673492940, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B47D5ED0AB2E73A1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751441673493122, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673493236, "dur": 307, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B47D5ED0AB2E73A1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751441673493614, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751441673493764, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751441673493913, "dur": 396, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751441673494450, "dur": 346, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751441673494887, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor-firstpass.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751441673495009, "dur": 228, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor-firstpass.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751441673495244, "dur": 1786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673497032, "dur": 1368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673498401, "dur": 1226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673499629, "dur": 1364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673501066, "dur": 1172, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Core\\BlendManager.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751441673500994, "dur": 2460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673503455, "dur": 992, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673504448, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673505548, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673506494, "dur": 988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673507483, "dur": 981, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673508530, "dur": 1685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673510215, "dur": 1053, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673511269, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673511387, "dur": 640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751441673512085, "dur": 1683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751441673513769, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673514089, "dur": 880, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673514981, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673515532, "dur": 612, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673516145, "dur": 200, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673516351, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673516419, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673516502, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673516615, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673516732, "dur": 756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673517488, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673517825, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673518050, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673518457, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673518533, "dur": 838, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673519371, "dur": 468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673519839, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673520076, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673520209, "dur": 742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673520952, "dur": 821, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673521773, "dur": 1077, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673522850, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673523126, "dur": 1072, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673524198, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673524354, "dur": 1094, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441673525449, "dur": 810818, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441674336269, "dur": 2009, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441674338286, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441674338353, "dur": 216468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673459775, "dur": 31756, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673491539, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_00CD99E311DC019E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751441673491812, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673492098, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_0660C742CA2CCB37.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751441673492211, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673492298, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_6E64691D4ACC974A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751441673492413, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673492629, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_963BCDCD478EB63D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751441673492952, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673493236, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_39F0A7015032F01B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751441673493406, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673493530, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751441673493688, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673493800, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673493878, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751441673493960, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751441673494039, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673494090, "dur": 365, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751441673494503, "dur": 221, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751441673494727, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751441673495017, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673495073, "dur": 345, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751441673495420, "dur": 1087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673496508, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673497449, "dur": 1382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673498833, "dur": 1463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673501029, "dur": 1205, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\inspectors\\AnimationPlayableAssetInspector.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751441673500298, "dur": 2568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673502866, "dur": 1066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673503933, "dur": 980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673504914, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673505967, "dur": 970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673506938, "dur": 829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673507767, "dur": 760, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673508528, "dur": 1691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673510219, "dur": 1008, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673511227, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673511416, "dur": 934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751441673512407, "dur": 1898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751441673514306, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673514429, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673514513, "dur": 988, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673515502, "dur": 652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673516154, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673516347, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673516652, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673516770, "dur": 711, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673517482, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673517794, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673518041, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673518474, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673518548, "dur": 797, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673519346, "dur": 479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673519825, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673520115, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673520230, "dur": 724, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673520955, "dur": 875, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673521830, "dur": 970, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673522801, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673523113, "dur": 1083, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673524197, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673524339, "dur": 1090, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673525429, "dur": 66499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673596227, "dur": 824, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 7, "ts": 1751441673591931, "dur": 5126, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441673597058, "dur": 739267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441674336326, "dur": 218485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673459852, "dur": 31694, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673491551, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_BF1A247579C7383B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441673491689, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673491782, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_592CC89A6471077C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441673491903, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673491981, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_4FB16FC3A796B887.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441673492083, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673492195, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B1475218F12198D8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441673492317, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673492387, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_00433992BAF13A73.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441673492482, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673492563, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_00433992BAF13A73.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441673492633, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C5419324AD6BC088.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441673492844, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673492927, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C5419324AD6BC088.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441673493135, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_A3850CF6800E4193.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441673493239, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673493318, "dur": 672, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_A3850CF6800E4193.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441673494144, "dur": 407, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1751441673494722, "dur": 432, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751441673495157, "dur": 1263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673496421, "dur": 1171, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673497593, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673498490, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673499238, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673501019, "dur": 1189, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\TimelineEditor.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751441673500132, "dur": 2215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673502348, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673503422, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673504438, "dur": 935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673505374, "dur": 969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673506344, "dur": 1180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673507525, "dur": 950, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673508497, "dur": 1700, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673510197, "dur": 1012, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673511209, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673511373, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441673511835, "dur": 1196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751441673513032, "dur": 1462, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673514580, "dur": 917, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673515548, "dur": 616, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673516165, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673516368, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673516596, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673516742, "dur": 719, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673517521, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673517804, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673518063, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673518476, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673518561, "dur": 803, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673519365, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673519859, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673520098, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673520225, "dur": 728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673520954, "dur": 831, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673521786, "dur": 1010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673522797, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673523107, "dur": 1096, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673524204, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673524365, "dur": 1070, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441673525436, "dur": 810834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441674336271, "dur": 218457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673459969, "dur": 31590, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673491564, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_80B8935D38233098.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441673491697, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673491797, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_970778ACBFA20360.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441673491986, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673492087, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_051DE14E9379244A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441673492321, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673492446, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_95C124FA2E4BBBB0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441673492539, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673492683, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_67F30B299A5D5154.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441673492949, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673493061, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9428A5C8BBBE8A51.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441673493163, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673493232, "dur": 294, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9428A5C8BBBE8A51.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441673493736, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751441673493903, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751441673494032, "dur": 191, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751441673494226, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751441673494412, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673494473, "dur": 393, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751441673494869, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673495060, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751441673495162, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673496292, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673497238, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673498229, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673499282, "dur": 1305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673501054, "dur": 1151, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.splines\\Editor\\GUI\\SplineGUIUtility.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751441673500588, "dur": 1926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673502515, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673503520, "dur": 974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673504495, "dur": 1002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673505498, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673506623, "dur": 1544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673508168, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673508523, "dur": 1726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673510250, "dur": 1007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673511258, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673511419, "dur": 1901, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673513320, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673513552, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673513751, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673514111, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673514434, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673514524, "dur": 996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673515520, "dur": 616, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673516208, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673516410, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673516498, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673516562, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673516616, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673516749, "dur": 716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673517465, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673517790, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673518040, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673518467, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673518534, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673518599, "dur": 759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673519359, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673519823, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673520069, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673520244, "dur": 716, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673520961, "dur": 808, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673521770, "dur": 1033, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673522803, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673523111, "dur": 1097, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673524209, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673524357, "dur": 1080, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441673525438, "dur": 810819, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441674336322, "dur": 216578, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751441674336265, "dur": 216638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 9, "ts": 1751441674553076, "dur": 1526, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751441673460073, "dur": 31498, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673491575, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_16A54265357DDF1F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441673491743, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_F4165E534DD27516.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441673491844, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673491940, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_C2E93A20F98E8229.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441673492185, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673492264, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_5AC43434136967AD.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441673492434, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673492592, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_20E0F9318BFD3B5B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441673492808, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673493010, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E8D1A39870CABAC1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441673493175, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673493279, "dur": 1060, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E8D1A39870CABAC1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441673494362, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441673494560, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673494630, "dur": 13744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751441673508376, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673508504, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673508594, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441673508815, "dur": 1226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751441673510042, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673510197, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673510289, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441673510501, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751441673511103, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673511213, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673511360, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441673511850, "dur": 2440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751441673514291, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673514400, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673514498, "dur": 430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441673515007, "dur": 2662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751441673517670, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673517787, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673517861, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441673518119, "dur": 1588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751441673519708, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673519825, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673519892, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441673520197, "dur": 625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751441673520823, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673520938, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673521012, "dur": 782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673521794, "dur": 1028, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673522823, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673523140, "dur": 1060, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673524200, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673524358, "dur": 1101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441673525459, "dur": 810879, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441674336339, "dur": 218460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673460154, "dur": 31430, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673491588, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FCC5A8FB58ECD1D2.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441673491714, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673491805, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_D3BB5BABC9B6A39A.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441673491917, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673491996, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_6C2CA7D176733A22.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441673492103, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673492231, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CB3FD07F3B098D2B.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441673492566, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673492821, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D7BFC8363EAC4767.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441673493030, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673493174, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751441673493349, "dur": 283, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751441673493638, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673493698, "dur": 1048, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751441673494748, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751441673494879, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751441673495069, "dur": 303, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1751441673495374, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673496653, "dur": 1387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673498041, "dur": 1510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673499552, "dur": 1134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673501047, "dur": 1143, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing\\PostProcessing\\Editor\\PostProcessLayerEditor.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751441673500687, "dur": 2011, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673502699, "dur": 839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673503539, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673504473, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673505229, "dur": 996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673506226, "dur": 857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673507084, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673507383, "dur": 1123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673508507, "dur": 1680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673510240, "dur": 985, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673511226, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673511401, "dur": 443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441673511845, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673511928, "dur": 1071, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751441673513000, "dur": 1035, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673514110, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673514170, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673514425, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673514527, "dur": 979, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673515506, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673516140, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673516349, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673516498, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673516620, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673516765, "dur": 697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673517463, "dur": 328, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673517792, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673518038, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673518537, "dur": 807, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673519396, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673519838, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673520073, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673520215, "dur": 726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673520942, "dur": 805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673521790, "dur": 1016, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673522806, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673523119, "dur": 1093, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673524215, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673524380, "dur": 1077, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441673525457, "dur": 810878, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441674336336, "dur": 218498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673460227, "dur": 31369, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673491600, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_493844D967613FA0.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441673491706, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673491788, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B52CD1DFCA664AB3.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441673491980, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673492060, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_4E756196A062909D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441673492154, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673492236, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_976F9C96B5CDAC08.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441673492338, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673492453, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3C810AF224C3A909.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441673492551, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673492737, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E0B83A434CA2B946.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441673492985, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673493099, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441673493304, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673493399, "dur": 376, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441673493800, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673493871, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 12, "ts": 1751441673493942, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Runtime.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751441673494146, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673494269, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751441673494489, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673494612, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673494681, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751441673494827, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751441673495031, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673495115, "dur": 1558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673496675, "dur": 1165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673497841, "dur": 1547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673499389, "dur": 1387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673501076, "dur": 1151, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing\\PostProcessing\\Editor\\Decorators\\TrackballDecorator.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751441673500778, "dur": 2436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673503216, "dur": 901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673504118, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673505189, "dur": 1041, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673506246, "dur": 809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673507056, "dur": 936, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673507992, "dur": 545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673508537, "dur": 1701, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673510238, "dur": 976, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673511215, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673511360, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441673511739, "dur": 247, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673511997, "dur": 1219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751441673513217, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673513539, "dur": 895, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673514450, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441673514843, "dur": 1284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751441673516128, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673516489, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673516612, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441673516893, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673517185, "dur": 932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751441673518118, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673518554, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673518645, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441673518941, "dur": 1085, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751441673520028, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673520239, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673520312, "dur": 638, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673520950, "dur": 804, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673521755, "dur": 1043, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673522799, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673523117, "dur": 1111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673524228, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673524349, "dur": 1082, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673525432, "dur": 71668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441673597101, "dur": 739201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441674336304, "dur": 218555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673460719, "dur": 30968, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673491692, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5543A9502BBA10BA.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751441673491831, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673491989, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751441673492193, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673492276, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_173731A5F5D7E041.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751441673492398, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673492546, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F275A7F2C1FA929A.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751441673492760, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673492859, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F275A7F2C1FA929A.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751441673492987, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_09E134ACBD96AA82.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751441673493124, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673493225, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_696347C4732CD601.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751441673493347, "dur": 315, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_696347C4732CD601.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751441673493729, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751441673493990, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1751441673494197, "dur": 474, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751441673494676, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751441673494973, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673495052, "dur": 218, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751441673495273, "dur": 1676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673496950, "dur": 719, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673497670, "dur": 1172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673498843, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673500151, "dur": 848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673501060, "dur": 1177, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Behaviours\\CinemachineShotQualityEvaluator.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751441673501000, "dur": 2001, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673503002, "dur": 922, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673503925, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673504984, "dur": 1043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673506028, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673506763, "dur": 1639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673508403, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673508495, "dur": 1699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673510194, "dur": 1034, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673511229, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673511426, "dur": 1898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673513325, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673513541, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673513638, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673514134, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673514453, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673514514, "dur": 1007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673515522, "dur": 619, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673516142, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673516358, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673516487, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673516614, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751441673516940, "dur": 984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751441673517925, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673518043, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673518140, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673518458, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673518532, "dur": 150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673518720, "dur": 622, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673519400, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673519837, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673520067, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673520239, "dur": 699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673520938, "dur": 808, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673521795, "dur": 1013, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673522808, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673523109, "dur": 1076, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673524240, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673524341, "dur": 1084, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441673525485, "dur": 810802, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441674336288, "dur": 218458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673460299, "dur": 31309, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673491612, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_989B89A6DC960046.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441673491724, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673491866, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_7F1C5DFD0AD39F77.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441673492018, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673492113, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_5E52B5CE937E0ED1.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441673492232, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673492313, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8B525A02B9DF9C7E.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441673492409, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673492473, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8B525A02B9DF9C7E.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441673492534, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FF5FCB0F465F7311.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441673492786, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673493242, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673493308, "dur": 1167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1751441673494480, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441673494766, "dur": 13554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751441673508322, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673508489, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673508567, "dur": 1623, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673510253, "dur": 977, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673511231, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673511441, "dur": 1870, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673513312, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673513576, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673513658, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673514129, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673514438, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673514517, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673514591, "dur": 912, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673515504, "dur": 634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673516138, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673516353, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673516486, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673516547, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673516617, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673516763, "dur": 726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673517489, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673517785, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673518051, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673518455, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673518536, "dur": 834, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673519370, "dur": 487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673519858, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673520086, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673520228, "dur": 729, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673520958, "dur": 801, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673521759, "dur": 1024, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673522834, "dur": 304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673523139, "dur": 1071, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673524210, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673524388, "dur": 1086, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441673525475, "dur": 810818, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441674336294, "dur": 218497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673460377, "dur": 31251, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673491636, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_B01BEAEDCAC9F962.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441673491870, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673492038, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_F24F3CD4D4D128AB.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441673492230, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673492404, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6678507EC9682BC1.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441673492596, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673492829, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B56095C3DA730DAC.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441673493013, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_42EBD154127454A5.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441673493121, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673493324, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441673493440, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673493595, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751441673493803, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673493919, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751441673493975, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751441673494146, "dur": 677, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751441673494826, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751441673494912, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673494972, "dur": 332, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751441673495308, "dur": 1679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673496988, "dur": 1035, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673498023, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673499106, "dur": 1197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673501065, "dur": 1177, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Animation\\BindingTreeViewDataSource.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751441673500304, "dur": 2087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673502392, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673503484, "dur": 886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673504371, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673505198, "dur": 1031, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673506229, "dur": 716, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673506946, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673507859, "dur": 656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673508516, "dur": 1711, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673510227, "dur": 1008, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673511235, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673511405, "dur": 579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441673512046, "dur": 1811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751441673513858, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673513992, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673514105, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673514503, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441673515019, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673515155, "dur": 1281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751441673516437, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673516627, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673516760, "dur": 725, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673517487, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673517782, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673517869, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441673518165, "dur": 1774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751441673519940, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673520074, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673520240, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441673520514, "dur": 1107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751441673521622, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673521759, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673521849, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441673522101, "dur": 873, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751441673522975, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673523108, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673523174, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441673523413, "dur": 653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751441673524067, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673524194, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673524328, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441673524643, "dur": 651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751441673525295, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673525452, "dur": 892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751441673527200, "dur": 155, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441674312797, "dur": 53, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441673528451, "dur": 787091, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751441674337492, "dur": 405, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751441674336259, "dur": 1869, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751441674338130, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441674338284, "dur": 216500, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673460508, "dur": 31138, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673491651, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_44286934693FF148.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441673491772, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673491834, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_44286934693FF148.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441673491903, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_2BCC56A3C6071870.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441673492084, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673492225, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_921E3B2D436CDAE7.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441673492404, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673492606, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_FD8C0D7AD7503969.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441673492778, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673492963, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3EA5AD5E9A933796.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441673493122, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673493228, "dur": 217, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3EA5AD5E9A933796.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441673493485, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673493557, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751441673493798, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673493874, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751441673494002, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673494067, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1751441673494245, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751441673494670, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673494758, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751441673494986, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673495048, "dur": 155, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751441673495525, "dur": 539, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem\\Tests\\TestFixture\\InputTestFixture.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751441673495205, "dur": 1272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673496477, "dur": 1064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673497542, "dur": 1452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673498995, "dur": 1241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673501063, "dur": 1159, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\inspectors\\EditorClipFactory.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751441673500236, "dur": 2057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673502294, "dur": 1066, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673503361, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673504375, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673505466, "dur": 1083, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673506550, "dur": 1833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673508384, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673508531, "dur": 1678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673510209, "dur": 1007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673511217, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673511368, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441673511753, "dur": 1144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673512906, "dur": 1402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751441673514309, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673514429, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673514605, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441673514929, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673515061, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441673515136, "dur": 2062, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1751441673517199, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673517470, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673517541, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673517793, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673518043, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673518469, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673518556, "dur": 799, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673519356, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673519866, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673520069, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673520205, "dur": 740, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673520945, "dur": 871, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673521816, "dur": 977, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673522793, "dur": 304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673523147, "dur": 1046, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673524193, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673524364, "dur": 1118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441673525483, "dur": 810815, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441674336300, "dur": 218481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673460653, "dur": 31015, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673491677, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_1C55A2A4A7EC33E2.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751441673491913, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673492052, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E009CCFB4A5E9FFB.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751441673492242, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673492380, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751441673492555, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673492853, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_A7B3B5FA99331E00.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751441673493054, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673493212, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673493275, "dur": 750, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1751441673494031, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673494229, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673494279, "dur": 620, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751441673494901, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751441673495056, "dur": 513, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751441673495570, "dur": 1792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673497363, "dur": 1507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673498872, "dur": 1338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673501011, "dur": 1187, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Manipulators\\HeaderSplitterManipulator.cs"}}, {"pid": 12345, "tid": 17, "ts": 1751441673500211, "dur": 2412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673502624, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673503662, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673504940, "dur": 1010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673505951, "dur": 915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673506867, "dur": 1096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673507964, "dur": 502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673508499, "dur": 1714, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673510214, "dur": 1039, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673511254, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673511421, "dur": 1901, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673513322, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673513538, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673513686, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673513986, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673514048, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673514099, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673514432, "dur": 726, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673515169, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673515508, "dur": 625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673516202, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673516365, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673516488, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673516547, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673516619, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673516747, "dur": 721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673517468, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673517787, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673518047, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673518546, "dur": 817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673519364, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673519842, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673520091, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673520222, "dur": 734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673520957, "dur": 805, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673521763, "dur": 1041, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673522805, "dur": 330, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673523136, "dur": 1065, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673524202, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673524333, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751441673524678, "dur": 749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673525428, "dur": 876, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441673526305, "dur": 810000, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441674336306, "dur": 218451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673460832, "dur": 30878, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673491718, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_9DC68FB86218AF77.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751441673492043, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673492189, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_00B5CEDD1ED5F4B3.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751441673492533, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673492644, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0D29C9963CEF79A6.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751441673492885, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673492964, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0D29C9963CEF79A6.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751441673493070, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_69199FEE963F7F27.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751441673493177, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673493243, "dur": 261, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_69199FEE963F7F27.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751441673493555, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673493720, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751441673493871, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673493927, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751441673494054, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673494159, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673494240, "dur": 255, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751441673494535, "dur": 318, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1751441673494859, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673494990, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673495058, "dur": 333, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1751441673495393, "dur": 1470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673496865, "dur": 1244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673498120, "dur": 1518, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673501040, "dur": 1227, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Utilities\\CustomTrackDrawerAttribute.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751441673499639, "dur": 2628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673502268, "dur": 1000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673503269, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673504069, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673504963, "dur": 1148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673506112, "dur": 496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673506609, "dur": 1786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673508396, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673508525, "dur": 1685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673510211, "dur": 1009, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673511221, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673511395, "dur": 580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751441673511976, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673512152, "dur": 3254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751441673515407, "dur": 1110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673516555, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673516642, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751441673517082, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673517243, "dur": 1332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751441673518576, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673518715, "dur": 645, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673519361, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673519820, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673520108, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673520257, "dur": 701, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673520959, "dur": 812, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673521772, "dur": 1039, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673522812, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673523121, "dur": 1078, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673524199, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673524344, "dur": 1119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441673525464, "dur": 810820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441674336285, "dur": 218440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673460941, "dur": 30792, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673491741, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8ECA4E4329F1BADB.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751441673492031, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673492168, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1830F8D8229EF0EB.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751441673492362, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673492522, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E522F23A96C2507B.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751441673492787, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673492863, "dur": 141, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E522F23A96C2507B.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751441673493079, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1751441673493405, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751441673493619, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673493738, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673493816, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673493873, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1751441673494081, "dur": 238, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1751441673494498, "dur": 571, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1751441673495075, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751441673495167, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673496363, "dur": 1444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673497809, "dur": 1564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673499374, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673501043, "dur": 1183, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.splines\\Editor\\Utilities\\EditorSplineUtility.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751441673500382, "dur": 2277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673502660, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673503751, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673504736, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673505663, "dur": 837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673506500, "dur": 1328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673507829, "dur": 632, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673508467, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673508536, "dur": 1680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673510217, "dur": 1001, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673511219, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673511437, "dur": 589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751441673512028, "dur": 1259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673513340, "dur": 1512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1751441673514854, "dur": 1828, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673516744, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673516824, "dur": 649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673517478, "dur": 321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673517799, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673518053, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673518471, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673518571, "dur": 801, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673519372, "dur": 449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673519821, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673520066, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673520200, "dur": 748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673520949, "dur": 856, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673521805, "dur": 986, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673522791, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673523106, "dur": 1083, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673524189, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673524338, "dur": 1085, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441673525461, "dur": 810859, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441674336321, "dur": 218446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673459384, "dur": 31991, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673491379, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_10CDAFD975BC0AB7.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441673491770, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673491871, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_711E053178EA7BF1.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441673491998, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673492106, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_C287EB2677BA7F93.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441673492271, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_5E37763FF8B95A1F.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441673492364, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673492458, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_5E37763FF8B95A1F.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441673492573, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_31922632D84128F5.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441673492866, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9F68AD12AC89760A.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441673492972, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673493085, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441673493193, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673493312, "dur": 453, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441673493805, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673493881, "dur": 270, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1751441673494173, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673494251, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751441673494505, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1751441673494697, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751441673494882, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor-firstpass.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751441673494963, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673495015, "dur": 308, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor-firstpass.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751441673495325, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673496597, "dur": 1259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673497857, "dur": 1593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673499451, "dur": 1223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673501017, "dur": 1155, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.splines\\Editor\\Controls\\DirectManipulation.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751441673500676, "dur": 2291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673502968, "dur": 1042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673504011, "dur": 1036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673505047, "dur": 1117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673506165, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673506915, "dur": 927, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673507842, "dur": 654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673508497, "dur": 1701, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673510199, "dur": 1005, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673511256, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673511378, "dur": 535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441673511914, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673512078, "dur": 1795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751441673513874, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673514047, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673514125, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441673514669, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673515056, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441673515117, "dur": 1362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751441673516480, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673516604, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673516684, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673516747, "dur": 750, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673517498, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673517801, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673518048, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673518539, "dur": 810, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673519349, "dur": 486, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673519836, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673520064, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673520186, "dur": 783, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673520969, "dur": 807, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673521776, "dur": 1034, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673522810, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673523156, "dur": 1049, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673524205, "dur": 186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673524392, "dur": 1047, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441673525440, "dur": 810841, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441674336283, "dur": 218460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673461176, "dur": 30588, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673491769, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_030C0AE8F7F2C9CD.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751441673491910, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673492005, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_61E5A1A2114B6332.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751441673492159, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673492243, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_889FFFE959BEFFC2.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751441673492348, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673492439, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_97D91564DB667E63.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751441673492546, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673492792, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_14A21DFAD1DFC142.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751441673492957, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673493234, "dur": 225, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1751441673493463, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751441673493590, "dur": 245, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1751441673493887, "dur": 166, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1751441673494096, "dur": 315, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1751441673494430, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673494515, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673494708, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751441673495001, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673495098, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673496138, "dur": 1065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673497204, "dur": 1018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673498223, "dur": 1240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673499464, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673501061, "dur": 1154, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.splines\\Editor\\GUI\\Editors\\EmbeddedSplineDataPropertyDrawer.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751441673500665, "dur": 1906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673502572, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673503606, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673504465, "dur": 862, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673505328, "dur": 1152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673506481, "dur": 1016, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673507498, "dur": 1021, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673508520, "dur": 1742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673510263, "dur": 948, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673511212, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673511367, "dur": 772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751441673512140, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673512515, "dur": 931, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1751441673513447, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673513647, "dur": 2427, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673516093, "dur": 588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751441673516683, "dur": 574, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673517268, "dur": 1623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1751441673518893, "dur": 385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673519366, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673519461, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673519821, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673519894, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673520063, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673520190, "dur": 743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673520933, "dur": 822, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673521756, "dur": 1031, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673522787, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673523101, "dur": 1085, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673524187, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673524333, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751441673524600, "dur": 862, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441673525462, "dur": 810828, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441674336292, "dur": 218439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673459451, "dur": 31936, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673491391, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B014F0E06EB9C8B3.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751441673491723, "dur": 298, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673492074, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_DCB00145011AACBC.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751441673492200, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673492304, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D22D11490AEAA9B0.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751441673492401, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673492477, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D22D11490AEAA9B0.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751441673492609, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_DC79D8DB49767D4F.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751441673492854, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673492938, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_DC79D8DB49767D4F.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751441673493136, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673493237, "dur": 238, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751441673493512, "dur": 209, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1751441673493724, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751441673493863, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673493926, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751441673494085, "dur": 540, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751441673494657, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673494744, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751441673494917, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673495059, "dur": 346, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1751441673495407, "dur": 1195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673496603, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673497473, "dur": 924, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673498398, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673499638, "dur": 1272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673501045, "dur": 1155, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Core\\IShotQualityEvaluator.cs"}}, {"pid": 12345, "tid": 22, "ts": 1751441673500911, "dur": 2188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673503100, "dur": 874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673503975, "dur": 1197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673505173, "dur": 1275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673506449, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673507277, "dur": 80, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673507385, "dur": 1092, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673508499, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673508572, "dur": 1633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673510205, "dur": 1031, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673511236, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673511444, "dur": 1864, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673513356, "dur": 198, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673513555, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673513641, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673513868, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673513994, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673514108, "dur": 287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673514535, "dur": 981, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673515516, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673516149, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673516350, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673516550, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673516632, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673516746, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673516812, "dur": 671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673517484, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673517800, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673518090, "dur": 382, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673518472, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673518589, "dur": 763, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673519353, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673519818, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673520061, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673520193, "dur": 744, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673520937, "dur": 815, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673521752, "dur": 1061, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673522813, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673523151, "dur": 1055, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673524206, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673524373, "dur": 1074, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441673525447, "dur": 810905, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441674336353, "dur": 218454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673461285, "dur": 30504, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673491794, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D447C5E6C0383BF9.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441673492010, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673492085, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F8AA39E8D5CE00CF.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441673492206, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673492288, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_ED94C9E717A89F9F.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441673492401, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673492501, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B5DB3F27CCAA63FE.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441673492595, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673492780, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_BDFB8953D401853F.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441673492927, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673493161, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_3AA3364A40DB9F8B.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441673493296, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673493387, "dur": 349, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_3AA3364A40DB9F8B.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441673493802, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673493918, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751441673494251, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751441673494351, "dur": 430, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751441673494782, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751441673494979, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673495063, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673495130, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673496308, "dur": 1213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673497521, "dur": 1178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673498700, "dur": 1285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673499987, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673501058, "dur": 1173, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Utilities\\ReferenceCollector.cs"}}, {"pid": 12345, "tid": 23, "ts": 1751441673501057, "dur": 2232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673503290, "dur": 962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673504253, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673505117, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673506199, "dur": 935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673507218, "dur": 183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673507401, "dur": 1068, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673508500, "dur": 1695, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673510196, "dur": 1049, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673511246, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673511435, "dur": 1883, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673513318, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673513540, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673513708, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673514093, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673514428, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673514574, "dur": 925, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673515553, "dur": 604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673516157, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673516352, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673516549, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673516615, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673516785, "dur": 684, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673517470, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673517851, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673518045, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673518462, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673518534, "dur": 832, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673519366, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673519832, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673520057, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673520189, "dur": 739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673520972, "dur": 795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673521767, "dur": 1071, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673522838, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673523128, "dur": 1079, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673524207, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673524375, "dur": 1044, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673525422, "dur": 759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1751441673526182, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441673526342, "dur": 809966, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441674336309, "dur": 218487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673459529, "dur": 31903, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673491440, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_438C72DDFEF65815.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441673491828, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673492020, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_6FBB1366163B00FB.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441673492128, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673492209, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_42AEDBADC03A3022.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441673492332, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673492410, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_783D7153E7AEE314.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441673492525, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673492670, "dur": 351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_54605A4C765A4A77.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441673493023, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673493281, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9C39811BC6DD0BF6.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441673493425, "dur": 317, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9C39811BC6DD0BF6.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441673493863, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673493945, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751441673494173, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673494261, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751441673494420, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 24, "ts": 1751441673494593, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751441673494707, "dur": 267, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751441673494979, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673495124, "dur": 1275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673496400, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673497712, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673498632, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673499417, "dur": 899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673501059, "dur": 1214, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Actions\\TrackAction.cs"}}, {"pid": 12345, "tid": 24, "ts": 1751441673500317, "dur": 2434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673502752, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673503932, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673504775, "dur": 1110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673505886, "dur": 977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673506863, "dur": 1407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673508270, "dur": 240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673508510, "dur": 1690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673510201, "dur": 1012, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673511213, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673511362, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441673511630, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673511826, "dur": 1178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751441673513005, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673513316, "dur": 624, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673513955, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441673514311, "dur": 1031, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751441673515342, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673515510, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673515774, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673516146, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673516357, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673516599, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673516735, "dur": 735, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673517471, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673517795, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673518052, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673518482, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673518569, "dur": 793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673519362, "dur": 482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673519844, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673520093, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673520246, "dur": 697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673520944, "dur": 820, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673521765, "dur": 1021, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673522786, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673523104, "dur": 1087, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673524192, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673524337, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441673524696, "dur": 747, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441673525444, "dur": 810866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441674336311, "dur": 218461, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441674583224, "dur": 4826, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 15948, "tid": 58934, "ts": 1751441674623068, "dur": 3988, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 15948, "tid": 58934, "ts": 1751441674627117, "dur": 4736, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 15948, "tid": 58934, "ts": 1751441674614882, "dur": 18776, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}
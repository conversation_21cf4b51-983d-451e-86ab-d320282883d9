{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 15948, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 15948, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 15948, "tid": 58885, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 15948, "tid": 58885, "ts": 1751441223477498, "dur": 8982, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 15948, "tid": 58885, "ts": 1751441223498968, "dur": 2796, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 15948, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 15948, "tid": 1, "ts": 1751441222455995, "dur": 9091, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 15948, "tid": 1, "ts": 1751441222465090, "dur": 105831, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 15948, "tid": 1, "ts": 1751441222570936, "dur": 61990, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 15948, "tid": 58885, "ts": 1751441223501775, "dur": 18, "ph": "X", "name": "", "args": {}}, {"pid": 15948, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222452021, "dur": 23379, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222475405, "dur": 978712, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222478769, "dur": 8593, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222487372, "dur": 5112, "ph": "X", "name": "ProcessMessages 20484", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222492493, "dur": 935, "ph": "X", "name": "ReadAsync 20484", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222493438, "dur": 57, "ph": "X", "name": "ProcessMessages 20489", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222493499, "dur": 147, "ph": "X", "name": "ReadAsync 20489", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222493653, "dur": 7, "ph": "X", "name": "ProcessMessages 1492", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222493662, "dur": 106, "ph": "X", "name": "ReadAsync 1492", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222493775, "dur": 4, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222493782, "dur": 79, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222493868, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222493873, "dur": 128, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222494009, "dur": 2, "ph": "X", "name": "ProcessMessages 227", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222494014, "dur": 110, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222494131, "dur": 5, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222494138, "dur": 100, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222494246, "dur": 4, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222494252, "dur": 94, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222494353, "dur": 4, "ph": "X", "name": "ProcessMessages 476", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222494360, "dur": 104, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222494471, "dur": 3, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222494477, "dur": 101, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222494585, "dur": 5, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222494594, "dur": 107, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222494709, "dur": 4, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222494716, "dur": 82, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222494807, "dur": 3, "ph": "X", "name": "ProcessMessages 218", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222494813, "dur": 118, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222494940, "dur": 5, "ph": "X", "name": "ProcessMessages 760", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222494948, "dur": 102, "ph": "X", "name": "ReadAsync 760", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222495058, "dur": 4, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222495064, "dur": 100, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222495172, "dur": 4, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222495178, "dur": 86, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222495271, "dur": 3, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222495277, "dur": 102, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222495386, "dur": 4, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222495392, "dur": 108, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222495507, "dur": 4, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222495513, "dur": 110, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222495630, "dur": 4, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222495637, "dur": 77, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222495720, "dur": 3, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222495726, "dur": 117, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222495850, "dur": 8, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222495861, "dur": 96, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222495963, "dur": 4, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222495970, "dur": 136, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222496115, "dur": 4, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222496122, "dur": 105, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222496234, "dur": 4, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222496240, "dur": 137, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222496386, "dur": 4, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222496394, "dur": 119, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222496520, "dur": 5, "ph": "X", "name": "ProcessMessages 990", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222496527, "dur": 85, "ph": "X", "name": "ReadAsync 990", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222496619, "dur": 3, "ph": "X", "name": "ProcessMessages 184", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222496625, "dur": 105, "ph": "X", "name": "ReadAsync 184", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222496737, "dur": 4, "ph": "X", "name": "ProcessMessages 658", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222496747, "dur": 102, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222496861, "dur": 4, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222496868, "dur": 123, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222496998, "dur": 4, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222497004, "dur": 97, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222497108, "dur": 3, "ph": "X", "name": "ProcessMessages 496", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222497114, "dur": 112, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222497245, "dur": 2, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222497249, "dur": 95, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222497352, "dur": 5, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222497360, "dur": 90, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222497454, "dur": 3, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222497458, "dur": 77, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222497543, "dur": 3, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222497549, "dur": 86, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222497639, "dur": 2, "ph": "X", "name": "ProcessMessages 706", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222497642, "dur": 79, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222497729, "dur": 3, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222497734, "dur": 88, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222497827, "dur": 2, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222497831, "dur": 69, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222497907, "dur": 3, "ph": "X", "name": "ProcessMessages 223", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222497913, "dur": 86, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222498003, "dur": 2, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222498006, "dur": 78, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222498091, "dur": 4, "ph": "X", "name": "ProcessMessages 534", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222498097, "dur": 87, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222498188, "dur": 2, "ph": "X", "name": "ProcessMessages 749", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222498192, "dur": 77, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222498276, "dur": 3, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222498281, "dur": 66, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222498351, "dur": 2, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222498355, "dur": 71, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222498432, "dur": 3, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222498438, "dur": 87, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222498530, "dur": 2, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222498533, "dur": 76, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222498616, "dur": 3, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222498622, "dur": 94, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222498723, "dur": 2, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222498727, "dur": 66, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222498800, "dur": 3, "ph": "X", "name": "ProcessMessages 74", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222498806, "dur": 99, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222498909, "dur": 2, "ph": "X", "name": "ProcessMessages 691", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222498913, "dur": 78, "ph": "X", "name": "ReadAsync 691", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222498998, "dur": 3, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222499003, "dur": 92, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222499100, "dur": 2, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222499103, "dur": 74, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222499184, "dur": 4, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222499191, "dur": 81, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222499276, "dur": 2, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222499280, "dur": 70, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222499357, "dur": 3, "ph": "X", "name": "ProcessMessages 231", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222499362, "dur": 85, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222499452, "dur": 2, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222499456, "dur": 75, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222499537, "dur": 7, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222499547, "dur": 75, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222499625, "dur": 2, "ph": "X", "name": "ProcessMessages 303", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222499629, "dur": 80, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222499718, "dur": 3, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222499724, "dur": 87, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222499816, "dur": 2, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222499820, "dur": 74, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222499901, "dur": 4, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222499907, "dur": 85, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222499996, "dur": 2, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222500000, "dur": 76, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222500083, "dur": 2, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222500088, "dur": 84, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222500176, "dur": 2, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222500180, "dur": 74, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222500260, "dur": 4, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222500266, "dur": 85, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222500356, "dur": 2, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222500360, "dur": 71, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222500435, "dur": 2, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222500438, "dur": 67, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222500513, "dur": 3, "ph": "X", "name": "ProcessMessages 155", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222500519, "dur": 88, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222500612, "dur": 2, "ph": "X", "name": "ProcessMessages 652", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222500616, "dur": 77, "ph": "X", "name": "ReadAsync 652", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222500700, "dur": 3, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222500706, "dur": 82, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222500792, "dur": 2, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222500796, "dur": 74, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222500877, "dur": 3, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222500883, "dur": 81, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222500968, "dur": 2, "ph": "X", "name": "ProcessMessages 209", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222500972, "dur": 88, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222501066, "dur": 4, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222501073, "dur": 358, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222501437, "dur": 4, "ph": "X", "name": "ProcessMessages 1383", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222501443, "dur": 247, "ph": "X", "name": "ReadAsync 1383", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222501697, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222501702, "dur": 114, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222501823, "dur": 4, "ph": "X", "name": "ProcessMessages 839", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222501830, "dur": 106, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222501942, "dur": 4, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222501949, "dur": 101, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222502056, "dur": 4, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222502063, "dur": 70, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222502140, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222502145, "dur": 114, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222502266, "dur": 4, "ph": "X", "name": "ProcessMessages 830", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222502272, "dur": 91, "ph": "X", "name": "ReadAsync 830", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222502384, "dur": 3, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222502390, "dur": 108, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222502505, "dur": 5, "ph": "X", "name": "ProcessMessages 658", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222502512, "dur": 86, "ph": "X", "name": "ReadAsync 658", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222502605, "dur": 3, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222502610, "dur": 96, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222502713, "dur": 3, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222502719, "dur": 99, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222502825, "dur": 4, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222502832, "dur": 96, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222502934, "dur": 4, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222502941, "dur": 97, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222503045, "dur": 4, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222503051, "dur": 99, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222503157, "dur": 4, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222503163, "dur": 110, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222503280, "dur": 4, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222503287, "dur": 98, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222503392, "dur": 4, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222503398, "dur": 77, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222503482, "dur": 3, "ph": "X", "name": "ProcessMessages 183", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222503487, "dur": 99, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222503592, "dur": 4, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222503599, "dur": 100, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222503706, "dur": 4, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222503712, "dur": 103, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222503822, "dur": 4, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222503828, "dur": 79, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222503914, "dur": 3, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222503920, "dur": 100, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222504027, "dur": 4, "ph": "X", "name": "ProcessMessages 694", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222504033, "dur": 97, "ph": "X", "name": "ReadAsync 694", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222504137, "dur": 4, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222504143, "dur": 95, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222504245, "dur": 4, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222504254, "dur": 82, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222504343, "dur": 3, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222504348, "dur": 97, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222504452, "dur": 4, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222504459, "dur": 96, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222504562, "dur": 4, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222504569, "dur": 99, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222504675, "dur": 4, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222504681, "dur": 81, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222504768, "dur": 3, "ph": "X", "name": "ProcessMessages 252", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222504774, "dur": 97, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222504878, "dur": 3, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222504884, "dur": 87, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222504978, "dur": 4, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222504984, "dur": 102, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222505093, "dur": 4, "ph": "X", "name": "ProcessMessages 553", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222505099, "dur": 96, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222505202, "dur": 4, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222505208, "dur": 96, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222505311, "dur": 3, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222505317, "dur": 102, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222505425, "dur": 4, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222505432, "dur": 99, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222505538, "dur": 4, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222505545, "dur": 89, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222505641, "dur": 3, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222505647, "dur": 100, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222505753, "dur": 4, "ph": "X", "name": "ProcessMessages 634", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222505762, "dur": 104, "ph": "X", "name": "ReadAsync 634", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222505872, "dur": 4, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222505879, "dur": 98, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222505984, "dur": 4, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222505990, "dur": 120, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222506119, "dur": 4, "ph": "X", "name": "ProcessMessages 120", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222506126, "dur": 130, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222506263, "dur": 5, "ph": "X", "name": "ProcessMessages 1099", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222506270, "dur": 116, "ph": "X", "name": "ReadAsync 1099", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222506394, "dur": 3, "ph": "X", "name": "ProcessMessages 375", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222506400, "dur": 87, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222506494, "dur": 3, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222506500, "dur": 63, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222506568, "dur": 2, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222506572, "dur": 62, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222506641, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222506646, "dur": 77, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222506730, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222506735, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222506802, "dur": 2, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222506805, "dur": 58, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222506870, "dur": 2, "ph": "X", "name": "ProcessMessages 235", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222506875, "dur": 84, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222506966, "dur": 3, "ph": "X", "name": "ProcessMessages 436", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222506971, "dur": 752, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222507729, "dur": 2, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222507733, "dur": 154, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222507895, "dur": 11, "ph": "X", "name": "ProcessMessages 2971", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222507908, "dur": 109, "ph": "X", "name": "ReadAsync 2971", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222508025, "dur": 3, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222508031, "dur": 97, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222508133, "dur": 2, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222508137, "dur": 67, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222508209, "dur": 2, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222508213, "dur": 67, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222508284, "dur": 2, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222508288, "dur": 66, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222508358, "dur": 2, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222508361, "dur": 66, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222508432, "dur": 2, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222508435, "dur": 66, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222508505, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222508509, "dur": 66, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222508579, "dur": 2, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222508583, "dur": 96, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222508687, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222508695, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222508783, "dur": 2, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222508787, "dur": 62, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222508856, "dur": 2, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222508861, "dur": 71, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222508937, "dur": 2, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222508941, "dur": 59, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509007, "dur": 3, "ph": "X", "name": "ProcessMessages 166", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509018, "dur": 56, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509077, "dur": 2, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509081, "dur": 49, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509134, "dur": 1, "ph": "X", "name": "ProcessMessages 135", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509137, "dur": 61, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509205, "dur": 3, "ph": "X", "name": "ProcessMessages 230", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509211, "dur": 60, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509275, "dur": 2, "ph": "X", "name": "ProcessMessages 82", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509281, "dur": 69, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509357, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509362, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509447, "dur": 2, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509451, "dur": 60, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509520, "dur": 2, "ph": "X", "name": "ProcessMessages 269", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509525, "dur": 68, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509597, "dur": 4, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509604, "dur": 61, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509672, "dur": 3, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509681, "dur": 69, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509754, "dur": 2, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509758, "dur": 66, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509831, "dur": 3, "ph": "X", "name": "ProcessMessages 218", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509837, "dur": 67, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509910, "dur": 2, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509914, "dur": 61, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509982, "dur": 3, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222509989, "dur": 67, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222510063, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222510068, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222510160, "dur": 4, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222510167, "dur": 70, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222510242, "dur": 2, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222510247, "dur": 67, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222510321, "dur": 5, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222510330, "dur": 69, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222510404, "dur": 2, "ph": "X", "name": "ProcessMessages 316", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222510408, "dur": 63, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222510478, "dur": 3, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222510483, "dur": 63, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222510551, "dur": 2, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222510555, "dur": 60, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222510622, "dur": 2, "ph": "X", "name": "ProcessMessages 233", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222510627, "dur": 49, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222510679, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222510684, "dur": 66, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222510758, "dur": 2, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222510764, "dur": 80, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222510849, "dur": 2, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222510853, "dur": 59, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222510920, "dur": 4, "ph": "X", "name": "ProcessMessages 78", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222510927, "dur": 67, "ph": "X", "name": "ReadAsync 78", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222510997, "dur": 2, "ph": "X", "name": "ProcessMessages 422", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222511001, "dur": 63, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222511071, "dur": 3, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222511077, "dur": 64, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222511146, "dur": 2, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222511151, "dur": 59, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222511218, "dur": 2, "ph": "X", "name": "ProcessMessages 220", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222511222, "dur": 64, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222511290, "dur": 2, "ph": "X", "name": "ProcessMessages 266", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222511294, "dur": 56, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222511357, "dur": 3, "ph": "X", "name": "ProcessMessages 77", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222511363, "dur": 49, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222511416, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222511421, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222511490, "dur": 3, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222511496, "dur": 114, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222511619, "dur": 4, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222511625, "dur": 142, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222511774, "dur": 3, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222511780, "dur": 83, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222511868, "dur": 2, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222511873, "dur": 66, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222511944, "dur": 2, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222511948, "dur": 57, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222512010, "dur": 2, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222512014, "dur": 57, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222512076, "dur": 2, "ph": "X", "name": "ProcessMessages 234", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222512080, "dur": 56, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222512140, "dur": 2, "ph": "X", "name": "ProcessMessages 143", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222512144, "dur": 53, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222512206, "dur": 4, "ph": "X", "name": "ProcessMessages 217", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222512213, "dur": 76, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222512293, "dur": 5, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222512300, "dur": 68, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222512375, "dur": 3, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222512382, "dur": 76, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222512462, "dur": 2, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222512467, "dur": 69, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222512542, "dur": 3, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222512551, "dur": 79, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222512635, "dur": 2, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222512639, "dur": 64, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222512711, "dur": 3, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222512717, "dur": 62, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222512782, "dur": 3, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222512787, "dur": 49, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222512841, "dur": 2, "ph": "X", "name": "ProcessMessages 133", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222512845, "dur": 82, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222512934, "dur": 3, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222512940, "dur": 74, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222513018, "dur": 2, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222513022, "dur": 69, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222513098, "dur": 4, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222513105, "dur": 63, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222513172, "dur": 2, "ph": "X", "name": "ProcessMessages 241", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222513176, "dur": 69, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222513253, "dur": 3, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222513259, "dur": 74, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222513337, "dur": 2, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222513342, "dur": 66, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222513415, "dur": 3, "ph": "X", "name": "ProcessMessages 374", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222513422, "dur": 74, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222513500, "dur": 2, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222513507, "dur": 69, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222513582, "dur": 3, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222513588, "dur": 87, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222513680, "dur": 3, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222513685, "dur": 77, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222513769, "dur": 3, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222513776, "dur": 83, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222513864, "dur": 3, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222513868, "dur": 68, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222513943, "dur": 3, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222513949, "dur": 60, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222514013, "dur": 2, "ph": "X", "name": "ProcessMessages 175", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222514017, "dur": 70, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222514096, "dur": 3, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222514103, "dur": 69, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222514176, "dur": 2, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222514180, "dur": 66, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222514253, "dur": 3, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222514259, "dur": 78, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222514341, "dur": 2, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222514345, "dur": 64, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222514417, "dur": 3, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222514423, "dur": 53, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222514479, "dur": 2, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222514483, "dur": 55, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222514542, "dur": 1, "ph": "X", "name": "ProcessMessages 246", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222514546, "dur": 68, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222514623, "dur": 4, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222514629, "dur": 79, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222514712, "dur": 2, "ph": "X", "name": "ProcessMessages 377", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222514733, "dur": 75, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222514815, "dur": 4, "ph": "X", "name": "ProcessMessages 746", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222514822, "dur": 78, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222514904, "dur": 2, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222514909, "dur": 65, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222514981, "dur": 3, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222514987, "dur": 72, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222515063, "dur": 2, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222515067, "dur": 64, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222515139, "dur": 2, "ph": "X", "name": "ProcessMessages 243", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222515145, "dur": 83, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222515232, "dur": 3, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222515237, "dur": 72, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222515316, "dur": 3, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222515322, "dur": 75, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222515401, "dur": 2, "ph": "X", "name": "ProcessMessages 504", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222515407, "dur": 65, "ph": "X", "name": "ReadAsync 504", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222515480, "dur": 4, "ph": "X", "name": "ProcessMessages 242", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222515486, "dur": 85, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222515576, "dur": 2, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222515580, "dur": 65, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222515652, "dur": 3, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222515659, "dur": 76, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222515739, "dur": 2, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222515743, "dur": 70, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222515820, "dur": 3, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222515826, "dur": 111, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222515941, "dur": 3, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222515945, "dur": 66, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222516019, "dur": 3, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222516026, "dur": 69, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222516100, "dur": 2, "ph": "X", "name": "ProcessMessages 287", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222516104, "dur": 66, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222516177, "dur": 3, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222516183, "dur": 73, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222516260, "dur": 2, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222516265, "dur": 66, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222516338, "dur": 3, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222516344, "dur": 77, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222516425, "dur": 2, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222516429, "dur": 68, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222516505, "dur": 4, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222516511, "dur": 77, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222516593, "dur": 2, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222516597, "dur": 68, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222516673, "dur": 3, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222516679, "dur": 75, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222516758, "dur": 2, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222516762, "dur": 65, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222516835, "dur": 3, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222516841, "dur": 76, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222516921, "dur": 2, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222516925, "dur": 74, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222517006, "dur": 3, "ph": "X", "name": "ProcessMessages 390", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222517012, "dur": 69, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222517086, "dur": 2, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222517090, "dur": 56, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222517154, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222517158, "dur": 63, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222517226, "dur": 3, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222517231, "dur": 57, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222517293, "dur": 2, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222517296, "dur": 67, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222517371, "dur": 3, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222517377, "dur": 73, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222517454, "dur": 5, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222517461, "dur": 61, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222517529, "dur": 3, "ph": "X", "name": "ProcessMessages 255", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222517535, "dur": 72, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222517611, "dur": 2, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222517615, "dur": 70, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222517692, "dur": 3, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222517699, "dur": 227, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222517931, "dur": 2, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222517935, "dur": 93, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222518036, "dur": 6, "ph": "X", "name": "ProcessMessages 1100", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222518045, "dur": 82, "ph": "X", "name": "ReadAsync 1100", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222518131, "dur": 2, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222518135, "dur": 65, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222518208, "dur": 3, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222518214, "dur": 76, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222518295, "dur": 2, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222518299, "dur": 70, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222518376, "dur": 3, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222518383, "dur": 71, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222518458, "dur": 2, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222518462, "dur": 67, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222518537, "dur": 3, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222518543, "dur": 76, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222518622, "dur": 2, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222518627, "dur": 67, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222518702, "dur": 3, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222518708, "dur": 74, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222518786, "dur": 2, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222518790, "dur": 62, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222518860, "dur": 4, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222518866, "dur": 66, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222518936, "dur": 2, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222518940, "dur": 53, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222518998, "dur": 2, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222519002, "dur": 321, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222519330, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222519334, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222519431, "dur": 1156, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222520598, "dur": 223, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222520835, "dur": 52, "ph": "X", "name": "ProcessMessages 1200", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222520893, "dur": 72, "ph": "X", "name": "ReadAsync 1200", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222520973, "dur": 18, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222520996, "dur": 85, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222521088, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222521094, "dur": 74, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222521174, "dur": 4, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222521181, "dur": 69, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222521259, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222521266, "dur": 80, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222521353, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222521359, "dur": 68, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222521433, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222521440, "dur": 57, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222521503, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222521507, "dur": 71, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222521585, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222521592, "dur": 80, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222521678, "dur": 4, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222521686, "dur": 87, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222521780, "dur": 5, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222521790, "dur": 78, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222521873, "dur": 3, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222521878, "dur": 49, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222521933, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222521938, "dur": 78, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222522032, "dur": 7, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222522044, "dur": 105, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222522155, "dur": 4, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222522160, "dur": 51, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222522216, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222522219, "dur": 58, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222522282, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222522286, "dur": 57, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222522349, "dur": 2, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222522354, "dur": 50, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222522412, "dur": 3, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222522418, "dur": 68, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222522495, "dur": 3, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222522501, "dur": 67, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222522572, "dur": 3, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222522577, "dur": 64, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222522646, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222522651, "dur": 57, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222522713, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222522717, "dur": 63, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222522786, "dur": 6, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222522798, "dur": 65, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222522868, "dur": 3, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222522874, "dur": 56, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222522938, "dur": 4, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222522949, "dur": 65, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222523024, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222523029, "dur": 77, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222523113, "dur": 4, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222523119, "dur": 80, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222523206, "dur": 5, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222523214, "dur": 74, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222523293, "dur": 4, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222523301, "dur": 85, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222523402, "dur": 8, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222523415, "dur": 80, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222523503, "dur": 8, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222523514, "dur": 80, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222523601, "dur": 3, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222523606, "dur": 80, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222523697, "dur": 5, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222523706, "dur": 66, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222523778, "dur": 6, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222523787, "dur": 61, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222523855, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222523862, "dur": 155, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222524022, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222524025, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222524083, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222524086, "dur": 12833, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222536928, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222536933, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222537021, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222537027, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222537097, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222537103, "dur": 95, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222537205, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222537210, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222537270, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222537273, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222537331, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222537335, "dur": 49, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222537388, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222537391, "dur": 143, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222537538, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222537542, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222537593, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222537596, "dur": 1108, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222538714, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222538722, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222538801, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222538805, "dur": 49, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222538859, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222538863, "dur": 134, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222539047, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222539051, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222539148, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222539152, "dur": 519, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222539687, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222539697, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222539770, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222539773, "dur": 105, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222539889, "dur": 3, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222539906, "dur": 277, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222540191, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222540247, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222540316, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222540321, "dur": 64, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222540403, "dur": 5, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222540412, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222540488, "dur": 6, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222540498, "dur": 59, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222540585, "dur": 5, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222540594, "dur": 390, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222541016, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222541024, "dur": 138, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222541187, "dur": 5, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222541195, "dur": 158, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222541359, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222541363, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222541427, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222541432, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222541525, "dur": 5, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222541534, "dur": 84, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222541626, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222541631, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222541790, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222541795, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222541876, "dur": 3, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222541881, "dur": 98, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222541997, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222542000, "dur": 148, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222542182, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222542186, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222542284, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222542290, "dur": 80, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222542389, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222542393, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222542514, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222542520, "dur": 126, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222542687, "dur": 3, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222542693, "dur": 89, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222542797, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222542804, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222542912, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222542917, "dur": 55, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222542993, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222542997, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222543066, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222543069, "dur": 46, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222543123, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222543144, "dur": 86, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222543237, "dur": 4, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222543331, "dur": 53, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222543388, "dur": 3, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222543393, "dur": 47, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222543441, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222543444, "dur": 9043, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222552582, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222552777, "dur": 194, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222552975, "dur": 20, "ph": "X", "name": "ProcessMessages 1700", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222553149, "dur": 128, "ph": "X", "name": "ReadAsync 1700", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222553555, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222553768, "dur": 188, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222553962, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222554020, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222554072, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222554076, "dur": 661, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222554839, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222554844, "dur": 538, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222555538, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222555542, "dur": 300, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222555847, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222555850, "dur": 257, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222556116, "dur": 1064, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441222557186, "dur": 619239, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223176440, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223176447, "dur": 120, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223176575, "dur": 4365, "ph": "X", "name": "ProcessMessages 8721", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223180947, "dur": 9718, "ph": "X", "name": "ReadAsync 8721", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223190675, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223190681, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223190727, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223190733, "dur": 546, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223191296, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223191383, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223191458, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223191464, "dur": 1125, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223192598, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223192603, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223192665, "dur": 25, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223192692, "dur": 228116, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223420819, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223420823, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223420903, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223420909, "dur": 1401, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223422315, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223422318, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223422392, "dur": 35, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223422431, "dur": 2051, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223424487, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223424489, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223424575, "dur": 885, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 15948, "tid": 12884901888, "ts": 1751441223425468, "dur": 28207, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 15948, "tid": 58885, "ts": 1751441223501795, "dur": 3716, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 15948, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 15948, "tid": 8589934592, "ts": 1751441222446489, "dur": 186489, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 15948, "tid": 8589934592, "ts": 1751441222632981, "dur": 9, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 15948, "tid": 8589934592, "ts": 1751441222632992, "dur": 2222, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 15948, "tid": 58885, "ts": 1751441223505514, "dur": 11, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 15948, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 15948, "tid": 4294967296, "ts": 1751441222409761, "dur": 1047457, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 15948, "tid": 4294967296, "ts": 1751441222418575, "dur": 14727, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 15948, "tid": 4294967296, "ts": 1751441223457754, "dur": 12496, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 15948, "tid": 4294967296, "ts": 1751441223465601, "dur": 273, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 15948, "tid": 4294967296, "ts": 1751441223470498, "dur": 35, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 15948, "tid": 58885, "ts": 1751441223505528, "dur": 91, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751441222466667, "dur": 4071, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441222470764, "dur": 1981, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441222472895, "dur": 221, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751441222473117, "dur": 2005, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441222475220, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ScriptAssemblies"}}, {"pid": 12345, "tid": 0, "ts": 1751441222475293, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_7A1FD51B51344812.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222475360, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_00CD99E311DC019E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222475420, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_BF1A247579C7383B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222475481, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_80B8935D38233098.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222475543, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_16A54265357DDF1F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222475608, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FCC5A8FB58ECD1D2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222475672, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_493844D967613FA0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222475731, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_989B89A6DC960046.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222475791, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_B01BEAEDCAC9F962.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222475850, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_44286934693FF148.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222475910, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_1C55A2A4A7EC33E2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222475970, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5543A9502BBA10BA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222476034, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_9DC68FB86218AF77.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222476094, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_52F12B5AEAC5FC8F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222476159, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_F4165E534DD27516.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222476341, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_921345D5C92C6DDE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222476406, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_030C0AE8F7F2C9CD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222476467, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_A8A2BEB223D0393B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222476525, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_592CC89A6471077C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222476591, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B52CD1DFCA664AB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222476652, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D447C5E6C0383BF9.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222476711, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_970778ACBFA20360.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222476771, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_D3BB5BABC9B6A39A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222476829, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_DC7672C0573B7C85.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222476891, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_129B2BEEEC662028.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222476952, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_7F1C5DFD0AD39F77.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222477010, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_711E053178EA7BF1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222477071, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_2BCC56A3C6071870.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222477134, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_47A61C91F1D08784.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222477193, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_C2E93A20F98E8229.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222477253, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46E217D19A6E01BD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222477320, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8ECA4E4329F1BADB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222477488, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222477554, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_61E5A1A2114B6332.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222477613, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_74513773BD43745E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222477672, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_6FBB1366163B00FB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222477733, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_F24F3CD4D4D128AB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222477790, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E009CCFB4A5E9FFB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222477850, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_6C2CA7D176733A22.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222477909, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_4E756196A062909D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222477967, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BA5431EB9EAB1D55.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222478025, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_DCB00145011AACBC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222478083, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F8AA39E8D5CE00CF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222478145, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_051DE14E9379244A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222478205, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_0660C742CA2CCB37.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222478264, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_C287EB2677BA7F93.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222478325, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_5E52B5CE937E0ED1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222478386, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_6C4C0BE13C75841D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222478447, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_7BFAB029D4D7DE5C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222478533, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1830F8D8229EF0EB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222478595, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_99CD71E244B915CF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222478656, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_00B5CEDD1ED5F4B3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222478714, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B1475218F12198D8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222478774, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_39C615B7DD834A4A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222478833, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_42AEDBADC03A3022.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222478892, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_921E3B2D436CDAE7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222478949, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CB3FD07F3B098D2B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222479008, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_976F9C96B5CDAC08.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222479066, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_889FFFE959BEFFC2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222479127, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_5AC43434136967AD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222479186, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_5E37763FF8B95A1F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222479245, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_173731A5F5D7E041.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222479303, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_7D7B641D38DA242D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222479363, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_ED94C9E717A89F9F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222479427, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_6E64691D4ACC974A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222479486, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D22D11490AEAA9B0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222479627, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8B525A02B9DF9C7E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222479692, "dur": 5782, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222485487, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_00433992BAF13A73.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222485550, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6678507EC9682BC1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222487929, "dur": 5702, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222493776, "dur": 113, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222493939, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222494152, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_BDDAB6C3AC1C4D86.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222494315, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222494434, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222494640, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441222494783, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222494892, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222495105, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441222495249, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222495366, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222495584, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441222495689, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222495804, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222496022, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441222496160, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222496271, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222496431, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441222496544, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222496690, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222496827, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Postprocessing.Runtime.ref.dll_D64FE1030D5C6CA1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222496933, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441222497064, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222497164, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222497289, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Settings.Editor.ref.dll_8A1461A5C565B4EA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222497401, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441222497515, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222497744, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441222497865, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222497928, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222498027, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222498192, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441222498305, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222498397, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222498476, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222498645, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441222498742, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222498814, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222498910, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222499103, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441222499188, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222499283, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222499473, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441222499565, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441222499654, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222499728, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222499900, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441222500004, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222500114, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222500275, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Cinemachine.ref.dll_F5DA8036D01E35DC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222500477, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222500566, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222500644, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222500826, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441222500898, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222500997, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222501071, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222501180, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Splines.Editor.ref.dll_A1961DBF72D8620D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222501245, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441222501981, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441222502108, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222502224, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222502443, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441222502552, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222502695, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222502882, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441222502997, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222503106, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222503452, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222503579, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222503781, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441222503894, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222504012, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222504231, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441222504332, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222504444, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222504646, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441222504758, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222504869, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222505046, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441222505180, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222505280, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222505481, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441222505594, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222505738, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222505855, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_553CFFCCE7430329.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222506044, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222506163, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222506287, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Multiplayer.Center.Editor.ref.dll_FC2D3B4057A989C4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222506416, "dur": 116, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222506547, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222506665, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222506768, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222506958, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Performance.Profile-Analyzer.Editor.ref.dll_8B44F821019897E5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222507066, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441222507145, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441222507246, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222507348, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222507411, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222507474, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222507605, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rider.Editor.ref.dll_DA88E6A3E9EB6155.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222507666, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441222507734, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441222507794, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222507860, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222507920, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222507988, "dur": 148, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222508218, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TestTools.CodeCoverage.Editor.ref.dll_C6D619E6129B87D3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222508319, "dur": 111, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441222508443, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441222508506, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222508580, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222508654, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222508728, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222508875, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_D0BA3615D322EF6C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222508942, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441222509006, "dur": 107, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441222509125, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222509222, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222509293, "dur": 101, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222509407, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222509585, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.SettingsProvider.Editor.ref.dll_EE1755C0672F8EE2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222509654, "dur": 108, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441222509775, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441222509874, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222509947, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222510048, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222510122, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222510294, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Shared.Editor.ref.dll_6CA31C4FA5286AB1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222510364, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441222510469, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441222510580, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222510641, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222510727, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222510823, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222510964, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualStudio.Editor.ref.dll_0FE027DDD6F41767.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222511034, "dur": 101, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441222511147, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441222511215, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222511313, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222511375, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222511491, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222511658, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441222511723, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222511788, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441222511886, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.TestRunner.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222511992, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441222512093, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222512231, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222512289, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441222512366, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222512512, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441222512584, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.EditorCoroutines.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222512674, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222512747, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441222512837, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441222512911, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222513006, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Performance.Profile-Analyzer.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222513130, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222513223, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Postprocessing.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222513294, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Settings.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441222513458, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222513552, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222513624, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441222513714, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441222513785, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222513878, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441222513959, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441222514086, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222514160, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222514298, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441222514382, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441222514451, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441222514545, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222514618, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222514752, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222514933, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441222515007, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441222515108, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222515181, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222515335, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222515456, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441222515528, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222515619, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Postprocessing.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222515695, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Splines.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441222515785, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441222515859, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TestTools.CodeCoverage.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222515956, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222516025, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441222516148, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222516230, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Cinemachine.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441222516310, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Cinemachine.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222516377, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Splines.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441222516467, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441222516536, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222516627, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222516815, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222516887, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441222516984, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441222517054, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222517144, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222517216, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441222517465, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441222517582, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222517676, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222517819, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor-firstpass.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441222517889, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441222517991, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor-firstpass.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222518229, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor-firstpass.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751441222518335, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751441222518416, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441222518508, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222518580, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751441222518753, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751441222518850, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751441222518918, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751441222518982, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751441222475200, "dur": 44020, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441222519237, "dur": 903470, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441223422711, "dur": 841, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441223423552, "dur": 85, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441223423644, "dur": 89, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441223423748, "dur": 52, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441223423874, "dur": 143, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441223424345, "dur": 76, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441223424471, "dur": 198, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441223424708, "dur": 18123, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751441222474020, "dur": 45231, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222519269, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_52F12B5AEAC5FC8F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441222519487, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222519570, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_16A54265357DDF1F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441222519684, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222519756, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_B52CD1DFCA664AB3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441222519858, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222520052, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_4E756196A062909D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441222520154, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222520238, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_00B5CEDD1ED5F4B3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441222520364, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222520464, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_783D7153E7AEE314.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441222520659, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_963BCDCD478EB63D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441222520894, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222520996, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_963BCDCD478EB63D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441222521114, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_9F68AD12AC89760A.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441222521229, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222521365, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_69199FEE963F7F27.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751441222521464, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222521754, "dur": 545, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751441222522397, "dur": 957, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751441222523359, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222523605, "dur": 405, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751441222524012, "dur": 1566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222525578, "dur": 863, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222526442, "dur": 1440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222527883, "dur": 998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222529494, "dur": 890, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Actions\\MarkerActions.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751441222528882, "dur": 1813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222530696, "dur": 1212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222531909, "dur": 1120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222533030, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222534192, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222535370, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222536682, "dur": 523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222537206, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222537501, "dur": 1458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222538960, "dur": 1009, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222539970, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222540187, "dur": 1440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222541634, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222541693, "dur": 368, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222542061, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222542423, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222542542, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222542612, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222542778, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222542973, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222543148, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222543302, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222543444, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222543557, "dur": 756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222544314, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222544621, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222544747, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222544906, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222544964, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222545030, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222545167, "dur": 614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222545782, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222546193, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222546656, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222547082, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222547272, "dur": 1264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222548537, "dur": 636, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222549174, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222549430, "dur": 784, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222550214, "dur": 554, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222550769, "dur": 1068, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222551838, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222551977, "dur": 914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222552892, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222553063, "dur": 1055, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441222554119, "dur": 636281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751441223190401, "dur": 232303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222474785, "dur": 44790, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222519581, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FCC5A8FB58ECD1D2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751441222519729, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222519809, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_FCC5A8FB58ECD1D2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751441222519917, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_6C2CA7D176733A22.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751441222520193, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222520279, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_921E3B2D436CDAE7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751441222520409, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222520518, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_B8F1FBD1DD727BB2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751441222520632, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222520861, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_67F30B299A5D5154.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751441222520982, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222521073, "dur": 809, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_67F30B299A5D5154.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751441222521970, "dur": 295, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751441222522299, "dur": 465, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751441222522813, "dur": 1079, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1751441222524001, "dur": 559, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Editor\\TMP\\TMP_SerializedPropertyHolder.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751441222523894, "dur": 1505, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222525400, "dur": 1087, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222526489, "dur": 1399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222527889, "dur": 1212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222529424, "dur": 912, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.postprocessing\\PostProcessing\\Editor\\PostProcessLayerEditor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751441222529101, "dur": 2375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222531477, "dur": 1190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222532668, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222533577, "dur": 1222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222534799, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222535996, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222536849, "dur": 378, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222537228, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222537519, "dur": 1489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222539008, "dur": 996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222540004, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222540137, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751441222540652, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222540721, "dur": 1629, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751441222542352, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222542551, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222542723, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222542976, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222543144, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222543264, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222543447, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222543600, "dur": 722, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222544323, "dur": 286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222544610, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222544717, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222544892, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222544950, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222545035, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222545091, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222545172, "dur": 616, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222545789, "dur": 849, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222546639, "dur": 424, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222547063, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222547278, "dur": 1277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222548556, "dur": 624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222549180, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222549436, "dur": 755, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222550192, "dur": 579, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222550772, "dur": 1060, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222551833, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222551947, "dur": 959, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222552906, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222553053, "dur": 1093, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441222554147, "dur": 636232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751441223190382, "dur": 232293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222474161, "dur": 45148, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222519314, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_10CDAFD975BC0AB7.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751441222519530, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222519614, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_B01BEAEDCAC9F962.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751441222519823, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_7F1C5DFD0AD39F77.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751441222520046, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222520204, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_7BFAB029D4D7DE5C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751441222520381, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222520529, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_9B1D775C88DF2080.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751441222520887, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222521029, "dur": 263, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_9B1D775C88DF2080.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751441222521363, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_9428A5C8BBBE8A51.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751441222521479, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222521699, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751441222521785, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751441222522171, "dur": 507, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751441222522776, "dur": 729, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751441222523645, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222524695, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222525599, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222526698, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222527860, "dur": 1181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222529498, "dur": 895, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.splines\\Editor\\GUI\\Editors\\EmbeddedSplineDataPropertyDrawer.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751441222529042, "dur": 2131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222531175, "dur": 1705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222532882, "dur": 1150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222534033, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222535308, "dur": 791, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222536100, "dur": 937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222537037, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222537215, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222537490, "dur": 1472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222538962, "dur": 996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222540015, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222540184, "dur": 851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751441222541037, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222541132, "dur": 1915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751441222543048, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222543437, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222543511, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222543575, "dur": 756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222544331, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222544656, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222544727, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222545100, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222545169, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222545803, "dur": 848, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222546651, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222547046, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222547253, "dur": 1281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222548535, "dur": 625, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222549211, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222549438, "dur": 735, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222550180, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222550236, "dur": 567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222550804, "dur": 1030, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222551834, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222551959, "dur": 934, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222552894, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222553017, "dur": 1087, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222554104, "dur": 787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441222554891, "dur": 635541, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751441223190433, "dur": 232332, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222474268, "dur": 45061, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222519337, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_B014F0E06EB9C8B3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441222519560, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222519679, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_F4165E534DD27516.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441222519886, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222520046, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_E009CCFB4A5E9FFB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441222520215, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222520386, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_8B525A02B9DF9C7E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441222520500, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222520601, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_FD8C0D7AD7503969.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441222520882, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222521038, "dur": 331, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_FD8C0D7AD7503969.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441222521377, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_91592B4E62CF2B71.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441222521486, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222521571, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_02726B6CC8A7DF56.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751441222521695, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222522113, "dur": 438, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751441222522558, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751441222522743, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222522875, "dur": 396, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751441222523276, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751441222523540, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222523596, "dur": 448, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751441222524045, "dur": 1315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222525361, "dur": 995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222526357, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222527457, "dur": 1440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222529491, "dur": 878, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.splines\\Editor\\GUI\\SplineMenu.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751441222528899, "dur": 2172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222531072, "dur": 1375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222532448, "dur": 1329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222533778, "dur": 1669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222535447, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222536726, "dur": 496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222537223, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222537509, "dur": 1476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222538986, "dur": 1012, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222539998, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222540162, "dur": 1491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222541654, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222542054, "dur": 359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222542427, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222542492, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222542543, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222542612, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222542684, "dur": 350, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222543042, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222543153, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222543249, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222543437, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222543564, "dur": 762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222544326, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222544636, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222544731, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222544905, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222544960, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222545068, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222545183, "dur": 611, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222545794, "dur": 872, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222546666, "dur": 379, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222547045, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222547241, "dur": 1289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222548531, "dur": 662, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222549193, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222549458, "dur": 747, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222550205, "dur": 560, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222550766, "dur": 1054, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222551820, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222551936, "dur": 948, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222552884, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222553022, "dur": 1108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441222554131, "dur": 636275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751441223190408, "dur": 232291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222474389, "dur": 44990, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222519387, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_438C72DDFEF65815.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751441222519598, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222519753, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_592CC89A6471077C.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751441222520062, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222520274, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_42AEDBADC03A3022.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751441222520495, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222520652, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_B563F2CB5E9D696E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751441222520845, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222520956, "dur": 214, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_B563F2CB5E9D696E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751441222521177, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_800370C755C131C9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751441222521264, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222521317, "dur": 285, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_800370C755C131C9.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751441222521612, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751441222521695, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222521748, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751441222521819, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222521884, "dur": 329, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751441222522216, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751441222522270, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222522322, "dur": 1078, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751441222523537, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751441222523591, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751441222523704, "dur": 976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222524681, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222525561, "dur": 1201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222526764, "dur": 1254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222528018, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222529483, "dur": 828, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Deprecated\\CinemachinePath.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751441222529179, "dur": 1864, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222531044, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222532261, "dur": 1239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222533502, "dur": 1507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222535010, "dur": 629, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222535640, "dur": 2026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222537667, "dur": 1323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222538990, "dur": 1000, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222539992, "dur": 184, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222540176, "dur": 1463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222541641, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222542060, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222542541, "dur": 833, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222543383, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222543450, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222543559, "dur": 757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222544316, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222544617, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222544735, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222544903, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222544972, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222545165, "dur": 635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222545801, "dur": 831, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222546682, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222547051, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222547247, "dur": 1296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222548544, "dur": 668, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222549213, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222549428, "dur": 760, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222550189, "dur": 565, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222550754, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222550848, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751441222551081, "dur": 623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751441222551818, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222551973, "dur": 927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222552901, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222553028, "dur": 1105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441222554134, "dur": 636269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751441223190404, "dur": 232321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222474435, "dur": 44959, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222519400, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_7A1FD51B51344812.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751441222519530, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222519601, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_989B89A6DC960046.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751441222519749, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222519835, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_2BCC56A3C6071870.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751441222520033, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222520119, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F8AA39E8D5CE00CF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751441222520250, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222520317, "dur": 357, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_F8AA39E8D5CE00CF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751441222520689, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A99F700E950BD818.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751441222520950, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222521021, "dur": 395, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_A99F700E950BD818.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751441222521443, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_5CAA8383FA1D1EAC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751441222521587, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222521747, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751441222521991, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222522124, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222522231, "dur": 219, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1751441222522559, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751441222522641, "dur": 575, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751441222523218, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751441222523362, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222523418, "dur": 500, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751441222523920, "dur": 1429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222525350, "dur": 1191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222526542, "dur": 1047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222527590, "dur": 1214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222529489, "dur": 831, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\inspectors\\IInspectorChangeHandler.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751441222528805, "dur": 1720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222530526, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222531534, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222532729, "dur": 1090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222533821, "dur": 1125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222534947, "dur": 1187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222536135, "dur": 813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222536949, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222537220, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222537514, "dur": 1439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222539015, "dur": 973, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222539989, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222540160, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751441222540672, "dur": 1555, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751441222542228, "dur": 407, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222542684, "dur": 930, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222543622, "dur": 699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222544321, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222544616, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222544715, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222544904, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222544956, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222545064, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222545156, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751441222545417, "dur": 679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751441222546097, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222546254, "dur": 407, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222546662, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222547052, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222547266, "dur": 1266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222548533, "dur": 661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222549195, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222549466, "dur": 737, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222550204, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222550780, "dur": 1028, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222551863, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222551953, "dur": 936, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222552889, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222553024, "dur": 1090, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441222554115, "dur": 636276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751441223190392, "dur": 232302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222474505, "dur": 44945, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222519459, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_00CD99E311DC019E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751441222519665, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222519850, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_47A61C91F1D08784.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751441222520100, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222520258, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_39C615B7DD834A4A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751441222520427, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222520511, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3C810AF224C3A909.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751441222520864, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222521026, "dur": 634, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_3C810AF224C3A909.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751441222521725, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222521793, "dur": 1511, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751441222523308, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751441222523482, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222523575, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751441222523725, "dur": 1488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222525214, "dur": 1296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222526511, "dur": 1270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222527783, "dur": 1713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222529498, "dur": 912, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Core\\CameraState.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751441222529498, "dur": 2221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222531720, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222532674, "dur": 1208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222533883, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222535176, "dur": 1155, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222536332, "dur": 52, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222536384, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222536554, "dur": 656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222537210, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222537506, "dur": 1450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222539022, "dur": 949, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222539971, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222540167, "dur": 1475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222541642, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222542116, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222542427, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222542545, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222542607, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222542684, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222542742, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222542986, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222543159, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222543257, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222543448, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222543555, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222543688, "dur": 927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751441222544617, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222544896, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222544975, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222545058, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222545164, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751441222545367, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222545586, "dur": 943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751441222546530, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222546641, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222546713, "dur": 326, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222547039, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222547273, "dur": 1255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222548529, "dur": 655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222549185, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222549442, "dur": 733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222550176, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222550801, "dur": 1051, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222551853, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222551985, "dur": 917, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222552903, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222553047, "dur": 1084, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441222554132, "dur": 636281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751441223190414, "dur": 232325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222474560, "dur": 44942, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222519510, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_BF1A247579C7383B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441222519709, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222519877, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_46E217D19A6E01BD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441222520262, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222520357, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_ED94C9E717A89F9F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441222520480, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222520578, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_71CC72797B2E953C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441222520872, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222521015, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_71CC72797B2E953C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441222521138, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_E671E5897665917D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441222521277, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222521387, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_54F233D7B0D751B2.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441222521460, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222521630, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751441222521812, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222521870, "dur": 294, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751441222522167, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751441222522349, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222522553, "dur": 380, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751441222522939, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222523088, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222523180, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751441222523467, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222523603, "dur": 454, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1751441222524059, "dur": 1578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222525638, "dur": 960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222526599, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222529458, "dur": 927, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Inspection\\MetadataCollectionAdaptor.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751441222527726, "dur": 2857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222530584, "dur": 1278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222531863, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222532734, "dur": 1361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222534096, "dur": 1264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222535361, "dur": 1346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222536708, "dur": 521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222537229, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222537497, "dur": 1482, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222538980, "dur": 1019, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222539999, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222540139, "dur": 1652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441222541794, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222541942, "dur": 2176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751441222544119, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222544318, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222544391, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441222544724, "dur": 795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751441222545520, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222545671, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222545787, "dur": 853, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222546641, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222547048, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222547240, "dur": 1286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222548527, "dur": 644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222549172, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222549427, "dur": 763, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222550190, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222550797, "dur": 1017, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222551815, "dur": 128, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222551944, "dur": 936, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222552881, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222553020, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751441222553318, "dur": 798, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441222554117, "dur": 636245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751441223190413, "dur": 359, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751441223190364, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751441223190829, "dur": 2022, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751441223192855, "dur": 229832, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222474627, "dur": 44912, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222519545, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_80B8935D38233098.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441222519713, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222519815, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_DC7672C0573B7C85.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441222520020, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222520141, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_0660C742CA2CCB37.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441222520322, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222520456, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6678507EC9682BC1.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441222520666, "dur": 291, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222520959, "dur": 233, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_6678507EC9682BC1.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441222521202, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_D7EC0EF22CAF3612.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441222521325, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222521476, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_3AA3364A40DB9F8B.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751441222521614, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222521774, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222521902, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751441222522100, "dur": 604, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751441222522766, "dur": 104, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1751441222523093, "dur": 688, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1751441222523785, "dur": 1336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222525122, "dur": 930, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222526052, "dur": 1059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222527113, "dur": 1229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222529468, "dur": 964, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\treeview\\TimelineTreeViewGUI.cs"}}, {"pid": 12345, "tid": 9, "ts": 1751441222528343, "dur": 2090, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222530434, "dur": 1316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222531751, "dur": 1335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222533087, "dur": 1148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222534236, "dur": 1261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222535499, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222536567, "dur": 641, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222537208, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222537499, "dur": 1499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222538999, "dur": 1017, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222540017, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222540164, "dur": 1472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222541637, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222542052, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222542422, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222542556, "dur": 65, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222542621, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222542725, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222542979, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222543143, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222543253, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222543438, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222543562, "dur": 757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222544320, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222544663, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222544732, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222544897, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222544949, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222545177, "dur": 608, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222545785, "dur": 882, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222546668, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222547054, "dur": 194, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222547249, "dur": 1289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222548539, "dur": 618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222549220, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222549464, "dur": 751, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222550216, "dur": 545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222550762, "dur": 1049, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222551870, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222551942, "dur": 927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222552931, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222553014, "dur": 1099, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441222554113, "dur": 636283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751441223190397, "dur": 232324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222474083, "dur": 45211, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222519300, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_BED6F64D074B3174.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441222519555, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222519653, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_5543A9502BBA10BA.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441222519792, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222520024, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_6FBB1366163B00FB.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441222520141, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222520493, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_95C124FA2E4BBBB0.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441222520649, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222520963, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_95C124FA2E4BBBB0.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441222521120, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_27EF5DC3D06E8F78.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441222521212, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222521264, "dur": 522, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_27EF5DC3D06E8F78.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441222521799, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222521849, "dur": 338, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1751441222522369, "dur": 588, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1751441222523061, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222523172, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751441222523504, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222523607, "dur": 1509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222525118, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222526385, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222527458, "dur": 1427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222529484, "dur": 860, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.splines\\Editor\\Utilities\\PathIcons.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751441222528886, "dur": 2114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222531001, "dur": 784, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222531786, "dur": 1285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222533072, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222534107, "dur": 1007, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222535115, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222535906, "dur": 988, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222536894, "dur": 324, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222537218, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222537547, "dur": 1436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222538983, "dur": 980, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222539963, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222540179, "dur": 1452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222541632, "dur": 430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222542062, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222542433, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222542551, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222542728, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222542985, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222543140, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222543250, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222543445, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222543560, "dur": 764, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222544324, "dur": 298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222544623, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222544722, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222544954, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222545068, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222545195, "dur": 602, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222545798, "dur": 851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222546649, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222547074, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222547258, "dur": 1301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222548559, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222549189, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222549444, "dur": 748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222550193, "dur": 587, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222550781, "dur": 1063, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222551846, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222551960, "dur": 927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222552888, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222553010, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751441222553288, "dur": 838, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441222554126, "dur": 636316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751441223190443, "dur": 232254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222474922, "dur": 44670, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222519599, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_493844D967613FA0.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441222519816, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222519906, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441222520118, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222520198, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_1D5E7DE71C1354F3.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441222520327, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_5AC43434136967AD.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441222520431, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222520535, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_B5DB3F27CCAA63FE.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441222520647, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222520869, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E0B83A434CA2B946.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441222521039, "dur": 1244, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_E0B83A434CA2B946.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441222522296, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441222522484, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222522556, "dur": 14792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751441222537350, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222537496, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222537587, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441222537793, "dur": 1054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751441222538848, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222538979, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222539049, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441222539256, "dur": 597, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751441222539968, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222540100, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441222540502, "dur": 1295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222541807, "dur": 3003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751441222544812, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222545029, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222545143, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441222545376, "dur": 1730, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751441222547107, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222547258, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222547332, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441222547543, "dur": 1505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751441222549049, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222549166, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222549237, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751441222549453, "dur": 616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751441222550070, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222550184, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222550258, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222550783, "dur": 1059, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222551843, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222551954, "dur": 913, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222552917, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222553038, "dur": 1085, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441222554124, "dur": 636304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751441223190430, "dur": 232323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222475031, "dur": 44584, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222519620, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_44286934693FF148.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441222519773, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222519854, "dur": 399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_C2E93A20F98E8229.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441222520254, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222520350, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_7D7B641D38DA242D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441222520503, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222520606, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_DC79D8DB49767D4F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441222520903, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222521033, "dur": 438, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_DC79D8DB49767D4F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441222521495, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751441222521631, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222521770, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222521865, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222521984, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222522177, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751441222522349, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751441222522568, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751441222522697, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222522831, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751441222522906, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222522957, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751441222523164, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751441222523364, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222523439, "dur": 159, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor-firstpass.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1751441222523600, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751441222523699, "dur": 972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222524672, "dur": 1709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222526383, "dur": 1327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222527710, "dur": 1003, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222529432, "dur": 958, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Manipulators\\Move\\MoveItemModeReplace.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751441222528714, "dur": 2412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222531127, "dur": 1059, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222532187, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222533288, "dur": 1198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222534487, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222535616, "dur": 1859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222537536, "dur": 1468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222539005, "dur": 970, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222539976, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222540102, "dur": 444, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441222540547, "dur": 1282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222541838, "dur": 1462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751441222543301, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222543566, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222543680, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441222543941, "dur": 952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751441222544897, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222545062, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222545165, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441222545546, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222545889, "dur": 1261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751441222547151, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222547245, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222547310, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751441222547549, "dur": 876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751441222548532, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222548605, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222549170, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222549426, "dur": 774, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222550200, "dur": 584, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222550784, "dur": 1043, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222551828, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222551933, "dur": 938, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222552872, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222553009, "dur": 1097, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222554107, "dur": 81361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222637987, "dur": 448, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 12, "ts": 1751441222635471, "dur": 2968, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441222638440, "dur": 551999, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751441223190440, "dur": 232293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222475097, "dur": 44534, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222519636, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_1C55A2A4A7EC33E2.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751441222519787, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222519985, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_74513773BD43745E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751441222520092, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222520172, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_5E52B5CE937E0ED1.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751441222520293, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222520405, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_00433992BAF13A73.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751441222520507, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222520594, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_20E0F9318BFD3B5B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751441222520951, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222521075, "dur": 693, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_20E0F9318BFD3B5B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751441222521839, "dur": 824, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751441222522665, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751441222522810, "dur": 207, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1751441222523188, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751441222523412, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751441222523586, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1751441222523658, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222524783, "dur": 1204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222525988, "dur": 1119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222527108, "dur": 1134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222528243, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222529483, "dur": 813, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Timeline\\CinemachineTrack.cs"}}, {"pid": 12345, "tid": 13, "ts": 1751441222529129, "dur": 1876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222531006, "dur": 923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222531930, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222533056, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222534141, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222535182, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222536403, "dur": 83, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222536487, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222536801, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222537203, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222537494, "dur": 1498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222538993, "dur": 1007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222540001, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222540193, "dur": 1433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222541678, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222542068, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222542431, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222542539, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222542609, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222542685, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222542738, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222542990, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222543262, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222543451, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222543556, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222543629, "dur": 700, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222544329, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222544626, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222544733, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222544891, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222544951, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222545066, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222545150, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1751441222545408, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1751441222546096, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222546188, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222546665, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222547055, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222547246, "dur": 1278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222548525, "dur": 661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222549187, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222549440, "dur": 754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222550195, "dur": 600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222550795, "dur": 1045, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222551841, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222551937, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222552004, "dur": 888, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222552892, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222553019, "dur": 1100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441222554120, "dur": 636267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1751441223190388, "dur": 232288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222475238, "dur": 44418, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222519664, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_9DC68FB86218AF77.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441222519894, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222520041, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_F24F3CD4D4D128AB.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441222520233, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222520373, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_6E64691D4ACC974A.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441222520537, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222520672, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C5419324AD6BC088.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441222520837, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222521009, "dur": 434, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_C5419324AD6BC088.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441222521456, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441222521606, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222521744, "dur": 1392, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_56315C1AB8A5DFB9.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441222523161, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441222523657, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222523741, "dur": 519, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441222524263, "dur": 12817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751441222537082, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222537222, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222537324, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222537511, "dur": 1460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222538972, "dur": 993, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222539965, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222540118, "dur": 1457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441222541577, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222541660, "dur": 1322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751441222542983, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222543148, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222543276, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441222543683, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1751441222543962, "dur": 852, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1751441222544815, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222544952, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222545061, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222545166, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222545799, "dur": 855, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222546655, "dur": 381, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222547036, "dur": 186, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222547295, "dur": 1222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222548586, "dur": 604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222549190, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222549455, "dur": 754, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222550209, "dur": 546, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222550756, "dur": 1073, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222551830, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222551945, "dur": 931, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222552877, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222553012, "dur": 1083, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441222554143, "dur": 636308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1751441223190452, "dur": 232266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222475275, "dur": 44395, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222519676, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8ECA4E4329F1BADB.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441222519847, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222520001, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_8ECA4E4329F1BADB.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441222520100, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BA5431EB9EAB1D55.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441222520215, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222520301, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_976F9C96B5CDAC08.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441222520413, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222520542, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E522F23A96C2507B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441222520650, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222520866, "dur": 199, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E522F23A96C2507B.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441222521076, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D7BFC8363EAC4767.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441222521170, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222521223, "dur": 697, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_D7BFC8363EAC4767.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441222521929, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751441222522150, "dur": 593, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751441222522762, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222522916, "dur": 555, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 15, "ts": 1751441222523494, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222523574, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1751441222523642, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222524735, "dur": 1427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222526163, "dur": 1266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222527430, "dur": 1152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222529469, "dur": 952, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\State\\SequencePath.cs"}}, {"pid": 12345, "tid": 15, "ts": 1751441222528583, "dur": 1929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222530513, "dur": 1191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222531704, "dur": 964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222532669, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222533830, "dur": 1221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222535053, "dur": 1002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222536057, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222537107, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222537213, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222537524, "dur": 1442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222538966, "dur": 1007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222539973, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222540120, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441222540441, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222540710, "dur": 2402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751441222543114, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222543256, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222543378, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441222543808, "dur": 1848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751441222545657, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222545791, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222546202, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222546653, "dur": 376, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222547030, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222547237, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222547340, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441222547581, "dur": 1719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751441222549301, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222549426, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222549490, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441222549710, "dur": 932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751441222550643, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222550760, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222550840, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441222551057, "dur": 728, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751441222551786, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222552029, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441222552228, "dur": 547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751441222552877, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222553028, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1751441222553435, "dur": 556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751441222553992, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222554092, "dur": 760, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751441222555557, "dur": 137, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1751441222556830, "dur": 620930, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751441223191085, "dur": 215, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 15, "ts": 1751441223190357, "dur": 1088, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1751441223191569, "dur": 231187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222475407, "dur": 44280, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222519693, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_921345D5C92C6DDE.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441222519840, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222519978, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_61E5A1A2114B6332.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441222520208, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222520289, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_CB3FD07F3B098D2B.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441222520392, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222520476, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_97D91564DB667E63.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441222520632, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222520891, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_14A21DFAD1DFC142.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441222521000, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222521089, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B56095C3DA730DAC.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441222521178, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222521232, "dur": 339, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_B56095C3DA730DAC.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441222521579, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9C39811BC6DD0BF6.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441222521675, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222521746, "dur": 237, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_9C39811BC6DD0BF6.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1751441222521999, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222522084, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1751441222522136, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Splines.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751441222522368, "dur": 461, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1751441222522863, "dur": 260, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1751441222523125, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751441222523274, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751441222523577, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1751441222523693, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222524688, "dur": 1332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222526021, "dur": 1534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222527556, "dur": 1952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222529509, "dur": 879, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Behaviours\\CinemachineSplineCart.cs"}}, {"pid": 12345, "tid": 16, "ts": 1751441222529509, "dur": 2323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222531833, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222533072, "dur": 1384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222534458, "dur": 1392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222535852, "dur": 2019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222537872, "dur": 1116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222538988, "dur": 990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222539980, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222540182, "dur": 1452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222541635, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222542056, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222542418, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222542541, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222542626, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222542727, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222543033, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222543170, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222543263, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222543465, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222543590, "dur": 713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222544309, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222544412, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222544613, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222544721, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222545077, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222545207, "dur": 585, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222545793, "dur": 870, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222546663, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222547072, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222547256, "dur": 1286, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222548542, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222549176, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222549460, "dur": 726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222550187, "dur": 588, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222550776, "dur": 1073, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222551849, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222551968, "dur": 914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222552883, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222553059, "dur": 1070, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441222554129, "dur": 636236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441223190366, "dur": 1160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1751441223191574, "dur": 231142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222475556, "dur": 44156, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222519720, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_030C0AE8F7F2C9CD.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751441222520180, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222520321, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_889FFFE959BEFFC2.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751441222520517, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222520882, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_BDFB8953D401853F.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751441222521001, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222521076, "dur": 200, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_BDFB8953D401853F.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751441222521284, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E8D1A39870CABAC1.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751441222521368, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222521441, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_E8D1A39870CABAC1.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751441222521564, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_39F0A7015032F01B.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751441222521773, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222521933, "dur": 295, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_39F0A7015032F01B.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751441222522234, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751441222522359, "dur": 225, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1751441222522588, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751441222522715, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222522800, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222522865, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1751441222522953, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222523033, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1751441222523400, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222523491, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222523572, "dur": 377, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1751441222523952, "dur": 1802, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222525756, "dur": 1466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222527224, "dur": 1440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222529480, "dur": 933, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Signals\\SignalEmitterInspector.cs"}}, {"pid": 12345, "tid": 17, "ts": 1751441222528665, "dur": 2290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222530956, "dur": 1507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222532464, "dur": 1226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222533691, "dur": 1198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222534892, "dur": 1305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222536198, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222536681, "dur": 530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222537212, "dur": 304, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222537517, "dur": 1456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222538975, "dur": 1010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222539985, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222540104, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751441222540495, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222540592, "dur": 909, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751441222541502, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222541634, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222541727, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1751441222542096, "dur": 1247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1751441222543344, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222543444, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222543572, "dur": 768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222544341, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222544624, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222544737, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222544895, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222544965, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222545028, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222545089, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222545196, "dur": 608, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222545804, "dur": 852, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222546657, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222547050, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222547262, "dur": 1287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222548550, "dur": 641, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222549192, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222549457, "dur": 740, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222550197, "dur": 566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222550763, "dur": 1084, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222551848, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222551965, "dur": 914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222552880, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222553011, "dur": 1110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441222554122, "dur": 636322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1751441223190445, "dur": 232317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222475629, "dur": 44110, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222519747, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_A8A2BEB223D0393B.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751441222520064, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222520216, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_1830F8D8229EF0EB.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751441222520411, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222520556, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_805FFE03DDD45823.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751441222520889, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222520997, "dur": 352, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_805FFE03DDD45823.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751441222521477, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222521543, "dur": 337, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_2CD7161C662F4679.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751441222521994, "dur": 330, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1751441222522327, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222522381, "dur": 610, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 18, "ts": 1751441222523047, "dur": 335, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751441222523482, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222523573, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1751441222523689, "dur": 1198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222524889, "dur": 1329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222526219, "dur": 1215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222527435, "dur": 1707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222529506, "dur": 864, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Impulse\\CinemachineImpulseListener.cs"}}, {"pid": 12345, "tid": 18, "ts": 1751441222529143, "dur": 2176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222531320, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222532405, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222533599, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222534726, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222535780, "dur": 2078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222537858, "dur": 1106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222538964, "dur": 1030, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222539996, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222540139, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1751441222540668, "dur": 1496, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1751441222542165, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222542424, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222542555, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222542619, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222542673, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222542726, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222542978, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222543054, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222543149, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222543251, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222543440, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222543566, "dur": 751, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222544318, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222544612, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222544725, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222544902, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222544953, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222545065, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222545156, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222545623, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222545791, "dur": 843, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222546690, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222547043, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222547239, "dur": 1312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222548552, "dur": 644, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222549196, "dur": 272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222549468, "dur": 734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222550202, "dur": 558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222550760, "dur": 1062, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222551822, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222551937, "dur": 958, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222552895, "dur": 130, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222553026, "dur": 1098, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441222554125, "dur": 636242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1751441223190369, "dur": 232333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222475759, "dur": 43999, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222519765, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_D447C5E6C0383BF9.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751441222520146, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222520241, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_B1475218F12198D8.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751441222520383, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222520471, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C099976D06C71E3D.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751441222520577, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222520650, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_C099976D06C71E3D.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751441222520714, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_54605A4C765A4A77.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751441222521038, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222521188, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3EA5AD5E9A933796.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751441222521306, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222521390, "dur": 629, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_3EA5AD5E9A933796.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1751441222522070, "dur": 364, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1751441222522605, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751441222522735, "dur": 334, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1751441222523163, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751441222523315, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751441222523525, "dur": 347, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-firstpass.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1751441222524191, "dur": 528, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Shared\\EmptyGraphWindow.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751441222523874, "dur": 1659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222525534, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222526447, "dur": 1296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222527745, "dur": 1127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222529460, "dur": 927, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Attributes\\ActiveInModeAttribute.cs"}}, {"pid": 12345, "tid": 19, "ts": 1751441222528873, "dur": 2342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222531216, "dur": 1142, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222532358, "dur": 966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222533325, "dur": 1427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222534753, "dur": 970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222535724, "dur": 1916, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222537641, "dur": 1342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222538984, "dur": 1026, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222540011, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222540173, "dur": 1457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222541630, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222542050, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222542420, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222542546, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222542617, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222542728, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222542792, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222543003, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222543155, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222543267, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222543433, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222543502, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222543585, "dur": 748, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222544333, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222544625, "dur": 85, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222544763, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222544908, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222544979, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222545062, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222545181, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222545836, "dur": 831, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222546667, "dur": 365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222547033, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222547243, "dur": 1280, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222548523, "dur": 654, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222549177, "dur": 245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222549422, "dur": 760, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222550182, "dur": 575, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222550758, "dur": 1060, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222551818, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222551939, "dur": 964, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222552904, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222553032, "dur": 1088, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441222554121, "dur": 636235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1751441223190475, "dur": 230374, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1300b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 19, "ts": 1751441223190402, "dur": 230451, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 19, "ts": 1751441223421024, "dur": 1557, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 20, "ts": 1751441222475814, "dur": 43971, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222519793, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_970778ACBFA20360.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441222520046, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222520127, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_051DE14E9379244A.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441222520277, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222520379, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_D22D11490AEAA9B0.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441222520481, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222520571, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F275A7F2C1FA929A.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441222520875, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222520987, "dur": 167, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_F275A7F2C1FA929A.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441222521167, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B47D5ED0AB2E73A1.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441222521304, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222521388, "dur": 124, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_B47D5ED0AB2E73A1.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441222521591, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441222521689, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222521756, "dur": 750, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_FA700FE84193BC68.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441222522567, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222522654, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751441222522806, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222522857, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751441222523015, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222523085, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751441222523207, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751441222523477, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222523549, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Performance.Profile-Analyzer.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1751441222523688, "dur": 1330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222525019, "dur": 835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222525854, "dur": 1212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222527068, "dur": 1761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222529504, "dur": 922, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\inspectors\\AnimationPlayableAssetInspector.cs"}}, {"pid": 12345, "tid": 20, "ts": 1751441222528831, "dur": 2260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222531092, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222532168, "dur": 1150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222533319, "dur": 1273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222534593, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222535551, "dur": 2299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222537851, "dur": 1118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222538970, "dur": 1012, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222539983, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222540131, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441222540619, "dur": 1249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751441222541869, "dur": 701, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222542618, "dur": 461, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222543092, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1751441222543520, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222543592, "dur": 919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1751441222544512, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222544720, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222544792, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222544898, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222544976, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222545073, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222545182, "dur": 597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222545850, "dur": 797, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222546648, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222547032, "dur": 192, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222547237, "dur": 1303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222548541, "dur": 642, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222549183, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222549449, "dur": 749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222550199, "dur": 568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222550767, "dur": 1068, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222551836, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222551957, "dur": 928, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222552885, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222553030, "dur": 1079, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222554110, "dur": 84338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441222638450, "dur": 551933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1751441223190385, "dur": 232293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222475888, "dur": 43915, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222519808, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_D3BB5BABC9B6A39A.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751441222520072, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222520223, "dur": 613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_99CD71E244B915CF.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751441222520837, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222520941, "dur": 742, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_99CD71E244B915CF.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751441222521875, "dur": 162, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 21, "ts": 1751441222522075, "dur": 561, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1751441222522639, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751441222523015, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751441222523128, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751441222523511, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222523589, "dur": 379, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1751441222523970, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222525187, "dur": 1163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222526350, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222527512, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222529492, "dur": 877, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\Manipulators\\Sequence\\TrackZoom.cs"}}, {"pid": 12345, "tid": 21, "ts": 1751441222528695, "dur": 1675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222530371, "dur": 1029, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222531401, "dur": 1561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222532963, "dur": 1526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222534489, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222535538, "dur": 1542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222537081, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222537226, "dur": 299, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222537526, "dur": 1450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222538977, "dur": 1010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222539987, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222540189, "dur": 1455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222541645, "dur": 400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222542054, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222542167, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222542424, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222542542, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222542676, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222542735, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222542975, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222543151, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222543308, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222543495, "dur": 98, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222543593, "dur": 718, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222544312, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222544615, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222544776, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222545071, "dur": 71, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222545149, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1751441222545545, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222545831, "dur": 1070, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1751441222546902, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222547032, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222547096, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222547285, "dur": 1261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222548547, "dur": 634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222549182, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222549506, "dur": 678, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222550185, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222550808, "dur": 1008, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222551817, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222551941, "dur": 957, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222552899, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222553020, "dur": 1071, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222554094, "dur": 681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1751441222554777, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441222554901, "dur": 635522, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1751441223190424, "dur": 232266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222475979, "dur": 43836, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222519820, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_129B2BEEEC662028.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751441222520078, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222520188, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_6C4C0BE13C75841D.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751441222520395, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_A97613F6BA90A6DC.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751441222520592, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222520681, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0D29C9963CEF79A6.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751441222520924, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222521002, "dur": 251, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_0D29C9963CEF79A6.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751441222521270, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_09E134ACBD96AA82.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751441222521448, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222521647, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222521704, "dur": 249, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1751441222522001, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222522093, "dur": 526, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1751441222522622, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751441222522751, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222522814, "dur": 419, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751441222523235, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1751441222523458, "dur": 532, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1751441222523992, "dur": 1548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222525541, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222526438, "dur": 842, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222527281, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222528440, "dur": 980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222529501, "dur": 849, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Core\\LensSettings.cs"}}, {"pid": 12345, "tid": 22, "ts": 1751441222529421, "dur": 2049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222531472, "dur": 1203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222532676, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222533732, "dur": 1388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222535121, "dur": 1240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222536362, "dur": 121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222536484, "dur": 71, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222536556, "dur": 660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222537216, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222537492, "dur": 1476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222538969, "dur": 991, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222540027, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222540128, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751441222540433, "dur": 1400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751441222541834, "dur": 1098, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222542981, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222543061, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751441222543442, "dur": 949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1751441222544392, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222544625, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222544741, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222544910, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222544968, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222545079, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222545205, "dur": 590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222545796, "dur": 863, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222546660, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222547038, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222547242, "dur": 1278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222548579, "dur": 618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222549200, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222549473, "dur": 704, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222550178, "dur": 595, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222550773, "dur": 1053, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222551826, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222551934, "dur": 940, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222552875, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222553003, "dur": 402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1751441222553442, "dur": 658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222554111, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441222554188, "dur": 636188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1751441223190377, "dur": 232306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222476055, "dur": 43772, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222519832, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_711E053178EA7BF1.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441222520025, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222520109, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_DCB00145011AACBC.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441222520246, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222520344, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_173731A5F5D7E041.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441222520454, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222520586, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_31922632D84128F5.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441222520887, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222520954, "dur": 140, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_31922632D84128F5.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441222521105, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_A7B3B5FA99331E00.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441222521191, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222521290, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_42EBD154127454A5.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441222521382, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222521462, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_A3850CF6800E4193.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441222521615, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222521709, "dur": 350, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_A3850CF6800E4193.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441222522172, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Postprocessing.Runtime.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751441222522335, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Cinemachine.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1751441222522567, "dur": 760, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.Timeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751441222523332, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222523406, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor-firstpass.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751441222523511, "dur": 329, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Assembly-CSharp-Editor-firstpass.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1751441222523844, "dur": 1351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222525196, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222526144, "dur": 976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222527121, "dur": 1072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222528194, "dur": 1025, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222529497, "dur": 911, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.cinemachine\\Runtime\\Deprecated\\AxisState.cs"}}, {"pid": 12345, "tid": 23, "ts": 1751441222529220, "dur": 2086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222531307, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222532342, "dur": 868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222533211, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222534266, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222535312, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222536186, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222536797, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222537201, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222537263, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222537507, "dur": 1474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222538982, "dur": 1043, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222540025, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222540157, "dur": 561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1751441222540755, "dur": 813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.EditorCoroutines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1751441222541569, "dur": 1110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222542737, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222542816, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222542981, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222543144, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222543204, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222543254, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222543487, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222543595, "dur": 732, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222544328, "dur": 279, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222544608, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222544719, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222544900, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222544961, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222545074, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222545191, "dur": 614, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222545805, "dur": 853, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222546658, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222547061, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222547252, "dur": 1293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222548546, "dur": 616, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222549163, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222549423, "dur": 757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222550181, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222550778, "dur": 1046, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222551825, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222551983, "dur": 914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222552898, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222553040, "dur": 1071, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441222554112, "dur": 636259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1751441223190374, "dur": 232306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222476172, "dur": 43705, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222519879, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_4FB16FC3A796B887.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441222520062, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222520161, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_C287EB2677BA7F93.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441222520333, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_5E37763FF8B95A1F.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441222520442, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222520564, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FF5FCB0F465F7311.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441222520883, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222521041, "dur": 291, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_FF5FCB0F465F7311.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441222521405, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222521549, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_696347C4732CD601.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441222521685, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751441222521854, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222521931, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751441222522118, "dur": 884, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751441222523143, "dur": 658, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1300b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1751441222523803, "dur": 1300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222525104, "dur": 949, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222526053, "dur": 850, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222526904, "dur": 952, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222527857, "dur": 1212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222529509, "dur": 890, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.splines\\Editor\\Components\\SplineComponentEditor.cs"}}, {"pid": 12345, "tid": 24, "ts": 1751441222529071, "dur": 2026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222531098, "dur": 1173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222532272, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222533511, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222534783, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222535963, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222536753, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222537215, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222537290, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222537504, "dur": 1457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222538961, "dur": 1006, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222539968, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222540111, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1751441222540389, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222540648, "dur": 1297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1300b0aE.dag/Unity.Settings.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1751441222541946, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222542080, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222542156, "dur": 269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222542426, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222542537, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222542611, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222542731, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222542983, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222543146, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222543256, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222543476, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222543587, "dur": 721, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222544365, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222544619, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222544750, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222545076, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222545187, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222545784, "dur": 451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222546272, "dur": 364, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222546637, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222547034, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222547264, "dur": 1284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222548548, "dur": 631, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222549179, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222549432, "dur": 739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222550230, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222550782, "dur": 1049, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222551831, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222551949, "dur": 941, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222552890, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222553016, "dur": 1111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441222554128, "dur": 636283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1751441223190412, "dur": 232363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751441223447952, "dur": 5268, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 15948, "tid": 58885, "ts": 1751441223508569, "dur": 4048, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 15948, "tid": 58885, "ts": 1751441223512678, "dur": 5078, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 15948, "tid": 58885, "ts": 1751441223493963, "dur": 25654, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}
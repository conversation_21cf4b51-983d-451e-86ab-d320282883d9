# Free Camera

The **FreeCamera** component provides you with an implementation for a simple free camera. When you add this component to a Camera, you can use the keyboard and mouse, or a controller, to control the Camera's position and rotation in Play Mode.

## Properties

| **Property**              | **Description**                                              |
| ------------------------- | ------------------------------------------------------------ |
| **Look Speed Controller** | Set the look speed of the Camera when using a controller.    |
| **Look Speed Mouse**      | Set the look speed of the Camera when using a mouse.         |
| **Move Speed**            | Set the speed at which the Camera moves.                     |
| **Move Speed Increment**  | Set the value of the increment that you can increase or decrease the **Move Speed** by. This is useful if you have a large Scene and the current **Move Speed** is not enough to easily traverse it. |
| **Turbo**                 | Set the value that this component multiplies the **Move Speed** by when you press the key or button assigned to "Fire1". |

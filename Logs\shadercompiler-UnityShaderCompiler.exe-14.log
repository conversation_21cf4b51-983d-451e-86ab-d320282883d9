Base path: 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data', plugins path 'C:/Program Files/Unity/Hub/Editor/6000.0.30f1/Editor/Data/PlaybackEngines'
Cmd: initializeCompiler

Cmd: compileSnippet
  insize=1025 file=Assets/DefaultResourcesExtra/Normal-VertexLit.shader name=Legacy Shaders/VertexLit pass=ShadowCaster ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_SHADOWCASTER uKW=INSTANCING_ON SHADOWS_CUBE dKW=SHADOWS_DEPTH UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2049 mask=6 start=123 ok=1 outsize=2009

Cmd: compileSnippet
  insize=18247 file=Assets/DefaultResourcesExtra/TerrainShaders/Details/VertexLit.shader name=Hidden/TerrainEngine/Details/Vertexlit pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL SHADOWS_SCREEN VERTEXLIGHT_ON dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=19 ok=1 outsize=2985

Cmd: compileSnippet
  insize=18247 file=Assets/DefaultResourcesExtra/TerrainShaders/Details/VertexLit.shader name=Hidden/TerrainEngine/Details/Vertexlit pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN VERTEXLIGHT_ON dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=19 ok=1 outsize=4597

Cmd: compileSnippet
  insize=18247 file=Assets/DefaultResourcesExtra/TerrainShaders/Details/VertexLit.shader name=Hidden/TerrainEngine/Details/Vertexlit pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN VERTEXLIGHT_ON dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=19 ok=1 outsize=4597

Cmd: compileSnippet
  insize=18247 file=Assets/DefaultResourcesExtra/TerrainShaders/Details/VertexLit.shader name=Hidden/TerrainEngine/Details/Vertexlit pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN VERTEXLIGHT_ON dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=19 ok=1 outsize=4597

Cmd: compileSnippet
  insize=18247 file=Assets/DefaultResourcesExtra/TerrainShaders/Details/VertexLit.shader name=Hidden/TerrainEngine/Details/Vertexlit pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL dKW=FOG_LINEAR FOG_EXP FOG_EXP2 LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2081 mask=6 start=19 ok=1 outsize=2310

Cmd: compileSnippet
  insize=4727 file=Assets/DefaultResourcesExtra/TerrainShaders/Details/VertexLit.shader name=Hidden/TerrainEngine/Details/Vertexlit pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=POINT dKW=FOG_LINEAR FOG_EXP FOG_EXP2 DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=271 ok=1 outsize=2701

Cmd: compileSnippet
  insize=13224 file=Assets/DefaultResourcesExtra/TerrainShaders/Details/VertexLit.shader name=Hidden/TerrainEngine/Details/Vertexlit pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW= dKW=INSTANCING_ON LIGHTPROBE_SH DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_HDR_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=132 ok=1 outsize=1922

Cmd: compileSnippet
  insize=13224 file=Assets/DefaultResourcesExtra/TerrainShaders/Details/VertexLit.shader name=Hidden/TerrainEngine/Details/Vertexlit pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=LIGHTPROBE_SH dKW=INSTANCING_ON DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_HDR_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=132 ok=1 outsize=2693

Cmd: compileSnippet
  insize=4439 file=/ name=Hidden/TerrainEngine/Details/Vertexlit pass= ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=POINT dKW=FOG_LINEAR FOG_EXP FOG_EXP2 SPOT UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=1 mask=6 start=1 ok=1 outsize=4421

Cmd: compileSnippet
  insize=18723 file=Assets/DefaultResourcesExtra/TerrainShaders/Details/WavingGrass.shader name=Hidden/TerrainEngine/Details/WavingDoublePass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=30 ok=1 outsize=3518

Cmd: compileSnippet
  insize=18723 file=Assets/DefaultResourcesExtra/TerrainShaders/Details/WavingGrass.shader name=Hidden/TerrainEngine/Details/WavingDoublePass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=30 ok=1 outsize=3518

Cmd: compileSnippet
  insize=18723 file=Assets/DefaultResourcesExtra/TerrainShaders/Details/WavingGrass.shader name=Hidden/TerrainEngine/Details/WavingDoublePass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=30 ok=1 outsize=3518

Cmd: compileSnippet
  insize=18723 file=Assets/DefaultResourcesExtra/TerrainShaders/Details/WavingGrass.shader name=Hidden/TerrainEngine/Details/WavingDoublePass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL LIGHTPROBE_SH dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=30 ok=1 outsize=4275

Cmd: compileSnippet
  insize=4977 file=Assets/DefaultResourcesExtra/TerrainShaders/Details/WavingGrass.shader name=Hidden/TerrainEngine/Details/WavingDoublePass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=POINT_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=286 ok=1 outsize=4366

Cmd: compileSnippet
  insize=4977 file=Assets/DefaultResourcesExtra/TerrainShaders/Details/WavingGrass.shader name=Hidden/TerrainEngine/Details/WavingDoublePass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=POINT_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=286 ok=1 outsize=4366

Cmd: compileSnippet
  insize=4977 file=Assets/DefaultResourcesExtra/TerrainShaders/Details/WavingGrass.shader name=Hidden/TerrainEngine/Details/WavingDoublePass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=POINT_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=286 ok=1 outsize=4366

Cmd: compileSnippet
  insize=7504 file=Assets/DefaultResourcesExtra/TerrainShaders/Details/WavingGrass.shader name=Hidden/TerrainEngine/Details/WavingDoublePass pass=ShadowCaster ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_SHADOWCASTER uKW=INSTANCING_ON SHADOWS_CUBE dKW=SHADOWS_DEPTH UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2081 mask=6 start=148 ok=1 outsize=4280

Cmd: compileSnippet
  insize=4481 file=/ name=Hidden/TerrainEngine/Details/WavingDoublePass pass= ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH uKW=SPOT dKW=FOG_LINEAR FOG_EXP FOG_EXP2 POINT UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=1 mask=6 start=1 ok=1 outsize=4835

Cmd: compileSnippet
  insize=19765 file=Assets/DefaultResourcesExtra/TerrainShaders/Details/WavingGrassBillboard.shader name=Hidden/TerrainEngine/Details/BillboardWavingDoublePass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL SHADOWS_SCREEN VERTEXLIGHT_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=55 ok=1 outsize=5000

Cmd: compileSnippet
  insize=19765 file=Assets/DefaultResourcesExtra/TerrainShaders/Details/WavingGrassBillboard.shader name=Hidden/TerrainEngine/Details/BillboardWavingDoublePass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN VERTEXLIGHT_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=55 ok=1 outsize=6992

Cmd: compileSnippet
  insize=19765 file=Assets/DefaultResourcesExtra/TerrainShaders/Details/WavingGrassBillboard.shader name=Hidden/TerrainEngine/Details/BillboardWavingDoublePass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN VERTEXLIGHT_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=55 ok=1 outsize=6992

Cmd: compileSnippet
  insize=5498 file=Assets/DefaultResourcesExtra/TerrainShaders/Details/WavingGrassBillboard.shader name=Hidden/TerrainEngine/Details/BillboardWavingDoublePass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=POINT_COOKIE SHADOWS_SOFT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=227 mask=6 start=311 ok=1 outsize=10033

Cmd: compileSnippet
  insize=5498 file=Assets/DefaultResourcesExtra/TerrainShaders/Details/WavingGrassBillboard.shader name=Hidden/TerrainEngine/Details/BillboardWavingDoublePass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=POINT_COOKIE SHADOWS_SOFT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=227 mask=6 start=311 ok=1 outsize=10033

Cmd: compileSnippet
  insize=5498 file=Assets/DefaultResourcesExtra/TerrainShaders/Details/WavingGrassBillboard.shader name=Hidden/TerrainEngine/Details/BillboardWavingDoublePass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=POINT_COOKIE SHADOWS_SOFT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=227 mask=6 start=311 ok=1 outsize=10033

Cmd: compileSnippet
  insize=8546 file=Assets/DefaultResourcesExtra/TerrainShaders/Details/WavingGrassBillboard.shader name=Hidden/TerrainEngine/Details/BillboardWavingDoublePass pass=ShadowCaster ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_SHADOWCASTER uKW=INSTANCING_ON SHADOWS_DEPTH dKW=SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2081 mask=6 start=173 ok=1 outsize=4514

Cmd: compileSnippet
  insize=292388 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL SHADOWS_SCREEN _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=44 ok=1 outsize=7107

Cmd: compileSnippet
  insize=292388 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=44 ok=1 outsize=7746

Cmd: compileSnippet
  insize=292388 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=44 ok=1 outsize=7746

Cmd: compileSnippet
  insize=292388 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=44 ok=1 outsize=7746

Cmd: compileSnippet
  insize=292388 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL VERTEXLIGHT_ON _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=44 ok=1 outsize=6036

Cmd: compileSnippet
  insize=292388 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL VERTEXLIGHT_ON _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=44 ok=1 outsize=6036

Cmd: compileSnippet
  insize=292388 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL VERTEXLIGHT_ON _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=44 ok=1 outsize=6036

Cmd: compileSnippet
  insize=292388 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL LIGHTPROBE_SH VERTEXLIGHT_ON _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=44 ok=1 outsize=7625

Cmd: compileSnippet
  insize=292388 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL LIGHTPROBE_SH VERTEXLIGHT_ON _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=44 ok=1 outsize=7625

Cmd: compileSnippet
  insize=292388 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL LIGHTPROBE_SH VERTEXLIGHT_ON _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=44 ok=1 outsize=7625

Cmd: compileSnippet
  insize=292388 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL SHADOWS_SCREEN VERTEXLIGHT_ON _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=44 ok=1 outsize=7107

Cmd: compileSnippet
  insize=292388 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL SHADOWS_SCREEN VERTEXLIGHT_ON _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=44 ok=1 outsize=7107

Cmd: compileSnippet
  insize=292388 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL SHADOWS_SCREEN VERTEXLIGHT_ON _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=44 ok=1 outsize=7107

Cmd: compileSnippet
  insize=292388 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN VERTEXLIGHT_ON _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=44 ok=1 outsize=8654

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON DIRECTIONAL_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=4841

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON DIRECTIONAL_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=4841

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON SPOT SHADOWS_DEPTH dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=6207

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON SPOT SHADOWS_DEPTH dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=6207

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON SPOT SHADOWS_DEPTH dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=6207

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON SPOT SHADOWS_DEPTH SHADOWS_SOFT dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=6729

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON SPOT SHADOWS_DEPTH SHADOWS_SOFT dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=6729

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON SPOT SHADOWS_DEPTH SHADOWS_SOFT dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=6729

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON DIRECTIONAL SHADOWS_SCREEN _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=5156

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON DIRECTIONAL SHADOWS_SCREEN _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=5156

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON DIRECTIONAL SHADOWS_SCREEN _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=5156

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON SHADOWS_SCREEN _ALPHATEST_ON DIRECTIONAL_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=5799

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON SHADOWS_SCREEN _ALPHATEST_ON DIRECTIONAL_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=5799

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON SHADOWS_SCREEN _ALPHATEST_ON DIRECTIONAL_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=5799

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON POINT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=5927

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON POINT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=5927

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON POINT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=5927

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON POINT SHADOWS_SOFT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=6323

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON POINT SHADOWS_SOFT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=6323

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON POINT SHADOWS_SOFT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=6323

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON POINT_COOKIE SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=6046

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON POINT_COOKIE SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=6046

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON POINT_COOKIE SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=6046

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON POINT_COOKIE SHADOWS_SOFT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=6441

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON POINT_COOKIE SHADOWS_SOFT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=6441

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _NORMALMAP DIRECTIONAL SHADOWS_SCREEN dKW=FOG_LINEAR FOG_EXP FOG_EXP2 POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=8139

Cmd: compileSnippet
  insize=162312 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _NORMALMAP _ALPHATEST_ON POINT_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=270 ok=1 outsize=8117

Cmd: compileSnippet
  insize=240148 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON LIGHTPROBE_SH _ALPHATEST_ON dKW=_NORMALMAP DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_HDR_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=155 ok=1 outsize=4996

Cmd: compileSnippet
  insize=240148 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON LIGHTPROBE_SH _ALPHATEST_ON dKW=_NORMALMAP DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_HDR_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=155 ok=1 outsize=4996

Cmd: compileSnippet
  insize=240148 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON _ALPHATEST_ON UNITY_HDR_ON dKW=_NORMALMAP LIGHTPROBE_SH DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=155 ok=1 outsize=4165

Cmd: compileSnippet
  insize=240148 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON _ALPHATEST_ON UNITY_HDR_ON dKW=_NORMALMAP LIGHTPROBE_SH DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=155 ok=1 outsize=4165

Cmd: compileSnippet
  insize=240148 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON _ALPHATEST_ON UNITY_HDR_ON dKW=_NORMALMAP LIGHTPROBE_SH DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=155 ok=1 outsize=4165

Cmd: compileSnippet
  insize=240148 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON LIGHTPROBE_SH _ALPHATEST_ON UNITY_HDR_ON dKW=_NORMALMAP DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=155 ok=1 outsize=4969

Cmd: compileSnippet
  insize=240148 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON LIGHTPROBE_SH _ALPHATEST_ON UNITY_HDR_ON dKW=_NORMALMAP DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=155 ok=1 outsize=4969

Cmd: compileSnippet
  insize=240148 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON LIGHTPROBE_SH _ALPHATEST_ON UNITY_HDR_ON dKW=_NORMALMAP DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=155 ok=1 outsize=4969

Cmd: compileSnippet
  insize=70392 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL SHADOWS_SCREEN VERTEXLIGHT_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=51 ok=1 outsize=3992

Cmd: compileSnippet
  insize=70392 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN VERTEXLIGHT_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=51 ok=1 outsize=5641

Cmd: compileSnippet
  insize=70392 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN VERTEXLIGHT_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=51 ok=1 outsize=5641

Cmd: compileSnippet
  insize=70392 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN VERTEXLIGHT_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=51 ok=1 outsize=5641

Cmd: compileSnippet
  insize=35122 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=POINT dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=271 ok=1 outsize=3655

Cmd: compileSnippet
  insize=35122 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=POINT dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=271 ok=1 outsize=3655

Cmd: compileSnippet
  insize=35122 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=POINT dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=271 ok=1 outsize=3655

Cmd: compileSnippet
  insize=35122 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=DIRECTIONAL dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=271 ok=1 outsize=2939

Cmd: compileSnippet
  insize=35122 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=DIRECTIONAL dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=271 ok=1 outsize=2939

Cmd: compileSnippet
  insize=35122 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=DIRECTIONAL dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=271 ok=1 outsize=2939

Cmd: compileSnippet
  insize=35122 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON POINT_COOKIE SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2081 mask=6 start=271 ok=1 outsize=5938

Cmd: compileSnippet
  insize=54606 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=UNITY_HDR_ON dKW=INSTANCING_ON LIGHTPROBE_SH DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=142 ok=1 outsize=2945

Cmd: compileSnippet
  insize=54606 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/AddPass.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-AddPass pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=LIGHTPROBE_SH UNITY_HDR_ON dKW=INSTANCING_ON DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=142 ok=1 outsize=3720

Cmd: compileSnippet
  insize=36749 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL SHADOWS_SCREEN dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=21 ok=1 outsize=2993

Cmd: compileSnippet
  insize=36749 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=21 ok=1 outsize=3743

Cmd: compileSnippet
  insize=36749 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=21 ok=1 outsize=3743

Cmd: compileSnippet
  insize=36749 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=21 ok=1 outsize=3743

Cmd: compileSnippet
  insize=36749 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL VERTEXLIGHT_ON dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=21 ok=1 outsize=1979

Cmd: compileSnippet
  insize=36749 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL VERTEXLIGHT_ON dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=21 ok=1 outsize=1979

Cmd: compileSnippet
  insize=36749 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL VERTEXLIGHT_ON dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=21 ok=1 outsize=1979

Cmd: compileSnippet
  insize=36749 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL LIGHTPROBE_SH VERTEXLIGHT_ON dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=21 ok=1 outsize=3597

Cmd: compileSnippet
  insize=36749 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL LIGHTPROBE_SH VERTEXLIGHT_ON _ALPHATEST_ON dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=21 ok=1 outsize=3739

Cmd: compileSnippet
  insize=17997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=DIRECTIONAL_COOKIE dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=271 ok=1 outsize=2677

Cmd: compileSnippet
  insize=17997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=DIRECTIONAL_COOKIE dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=271 ok=1 outsize=2677

Cmd: compileSnippet
  insize=17997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=SPOT SHADOWS_DEPTH dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=271 ok=1 outsize=3913

Cmd: compileSnippet
  insize=17997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=SPOT SHADOWS_DEPTH dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=271 ok=1 outsize=3913

Cmd: compileSnippet
  insize=17997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=SPOT SHADOWS_DEPTH dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=271 ok=1 outsize=3913

Cmd: compileSnippet
  insize=17997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=SPOT SHADOWS_DEPTH SHADOWS_SOFT dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=4326

Cmd: compileSnippet
  insize=17997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=SPOT SHADOWS_DEPTH SHADOWS_SOFT dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=4326

Cmd: compileSnippet
  insize=17997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=SPOT SHADOWS_DEPTH SHADOWS_SOFT dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=4326

Cmd: compileSnippet
  insize=17997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=DIRECTIONAL SHADOWS_SCREEN dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=2959

Cmd: compileSnippet
  insize=17997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=DIRECTIONAL SHADOWS_SCREEN dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=2959

Cmd: compileSnippet
  insize=17997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=DIRECTIONAL SHADOWS_SCREEN dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=2959

Cmd: compileSnippet
  insize=17997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=SHADOWS_SCREEN DIRECTIONAL_COOKIE dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=3647

Cmd: compileSnippet
  insize=17997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON SHADOWS_SCREEN DIRECTIONAL_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=271 ok=1 outsize=4573

Cmd: compileSnippet
  insize=27050 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON dKW=LIGHTPROBE_SH DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_HDR_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2081 mask=6 start=135 ok=1 outsize=2874

Cmd: compileSnippet
  insize=27050 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON dKW=LIGHTPROBE_SH DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_HDR_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2081 mask=6 start=135 ok=1 outsize=2874

Cmd: compileSnippet
  insize=27050 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON dKW=LIGHTPROBE_SH DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_HDR_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2081 mask=6 start=135 ok=1 outsize=2874

Cmd: compileSnippet
  insize=14690 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=ShadowCaster ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_SHADOWCASTER uKW=INSTANCING_ON SHADOWS_DEPTH dKW=SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2081 mask=6 start=203 ok=1 outsize=3268

Cmd: compileSnippet
  insize=14690 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/DiffuseBase.shader name=Hidden/TerrainEngine/Splatmap/Diffuse-Base pass=ShadowCaster ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_SHADOWCASTER uKW=INSTANCING_ON SHADOWS_DEPTH dKW=SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2081 mask=6 start=203 ok=1 outsize=3268

Cmd: compileSnippet
  insize=290948 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON _NORMALMAP DIRECTIONAL SHADOWS_SCREEN _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=45 ok=1 outsize=8421

Cmd: compileSnippet
  insize=290948 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON _NORMALMAP DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=45 ok=1 outsize=9132

Cmd: compileSnippet
  insize=290948 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON _NORMALMAP DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=45 ok=1 outsize=9132

Cmd: compileSnippet
  insize=290948 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON _NORMALMAP DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=45 ok=1 outsize=9132

Cmd: compileSnippet
  insize=290948 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON _NORMALMAP DIRECTIONAL VERTEXLIGHT_ON _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=45 ok=1 outsize=7318

Cmd: compileSnippet
  insize=290948 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON _NORMALMAP DIRECTIONAL VERTEXLIGHT_ON _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=45 ok=1 outsize=7318

Cmd: compileSnippet
  insize=290948 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON _NORMALMAP DIRECTIONAL VERTEXLIGHT_ON _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=45 ok=1 outsize=7318

Cmd: compileSnippet
  insize=290948 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON _NORMALMAP DIRECTIONAL LIGHTPROBE_SH VERTEXLIGHT_ON _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=45 ok=1 outsize=8896

Cmd: compileSnippet
  insize=290948 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON _NORMALMAP DIRECTIONAL LIGHTPROBE_SH VERTEXLIGHT_ON _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=45 ok=1 outsize=8896

Cmd: compileSnippet
  insize=290948 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON _NORMALMAP DIRECTIONAL LIGHTPROBE_SH VERTEXLIGHT_ON _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=45 ok=1 outsize=8896

Cmd: compileSnippet
  insize=290948 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON _NORMALMAP DIRECTIONAL SHADOWS_SCREEN VERTEXLIGHT_ON _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=45 ok=1 outsize=8421

Cmd: compileSnippet
  insize=290948 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON _NORMALMAP DIRECTIONAL SHADOWS_SCREEN VERTEXLIGHT_ON _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=45 ok=1 outsize=8421

Cmd: compileSnippet
  insize=290948 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON _NORMALMAP DIRECTIONAL SHADOWS_SCREEN VERTEXLIGHT_ON _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=45 ok=1 outsize=8421

Cmd: compileSnippet
  insize=160872 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_NORMALMAP _ALPHATEST_ON DIRECTIONAL_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=6663

Cmd: compileSnippet
  insize=160872 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_NORMALMAP _ALPHATEST_ON DIRECTIONAL_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=6663

Cmd: compileSnippet
  insize=160872 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_NORMALMAP _ALPHATEST_ON SPOT SHADOWS_DEPTH dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=7872

Cmd: compileSnippet
  insize=160872 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_NORMALMAP _ALPHATEST_ON SPOT SHADOWS_DEPTH dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=7872

Cmd: compileSnippet
  insize=160872 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_NORMALMAP _ALPHATEST_ON SPOT SHADOWS_DEPTH dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=7872

Cmd: compileSnippet
  insize=160872 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_NORMALMAP _ALPHATEST_ON SPOT SHADOWS_DEPTH SHADOWS_SOFT dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=8299

Cmd: compileSnippet
  insize=160872 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_NORMALMAP _ALPHATEST_ON SPOT SHADOWS_DEPTH SHADOWS_SOFT dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=8299

Cmd: compileSnippet
  insize=160872 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_NORMALMAP _ALPHATEST_ON SPOT SHADOWS_DEPTH SHADOWS_SOFT dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=8299

Cmd: compileSnippet
  insize=160872 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_NORMALMAP DIRECTIONAL SHADOWS_SCREEN _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=6957

Cmd: compileSnippet
  insize=160872 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_NORMALMAP DIRECTIONAL SHADOWS_SCREEN _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=6957

Cmd: compileSnippet
  insize=160872 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_NORMALMAP DIRECTIONAL SHADOWS_SCREEN _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=6957

Cmd: compileSnippet
  insize=160872 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_NORMALMAP SHADOWS_SCREEN _ALPHATEST_ON DIRECTIONAL_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=7594

Cmd: compileSnippet
  insize=160872 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_NORMALMAP SHADOWS_SCREEN _ALPHATEST_ON DIRECTIONAL_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=7594

Cmd: compileSnippet
  insize=160872 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_NORMALMAP SHADOWS_SCREEN _ALPHATEST_ON DIRECTIONAL_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=7594

Cmd: compileSnippet
  insize=160872 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_NORMALMAP _ALPHATEST_ON POINT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=7745

Cmd: compileSnippet
  insize=160872 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_NORMALMAP _ALPHATEST_ON POINT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=7745

Cmd: compileSnippet
  insize=160872 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_NORMALMAP _ALPHATEST_ON POINT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=7745

Cmd: compileSnippet
  insize=160872 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_NORMALMAP _ALPHATEST_ON POINT SHADOWS_SOFT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=8033

Cmd: compileSnippet
  insize=160872 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_NORMALMAP _ALPHATEST_ON POINT SHADOWS_SOFT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=8033

Cmd: compileSnippet
  insize=160872 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_NORMALMAP _ALPHATEST_ON POINT SHADOWS_SOFT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=8033

Cmd: compileSnippet
  insize=160872 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_NORMALMAP _ALPHATEST_ON POINT_COOKIE SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=7838

Cmd: compileSnippet
  insize=160872 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_NORMALMAP _ALPHATEST_ON POINT_COOKIE SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=271 ok=1 outsize=7838

Cmd: compileSnippet
  insize=240244 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON _NORMALMAP LIGHTPROBE_SH _ALPHATEST_ON dKW=DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_HDR_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=155 ok=1 outsize=7928

Cmd: compileSnippet
  insize=240244 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON _NORMALMAP LIGHTPROBE_SH _ALPHATEST_ON dKW=DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_HDR_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=155 ok=1 outsize=7928

Cmd: compileSnippet
  insize=240244 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON _NORMALMAP _ALPHATEST_ON UNITY_HDR_ON dKW=LIGHTPROBE_SH DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=155 ok=1 outsize=7136

Cmd: compileSnippet
  insize=240244 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON _NORMALMAP _ALPHATEST_ON UNITY_HDR_ON dKW=LIGHTPROBE_SH DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=155 ok=1 outsize=7136

Cmd: compileSnippet
  insize=240244 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON _NORMALMAP _ALPHATEST_ON UNITY_HDR_ON dKW=LIGHTPROBE_SH DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=155 ok=1 outsize=7136

Cmd: compileSnippet
  insize=240244 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON _NORMALMAP LIGHTPROBE_SH _ALPHATEST_ON UNITY_HDR_ON dKW=DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=155 ok=1 outsize=7899

Cmd: compileSnippet
  insize=240244 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON _NORMALMAP LIGHTPROBE_SH _ALPHATEST_ON UNITY_HDR_ON dKW=DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=155 ok=1 outsize=7899

Cmd: compileSnippet
  insize=122084 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=ShadowCaster ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_SHADOWCASTER uKW=INSTANCING_ON _NORMALMAP SHADOWS_CUBE dKW=SHADOWS_DEPTH UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=224 ok=1 outsize=3185

Cmd: compileSnippet
  insize=122084 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=ShadowCaster ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_SHADOWCASTER uKW=INSTANCING_ON _NORMALMAP SHADOWS_CUBE dKW=SHADOWS_DEPTH UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=224 ok=1 outsize=3185

Cmd: compileSnippet
  insize=122084 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=ShadowCaster ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_SHADOWCASTER uKW=INSTANCING_ON _NORMALMAP _ALPHATEST_ON SHADOWS_DEPTH dKW=SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=224 ok=1 outsize=3520

Cmd: compileSnippet
  insize=122084 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=ShadowCaster ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_SHADOWCASTER uKW=INSTANCING_ON _NORMALMAP _ALPHATEST_ON SHADOWS_DEPTH dKW=SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=224 ok=1 outsize=3520

Cmd: compileSnippet
  insize=70032 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL SHADOWS_SCREEN dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=55 ok=1 outsize=3992

Cmd: compileSnippet
  insize=70032 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=55 ok=1 outsize=4759

Cmd: compileSnippet
  insize=70032 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=55 ok=1 outsize=4759

Cmd: compileSnippet
  insize=70032 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=55 ok=1 outsize=4759

Cmd: compileSnippet
  insize=34762 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON POINT_COOKIE SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2081 mask=6 start=275 ok=1 outsize=9520

Cmd: compileSnippet
  insize=34762 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON POINT_COOKIE SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2081 mask=6 start=275 ok=1 outsize=9520

Cmd: compileSnippet
  insize=34762 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON POINT_COOKIE SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2081 mask=6 start=275 ok=1 outsize=9520

Cmd: compileSnippet
  insize=34762 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON POINT_COOKIE SHADOWS_SOFT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=275 ok=1 outsize=10214

Cmd: compileSnippet
  insize=34762 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON POINT_COOKIE SHADOWS_SOFT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=275 ok=1 outsize=10214

Cmd: compileSnippet
  insize=34762 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON POINT_COOKIE SHADOWS_SOFT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=275 ok=1 outsize=10214

Cmd: compileSnippet
  insize=54630 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=UNITY_HDR_ON dKW=INSTANCING_ON LIGHTPROBE_SH DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=145 ok=1 outsize=2945

Cmd: compileSnippet
  insize=54630 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=UNITY_HDR_ON dKW=INSTANCING_ON LIGHTPROBE_SH DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=145 ok=1 outsize=2945

Cmd: compileSnippet
  insize=29238 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/FirstPass.shader name=Nature/Terrain/Diffuse pass=ShadowCaster ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_SHADOWCASTER uKW=SHADOWS_CUBE dKW=INSTANCING_ON SHADOWS_DEPTH UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=33 mask=6 start=216 ok=1 outsize=2331

Cmd: compileSnippet
  insize=319997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=26 ok=1 outsize=8067

Cmd: compileSnippet
  insize=319997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=26 ok=1 outsize=8556

Cmd: compileSnippet
  insize=319997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=26 ok=1 outsize=8556

Cmd: compileSnippet
  insize=319997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL LIGHTPROBE_SH dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=26 ok=1 outsize=8830

Cmd: compileSnippet
  insize=319997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL LIGHTPROBE_SH dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=26 ok=1 outsize=9542

Cmd: compileSnippet
  insize=319997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL LIGHTPROBE_SH dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=26 ok=1 outsize=9542

Cmd: compileSnippet
  insize=319997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL SHADOWS_SCREEN dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=26 ok=1 outsize=8982

Cmd: compileSnippet
  insize=319997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL SHADOWS_SCREEN dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=26 ok=1 outsize=9329

Cmd: compileSnippet
  insize=319997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL SHADOWS_SCREEN dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=26 ok=1 outsize=9329

Cmd: compileSnippet
  insize=319997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=26 ok=1 outsize=9847

Cmd: compileSnippet
  insize=319997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=26 ok=1 outsize=10076

Cmd: compileSnippet
  insize=319997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=26 ok=1 outsize=10076

Cmd: compileSnippet
  insize=319997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL VERTEXLIGHT_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=26 ok=1 outsize=8067

Cmd: compileSnippet
  insize=319997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL VERTEXLIGHT_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=26 ok=1 outsize=8556

Cmd: compileSnippet
  insize=319997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL VERTEXLIGHT_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=26 ok=1 outsize=8556

Cmd: compileSnippet
  insize=319997 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=_NORMALMAP DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN VERTEXLIGHT_ON _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=26 ok=1 outsize=11156

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=POINT dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=4776

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=POINT dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=5246

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=POINT dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=5246

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=DIRECTIONAL dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=3891

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=DIRECTIONAL dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=4551

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=DIRECTIONAL dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=4551

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=SPOT dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=4857

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=SPOT dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=5331

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=SPOT dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=5331

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=POINT_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=4866

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=POINT_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=5338

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=POINT_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=5338

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=DIRECTIONAL_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=4697

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=DIRECTIONAL_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=5250

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=DIRECTIONAL_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=5250

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=SPOT SHADOWS_DEPTH dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=5703

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=SPOT SHADOWS_DEPTH dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=6107

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=SPOT SHADOWS_DEPTH dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=6107

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=SPOT SHADOWS_DEPTH SHADOWS_SOFT dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=6381

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=SPOT SHADOWS_DEPTH SHADOWS_SOFT dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=6642

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=SPOT SHADOWS_DEPTH SHADOWS_SOFT dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=6642

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=DIRECTIONAL SHADOWS_SCREEN dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=4946

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=DIRECTIONAL SHADOWS_SCREEN dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=5458

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=DIRECTIONAL SHADOWS_SCREEN dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=5458

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=SHADOWS_SCREEN DIRECTIONAL_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=5618

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=SHADOWS_SCREEN DIRECTIONAL_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=6125

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_ALPHATEST_ON POINT_COOKIE SHADOWS_SOFT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=6763

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_ALPHATEST_ON POINT_COOKIE SHADOWS_SOFT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON _NORMALMAP POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=6763

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_NORMALMAP DIRECTIONAL SHADOWS_SCREEN dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=8131

Cmd: compileSnippet
  insize=194161 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_NORMALMAP _ALPHATEST_ON POINT_COOKIE SHADOWS_SOFT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 INSTANCING_ON POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=285 ok=1 outsize=9374

Cmd: compileSnippet
  insize=283893 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON dKW=_NORMALMAP LIGHTPROBE_SH DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_HDR_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=168 ok=1 outsize=5439

Cmd: compileSnippet
  insize=283893 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON dKW=_NORMALMAP LIGHTPROBE_SH DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_HDR_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=168 ok=1 outsize=5439

Cmd: compileSnippet
  insize=283893 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON dKW=_NORMALMAP LIGHTPROBE_SH DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_HDR_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=168 ok=1 outsize=5439

Cmd: compileSnippet
  insize=283893 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON LIGHTPROBE_SH dKW=_NORMALMAP DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_HDR_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=168 ok=1 outsize=6206

Cmd: compileSnippet
  insize=283893 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON LIGHTPROBE_SH dKW=_NORMALMAP DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_HDR_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=168 ok=1 outsize=6206

Cmd: compileSnippet
  insize=283893 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON LIGHTPROBE_SH dKW=_NORMALMAP DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_HDR_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=168 ok=1 outsize=6206

Cmd: compileSnippet
  insize=283893 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON UNITY_HDR_ON dKW=_NORMALMAP LIGHTPROBE_SH DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=168 ok=1 outsize=5464

Cmd: compileSnippet
  insize=283893 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON LIGHTPROBE_SH _ALPHATEST_ON dKW=_NORMALMAP DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_HDR_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=168 ok=1 outsize=6394

Cmd: compileSnippet
  insize=144405 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=ShadowCaster ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_SHADOWCASTER uKW=SHADOWS_CUBE dKW=INSTANCING_ON _NORMALMAP SHADOWS_DEPTH UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=247 ok=1 outsize=2331

Cmd: compileSnippet
  insize=144405 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=ShadowCaster ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_SHADOWCASTER uKW=SHADOWS_CUBE dKW=INSTANCING_ON _NORMALMAP SHADOWS_DEPTH UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=247 ok=1 outsize=2331

Cmd: compileSnippet
  insize=144405 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=ShadowCaster ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_SHADOWCASTER uKW=_ALPHATEST_ON SHADOWS_DEPTH dKW=INSTANCING_ON _NORMALMAP SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=247 ok=1 outsize=2657

Cmd: compileSnippet
  insize=144405 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=ShadowCaster ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_SHADOWCASTER uKW=_ALPHATEST_ON SHADOWS_DEPTH dKW=INSTANCING_ON _NORMALMAP SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=247 ok=1 outsize=2657

Cmd: compileSnippet
  insize=144405 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-FirstPass.shader name=Nature/Terrain/Standard pass=ShadowCaster ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_SHADOWCASTER uKW=_NORMALMAP SHADOWS_CUBE dKW=INSTANCING_ON SHADOWS_DEPTH UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=247 ok=1 outsize=2331

Cmd: compileSnippet
  insize=319516 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL LIGHTPROBE_SH VERTEXLIGHT_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=23 ok=1 outsize=9243

Cmd: compileSnippet
  insize=319516 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL LIGHTPROBE_SH VERTEXLIGHT_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=23 ok=1 outsize=9243

Cmd: compileSnippet
  insize=319516 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL SHADOWS_SCREEN VERTEXLIGHT_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=23 ok=1 outsize=7743

Cmd: compileSnippet
  insize=319516 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL SHADOWS_SCREEN VERTEXLIGHT_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=23 ok=1 outsize=8567

Cmd: compileSnippet
  insize=319516 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL SHADOWS_SCREEN VERTEXLIGHT_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=23 ok=1 outsize=8567

Cmd: compileSnippet
  insize=319516 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN VERTEXLIGHT_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=23 ok=1 outsize=9354

Cmd: compileSnippet
  insize=319516 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN VERTEXLIGHT_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=23 ok=1 outsize=10013

Cmd: compileSnippet
  insize=319516 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN VERTEXLIGHT_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=23 ok=1 outsize=10013

Cmd: compileSnippet
  insize=319516 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=23 ok=1 outsize=6960

Cmd: compileSnippet
  insize=319516 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=23 ok=1 outsize=7672

Cmd: compileSnippet
  insize=319516 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=23 ok=1 outsize=7672

Cmd: compileSnippet
  insize=319516 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL LIGHTPROBE_SH _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=23 ok=1 outsize=7786

Cmd: compileSnippet
  insize=319516 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL LIGHTPROBE_SH _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=23 ok=1 outsize=8542

Cmd: compileSnippet
  insize=319516 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL LIGHTPROBE_SH _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=23 ok=1 outsize=8542

Cmd: compileSnippet
  insize=319516 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL SHADOWS_SCREEN _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=23 ok=1 outsize=7942

Cmd: compileSnippet
  insize=319516 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL SHADOWS_SCREEN _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=23 ok=1 outsize=8780

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON DIRECTIONAL_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=283 ok=1 outsize=10843

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON DIRECTIONAL_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=283 ok=1 outsize=10843

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON SPOT SHADOWS_DEPTH dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=283 ok=1 outsize=12549

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON SPOT SHADOWS_DEPTH dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=283 ok=1 outsize=13121

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON SPOT SHADOWS_DEPTH dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=283 ok=1 outsize=13121

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON SPOT SHADOWS_DEPTH SHADOWS_SOFT dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=283 ok=1 outsize=13213

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON SPOT SHADOWS_DEPTH SHADOWS_SOFT dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=283 ok=1 outsize=13785

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON SPOT SHADOWS_DEPTH SHADOWS_SOFT dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=283 ok=1 outsize=13785

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON DIRECTIONAL SHADOWS_SCREEN _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=283 ok=1 outsize=11035

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON DIRECTIONAL SHADOWS_SCREEN _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=283 ok=1 outsize=11612

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON DIRECTIONAL SHADOWS_SCREEN _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=283 ok=1 outsize=11612

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON SHADOWS_SCREEN _ALPHATEST_ON DIRECTIONAL_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=283 ok=1 outsize=11973

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON SHADOWS_SCREEN _ALPHATEST_ON DIRECTIONAL_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=283 ok=1 outsize=12550

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON SHADOWS_SCREEN _ALPHATEST_ON DIRECTIONAL_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=283 ok=1 outsize=12550

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON POINT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=283 ok=1 outsize=12229

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON POINT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=283 ok=1 outsize=12821

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON POINT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=283 ok=1 outsize=12821

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON POINT SHADOWS_SOFT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=283 ok=1 outsize=12906

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON POINT SHADOWS_SOFT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=283 ok=1 outsize=13522

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON POINT SHADOWS_SOFT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=283 ok=1 outsize=13522

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON POINT_COOKIE SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=283 ok=1 outsize=12385

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON POINT_COOKIE SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=283 ok=1 outsize=12977

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON POINT_COOKIE SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=283 ok=1 outsize=12977

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON POINT_COOKIE SHADOWS_SOFT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=283 ok=1 outsize=13062

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON POINT_COOKIE SHADOWS_SOFT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 _NORMALMAP POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=283 ok=1 outsize=13678

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _NORMALMAP DIRECTIONAL SHADOWS_SCREEN dKW=FOG_LINEAR FOG_EXP FOG_EXP2 POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=283 ok=1 outsize=14748

Cmd: compileSnippet
  insize=193680 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _NORMALMAP _ALPHATEST_ON DIRECTIONAL_COOKIE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=283 ok=1 outsize=8851

Cmd: compileSnippet
  insize=281876 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON UNITY_HDR_ON dKW=_NORMALMAP LIGHTPROBE_SH DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=167 ok=1 outsize=8470

Cmd: compileSnippet
  insize=281876 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON LIGHTPROBE_SH UNITY_HDR_ON dKW=_NORMALMAP DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=167 ok=1 outsize=9562

Cmd: compileSnippet
  insize=281876 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON LIGHTPROBE_SH UNITY_HDR_ON dKW=_NORMALMAP DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=167 ok=1 outsize=9562

Cmd: compileSnippet
  insize=281876 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON LIGHTPROBE_SH UNITY_HDR_ON dKW=_NORMALMAP DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=167 ok=1 outsize=9562

Cmd: compileSnippet
  insize=281876 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON _ALPHATEST_ON dKW=_NORMALMAP LIGHTPROBE_SH DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_HDR_ON UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=167 ok=1 outsize=8658

Cmd: compileSnippet
  insize=281876 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON _ALPHATEST_ON dKW=_NORMALMAP LIGHTPROBE_SH DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_HDR_ON UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=167 ok=1 outsize=8658

Cmd: compileSnippet
  insize=281876 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON _ALPHATEST_ON dKW=_NORMALMAP LIGHTPROBE_SH DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_HDR_ON UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=167 ok=1 outsize=8658

Cmd: compileSnippet
  insize=281876 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-AddPass.shader name=Hidden/TerrainEngine/Splatmap/Standard-AddPass pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON LIGHTPROBE_SH _ALPHATEST_ON dKW=_NORMALMAP DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_HDR_ON UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=167 ok=1 outsize=9823

Cmd: compileSnippet
  insize=42726 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL SHADOWS_SCREEN _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=29 ok=1 outsize=11606

Cmd: compileSnippet
  insize=42726 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=29 ok=1 outsize=12014

Cmd: compileSnippet
  insize=42726 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=29 ok=1 outsize=12730

Cmd: compileSnippet
  insize=42726 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL LIGHTPROBE_SH SHADOWS_SCREEN _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=29 ok=1 outsize=12730

Cmd: compileSnippet
  insize=42726 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL VERTEXLIGHT_ON _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=29 ok=1 outsize=9222

Cmd: compileSnippet
  insize=42726 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL VERTEXLIGHT_ON _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=29 ok=1 outsize=10033

Cmd: compileSnippet
  insize=42726 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_ASTC_NORMALMAP_ENCODING SHADER_API_GLES30 UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL VERTEXLIGHT_ON _ALPHATEST_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=gles3 reqs=2275 mask=6 start=29 ok=1 outsize=10033

Cmd: compileSnippet
  insize=25206 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_ALPHATEST_ON POINT dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=301 ok=1 outsize=3680

Cmd: compileSnippet
  insize=25206 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_ALPHATEST_ON POINT dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=301 ok=1 outsize=4051

Cmd: compileSnippet
  insize=25206 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_ALPHATEST_ON POINT dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 DIRECTIONAL SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=301 ok=1 outsize=4051

Cmd: compileSnippet
  insize=25206 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=DIRECTIONAL _ALPHATEST_ON dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=301 ok=1 outsize=2849

Cmd: compileSnippet
  insize=25206 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=DIRECTIONAL _ALPHATEST_ON dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=301 ok=1 outsize=3408

Cmd: compileSnippet
  insize=25206 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=DIRECTIONAL _ALPHATEST_ON dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 POINT SPOT POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=301 ok=1 outsize=3408

Cmd: compileSnippet
  insize=25206 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_ALPHATEST_ON SPOT dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=301 ok=1 outsize=3686

Cmd: compileSnippet
  insize=25206 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_ALPHATEST_ON SPOT dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=301 ok=1 outsize=4222

Cmd: compileSnippet
  insize=25206 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_ALPHATEST_ON SPOT dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL POINT_COOKIE DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=301 ok=1 outsize=4222

Cmd: compileSnippet
  insize=25206 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_ALPHATEST_ON POINT_COOKIE dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=301 ok=1 outsize=3774

Cmd: compileSnippet
  insize=25206 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_ALPHATEST_ON POINT_COOKIE dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=301 ok=1 outsize=4135

Cmd: compileSnippet
  insize=25206 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_ALPHATEST_ON POINT_COOKIE dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=301 ok=1 outsize=4135

Cmd: compileSnippet
  insize=25206 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=_ALPHATEST_ON DIRECTIONAL_COOKIE dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_SCREEN SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=301 ok=1 outsize=3523

Cmd: compileSnippet
  insize=25206 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=SHADOWS_SCREEN _ALPHATEST_ON DIRECTIONAL_COOKIE dKW=INSTANCING_ON FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT POINT_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SOFT SHADOWS_CUBE UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=301 ok=1 outsize=4882

Cmd: compileSnippet
  insize=25206 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDADD uKW=INSTANCING_ON _ALPHATEST_ON POINT_COOKIE SHADOWS_SOFT SHADOWS_CUBE dKW=FOG_LINEAR FOG_EXP FOG_EXP2 POINT DIRECTIONAL SPOT DIRECTIONAL_COOKIE SHADOWS_SHADOWMASK LIGHTMAP_SHADOW_MIXING SHADOWS_DEPTH SHADOWS_SCREEN UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=301 ok=1 outsize=7058

Cmd: compileSnippet
  insize=35437 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW= dKW=INSTANCING_ON LIGHTPROBE_SH DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_HDR_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=178 ok=1 outsize=2129

Cmd: compileSnippet
  insize=35437 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW= dKW=INSTANCING_ON LIGHTPROBE_SH DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_HDR_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=178 ok=1 outsize=2129

Cmd: compileSnippet
  insize=35437 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW= dKW=INSTANCING_ON LIGHTPROBE_SH DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_HDR_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=178 ok=1 outsize=2129

Cmd: compileSnippet
  insize=35437 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=LIGHTPROBE_SH dKW=INSTANCING_ON DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_HDR_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=178 ok=1 outsize=2955

Cmd: compileSnippet
  insize=35437 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON UNITY_HDR_ON dKW=LIGHTPROBE_SH DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=178 ok=1 outsize=3788

Cmd: compileSnippet
  insize=19493 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=ShadowCaster ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_SHADOWCASTER uKW=_ALPHATEST_ON SHADOWS_CUBE dKW=INSTANCING_ON SHADOWS_DEPTH UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=254 ok=1 outsize=2542

Cmd: compileSnippet
  insize=19493 file=Assets/DefaultResourcesExtra/TerrainShaders/Splats/Standard-Base.shader name=Hidden/TerrainEngine/Splatmap/Standard-Base pass=ShadowCaster ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_SHADOWCASTER uKW=_ALPHATEST_ON SHADOWS_CUBE dKW=INSTANCING_ON SHADOWS_DEPTH UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=227 mask=6 start=254 ok=1 outsize=2542

Cmd: compileSnippet
  insize=17873 file=Assets/DefaultResourcesExtra/Normal-Diffuse.shader name=Legacy Shaders/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER3 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL LIGHTPROBE_SH dKW=FOG_LINEAR FOG_EXP FOG_EXP2 SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED SHADOWS_SCREEN VERTEXLIGHT_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER2 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2081 mask=6 start=20 ok=1 outsize=3468

Cmd: compileSnippet
  insize=17873 file=Assets/DefaultResourcesExtra/Normal-Diffuse.shader name=Legacy Shaders/Diffuse pass=FORWARD ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF3 SHADER_API_MOBILE UNITY_HARDWARE_TIER1 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_FORWARDBASE uKW=INSTANCING_ON DIRECTIONAL SHADOWS_SCREEN VERTEXLIGHT_ON dKW=FOG_LINEAR FOG_EXP FOG_EXP2 LIGHTPROBE_SH SHADOWS_SHADOWMASK DYNAMICLIGHTMAP_ON LIGHTMAP_ON LIGHTMAP_SHADOW_MIXING DIRLIGHTMAP_COMBINED UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF2 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER2 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2275 mask=6 start=20 ok=1 outsize=3257

Cmd: compileSnippet
  insize=13000 file=Assets/DefaultResourcesExtra/Normal-Diffuse.shader name=Legacy Shaders/Diffuse pass=DEFERRED ppOnly=0 stripLineD=0 buildPlatform=13 rsLen=0 pKW=UNITY_NO_RGBM UNITY_ENABLE_REFLECTION_BUFFERS UNITY_NO_CUBEMAP_ARRAY UNITY_NO_SCREENSPACE_SHADOWS UNITY_PBS_USE_BRDF2 SHADER_API_MOBILE UNITY_HARDWARE_TIER2 UNITY_LIGHTMAP_RGBM_ENCODING UNITY_PRETRANSFORM_TO_DISPLAY_ORIENTATION UNITY_ASTC_NORMALMAP_ENCODING UNITY_PLATFORM_SUPPORTS_DEPTH_FETCH UNITY_PASS_DEFERRED uKW=INSTANCING_ON LIGHTPROBE_SH dKW=DYNAMICLIGHTMAP_ON SHADOWS_SHADOWMASK LIGHTMAP_ON DIRLIGHTMAP_COMBINED UNITY_HDR_ON UNITY_NO_DXT5nm UNITY_FRAMEBUFFER_FETCH_AVAILABLE UNITY_METAL_SHADOWS_USE_POINT_FILTERING UNITY_USE_DITHER_MASK_FOR_ALPHABLENDED_SHADOWS UNITY_PBS_USE_BRDF1 UNITY_PBS_USE_BRDF3 UNITY_SPECCUBE_BOX_PROJECTION UNITY_SPECCUBE_BLENDING UNITY_ENABLE_DETAIL_NORMALMAP UNITY_HARDWARE_TIER1 UNITY_HARDWARE_TIER3 UNITY_COLORSPACE_GAMMA UNITY_LIGHT_PROBE_PROXY_VOLUME UNITY_LIGHTMAP_DLDR_ENCODING UNITY_LIGHTMAP_FULL_HDR UNITY_VIRTUAL_TEXTURING SHADER_API_GLES30 UNITY_UNIFIED_SHADER_PRECISION_MODEL flags=268435456 lang=0 type=Vertex platform=vulkan reqs=2081 mask=6 start=128 ok=1 outsize=3366


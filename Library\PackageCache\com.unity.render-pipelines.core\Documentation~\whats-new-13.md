# What's new in SRP Core version 13 / Unity 2022.1

This page contains an overview of new features, improvements, and issues resolved in version 13 of the Core Render Pipeline package, embedded in Unity 2022.1.

## Added

### AMD Fidelity FX Super Sampling helper API - FSRUtils

Introducing new stream lined API for AMD Fidelity FX Super Sampling. The new API is located in the static class FSRUtils and allows scriptable pipelines to have direct access / implement and integrate easilty FSR super sampler.
For more information please review the API located in Runtime/Utitilies/FSRUtils.cs
